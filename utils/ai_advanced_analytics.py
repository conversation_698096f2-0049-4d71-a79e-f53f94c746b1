"""
Advanced AI Analytics for LogistiLink platform.
Provides fraud detection, demand forecasting, and container optimization.
"""

import os
import json
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import seaborn as sns
from django.db.models import Count, Sum, Avg, F, Q
from django.utils import timezone

# Import models once Django is initialized
def get_models():
    """Get Django models after initialization"""
    from shipments.models import Shipment, ShipmentTracking, ShippingRate, CargoType, ContainerType, SharedContainer, SharedContainerBooking
    from payments.models import Payment, Contract, Insurance, CustomsDeclaration
    from core.models import User
    from core.notification_models import Notification
    
    return {
        'Shipment': Shipment,
        'ShipmentTracking': ShipmentTracking,
        'ShippingRate': ShippingRate,
        'CargoType': CargoType,
        'ContainerType': ContainerType,
        'SharedContainer': SharedContainer,
        'SharedContainerBooking': SharedContainerBooking,
        'Payment': Payment,
        'Contract': Contract,
        'Insurance': Insurance,
        'CustomsDeclaration': CustomsDeclaration,
        'User': User,
        'Notification': Notification
    }

# -------------------- FRAUD DETECTION & ANOMALY MONITORING --------------------

def detect_booking_anomalies(days=30, threshold_multiplier=2.5):
    """
    Detect anomalies in booking patterns
    
    Args:
        days: Number of days to analyze
        threshold_multiplier: Multiplier for standard deviation to determine anomaly threshold
        
    Returns:
        DataFrame with detected anomalies
    """
    models = get_models()
    Shipment = models['Shipment']
    
    # Calculate date threshold
    date_threshold = timezone.now() - timedelta(days=days)
    
    # Get shipment data
    shipments = Shipment.objects.filter(created_at__gte=date_threshold).select_related(
        'customer', 'logistics_provider'
    )
    
    # Prepare data for analysis
    shipment_data = []
    for shipment in shipments:
        shipment_data.append({
            'id': shipment.id,
            'tracking_number': shipment.tracking_number,
            'customer_id': shipment.customer.id if shipment.customer else None,
            'customer_name': shipment.customer.company_name if shipment.customer else 'Unknown',
            'logistics_provider_id': shipment.logistics_provider.id if shipment.logistics_provider else None,
            'logistics_provider_name': shipment.logistics_provider.company_name if shipment.logistics_provider else 'Unknown',
            'origin_country': shipment.origin_country,
            'destination_country': shipment.destination_country,
            'transport_type': str(shipment.transport_type),
            'cargo_value': float(shipment.total_price) if shipment.total_price else 0.0,
            'created_at': shipment.created_at,
            'status': shipment.status,
        })
    
    if not shipment_data:
        return pd.DataFrame(), []
    
    # Convert to DataFrame
    df = pd.DataFrame(shipment_data)
    
    # Analyze booking patterns by customer
    customer_booking_counts = df.groupby('customer_id').size().reset_index(name='booking_count')
    customer_value_sum = df.groupby('customer_id')['cargo_value'].sum().reset_index(name='total_value')
    customer_metrics = pd.merge(customer_booking_counts, customer_value_sum, on='customer_id')
    
    # Calculate statistics for anomaly detection
    booking_mean = customer_metrics['booking_count'].mean()
    booking_std = customer_metrics['booking_count'].std()
    value_mean = customer_metrics['total_value'].mean()
    value_std = customer_metrics['total_value'].std()
    
    # Set thresholds for anomalies
    booking_threshold = booking_mean + (booking_std * threshold_multiplier)
    value_threshold = value_mean + (value_std * threshold_multiplier)
    
    # Detect anomalies
    booking_anomalies = customer_metrics[customer_metrics['booking_count'] > booking_threshold]
    value_anomalies = customer_metrics[customer_metrics['total_value'] > value_threshold]
    
    # Combine anomalies
    all_anomalies = pd.concat([booking_anomalies, value_anomalies]).drop_duplicates()
    
    # Add customer names
    customer_names = df[['customer_id', 'customer_name']].drop_duplicates()
    all_anomalies = pd.merge(all_anomalies, customer_names, on='customer_id')
    
    # Get additional anomaly patterns
    anomaly_patterns = []
    
    # 1. Multiple high-value shipments in short time
    if not df.empty and 'created_at' in df.columns and 'cargo_value' in df.columns:
        df['created_at'] = pd.to_datetime(df['created_at'])
        df = df.sort_values('created_at')
        
        # Group by customer and day
        df['date'] = df['created_at'].dt.date
        daily_high_value = df[df['cargo_value'] > value_mean * 1.5].groupby(['customer_id', 'date']).size().reset_index(name='high_value_count')
        
        # Find customers with multiple high-value shipments on same day
        suspicious_days = daily_high_value[daily_high_value['high_value_count'] >= 3]
        
        for _, row in suspicious_days.iterrows():
            customer_info = df[df['customer_id'] == row['customer_id']].iloc[0]
            anomaly_patterns.append({
                'type': 'Multiple high-value shipments in short time',
                'customer_id': row['customer_id'],
                'customer_name': customer_info['customer_name'],
                'date': row['date'],
                'count': row['high_value_count'],
                'risk_level': 'High' if row['high_value_count'] >= 5 else 'Medium'
            })
    
    # 2. Unusual destination patterns
    if not df.empty:
        # Get common destinations for each customer
        customer_destinations = df.groupby(['customer_id', 'destination_country']).size().reset_index(name='count')
        
        # For each customer, find destinations that are used only once (which might be unusual)
        common_customers = df['customer_id'].value_counts()[df['customer_id'].value_counts() > 5].index
        
        for customer_id in common_customers:
            customer_dest = customer_destinations[customer_destinations['customer_id'] == customer_id]
            unusual_destinations = customer_dest[customer_dest['count'] == 1]['destination_country'].tolist()
            
            if unusual_destinations:
                customer_info = df[df['customer_id'] == customer_id].iloc[0]
                anomaly_patterns.append({
                    'type': 'Unusual destination countries',
                    'customer_id': customer_id,
                    'customer_name': customer_info['customer_name'],
                    'unusual_destinations': unusual_destinations,
                    'risk_level': 'Medium'
                })
    
    # 3. Insurance anomalies
    # This would require joining with insurance data, implement in a future update
    
    return all_anomalies, anomaly_patterns

def detect_payment_anomalies(days=90):
    """
    Detect anomalies in payment patterns
    
    Args:
        days: Number of days to analyze
        
    Returns:
        List of detected payment anomalies
    """
    models = get_models()
    Payment = models['Payment']
    
    # Calculate date threshold
    date_threshold = timezone.now() - timedelta(days=days)
    
    # Get payment data
    payments = Payment.objects.filter(created_at__gte=date_threshold).select_related(
        'user', 'shipment'
    )
    
    # Prepare data for analysis
    payment_data = []
    for payment in payments:
        payment_data.append({
            'id': payment.id,
            'user_id': payment.user.id if payment.user else None,
            'user_name': payment.user.company_name if payment.user else 'Unknown',
            'shipment_id': payment.shipment.id if payment.shipment else None,
            'amount': payment.amount,
            'payment_method': payment.payment_method,
            'status': payment.status,
            'created_at': payment.created_at,
            'has_proof': bool(payment.payment_proof),
        })
    
    if not payment_data:
        return []
    
    # Convert to DataFrame
    df = pd.DataFrame(payment_data)
    
    # Detect anomalies
    anomalies = []
    
    # 1. Multiple failed payment attempts
    if 'status' in df.columns and 'user_id' in df.columns:
        failed_payments = df[df['status'] == 'FAILED'].groupby('user_id').size().reset_index(name='failed_count')
        suspicious_users = failed_payments[failed_payments['failed_count'] >= 3]
        
        for _, row in suspicious_users.iterrows():
            user_info = df[df['user_id'] == row['user_id']].iloc[0]
            anomalies.append({
                'type': 'Multiple failed payment attempts',
                'user_id': row['user_id'],
                'user_name': user_info['user_name'],
                'failed_count': row['failed_count'],
                'risk_level': 'High' if row['failed_count'] >= 5 else 'Medium'
            })
    
    # 2. Unusual payment methods for a user
    if 'payment_method' in df.columns and 'user_id' in df.columns:
        user_methods = df.groupby(['user_id', 'payment_method']).size().reset_index(name='method_count')
        
        for user_id in df['user_id'].unique():
            if pd.isna(user_id):
                continue
                
            user_payment_methods = user_methods[user_methods['user_id'] == user_id]
            if len(user_payment_methods) >= 3:  # If a user has used 3+ different payment methods
                user_info = df[df['user_id'] == user_id].iloc[0]
                anomalies.append({
                    'type': 'Multiple payment methods',
                    'user_id': user_id,
                    'user_name': user_info['user_name'],
                    'method_count': len(user_payment_methods),
                    'methods': user_payment_methods['payment_method'].tolist(),
                    'risk_level': 'Medium'
                })
    
    # 3. Bank transfers without proof
    if 'payment_method' in df.columns and 'has_proof' in df.columns:
        missing_proof = df[(df['payment_method'] == 'BANK_TRANSFER') & (df['has_proof'] == False)]
        
        for user_id in missing_proof['user_id'].unique():
            if pd.isna(user_id):
                continue
                
            user_missing_proofs = missing_proof[missing_proof['user_id'] == user_id]
            if len(user_missing_proofs) >= 2:
                user_info = df[df['user_id'] == user_id].iloc[0]
                anomalies.append({
                    'type': 'Bank transfers without proof',
                    'user_id': user_id,
                    'user_name': user_info['user_name'],
                    'missing_proof_count': len(user_missing_proofs),
                    'risk_level': 'High'
                })
    
    return anomalies

def create_fraud_alert_notification(user_id, alert_type, details):
    """
    Create a notification for a fraud alert
    
    Args:
        user_id: ID of the admin user to notify
        alert_type: Type of fraud alert
        details: Details of the alert
        
    Returns:
        Notification object ID
    """
    models = get_models()
    User = models['User']
    Notification = models['Notification']
    
    try:
        # Find admin user
        admin_user = User.objects.filter(user_type='ADMIN').first()
        
        if not admin_user and user_id:
            # Fall back to specified user
            admin_user = User.objects.get(id=user_id)
        
        if not admin_user:
            return None
        
        # Create notification
        notification = Notification.objects.create(
            user=admin_user,
            title=f"FRAUD ALERT: {alert_type}",
            message=details,
            notification_type='SYSTEM',
            priority='HIGH',
            is_read=False,
            related_object_type='fraud_alert'
        )
        
        return notification.id
    
    except Exception as e:
        print(f"Error creating fraud alert notification: {str(e)}")
        return None

# -------------------- AI DEMAND FORECASTING --------------------

def forecast_shipping_demand(days_history=180, forecast_days=90):
    """
    Forecast shipping demand by route and transport type
    
    Args:
        days_history: Number of days of historical data to use
        forecast_days: Number of days to forecast into the future
        
    Returns:
        Dictionary with forecast results
    """
    models = get_models()
    Shipment = models['Shipment']
    
    # Calculate date threshold
    date_threshold = timezone.now() - timedelta(days=days_history)
    
    # Get shipment data
    shipments = Shipment.objects.filter(created_at__gte=date_threshold)
    
    # Prepare data for analysis
    shipment_data = []
    for shipment in shipments:
        shipment_data.append({
            'id': shipment.id,
            'origin_country': shipment.origin_country,
            'destination_country': shipment.destination_country,
            'transport_type': str(shipment.transport_type),
            'created_at': shipment.created_at,
            'cargo_type': shipment.cargo_type.name if shipment.cargo_type else 'Unknown',
            'weight_kg': shipment.weight_kg,
        })
    
    if not shipment_data:
        return {
            "error": "Insufficient data for forecasting",
            "forecast": {}
        }
    
    # Convert to DataFrame
    df = pd.DataFrame(shipment_data)
    
    # Convert dates and create time features
    df['created_at'] = pd.to_datetime(df['created_at'])
    df['date'] = df['created_at'].dt.date
    df['month'] = df['created_at'].dt.month
    df['day_of_week'] = df['created_at'].dt.dayofweek
    df['quarter'] = df['created_at'].dt.quarter
    
    # Group by date and count shipments
    daily_counts = df.groupby('date').size().reset_index(name='shipment_count')
    daily_counts['date'] = pd.to_datetime(daily_counts['date'])
    
    # Fill in missing dates with zeros
    date_range = pd.date_range(start=daily_counts['date'].min(), end=daily_counts['date'].max())
    daily_counts = daily_counts.set_index('date').reindex(date_range, fill_value=0).reset_index()
    daily_counts = daily_counts.rename(columns={'index': 'date'})
    
    # Prepare route-based analysis
    route_data = df.groupby(['origin_country', 'destination_country', 'transport_type', 'date']).size().reset_index(name='count')
    
    # Calculate route averages
    route_avg = route_data.groupby(['origin_country', 'destination_country', 'transport_type'])['count'].mean().reset_index()
    
    # Find popular routes
    popular_routes = route_avg.sort_values('count', ascending=False).head(10)
    
    # Create seasonal factors based on historical data
    monthly_factors = df.groupby('month').size() / df.groupby('month').size().mean()
    quarterly_factors = df.groupby('quarter').size() / df.groupby('quarter').size().mean()
    
    # Forecast function (simple approach)
    def generate_forecast(route_row, days):
        origin = route_row['origin_country']
        destination = route_row['destination_country']
        transport = route_row['transport_type']
        baseline = route_row['count']
        
        # Get route data
        route_history = route_data[
            (route_data['origin_country'] == origin) &
            (route_data['destination_country'] == destination) &
            (route_data['transport_type'] == transport)
        ]
        
        # If insufficient route data, use baseline
        if len(route_history) < 30:
            forecast_values = np.ones(days) * baseline
            
            # Apply some basic seasonality
            current_date = timezone.now().date()
            forecast_dates = [current_date + timedelta(days=i) for i in range(days)]
            forecast_months = [d.month for d in forecast_dates]
            
            # Apply monthly seasonality if available
            for i, month in enumerate(forecast_months):
                if month in monthly_factors:
                    forecast_values[i] *= monthly_factors[month]
        
        else:
            # Use historical mean with seasonal adjustments
            current_date = timezone.now().date()
            forecast_values = []
            forecast_dates = [current_date + timedelta(days=i) for i in range(days)]
            
            for i, date in enumerate(forecast_dates):
                # Apply seasonal factors
                month_factor = monthly_factors[date.month] if date.month in monthly_factors else 1
                quarter_factor = quarterly_factors[(date.month-1)//3 + 1] if (date.month-1)//3 + 1 in quarterly_factors else 1
                
                # Combine factors
                seasonal_factor = (month_factor + quarter_factor) / 2
                
                # Apply basic trend (slight growth)
                trend_factor = 1 + (i / (days * 10))  # 10% growth over the forecast period
                
                forecast_values.append(baseline * seasonal_factor * trend_factor)
        
        return {
            'route': f"{origin} to {destination} ({transport})",
            'current_daily_avg': baseline,
            'forecast': list(forecast_values),
            'dates': [d.strftime('%Y-%m-%d') for d in forecast_dates]
        }
    
    # Generate forecasts for popular routes
    route_forecasts = []
    for _, route in popular_routes.iterrows():
        forecast = generate_forecast(route, forecast_days)
        route_forecasts.append(forecast)
    
    # Generate transport type forecasts
    transport_avg = df.groupby(['transport_type', 'date']).size().reset_index(name='count')
    transport_summary = transport_avg.groupby('transport_type')['count'].mean().reset_index()
    
    transport_forecasts = {}
    for _, transport in transport_summary.iterrows():
        transport_type = transport['transport_type']
        baseline = transport['count']
        
        # Create simple forecast
        current_date = timezone.now().date()
        forecast_values = []
        forecast_dates = [current_date + timedelta(days=i) for i in range(forecast_days)]
        
        for i, date in enumerate(forecast_dates):
            # Apply seasonal factors
            month_factor = monthly_factors[date.month] if date.month in monthly_factors else 1
            
            # Apply basic trend
            trend_factor = 1 + (i / (forecast_days * 10))
            
            forecast_values.append(baseline * month_factor * trend_factor)
        
        transport_forecasts[transport_type] = {
            'current_daily_avg': baseline,
            'forecast': list(forecast_values),
            'dates': [d.strftime('%Y-%m-%d') for d in forecast_dates]
        }
    
    # Identify potential capacity issues
    capacity_alerts = []
    for forecast in route_forecasts:
        peak_demand = max(forecast['forecast'])
        if peak_demand > forecast['current_daily_avg'] * 1.5:
            peak_date = forecast['dates'][forecast['forecast'].index(peak_demand)]
            capacity_alerts.append({
                'route': forecast['route'],
                'peak_date': peak_date,
                'peak_demand': peak_demand,
                'current_capacity': forecast['current_daily_avg'],
                'increase_percentage': ((peak_demand / forecast['current_daily_avg']) - 1) * 100
            })
    
    # Identify underutilized routes
    route_utilization = []
    for _, route in route_avg.iterrows():
        # Skip routes with no data
        if route['count'] == 0:
            continue
            
        utilization = {
            'origin': route['origin_country'],
            'destination': route['destination_country'],
            'transport_type': route['transport_type'],
            'avg_daily_shipments': route['count']
        }
        
        # Check if this is a popular route
        is_popular = False
        for _, pop_route in popular_routes.iterrows():
            if (pop_route['origin_country'] == route['origin_country'] and 
                pop_route['destination_country'] == route['destination_country'] and
                str(pop_route['transport_type']) == str(route['transport_type'])):
                is_popular = True
                break
        
        utilization['is_popular'] = is_popular
        
        if not is_popular and route['count'] < 0.5:  # Less than 0.5 shipments per day on average
            utilization['status'] = 'Underutilized'
        else:
            utilization['status'] = 'Normal'
        
        route_utilization.append(utilization)
    
    # Prepare final forecast result
    forecast_result = {
        'route_forecasts': route_forecasts,
        'transport_forecasts': transport_forecasts,
        'capacity_alerts': capacity_alerts,
        'route_utilization': route_utilization,
        'seasonality': {
            'monthly_factors': monthly_factors.to_dict(),
            'quarterly_factors': quarterly_factors.to_dict()
        }
    }
    
    return forecast_result

def generate_demand_forecast_charts(forecast_data):
    """
    Generate charts for demand forecasting
    
    Args:
        forecast_data: Data from forecast_shipping_demand
        
    Returns:
        Dictionary with chart paths
    """
    # Create directory for charts if it doesn't exist
    charts_dir = os.path.join(os.getcwd(), 'static', 'charts')
    os.makedirs(charts_dir, exist_ok=True)
    
    chart_paths = {}
    
    # 1. Route Forecast Chart (Top 5 routes)
    if 'route_forecasts' in forecast_data and forecast_data['route_forecasts']:
        plt.figure(figsize=(12, 6))
        
        # Get top 5 routes by current volume
        top_routes = sorted(forecast_data['route_forecasts'], key=lambda x: x['current_daily_avg'], reverse=True)[:5]
        
        for route_forecast in top_routes:
            plt.plot(
                pd.to_datetime(route_forecast['dates']), 
                route_forecast['forecast'],
                label=route_forecast['route']
            )
        
        plt.title('Shipping Demand Forecast - Top 5 Routes')
        plt.xlabel('Date')
        plt.ylabel('Projected Daily Shipments')
        plt.legend()
        plt.grid(True, linestyle='--', alpha=0.7)
        
        # Save chart
        route_chart_path = os.path.join(charts_dir, 'route_forecast.png')
        plt.savefig(route_chart_path)
        plt.close()
        
        chart_paths['route_forecast'] = '/static/charts/route_forecast.png'
    
    # 2. Transport Type Forecast Chart
    if 'transport_forecasts' in forecast_data and forecast_data['transport_forecasts']:
        plt.figure(figsize=(12, 6))
        
        for transport_type, forecast in forecast_data['transport_forecasts'].items():
            plt.plot(
                pd.to_datetime(forecast['dates']),
                forecast['forecast'],
                label=transport_type
            )
        
        plt.title('Shipping Demand Forecast by Transport Type')
        plt.xlabel('Date')
        plt.ylabel('Projected Daily Shipments')
        plt.legend()
        plt.grid(True, linestyle='--', alpha=0.7)
        
        # Save chart
        transport_chart_path = os.path.join(charts_dir, 'transport_forecast.png')
        plt.savefig(transport_chart_path)
        plt.close()
        
        chart_paths['transport_forecast'] = '/static/charts/transport_forecast.png'
    
    # 3. Seasonality Chart
    if 'seasonality' in forecast_data and 'monthly_factors' in forecast_data['seasonality']:
        plt.figure(figsize=(10, 5))
        
        months = list(forecast_data['seasonality']['monthly_factors'].keys())
        factors = list(forecast_data['seasonality']['monthly_factors'].values())
        
        # Convert numeric months to names
        month_names = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
        named_months = [month_names[m-1] for m in months]
        
        plt.bar(named_months, factors, color='skyblue')
        plt.axhline(y=1.0, color='r', linestyle='-', alpha=0.3)
        
        plt.title('Monthly Seasonality Factors')
        plt.xlabel('Month')
        plt.ylabel('Relative Demand (1.0 = Average)')
        plt.ylim(0, max(factors) * 1.2)
        
        # Save chart
        season_chart_path = os.path.join(charts_dir, 'seasonality_factors.png')
        plt.savefig(season_chart_path)
        plt.close()
        
        chart_paths['seasonality_chart'] = '/static/charts/seasonality_factors.png'
    
    # 4. Capacity Alerts Chart
    if 'capacity_alerts' in forecast_data and forecast_data['capacity_alerts']:
        plt.figure(figsize=(12, 6))
        
        routes = [alert['route'] for alert in forecast_data['capacity_alerts']]
        increases = [alert['increase_percentage'] for alert in forecast_data['capacity_alerts']]
        
        # Sort by increase percentage
        sorted_indices = np.argsort(increases)[::-1]
        routes = [routes[i] for i in sorted_indices]
        increases = [increases[i] for i in sorted_indices]
        
        # Limit to top 10
        if len(routes) > 10:
            routes = routes[:10]
            increases = increases[:10]
        
        colors = ['red' if inc > 80 else 'orange' if inc > 50 else 'gold' for inc in increases]
        
        plt.barh(routes, increases, color=colors)
        
        plt.title('Projected Capacity Pressure by Route')
        plt.xlabel('Projected Demand Increase (%)')
        plt.tight_layout()
        
        # Save chart
        capacity_chart_path = os.path.join(charts_dir, 'capacity_alerts.png')
        plt.savefig(capacity_chart_path)
        plt.close()
        
        chart_paths['capacity_alerts'] = '/static/charts/capacity_alerts.png'
    
    return chart_paths

# -------------------- SMART CONTAINER GROUPING --------------------

def optimize_container_grouping(origin=None, destination=None, available_containers=None):
    """
    Optimize container grouping for shared shipments
    
    Args:
        origin: Origin country (optional filter)
        destination: Destination country (optional filter)
        available_containers: List of available container types
        
    Returns:
        Dictionary with optimized groupings
    """
    models = get_models()
    Shipment = models['Shipment']
    SharedContainer = models['SharedContainer']
    ContainerType = models['ContainerType']
    
    # Get pending shipments waiting for container sharing
    filters = Q(status='PENDING') | Q(status='BOOKED')
    
    if origin:
        filters &= Q(origin_country=origin)
    if destination:
        filters &= Q(destination_country=destination)
    
    pending_shipments = Shipment.objects.filter(filters).select_related(
        'cargo_type', 'customer'
    )
    
    # Get available container types
    if not available_containers:
        container_types = ContainerType.objects.all()
        available_containers = [{
            'id': ct.id,
            'name': ct.name,
            'volume_cbm': ct.length_m * ct.width_m * ct.height_m,
            'max_weight_kg': ct.max_weight_kg
        } for ct in container_types]
    
    # Prepare shipment data
    shipment_data = []
    for shipment in pending_shipments:
        # Skip shipments that don't have volume or weight
        if not shipment.volume_m3 or not shipment.weight_kg:
            continue
            
        shipment_data.append({
            'id': shipment.id,
            'tracking_number': shipment.tracking_number,
            'customer_id': shipment.customer.id if shipment.customer else None,
            'customer_name': shipment.customer.company_name if shipment.customer else 'Unknown',
            'origin_country': shipment.origin_country,
            'destination_country': shipment.destination_country,
            'transport_type': str(shipment.transport_type),
            'cargo_type': shipment.cargo_type.name if shipment.cargo_type else 'Unknown',
            'volume_cbm': shipment.volume_m3,
            'weight_kg': shipment.weight_kg,
            'cargo_value': float(shipment.total_price) if shipment.total_price else 0.0,
            'status': shipment.status,
        })
    
    if not shipment_data:
        return {"error": "No suitable shipments found for container grouping"}
    
    # Convert to DataFrame
    df = pd.DataFrame(shipment_data)
    
    # Group shipments by route
    route_groups = df.groupby(['origin_country', 'destination_country', 'transport_type'])
    
    optimized_groups = []
    
    for (origin, destination, transport), group in route_groups:
        # Skip if less than 2 shipments (no grouping needed)
        if len(group) < 2:
            continue
        
        # Find optimal container type
        total_volume = group['volume_cbm'].sum()
        total_weight = group['weight_kg'].sum()
        
        # Find container types that can fit this group
        suitable_containers = []
        for container in available_containers:
            volume_capacity = container['volume_cbm']
            weight_capacity = container['max_weight_kg']
            
            if volume_capacity >= total_volume and weight_capacity >= total_weight:
                # Calculate utilization
                volume_utilization = (total_volume / volume_capacity) * 100
                weight_utilization = (total_weight / weight_capacity) * 100
                
                # Overall utilization (average of volume and weight)
                overall_utilization = (volume_utilization + weight_utilization) / 2
                
                suitable_containers.append({
                    'container_id': container['id'],
                    'container_name': container['name'],
                    'volume_capacity': volume_capacity,
                    'weight_capacity': weight_capacity,
                    'volume_utilization': volume_utilization,
                    'weight_utilization': weight_utilization,
                    'overall_utilization': overall_utilization
                })
        
        # If no suitable container found for the entire group, try creating multiple groups
        if not suitable_containers:
            # Sort shipments by volume (descending)
            sorted_shipments = group.sort_values('volume_cbm', ascending=False)
            
            # Start with largest container
            largest_container = max(available_containers, key=lambda x: x['volume_cbm'])
            
            # Initialize groups
            current_group = []
            current_volume = 0
            current_weight = 0
            
            # Assign shipments to groups
            for _, shipment in sorted_shipments.iterrows():
                # If adding this shipment exceeds container capacity, start a new group
                if (current_volume + shipment['volume_cbm'] > largest_container['volume_cbm'] or
                    current_weight + shipment['weight_kg'] > largest_container['max_weight_kg']):
                    
                    # Save current group if not empty
                    if current_group:
                        optimized_groups.append({
                            'origin': origin,
                            'destination': destination,
                            'transport_type': transport,
                            'container_type': largest_container['name'],
                            'container_id': largest_container['id'],
                            'shipments': current_group,
                            'total_volume_cbm': current_volume,
                            'total_weight_kg': current_weight,
                            'volume_utilization': (current_volume / largest_container['volume_cbm']) * 100,
                            'weight_utilization': (current_weight / largest_container['max_weight_kg']) * 100
                        })
                    
                    # Start new group
                    current_group = [{
                        'id': shipment['id'],
                        'tracking_number': shipment['tracking_number'],
                        'customer_name': shipment['customer_name'],
                        'cargo_type': shipment['cargo_type'],
                        'volume_cbm': shipment['volume_cbm'],
                        'weight_kg': shipment['weight_kg']
                    }]
                    current_volume = shipment['volume_cbm']
                    current_weight = shipment['weight_kg']
                else:
                    # Add to current group
                    current_group.append({
                        'id': shipment['id'],
                        'tracking_number': shipment['tracking_number'],
                        'customer_name': shipment['customer_name'],
                        'cargo_type': shipment['cargo_type'],
                        'volume_cbm': shipment['volume_cbm'],
                        'weight_kg': shipment['weight_kg']
                    })
                    current_volume += shipment['volume_cbm']
                    current_weight += shipment['weight_kg']
            
            # Add last group if not empty
            if current_group:
                optimized_groups.append({
                    'origin': origin,
                    'destination': destination,
                    'transport_type': transport,
                    'container_type': largest_container['name'],
                    'container_id': largest_container['id'],
                    'shipments': current_group,
                    'total_volume_cbm': current_volume,
                    'total_weight_kg': current_weight,
                    'volume_utilization': (current_volume / largest_container['volume_cbm']) * 100,
                    'weight_utilization': (current_weight / largest_container['max_weight_kg']) * 100
                })
        else:
            # Find the most efficiently utilized container
            best_container = max(suitable_containers, key=lambda x: x['overall_utilization'])
            
            # Create optimized group
            shipment_list = []
            for _, shipment in group.iterrows():
                shipment_list.append({
                    'id': shipment['id'],
                    'tracking_number': shipment['tracking_number'],
                    'customer_name': shipment['customer_name'],
                    'cargo_type': shipment['cargo_type'],
                    'volume_cbm': shipment['volume_cbm'],
                    'weight_kg': shipment['weight_kg']
                })
            
            optimized_groups.append({
                'origin': origin,
                'destination': destination,
                'transport_type': transport,
                'container_type': best_container['container_name'],
                'container_id': best_container['container_id'],
                'shipments': shipment_list,
                'total_volume_cbm': total_volume,
                'total_weight_kg': total_weight,
                'volume_utilization': best_container['volume_utilization'],
                'weight_utilization': best_container['weight_utilization']
            })
    
    # Sort groups by utilization (descending)
    optimized_groups.sort(key=lambda x: (x['volume_utilization'] + x['weight_utilization'])/2, reverse=True)
    
    return {
        'optimized_groups': optimized_groups,
        'total_groups': len(optimized_groups),
        'total_shipments_grouped': sum(len(group['shipments']) for group in optimized_groups)
    }

def create_shared_container_from_grouping(group_data):
    """
    Create a shared container from grouping data
    
    Args:
        group_data: Group data from optimize_container_grouping
        
    Returns:
        ID of created shared container
    """
    models = get_models()
    Shipment = models['Shipment']
    SharedContainer = models['SharedContainer']
    SharedContainerBooking = models['SharedContainerBooking']
    ContainerType = models['ContainerType']
    TransportType = models['TransportType']
    
    try:
        # Get container type
        container_type = ContainerType.objects.get(id=group_data['container_id'])
        
        # Get transport type object from string
        transport_type = TransportType.objects.get(name=group_data['transport_type'])
        
        # Create shared container
        shared_container = SharedContainer.objects.create(
            origin_country=group_data['origin'],
            destination_country=group_data['destination'],
            transport_type=transport_type,
            container_type=container_type,
            status='PENDING',
            total_volume_cbm=group_data['total_volume_cbm'],
            total_weight_kg=group_data['total_weight_kg'],
            volume_utilization=group_data['volume_utilization'],
            weight_utilization=group_data['weight_utilization'],
            target_departure_date=timezone.now() + timedelta(days=7)  # Default to 7 days from now
        )
        
        # Add shipments to shared container
        for shipment_data in group_data['shipments']:
            shipment = Shipment.objects.get(id=shipment_data['id'])
            
            # Create booking
            booking = SharedContainerBooking.objects.create(
                shared_container=shared_container,
                shipment=shipment,
                volume_cbm=shipment.volume_m3,
                weight_kg=shipment.weight_kg,
                status='PENDING'
            )
        
        return shared_container.id
    
    except Exception as e:
        print(f"Error creating shared container: {str(e)}")
        return None