"""
Process Flow Router - Unified Shipment Creation and Management System
Handles all shipment types (direct, quote, private) and process stage management
"""

import logging
from datetime import datetime, timezone
from typing import Dict, Any, Optional, List
from django.db import transaction
from django.utils import timezone as django_timezone
from asgiref.sync import sync_to_async

logger = logging.getLogger(__name__)

class ProcessFlowRouter:
    """
    Unified router for handling all shipment creation types and process stage management.
    Supports direct, quote-based, and private shipment flows with stage validation.
    """
    
    # Stage transition validation - prevents invalid stage jumps
    ALLOWED_TRANSITIONS = {
        "created": ["contract"],
        "contract": ["payment"],
        "payment": ["container", "driver", "customs", "trip"],  # Multiple paths possible
        "container": ["driver", "customs", "trip"],
        "driver": ["customs", "trip"],
        "customs": ["insurance", "trip"],
        "insurance": ["trip"],
        "trip": ["delivery"],
        "delivery": ["feedback"],
        "feedback": ["completed"],
        "completed": [],  # Terminal state
    }
    
    def __init__(self):
        self.logger = logger
    
    async def create_shipment(self, origin_type: str, data: Dict[str, Any], 
                             source_id: Optional[int] = None) -> Dict[str, Any]:
        """
        Unified shipment creation handler for all origin types.
        
        Args:
            origin_type: 'direct', 'quote', or 'private'
            data: Shipment data dict
            source_id: ID of source object (quote_id or invitation_id)
            
        Returns:
            Dict with shipment info and creation status
        """
        try:
            with transaction.atomic():
                if origin_type == 'direct':
                    return await self._create_direct_shipment(data)
                elif origin_type == 'quote':
                    return await self._create_quote_shipment(data, source_id)
                elif origin_type == 'private':
                    return await self._create_private_shipment(data, source_id)
                else:
                    raise ValueError(f"Invalid origin_type: {origin_type}")
                    
        except Exception as e:
            self.logger.error(f"Error creating {origin_type} shipment: {e}")
            return {
                'success': False,
                'error': str(e),
                'shipment_id': None
            }
    
    async def _create_direct_shipment(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Create shipment from direct provider selection"""
        from shipments.models import Shipment
        
        shipment = await sync_to_async(Shipment.objects.create)(
            origin_type='direct',
            process_stage='created',
            stage_timestamps={'created': django_timezone.now().isoformat()},
            customer_id=data['customer_id'],
            logistics_provider_id=data['logistics_provider_id'],
            **self._extract_shipment_fields(data)
        )
        
        # Trigger process stage event
        await self._trigger_stage_event(shipment, 'created', 'shipment_created')
        
        return {
            'success': True,
            'shipment_id': shipment.id,
            'tracking_number': shipment.tracking_number,
            'next_stage': 'contract'
        }
    
    async def _create_quote_shipment(self, data: Dict[str, Any], quote_id: int) -> Dict[str, Any]:
        """Create shipment from accepted quote"""
        from shipments.models import Shipment
        from core.models import QuoteRequest
        
        # Get quote details
        try:
            quote = await sync_to_async(QuoteRequest.objects.get)(id=quote_id)
        except QuoteRequest.DoesNotExist:
            raise ValueError(f"Quote {quote_id} not found")
        
        shipment = await sync_to_async(Shipment.objects.create)(
            origin_type='quote',
            process_stage='created',
            source_quote_id=quote_id,
            stage_timestamps={'created': django_timezone.now().isoformat()},
            customer=quote.customer,
            logistics_provider=quote.logistics_provider,
            **self._extract_shipment_fields(data)
        )
        
        # Update quote status
        quote.status = 'ACCEPTED'
        await sync_to_async(quote.save)()
        
        await self._trigger_stage_event(shipment, 'created', 'quote_accepted_shipment_created')
        
        return {
            'success': True,
            'shipment_id': shipment.id,
            'tracking_number': shipment.tracking_number,
            'next_stage': 'contract',
            'source_quote': quote_id
        }
    
    async def _create_private_shipment(self, data: Dict[str, Any], invitation_id: int) -> Dict[str, Any]:
        """Create shipment from accepted private invitation"""
        from shipments.models import Shipment
        
        # Note: Private invitation model integration would go here
        # For now, create with invitation reference
        
        shipment = await sync_to_async(Shipment.objects.create)(
            origin_type='private',
            process_stage='created',
            source_invitation_id=invitation_id,
            stage_timestamps={'created': django_timezone.now().isoformat()},
            **self._extract_shipment_fields(data)
        )
        
        await self._trigger_stage_event(shipment, 'created', 'private_invitation_accepted_shipment_created')
        
        return {
            'success': True,
            'shipment_id': shipment.id,
            'tracking_number': shipment.tracking_number,
            'next_stage': 'contract',
            'source_invitation': invitation_id
        }
    
    def _extract_shipment_fields(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Extract and validate shipment fields from input data"""
        required_fields = [
            'origin_country', 'origin_city', 'destination_country', 'destination_city',
            'cargo_description', 'weight_kg', 'scheduled_pickup_date', 'estimated_delivery_date'
        ]
        
        shipment_data = {}
        for field in required_fields:
            if field not in data:
                raise ValueError(f"Required field '{field}' missing")
            shipment_data[field] = data[field]
        
        # Optional fields
        optional_fields = [
            'volume_m3', 'container_count', 'has_hazardous_items', 'shipping_type',
            'base_price', 'total_price', 'transport_type_id', 'cargo_type_id'
        ]
        
        for field in optional_fields:
            if field in data:
                shipment_data[field] = data[field]
        
        # Auto-generate tracking number if not provided
        if 'tracking_number' not in shipment_data:
            shipment_data['tracking_number'] = self._generate_tracking_number()
        
        return shipment_data
    
    def _generate_tracking_number(self) -> str:
        """Generate unique tracking number"""
        import secrets
        import string
        
        prefix = "CL"
        random_part = ''.join(secrets.choice(string.ascii_uppercase + string.digits) for _ in range(8))
        return f"{prefix}{random_part}"
    
    async def advance_stage(self, shipment_id: int, new_stage: str, 
                           user_id: int, notes: str = "") -> Dict[str, Any]:
        """
        Advance shipment to next process stage with validation.
        
        Args:
            shipment_id: Shipment ID
            new_stage: Target stage
            user_id: User performing the action
            notes: Optional notes for the stage change
            
        Returns:
            Dict with success status and next stage info
        """
        try:
            from shipments.models import Shipment
            
            shipment = await sync_to_async(Shipment.objects.get)(id=shipment_id)
            current_stage = shipment.process_stage
            
            # Validate stage transition
            if not self._is_valid_transition(current_stage, new_stage):
                return {
                    'success': False,
                    'error': f"Invalid transition from {current_stage} to {new_stage}",
                    'allowed_transitions': self.ALLOWED_TRANSITIONS.get(current_stage, [])
                }
            
            # Update shipment stage
            with transaction.atomic():
                shipment.process_stage = new_stage
                
                # Update timestamps
                timestamps = shipment.stage_timestamps or {}
                timestamps[new_stage] = django_timezone.now().isoformat()
                shipment.stage_timestamps = timestamps
                
                # Add to process metadata for audit trail
                metadata = shipment.process_metadata or {}
                if 'stage_history' not in metadata:
                    metadata['stage_history'] = []
                
                metadata['stage_history'].append({
                    'from_stage': current_stage,
                    'to_stage': new_stage,
                    'timestamp': django_timezone.now().isoformat(),
                    'user_id': user_id,
                    'notes': notes
                })
                shipment.process_metadata = metadata
                
                await sync_to_async(shipment.save)()
            
            # Trigger stage change event
            await self._trigger_stage_event(shipment, new_stage, 'stage_advanced', {
                'from_stage': current_stage,
                'user_id': user_id,
                'notes': notes
            })
            
            # Determine next possible stages
            next_stages = self.ALLOWED_TRANSITIONS.get(new_stage, [])
            
            return {
                'success': True,
                'current_stage': new_stage,
                'next_stages': next_stages,
                'completed': new_stage == 'completed'
            }
            
        except Exception as e:
            self.logger.error(f"Error advancing stage for shipment {shipment_id}: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _is_valid_transition(self, current_stage: str, new_stage: str) -> bool:
        """Validate if stage transition is allowed"""
        allowed = self.ALLOWED_TRANSITIONS.get(current_stage, [])
        return new_stage in allowed
    
    async def _trigger_stage_event(self, shipment, stage: str, event_type: str, 
                                  additional_data: Dict[str, Any] = None):
        """
        Trigger event hooks for stage changes (ready for future enhancements).
        This is where audit logging, HTTP notifications, and AI insights will hook in.
        """
        event_data = {
            'shipment_id': shipment.id,
            'tracking_number': shipment.tracking_number,
            'stage': stage,
            'event_type': event_type,
            'timestamp': django_timezone.now().isoformat(),
            'origin_type': shipment.origin_type,
        }
        
        if additional_data:
            event_data.update(additional_data)
        
        # Log the event (ready for audit logging enhancement)
        self.logger.info(f"Process stage event: {event_type} for shipment {shipment.id} at stage {stage}")
        
        # Future enhancement hooks:
        # - await self._send_http_notification(event_data)
        # - await self._log_audit_event(event_data)
        # - await self._check_ai_insights(event_data)
        # - await self._send_email_notifications(event_data)
    
    async def get_stage_progress(self, shipment_id: int) -> Dict[str, Any]:
        """
        Get current stage and progress information for a shipment.
        Used by the process tracker component.
        """
        try:
            from shipments.models import Shipment
            
            shipment = await sync_to_async(Shipment.objects.get)(id=shipment_id)
            
            all_stages = [choice[0] for choice in shipment.PROCESS_STAGE_CHOICES]
            current_index = all_stages.index(shipment.process_stage)
            
            completed_stages = all_stages[:current_index]
            next_stages = self.ALLOWED_TRANSITIONS.get(shipment.process_stage, [])
            
            return {
                'shipment_id': shipment_id,
                'tracking_number': shipment.tracking_number,
                'origin_type': shipment.origin_type,
                'current_stage': shipment.process_stage,
                'completed_stages': completed_stages,
                'next_stages': next_stages,
                'progress_percentage': round((current_index / len(all_stages)) * 100, 1),
                'stage_timestamps': shipment.stage_timestamps or {},
                'is_completed': shipment.process_stage == 'completed'
            }
            
        except Exception as e:
            self.logger.error(f"Error getting stage progress for shipment {shipment_id}: {e}")
            return {
                'error': str(e)
            }
    
    async def get_stage_analytics(self, date_range: Optional[Dict] = None) -> Dict[str, Any]:
        """
        Get analytics for process stages (ready for admin dashboard).
        """
        try:
            from shipments.models import Shipment
            from django.db.models import Count
            
            # Base queryset
            queryset = Shipment.objects.all()
            
            # Apply date filter if provided
            if date_range:
                if 'start_date' in date_range:
                    queryset = queryset.filter(created_at__gte=date_range['start_date'])
                if 'end_date' in date_range:
                    queryset = queryset.filter(created_at__lte=date_range['end_date'])
            
            # Get stage distribution
            stage_counts = await sync_to_async(
                lambda: dict(queryset.values_list('process_stage').annotate(count=Count('id')))
            )()
            
            # Get origin type distribution
            origin_counts = await sync_to_async(
                lambda: dict(queryset.values_list('origin_type').annotate(count=Count('id')))
            )()
            
            # Calculate completion rate
            total_shipments = await sync_to_async(queryset.count)()
            completed_shipments = await sync_to_async(
                queryset.filter(process_stage='completed').count
            )()
            
            completion_rate = (completed_shipments / total_shipments * 100) if total_shipments > 0 else 0
            
            return {
                'total_shipments': total_shipments,
                'completion_rate': round(completion_rate, 1),
                'stage_distribution': stage_counts,
                'origin_type_distribution': origin_counts,
                'completed_shipments': completed_shipments
            }
            
        except Exception as e:
            self.logger.error(f"Error getting stage analytics: {e}")
            return {
                'error': str(e)
            }

# Global router instance
process_router = ProcessFlowRouter()