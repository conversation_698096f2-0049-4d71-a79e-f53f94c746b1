"""
Chart.js Integration Fix System
Resolves JavaScript errors and Chart.js conflicts across all dashboards
"""

import json
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import asyncio
import random

class ChartDataProcessor:
    """Process and format data for Chart.js visualization"""
    
    def __init__(self):
        self.chart_configs = {}
        self.error_log = []
    
    def format_line_chart_data(self, data: List[Dict], x_field: str, y_field: str, label: str = "Data") -> Dict:
        """Format data for Chart.js line charts"""
        try:
            # Sort data by x_field to ensure proper line progression
            sorted_data = sorted(data, key=lambda x: x.get(x_field, 0))
            
            labels = [str(item.get(x_field, '')) for item in sorted_data]
            values = [float(item.get(y_field, 0)) for item in sorted_data]
            
            return {
                "type": "line",
                "data": {
                    "labels": labels,
                    "datasets": [{
                        "label": label,
                        "data": values,
                        "borderColor": "#007bff",
                        "backgroundColor": "rgba(0, 123, 255, 0.1)",
                        "tension": 0.1,
                        "fill": True
                    }]
                },
                "options": {
                    "responsive": True,
                    "maintainAspectRatio": False,
                    "scales": {
                        "y": {
                            "beginAtZero": False
                        }
                    },
                    "plugins": {
                        "legend": {
                            "display": True,
                            "position": "top"
                        }
                    }
                }
            }
        except Exception as e:
            self.log_error(f"Line chart formatting error: {str(e)}")
            return self.get_fallback_chart_config("line")
    
    def format_doughnut_chart_data(self, data: List[Dict], label_field: str, value_field: str) -> Dict:
        """Format data for Chart.js doughnut charts"""
        try:
            labels = [str(item.get(label_field, '')) for item in data]
            values = [float(item.get(value_field, 0)) for item in data]
            
            # Generate distinct colors for each segment
            colors = self.generate_colors(len(labels))
            
            return {
                "type": "doughnut",
                "data": {
                    "labels": labels,
                    "datasets": [{
                        "data": values,
                        "backgroundColor": colors,
                        "borderWidth": 2,
                        "borderColor": "#fff"
                    }]
                },
                "options": {
                    "responsive": True,
                    "maintainAspectRatio": False,
                    "plugins": {
                        "legend": {
                            "display": True,
                            "position": "bottom"
                        }
                    }
                }
            }
        except Exception as e:
            self.log_error(f"Doughnut chart formatting error: {str(e)}")
            return self.get_fallback_chart_config("doughnut")
    
    def format_bar_chart_data(self, data: List[Dict], x_field: str, y_field: str, label: str = "Data") -> Dict:
        """Format data for Chart.js bar charts"""
        try:
            labels = [str(item.get(x_field, '')) for item in data]
            values = [float(item.get(y_field, 0)) for item in data]
            
            return {
                "type": "bar",
                "data": {
                    "labels": labels,
                    "datasets": [{
                        "label": label,
                        "data": values,
                        "backgroundColor": "rgba(40, 167, 69, 0.8)",
                        "borderColor": "#28a745",
                        "borderWidth": 1
                    }]
                },
                "options": {
                    "responsive": True,
                    "maintainAspectRatio": False,
                    "scales": {
                        "y": {
                            "beginAtZero": True
                        }
                    },
                    "plugins": {
                        "legend": {
                            "display": True,
                            "position": "top"
                        }
                    }
                }
            }
        except Exception as e:
            self.log_error(f"Bar chart formatting error: {str(e)}")
            return self.get_fallback_chart_config("bar")
    
    def format_mixed_chart_data(self, datasets: List[Dict]) -> Dict:
        """Format data for mixed Chart.js charts (line + bar combination)"""
        try:
            # Extract labels from first dataset
            labels = datasets[0].get("labels", [])
            
            chart_datasets = []
            for i, dataset in enumerate(datasets):
                chart_type = dataset.get("type", "line")
                chart_datasets.append({
                    "type": chart_type,
                    "label": dataset.get("label", f"Dataset {i+1}"),
                    "data": dataset.get("data", []),
                    "borderColor": dataset.get("color", self.generate_colors(1)[0]),
                    "backgroundColor": dataset.get("background_color", "rgba(0, 123, 255, 0.1)"),
                    "yAxisID": dataset.get("y_axis", "y")
                })
            
            return {
                "type": "line",
                "data": {
                    "labels": labels,
                    "datasets": chart_datasets
                },
                "options": {
                    "responsive": True,
                    "maintainAspectRatio": False,
                    "interaction": {
                        "mode": "index",
                        "intersect": False
                    },
                    "scales": {
                        "y": {
                            "type": "linear",
                            "display": True,
                            "position": "left"
                        }
                    },
                    "plugins": {
                        "legend": {
                            "display": True,
                            "position": "top"
                        }
                    }
                }
            }
        except Exception as e:
            self.log_error(f"Mixed chart formatting error: {str(e)}")
            return self.get_fallback_chart_config("line")
    
    def generate_colors(self, count: int) -> List[str]:
        """Generate distinct colors for chart segments"""
        base_colors = [
            "#007bff", "#28a745", "#dc3545", "#ffc107", 
            "#17a2b8", "#6f42c1", "#fd7e14", "#20c997",
            "#e83e8c", "#6c757d", "#343a40", "#f8f9fa"
        ]
        
        if count <= len(base_colors):
            return base_colors[:count]
        
        # Generate additional colors if needed
        colors = base_colors[:]
        for i in range(count - len(base_colors)):
            # Generate random colors with good contrast
            hue = (i * 360 / (count - len(base_colors))) % 360
            colors.append(f"hsl({hue}, 70%, 50%)")
        
        return colors[:count]
    
    def get_fallback_chart_config(self, chart_type: str) -> Dict:
        """Get fallback chart configuration for errors"""
        fallback_data = {
            "line": {
                "type": "line",
                "data": {
                    "labels": ["No Data"],
                    "datasets": [{
                        "label": "No Data Available",
                        "data": [0],
                        "borderColor": "#dc3545",
                        "backgroundColor": "rgba(220, 53, 69, 0.1)"
                    }]
                }
            },
            "doughnut": {
                "type": "doughnut",
                "data": {
                    "labels": ["No Data"],
                    "datasets": [{
                        "data": [1],
                        "backgroundColor": ["#dc3545"]
                    }]
                }
            },
            "bar": {
                "type": "bar",
                "data": {
                    "labels": ["No Data"],
                    "datasets": [{
                        "label": "No Data Available",
                        "data": [0],
                        "backgroundColor": "#dc3545"
                    }]
                }
            }
        }
        
        config = fallback_data.get(chart_type, fallback_data["line"])
        config["options"] = {
            "responsive": True,
            "maintainAspectRatio": False,
            "plugins": {
                "legend": {
                    "display": True
                }
            }
        }
        
        return config
    
    def log_error(self, error_message: str):
        """Log chart errors for debugging"""
        self.error_log.append({
            "timestamp": datetime.now().isoformat(),
            "error": error_message
        })
        print(f"Chart Error: {error_message}")

class MarketIntelligenceChartData:
    """Generate chart data for Market Intelligence Hub"""
    
    def __init__(self):
        self.processor = ChartDataProcessor()
    
    async def get_price_trend_data(self, transport_type: str = None, route: str = None) -> Dict:
        """Get price trend data for line charts"""
        # Generate realistic price trend data
        months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun"]
        base_price = 2.20
        
        price_data = []
        for i, month in enumerate(months):
            # Add realistic price fluctuations
            trend = base_price + (i * 0.05) + random.uniform(-0.15, 0.15)
            price_data.append({
                "month": month,
                "price": round(max(1.50, trend), 2),
                "volume": random.randint(150, 400)
            })
        
        return self.processor.format_line_chart_data(
            price_data, 
            "month", 
            "price", 
            f"Average Rate ($/kg) - {transport_type or 'All Modes'}"
        )
    
    async def get_capacity_utilization_data(self) -> Dict:
        """Get capacity utilization data for doughnut charts"""
        capacity_data = [
            {"transport_mode": "Truck", "utilization": 85.2},
            {"transport_mode": "Ship", "utilization": 78.6},
            {"transport_mode": "Air", "utilization": 92.1},
            {"transport_mode": "Rail", "utilization": 67.3}
        ]
        
        return self.processor.format_doughnut_chart_data(
            capacity_data,
            "transport_mode",
            "utilization"
        )
    
    async def get_provider_performance_data(self) -> Dict:
        """Get provider performance data for bar charts"""
        providers = [
            {"name": "FastLogistics", "score": 4.8, "deliveries": 245},
            {"name": "GlobalTrans", "score": 4.6, "deliveries": 189},
            {"name": "OceanicFreight", "score": 4.7, "deliveries": 156},
            {"name": "EuroLogistics", "score": 4.5, "deliveries": 203},
            {"name": "AsiaLink", "score": 4.4, "deliveries": 178}
        ]
        
        return self.processor.format_bar_chart_data(
            providers,
            "name",
            "score",
            "Provider Performance Score"
        )
    
    async def get_route_analytics_data(self, origin: str = None, destination: str = None) -> Dict:
        """Get route analytics data for mixed charts"""
        route_name = f"{origin or 'Global'} → {destination or 'Routes'}"
        
        datasets = [
            {
                "type": "line",
                "label": "Average Price ($/kg)",
                "data": [2.15, 2.22, 2.28, 2.35, 2.41, 2.48],
                "color": "#007bff",
                "background_color": "rgba(0, 123, 255, 0.1)",
                "y_axis": "y"
            },
            {
                "type": "bar",
                "label": "Shipment Volume",
                "data": [89, 95, 102, 87, 93, 98],
                "color": "#28a745",
                "background_color": "rgba(40, 167, 69, 0.8)",
                "y_axis": "y1"
            }
        ]
        
        datasets[0]["labels"] = ["Jan", "Feb", "Mar", "Apr", "May", "Jun"]
        
        return self.processor.format_mixed_chart_data(datasets)

class AIAnalyticsChartData:
    """Generate chart data for AI Analytics dashboard"""
    
    def __init__(self):
        self.processor = ChartDataProcessor()
    
    async def get_demand_forecast_data(self) -> Dict:
        """Get demand forecasting chart data"""
        forecast_data = []
        base_date = datetime.now()
        
        for i in range(12):
            date = base_date + timedelta(days=i * 30)
            demand = 100 + (i * 5) + random.uniform(-10, 15)
            forecast_data.append({
                "date": date.strftime("%b %Y"),
                "demand": round(max(80, demand), 1),
                "confidence": random.uniform(0.85, 0.95)
            })
        
        return self.processor.format_line_chart_data(
            forecast_data,
            "date",
            "demand",
            "Predicted Demand (Index)"
        )
    
    async def get_price_prediction_data(self) -> Dict:
        """Get price prediction chart data"""
        prediction_data = []
        base_price = 2.45
        
        for i in range(8):
            week = f"Week {i+1}"
            predicted_price = base_price + (i * 0.02) + random.uniform(-0.08, 0.08)
            prediction_data.append({
                "week": week,
                "price": round(max(2.00, predicted_price), 2),
                "accuracy": random.uniform(0.88, 0.96)
            })
        
        return self.processor.format_line_chart_data(
            prediction_data,
            "week",
            "price",
            "Predicted Price ($/kg)"
        )
    
    async def get_optimization_results_data(self) -> Dict:
        """Get route optimization results"""
        optimization_data = [
            {"metric": "Cost Reduction", "value": 18.5},
            {"metric": "Time Savings", "value": 22.3},
            {"metric": "Fuel Efficiency", "value": 15.7},
            {"metric": "Route Optimization", "value": 28.9},
            {"metric": "Capacity Utilization", "value": 34.2}
        ]
        
        return self.processor.format_bar_chart_data(
            optimization_data,
            "metric",
            "value",
            "Optimization Improvement (%)"
        )

class LogisticsIndexChartData:
    """Generate chart data for Cloverics Logistics Index"""
    
    def __init__(self):
        self.processor = ChartDataProcessor()
    
    async def get_cli_index_data(self) -> Dict:
        """Get Cloverics Logistics Index trend data"""
        index_data = []
        base_index = 100.0
        
        for i in range(12):
            month = (datetime.now() - timedelta(days=(11-i) * 30)).strftime("%b %Y")
            index_value = base_index + (i * 2.5) + random.uniform(-3, 5)
            index_data.append({
                "month": month,
                "index": round(max(95, index_value), 1),
                "change": random.uniform(-2.5, 4.2)
            })
        
        return self.processor.format_line_chart_data(
            index_data,
            "month",
            "index",
            "Cloverics Logistics Index (CLI)"
        )
    
    async def get_transport_mode_index_data(self) -> Dict:
        """Get transport mode specific index data"""
        transport_data = [
            {"mode": "Truck Transport", "index": 102.5},
            {"mode": "Ocean Freight", "index": 98.7},
            {"mode": "Air Cargo", "index": 105.3},
            {"mode": "Rail Transport", "index": 96.2},
            {"mode": "Intermodal", "index": 101.8}
        ]
        
        return self.processor.format_bar_chart_data(
            transport_data,
            "mode",
            "index",
            "CLI by Transport Mode"
        )

# Global chart data instance
chart_data_processor = ChartDataProcessor()
market_intelligence_charts = MarketIntelligenceChartData()
ai_analytics_charts = AIAnalyticsChartData()
logistics_index_charts = LogisticsIndexChartData()