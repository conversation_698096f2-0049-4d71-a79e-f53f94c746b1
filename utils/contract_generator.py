"""
Enhanced Contract Generator for LogistiLink

This module provides functionality for generating detailed contract documents
with comprehensive shipment breakdowns, insurance terms, and proper signatures.
"""

import os
import datetime
from io import BytesIO
import tempfile
from decimal import Decimal

import pandas as pd
from reportlab.lib.pagesizes import letter
from reportlab.lib import colors
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch

def format_currency(amount):
    """Format decimal value as currency string."""
    if isinstance(amount, Decimal):
        return f"${amount:.2f}"
    return f"${float(amount):.2f}"

def generate_contract_pdf(contract, shipment, insurance=None):
    """
    Generate a detailed contract PDF with comprehensive details.
    
    Args:
        contract: Contract model instance
        shipment: Shipment model instance
        insurance: Insurance model instance (optional)
        
    Returns:
        BytesIO object containing the PDF
    """
    buffer = BytesIO()
    doc = SimpleDocTemplate(buffer, pagesize=letter)
    styles = getSampleStyleSheet()
    
    # Custom styles
    styles.add(ParagraphStyle(
        name='ContractTitle',
        parent=styles['Heading1'],
        fontSize=16,
        alignment=1,
        spaceAfter=20
    ))
    styles.add(ParagraphStyle(
        name='SectionTitle',
        parent=styles['Heading2'],
        fontSize=14,
        spaceBefore=15,
        spaceAfter=10
    ))
    styles.add(ParagraphStyle(
        name='SubsectionTitle',
        parent=styles['Heading3'],
        fontSize=12,
        spaceBefore=10,
        spaceAfter=5
    ))
    
    # Document elements
    elements = []
    
    # Header
    elements.append(Paragraph(f"LOGISTICS CONTRACT #{contract.contract_number}", styles['ContractTitle']))
    elements.append(Paragraph(f"Date: {contract.created_at.strftime('%B %d, %Y')}", styles['Normal']))
    elements.append(Spacer(1, 0.2*inch))
    
    # Parties
    elements.append(Paragraph("PARTIES", styles['SectionTitle']))
    
    parties_data = [
        ["SERVICE PROVIDER:", "CUSTOMER:"],
        [f"{contract.logistics_provider.company_name}", f"{contract.customer.company_name}"],
        [f"Tax ID: {contract.logistics_provider.tax_id}", f"Tax ID: {contract.customer.tax_id}"],
        [f"Address: {contract.logistics_provider.address}", f"Address: {contract.customer.address}"]
    ]
    
    parties_table = Table(parties_data, colWidths=[doc.width/2.0, doc.width/2.0])
    parties_table.setStyle(TableStyle([
        ('GRID', (0, 0), (-1, -1), 0.5, colors.grey),
        ('BACKGROUND', (0, 0), (0, 0), colors.lightgrey),
        ('BACKGROUND', (1, 0), (1, 0), colors.lightgrey),
        ('VALIGN', (0, 0), (-1, -1), 'TOP'),
    ]))
    elements.append(parties_table)
    elements.append(Spacer(1, 0.2*inch))
    
    # Contract value
    elements.append(Paragraph("CONTRACT VALUE", styles['SectionTitle']))
    value_text = f"The total contract value is {format_currency(contract.contract_value)}, payable according to the terms specified in the PAYMENT TERMS section."
    elements.append(Paragraph(value_text, styles['Normal']))
    elements.append(Spacer(1, 0.2*inch))
    
    # Shipment details
    elements.append(Paragraph("SHIPMENT DETAILS", styles['SectionTitle']))
    
    shipment_data = [
        ["Tracking Number:", shipment.tracking_number],
        ["Origin:", f"{shipment.origin_city}, {shipment.origin_country}"],
        ["Destination:", f"{shipment.destination_city}, {shipment.destination_country}"],
        ["Cargo Description:", shipment.cargo_description],
        ["Weight (kg):", str(shipment.weight_kg)],
        ["Volume (m³):", str(shipment.volume_m3)],
        ["Container Type:", shipment.container_type],
        ["Transport Mode:", shipment.transport_type],
        ["Scheduled Pickup:", shipment.scheduled_pickup_date.strftime('%B %d, %Y') if shipment.scheduled_pickup_date else "TBD"],
        ["Estimated Delivery:", shipment.estimated_delivery_date.strftime('%B %d, %Y') if shipment.estimated_delivery_date else "TBD"]
    ]
    
    shipment_table = Table(shipment_data, colWidths=[doc.width/3.0, 2*doc.width/3.0])
    shipment_table.setStyle(TableStyle([
        ('GRID', (0, 0), (-1, -1), 0.5, colors.grey),
        ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
        ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ('ALIGN', (0, 0), (0, -1), 'RIGHT'),
    ]))
    elements.append(shipment_table)
    elements.append(Spacer(1, 0.2*inch))
    
    # Cost breakdown
    elements.append(Paragraph("COST BREAKDOWN", styles['SectionTitle']))
    
    cost_data = [
        ["Item", "Amount"],
        ["Base Price", format_currency(shipment.base_price)],
        ["Insurance", format_currency(shipment.insurance_price)],
        ["Customs Fees", format_currency(shipment.customs_price)],
        ["Additional Fees", format_currency(shipment.additional_fees)],
        ["TOTAL", format_currency(shipment.total_price)]
    ]
    
    cost_table = Table(cost_data, colWidths=[2*doc.width/3.0, doc.width/3.0])
    cost_table.setStyle(TableStyle([
        ('GRID', (0, 0), (-1, -1), 0.5, colors.grey),
        ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
        ('BACKGROUND', (0, -1), (-1, -1), colors.lightgrey),
        ('ALIGN', (1, 0), (1, -1), 'RIGHT'),
        ('FONTNAME', (0, -1), (-1, -1), 'Helvetica-Bold'),
    ]))
    elements.append(cost_table)
    elements.append(Spacer(1, 0.2*inch))
    
    # Insurance terms if available
    if insurance:
        elements.append(Paragraph("INSURANCE COVERAGE", styles['SectionTitle']))
        
        insurance_data = [
            ["Policy Number:", insurance.policy_number],
            ["Insurance Provider:", insurance.insurance_provider.company_name],
            ["Coverage Amount:", format_currency(insurance.coverage_amount)],
            ["Premium:", format_currency(insurance.premium_amount)],
            ["Deductible:", format_currency(insurance.deductible_amount)],
            ["Coverage Period:", f"{insurance.start_date.strftime('%B %d, %Y')} to {insurance.end_date.strftime('%B %d, %Y')}"]
        ]
        
        insurance_table = Table(insurance_data, colWidths=[doc.width/3.0, 2*doc.width/3.0])
        insurance_table.setStyle(TableStyle([
            ('GRID', (0, 0), (-1, -1), 0.5, colors.grey),
            ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('ALIGN', (0, 0), (0, -1), 'RIGHT'),
        ]))
        elements.append(insurance_table)
        elements.append(Spacer(1, 0.2*inch))
    
    # Terms and conditions
    elements.append(Paragraph("TERMS & CONDITIONS", styles['SectionTitle']))
    
    terms_text = """
    1. SERVICES: Provider will transport the cargo described above from origin to destination using the specified transport mode.
    
    2. PAYMENT: Payment must be made in full before the shipment can be released at the destination.
    
    3. DELIVERY TIMEFRAME: Estimated delivery dates are non-binding and subject to external factors including weather, port congestion, and customs clearance times.
    
    4. INSURANCE: All shipments are covered per the insurance terms specified in this contract. Claims must be filed within 30 days of delivery.
    
    5. LIABILITY: Provider's liability is limited to the coverage amount specified in the insurance section.
    
    6. FORCE MAJEURE: Neither party shall be liable for delays or failures in performance resulting from acts beyond its reasonable control.
    
    7. GOVERNING LAW: This contract is governed by international shipping regulations and the laws of the country of origin.
    
    8. CUSTOMS: Customer is responsible for providing accurate information for customs declarations.
    
    9. TERMINATION: This contract may be terminated by mutual agreement of both parties or if either party breaches a material term.
    
    10. DOCUMENT VALIDITY: This contract is valid only with authorized signatures from both parties.
    """
    
    for term in terms_text.strip().split('\n\n'):
        elements.append(Paragraph(term.strip(), styles['Normal']))
        elements.append(Spacer(1, 0.1*inch))
    
    elements.append(Spacer(1, 0.2*inch))
    
    # Signatures
    elements.append(Paragraph("SIGNATURES", styles['SectionTitle']))
    
    # Add signature table
    signature_data = [
        ["LOGISTICS PROVIDER", "CUSTOMER"],
        ["Signature: __________________", "Signature: __________________"],
        [f"Date: {contract.provider_signature_date.strftime('%B %d, %Y') if contract.provider_signature_date else '_________________'}", 
         f"Date: {contract.customer_signature_date.strftime('%B %d, %Y') if contract.customer_signature_date else '_________________'}"]
    ]
    
    signature_table = Table(signature_data, colWidths=[doc.width/2.0, doc.width/2.0])
    signature_table.setStyle(TableStyle([
        ('GRID', (0, 0), (-1, -1), 0.5, colors.grey),
        ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
        ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
    ]))
    elements.append(signature_table)
    
    # Contract status
    elements.append(Spacer(1, 0.3*inch))
    
    status_text = "CONTRACT STATUS: "
    if contract.terms_accepted_by_customer and contract.terms_accepted_by_provider:
        status_text += "FULLY SIGNED AND EXECUTED"
    elif contract.terms_accepted_by_customer:
        status_text += "SIGNED BY CUSTOMER ONLY (AWAITING PROVIDER SIGNATURE)"
    elif contract.terms_accepted_by_provider:
        status_text += "SIGNED BY PROVIDER ONLY (AWAITING CUSTOMER SIGNATURE)"
    else:
        status_text += "PENDING SIGNATURES"
    
    elements.append(Paragraph(status_text, styles['Normal']))
    
    # Add footer with contract ID and date
    footer_text = f"Contract #{contract.contract_number} | Generated on {datetime.datetime.now().strftime('%B %d, %Y at %H:%M')} | LogistiLink Platform"
    elements.append(Spacer(1, 0.3*inch))
    elements.append(Paragraph(footer_text, styles['Normal']))
    
    # Build the PDF
    doc.build(elements)
    buffer.seek(0)
    return buffer

def download_contract(contract, shipment, insurance=None):
    """
    Generate contract PDF and save to temporary file for download.
    
    Args:
        contract: Contract model instance
        shipment: Shipment model instance
        insurance: Insurance model instance (optional)
        
    Returns:
        Path to temporary file containing the PDF
    """
    pdf_buffer = generate_contract_pdf(contract, shipment, insurance)
    
    # Create a temporary file
    fd, temp_path = tempfile.mkstemp(suffix='.pdf')
    os.close(fd)
    
    # Write PDF to temporary file
    with open(temp_path, 'wb') as f:
        f.write(pdf_buffer.read())
    
    return temp_path