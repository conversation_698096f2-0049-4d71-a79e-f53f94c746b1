"""
Country-based filtering utilities for customs authorities
Implements data segregation ensuring each customs authority only sees relevant data
"""

import re
from typing import Optional, List
from asgiref.sync import sync_to_async


def extract_country_from_customs_email(email: str) -> Optional[str]:
    """
    Extract country name from customs email pattern: customsof{country}@demo.com
    Returns standardized country name for filtering
    """
    if not email or not email.startswith('customsof'):
        return None
    
    # Extract country from email pattern
    match = re.match(r'customsof([a-z]+)@demo\.com', email.lower())
    if not match:
        return None
    
    country_code = match.group(1)
    
    # Map common country codes to full country names as stored in database
    country_mapping = {
        'azerbaijan': 'Azerbaijan',
        'turkey': 'Turkey', 
        'china': 'China',
        'usa': 'USA',
        'unitedstates': 'United States',
        'uk': 'UK',
        'unitedkingdom': 'United Kingdom',
        'italy': 'Italy',
        'georgia': 'Georgia',
        'afghanistan': 'Afghanistan',
        'hungary': 'Hungary',
        'austria': 'Austria',
        'florida': 'United States - Florida',
        'virginia': 'United States - Virginia',
        # Add more mappings as needed
        'germany': 'Germany',
        'france': 'France',
        'spain': 'Spain',
        'portugal': 'Portugal',
        'netherlands': 'Netherlands',
        'belgium': 'Belgium',
        'switzerland': 'Switzerland',
        'norway': 'Norway',
        'sweden': 'Sweden',
        'denmark': 'Denmark',
        'finland': 'Finland',
        'poland': 'Poland',
        'czechrepublic': 'Czech Republic',
        'slovakia': 'Slovakia',
        'slovenia': 'Slovenia',
        'croatia': 'Croatia',
        'bosnia': 'Bosnia and Herzegovina',
        'serbia': 'Serbia',
        'montenegro': 'Montenegro',
        'albania': 'Albania',
        'greece': 'Greece',
        'bulgaria': 'Bulgaria',
        'romania': 'Romania',
        'ukraine': 'Ukraine',
        'russia': 'Russia',
        'belarus': 'Belarus',
        'lithuania': 'Lithuania',
        'latvia': 'Latvia',
        'estonia': 'Estonia',
        'moldova': 'Moldova',
        'armenia': 'Armenia',
        'georgia': 'Georgia',
        'kazakhstan': 'Kazakhstan',
        'uzbekistan': 'Uzbekistan',
        'turkmenistan': 'Turkmenistan',
        'kyrgyzstan': 'Kyrgyzstan',
        'tajikistan': 'Tajikistan',
        'mongolia': 'Mongolia',
        'japan': 'Japan',
        'southkorea': 'South Korea',
        'northkorea': 'North Korea',
        'india': 'India',
        'pakistan': 'Pakistan',
        'bangladesh': 'Bangladesh',
        'srilanka': 'Sri Lanka',
        'nepal': 'Nepal',
        'bhutan': 'Bhutan',
        'myanmar': 'Myanmar',
        'thailand': 'Thailand',
        'vietnam': 'Vietnam',
        'laos': 'Laos',
        'cambodia': 'Cambodia',
        'malaysia': 'Malaysia',
        'singapore': 'Singapore',
        'brunei': 'Brunei',
        'indonesia': 'Indonesia',
        'philippines': 'Philippines',
        'australia': 'Australia',
        'newzealand': 'New Zealand',
        'canada': 'Canada',
        'mexico': 'Mexico',
        'brazil': 'Brazil',
        'argentina': 'Argentina',
        'chile': 'Chile',
        'peru': 'Peru',
        'colombia': 'Colombia',
        'venezuela': 'Venezuela',
        'ecuador': 'Ecuador',
        'bolivia': 'Bolivia',
        'uruguay': 'Uruguay',
        'paraguay': 'Paraguay',
        'egypt': 'Egypt',
        'libya': 'Libya',
        'tunisia': 'Tunisia',
        'algeria': 'Algeria',
        'morocco': 'Morocco',
        'sudan': 'Sudan',
        'ethiopia': 'Ethiopia',
        'kenya': 'Kenya',
        'tanzania': 'Tanzania',
        'uganda': 'Uganda',
        'rwanda': 'Rwanda',
        'burundi': 'Burundi',
        'somalia': 'Somalia',
        'djibouti': 'Djibouti',
        'eritrea': 'Eritrea',
        'southafrica': 'South Africa',
        'namibia': 'Namibia',
        'botswana': 'Botswana',
        'zimbabwe': 'Zimbabwe',
        'zambia': 'Zambia',
        'malawi': 'Malawi',
        'mozambique': 'Mozambique',
        'madagascar': 'Madagascar',
        'mauritius': 'Mauritius',
        'seychelles': 'Seychelles',
        'ghana': 'Ghana',
        'nigeria': 'Nigeria',
        'senegal': 'Senegal',
        'mali': 'Mali',
        'burkinafaso': 'Burkina Faso',
        'niger': 'Niger',
        'chad': 'Chad',
        'cameroon': 'Cameroon',
        'centralafrican': 'Central African Republic',
        'gabon': 'Gabon',
        'congo': 'Congo',
        'drcongo': 'Democratic Republic of Congo',
        'angola': 'Angola',
        'ivorycoast': 'Ivory Coast',
        'liberia': 'Liberia',
        'sierraleone': 'Sierra Leone',
        'guinea': 'Guinea',
        'guineabissau': 'Guinea-Bissau',
        'gambia': 'Gambia',
        'capeverde': 'Cape Verde',
        'saotome': 'Sao Tome and Principe',
    }
    
    return country_mapping.get(country_code, country_code.title())


def shipment_related_to_customs_country(shipment, customs_country: str) -> bool:
    """
    Check if a shipment is relevant to a specific customs authority country
    Returns True if the shipment involves the customs country as origin or destination
    """
    if not customs_country:
        return False
    
    try:
        # Handle both string fields and potential variations
        origin = getattr(shipment, 'origin_country', None) or ''
        destination = getattr(shipment, 'destination_country', None) or ''
        
        # Normalize country names for comparison
        origin_normalized = normalize_country_name(origin)
        destination_normalized = normalize_country_name(destination)
        customs_normalized = normalize_country_name(customs_country)
        
        return (origin_normalized == customs_normalized or 
                destination_normalized == customs_normalized)
                
    except Exception as e:
        print(f"[Country Filter Error] {e}")
        return False


def declaration_related_to_customs_country(declaration, customs_country: str) -> bool:
    """
    Check if a customs declaration is relevant to a specific customs authority country
    """
    if not customs_country:
        return False
    
    try:
        # Check country_of_origin field
        declaration_country = getattr(declaration, 'country_of_origin', None) or ''
        declaration_normalized = normalize_country_name(declaration_country)
        customs_normalized = normalize_country_name(customs_country)
        
        # Also check the related shipment if available
        if hasattr(declaration, 'shipment') and declaration.shipment:
            shipment_relevant = shipment_related_to_customs_country(declaration.shipment, customs_country)
            return (declaration_normalized == customs_normalized or shipment_relevant)
        
        return declaration_normalized == customs_normalized
        
    except Exception as e:
        print(f"[Declaration Filter Error] {e}")
        return False


def normalize_country_name(country: str) -> str:
    """
    Normalize country names for consistent comparison
    """
    if not country:
        return ''
    
    # Remove extra spaces and convert to title case
    normalized = country.strip().title()
    
    # Handle common variations
    variations = {
        'Usa': 'USA',
        'Uk': 'UK',
        'Us': 'USA',
        'United States Of America': 'United States',
        'United Kingdom': 'UK',
    }
    
    return variations.get(normalized, normalized)


async def filter_shipments_for_customs(customs_user, base_queryset):
    """
    Filter shipments queryset to only include shipments relevant to customs country
    """
    if not hasattr(customs_user, 'email') or not customs_user.email:
        return base_queryset.none()
    
    customs_country = extract_country_from_customs_email(customs_user.email)
    if not customs_country:
        return base_queryset.none()
    
    # Use database filtering for efficiency
    from django.db.models import Q
    
    customs_normalized = normalize_country_name(customs_country)
    
    # Filter by origin or destination country
    return base_queryset.filter(
        Q(origin_country__icontains=customs_normalized) |
        Q(destination_country__icontains=customs_normalized)
    )


async def filter_declarations_for_customs(customs_user, base_queryset):
    """
    Filter customs declarations queryset to only include declarations relevant to customs country
    """
    if not hasattr(customs_user, 'email') or not customs_user.email:
        return base_queryset.none()
    
    customs_country = extract_country_from_customs_email(customs_user.email)
    if not customs_country:
        return base_queryset.none()
    
    from django.db.models import Q
    
    customs_normalized = normalize_country_name(customs_country)
    
    # Filter by country_of_origin or related shipment countries
    from django.db.models import Q
    return base_queryset.filter(
        Q(country_of_origin__icontains=customs_normalized) |
        Q(shipment__origin_country__icontains=customs_normalized) |
        Q(shipment__destination_country__icontains=customs_normalized)
    )


def validate_customs_permission(customs_user, shipment_or_declaration) -> bool:
    """
    Validate if a customs user has permission to access a specific shipment or declaration
    Used for security checks in API endpoints
    """
    if not hasattr(customs_user, 'email') or not customs_user.email:
        return False
    
    customs_country = extract_country_from_customs_email(customs_user.email)
    if not customs_country:
        return False
    
    # Check if it's a shipment or declaration
    if hasattr(shipment_or_declaration, 'origin_country'):
        # It's a shipment
        return shipment_related_to_customs_country(shipment_or_declaration, customs_country)
    elif hasattr(shipment_or_declaration, 'country_of_origin'):
        # It's a declaration
        return declaration_related_to_customs_country(shipment_or_declaration, customs_country)
    
    return False