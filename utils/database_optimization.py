"""
LogistiLink Database Optimization Module

This module provides database indexing recommendations, query optimization,
and performance tuning for high-traffic scenarios.
"""

import logging
from django.db import connection
from django.core.management.base import BaseCommand
from utils.performance_security import db_manager

# Configure database optimization logging
db_opt_logger = logging.getLogger('logistilink.database_optimization')

class DatabaseIndexOptimizer:
    """Analyzes and optimizes database indexes for performance"""
    
    def __init__(self):
        self.recommended_indexes = []
        
    def analyze_query_patterns(self):
        """Analyze common query patterns and recommend indexes"""
        recommendations = []
        
        # Customer marketplace queries
        recommendations.extend([
            {
                'table': 'shipments_customeroffer',
                'columns': ['customer_id', 'status', 'created_at'],
                'reason': 'Customer offer lookups with status filtering',
                'query_pattern': 'Customer viewing their offers'
            },
            {
                'table': 'shipments_logisticsinterest',
                'columns': ['offer_id', 'status', 'created_at'],
                'reason': 'Quote management and status tracking',
                'query_pattern': 'Logistics companies managing quotes'
            },
            {
                'table': 'shipments_shipment',
                'columns': ['customer_id', 'status', 'created_at'],
                'reason': 'Customer shipment tracking',
                'query_pattern': 'Customer viewing shipment history'
            },
            {
                'table': 'shipments_shipment',
                'columns': ['logistics_company_id', 'status'],
                'reason': 'Logistics company shipment management',
                'query_pattern': 'Logistics viewing assigned shipments'
            },
            {
                'table': 'payments_payment',
                'columns': ['shipment_id', 'status'],
                'reason': 'Payment status lookups',
                'query_pattern': 'Payment processing and tracking'
            }
        ])
        
        # Geographic and routing indexes
        recommendations.extend([
            {
                'table': 'shipments_customeroffer',
                'columns': ['origin_country', 'destination_country'],
                'reason': 'Route-based offer searching',
                'query_pattern': 'Logistics companies finding offers by route'
            },
            {
                'table': 'shipments_shipment',
                'columns': ['origin_country', 'destination_country', 'transport_type'],
                'reason': 'Route and transport filtering',
                'query_pattern': 'Shipment tracking by route and mode'
            }
        ])
        
        # Performance-critical composite indexes
        recommendations.extend([
            {
                'table': 'core_user',
                'columns': ['email', 'is_active'],
                'reason': 'Login authentication optimization',
                'query_pattern': 'User authentication queries'
            },
            {
                'table': 'core_notification',
                'columns': ['user_id', 'is_read', 'created_at'],
                'reason': 'Notification system performance',
                'query_pattern': 'User notification retrieval'
            }
        ])
        
        return recommendations
    
    def generate_index_sql(self, recommendation):
        """Generate SQL for creating recommended index"""
        table = recommendation['table']
        columns = recommendation['columns']
        
        # Create descriptive index name
        index_name = f"idx_{table}_{'_'.join(columns)}"
        column_list = ', '.join(columns)
        
        sql = f"CREATE INDEX CONCURRENTLY {index_name} ON {table} ({column_list});"
        
        return {
            'sql': sql,
            'index_name': index_name,
            'estimated_benefit': self._estimate_performance_benefit(recommendation)
        }
    
    def _estimate_performance_benefit(self, recommendation):
        """Estimate performance benefit of proposed index"""
        # Base benefit score
        benefit_score = 50
        
        # High-frequency query patterns get higher scores
        high_frequency_patterns = [
            'Customer viewing their offers',
            'User authentication queries',
            'Customer shipment tracking'
        ]
        
        if recommendation['query_pattern'] in high_frequency_patterns:
            benefit_score += 30
        
        # Multi-column indexes provide better filtering
        if len(recommendation['columns']) > 1:
            benefit_score += 20
        
        return min(100, benefit_score)
    
    def check_existing_indexes(self):
        """Check which recommended indexes already exist"""
        with connection.cursor() as cursor:
            # Get all existing indexes
            cursor.execute("""
                SELECT indexname, tablename, indexdef 
                FROM pg_indexes 
                WHERE schemaname = 'public'
                ORDER BY tablename, indexname;
            """)
            
            existing_indexes = cursor.fetchall()
            
        recommendations = self.analyze_query_patterns()
        index_status = []
        
        for rec in recommendations:
            proposed_sql = self.generate_index_sql(rec)
            
            # Check if similar index exists
            exists = any(
                rec['table'] in existing[1] and 
                any(col in existing[2] for col in rec['columns'])
                for existing in existing_indexes
            )
            
            index_status.append({
                'recommendation': rec,
                'proposed_sql': proposed_sql,
                'exists': exists,
                'priority': 'HIGH' if proposed_sql['estimated_benefit'] > 80 else 'MEDIUM'
            })
        
        return index_status

class QueryPerformanceAnalyzer:
    """Analyzes and optimizes database query performance"""
    
    def __init__(self):
        self.slow_queries = []
        
    def analyze_slow_queries(self):
        """Identify and analyze slow-running queries"""
        with connection.cursor() as cursor:
            # Enable query logging if not already enabled
            cursor.execute("SELECT name, setting FROM pg_settings WHERE name LIKE 'log_%';")
            settings = cursor.fetchall()
            
            # Get query statistics if pg_stat_statements is available
            try:
                cursor.execute("""
                    SELECT query, calls, total_time, mean_time, rows
                    FROM pg_stat_statements 
                    WHERE mean_time > 1000  -- Queries taking more than 1 second
                    ORDER BY mean_time DESC 
                    LIMIT 10;
                """)
                slow_queries = cursor.fetchall()
                
                return [
                    {
                        'query': query[0][:200] + '...' if len(query[0]) > 200 else query[0],
                        'calls': query[1],
                        'total_time_ms': query[2],
                        'mean_time_ms': query[3],
                        'avg_rows': query[4],
                        'optimization_priority': 'HIGH' if query[3] > 5000 else 'MEDIUM'
                    }
                    for query in slow_queries
                ]
                
            except Exception as e:
                db_opt_logger.info("pg_stat_statements not available, using alternative analysis")
                return []
    
    def suggest_query_optimizations(self, query_analysis):
        """Suggest optimizations for slow queries"""
        suggestions = []
        
        for query_info in query_analysis:
            query = query_info['query'].lower()
            optimizations = []
            
            # Common optimization patterns
            if 'select *' in query:
                optimizations.append("Replace SELECT * with specific columns")
            
            if 'order by' in query and 'limit' not in query:
                optimizations.append("Consider adding LIMIT to ORDER BY queries")
            
            if 'join' in query and 'where' not in query:
                optimizations.append("Add WHERE conditions to reduce JOIN result set")
            
            if query_info['avg_rows'] > 1000:
                optimizations.append("Consider pagination for large result sets")
            
            if optimizations:
                suggestions.append({
                    'query_snippet': query_info['query'][:100] + '...',
                    'mean_time_ms': query_info['mean_time_ms'],
                    'suggestions': optimizations
                })
        
        return suggestions

class ConnectionPoolOptimizer:
    """Optimizes database connection pooling for concurrent users"""
    
    def __init__(self):
        self.pool_recommendations = {}
        
    def analyze_connection_usage(self):
        """Analyze current connection usage patterns"""
        with connection.cursor() as cursor:
            # Get current connection statistics
            cursor.execute("""
                SELECT 
                    count(*) as total_connections,
                    count(*) FILTER (WHERE state = 'active') as active_connections,
                    count(*) FILTER (WHERE state = 'idle') as idle_connections,
                    max(backend_start) as oldest_connection
                FROM pg_stat_activity 
                WHERE datname = current_database();
            """)
            
            stats = cursor.fetchone()
            
            # Get connection limits
            cursor.execute("SHOW max_connections;")
            max_connections = cursor.fetchone()[0]
            
        connection_analysis = {
            'total_connections': stats[0],
            'active_connections': stats[1],
            'idle_connections': stats[2],
            'oldest_connection': stats[3],
            'max_connections': int(max_connections),
            'utilization_percent': (stats[0] / int(max_connections)) * 100
        }
        
        return connection_analysis
    
    def recommend_pool_settings(self, connection_analysis):
        """Recommend optimal connection pool settings"""
        recommendations = []
        
        utilization = connection_analysis['utilization_percent']
        
        if utilization > 80:
            recommendations.append({
                'setting': 'max_connections',
                'current': connection_analysis['max_connections'],
                'recommended': connection_analysis['max_connections'] * 1.5,
                'reason': 'High connection utilization detected'
            })
        
        if connection_analysis['idle_connections'] > connection_analysis['active_connections'] * 2:
            recommendations.append({
                'setting': 'connection_timeout',
                'recommended': '300 seconds',
                'reason': 'Many idle connections detected'
            })
        
        # Django-specific settings
        recommendations.extend([
            {
                'setting': 'CONN_MAX_AGE',
                'recommended': '300',
                'reason': 'Enable connection reuse for better performance'
            },
            {
                'setting': 'DATABASE_POOL_SIZE',
                'recommended': min(20, connection_analysis['max_connections'] // 4),
                'reason': 'Optimal pool size for concurrent users'
            }
        ])
        
        return recommendations

class PerformanceDashboard:
    """Provides performance monitoring dashboard data"""
    
    def __init__(self):
        self.optimizer = DatabaseIndexOptimizer()
        self.analyzer = QueryPerformanceAnalyzer()
        self.pool_optimizer = ConnectionPoolOptimizer()
    
    def get_dashboard_data(self):
        """Compile comprehensive performance dashboard data"""
        # Index analysis
        index_status = self.optimizer.check_existing_indexes()
        missing_indexes = [idx for idx in index_status if not idx['exists']]
        
        # Query performance
        slow_queries = self.analyzer.analyze_slow_queries()
        optimization_suggestions = self.analyzer.suggest_query_optimizations(slow_queries)
        
        # Connection analysis
        connection_stats = self.pool_optimizer.analyze_connection_usage()
        pool_recommendations = self.pool_optimizer.recommend_pool_settings(connection_stats)
        
        # Performance score calculation
        performance_score = self._calculate_performance_score(
            len(missing_indexes), len(slow_queries), connection_stats['utilization_percent']
        )
        
        return {
            'performance_score': performance_score,
            'index_optimization': {
                'missing_indexes': len(missing_indexes),
                'high_priority_indexes': len([idx for idx in missing_indexes if idx['priority'] == 'HIGH']),
                'recommendations': missing_indexes[:5]  # Top 5 recommendations
            },
            'query_performance': {
                'slow_query_count': len(slow_queries),
                'optimization_suggestions': optimization_suggestions[:3]  # Top 3 suggestions
            },
            'connection_health': {
                'utilization_percent': connection_stats['utilization_percent'],
                'active_connections': connection_stats['active_connections'],
                'recommendations': pool_recommendations
            }
        }
    
    def _calculate_performance_score(self, missing_indexes, slow_queries, connection_utilization):
        """Calculate overall database performance score"""
        score = 100
        
        # Deduct for missing critical indexes
        score -= missing_indexes * 5
        
        # Deduct for slow queries
        score -= slow_queries * 3
        
        # Deduct for high connection utilization
        if connection_utilization > 80:
            score -= 20
        elif connection_utilization > 60:
            score -= 10
        
        return max(0, score)

# Global instance
performance_dashboard = PerformanceDashboard()