"""
Utility functions for working with LogistiLink tracking numbers.
"""
import re
from datetime import datetime
from shipments.models import generate_tracking_number, validate_tracking_number, TrackingNumberCounter

def get_tracking_info(tracking_number):
    """
    Parse a tracking number and extract its components.
    
    Args:
        tracking_number (str): A LogistiLink tracking number
        
    Returns:
        dict: A dictionary containing the components of the tracking number
              or None if the tracking number is invalid
    """
    if not validate_tracking_number(tracking_number):
        return None
    
    # Extract components
    prefix = tracking_number[0:3]
    date_str = tracking_number[3:9]
    serial_number = tracking_number[9:17]
    country_code = tracking_number[17:19]
    
    # Parse date string (YYMMDD)
    try:
        date_obj = datetime.strptime(date_str, "%y%m%d").date()
    except ValueError:
        date_obj = None
    
    return {
        'prefix': prefix,
        'date_code': date_str,
        'date': date_obj,
        'serial_number': serial_number,
        'country_code': country_code,
        'is_valid': True
    }


def format_tracking_number(tracking_number):
    """
    Format a tracking number for user-friendly display.
    
    Args:
        tracking_number (str): A LogistiLink tracking number
        
    Returns:
        str: A formatted tracking number with spaces for readability
    """
    if not tracking_number or len(tracking_number) != 19:
        return tracking_number
    
    # Format: LGL 240514 00004523 AZ (with spaces for readability)
    return f"{tracking_number[0:3]} {tracking_number[3:9]} {tracking_number[9:17]} {tracking_number[17:19]}"


def generate_test_tracking_number(origin_country_code):
    """
    Generate a test tracking number for development/testing purposes.
    
    Args:
        origin_country_code (str): ISO Alpha-2 country code
        
    Returns:
        str: A formatted tracking number
    """
    # Use the regular generator or create a mock one for testing without DB access
    import random
    
    # Fixed platform prefix
    prefix = "LGL"
    
    # Date code (YYMMDD)
    from datetime import date
    today = date.today()
    date_code = today.strftime("%y%m%d")
    
    # Generate a random 8-digit serial for testing
    serial_number = f"{random.randint(1, 99999999):08d}"
    
    # Ensure country code is valid (2 uppercase letters)
    country_code = origin_country_code.upper()
    if not re.match(r'^[A-Z]{2}$', country_code):
        country_code = "XX"  # Default if invalid
    
    # Create the tracking number
    tracking_number = f"{prefix}{date_code}{serial_number}{country_code}"
    
    return tracking_number


def display_tracking_details(tracking_number):
    """
    Return HTML markup for displaying tracking number details in a Streamlit app.
    
    Args:
        tracking_number (str): A LogistiLink tracking number
        
    Returns:
        str: HTML markup for displaying tracking details
    """
    tracking_info = get_tracking_info(tracking_number)
    
    if not tracking_info:
        return "<div style='color:red'>Invalid tracking number format</div>"
    
    formatted_number = format_tracking_number(tracking_number)
    
    # Extract information safely with defaults
    prefix = tracking_info.get('prefix', 'N/A')
    date_code = tracking_info.get('date_code', 'N/A')
    date_obj = tracking_info.get('date')
    formatted_date = date_obj.strftime('%b %d, %Y') if date_obj else 'Invalid date'
    serial_number = tracking_info.get('serial_number', 'N/A')
    country_code = tracking_info.get('country_code', 'N/A')
    
    html = f"""
    <div style='background-color:#f8f9fa; padding:10px; border-radius:5px; margin-bottom:10px;'>
        <h4 style='margin-top:0;'>{formatted_number}</h4>
        <table style='width:100%; border-collapse:collapse;'>
            <tr>
                <td style='padding:5px; font-weight:bold;'>Platform:</td>
                <td style='padding:5px;'>{prefix}</td>
            </tr>
            <tr>
                <td style='padding:5px; font-weight:bold;'>Date Code:</td>
                <td style='padding:5px;'>{date_code} ({formatted_date})</td>
            </tr>
            <tr>
                <td style='padding:5px; font-weight:bold;'>Serial Number:</td>
                <td style='padding:5px;'>{serial_number}</td>
            </tr>
            <tr>
                <td style='padding:5px; font-weight:bold;'>Origin Country:</td>
                <td style='padding:5px;'>{country_code}</td>
            </tr>
        </table>
    </div>
    """
    
    return html