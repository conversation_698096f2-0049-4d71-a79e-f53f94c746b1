# -*- coding: utf-8 -*-
"""
Cloverics Multi-Language Support System
Supports: English, Russian, French, Turkish, Arabic, Chinese, Spanish
"""

import json
import os
from typing import Dict, Optional

class I18nManager:
    """Internationalization manager for Cloverics platform"""
    
    def __init__(self):
        self.supported_languages = {
            'en': 'English',
            'ru': 'Русский',
            'fr': 'Français', 
            'tr': 'Türkçe',
            'ar': 'العربية',
            'zh': '中文',
            'es': 'Español'
        }
        
        self.translations = {}
        self.default_language = 'en'
        self.current_language = 'en'
        
        # Load all translations
        self.load_translations()
    
    def load_translations(self):
        """Load translation files for all supported languages"""
        base_path = os.path.dirname(__file__)
        translations_dir = os.path.join(base_path, 'translations')
        
        # Create translations directory if it doesn't exist
        if not os.path.exists(translations_dir):
            os.makedirs(translations_dir)
        
        for lang_code in self.supported_languages.keys():
            translation_file = os.path.join(translations_dir, f'{lang_code}.json')
            
            if os.path.exists(translation_file):
                try:
                    with open(translation_file, 'r', encoding='utf-8') as f:
                        self.translations[lang_code] = json.load(f)
                except Exception as e:
                    print(f"Error loading translation for {lang_code}: {e}")
                    self.translations[lang_code] = {}
            else:
                self.translations[lang_code] = {}
    
    def set_language(self, language_code: str):
        """Set the current language"""
        if language_code in self.supported_languages:
            self.current_language = language_code
            return True
        return False
    
    def get_language(self) -> str:
        """Get the current language code"""
        return self.current_language
    
    def get_language_name(self, lang_code: str = None) -> str:
        """Get the language name"""
        if lang_code is None:
            lang_code = self.current_language
        return self.supported_languages.get(lang_code, 'English')
    
    def get_supported_languages(self) -> Dict[str, str]:
        """Get all supported languages"""
        return self.supported_languages
    
    def translate(self, key: str, lang_code: str = None, **kwargs) -> str:
        """Translate a key to the specified language"""
        if lang_code is None:
            lang_code = self.current_language
        
        # Get translation from the language file
        translation = self.translations.get(lang_code, {}).get(key)
        
        # Fallback to English if translation not found
        if translation is None and lang_code != 'en':
            translation = self.translations.get('en', {}).get(key)
        
        # Final fallback to the key itself
        if translation is None:
            translation = key
        
        # Format with kwargs if provided
        if kwargs:
            try:
                translation = translation.format(**kwargs)
            except:
                pass
        
        return translation
    
    def t(self, key: str, **kwargs) -> str:
        """Short alias for translate"""
        return self.translate(key, **kwargs)
    
    def get_direction(self, lang_code: str = None) -> str:
        """Get text direction (ltr/rtl) for language"""
        if lang_code is None:
            lang_code = self.current_language
        
        rtl_languages = ['ar']  # Arabic is RTL
        return 'rtl' if lang_code in rtl_languages else 'ltr'
    
    def get_locale(self, lang_code: str = None) -> str:
        """Get locale string for language"""
        if lang_code is None:
            lang_code = self.current_language
        
        locale_map = {
            'en': 'en-US',
            'ru': 'ru-RU',
            'fr': 'fr-FR',
            'tr': 'tr-TR',
            'ar': 'ar-SA',
            'zh': 'zh-CN',
            'es': 'es-ES'
        }
        
        return locale_map.get(lang_code, 'en-US')

# Global instance
i18n = I18nManager()

# Helper functions for templates
def _(key: str, **kwargs) -> str:
    """Template helper function for translations"""
    return i18n.translate(key, **kwargs)

def get_language_selector_data():
    """Get language selector data for templates"""
    return {
        'current_language': i18n.get_language(),
        'current_language_name': i18n.get_language_name(),
        'supported_languages': i18n.get_supported_languages(),
        'direction': i18n.get_direction()
    }

def format_currency(amount: float, currency: str = 'USD', lang_code: str = None) -> str:
    """Format currency according to language/locale"""
    if lang_code is None:
        lang_code = i18n.get_language()
    
    # Simple currency formatting based on language
    currency_symbols = {
        'USD': '$',
        'EUR': '€',
        'GBP': '£',
        'RUB': '₽',
        'TRY': '₺',
        'CNY': '¥',
        'SAR': 'SR',
    }
    
    symbol = currency_symbols.get(currency, '$')
    
    # Format based on language conventions
    if lang_code == 'ru':
        return f"{amount:,.2f} {symbol}"
    elif lang_code == 'fr':
        return f"{amount:,.2f} {symbol}".replace(',', ' ').replace('.', ',')
    elif lang_code == 'ar':
        return f"{symbol} {amount:,.2f}"
    else:
        return f"{symbol}{amount:,.2f}"

def format_date(date_obj, lang_code: str = None) -> str:
    """Format date according to language/locale"""
    if lang_code is None:
        lang_code = i18n.get_language()
    
    if not date_obj:
        return ""
    
    # Basic date formatting by language
    try:
        if lang_code == 'ru':
            return date_obj.strftime("%d.%m.%Y")
        elif lang_code == 'fr':
            return date_obj.strftime("%d/%m/%Y")
        elif lang_code == 'tr':
            return date_obj.strftime("%d.%m.%Y")
        elif lang_code == 'ar':
            return date_obj.strftime("%d/%m/%Y")
        elif lang_code == 'zh':
            return date_obj.strftime("%Y年%m月%d日")
        elif lang_code == 'es':
            return date_obj.strftime("%d/%m/%Y")
        else:  # English default
            return date_obj.strftime("%m/%d/%Y")
    except:
        return str(date_obj)