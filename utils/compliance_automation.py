"""
Compliance Automation System for Cloverics Platform
Advanced automated compliance management, regulatory monitoring, and audit trail generation
"""

import json
import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from enum import Enum
import hashlib
import re
from decimal import Decimal

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ComplianceStatus(Enum):
    COMPLIANT = "compliant"
    NON_COMPLIANT = "non_compliant"
    PENDING_REVIEW = "pending_review"
    REQUIRES_ACTION = "requires_action"
    EXEMPTED = "exempted"

class ComplianceFramework(Enum):
    GDPR = "gdpr"
    SOC2 = "soc2"
    ISO27001 = "iso27001"
    HIPAA = "hipaa"
    PCI_DSS = "pci_dss"
    CUSTOMS_REGULATIONS = "customs_regulations"
    TRANSPORTATION_REGULATIONS = "transportation_regulations"
    INTERNATIONAL_TRADE = "international_trade"

class ComplianceType(Enum):
    DATA_PROTECTION = "data_protection"
    SECURITY_CONTROLS = "security_controls"
    AUDIT_LOGGING = "audit_logging"
    ACCESS_CONTROLS = "access_controls"
    INCIDENT_RESPONSE = "incident_response"
    BUSINESS_CONTINUITY = "business_continuity"
    VENDOR_MANAGEMENT = "vendor_management"
    TRADE_COMPLIANCE = "trade_compliance"
    CUSTOMS_COMPLIANCE = "customs_compliance"
    TRANSPORTATION_SAFETY = "transportation_safety"

class RiskLevel(Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

@dataclass
class ComplianceRule:
    """Represents a compliance rule with automated validation"""
    id: str
    name: str
    description: str
    framework: ComplianceFramework
    compliance_type: ComplianceType
    risk_level: RiskLevel
    validation_criteria: Dict[str, Any]
    remediation_steps: List[str]
    automated_check: bool = True
    frequency: str = "daily"  # daily, weekly, monthly, quarterly
    last_checked: Optional[datetime] = None
    status: ComplianceStatus = ComplianceStatus.PENDING_REVIEW
    
    def __post_init__(self):
        if not self.last_checked:
            self.last_checked = datetime.now(timezone.utc)

@dataclass
class ComplianceViolation:
    """Represents a compliance violation with remediation tracking"""
    id: str
    rule_id: str
    description: str
    severity: RiskLevel
    detected_at: datetime
    entity_type: str  # shipment, user, contract, etc.
    entity_id: str
    current_status: ComplianceStatus
    remediation_actions: List[str] = field(default_factory=list)
    assigned_to: Optional[str] = None
    due_date: Optional[datetime] = None
    resolved_at: Optional[datetime] = None
    audit_trail: List[Dict] = field(default_factory=list)

@dataclass
class ComplianceAuditRecord:
    """Audit trail record for compliance activities"""
    id: str
    timestamp: datetime
    event_type: str
    user_id: Optional[str]
    entity_type: str
    entity_id: str
    action: str
    previous_state: Optional[Dict]
    new_state: Optional[Dict]
    compliance_framework: ComplianceFramework
    metadata: Dict = field(default_factory=dict)

class ComplianceRuleEngine:
    """Automated compliance rule validation engine"""
    
    def __init__(self):
        self.rules = self._initialize_compliance_rules()
        self.violations = []
        self.audit_records = []
    
    def _initialize_compliance_rules(self) -> Dict[str, ComplianceRule]:
        """Initialize comprehensive compliance rules"""
        rules = {}
        
        # GDPR Rules
        rules['gdpr_data_encryption'] = ComplianceRule(
            id='gdpr_data_encryption',
            name='Data Encryption at Rest and in Transit',
            description='All personal data must be encrypted using AES-256 or equivalent',
            framework=ComplianceFramework.GDPR,
            compliance_type=ComplianceType.DATA_PROTECTION,
            risk_level=RiskLevel.HIGH,
            validation_criteria={
                'encryption_algorithm': 'AES-256',
                'ssl_tls_version': 'TLSv1.3',
                'database_encryption': True,
                'backup_encryption': True
            },
            remediation_steps=[
                'Enable database encryption',
                'Implement TLS 1.3 for data in transit',
                'Encrypt backup files',
                'Review encryption key management'
            ]
        )
        
        rules['gdpr_consent_management'] = ComplianceRule(
            id='gdpr_consent_management',
            name='User Consent Management',
            description='Explicit consent must be obtained for data processing',
            framework=ComplianceFramework.GDPR,
            compliance_type=ComplianceType.DATA_PROTECTION,
            risk_level=RiskLevel.HIGH,
            validation_criteria={
                'consent_recorded': True,
                'consent_specific': True,
                'consent_withdrawable': True,
                'processing_lawful_basis': True
            },
            remediation_steps=[
                'Implement consent management system',
                'Provide clear consent withdrawal mechanism',
                'Document lawful basis for processing',
                'Review consent records regularly'
            ]
        )
        
        rules['gdpr_data_retention'] = ComplianceRule(
            id='gdpr_data_retention',
            name='Data Retention Policy',
            description='Personal data must not be kept longer than necessary',
            framework=ComplianceFramework.GDPR,
            compliance_type=ComplianceType.DATA_PROTECTION,
            risk_level=RiskLevel.MEDIUM,
            validation_criteria={
                'retention_policy_defined': True,
                'automated_deletion': True,
                'retention_period_documented': True,
                'data_minimization': True
            },
            remediation_steps=[
                'Define data retention policy',
                'Implement automated data deletion',
                'Document retention periods',
                'Regular data minimization reviews'
            ]
        )
        
        # SOC2 Rules
        rules['soc2_access_controls'] = ComplianceRule(
            id='soc2_access_controls',
            name='Logical Access Controls',
            description='System access must be restricted to authorized users',
            framework=ComplianceFramework.SOC2,
            compliance_type=ComplianceType.ACCESS_CONTROLS,
            risk_level=RiskLevel.HIGH,
            validation_criteria={
                'multi_factor_authentication': True,
                'role_based_access': True,
                'access_review_process': True,
                'privileged_access_management': True
            },
            remediation_steps=[
                'Implement multi-factor authentication',
                'Establish role-based access controls',
                'Conduct regular access reviews',
                'Manage privileged access accounts'
            ]
        )
        
        rules['soc2_system_monitoring'] = ComplianceRule(
            id='soc2_system_monitoring',
            name='System Monitoring and Logging',
            description='System activities must be monitored and logged',
            framework=ComplianceFramework.SOC2,
            compliance_type=ComplianceType.AUDIT_LOGGING,
            risk_level=RiskLevel.MEDIUM,
            validation_criteria={
                'comprehensive_logging': True,
                'log_integrity_protection': True,
                'real_time_monitoring': True,
                'incident_detection': True
            },
            remediation_steps=[
                'Enable comprehensive logging',
                'Implement log integrity protection',
                'Set up real-time monitoring',
                'Deploy incident detection system'
            ]
        )
        
        # Trade Compliance Rules
        rules['trade_sanctions_screening'] = ComplianceRule(
            id='trade_sanctions_screening',
            name='Trade Sanctions Screening',
            description='All parties must be screened against sanctions lists',
            framework=ComplianceFramework.INTERNATIONAL_TRADE,
            compliance_type=ComplianceType.TRADE_COMPLIANCE,
            risk_level=RiskLevel.CRITICAL,
            validation_criteria={
                'sanctions_list_screening': True,
                'automated_screening': True,
                'screening_documentation': True,
                'regular_list_updates': True
            },
            remediation_steps=[
                'Implement sanctions screening system',
                'Automate screening process',
                'Document screening results',
                'Update sanctions lists regularly'
            ]
        )
        
        rules['export_control_compliance'] = ComplianceRule(
            id='export_control_compliance',
            name='Export Control Compliance',
            description='Export/import activities must comply with regulations',
            framework=ComplianceFramework.INTERNATIONAL_TRADE,
            compliance_type=ComplianceType.TRADE_COMPLIANCE,
            risk_level=RiskLevel.HIGH,
            validation_criteria={
                'export_license_verification': True,
                'classification_accuracy': True,
                'destination_control': True,
                'end_user_verification': True
            },
            remediation_steps=[
                'Verify export/import licenses',
                'Ensure accurate classification',
                'Implement destination controls',
                'Verify end users'
            ]
        )
        
        # Customs Compliance Rules
        rules['customs_declaration_accuracy'] = ComplianceRule(
            id='customs_declaration_accuracy',
            name='Customs Declaration Accuracy',
            description='All customs declarations must be accurate and complete',
            framework=ComplianceFramework.CUSTOMS_REGULATIONS,
            compliance_type=ComplianceType.CUSTOMS_COMPLIANCE,
            risk_level=RiskLevel.HIGH,
            validation_criteria={
                'valuation_accuracy': True,
                'classification_correctness': True,
                'origin_verification': True,
                'documentation_completeness': True
            },
            remediation_steps=[
                'Verify customs valuation',
                'Ensure correct classification',
                'Verify country of origin',
                'Complete all documentation'
            ]
        )
        
        # Transportation Safety Rules
        rules['transportation_safety_compliance'] = ComplianceRule(
            id='transportation_safety_compliance',
            name='Transportation Safety Compliance',
            description='Transportation activities must comply with safety regulations',
            framework=ComplianceFramework.TRANSPORTATION_REGULATIONS,
            compliance_type=ComplianceType.TRANSPORTATION_SAFETY,
            risk_level=RiskLevel.HIGH,
            validation_criteria={
                'driver_qualification': True,
                'vehicle_safety_inspection': True,
                'cargo_securing': True,
                'hazmat_compliance': True
            },
            remediation_steps=[
                'Verify driver qualifications',
                'Conduct vehicle safety inspections',
                'Ensure proper cargo securing',
                'Comply with hazmat regulations'
            ]
        )
        
        return rules
    
    def evaluate_compliance(self, entity_type: str, entity_data: Dict) -> List[ComplianceViolation]:
        """Evaluate compliance for a specific entity"""
        violations = []
        
        try:
            for rule_id, rule in self.rules.items():
                if self._should_evaluate_rule(rule, entity_type, entity_data):
                    violation = self._evaluate_rule(rule, entity_type, entity_data)
                    if violation:
                        violations.append(violation)
                        self.violations.append(violation)
            
            return violations
            
        except Exception as e:
            logger.error(f"Error evaluating compliance: {str(e)}")
            return []
    
    def _should_evaluate_rule(self, rule: ComplianceRule, entity_type: str, entity_data: Dict) -> bool:
        """Determine if a rule should be evaluated for the given entity"""
        # Rule applicability logic
        if rule.compliance_type == ComplianceType.DATA_PROTECTION:
            return entity_type in ['user', 'customer', 'shipment']
        elif rule.compliance_type == ComplianceType.TRADE_COMPLIANCE:
            return entity_type in ['shipment', 'customs_declaration']
        elif rule.compliance_type == ComplianceType.CUSTOMS_COMPLIANCE:
            return entity_type in ['customs_declaration', 'shipment']
        elif rule.compliance_type == ComplianceType.TRANSPORTATION_SAFETY:
            return entity_type in ['shipment', 'driver', 'vehicle']
        else:
            return True
    
    def _evaluate_rule(self, rule: ComplianceRule, entity_type: str, entity_data: Dict) -> Optional[ComplianceViolation]:
        """Evaluate a specific compliance rule"""
        try:
            # Rule-specific evaluation logic
            if rule.id == 'gdpr_data_encryption':
                return self._evaluate_data_encryption(rule, entity_type, entity_data)
            elif rule.id == 'gdpr_consent_management':
                return self._evaluate_consent_management(rule, entity_type, entity_data)
            elif rule.id == 'trade_sanctions_screening':
                return self._evaluate_sanctions_screening(rule, entity_type, entity_data)
            elif rule.id == 'customs_declaration_accuracy':
                return self._evaluate_customs_accuracy(rule, entity_type, entity_data)
            elif rule.id == 'transportation_safety_compliance':
                return self._evaluate_transportation_safety(rule, entity_type, entity_data)
            
            return None
            
        except Exception as e:
            logger.error(f"Error evaluating rule {rule.id}: {str(e)}")
            return None
    
    def _evaluate_data_encryption(self, rule: ComplianceRule, entity_type: str, entity_data: Dict) -> Optional[ComplianceViolation]:
        """Evaluate data encryption compliance"""
        issues = []
        
        # Check encryption status
        if not entity_data.get('encrypted_at_rest', False):
            issues.append('Data not encrypted at rest')
        
        if not entity_data.get('encrypted_in_transit', False):
            issues.append('Data not encrypted in transit')
        
        encryption_algorithm = entity_data.get('encryption_algorithm', '')
        if encryption_algorithm not in ['AES-256', 'AES-128']:
            issues.append(f'Weak encryption algorithm: {encryption_algorithm}')
        
        if issues:
            return ComplianceViolation(
                id=self._generate_violation_id(),
                rule_id=rule.id,
                description=f"Data encryption violations: {', '.join(issues)}",
                severity=rule.risk_level,
                detected_at=datetime.now(timezone.utc),
                entity_type=entity_type,
                entity_id=entity_data.get('id', 'unknown'),
                current_status=ComplianceStatus.NON_COMPLIANT,
                remediation_actions=rule.remediation_steps,
                due_date=datetime.now(timezone.utc).replace(hour=23, minute=59, second=59)
            )
        
        return None
    
    def _evaluate_consent_management(self, rule: ComplianceRule, entity_type: str, entity_data: Dict) -> Optional[ComplianceViolation]:
        """Evaluate consent management compliance"""
        if entity_type != 'user':
            return None
        
        issues = []
        
        if not entity_data.get('consent_given', False):
            issues.append('No consent record found')
        
        if not entity_data.get('consent_specific', False):
            issues.append('Consent not specific enough')
        
        if not entity_data.get('consent_withdrawable', False):
            issues.append('Consent withdrawal mechanism not available')
        
        if issues:
            return ComplianceViolation(
                id=self._generate_violation_id(),
                rule_id=rule.id,
                description=f"Consent management violations: {', '.join(issues)}",
                severity=rule.risk_level,
                detected_at=datetime.now(timezone.utc),
                entity_type=entity_type,
                entity_id=entity_data.get('id', 'unknown'),
                current_status=ComplianceStatus.NON_COMPLIANT,
                remediation_actions=rule.remediation_steps
            )
        
        return None
    
    def _evaluate_sanctions_screening(self, rule: ComplianceRule, entity_type: str, entity_data: Dict) -> Optional[ComplianceViolation]:
        """Evaluate sanctions screening compliance"""
        if entity_type not in ['shipment', 'user', 'customer']:
            return None
        
        issues = []
        
        if not entity_data.get('sanctions_screened', False):
            issues.append('Entity not screened against sanctions lists')
        
        screening_date = entity_data.get('sanctions_screening_date')
        if screening_date:
            days_since_screening = (datetime.now(timezone.utc) - screening_date).days
            if days_since_screening > 30:
                issues.append(f'Sanctions screening outdated ({days_since_screening} days)')
        
        if entity_data.get('sanctions_match_found', False):
            issues.append('Potential sanctions match requires review')
        
        if issues:
            return ComplianceViolation(
                id=self._generate_violation_id(),
                rule_id=rule.id,
                description=f"Sanctions screening violations: {', '.join(issues)}",
                severity=RiskLevel.CRITICAL,
                detected_at=datetime.now(timezone.utc),
                entity_type=entity_type,
                entity_id=entity_data.get('id', 'unknown'),
                current_status=ComplianceStatus.REQUIRES_ACTION,
                remediation_actions=rule.remediation_steps
            )
        
        return None
    
    def _evaluate_customs_accuracy(self, rule: ComplianceRule, entity_type: str, entity_data: Dict) -> Optional[ComplianceViolation]:
        """Evaluate customs declaration accuracy"""
        if entity_type not in ['customs_declaration', 'shipment']:
            return None
        
        issues = []
        
        # Check valuation accuracy
        declared_value = entity_data.get('declared_value', 0)
        estimated_value = entity_data.get('estimated_value', 0)
        if declared_value and estimated_value:
            variance = abs(declared_value - estimated_value) / estimated_value
            if variance > 0.1:  # 10% variance threshold
                issues.append(f'Valuation variance too high: {variance*100:.1f}%')
        
        # Check classification
        if not entity_data.get('hs_code'):
            issues.append('Missing HS code classification')
        
        # Check origin verification
        if not entity_data.get('origin_verified', False):
            issues.append('Country of origin not verified')
        
        # Check documentation completeness
        required_docs = entity_data.get('required_documents', [])
        provided_docs = entity_data.get('provided_documents', [])
        missing_docs = set(required_docs) - set(provided_docs)
        if missing_docs:
            issues.append(f'Missing documents: {", ".join(missing_docs)}')
        
        if issues:
            return ComplianceViolation(
                id=self._generate_violation_id(),
                rule_id=rule.id,
                description=f"Customs declaration violations: {', '.join(issues)}",
                severity=rule.risk_level,
                detected_at=datetime.now(timezone.utc),
                entity_type=entity_type,
                entity_id=entity_data.get('id', 'unknown'),
                current_status=ComplianceStatus.NON_COMPLIANT,
                remediation_actions=rule.remediation_steps
            )
        
        return None
    
    def _evaluate_transportation_safety(self, rule: ComplianceRule, entity_type: str, entity_data: Dict) -> Optional[ComplianceViolation]:
        """Evaluate transportation safety compliance"""
        if entity_type not in ['shipment', 'driver', 'vehicle']:
            return None
        
        issues = []
        
        # Check driver qualification
        if entity_type in ['shipment', 'driver']:
            if not entity_data.get('driver_qualified', False):
                issues.append('Driver qualification not verified')
            
            license_expiry = entity_data.get('license_expiry_date')
            if license_expiry and license_expiry < datetime.now(timezone.utc):
                issues.append('Driver license expired')
        
        # Check vehicle safety
        if entity_type in ['shipment', 'vehicle']:
            if not entity_data.get('vehicle_inspected', False):
                issues.append('Vehicle safety inspection not completed')
            
            inspection_date = entity_data.get('last_inspection_date')
            if inspection_date:
                days_since_inspection = (datetime.now(timezone.utc) - inspection_date).days
                if days_since_inspection > 365:
                    issues.append(f'Vehicle inspection overdue ({days_since_inspection} days)')
        
        # Check hazmat compliance
        if entity_data.get('hazmat_cargo', False):
            if not entity_data.get('hazmat_certified', False):
                issues.append('Hazmat certification required for dangerous goods')
        
        if issues:
            return ComplianceViolation(
                id=self._generate_violation_id(),
                rule_id=rule.id,
                description=f"Transportation safety violations: {', '.join(issues)}",
                severity=rule.risk_level,
                detected_at=datetime.now(timezone.utc),
                entity_type=entity_type,
                entity_id=entity_data.get('id', 'unknown'),
                current_status=ComplianceStatus.NON_COMPLIANT,
                remediation_actions=rule.remediation_steps
            )
        
        return None
    
    def _generate_violation_id(self) -> str:
        """Generate unique violation ID"""
        timestamp = datetime.now(timezone.utc).strftime('%Y%m%d%H%M%S')
        random_suffix = hashlib.md5(str(datetime.now().timestamp()).encode()).hexdigest()[:8]
        return f"VIOL-{timestamp}-{random_suffix.upper()}"

class ComplianceReportingEngine:
    """Advanced compliance reporting and analytics"""
    
    def __init__(self, rule_engine: ComplianceRuleEngine):
        self.rule_engine = rule_engine
    
    def generate_compliance_dashboard(self, timeframe_days: int = 30) -> Dict:
        """Generate comprehensive compliance dashboard"""
        try:
            # Get recent violations
            cutoff_date = datetime.now(timezone.utc) - timedelta(days=timeframe_days)
            recent_violations = [
                v for v in self.rule_engine.violations 
                if v.detected_at >= cutoff_date
            ]
            
            # Calculate metrics
            total_violations = len(recent_violations)
            critical_violations = len([v for v in recent_violations if v.severity == RiskLevel.CRITICAL])
            high_violations = len([v for v in recent_violations if v.severity == RiskLevel.HIGH])
            resolved_violations = len([v for v in recent_violations if v.current_status == ComplianceStatus.COMPLIANT])
            
            # Framework breakdown
            framework_breakdown = {}
            for rule in self.rule_engine.rules.values():
                framework = rule.framework.value
                if framework not in framework_breakdown:
                    framework_breakdown[framework] = {
                        'total_rules': 0,
                        'compliant': 0,
                        'violations': 0
                    }
                framework_breakdown[framework]['total_rules'] += 1
                
                # Count violations for this framework
                framework_violations = [
                    v for v in recent_violations 
                    if v.rule_id == rule.id
                ]
                framework_breakdown[framework]['violations'] += len(framework_violations)
                
                if not framework_violations:
                    framework_breakdown[framework]['compliant'] += 1
            
            # Calculate compliance scores
            compliance_scores = {}
            for framework, data in framework_breakdown.items():
                if data['total_rules'] > 0:
                    compliance_scores[framework] = (
                        data['compliant'] / data['total_rules'] * 100
                    )
            
            # Top violation types
            violation_types = {}
            for violation in recent_violations:
                rule = self.rule_engine.rules.get(violation.rule_id)
                if rule:
                    compliance_type = rule.compliance_type.value
                    violation_types[compliance_type] = violation_types.get(compliance_type, 0) + 1
            
            # Trend analysis
            trend_data = self._calculate_compliance_trends(recent_violations)
            
            return {
                'success': True,
                'dashboard': {
                    'summary': {
                        'total_violations': total_violations,
                        'critical_violations': critical_violations,
                        'high_violations': high_violations,
                        'resolved_violations': resolved_violations,
                        'resolution_rate': (resolved_violations / total_violations * 100) if total_violations > 0 else 100,
                        'overall_compliance_score': sum(compliance_scores.values()) / len(compliance_scores) if compliance_scores else 100
                    },
                    'framework_breakdown': framework_breakdown,
                    'compliance_scores': compliance_scores,
                    'violation_types': violation_types,
                    'trend_data': trend_data,
                    'recent_violations': [
                        {
                            'id': v.id,
                            'rule_id': v.rule_id,
                            'description': v.description,
                            'severity': v.severity.value,
                            'entity_type': v.entity_type,
                            'entity_id': v.entity_id,
                            'status': v.current_status.value,
                            'detected_at': v.detected_at.isoformat()
                        }
                        for v in recent_violations[:10]  # Latest 10 violations
                    ]
                }
            }
            
        except Exception as e:
            logger.error(f"Error generating compliance dashboard: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _calculate_compliance_trends(self, violations: List[ComplianceViolation]) -> Dict:
        """Calculate compliance trends over time"""
        # Group violations by date
        daily_violations = {}
        for violation in violations:
            date_key = violation.detected_at.date().isoformat()
            if date_key not in daily_violations:
                daily_violations[date_key] = 0
            daily_violations[date_key] += 1
        
        # Calculate 7-day moving average
        dates = sorted(daily_violations.keys())
        trend_data = []
        
        for i, date in enumerate(dates):
            if i >= 6:  # Need at least 7 days for moving average
                recent_dates = dates[i-6:i+1]
                avg_violations = sum(daily_violations[d] for d in recent_dates) / 7
                trend_data.append({
                    'date': date,
                    'violations': daily_violations[date],
                    'moving_average': round(avg_violations, 2)
                })
        
        return {
            'daily_violations': daily_violations,
            'trend_data': trend_data
        }

class ComplianceAutomationEngine:
    """Main compliance automation orchestrator"""
    
    def __init__(self):
        self.rule_engine = ComplianceRuleEngine()
        self.reporting_engine = ComplianceReportingEngine(self.rule_engine)
        self.audit_trail = []
    
    def run_compliance_scan(self, entities: List[Dict]) -> Dict:
        """Run comprehensive compliance scan on entities"""
        try:
            all_violations = []
            scan_results = {}
            
            for entity in entities:
                entity_type = entity.get('type', 'unknown')
                entity_id = entity.get('id', 'unknown')
                
                # Run compliance evaluation
                violations = self.rule_engine.evaluate_compliance(entity_type, entity)
                all_violations.extend(violations)
                
                scan_results[entity_id] = {
                    'entity_type': entity_type,
                    'violations_count': len(violations),
                    'compliance_status': 'compliant' if not violations else 'non_compliant',
                    'violations': [
                        {
                            'id': v.id,
                            'rule_id': v.rule_id,
                            'description': v.description,
                            'severity': v.severity.value,
                            'status': v.current_status.value
                        }
                        for v in violations
                    ]
                }
                
                # Log audit record
                self._log_audit_record(
                    event_type='compliance_scan',
                    entity_type=entity_type,
                    entity_id=entity_id,
                    action='automated_compliance_scan',
                    metadata={
                        'violations_found': len(violations),
                        'scan_timestamp': datetime.now(timezone.utc).isoformat()
                    }
                )
            
            # Generate summary
            summary = {
                'total_entities_scanned': len(entities),
                'total_violations_found': len(all_violations),
                'critical_violations': len([v for v in all_violations if v.severity == RiskLevel.CRITICAL]),
                'high_violations': len([v for v in all_violations if v.severity == RiskLevel.HIGH]),
                'medium_violations': len([v for v in all_violations if v.severity == RiskLevel.MEDIUM]),
                'low_violations': len([v for v in all_violations if v.severity == RiskLevel.LOW]),
                'overall_compliance_rate': (
                    (len(entities) - len([r for r in scan_results.values() if r['compliance_status'] == 'non_compliant'])) / 
                    len(entities) * 100
                ) if entities else 100
            }
            
            return {
                'success': True,
                'scan_id': self._generate_scan_id(),
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'summary': summary,
                'results': scan_results
            }
            
        except Exception as e:
            logger.error(f"Error running compliance scan: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_compliance_dashboard(self, timeframe_days: int = 30) -> Dict:
        """Get comprehensive compliance dashboard"""
        return self.reporting_engine.generate_compliance_dashboard(timeframe_days)
    
    def get_violation_details(self, violation_id: str) -> Dict:
        """Get detailed information about a specific violation"""
        try:
            violation = next(
                (v for v in self.rule_engine.violations if v.id == violation_id), 
                None
            )
            
            if not violation:
                return {
                    'success': False,
                    'error': 'Violation not found'
                }
            
            rule = self.rule_engine.rules.get(violation.rule_id)
            
            return {
                'success': True,
                'violation': {
                    'id': violation.id,
                    'rule_id': violation.rule_id,
                    'rule_name': rule.name if rule else 'Unknown Rule',
                    'description': violation.description,
                    'severity': violation.severity.value,
                    'entity_type': violation.entity_type,
                    'entity_id': violation.entity_id,
                    'current_status': violation.current_status.value,
                    'detected_at': violation.detected_at.isoformat(),
                    'due_date': violation.due_date.isoformat() if violation.due_date else None,
                    'resolved_at': violation.resolved_at.isoformat() if violation.resolved_at else None,
                    'remediation_actions': violation.remediation_actions,
                    'assigned_to': violation.assigned_to,
                    'audit_trail': violation.audit_trail
                }
            }
            
        except Exception as e:
            logger.error(f"Error getting violation details: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def resolve_violation(self, violation_id: str, resolution_notes: str, user_id: str) -> Dict:
        """Mark a violation as resolved"""
        try:
            violation = next(
                (v for v in self.rule_engine.violations if v.id == violation_id), 
                None
            )
            
            if not violation:
                return {
                    'success': False,
                    'error': 'Violation not found'
                }
            
            # Update violation status
            violation.current_status = ComplianceStatus.COMPLIANT
            violation.resolved_at = datetime.now(timezone.utc)
            violation.audit_trail.append({
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'action': 'resolved',
                'user_id': user_id,
                'notes': resolution_notes
            })
            
            # Log audit record
            self._log_audit_record(
                event_type='violation_resolution',
                entity_type=violation.entity_type,
                entity_id=violation.entity_id,
                action='violation_resolved',
                user_id=user_id,
                metadata={
                    'violation_id': violation_id,
                    'resolution_notes': resolution_notes
                }
            )
            
            return {
                'success': True,
                'message': 'Violation resolved successfully'
            }
            
        except Exception as e:
            logger.error(f"Error resolving violation: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _log_audit_record(self, event_type: str, entity_type: str, entity_id: str, 
                         action: str, user_id: str = None, metadata: Dict = None):
        """Log audit record for compliance activities"""
        record = ComplianceAuditRecord(
            id=self._generate_audit_id(),
            timestamp=datetime.now(timezone.utc),
            event_type=event_type,
            user_id=user_id,
            entity_type=entity_type,
            entity_id=entity_id,
            action=action,
            previous_state=None,
            new_state=None,
            compliance_framework=ComplianceFramework.GDPR,  # Default
            metadata=metadata or {}
        )
        
        self.audit_trail.append(record)
    
    def _generate_scan_id(self) -> str:
        """Generate unique scan ID"""
        timestamp = datetime.now(timezone.utc).strftime('%Y%m%d%H%M%S')
        return f"SCAN-{timestamp}"
    
    def _generate_audit_id(self) -> str:
        """Generate unique audit ID"""
        timestamp = datetime.now(timezone.utc).strftime('%Y%m%d%H%M%S')
        random_suffix = hashlib.md5(str(datetime.now().timestamp()).encode()).hexdigest()[:8]
        return f"AUDIT-{timestamp}-{random_suffix.upper()}"

# Global instance
compliance_automation_engine = ComplianceAutomationEngine()