"""
Cloverics Advanced Threat Detection Module

This module provides real-time threat detection, anomaly monitoring,
and automated security response capabilities.
"""

import logging
import hashlib
from datetime import datetime, timedelta
from collections import defaultdict, deque
from utils.security import log_user_action

# Configure threat detection logging
threat_logger = logging.getLogger('cloverics.threat_detection')

class ThreatDetectionEngine:
    """Advanced threat detection and monitoring"""
    
    def __init__(self):
        self.user_behavior_profiles = defaultdict(dict)
        self.ip_reputation = defaultdict(int)
        self.failed_attempts = defaultdict(deque)
        self.suspicious_patterns = []
        
        # Threat detection rules
        self.detection_rules = {
            "brute_force": {
                "threshold": 10,
                "window_minutes": 5,
                "severity": "HIGH"
            },
            "rapid_requests": {
                "threshold": 100,
                "window_minutes": 1,
                "severity": "MEDIUM"
            },
            "unusual_hours": {
                "start_hour": 2,
                "end_hour": 6,
                "severity": "LOW"
            },
            "geographic_anomaly": {
                "max_distance_km": 1000,
                "time_window_hours": 1,
                "severity": "HIGH"
            }
        }
    
    def analyze_login_attempt(self, user_id, ip_address, success, user_agent=None):
        """
        Analyze login attempt for suspicious patterns
        
        Args:
            user_id: ID of the user attempting login
            ip_address: IP address of the attempt
            success: Whether login was successful
            user_agent: Browser user agent string
            
        Returns:
            dict: Threat analysis result
        """
        current_time = datetime.now()
        threat_score = 0
        threats_detected = []
        
        # Track failed attempts for brute force detection
        if not success:
            self.failed_attempts[user_id].append(current_time)
            
            # Remove old attempts outside detection window
            cutoff_time = current_time - timedelta(minutes=self.detection_rules["brute_force"]["window_minutes"])
            while self.failed_attempts[user_id] and self.failed_attempts[user_id][0] < cutoff_time:
                self.failed_attempts[user_id].popleft()
            
            # Check for brute force attack
            if len(self.failed_attempts[user_id]) >= self.detection_rules["brute_force"]["threshold"]:
                threat_score += 50
                threats_detected.append("brute_force_attack")
                threat_logger.warning(f"Brute force attack detected for user {user_id} from IP {ip_address}")
        
        # Check for unusual login hours
        if self._is_unusual_hour(current_time):
            threat_score += 15
            threats_detected.append("unusual_hour_login")
        
        # Check IP reputation
        ip_reputation = self.ip_reputation.get(ip_address, 0)
        if ip_reputation < -10:
            threat_score += 30
            threats_detected.append("suspicious_ip")
        
        # Update IP reputation based on success/failure
        if success:
            self.ip_reputation[ip_address] += 1
        else:
            self.ip_reputation[ip_address] -= 2
        
        # Log threat analysis
        log_user_action("threat_analysis", user_id, {
            "ip_address": ip_address,
            "threat_score": threat_score,
            "threats": threats_detected,
            "success": success
        })
        
        return {
            "threat_score": threat_score,
            "threats_detected": threats_detected,
            "risk_level": self._calculate_risk_level(threat_score)
        }
    
    def analyze_user_behavior(self, user_id, action, metadata=None):
        """
        Analyze user behavior for anomalies
        
        Args:
            user_id: ID of the user
            action: Action being performed
            metadata: Additional context data
            
        Returns:
            dict: Behavior analysis result
        """
        current_time = datetime.now()
        
        # Initialize user profile if not exists
        if user_id not in self.user_behavior_profiles:
            self.user_behavior_profiles[user_id] = {
                "actions": defaultdict(int),
                "typical_hours": set(),
                "locations": set(),
                "last_seen": None
            }
        
        profile = self.user_behavior_profiles[user_id]
        anomalies = []
        anomaly_score = 0
        
        # Track action frequency
        profile["actions"][action] += 1
        
        # Check for unusual action patterns
        if action in ["payment", "contract_signing"] and profile["actions"][action] > 10:
            # Too many financial actions
            anomalies.append("excessive_financial_activity")
            anomaly_score += 25
        
        if action == "data_export" and profile["actions"][action] > 5:
            # Too many data exports
            anomalies.append("excessive_data_access")
            anomaly_score += 30
        
        # Track typical hours
        current_hour = current_time.hour
        profile["typical_hours"].add(current_hour)
        
        # Check for session hijacking indicators
        if profile["last_seen"]:
            time_gap = current_time - profile["last_seen"]
            if time_gap.total_seconds() < 1:  # Too rapid actions
                anomalies.append("rapid_actions")
                anomaly_score += 20
        
        profile["last_seen"] = current_time
        
        # Log behavior analysis
        if anomalies:
            threat_logger.info(f"Behavior anomalies detected for user {user_id}: {anomalies}")
            log_user_action("behavior_anomaly", user_id, {
                "action": action,
                "anomalies": anomalies,
                "score": anomaly_score
            })
        
        return {
            "anomaly_score": anomaly_score,
            "anomalies": anomalies,
            "risk_level": self._calculate_risk_level(anomaly_score)
        }
    
    def detect_data_exfiltration(self, user_id, data_type, volume):
        """
        Detect potential data exfiltration attempts
        
        Args:
            user_id: ID of the user accessing data
            data_type: Type of data being accessed
            volume: Amount of data being accessed
            
        Returns:
            dict: Exfiltration analysis result
        """
        # Define normal access patterns
        normal_volumes = {
            "shipment_data": 50,   # Normal: up to 50 shipments at once
            "customer_data": 20,   # Normal: up to 20 customers at once
            "payment_data": 10,    # Normal: up to 10 payments at once
            "contract_data": 5     # Normal: up to 5 contracts at once
        }
        
        normal_volume = normal_volumes.get(data_type, 10)
        exfiltration_score = 0
        alerts = []
        
        if volume > normal_volume * 5:  # 5x normal volume
            exfiltration_score += 40
            alerts.append("massive_data_access")
            threat_logger.warning(f"Massive data access detected: User {user_id} accessed {volume} {data_type}")
        
        elif volume > normal_volume * 2:  # 2x normal volume
            exfiltration_score += 20
            alerts.append("elevated_data_access")
        
        # Log exfiltration analysis
        if alerts:
            log_user_action("exfiltration_check", user_id, {
                "data_type": data_type,
                "volume": volume,
                "normal_volume": normal_volume,
                "alerts": alerts,
                "score": exfiltration_score
            })
        
        return {
            "exfiltration_score": exfiltration_score,
            "alerts": alerts,
            "risk_level": self._calculate_risk_level(exfiltration_score)
        }
    
    def _is_unusual_hour(self, timestamp):
        """Check if timestamp falls in unusual hours"""
        hour = timestamp.hour
        return self.detection_rules["unusual_hours"]["start_hour"] <= hour <= self.detection_rules["unusual_hours"]["end_hour"]
    
    def _calculate_risk_level(self, score):
        """Calculate risk level based on threat score"""
        if score >= 50:
            return "CRITICAL"
        elif score >= 30:
            return "HIGH"
        elif score >= 15:
            return "MEDIUM"
        elif score > 0:
            return "LOW"
        else:
            return "NORMAL"

class SecurityIncidentManager:
    """Manages security incidents and automated responses"""
    
    def __init__(self):
        self.active_incidents = {}
        self.incident_counter = 0
        
    def create_incident(self, user_id, incident_type, details, severity="MEDIUM"):
        """
        Create new security incident
        
        Args:
            user_id: ID of the affected user
            incident_type: Type of security incident
            details: Incident details
            severity: Severity level
            
        Returns:
            str: Incident ID
        """
        self.incident_counter += 1
        incident_id = f"SEC-{datetime.now().strftime('%Y%m%d')}-{self.incident_counter:04d}"
        
        incident = {
            "id": incident_id,
            "user_id": user_id,
            "type": incident_type,
            "details": details,
            "severity": severity,
            "status": "OPEN",
            "created_at": datetime.now(),
            "actions_taken": [],
            "resolved_at": None
        }
        
        self.active_incidents[incident_id] = incident
        
        # Log incident creation
        threat_logger.error(f"Security incident created: {incident_id} - {incident_type} for user {user_id}")
        log_user_action("security_incident", user_id, {
            "incident_id": incident_id,
            "type": incident_type,
            "severity": severity
        })
        
        # Trigger automated response
        self._trigger_automated_response(incident)
        
        return incident_id
    
    def _trigger_automated_response(self, incident):
        """
        Trigger automated security response based on incident
        
        Args:
            incident: The security incident
        """
        user_id = incident["user_id"]
        incident_type = incident["type"]
        severity = incident["severity"]
        
        actions_taken = []
        
        if severity in ["CRITICAL", "HIGH"]:
            if incident_type == "brute_force_attack":
                # Temporarily lock account
                actions_taken.append("account_temporarily_locked")
                # In production, implement actual account locking
                
            elif incident_type == "massive_data_access":
                # Revoke API keys and require re-authentication
                actions_taken.append("api_keys_revoked")
                actions_taken.append("force_reauth_required")
        
        elif severity == "MEDIUM":
            if incident_type in ["unusual_hour_login", "elevated_data_access"]:
                # Require additional verification
                actions_taken.append("additional_verification_required")
        
        # Update incident with actions taken
        incident["actions_taken"] = actions_taken
        
        # Log automated response
        if actions_taken:
            threat_logger.info(f"Automated response for incident {incident['id']}: {actions_taken}")
            log_user_action("automated_security_response", user_id, {
                "incident_id": incident["id"],
                "actions": actions_taken
            })
    
    def get_incident_summary(self):
        """Get summary of current security incidents"""
        summary = {
            "total_incidents": len(self.active_incidents),
            "by_severity": defaultdict(int),
            "by_type": defaultdict(int),
            "recent_incidents": []
        }
        
        for incident in self.active_incidents.values():
            summary["by_severity"][incident["severity"]] += 1
            summary["by_type"][incident["type"]] += 1
            
            # Add to recent incidents if created in last 24 hours
            if datetime.now() - incident["created_at"] < timedelta(hours=24):
                summary["recent_incidents"].append({
                    "id": incident["id"],
                    "type": incident["type"],
                    "severity": incident["severity"],
                    "created_at": incident["created_at"].isoformat()
                })
        
        return summary

class SecurityMetrics:
    """Collects and analyzes security metrics"""
    
    def __init__(self):
        self.metrics = defaultdict(int)
        self.daily_metrics = defaultdict(lambda: defaultdict(int))
    
    def record_metric(self, metric_name, value=1):
        """Record security metric"""
        today = datetime.now().strftime("%Y-%m-%d")
        self.metrics[metric_name] += value
        self.daily_metrics[today][metric_name] += value
    
    def get_security_dashboard_data(self):
        """Get data for security dashboard"""
        today = datetime.now().strftime("%Y-%m-%d")
        yesterday = (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d")
        
        return {
            "today_metrics": dict(self.daily_metrics[today]),
            "yesterday_metrics": dict(self.daily_metrics[yesterday]),
            "total_metrics": dict(self.metrics),
            "security_score": self._calculate_security_score()
        }
    
    def _calculate_security_score(self):
        """Calculate overall security score"""
        # Base score
        score = 100
        
        # Deduct points for security events
        score -= self.metrics.get("login_failures", 0) * 0.1
        score -= self.metrics.get("security_incidents", 0) * 5
        score -= self.metrics.get("blocked_attacks", 0) * 0.5
        
        # Add points for security measures
        score += self.metrics.get("successful_validations", 0) * 0.01
        
        return max(0, min(100, score))

# Global instances
threat_engine = ThreatDetectionEngine()
incident_manager = SecurityIncidentManager()
security_metrics = SecurityMetrics()