"""
LogistiLink Booking Cancellation Management System

This module handles booking cancellations for both logistics providers and customers,
implementing business rules for paid vs unpaid bookings.
"""

import datetime
from django.utils import timezone
from django.db import transaction
from decimal import Decimal
from shipments.models import Shipment
from core.notification_models import Notification
from core.models import User


def get_cancelable_bookings(logistics_provider):
    """
    Get bookings that can be canceled by logistics provider.
    
    Args:
        logistics_provider (User): The logistics provider
        
    Returns:
        QuerySet: Shipments that can be canceled
    """
    current_time = timezone.now()
    
    # Get all unpaid bank transfer bookings
    unpaid_bookings = Shipment.objects.filter(
        logistics_provider=logistics_provider,
        payment_method='bank_transfer',
        is_paid=False,
        cancellation_status='none',
        status__in=['BOOKED', 'PENDING', 'CONFIRMED']
    )
    
    # Filter based on payment due date with timezone handling
    cancelable_unpaid = []
    for booking in unpaid_bookings:
        if booking.payment_due_date:
            payment_due = booking.payment_due_date
            if timezone.is_naive(payment_due):
                payment_due = timezone.make_aware(payment_due)
            
            if payment_due < current_time:
                cancelable_unpaid.append(booking.id)
    
    return Shipment.objects.filter(id__in=cancelable_unpaid)


def get_cancellation_requests(logistics_provider):
    """
    Get customer cancellation requests pending approval.
    
    Args:
        logistics_provider (User): The logistics provider
        
    Returns:
        QuerySet: Shipments with pending cancellation requests
    """
    return Shipment.objects.filter(
        logistics_provider=logistics_provider,
        cancellation_status='requested_by_customer',
        status__in=['BOOKED', 'PENDING', 'CONFIRMED', 'IN_TRANSIT']
    ).select_related('customer')


def cancel_unpaid_booking(shipment_id, logistics_provider, reason="Payment deadline exceeded"):
    """
    Cancel an unpaid booking by logistics provider.
    
    Args:
        shipment_id (int): Shipment ID to cancel
        logistics_provider (User): The logistics provider
        reason (str): Reason for cancellation
        
    Returns:
        dict: Result of the cancellation attempt
    """
    try:
        with transaction.atomic():
            shipment = Shipment.objects.select_for_update().get(
                id=shipment_id,
                logistics_provider=logistics_provider
            )
            
            # Verify cancellation is allowed
            current_time = timezone.now()
            
            # Ensure payment_due_date is timezone-aware for comparison
            payment_due = shipment.payment_due_date
            if payment_due and timezone.is_naive(payment_due):
                payment_due = timezone.make_aware(payment_due)
            
            if (shipment.payment_method != 'bank_transfer' or 
                shipment.is_paid or 
                (payment_due and payment_due >= current_time) or
                shipment.cancellation_status != 'none'):
                return {
                    'success': False,
                    'message': 'This booking cannot be canceled at this time'
                }
            
            # Update shipment status
            shipment.cancellation_status = 'cancelled_by_logistics'
            shipment.cancellation_reason = reason
            shipment.cancellation_processed_at = current_time
            shipment.status = 'CANCELLED'
            shipment.save()
            
            # Notify customer
            Notification.objects.create(
                user=shipment.customer,
                title="Booking Canceled",
                message=f"Your booking {shipment.tracking_number} has been canceled due to unpaid bank transfer. Reason: {reason}",
                notification_type='cancellation',
                priority='high'
            )
            
            # Notify logistics provider (confirmation)
            Notification.objects.create(
                user=logistics_provider,
                title="Booking Canceled",
                message=f"You have successfully canceled booking {shipment.tracking_number}",
                notification_type='cancellation',
                priority='medium'
            )
            
            return {
                'success': True,
                'message': f'Booking {shipment.tracking_number} has been canceled successfully'
            }
            
    except Shipment.DoesNotExist:
        return {
            'success': False,
            'message': 'Booking not found or access denied'
        }
    except Exception as e:
        return {
            'success': False,
            'message': f'Error canceling booking: {str(e)}'
        }


def request_cancellation(shipment_id, customer, reason=""):
    """
    Request cancellation of a paid booking by customer.
    
    Args:
        shipment_id (int): Shipment ID to request cancellation for
        customer (User): The customer requesting cancellation
        reason (str): Reason for cancellation request
        
    Returns:
        dict: Result of the cancellation request
    """
    try:
        with transaction.atomic():
            shipment = Shipment.objects.select_for_update().get(
                id=shipment_id,
                customer=customer
            )
            
            # Verify request is allowed
            if (not shipment.is_paid or 
                shipment.cancellation_status != 'none' or
                shipment.status in ['DELIVERED', 'CANCELLED']):
                return {
                    'success': False,
                    'message': 'This booking cannot be canceled or request is already pending'
                }
            
            # Update shipment status
            shipment.cancellation_status = 'requested_by_customer'
            shipment.cancellation_reason = reason
            shipment.cancellation_requested_at = timezone.now()
            shipment.save()
            
            # Notify logistics provider
            Notification.objects.create(
                user=shipment.logistics_provider,
                title="Cancellation Request",
                message=f"Customer {shipment.customer.company_name} has requested to cancel booking {shipment.tracking_number}. Reason: {reason or 'No reason provided'}",
                notification_type='cancellation_request',
                priority='high'
            )
            
            # Notify customer (confirmation)
            Notification.objects.create(
                user=customer,
                title="Cancellation Request Submitted",
                message=f"Your cancellation request for booking {shipment.tracking_number} has been submitted and is pending approval",
                notification_type='cancellation_request',
                priority='medium'
            )
            
            return {
                'success': True,
                'message': 'Cancellation request submitted successfully'
            }
            
    except Shipment.DoesNotExist:
        return {
            'success': False,
            'message': 'Booking not found or access denied'
        }
    except Exception as e:
        return {
            'success': False,
            'message': f'Error submitting cancellation request: {str(e)}'
        }


def approve_cancellation_request(shipment_id, logistics_provider):
    """
    Approve a customer's cancellation request.
    
    Args:
        shipment_id (int): Shipment ID to approve cancellation for
        logistics_provider (User): The logistics provider
        
    Returns:
        dict: Result of the approval
    """
    try:
        with transaction.atomic():
            shipment = Shipment.objects.select_for_update().get(
                id=shipment_id,
                logistics_provider=logistics_provider
            )
            
            # Verify approval is allowed
            if shipment.cancellation_status != 'requested_by_customer':
                return {
                    'success': False,
                    'message': 'No pending cancellation request for this booking'
                }
            
            # Calculate penalty and refund amounts
            penalty_percentage = Decimal('0.10')  # 10% penalty
            penalty_amount = shipment.total_price * penalty_percentage
            refund_amount = shipment.total_price - penalty_amount
            
            # Update shipment status
            shipment.cancellation_status = 'mutually_cancelled'
            shipment.cancellation_processed_at = timezone.now()
            shipment.status = 'CANCELLED'
            shipment.refund_issued = True
            shipment.penalty_deducted = True
            shipment.save()
            
            # Notify customer
            Notification.objects.create(
                user=shipment.customer,
                title="Cancellation Approved",
                message=f"Your cancellation request for booking {shipment.tracking_number} has been approved. Refund amount: ${refund_amount:.2f} (after 10% penalty). Please contact logistics provider for refund processing.",
                notification_type='cancellation_approved',
                priority='high'
            )
            
            # Notify logistics provider (confirmation)
            Notification.objects.create(
                user=logistics_provider,
                title="Cancellation Approved",
                message=f"You have approved cancellation for booking {shipment.tracking_number}. Please process refund of ${refund_amount:.2f} to customer (penalty ${penalty_amount:.2f} retained).",
                notification_type='cancellation_approved',
                priority='high'
            )
            
            return {
                'success': True,
                'message': f'Cancellation approved. Refund amount: ${refund_amount:.2f}',
                'refund_amount': refund_amount,
                'penalty_amount': penalty_amount
            }
            
    except Shipment.DoesNotExist:
        return {
            'success': False,
            'message': 'Booking not found or access denied'
        }
    except Exception as e:
        return {
            'success': False,
            'message': f'Error approving cancellation: {str(e)}'
        }


def decline_cancellation_request(shipment_id, logistics_provider, reason=""):
    """
    Decline a customer's cancellation request.
    
    Args:
        shipment_id (int): Shipment ID to decline cancellation for
        logistics_provider (User): The logistics provider
        reason (str): Reason for declining
        
    Returns:
        dict: Result of the decline
    """
    try:
        with transaction.atomic():
            shipment = Shipment.objects.select_for_update().get(
                id=shipment_id,
                logistics_provider=logistics_provider
            )
            
            # Verify decline is allowed
            if shipment.cancellation_status != 'requested_by_customer':
                return {
                    'success': False,
                    'message': 'No pending cancellation request for this booking'
                }
            
            # Update shipment status
            shipment.cancellation_status = 'declined_by_logistics'
            shipment.cancellation_processed_at = timezone.now()
            if reason:
                shipment.cancellation_reason += f" | Declined: {reason}"
            shipment.save()
            
            # Notify customer
            Notification.objects.create(
                user=shipment.customer,
                title="Cancellation Request Declined",
                message=f"Your cancellation request for booking {shipment.tracking_number} has been declined. Reason: {reason or 'No reason provided'}",
                notification_type='cancellation_declined',
                priority='high'
            )
            
            # Notify logistics provider (confirmation)
            Notification.objects.create(
                user=logistics_provider,
                title="Cancellation Request Declined",
                message=f"You have declined cancellation request for booking {shipment.tracking_number}",
                notification_type='cancellation_declined',
                priority='medium'
            )
            
            return {
                'success': True,
                'message': 'Cancellation request declined successfully'
            }
            
    except Shipment.DoesNotExist:
        return {
            'success': False,
            'message': 'Booking not found or access denied'
        }
    except Exception as e:
        return {
            'success': False,
            'message': f'Error declining cancellation: {str(e)}'
        }


def set_payment_due_date(shipment, days=5):
    """
    Set payment due date for bank transfer bookings.
    
    Args:
        shipment (Shipment): The shipment object
        days (int): Number of days from booking date
    """
    if shipment.payment_method == 'bank_transfer' and not shipment.payment_due_date:
        shipment.payment_due_date = shipment.created_at + datetime.timedelta(days=days)
        shipment.save()


def get_customer_cancelable_shipments(customer):
    """
    Get shipments that customer can request cancellation for.
    
    Args:
        customer (User): The customer
        
    Returns:
        QuerySet: Shipments eligible for cancellation request
    """
    return Shipment.objects.filter(
        customer=customer,
        is_paid=True,
        cancellation_status='none',
        status__in=['BOOKED', 'PENDING', 'CONFIRMED', 'IN_TRANSIT']
    ).select_related('logistics_provider')


def get_pending_cancellation_requests(customer):
    """
    Get customer's pending cancellation requests.
    
    Args:
        customer (User): The customer
        
    Returns:
        QuerySet: Shipments with pending cancellation requests
    """
    return Shipment.objects.filter(
        customer=customer,
        cancellation_status__in=['requested_by_customer', 'declined_by_logistics']
    ).select_related('logistics_provider')