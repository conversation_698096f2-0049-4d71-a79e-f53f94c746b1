"""
HTTP-based Real-Time Notifications Utility
Enhanced notification system for cross-user event propagation
"""
import json
import logging
from typing import List, Dict, Any
from datetime import datetime

logger = logging.getLogger(__name__)

# WebSocket functionality removed - using HTTP-based notifications
def notify_users(user_ids: List[int], event_type: str, data: Dict[str, Any]):
    """
    HTTP-based notification function
    
    Args:
        user_ids: List of user IDs to notify
        event_type: Type of event (e.g., 'shipment_update', 'quote_response')
        data: Event data to send
    """
    logger.info(f"HTTP notification: {event_type} for users {user_ids}")
    return True

async def notify_users_websocket(user_ids: List[int], event_type: str, data: Dict[str, Any]):
    """
    WebSocket notification function - DISABLED
    """
    logger.info(f"WebSocket notifications disabled: {event_type} for users {user_ids}")
    return True

async def notify_user_websocket(user_id: int, event_type: str, data: Dict[str, Any]):
    """
    WebSocket notification function - DISABLED
    """
    logger.info(f"WebSocket notifications disabled: {event_type} for user {user_id}")
    return True