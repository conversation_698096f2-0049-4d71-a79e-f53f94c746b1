import os
import json
import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler
from sklearn.cluster import KMeans
from sklearn.decomposition import PCA
from utils.data_processor import get_numeric_columns, get_categorical_columns

def warning_handler(message):
    """Handle warnings without Streamlit dependency"""
    print(f"Warning: {message}")

# Initialize the OpenAI client (conditionally)
try:
    from openai import OpenAI
    
    # Check for API key in environment variables
    OPENAI_API_KEY = os.environ.get("OPENAI_API_KEY")
    
    # Only initialize if API key is available from environment
    if OPENAI_API_KEY:
        openai = OpenAI(api_key=OPENAI_API_KEY)
        openai_available = True
    else:
        openai_available = False
        warning_handler("OpenAI API key not found in environment. AI-powered insights will be limited.")
except ImportError:
    openai_available = False
    warning_handler("OpenAI Python package is not installed. AI-powered insights will be limited.")
except Exception:
    openai_available = False
    warning_handler("Error initializing OpenAI. AI-powered insights will be limited.")

def analyze_dataset_with_ai(df, context=None):
    """
    Analyze a dataset with OpenAI to get insights.
    
    Args:
        df: Pandas DataFrame to analyze
        context: Optional context about the data or specific questions
        
    Returns:
        Dictionary with insights
    """
    # Check if OpenAI is available
    if not globals().get('openai_available', False):
        return {
            "error": "OpenAI API key is not configured. Please add your API key in the Settings page.",
            "key_insights": ["OpenAI integration is not available. Please go to the Settings page (⚙️) and add your OpenAI API key."]
        }
        
    # Generate a sample and summary of the data to send to OpenAI
    data_sample = df.head(5).to_dict()
    data_types = {col: str(dtype) for col, dtype in df.dtypes.items()}
    data_summary = {
        "columns": list(df.columns),
        "data_types": data_types,
        "row_count": len(df),
        "numeric_summary": df.describe().to_dict() if not df.select_dtypes(include=['number']).empty else {},
        "unique_values": {col: df[col].nunique() for col in df.columns},
        "missing_values": df.isnull().sum().to_dict()
    }
    
    # For categorical columns, include value counts for the top 5 values
    cat_summary = {}
    for col in get_categorical_columns(df):
        try:
            cat_summary[col] = df[col].value_counts().head(5).to_dict()
        except:
            pass
    
    data_summary["categorical_summary"] = cat_summary
    
    # Construct prompt with the data information
    prompt_content = f"""
    Based on the following dataset information, provide insightful analysis and recommendations:
    
    DATASET SUMMARY:
    {json.dumps(data_summary, indent=2)}
    
    SAMPLE DATA:
    {json.dumps(data_sample, indent=2)}
    
    {'CONTEXT:' + chr(10) + context if context else ''}
    
    Please provide the following in JSON format:
    1. Key Insights: Identify 3-5 notable patterns or insights from the data
    2. Recommendations: Suggest 2-3 actionable recommendations for product managers based on this data
    3. Further Analysis: Recommend 2-3 additional analyses that could provide deeper insights
    4. Data Quality Issues: Identify any potential data quality issues that should be addressed
    5. Visualization Suggestions: Recommend specific visualizations that would be valuable
    """
    
    try:
        # The newest OpenAI model is "gpt-4o" which was released May 13, 2024.
        # do not change this unless explicitly requested by the user
        response = openai.chat.completions.create(
            model="gpt-4o",
            messages=[
                {"role": "system", "content": "You are a data analysis expert providing insights for product managers."},
                {"role": "user", "content": prompt_content}
            ],
            response_format={"type": "json_object"},
            max_tokens=2000
        )
        
        # Parse and return the JSON response
        insights = json.loads(response.choices[0].message.content)
        return insights
    
    except Exception as e:
        st.error(f"Error analyzing data with AI: {str(e)}")
        return {
            "error": str(e),
            "key_insights": ["Failed to generate AI insights. Please try again or check your API key."]
        }

def analyze_specific_question(df, question):
    """
    Analyze a specific question about the dataset using AI.
    
    Args:
        df: Pandas DataFrame
        question: The specific question to analyze
        
    Returns:
        Dictionary with the analysis
    """
    # Check if OpenAI is available
    if not globals().get('openai_available', False):
        return {
            "error": "OpenAI API key is not configured. Please add your API key in the Settings page.",
            "answer": "OpenAI integration is not available. Please go to the Settings page (⚙️) and add your OpenAI API key."
        }
    
    # Generate a sample of the data
    data_sample = df.head(5).to_dict()
    data_summary = {
        "columns": list(df.columns),
        "data_types": {col: str(dtype) for col, dtype in df.dtypes.items()},
        "row_count": len(df),
    }
    
    prompt_content = f"""
    Based on the following dataset information, answer this specific question:
    
    QUESTION: {question}
    
    DATASET SUMMARY:
    {json.dumps(data_summary, indent=2)}
    
    SAMPLE DATA:
    {json.dumps(data_sample, indent=2)}
    
    Provide a comprehensive answer with supporting analysis where possible. Format your response in JSON with these keys:
    1. "answer": Your main answer to the question
    2. "analysis": Supporting analysis and reasoning
    3. "visualization_suggestion": Suggestion for how to visualize this information if applicable
    """
    
    try:
        # The newest OpenAI model is "gpt-4o" which was released May 13, 2024.
        # do not change this unless explicitly requested by the user
        response = openai.chat.completions.create(
            model="gpt-4o",
            messages=[
                {"role": "system", "content": "You are a data analysis expert providing insights for product managers."},
                {"role": "user", "content": prompt_content}
            ],
            response_format={"type": "json_object"},
            max_tokens=1000
        )
        
        # Parse and return the JSON response
        analysis = json.loads(response.choices[0].message.content)
        return analysis
    
    except Exception as e:
        st.error(f"Error analyzing specific question with AI: {str(e)}")
        return {
            "error": str(e),
            "answer": "Failed to generate AI analysis. Please try again or check your API key."
        }

def create_natural_language_query(df, query):
    """
    Convert a natural language query into a pandas query or filtered dataframe.
    
    Args:
        df: Pandas DataFrame
        query: Natural language query string
        
    Returns:
        Filtered DataFrame based on the query
    """
    # Check if OpenAI is available
    if not globals().get('openai_available', False):
        return df, "OpenAI API key is not configured. Please go to the Settings page (⚙️) and add your OpenAI API key."
    
    # Format information about the DataFrame
    column_info = {col: {
        "type": str(df[col].dtype),
        "sample_values": df[col].sample(min(5, len(df))).tolist(),
        "unique_count": df[col].nunique(),
        "is_numeric": pd.api.types.is_numeric_dtype(df[col])
    } for col in df.columns}
    
    prompt_content = f"""
    Convert the following natural language query into a pandas dataframe query/filter.
    
    DATAFRAME COLUMNS:
    {json.dumps(column_info, indent=2)}
    
    NATURAL LANGUAGE QUERY:
    {query}
    
    Return a JSON object with:
    1. "pandas_code": The exact Python code using pandas to filter the dataframe
    2. "explanation": A brief explanation of what the code does
    
    Example format:
    {{
        "pandas_code": "df[df['column'] > 100].sort_values('other_column', ascending=False)",
        "explanation": "This filters rows where 'column' is greater than 100 and sorts by 'other_column' in descending order"
    }}
    
    IMPORTANT: The code should be valid pandas syntax that actually works, using df as the dataframe name.
    """
    
    try:
        # The newest OpenAI model is "gpt-4o" which was released May 13, 2024.
        # do not change this unless explicitly requested by the user
        response = openai.chat.completions.create(
            model="gpt-4o",
            messages=[
                {"role": "system", "content": "You are a Python pandas expert that converts natural language to working code."},
                {"role": "user", "content": prompt_content}
            ],
            response_format={"type": "json_object"},
            max_tokens=500
        )
        
        # Parse the response
        result = json.loads(response.choices[0].message.content)
        pandas_code = result.get("pandas_code", "")
        
        # Execute the pandas code (securely, with limited scope)
        try:
            # Use a local copy of the dataframe to avoid modifying the original
            local_df = df.copy()
            # Execute the pandas code in a restricted scope
            filtered_df = eval(pandas_code, {"df": local_df, "pd": pd, "np": np})
            return filtered_df, result.get("explanation", "")
        except Exception as code_error:
            st.error(f"Error executing generated code: {str(code_error)}")
            return df, f"Error executing query: {str(code_error)}"
    
    except Exception as e:
        st.error(f"Error generating pandas query: {str(e)}")
        return df, f"Error generating query: {str(e)}"

def generate_report_insights(df, report_title, sections):
    """
    Generate insights for a specific report using AI.
    
    Args:
        df: Pandas DataFrame
        report_title: Title of the report
        sections: List of sections to include in the report
        
    Returns:
        Dictionary with insights for each section
    """
    # Check if OpenAI is available
    if not globals().get('openai_available', False):
        return {section: {
            "error": "OpenAI API key is not configured. Please add your API key in the Settings page.",
            "key_insights": ["OpenAI integration is not available. Please go to the Settings page (⚙️) and add your OpenAI API key."],
            "visualization_suggestion": "Not available",
            "recommendations": ["Configure OpenAI API key in Settings to enable AI-powered insights."]
        } for section in sections}
    
    # Generate a summary of the data
    data_summary = {
        "columns": list(df.columns),
        "row_count": len(df),
        "numeric_summary": df.describe().to_dict() if not df.select_dtypes(include=['number']).empty else {},
    }
    
    prompt_content = f"""
    Generate insights for a product management report with the following title and sections:
    
    REPORT TITLE: {report_title}
    
    REPORT SECTIONS: {', '.join(sections)}
    
    DATASET SUMMARY:
    {json.dumps(data_summary, indent=2)}
    
    For each section, provide:
    1. Key insights relevant to that section
    2. Suggested visualizations or metrics to include
    3. Actionable recommendations based on the data
    
    Format your response as a JSON object with each section name as a key.
    """
    
    try:
        # The newest OpenAI model is "gpt-4o" which was released May 13, 2024.
        # do not change this unless explicitly requested by the user
        response = openai.chat.completions.create(
            model="gpt-4o",
            messages=[
                {"role": "system", "content": "You are a product management reporting expert."},
                {"role": "user", "content": prompt_content}
            ],
            response_format={"type": "json_object"},
            max_tokens=1500
        )
        
        # Parse and return the JSON response
        section_insights = json.loads(response.choices[0].message.content)
        return section_insights
    
    except Exception as e:
        st.error(f"Error generating report insights: {str(e)}")
        return {section: {"error": str(e)} for section in sections}

def perform_anomaly_detection(df, column):
    """
    Detect anomalies in a numeric column using statistical methods.
    
    Args:
        df: Pandas DataFrame
        column: Column name to analyze
        
    Returns:
        DataFrame with anomalies and explanation
    """
    if column not in df.columns or not pd.api.types.is_numeric_dtype(df[column]):
        return None, "Selected column must be numeric"
    
    # Calculate Z-scores
    mean = df[column].mean()
    std = df[column].std()
    df['z_score'] = abs((df[column] - mean) / std)
    
    # Identify anomalies (Z-score > 3)
    anomalies = df[df['z_score'] > 3].copy()
    
    # Calculate IQR
    Q1 = df[column].quantile(0.25)
    Q3 = df[column].quantile(0.75)
    IQR = Q3 - Q1
    lower_bound = Q1 - 1.5 * IQR
    upper_bound = Q3 + 1.5 * IQR
    
    # Add IQR-based anomalies
    iqr_anomalies = df[(df[column] < lower_bound) | (df[column] > upper_bound)].copy()
    iqr_anomalies['anomaly_type'] = 'IQR'
    
    # Set anomaly type for Z-score anomalies
    anomalies['anomaly_type'] = 'Z-score'
    
    # Combine anomalies
    all_anomalies = pd.concat([anomalies, iqr_anomalies]).drop_duplicates()
    
    # Remove the z_score column
    if 'z_score' in all_anomalies.columns:
        all_anomalies = all_anomalies.drop('z_score', axis=1)
    
    explanation = f"""
    Anomaly detection results for column '{column}':
    - Z-score method: {len(anomalies)} anomalies detected (|z| > 3)
    - IQR method: {len(iqr_anomalies)} anomalies detected (outside Q1-1.5*IQR, Q3+1.5*IQR)
    - Total unique anomalies: {len(all_anomalies)}
    
    Stats:
    - Mean: {mean:.2f}
    - Standard Deviation: {std:.2f}
    - Q1 (25th percentile): {Q1:.2f}
    - Q3 (75th percentile): {Q3:.2f}
    - IQR: {IQR:.2f}
    - Lower bound: {lower_bound:.2f}
    - Upper bound: {upper_bound:.2f}
    """
    
    return all_anomalies, explanation

def perform_clustering(df, columns, n_clusters=3):
    """
    Perform K-means clustering on selected columns.
    
    Args:
        df: Pandas DataFrame
        columns: List of columns to use for clustering
        n_clusters: Number of clusters to create
        
    Returns:
        DataFrame with cluster assignments and analysis
    """
    if not all(col in df.columns for col in columns):
        return None, "One or more selected columns not found in dataframe"
    
    # Check if all columns are numeric
    if not all(pd.api.types.is_numeric_dtype(df[col]) for col in columns):
        return None, "All columns for clustering must be numeric"
    
    try:
        # Extract features for clustering
        features = df[columns].copy()
        
        # Handle missing values
        features = features.fillna(features.mean())
        
        # Scale the features
        scaler = StandardScaler()
        scaled_features = scaler.fit_transform(features)
        
        # Perform K-means clustering
        kmeans = KMeans(n_clusters=n_clusters, random_state=42)
        cluster_labels = kmeans.fit_predict(scaled_features)
        
        # Add cluster labels to the dataframe
        result_df = df.copy()
        result_df['cluster'] = cluster_labels
        
        # Generate cluster statistics
        cluster_stats = {}
        for i in range(n_clusters):
            cluster_df = result_df[result_df['cluster'] == i]
            cluster_stats[f'Cluster {i}'] = {
                'size': len(cluster_df),
                'percentage': len(cluster_df) / len(df) * 100,
                'feature_means': {col: cluster_df[col].mean() for col in columns}
            }
        
        # Generate analysis
        analysis = f"""
        K-means clustering with {n_clusters} clusters:
        
        Cluster Sizes:
        {', '.join([f"Cluster {i}: {stats['size']} records ({stats['percentage']:.1f}%)" 
                  for i, stats in cluster_stats.items()])}
        
        Feature Importance:
        The centroids of each cluster indicate what makes each group distinct:
        """
        
        for i, stats in cluster_stats.items():
            analysis += chr(10) + f"{i}:" + chr(10)
            for col, mean in stats['feature_means'].items():
                overall_mean = df[col].mean()
                diff_pct = (mean - overall_mean) / overall_mean * 100 if overall_mean != 0 else 0
                analysis += f"  - {col}: {mean:.2f} ({diff_pct:+.1f}% from overall mean)\n"
        
        return result_df, analysis
        
    except Exception as e:
        return None, f"Error performing clustering: {str(e)}"

def perform_pca_analysis(df, columns, n_components=2):
    """
    Perform PCA dimensionality reduction on selected columns.
    
    Args:
        df: Pandas DataFrame
        columns: List of columns to use for PCA
        n_components: Number of principal components
        
    Returns:
        DataFrame with PCA results and analysis
    """
    if not all(col in df.columns for col in columns):
        return None, "One or more selected columns not found in dataframe"
    
    # Check if all columns are numeric
    if not all(pd.api.types.is_numeric_dtype(df[col]) for col in columns):
        return None, "All columns for PCA must be numeric"
    
    try:
        # Extract features for PCA
        features = df[columns].copy()
        
        # Handle missing values
        features = features.fillna(features.mean())
        
        # Scale the features
        scaler = StandardScaler()
        scaled_features = scaler.fit_transform(features)
        
        # Perform PCA
        pca = PCA(n_components=n_components)
        pca_result = pca.fit_transform(scaled_features)
        
        # Create a dataframe with PCA results
        pca_df = pd.DataFrame(
            data=pca_result,
            columns=[f'PC{i+1}' for i in range(n_components)]
        )
        
        # Add original data
        result_df = pd.concat([df.reset_index(drop=True), pca_df.reset_index(drop=True)], axis=1)
        
        # Generate analysis
        explained_variance = pca.explained_variance_ratio_
        cumulative_variance = np.cumsum(explained_variance)
        
        analysis = f"""
        PCA Analysis with {n_components} components:
        
        Explained Variance:
        {', '.join([f"PC{i+1}: {var:.2%}" for i, var in enumerate(explained_variance)])}
        
        Cumulative Explained Variance:
        {', '.join([f"PC{i+1}: {var:.2%}" for i, var in enumerate(cumulative_variance)])}
        
        Feature Contributions:
        """
        
        for i in range(n_components):
            pc_loadings = pca.components_[i]
            sorted_indices = np.argsort(np.abs(pc_loadings))[::-1]
            analysis += f"\nPC{i+1} (explained variance: {explained_variance[i]:.2%}):\n"
            
            for idx in sorted_indices:
                feature = columns[idx]
                loading = pc_loadings[idx]
                analysis += f"  - {feature}: {loading:.3f}\n"
        
        return result_df, analysis
        
    except Exception as e:
        return None, f"Error performing PCA: {str(e)}"
