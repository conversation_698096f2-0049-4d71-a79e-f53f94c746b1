"""
AI Chatbot Widget Deployment System
Deploys the comprehensive AI chatbot as a customer-facing widget
"""

import json
import asyncio
from typing import Dict, List, Any, Optional
from datetime import datetime
import random
import re

class ChatbotWidgetManager:
    """Manages the AI chatbot widget deployment and interactions"""
    
    def __init__(self):
        self.active_sessions = {}
        self.conversation_history = {}
        self.widget_config = {
            "position": "bottom-right",
            "theme": "cloverics",
            "auto_open": False,
            "show_typing": True,
            "max_history": 50,
            "typing_delay": 1500
        }
    
    def get_widget_html(self) -> str:
        """Generate HTML for the chatbot widget"""
        return """
        <!-- AI Chatbot Widget -->
        <div id="chatbot-widget" class="chatbot-widget">
            <div id="chatbot-toggle" class="chatbot-toggle" onclick="toggleChatbot()">
                <i class="fas fa-comments"></i>
                <span class="chat-notification" id="chat-notification" style="display: none;">1</span>
            </div>
            
            <div id="chatbot-window" class="chatbot-window" style="display: none;">
                <div class="chatbot-header">
                    <div class="chatbot-avatar">
                        <i class="fas fa-robot"></i>
                    </div>
                    <div class="chatbot-title">
                        <h6 class="mb-0">AI Assistant</h6>
                        <small class="text-muted">Always here to help</small>
                    </div>
                    <button class="chatbot-close" onclick="toggleChatbot()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <div class="chatbot-messages" id="chatbot-messages">
                    <div class="message bot-message">
                        <div class="message-avatar">
                            <i class="fas fa-robot"></i>
                        </div>
                        <div class="message-content">
                            <p>Hello! I'm your AI logistics assistant. I can help you with:</p>
                            <ul class="chat-capabilities">
                                <li>Tracking shipments</li>
                                <li>Quote calculations</li>
                                <li>Route optimization</li>
                                <li>Documentation assistance</li>
                                <li>Platform navigation</li>
                            </ul>
                            <p>How can I assist you today?</p>
                        </div>
                    </div>
                </div>
                
                <div class="chatbot-typing" id="chatbot-typing" style="display: none;">
                    <div class="typing-indicator">
                        <span></span>
                        <span></span>
                        <span></span>
                    </div>
                    <small>AI Assistant is typing...</small>
                </div>
                
                <div class="chatbot-input">
                    <div class="quick-actions" id="quick-actions">
                        <button class="quick-action" onclick="sendQuickMessage('Track my shipment')">
                            📦 Track Shipment
                        </button>
                        <button class="quick-action" onclick="sendQuickMessage('Get a quote')">
                            💰 Get Quote
                        </button>
                        <button class="quick-action" onclick="sendQuickMessage('Help with documentation')">
                            📄 Documentation
                        </button>
                    </div>
                    <div class="input-group">
                        <input type="text" id="chatbot-input" class="form-control" 
                               placeholder="Type your message..." 
                               onkeypress="handleChatKeyPress(event)">
                        <button class="btn btn-primary" onclick="sendChatMessage()">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        """
    
    def get_widget_css(self) -> str:
        """Generate CSS for the chatbot widget"""
        return """
        <style>
        .chatbot-widget {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1050;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .chatbot-toggle {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #007bff, #0056b3);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
            transition: all 0.3s ease;
            position: relative;
        }
        
        .chatbot-toggle:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
        }
        
        .chatbot-toggle i {
            color: white;
            font-size: 24px;
        }
        
        .chat-notification {
            position: absolute;
            top: -5px;
            right: -5px;
            background: #dc3545;
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }
        
        .chatbot-window {
            width: 350px;
            height: 500px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            position: absolute;
            bottom: 80px;
            right: 0;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            border: 1px solid #e9ecef;
        }
        
        .chatbot-header {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            padding: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .chatbot-avatar {
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .chatbot-title {
            flex: 1;
        }
        
        .chatbot-close {
            background: none;
            border: none;
            color: white;
            cursor: pointer;
            padding: 5px;
            border-radius: 4px;
            transition: background 0.2s;
        }
        
        .chatbot-close:hover {
            background: rgba(255, 255, 255, 0.2);
        }
        
        .chatbot-messages {
            flex: 1;
            padding: 15px;
            overflow-y: auto;
            background: #f8f9fa;
        }
        
        .message {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
            animation: messageSlideIn 0.3s ease-out;
        }
        
        @keyframes messageSlideIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .user-message {
            justify-content: flex-end;
        }
        
        .user-message .message-content {
            background: #007bff;
            color: white;
            order: -1;
        }
        
        .bot-message .message-avatar {
            width: 32px;
            height: 32px;
            background: #6c757d;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
            flex-shrink: 0;
        }
        
        .user-message .message-avatar {
            width: 32px;
            height: 32px;
            background: #007bff;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
            flex-shrink: 0;
        }
        
        .message-content {
            background: white;
            padding: 12px 15px;
            border-radius: 18px;
            max-width: 70%;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        
        .message-content p {
            margin: 0 0 8px 0;
        }
        
        .message-content p:last-child {
            margin-bottom: 0;
        }
        
        .chat-capabilities {
            margin: 8px 0;
            padding-left: 16px;
        }
        
        .chat-capabilities li {
            margin-bottom: 4px;
            font-size: 14px;
        }
        
        .chatbot-typing {
            padding: 10px 15px;
            background: #f8f9fa;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .typing-indicator {
            display: flex;
            gap: 4px;
        }
        
        .typing-indicator span {
            width: 8px;
            height: 8px;
            background: #6c757d;
            border-radius: 50%;
            animation: typing 1.5s infinite;
        }
        
        .typing-indicator span:nth-child(2) {
            animation-delay: 0.2s;
        }
        
        .typing-indicator span:nth-child(3) {
            animation-delay: 0.4s;
        }
        
        @keyframes typing {
            0%, 60%, 100% {
                transform: scale(1);
                opacity: 0.7;
            }
            30% {
                transform: scale(1.2);
                opacity: 1;
            }
        }
        
        .chatbot-input {
            padding: 15px;
            background: white;
            border-top: 1px solid #e9ecef;
        }
        
        .quick-actions {
            display: flex;
            gap: 8px;
            margin-bottom: 10px;
            flex-wrap: wrap;
        }
        
        .quick-action {
            background: #e9ecef;
            border: none;
            padding: 6px 10px;
            border-radius: 12px;
            font-size: 12px;
            cursor: pointer;
            transition: background 0.2s;
        }
        
        .quick-action:hover {
            background: #dee2e6;
        }
        
        .input-group {
            display: flex;
            gap: 8px;
        }
        
        .input-group .form-control {
            flex: 1;
            border-radius: 20px;
            border: 1px solid #ced4da;
            padding: 8px 15px;
        }
        
        .input-group .btn {
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0;
        }
        
        @media (max-width: 768px) {
            .chatbot-window {
                width: 300px;
                height: 450px;
            }
        }
        </style>
        """
    
    def get_widget_javascript(self) -> str:
        """Generate JavaScript for the chatbot widget"""
        return """
        <script>
        let chatbotOpen = false;
        let messageHistory = [];
        
        function toggleChatbot() {
            const window = document.getElementById('chatbot-window');
            const notification = document.getElementById('chat-notification');
            
            chatbotOpen = !chatbotOpen;
            window.style.display = chatbotOpen ? 'flex' : 'none';
            
            if (chatbotOpen) {
                notification.style.display = 'none';
                document.getElementById('chatbot-input').focus();
            }
        }
        
        function sendChatMessage() {
            const input = document.getElementById('chatbot-input');
            const message = input.value.trim();
            
            if (message) {
                addMessage(message, 'user');
                input.value = '';
                processUserMessage(message);
            }
        }
        
        function sendQuickMessage(message) {
            addMessage(message, 'user');
            processUserMessage(message);
        }
        
        function handleChatKeyPress(event) {
            if (event.key === 'Enter') {
                sendChatMessage();
            }
        }
        
        function addMessage(content, sender) {
            const messagesContainer = document.getElementById('chatbot-messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}-message`;
            
            const avatar = document.createElement('div');
            avatar.className = 'message-avatar';
            
            if (sender === 'bot') {
                avatar.innerHTML = '<i class="fas fa-robot"></i>';
            } else {
                avatar.innerHTML = '<i class="fas fa-user"></i>';
            }
            
            const messageContent = document.createElement('div');
            messageContent.className = 'message-content';
            messageContent.innerHTML = content;
            
            messageDiv.appendChild(avatar);
            messageDiv.appendChild(messageContent);
            messagesContainer.appendChild(messageDiv);
            
            // Scroll to bottom
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
            
            // Store in history
            messageHistory.push({ content, sender, timestamp: new Date() });
        }
        
        function showTyping() {
            document.getElementById('chatbot-typing').style.display = 'flex';
            const messagesContainer = document.getElementById('chatbot-messages');
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }
        
        function hideTyping() {
            document.getElementById('chatbot-typing').style.display = 'none';
        }
        
        async function processUserMessage(message) {
            showTyping();
            
            try {
                // Simulate processing delay
                await new Promise(resolve => setTimeout(resolve, 1500));
                
                const response = await fetch('/api/chatbot/query', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        message: message,
                        history: messageHistory.slice(-10) // Last 10 messages for context
                    })
                });
                
                const result = await response.json();
                
                hideTyping();
                
                if (result.success) {
                    addMessage(result.response, 'bot');
                    
                    // Handle special actions
                    if (result.actions) {
                        handleChatbotActions(result.actions);
                    }
                } else {
                    addMessage('I apologize, but I encountered an error. Please try again or contact support.', 'bot');
                }
            } catch (error) {
                hideTyping();
                console.error('Chatbot error:', error);
                addMessage('I'm having trouble connecting right now. Please try again in a moment.', 'bot');
            }
        }
        
        function handleChatbotActions(actions) {
            actions.forEach(action => {
                switch (action.type) {
                    case 'show_notification':
                        showChatNotification();
                        break;
                    case 'redirect':
                        setTimeout(() => {
                            window.location.href = action.url;
                        }, 2000);
                        break;
                    case 'open_modal':
                        // Handle modal opening if needed
                        break;
                }
            });
        }
        
        function showChatNotification() {
            if (!chatbotOpen) {
                document.getElementById('chat-notification').style.display = 'flex';
            }
        }
        
        // Initialize chatbot
        document.addEventListener('DOMContentLoaded', function() {
            // Show welcome notification after 3 seconds
            setTimeout(() => {
                if (!chatbotOpen) {
                    showChatNotification();
                }
            }, 3000);
        });
        </script>
        """

class ChatbotResponseEngine:
    """Generate intelligent responses for the chatbot"""
    
    def __init__(self):
        self.intents = {
            "greeting": ["hello", "hi", "hey", "good morning", "good afternoon"],
            "tracking": ["track", "tracking", "shipment status", "where is my", "delivery"],
            "quote": ["quote", "price", "cost", "how much", "estimate"],
            "documentation": ["document", "papers", "customs", "invoice", "bill"],
            "navigation": ["how to", "where can i", "help me find", "navigate"],
            "support": ["help", "support", "problem", "issue", "contact"]
        }
        
        self.responses = {
            "greeting": [
                "Hello! Welcome to Cloverics. I'm here to help you with all your logistics needs.",
                "Hi there! How can I assist you with your shipments today?",
                "Welcome! I'm your AI logistics assistant. What can I help you with?"
            ],
            "tracking": [
                "I can help you track your shipment. Could you provide your tracking number or shipment ID?",
                "To track your shipment, I'll need your tracking number. You can find it in your confirmation email.",
                "Let me help you track your shipment. Please share your tracking number with me."
            ],
            "quote": [
                "I'd be happy to help you get a shipping quote. I'll need some details about your shipment.",
                "To provide an accurate quote, please tell me the origin, destination, weight, and cargo type.",
                "Let me help you calculate shipping costs. What are you looking to ship and where?"
            ],
            "documentation": [
                "I can assist with various shipping documents like commercial invoices, bills of lading, and customs declarations.",
                "Our platform can automatically generate all required shipping documentation. What specific documents do you need?",
                "I can help you with customs documentation, commercial invoices, packing lists, and more."
            ],
            "navigation": [
                "I can guide you through the platform. What specific feature are you looking for?",
                "Let me help you navigate. Are you looking for shipment management, quotes, or account settings?",
                "I'm here to help you find what you need. What would you like to do?"
            ],
            "support": [
                "I'm here to help! Please describe the issue you're experiencing.",
                "What seems to be the problem? I'll do my best to assist you.",
                "I can help resolve your issue. Please provide more details about what's happening."
            ]
        }
    
    async def generate_response(self, message: str, context: List[Dict] = None) -> Dict:
        """Generate intelligent response to user message"""
        message_lower = message.lower()
        
        # Detect intent
        detected_intent = self.detect_intent(message_lower)
        
        # Generate response based on intent
        if detected_intent:
            response = random.choice(self.responses[detected_intent])
            actions = self.get_intent_actions(detected_intent, message)
        else:
            response = self.generate_contextual_response(message, context)
            actions = []
        
        return {
            "success": True,
            "response": response,
            "intent": detected_intent,
            "actions": actions,
            "timestamp": datetime.now().isoformat()
        }
    
    def detect_intent(self, message: str) -> Optional[str]:
        """Detect user intent from message"""
        for intent, keywords in self.intents.items():
            if any(keyword in message for keyword in keywords):
                return intent
        return None
    
    def get_intent_actions(self, intent: str, message: str) -> List[Dict]:
        """Get actions to perform based on intent"""
        actions = []
        
        if intent == "tracking":
            # Extract potential tracking numbers
            tracking_pattern = r'[A-Z]{2}\d{8}[A-Z]?'
            if re.search(tracking_pattern, message.upper()):
                actions.append({
                    "type": "redirect",
                    "url": "/customer/track-shipment"
                })
        
        elif intent == "quote":
            actions.append({
                "type": "redirect",
                "url": "/customer/search-shipping"
            })
        
        elif intent == "documentation":
            actions.append({
                "type": "redirect",
                "url": "/customer/document-automation"
            })
        
        return actions
    
    def generate_contextual_response(self, message: str, context: List[Dict] = None) -> str:
        """Generate contextual response for unknown intents"""
        default_responses = [
            "I understand you need assistance. Could you please provide more specific details about what you're looking for?",
            "I'm here to help! Could you clarify what you'd like me to assist you with?",
            "I want to make sure I give you the best help possible. Could you tell me more about what you need?",
            "I can help you with shipment tracking, quotes, documentation, and platform navigation. What would you like to do?"
        ]
        
        return random.choice(default_responses)

# Global instances
chatbot_widget_manager = ChatbotWidgetManager()
chatbot_response_engine = ChatbotResponseEngine()