"""
Public API Portal System - Phase 3.2
Developer portal with API documentation, key management, and webhook system
Complete third-party integration ecosystem
"""

import asyncio
import json
import secrets
import hashlib
from typing import Dict, List, Optional, Union
from datetime import datetime, timedelta
from decimal import Decimal
import uuid
from dataclasses import dataclass, asdict
import hmac

from django.utils import timezone
from django.core.cache import cache
from asgiref.sync import sync_to_async


@dataclass
class APIKey:
    """API key data structure"""
    key_id: str
    api_key: str
    secret_key: str
    developer_email: str
    company_name: str
    tier: str  # 'free', 'basic', 'pro', 'enterprise'
    status: str  # 'active', 'suspended', 'revoked'
    created_at: datetime
    last_used: Optional[datetime]
    rate_limit: int
    rate_limit_window: int  # seconds
    current_usage: int
    features_enabled: List[str]
    webhook_url: Optional[str]
    webhook_secret: Optional[str]


@dataclass
class APIEndpoint:
    """API endpoint documentation structure"""
    endpoint: str
    method: str
    description: str
    parameters: List[Dict]
    response_example: Dict
    rate_limit: int
    tier_required: str
    authentication_required: bool
    category: str


class APIKeyManager:
    """Manages API key creation, validation, and rate limiting"""
    
    def __init__(self):
        self.rate_limits = {
            'free': {'requests_per_hour': 100, 'features': ['basic_tracking', 'rate_quotes']},
            'basic': {'requests_per_hour': 1000, 'features': ['basic_tracking', 'rate_quotes', 'shipment_creation']},
            'pro': {'requests_per_hour': 10000, 'features': ['basic_tracking', 'rate_quotes', 'shipment_creation', 'analytics', 'webhooks']},
            'enterprise': {'requests_per_hour': 100000, 'features': ['all']}
        }
    
    async def create_api_key(self, developer_data: Dict) -> Dict:
        """Create new API key for developer"""
        
        try:
            # Generate API credentials
            key_id = f"clv_{secrets.token_urlsafe(16)}"
            api_key = f"clv_live_{secrets.token_urlsafe(32)}"
            secret_key = secrets.token_urlsafe(64)
            
            # Determine tier based on request
            tier = developer_data.get('tier', 'free')
            if tier not in self.rate_limits:
                tier = 'free'
            
            # Create API key object
            api_key_obj = APIKey(
                key_id=key_id,
                api_key=api_key,
                secret_key=secret_key,
                developer_email=developer_data['email'],
                company_name=developer_data.get('company_name', ''),
                tier=tier,
                status='active',
                created_at=datetime.now(),
                last_used=None,
                rate_limit=self.rate_limits[tier]['requests_per_hour'],
                rate_limit_window=3600,  # 1 hour
                current_usage=0,
                features_enabled=self.rate_limits[tier]['features'],
                webhook_url=developer_data.get('webhook_url'),
                webhook_secret=secrets.token_urlsafe(32) if developer_data.get('webhook_url') else None
            )
            
            # Save to database (simulated)
            await self._save_api_key(api_key_obj)
            
            return {
                'success': True,
                'key_id': key_id,
                'api_key': api_key,
                'secret_key': secret_key,
                'tier': tier,
                'rate_limit': api_key_obj.rate_limit,
                'features_enabled': api_key_obj.features_enabled,
                'webhook_secret': api_key_obj.webhook_secret,
                'created_at': api_key_obj.created_at.isoformat()
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    async def validate_api_key(self, api_key: str) -> Optional[APIKey]:
        """Validate API key and return key data"""
        
        try:
            # Get API key from database/cache
            api_key_obj = await self._get_api_key(api_key)
            
            if not api_key_obj or api_key_obj.status != 'active':
                return None
            
            # Check rate limiting
            if not await self._check_rate_limit(api_key_obj):
                return None
            
            # Update last used
            api_key_obj.last_used = datetime.now()
            await self._update_api_key_usage(api_key_obj)
            
            return api_key_obj
            
        except Exception as e:
            return None
    
    async def revoke_api_key(self, key_id: str, developer_email: str) -> Dict:
        """Revoke API key"""
        
        try:
            api_key_obj = await self._get_api_key_by_id(key_id)
            
            if not api_key_obj:
                return {'success': False, 'error': 'API key not found'}
            
            if api_key_obj.developer_email != developer_email:
                return {'success': False, 'error': 'Access denied'}
            
            api_key_obj.status = 'revoked'
            await self._save_api_key(api_key_obj)
            
            return {'success': True, 'message': 'API key revoked successfully'}
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def get_usage_statistics(self, api_key: str) -> Dict:
        """Get usage statistics for API key"""
        
        try:
            api_key_obj = await self._get_api_key(api_key)
            
            if not api_key_obj:
                return {'success': False, 'error': 'Invalid API key'}
            
            # Calculate usage statistics
            usage_stats = await self._calculate_usage_stats(api_key_obj)
            
            return {
                'success': True,
                'statistics': usage_stats
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    # Internal methods
    
    async def _save_api_key(self, api_key_obj: APIKey):
        """Save API key to database"""
        # This would save to actual database
        cache_key = f"api_key:{api_key_obj.api_key}"
        cache.set(cache_key, asdict(api_key_obj), timeout=86400)  # 24 hours
    
    async def _get_api_key(self, api_key: str) -> Optional[APIKey]:
        """Get API key from database/cache"""
        cache_key = f"api_key:{api_key}"
        data = cache.get(cache_key)
        
        if data:
            # Convert back to datetime objects
            if isinstance(data['created_at'], str):
                data['created_at'] = datetime.fromisoformat(data['created_at'])
            if data['last_used'] and isinstance(data['last_used'], str):
                data['last_used'] = datetime.fromisoformat(data['last_used'])
            
            return APIKey(**data)
        
        return None
    
    async def _get_api_key_by_id(self, key_id: str) -> Optional[APIKey]:
        """Get API key by key ID"""
        # This would query database by key_id
        # For now, return None as it's not implemented
        return None
    
    async def _check_rate_limit(self, api_key_obj: APIKey) -> bool:
        """Check if API key is within rate limits"""
        
        current_time = datetime.now()
        window_start = current_time - timedelta(seconds=api_key_obj.rate_limit_window)
        
        # Get current usage count from cache
        usage_key = f"rate_limit:{api_key_obj.key_id}:{current_time.strftime('%Y%m%d%H')}"
        current_usage = cache.get(usage_key, 0)
        
        if current_usage >= api_key_obj.rate_limit:
            return False
        
        # Increment usage
        cache.set(usage_key, current_usage + 1, timeout=3600)
        
        return True
    
    async def _update_api_key_usage(self, api_key_obj: APIKey):
        """Update API key last used timestamp"""
        cache_key = f"api_key:{api_key_obj.api_key}"
        cache.set(cache_key, asdict(api_key_obj), timeout=86400)
    
    async def _calculate_usage_stats(self, api_key_obj: APIKey) -> Dict:
        """Calculate usage statistics for API key"""
        
        # Mock usage statistics
        return {
            'total_requests': 2547,
            'requests_this_month': 1234,
            'requests_today': 89,
            'remaining_requests': api_key_obj.rate_limit - 89,
            'tier': api_key_obj.tier,
            'features_used': ['rate_quotes', 'shipment_tracking'],
            'last_request': api_key_obj.last_used.isoformat() if api_key_obj.last_used else None,
            'success_rate': 98.5,
            'average_response_time': 245  # ms
        }


class WebhookManager:
    """Manages webhook subscriptions and delivery"""
    
    def __init__(self):
        self.webhook_events = [
            'shipment.created',
            'shipment.updated',
            'shipment.delivered',
            'quote.received',
            'quote.accepted',
            'payment.completed',
            'customs.cleared'
        ]
    
    async def send_webhook(self, api_key_obj: APIKey, event_type: str, payload: Dict) -> Dict:
        """Send webhook notification to developer endpoint"""
        
        if not api_key_obj.webhook_url or 'webhooks' not in api_key_obj.features_enabled:
            return {'success': False, 'error': 'Webhooks not enabled'}
        
        try:
            # Create webhook payload
            webhook_payload = {
                'event': event_type,
                'timestamp': datetime.now().isoformat(),
                'data': payload,
                'api_version': '2025-01'
            }
            
            # Create signature
            signature = self._create_webhook_signature(
                json.dumps(webhook_payload),
                api_key_obj.webhook_secret
            )
            
            # Send webhook (simulated)
            headers = {
                'Content-Type': 'application/json',
                'X-Cloverics-Signature': signature,
                'X-Cloverics-Event': event_type,
                'User-Agent': 'Cloverics-Webhooks/1.0'
            }
            
            # Log webhook attempt
            await self._log_webhook_attempt(api_key_obj.key_id, event_type, webhook_payload, 'success')
            
            return {
                'success': True,
                'webhook_id': str(uuid.uuid4()),
                'event_type': event_type,
                'delivered_at': datetime.now().isoformat()
            }
            
        except Exception as e:
            await self._log_webhook_attempt(api_key_obj.key_id, event_type, {}, f'failed: {str(e)}')
            return {'success': False, 'error': str(e)}
    
    def _create_webhook_signature(self, payload: str, secret: str) -> str:
        """Create HMAC signature for webhook payload"""
        return hmac.new(
            secret.encode('utf-8'),
            payload.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
    
    async def _log_webhook_attempt(self, key_id: str, event_type: str, payload: Dict, status: str):
        """Log webhook delivery attempt"""
        log_entry = {
            'key_id': key_id,
            'event_type': event_type,
            'status': status,
            'timestamp': datetime.now().isoformat(),
            'payload_size': len(json.dumps(payload))
        }
        # This would log to database
        cache.set(f"webhook_log:{uuid.uuid4()}", log_entry, timeout=86400 * 30)  # 30 days


class PublicAPIDocumentation:
    """Manages API documentation and endpoint definitions"""
    
    def __init__(self):
        self.api_endpoints = self._load_api_endpoints()
    
    def _load_api_endpoints(self) -> List[APIEndpoint]:
        """Load all available API endpoints"""
        
        return [
            APIEndpoint(
                endpoint="/api/v1/quotes/request",
                method="POST",
                description="Request shipping quotes from multiple providers",
                parameters=[
                    {"name": "origin_country", "type": "string", "required": True, "description": "Origin country code"},
                    {"name": "destination_country", "type": "string", "required": True, "description": "Destination country code"},
                    {"name": "weight_kg", "type": "number", "required": True, "description": "Shipment weight in kilograms"},
                    {"name": "cargo_type", "type": "string", "required": False, "description": "Type of cargo (general, fragile, hazardous)"},
                    {"name": "transport_mode", "type": "string", "required": False, "description": "Preferred transport mode (truck, ship, air, rail)"}
                ],
                response_example={
                    "success": True,
                    "quote_batch_id": "qb_abc123",
                    "quotes": [
                        {
                            "provider_id": "provider_xyz",
                            "price": 125.50,
                            "currency": "USD",
                            "estimated_days": 5,
                            "transport_mode": "truck"
                        }
                    ]
                },
                rate_limit=50,
                tier_required="free",
                authentication_required=True,
                category="quotes"
            ),
            APIEndpoint(
                endpoint="/api/v1/shipments/{shipment_id}/track",
                method="GET",
                description="Track shipment status and location",
                parameters=[
                    {"name": "shipment_id", "type": "string", "required": True, "description": "Unique shipment identifier"}
                ],
                response_example={
                    "success": True,
                    "shipment": {
                        "id": "ship_123",
                        "status": "in_transit",
                        "current_location": "Hamburg, Germany",
                        "estimated_delivery": "2025-07-10T14:30:00Z",
                        "tracking_events": []
                    }
                },
                rate_limit=100,
                tier_required="free",
                authentication_required=True,
                category="tracking"
            ),
            APIEndpoint(
                endpoint="/api/v1/shipments/create",
                method="POST",
                description="Create new shipment booking",
                parameters=[
                    {"name": "quote_id", "type": "string", "required": True, "description": "Accepted quote identifier"},
                    {"name": "shipper_details", "type": "object", "required": True, "description": "Shipper contact information"},
                    {"name": "consignee_details", "type": "object", "required": True, "description": "Consignee contact information"},
                    {"name": "cargo_description", "type": "string", "required": True, "description": "Detailed cargo description"}
                ],
                response_example={
                    "success": True,
                    "shipment": {
                        "id": "ship_456",
                        "tracking_number": "CL789ABC",
                        "status": "created",
                        "estimated_pickup": "2025-07-05T10:00:00Z"
                    }
                },
                rate_limit=25,
                tier_required="basic",
                authentication_required=True,
                category="shipments"
            ),
            APIEndpoint(
                endpoint="/api/v1/analytics/performance",
                method="GET",
                description="Get shipment performance analytics",
                parameters=[
                    {"name": "date_from", "type": "string", "required": False, "description": "Start date (ISO format)"},
                    {"name": "date_to", "type": "string", "required": False, "description": "End date (ISO format)"},
                    {"name": "metric", "type": "string", "required": False, "description": "Specific metric (delivery_time, cost_savings, success_rate)"}
                ],
                response_example={
                    "success": True,
                    "analytics": {
                        "total_shipments": 245,
                        "average_delivery_time": 4.2,
                        "cost_savings": 15.8,
                        "success_rate": 98.5
                    }
                },
                rate_limit=10,
                tier_required="pro",
                authentication_required=True,
                category="analytics"
            ),
            APIEndpoint(
                endpoint="/api/v1/logistics-index/current",
                method="GET",
                description="Get current Cloverics Logistics Index (CLI)",
                parameters=[],
                response_example={
                    "success": True,
                    "data": {
                        "cli_value": 102.5,
                        "change_percentage": 2.3,
                        "trend": "up",
                        "components": {
                            "ocean_freight": {"value": 98.7},
                            "road_transport": {"value": 105.2}
                        }
                    }
                },
                rate_limit=20,
                tier_required="basic",
                authentication_required=True,
                category="market_data"
            ),
            APIEndpoint(
                endpoint="/api/v1/documents/generate",
                method="POST",
                description="Generate shipping documents (Bills of Lading, Commercial Invoices)",
                parameters=[
                    {"name": "shipment_id", "type": "string", "required": True, "description": "Shipment identifier"},
                    {"name": "document_types", "type": "array", "required": True, "description": "Types of documents to generate"}
                ],
                response_example={
                    "success": True,
                    "documents": [
                        {
                            "document_type": "bill_of_lading",
                            "document_id": "BOL-20250703-ABC123",
                            "download_url": "/api/v1/documents/download/BOL-20250703-ABC123"
                        }
                    ]
                },
                rate_limit=15,
                tier_required="pro",
                authentication_required=True,
                category="documents"
            )
        ]
    
    def get_documentation(self, tier: str = 'free') -> Dict:
        """Get API documentation for specific tier"""
        
        # Filter endpoints by tier
        available_endpoints = []
        tier_hierarchy = ['free', 'basic', 'pro', 'enterprise']
        tier_level = tier_hierarchy.index(tier) if tier in tier_hierarchy else 0
        
        for endpoint in self.api_endpoints:
            endpoint_tier_level = tier_hierarchy.index(endpoint.tier_required)
            if tier_level >= endpoint_tier_level:
                available_endpoints.append(endpoint)
        
        # Group by category
        categories = {}
        for endpoint in available_endpoints:
            if endpoint.category not in categories:
                categories[endpoint.category] = []
            categories[endpoint.category].append(asdict(endpoint))
        
        return {
            'api_version': '1.0',
            'base_url': 'https://api.cloverics.com',
            'authentication': {
                'type': 'API Key',
                'header': 'Authorization: Bearer YOUR_API_KEY',
                'description': 'Include your API key in the Authorization header'
            },
            'rate_limiting': {
                'header': 'X-RateLimit-Remaining',
                'description': 'Check remaining requests in current window'
            },
            'categories': categories,
            'total_endpoints': len(available_endpoints),
            'tier': tier,
            'webhook_events': [
                {
                    'event': 'shipment.created',
                    'description': 'Triggered when a new shipment is created',
                    'tier_required': 'pro'
                },
                {
                    'event': 'shipment.delivered',
                    'description': 'Triggered when a shipment is delivered',
                    'tier_required': 'basic'
                },
                {
                    'event': 'quote.received',
                    'description': 'Triggered when a new quote is received',
                    'tier_required': 'basic'
                }
            ]
        }
    
    def get_openapi_spec(self) -> Dict:
        """Generate OpenAPI 3.0 specification"""
        
        paths = {}
        for endpoint in self.api_endpoints:
            if endpoint.endpoint not in paths:
                paths[endpoint.endpoint] = {}
            
            method = endpoint.method.lower()
            paths[endpoint.endpoint][method] = {
                'summary': endpoint.description,
                'parameters': endpoint.parameters,
                'responses': {
                    '200': {
                        'description': 'Successful response',
                        'content': {
                            'application/json': {
                                'example': endpoint.response_example
                            }
                        }
                    }
                },
                'security': [{'ApiKeyAuth': []}] if endpoint.authentication_required else [],
                'tags': [endpoint.category]
            }
        
        return {
            'openapi': '3.0.3',
            'info': {
                'title': 'Cloverics Logistics API',
                'version': '1.0.0',
                'description': 'Comprehensive logistics and shipping API',
                'contact': {
                    'name': 'API Support',
                    'email': '<EMAIL>'
                }
            },
            'servers': [
                {
                    'url': 'https://api.cloverics.com/v1',
                    'description': 'Production server'
                }
            ],
            'components': {
                'securitySchemes': {
                    'ApiKeyAuth': {
                        'type': 'apiKey',
                        'in': 'header',
                        'name': 'Authorization'
                    }
                }
            },
            'paths': paths
        }


class DeveloperPortal:
    """Main developer portal interface"""
    
    def __init__(self):
        self.key_manager = APIKeyManager()
        self.webhook_manager = WebhookManager()
        self.documentation = PublicAPIDocumentation()
    
    async def register_developer(self, registration_data: Dict) -> Dict:
        """Register new developer and create API key"""
        
        try:
            # Validate registration data
            required_fields = ['email', 'company_name', 'use_case']
            for field in required_fields:
                if field not in registration_data:
                    return {'success': False, 'error': f'Missing required field: {field}'}
            
            # Create API key
            api_key_result = await self.key_manager.create_api_key(registration_data)
            
            if not api_key_result['success']:
                return api_key_result
            
            # Get documentation for their tier
            docs = self.documentation.get_documentation(api_key_result['tier'])
            
            return {
                'success': True,
                'developer_id': str(uuid.uuid4()),
                'api_credentials': api_key_result,
                'getting_started': {
                    'documentation_url': '/developers/docs',
                    'example_requests': '/developers/examples',
                    'testing_environment': '/developers/sandbox'
                },
                'available_endpoints': len(docs['categories']),
                'next_steps': [
                    'Review API documentation',
                    'Test endpoints in sandbox',
                    'Configure webhooks (Pro tier)',
                    'Implement authentication'
                ]
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def get_developer_dashboard_data(self, api_key: str) -> Dict:
        """Get dashboard data for developer portal"""
        
        try:
            api_key_obj = await self.key_manager.validate_api_key(api_key)
            if not api_key_obj:
                return {'success': False, 'error': 'Invalid API key'}
            
            # Get usage statistics
            usage_stats = await self.key_manager.get_usage_statistics(api_key)
            
            # Get available documentation
            docs = self.documentation.get_documentation(api_key_obj.tier)
            
            return {
                'success': True,
                'developer_info': {
                    'email': api_key_obj.developer_email,
                    'company': api_key_obj.company_name,
                    'tier': api_key_obj.tier,
                    'status': api_key_obj.status,
                    'member_since': api_key_obj.created_at.isoformat()
                },
                'api_key_info': {
                    'key_id': api_key_obj.key_id,
                    'rate_limit': api_key_obj.rate_limit,
                    'features_enabled': api_key_obj.features_enabled,
                    'last_used': api_key_obj.last_used.isoformat() if api_key_obj.last_used else None
                },
                'usage_statistics': usage_stats['statistics'] if usage_stats['success'] else {},
                'available_endpoints': len(docs['categories']),
                'webhook_configured': bool(api_key_obj.webhook_url),
                'documentation': docs
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}