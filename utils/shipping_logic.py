"""
Shipping Logic Utilities

This module handles the business logic for domestic vs international shipping,
including customs requirements and validation.
"""

def is_domestic_shipping(origin_country, destination_country):
    """
    Determine if a shipment is domestic based on origin and destination countries.
    
    Args:
        origin_country (str): Origin country name
        destination_country (str): Destination country name
    
    Returns:
        bool: True if domestic, False if international
    """
    return origin_country.strip().lower() == destination_country.strip().lower()

def get_shipping_type(origin_country, destination_country):
    """
    Get the shipping type based on origin and destination countries.
    
    Args:
        origin_country (str): Origin country name
        destination_country (str): Destination country name
    
    Returns:
        str: 'domestic' or 'international'
    """
    return 'domestic' if is_domestic_shipping(origin_country, destination_country) else 'international'

def requires_customs_clearance(shipping_type, has_customs_documents=True):
    """
    Determine if customs clearance is required for a shipment.
    
    Args:
        shipping_type (str): 'domestic' or 'international'
        has_customs_documents (bool): Whether customs documents are available
    
    Returns:
        bool: True if customs clearance is required
    """
    if shipping_type == 'domestic':
        return False
    return shipping_type == 'international' and has_customs_documents

def get_required_documents(shipping_type):
    """
    Get the list of required documents based on shipping type.
    
    Args:
        shipping_type (str): 'domestic' or 'international'
    
    Returns:
        list: List of required document types
    """
    if shipping_type == 'domestic':
        return [
            'Commercial Invoice',
            'Packing List',
            'Bill of Lading'
        ]
    else:  # international
        return [
            'Commercial Invoice',
            'Packing List',
            'Bill of Lading',
            'Export License',
            'Certificate of Origin',
            'Customs Declaration',
            'Insurance Certificate'
        ]

def calculate_shipping_duration(shipping_type, transport_type, distance_km=None):
    """
    Calculate estimated shipping duration based on type and transport mode.
    
    Args:
        shipping_type (str): 'domestic' or 'international'
        transport_type (str): Transport mode ('Sea', 'Air', 'Rail', 'Road')
        distance_km (float, optional): Distance in kilometers
    
    Returns:
        dict: Duration estimates with min/max days
    """
    base_durations = {
        'Sea': {'domestic': (5, 10), 'international': (15, 30)},
        'Air': {'domestic': (1, 2), 'international': (2, 5)},
        'Rail': {'domestic': (3, 7), 'international': (10, 20)},
        'Road': {'domestic': (1, 5), 'international': (5, 15)}
    }
    
    if transport_type not in base_durations:
        transport_type = 'Sea'  # Default fallback
    
    min_days, max_days = base_durations[transport_type][shipping_type]
    
    # Adjust for distance if provided
    if distance_km:
        if distance_km > 5000:  # Long distance
            min_days = int(min_days * 1.3)
            max_days = int(max_days * 1.3)
        elif distance_km < 1000:  # Short distance
            min_days = max(1, int(min_days * 0.7))
            max_days = max(2, int(max_days * 0.7))
    
    return {
        'min_days': min_days,
        'max_days': max_days,
        'estimate_text': f"{min_days}-{max_days} days"
    }

def get_shipping_restrictions(shipping_type, transport_type):
    """
    Get shipping restrictions based on type and transport mode.
    
    Args:
        shipping_type (str): 'domestic' or 'international'
        transport_type (str): Transport mode
    
    Returns:
        dict: Restrictions and guidelines
    """
    restrictions = {
        'weight_limit_kg': None,
        'volume_limit_m3': None,
        'prohibited_items': [],
        'special_requirements': []
    }
    
    # Common prohibited items for international shipping
    if shipping_type == 'international':
        restrictions['prohibited_items'].extend([
            'Hazardous chemicals',
            'Weapons and ammunition',
            'Perishable foods (without permits)',
            'Live animals (without permits)',
            'Currency and precious metals (above limits)'
        ])
        restrictions['special_requirements'].extend([
            'Customs declaration required',
            'Export/import licenses may be needed',
            'Additional insurance recommended'
        ])
    
    # Transport-specific restrictions
    if transport_type == 'Air':
        restrictions['weight_limit_kg'] = 500
        restrictions['prohibited_items'].extend([
            'Lithium batteries (bulk)',
            'Flammable liquids',
            'Compressed gases'
        ])
    elif transport_type == 'Sea':
        restrictions['weight_limit_kg'] = 25000
        restrictions['volume_limit_m3'] = 50
    
    return restrictions

def validate_shipping_configuration(origin_country, destination_country, transport_type, cargo_details):
    """
    Validate a shipping configuration for compliance and feasibility.
    
    Args:
        origin_country (str): Origin country
        destination_country (str): Destination country
        transport_type (str): Transport mode
        cargo_details (dict): Cargo information
    
    Returns:
        dict: Validation results with errors and warnings
    """
    shipping_type = get_shipping_type(origin_country, destination_country)
    restrictions = get_shipping_restrictions(shipping_type, transport_type)
    
    validation = {
        'is_valid': True,
        'errors': [],
        'warnings': [],
        'shipping_type': shipping_type
    }
    
    # Check weight limits
    if restrictions['weight_limit_kg'] and cargo_details.get('weight_kg', 0) > restrictions['weight_limit_kg']:
        validation['errors'].append(f"Weight exceeds {transport_type} limit of {restrictions['weight_limit_kg']} kg")
        validation['is_valid'] = False
    
    # Check volume limits
    if restrictions['volume_limit_m3'] and cargo_details.get('volume_m3', 0) > restrictions['volume_limit_m3']:
        validation['errors'].append(f"Volume exceeds {transport_type} limit of {restrictions['volume_limit_m3']} m³")
        validation['is_valid'] = False
    
    # Check for prohibited items
    cargo_description = cargo_details.get('description', '').lower()
    for prohibited_item in restrictions['prohibited_items']:
        if any(word in cargo_description for word in prohibited_item.lower().split()):
            validation['warnings'].append(f"Possible prohibited item detected: {prohibited_item}")
    
    # Check hazardous materials
    if cargo_details.get('has_hazardous_items'):
        if transport_type == 'Air':
            validation['warnings'].append("Hazardous materials have strict regulations for air transport")
        validation['warnings'].append("Special handling and documentation required for hazardous materials")
    
    return validation