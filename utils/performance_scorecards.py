# Performance Scorecards System
# Comprehensive provider evaluation and scoring system

import os
import django
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Dict, List, Optional, Tuple
import json
import statistics
from django.utils import timezone
from django.db.models import Avg, Count, Q, F
from asgiref.sync import sync_to_async

# Configure Django settings
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'cloverics_django.cloverics.settings')
django.setup()

from core.models import User, Notification
from shipments.models import Shipment, ShippingRate, QuoteRequest

class PerformanceScorecardEngine:
    """
    Comprehensive performance evaluation system for logistics providers
    """
    
    def __init__(self):
        self.scoring_weights = {
            'on_time_delivery': 0.25,
            'customer_rating': 0.20,
            'response_time': 0.15,
            'completion_rate': 0.15,
            'pricing_competitiveness': 0.10,
            'communication_quality': 0.10,
            'reliability_score': 0.05
        }
        
        self.performance_thresholds = {
            'excellent': 4.5,
            'good': 3.5,
            'average': 2.5,
            'poor': 1.5
        }
    
    @sync_to_async
    def calculate_provider_scorecard(self, provider_id: int, days_back: int = 90) -> Dict:
        """
        Calculate comprehensive scorecard for a logistics provider
        """
        try:
            provider = User.objects.get(id=provider_id, user_type='LOGISTICS_PROVIDER')
            cutoff_date = timezone.now() - timedelta(days=days_back)
            
            # Get provider's shipments
            shipments = Shipment.objects.filter(
                logistics_provider=provider,
                created_at__gte=cutoff_date
            )
            
            # Get provider's quotes
            quotes = QuoteRequest.objects.filter(
                logistics_provider=provider,
                created_at__gte=cutoff_date
            )
            
            # Calculate individual metrics
            on_time_delivery = self._calculate_on_time_delivery(shipments)
            customer_rating = self._calculate_customer_rating(shipments)
            response_time = self._calculate_response_time(quotes)
            completion_rate = self._calculate_completion_rate(shipments)
            pricing_competitiveness = self._calculate_pricing_competitiveness(provider, cutoff_date)
            communication_quality = self._calculate_communication_quality(shipments)
            reliability_score = self._calculate_reliability_score(shipments, quotes)
            
            # Calculate weighted overall score
            overall_score = (
                on_time_delivery * self.scoring_weights['on_time_delivery'] +
                customer_rating * self.scoring_weights['customer_rating'] +
                response_time * self.scoring_weights['response_time'] +
                completion_rate * self.scoring_weights['completion_rate'] +
                pricing_competitiveness * self.scoring_weights['pricing_competitiveness'] +
                communication_quality * self.scoring_weights['communication_quality'] +
                reliability_score * self.scoring_weights['reliability_score']
            )
            
            # Generate performance badges
            badges = self._generate_performance_badges(
                on_time_delivery, customer_rating, response_time, 
                completion_rate, pricing_competitiveness
            )
            
            # Calculate trends
            trends = self._calculate_performance_trends(provider, cutoff_date)
            
            # Generate recommendations
            recommendations = self._generate_recommendations(
                on_time_delivery, customer_rating, response_time, completion_rate
            )
            
            scorecard = {
                'provider_id': provider_id,
                'provider_name': provider.company_name or f"{provider.first_name} {provider.last_name}",
                'calculation_date': timezone.now().isoformat(),
                'period_days': days_back,
                'overall_score': round(overall_score, 2),
                'performance_tier': self._get_performance_tier(overall_score),
                'metrics': {
                    'on_time_delivery': {
                        'score': round(on_time_delivery, 2),
                        'percentage': round(on_time_delivery * 20, 1),  # Convert to percentage
                        'weight': self.scoring_weights['on_time_delivery']
                    },
                    'customer_rating': {
                        'score': round(customer_rating, 2),
                        'rating': round(customer_rating, 1),
                        'weight': self.scoring_weights['customer_rating']
                    },
                    'response_time': {
                        'score': round(response_time, 2),
                        'average_hours': self._get_average_response_hours(quotes),
                        'weight': self.scoring_weights['response_time']
                    },
                    'completion_rate': {
                        'score': round(completion_rate, 2),
                        'percentage': round(completion_rate * 20, 1),
                        'weight': self.scoring_weights['completion_rate']
                    },
                    'pricing_competitiveness': {
                        'score': round(pricing_competitiveness, 2),
                        'market_position': self._get_pricing_position(pricing_competitiveness),
                        'weight': self.scoring_weights['pricing_competitiveness']
                    },
                    'communication_quality': {
                        'score': round(communication_quality, 2),
                        'rating': round(communication_quality, 1),
                        'weight': self.scoring_weights['communication_quality']
                    },
                    'reliability_score': {
                        'score': round(reliability_score, 2),
                        'consistency_level': self._get_reliability_level(reliability_score),
                        'weight': self.scoring_weights['reliability_score']
                    }
                },
                'badges': badges,
                'trends': trends,
                'recommendations': recommendations,
                'statistics': {
                    'total_shipments': shipments.count(),
                    'total_quotes': quotes.count(),
                    'active_routes': self._get_active_routes_count(provider),
                    'revenue_generated': self._calculate_revenue_generated(shipments),
                    'customer_count': self._get_unique_customers_count(shipments)
                }
            }
            
            return scorecard
            
        except User.DoesNotExist:
            return {'error': 'Provider not found'}
        except Exception as e:
            return {'error': f'Error calculating scorecard: {str(e)}'}
    
    def _calculate_on_time_delivery(self, shipments) -> float:
        """Calculate on-time delivery performance (0-5 scale)"""
        completed_shipments = shipments.filter(status='DELIVERED')
        if not completed_shipments.exists():
            return 3.0  # Neutral score for new providers
        
        on_time_count = 0
        total_count = completed_shipments.count()
        
        for shipment in completed_shipments:
            if hasattr(shipment, 'estimated_delivery_date') and hasattr(shipment, 'actual_delivery_date'):
                if shipment.actual_delivery_date and shipment.estimated_delivery_date:
                    if shipment.actual_delivery_date <= shipment.estimated_delivery_date:
                        on_time_count += 1
                else:
                    # Check tracking updates for delivery confirmation
                    tracking_updates = TrackingUpdate.objects.filter(shipment=shipment)
                    if tracking_updates.exists():
                        on_time_count += 1  # Assume on-time if tracking exists
        
        on_time_percentage = on_time_count / total_count if total_count > 0 else 0
        return min(5.0, on_time_percentage * 5)  # Scale to 0-5
    
    def _calculate_customer_rating(self, shipments) -> float:
        """Calculate average customer rating (0-5 scale)"""
        rated_shipments = shipments.exclude(customer_rating__isnull=True)
        if not rated_shipments.exists():
            return 3.5  # Default rating for new providers
        
        avg_rating = rated_shipments.aggregate(Avg('customer_rating'))['customer_rating__avg']
        return float(avg_rating) if avg_rating else 3.5
    
    def _calculate_response_time(self, quotes) -> float:
        """Calculate quote response time performance (0-5 scale)"""
        if not quotes.exists():
            return 3.0  # Neutral score for new providers
        
        response_times = []
        for quote in quotes:
            if quote.created_at and quote.updated_at:
                response_time_hours = (quote.updated_at - quote.created_at).total_seconds() / 3600
                response_times.append(response_time_hours)
        
        if not response_times:
            return 3.0
        
        avg_response_time = statistics.mean(response_times)
        
        # Score based on response time (lower is better)
        if avg_response_time <= 2:  # Within 2 hours
            return 5.0
        elif avg_response_time <= 6:  # Within 6 hours
            return 4.0
        elif avg_response_time <= 24:  # Within 24 hours
            return 3.0
        elif avg_response_time <= 48:  # Within 48 hours
            return 2.0
        else:
            return 1.0
    
    def _calculate_completion_rate(self, shipments) -> float:
        """Calculate shipment completion rate (0-5 scale)"""
        if not shipments.exists():
            return 3.0  # Neutral score for new providers
        
        total_shipments = shipments.count()
        completed_shipments = shipments.filter(
            status__in=['DELIVERED', 'COMPLETED']
        ).count()
        
        completion_rate = completed_shipments / total_shipments if total_shipments > 0 else 0
        return min(5.0, completion_rate * 5)  # Scale to 0-5
    
    def _calculate_pricing_competitiveness(self, provider, cutoff_date) -> float:
        """Calculate pricing competitiveness (0-5 scale)"""
        try:
            provider_rates = ShippingRate.objects.filter(
                logistics_provider=provider,
                created_at__gte=cutoff_date
            )
            
            if not provider_rates.exists():
                return 3.0  # Neutral score for new providers
            
            # Compare with market average for similar routes
            total_competitiveness = 0
            comparison_count = 0
            
            for rate in provider_rates:
                # Find similar routes from other providers
                similar_routes = ShippingRate.objects.filter(
                    route__origin_country=rate.route.origin_country,
                    route__destination_country=rate.route.destination_country,
                    transport_type=rate.transport_type,
                    created_at__gte=cutoff_date
                ).exclude(logistics_provider=provider)
                
                if similar_routes.exists():
                    avg_market_price = similar_routes.aggregate(
                        Avg('base_price_per_kg')
                    )['base_price_per_kg__avg']
                    
                    if avg_market_price and rate.base_price_per_kg:
                        # Calculate competitiveness (lower price = higher score)
                        price_ratio = float(rate.base_price_per_kg) / float(avg_market_price)
                        if price_ratio <= 0.8:  # 20% below market
                            competitiveness = 5.0
                        elif price_ratio <= 0.9:  # 10% below market
                            competitiveness = 4.0
                        elif price_ratio <= 1.1:  # Within 10% of market
                            competitiveness = 3.0
                        elif price_ratio <= 1.2:  # 20% above market
                            competitiveness = 2.0
                        else:  # More than 20% above market
                            competitiveness = 1.0
                        
                        total_competitiveness += competitiveness
                        comparison_count += 1
            
            return total_competitiveness / comparison_count if comparison_count > 0 else 3.0
            
        except Exception:
            return 3.0  # Fallback score
    
    def _calculate_communication_quality(self, shipments) -> float:
        """Calculate communication quality score (0-5 scale)"""
        # Base score - can be enhanced with actual communication tracking
        if not shipments.exists():
            return 3.5  # Default for new providers
        
        # For now, use tracking updates as proxy for communication
        total_shipments = shipments.count()
        shipments_with_tracking = 0
        
        for shipment in shipments:
            tracking_updates = TrackingUpdate.objects.filter(shipment=shipment)
            if tracking_updates.count() >= 2:  # At least 2 tracking updates
                shipments_with_tracking += 1
        
        communication_rate = shipments_with_tracking / total_shipments if total_shipments > 0 else 0
        return min(5.0, 3.0 + (communication_rate * 2))  # Scale from 3-5
    
    def _calculate_reliability_score(self, shipments, quotes) -> float:
        """Calculate overall reliability score (0-5 scale)"""
        if not shipments.exists() and not quotes.exists():
            return 3.0  # Neutral for new providers
        
        # Combine various reliability factors
        reliability_factors = []
        
        # Quote acceptance rate
        if quotes.exists():
            accepted_quotes = quotes.filter(status='ACCEPTED').count()
            quote_acceptance_rate = accepted_quotes / quotes.count()
            reliability_factors.append(quote_acceptance_rate * 5)
        
        # Shipment success rate
        if shipments.exists():
            successful_shipments = shipments.filter(
                status__in=['DELIVERED', 'COMPLETED']
            ).count()
            success_rate = successful_shipments / shipments.count()
            reliability_factors.append(success_rate * 5)
        
        # Cancellation rate (inverse)
        if shipments.exists():
            cancelled_shipments = shipments.filter(status='CANCELLED').count()
            cancellation_rate = cancelled_shipments / shipments.count()
            reliability_factors.append((1 - cancellation_rate) * 5)
        
        return statistics.mean(reliability_factors) if reliability_factors else 3.0
    
    def _generate_performance_badges(self, on_time, rating, response, completion, pricing) -> List[Dict]:
        """Generate performance badges based on metrics"""
        badges = []
        
        if on_time >= 4.5:
            badges.append({
                'name': 'On-Time Champion',
                'icon': 'clock',
                'color': 'success',
                'description': 'Consistently delivers on time'
            })
        
        if rating >= 4.5:
            badges.append({
                'name': 'Customer Favorite',
                'icon': 'star',
                'color': 'warning',
                'description': 'Highly rated by customers'
            })
        
        if response >= 4.5:
            badges.append({
                'name': 'Quick Responder',
                'icon': 'lightning',
                'color': 'info',
                'description': 'Fast quote response times'
            })
        
        if completion >= 4.5:
            badges.append({
                'name': 'Reliable Partner',
                'icon': 'shield',
                'color': 'primary',
                'description': 'High completion rate'
            })
        
        if pricing >= 4.0:
            badges.append({
                'name': 'Competitive Pricing',
                'icon': 'dollar',
                'color': 'success',
                'description': 'Market-competitive rates'
            })
        
        # Special combination badges
        if all(score >= 4.0 for score in [on_time, rating, response, completion]):
            badges.append({
                'name': 'Premium Provider',
                'icon': 'award',
                'color': 'primary',
                'description': 'Excellence across all metrics'
            })
        
        return badges
    
    def _calculate_performance_trends(self, provider, cutoff_date) -> Dict:
        """Calculate performance trends over time"""
        try:
            # Get data for different time periods
            periods = {
                'last_30_days': 30,
                'last_60_days': 60,
                'last_90_days': 90
            }
            
            trends = {}
            
            for period_name, days in periods.items():
                period_cutoff = timezone.now() - timedelta(days=days)
                period_shipments = Shipment.objects.filter(
                    logistics_provider=provider,
                    created_at__gte=period_cutoff
                )
                
                if period_shipments.exists():
                    # Calculate average rating for period
                    avg_rating = period_shipments.exclude(
                        customer_rating__isnull=True
                    ).aggregate(Avg('customer_rating'))['customer_rating__avg']
                    
                    # Calculate completion rate for period
                    completed = period_shipments.filter(
                        status__in=['DELIVERED', 'COMPLETED']
                    ).count()
                    completion_rate = (completed / period_shipments.count()) * 100
                    
                    trends[period_name] = {
                        'average_rating': float(avg_rating) if avg_rating else 0,
                        'completion_rate': round(completion_rate, 1),
                        'total_shipments': period_shipments.count()
                    }
                else:
                    trends[period_name] = {
                        'average_rating': 0,
                        'completion_rate': 0,
                        'total_shipments': 0
                    }
            
            # Calculate trend direction
            if (trends['last_30_days']['average_rating'] > 
                trends['last_60_days']['average_rating']):
                rating_trend = 'improving'
            elif (trends['last_30_days']['average_rating'] < 
                  trends['last_60_days']['average_rating']):
                rating_trend = 'declining'
            else:
                rating_trend = 'stable'
            
            trends['trend_analysis'] = {
                'rating_trend': rating_trend,
                'performance_direction': 'improving' if rating_trend == 'improving' else 'stable'
            }
            
            return trends
            
        except Exception:
            return {'trend_analysis': {'rating_trend': 'stable', 'performance_direction': 'stable'}}
    
    def _generate_recommendations(self, on_time, rating, response, completion) -> List[str]:
        """Generate improvement recommendations"""
        recommendations = []
        
        if on_time < 3.5:
            recommendations.append(
                "Improve delivery time accuracy by updating estimated delivery dates"
            )
        
        if rating < 3.5:
            recommendations.append(
                "Focus on customer service quality and shipment handling"
            )
        
        if response < 3.5:
            recommendations.append(
                "Reduce quote response time to improve customer experience"
            )
        
        if completion < 3.5:
            recommendations.append(
                "Investigate causes of incomplete shipments and implement preventive measures"
            )
        
        # Positive recommendations
        if all(score >= 4.0 for score in [on_time, rating, response, completion]):
            recommendations.append(
                "Excellent performance! Consider expanding to new routes or markets"
            )
        
        if not recommendations:
            recommendations.append(
                "Maintain current performance standards and continue monitoring metrics"
            )
        
        return recommendations
    
    def _get_performance_tier(self, score: float) -> str:
        """Determine performance tier based on overall score"""
        if score >= self.performance_thresholds['excellent']:
            return 'Excellent'
        elif score >= self.performance_thresholds['good']:
            return 'Good'
        elif score >= self.performance_thresholds['average']:
            return 'Average'
        else:
            return 'Needs Improvement'
    
    def _get_average_response_hours(self, quotes) -> float:
        """Get average response time in hours"""
        if not quotes.exists():
            return 0
        
        response_times = []
        for quote in quotes:
            if quote.created_at and quote.updated_at:
                hours = (quote.updated_at - quote.created_at).total_seconds() / 3600
                response_times.append(hours)
        
        return round(statistics.mean(response_times), 1) if response_times else 0
    
    def _get_pricing_position(self, score: float) -> str:
        """Get pricing position description"""
        if score >= 4.5:
            return 'Highly Competitive'
        elif score >= 3.5:
            return 'Competitive'
        elif score >= 2.5:
            return 'Market Average'
        else:
            return 'Above Market'
    
    def _get_reliability_level(self, score: float) -> str:
        """Get reliability level description"""
        if score >= 4.5:
            return 'Excellent'
        elif score >= 3.5:
            return 'Good'
        elif score >= 2.5:
            return 'Average'
        else:
            return 'Needs Improvement'
    
    def _get_active_routes_count(self, provider) -> int:
        """Get count of active routes for provider"""
        return ShippingRate.objects.filter(
            logistics_provider=provider,
            is_active=True
        ).count()
    
    def _calculate_revenue_generated(self, shipments) -> float:
        """Calculate total revenue from completed shipments"""
        completed_shipments = shipments.filter(
            status__in=['DELIVERED', 'COMPLETED']
        )
        
        total_revenue = 0
        for shipment in completed_shipments:
            if hasattr(shipment, 'total_price') and shipment.total_price:
                total_revenue += float(shipment.total_price)
        
        return round(total_revenue, 2)
    
    def _get_unique_customers_count(self, shipments) -> int:
        """Get count of unique customers served"""
        return shipments.values('customer').distinct().count()

class ScorecardAlertSystem:
    """
    Alert system for performance scorecard changes
    """
    
    def check_performance_alerts(self, provider_id: int, new_scorecard: Dict) -> List[Dict]:
        """
        Check for performance alerts and generate notifications
        """
        alerts = []
        
        try:
            overall_score = new_scorecard.get('overall_score', 0)
            performance_tier = new_scorecard.get('performance_tier', 'Average')
            
            # Check for low performance alert
            if overall_score < 2.5:
                alerts.append({
                    'type': 'performance_warning',
                    'severity': 'high',
                    'message': f'Performance score has dropped to {overall_score}/5.0',
                    'recommendation': 'Review performance metrics and implement improvements'
                })
            
            # Check individual metric alerts
            metrics = new_scorecard.get('metrics', {})
            
            for metric_name, metric_data in metrics.items():
                score = metric_data.get('score', 0)
                if score < 2.0:
                    alerts.append({
                        'type': 'metric_alert',
                        'severity': 'medium',
                        'metric': metric_name,
                        'message': f'{metric_name.replace("_", " ").title()} score is low: {score}/5.0',
                        'recommendation': f'Focus on improving {metric_name.replace("_", " ")}'
                    })
            
            # Create notifications for alerts
            if alerts:
                try:
                    self._create_performance_notifications(provider_id, alerts)
                except Exception:
                    pass  # Non-blocking notification creation
            
            return alerts
            
        except Exception as e:
            return [{'type': 'error', 'message': f'Error checking alerts: {str(e)}'}]
    
    @sync_to_async
    def _create_performance_notifications(self, provider_id: int, alerts: List[Dict]):
        """Create notifications for performance alerts"""
        try:
            provider = User.objects.get(id=provider_id)
            
            for alert in alerts:
                Notification.objects.create(
                    user=provider,
                    title='Performance Alert',
                    message=alert['message'],
                    notification_type='PERFORMANCE_ALERT',
                    is_read=False
                )
                
        except Exception as e:
            print(f"Error creating performance notifications: {str(e)}")

# Performance Scorecard Utilities
class ScorecardUtils:
    """
    Utility functions for scorecard operations
    """
    
    @staticmethod
    @sync_to_async
    def get_top_performers(limit: int = 10) -> List[Dict]:
        """Get top performing logistics providers"""
        try:
            # This would typically use cached scorecard data
            # For now, we'll return a simplified version
            top_providers = User.objects.filter(
                user_type='LOGISTICS_PROVIDER'
            ).annotate(
                avg_rating=Avg('logistics_shipments__customer_rating'),
                shipment_count=Count('logistics_shipments')
            ).filter(
                shipment_count__gte=5  # Minimum 5 shipments
            ).order_by('-avg_rating')[:limit]
            
            performers = []
            for provider in top_providers:
                performers.append({
                    'provider_id': provider.id,
                    'provider_name': provider.company_name or f"{provider.first_name} {provider.last_name}",
                    'average_rating': round(provider.avg_rating, 2) if provider.avg_rating else 0,
                    'total_shipments': provider.shipment_count,
                    'performance_tier': 'Excellent' if (provider.avg_rating or 0) >= 4.5 else 'Good'
                })
            
            return performers
            
        except Exception as e:
            return [{'error': f'Error getting top performers: {str(e)}'}]
    
    @staticmethod
    @sync_to_async
    def get_performance_summary() -> Dict:
        """Get platform-wide performance summary"""
        try:
            providers = User.objects.filter(user_type='LOGISTICS_PROVIDER')
            total_providers = providers.count()
            
            # Calculate performance distribution
            excellent_count = 0
            good_count = 0
            average_count = 0
            poor_count = 0
            
            for provider in providers:
                avg_rating = Shipment.objects.filter(
                    logistics_provider=provider
                ).exclude(customer_rating__isnull=True).aggregate(
                    Avg('customer_rating')
                )['customer_rating__avg']
                
                if avg_rating:
                    if avg_rating >= 4.5:
                        excellent_count += 1
                    elif avg_rating >= 3.5:
                        good_count += 1
                    elif avg_rating >= 2.5:
                        average_count += 1
                    else:
                        poor_count += 1
                else:
                    average_count += 1  # New providers
            
            return {
                'total_providers': total_providers,
                'performance_distribution': {
                    'excellent': excellent_count,
                    'good': good_count,
                    'average': average_count,
                    'poor': poor_count
                },
                'average_platform_rating': Shipment.objects.exclude(
                    customer_rating__isnull=True
                ).aggregate(Avg('customer_rating'))['customer_rating__avg'] or 0
            }
            
        except Exception as e:
            return {'error': f'Error getting performance summary: {str(e)}'}