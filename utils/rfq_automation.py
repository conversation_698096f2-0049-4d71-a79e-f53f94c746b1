"""
Advanced RFQ (Request for Quote) Automation System
Comprehensive enterprise-grade RFQ management with AI-powered evaluation, 
scenario modeling, and structured negotiation workflows.
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum
import json
import uuid

logger = logging.getLogger(__name__)

class RFQStatus(Enum):
    DRAFT = "DRAFT"
    PUBLISHED = "PUBLISHED"
    RESPONSES_RECEIVED = "RESPONSES_RECEIVED"
    EVALUATION_COMPLETE = "EVALUATION_COMPLETE"
    NEGOTIATION_IN_PROGRESS = "NEGOTIATION_IN_PROGRESS"
    AWARDED = "AWARDED"
    CANCELLED = "CANCELLED"

class RFQPriority(Enum):
    LOW = "LOW"
    MEDIUM = "MEDIUM"
    HIGH = "HIGH"
    URGENT = "URGENT"

class ScenarioType(Enum):
    SINGLE_ROUTE = "SINGLE_ROUTE"
    MULTI_ROUTE = "MULTI_ROUTE"
    SEASONAL_CONTRACT = "SEASONAL_CONTRACT"
    VOLUME_COMMITMENT = "VOLUME_COMMITMENT"
    EMERGENCY_SHIPPING = "EMERGENCY_SHIPPING"

@dataclass
class RFQScenario:
    scenario_id: str
    scenario_type: ScenarioType
    name: str
    description: str
    requirements: Dict[str, Any]
    evaluation_criteria: Dict[str, float]  # weight for each criterion
    target_providers: List[str]
    deadline: datetime
    budget_range: Dict[str, float]
    special_requirements: List[str]

@dataclass
class RFQResponse:
    response_id: str
    rfq_id: str
    provider_id: str
    provider_name: str
    total_cost: float
    delivery_time: int
    proposal_details: Dict[str, Any]
    compliance_score: float
    response_date: datetime
    validity_period: int
    special_terms: List[str]

@dataclass
class EvaluationResult:
    response_id: str
    overall_score: float
    criteria_scores: Dict[str, float]
    ranking: int
    recommendation: str
    risk_assessment: Dict[str, Any]
    cost_analysis: Dict[str, Any]

class RFQScenarioModeler:
    """Advanced scenario modeling for complex RFQ requirements"""
    
    def __init__(self):
        self.scenario_templates = self._load_scenario_templates()
        self.evaluation_matrix = self._build_evaluation_matrix()
    
    def _load_scenario_templates(self) -> Dict[str, Dict]:
        """Load predefined scenario templates for common RFQ types"""
        return {
            "SINGLE_ROUTE": {
                "name": "Single Route Shipping",
                "criteria": {
                    "cost": 0.35,
                    "delivery_time": 0.25,
                    "reliability": 0.20,
                    "service_quality": 0.15,
                    "insurance_coverage": 0.05
                },
                "requirements": ["pickup_location", "delivery_location", "cargo_details", "delivery_window"]
            },
            "MULTI_ROUTE": {
                "name": "Multi-Route Distribution",
                "criteria": {
                    "cost": 0.30,
                    "delivery_time": 0.20,
                    "reliability": 0.25,
                    "network_coverage": 0.15,
                    "consolidation_capability": 0.10
                },
                "requirements": ["route_matrix", "volume_distribution", "delivery_schedules", "consolidation_points"]
            },
            "SEASONAL_CONTRACT": {
                "name": "Seasonal Contract Agreement",
                "criteria": {
                    "cost": 0.25,
                    "capacity_guarantee": 0.30,
                    "seasonal_flexibility": 0.20,
                    "reliability": 0.15,
                    "peak_support": 0.10
                },
                "requirements": ["seasonal_volumes", "peak_periods", "capacity_requirements", "flexibility_terms"]
            },
            "VOLUME_COMMITMENT": {
                "name": "Volume Commitment Contract",
                "criteria": {
                    "cost": 0.40,
                    "volume_discounts": 0.25,
                    "reliability": 0.15,
                    "service_levels": 0.15,
                    "payment_terms": 0.05
                },
                "requirements": ["annual_volume", "minimum_commitments", "discount_tiers", "service_level_agreements"]
            },
            "EMERGENCY_SHIPPING": {
                "name": "Emergency/Express Shipping",
                "criteria": {
                    "delivery_time": 0.45,
                    "availability": 0.25,
                    "cost": 0.15,
                    "reliability": 0.10,
                    "special_handling": 0.05
                },
                "requirements": ["urgency_level", "special_handling", "tracking_requirements", "insurance_coverage"]
            }
        }
    
    def _build_evaluation_matrix(self) -> Dict[str, Dict]:
        """Build comprehensive evaluation matrix for different scenario types"""
        return {
            "cost_factors": {
                "base_rate": 0.40,
                "fuel_surcharge": 0.15,
                "additional_fees": 0.20,
                "volume_discounts": 0.15,
                "payment_terms": 0.10
            },
            "service_factors": {
                "on_time_delivery": 0.30,
                "damage_rate": 0.25,
                "customer_service": 0.20,
                "tracking_capability": 0.15,
                "claim_handling": 0.10
            },
            "operational_factors": {
                "capacity_availability": 0.35,
                "geographic_coverage": 0.25,
                "equipment_suitability": 0.20,
                "technology_integration": 0.20
            }
        }
    
    async def create_scenario(self, scenario_data: Dict[str, Any]) -> RFQScenario:
        """Create a new RFQ scenario with intelligent modeling"""
        try:
            scenario_type = ScenarioType(scenario_data.get('scenario_type', 'SINGLE_ROUTE'))
            template = self.scenario_templates.get(scenario_type.value, {})
            
            scenario = RFQScenario(
                scenario_id=str(uuid.uuid4()),
                scenario_type=scenario_type,
                name=scenario_data.get('name', template.get('name', 'Custom RFQ')),
                description=scenario_data.get('description', ''),
                requirements=self._merge_requirements(template.get('requirements', []), scenario_data.get('requirements', {})),
                evaluation_criteria=self._customize_criteria(template.get('criteria', {}), scenario_data.get('criteria_weights', {})),
                target_providers=scenario_data.get('target_providers', []),
                deadline=datetime.fromisoformat(scenario_data.get('deadline')) if scenario_data.get('deadline') else datetime.now() + timedelta(days=7),
                budget_range=scenario_data.get('budget_range', {'min': 0, 'max': 100000}),
                special_requirements=scenario_data.get('special_requirements', [])
            )
            
            logger.info(f"Created RFQ scenario: {scenario.scenario_id}")
            return scenario
            
        except Exception as e:
            logger.error(f"Error creating RFQ scenario: {e}")
            raise
    
    def _merge_requirements(self, template_reqs: List[str], custom_reqs: Dict[str, Any]) -> Dict[str, Any]:
        """Merge template requirements with custom requirements"""
        merged = {}
        for req in template_reqs:
            merged[req] = custom_reqs.get(req, "")
        
        # Add any additional custom requirements
        for key, value in custom_reqs.items():
            if key not in merged:
                merged[key] = value
        
        return merged
    
    def _customize_criteria(self, template_criteria: Dict[str, float], custom_weights: Dict[str, float]) -> Dict[str, float]:
        """Customize evaluation criteria weights based on customer preferences"""
        criteria = template_criteria.copy()
        
        # Apply custom weights
        for criterion, weight in custom_weights.items():
            if criterion in criteria:
                criteria[criterion] = weight
        
        # Normalize weights to sum to 1.0
        total_weight = sum(criteria.values())
        if total_weight > 0:
            criteria = {k: v / total_weight for k, v in criteria.items()}
        
        return criteria
    
    async def generate_scenario_variations(self, base_scenario: RFQScenario) -> List[RFQScenario]:
        """Generate scenario variations for comparison analysis"""
        try:
            variations = []
            
            # Cost-optimized variation
            cost_optimized = self._create_variation(base_scenario, "Cost Optimized", {
                "cost": 0.50,
                "delivery_time": 0.20,
                "reliability": 0.15,
                "service_quality": 0.10,
                "insurance_coverage": 0.05
            })
            variations.append(cost_optimized)
            
            # Speed-optimized variation
            speed_optimized = self._create_variation(base_scenario, "Speed Optimized", {
                "delivery_time": 0.45,
                "cost": 0.25,
                "reliability": 0.15,
                "service_quality": 0.10,
                "insurance_coverage": 0.05
            })
            variations.append(speed_optimized)
            
            # Quality-optimized variation
            quality_optimized = self._create_variation(base_scenario, "Quality Optimized", {
                "reliability": 0.35,
                "service_quality": 0.30,
                "cost": 0.20,
                "delivery_time": 0.10,
                "insurance_coverage": 0.05
            })
            variations.append(quality_optimized)
            
            return variations
            
        except Exception as e:
            logger.error(f"Error generating scenario variations: {e}")
            return []
    
    def _create_variation(self, base_scenario: RFQScenario, variation_name: str, criteria: Dict[str, float]) -> RFQScenario:
        """Create a scenario variation with modified criteria"""
        return RFQScenario(
            scenario_id=str(uuid.uuid4()),
            scenario_type=base_scenario.scenario_type,
            name=f"{base_scenario.name} - {variation_name}",
            description=f"Variation of {base_scenario.name} optimized for {variation_name.lower()}",
            requirements=base_scenario.requirements.copy(),
            evaluation_criteria=criteria,
            target_providers=base_scenario.target_providers.copy(),
            deadline=base_scenario.deadline,
            budget_range=base_scenario.budget_range.copy(),
            special_requirements=base_scenario.special_requirements.copy()
        )

class AIResponseEvaluator:
    """AI-powered evaluation system for RFQ responses"""
    
    def __init__(self):
        self.evaluation_algorithms = self._initialize_algorithms()
        self.risk_assessment_models = self._load_risk_models()
    
    def _initialize_algorithms(self) -> Dict[str, callable]:
        """Initialize AI evaluation algorithms"""
        return {
            "cost_analysis": self._analyze_cost_competitiveness,
            "service_evaluation": self._evaluate_service_quality,
            "risk_assessment": self._assess_provider_risk,
            "compliance_check": self._check_compliance,
            "delivery_analysis": self._analyze_delivery_capability
        }
    
    def _load_risk_models(self) -> Dict[str, Dict]:
        """Load risk assessment models"""
        return {
            "financial_risk": {
                "company_size": 0.25,
                "years_in_business": 0.20,
                "credit_rating": 0.30,
                "insurance_coverage": 0.15,
                "financial_stability": 0.10
            },
            "operational_risk": {
                "on_time_performance": 0.30,
                "damage_rate": 0.25,
                "capacity_reliability": 0.20,
                "technology_capability": 0.15,
                "geographic_coverage": 0.10
            },
            "compliance_risk": {
                "regulatory_compliance": 0.40,
                "safety_record": 0.25,
                "environmental_compliance": 0.20,
                "documentation_accuracy": 0.15
            }
        }
    
    async def evaluate_response(self, response: RFQResponse, scenario: RFQScenario) -> EvaluationResult:
        """Comprehensive AI-powered evaluation of RFQ response"""
        try:
            criteria_scores = {}
            
            # Evaluate each criterion
            for criterion, weight in scenario.evaluation_criteria.items():
                if criterion in self.evaluation_algorithms:
                    score = await self.evaluation_algorithms[criterion](response, scenario)
                    criteria_scores[criterion] = score
                else:
                    # Generic evaluation for custom criteria
                    criteria_scores[criterion] = await self._generic_evaluation(criterion, response, scenario)
            
            # Calculate overall score
            overall_score = sum(score * scenario.evaluation_criteria.get(criterion, 0) 
                              for criterion, score in criteria_scores.items())
            
            # Risk assessment
            risk_assessment = await self._comprehensive_risk_assessment(response)
            
            # Cost analysis
            cost_analysis = await self._detailed_cost_analysis(response, scenario)
            
            # Generate recommendation
            recommendation = await self._generate_recommendation(overall_score, criteria_scores, risk_assessment)
            
            return EvaluationResult(
                response_id=response.response_id,
                overall_score=overall_score,
                criteria_scores=criteria_scores,
                ranking=0,  # Will be set during ranking process
                recommendation=recommendation,
                risk_assessment=risk_assessment,
                cost_analysis=cost_analysis
            )
            
        except Exception as e:
            logger.error(f"Error evaluating RFQ response: {e}")
            raise
    
    async def _analyze_cost_competitiveness(self, response: RFQResponse, scenario: RFQScenario) -> float:
        """Analyze cost competitiveness with market benchmarks"""
        try:
            budget_range = scenario.budget_range
            cost = response.total_cost
            
            # Calculate cost score based on budget range
            if budget_range['max'] > budget_range['min']:
                if cost <= budget_range['min']:
                    return 1.0  # Excellent - under budget
                elif cost <= budget_range['max']:
                    # Linear scale between min and max
                    ratio = (budget_range['max'] - cost) / (budget_range['max'] - budget_range['min'])
                    return max(0.5, ratio)
                else:
                    # Over budget - low score
                    over_ratio = (cost - budget_range['max']) / budget_range['max']
                    return max(0.1, 1.0 - over_ratio)
            else:
                # Single budget target
                target = budget_range.get('target', budget_range['max'])
                if cost <= target:
                    return min(1.0, target / cost)
                else:
                    return max(0.1, target / cost)
                    
        except Exception as e:
            logger.error(f"Error in cost analysis: {e}")
            return 0.5
    
    async def _evaluate_service_quality(self, response: RFQResponse, scenario: RFQScenario) -> float:
        """Evaluate service quality metrics"""
        try:
            service_factors = [
                response.proposal_details.get('tracking_capability', 0.5),
                response.proposal_details.get('customer_service_rating', 0.5),
                response.proposal_details.get('insurance_coverage', 0.5),
                response.proposal_details.get('special_handling_capability', 0.5),
                response.compliance_score
            ]
            
            return sum(service_factors) / len(service_factors)
            
        except Exception as e:
            logger.error(f"Error in service evaluation: {e}")
            return 0.5
    
    async def _assess_provider_risk(self, response: RFQResponse, scenario: RFQScenario) -> float:
        """Assess provider risk factors"""
        try:
            risk_factors = response.proposal_details.get('risk_factors', {})
            
            # Calculate risk score (lower is better, so invert for scoring)
            financial_risk = 1.0 - risk_factors.get('financial_risk', 0.3)
            operational_risk = 1.0 - risk_factors.get('operational_risk', 0.3)
            compliance_risk = 1.0 - risk_factors.get('compliance_risk', 0.3)
            
            overall_risk_score = (financial_risk + operational_risk + compliance_risk) / 3
            return max(0.1, overall_risk_score)
            
        except Exception as e:
            logger.error(f"Error in risk assessment: {e}")
            return 0.5
    
    async def _check_compliance(self, response: RFQResponse, scenario: RFQScenario) -> float:
        """Check compliance with RFQ requirements"""
        try:
            return response.compliance_score
            
        except Exception as e:
            logger.error(f"Error in compliance check: {e}")
            return 0.5
    
    async def _analyze_delivery_capability(self, response: RFQResponse, scenario: RFQScenario) -> float:
        """Analyze delivery time and capability"""
        try:
            required_delivery = scenario.requirements.get('delivery_window', 7)
            offered_delivery = response.delivery_time
            
            if offered_delivery <= required_delivery:
                # Early delivery bonus
                return min(1.0, 1.0 + (required_delivery - offered_delivery) * 0.1)
            else:
                # Late delivery penalty
                delay_penalty = (offered_delivery - required_delivery) / required_delivery
                return max(0.1, 1.0 - delay_penalty)
                
        except Exception as e:
            logger.error(f"Error in delivery analysis: {e}")
            return 0.5
    
    async def _generic_evaluation(self, criterion: str, response: RFQResponse, scenario: RFQScenario) -> float:
        """Generic evaluation for custom criteria"""
        try:
            # Look for criterion in proposal details
            if criterion in response.proposal_details:
                value = response.proposal_details[criterion]
                if isinstance(value, (int, float)):
                    return min(1.0, max(0.0, value))
                elif isinstance(value, bool):
                    return 1.0 if value else 0.0
                else:
                    return 0.5  # Default for non-numeric values
            else:
                return 0.5  # Default score for missing criteria
                
        except Exception as e:
            logger.error(f"Error in generic evaluation for {criterion}: {e}")
            return 0.5
    
    async def _comprehensive_risk_assessment(self, response: RFQResponse) -> Dict[str, Any]:
        """Comprehensive risk assessment"""
        try:
            risk_data = response.proposal_details.get('risk_factors', {})
            
            return {
                "overall_risk_level": risk_data.get('overall_risk', 'MEDIUM'),
                "financial_risk_score": risk_data.get('financial_risk', 0.3),
                "operational_risk_score": risk_data.get('operational_risk', 0.3),
                "compliance_risk_score": risk_data.get('compliance_risk', 0.3),
                "risk_mitigation_factors": risk_data.get('mitigation_factors', []),
                "insurance_coverage": risk_data.get('insurance_coverage', 'Standard'),
                "contingency_plans": risk_data.get('contingency_plans', [])
            }
            
        except Exception as e:
            logger.error(f"Error in risk assessment: {e}")
            return {"overall_risk_level": "MEDIUM"}
    
    async def _detailed_cost_analysis(self, response: RFQResponse, scenario: RFQScenario) -> Dict[str, Any]:
        """Detailed cost breakdown analysis"""
        try:
            cost_breakdown = response.proposal_details.get('cost_breakdown', {})
            
            return {
                "total_cost": response.total_cost,
                "base_rate": cost_breakdown.get('base_rate', response.total_cost * 0.7),
                "fuel_surcharge": cost_breakdown.get('fuel_surcharge', response.total_cost * 0.1),
                "additional_fees": cost_breakdown.get('additional_fees', response.total_cost * 0.1),
                "taxes": cost_breakdown.get('taxes', response.total_cost * 0.1),
                "cost_per_unit": response.total_cost / max(1, scenario.requirements.get('quantity', 1)),
                "payment_terms": response.proposal_details.get('payment_terms', 'Net 30'),
                "currency": response.proposal_details.get('currency', 'USD'),
                "cost_validity": response.validity_period
            }
            
        except Exception as e:
            logger.error(f"Error in cost analysis: {e}")
            return {"total_cost": response.total_cost}
    
    async def _generate_recommendation(self, overall_score: float, criteria_scores: Dict[str, float], risk_assessment: Dict[str, Any]) -> str:
        """Generate AI-powered recommendation"""
        try:
            if overall_score >= 0.8:
                recommendation = "HIGHLY RECOMMENDED"
                detail = "Excellent overall performance across all evaluation criteria."
            elif overall_score >= 0.6:
                recommendation = "RECOMMENDED"
                detail = "Good performance with minor areas for improvement."
            elif overall_score >= 0.4:
                recommendation = "CONSIDER WITH CAUTION"
                detail = "Adequate performance but with notable concerns."
            else:
                recommendation = "NOT RECOMMENDED"
                detail = "Poor performance across multiple criteria."
            
            # Add risk-based insights
            risk_level = risk_assessment.get('overall_risk_level', 'MEDIUM')
            if risk_level == 'HIGH':
                detail += " HIGH RISK PROVIDER - Additional due diligence recommended."
            elif risk_level == 'LOW':
                detail += " Low risk provider with strong reliability indicators."
            
            return f"{recommendation}: {detail}"
            
        except Exception as e:
            logger.error(f"Error generating recommendation: {e}")
            return "NEEDS REVIEW: Unable to generate automated recommendation."

class NegotiationWorkflowManager:
    """Structured negotiation workflow management system"""
    
    def __init__(self):
        self.workflow_templates = self._load_workflow_templates()
        self.negotiation_strategies = self._initialize_strategies()
    
    def _load_workflow_templates(self) -> Dict[str, Dict]:
        """Load negotiation workflow templates"""
        return {
            "STANDARD": {
                "name": "Standard Negotiation",
                "stages": ["initial_review", "clarification", "negotiation", "final_terms", "award"],
                "timeouts": {"clarification": 3, "negotiation": 7, "final_terms": 2},
                "auto_actions": ["send_clarification_requests", "schedule_reviews"]
            },
            "EXPEDITED": {
                "name": "Expedited Negotiation",
                "stages": ["fast_review", "direct_negotiation", "immediate_award"],
                "timeouts": {"fast_review": 1, "direct_negotiation": 2, "immediate_award": 1},
                "auto_actions": ["prioritize_responses", "accelerated_evaluation"]
            },
            "COMPLEX": {
                "name": "Complex Multi-Round",
                "stages": ["technical_review", "commercial_review", "legal_review", "final_negotiation", "contract_finalization"],
                "timeouts": {"technical_review": 5, "commercial_review": 4, "legal_review": 3, "final_negotiation": 5, "contract_finalization": 2},
                "auto_actions": ["technical_validation", "commercial_analysis", "legal_compliance_check"]
            }
        }
    
    def _initialize_strategies(self) -> Dict[str, Dict]:
        """Initialize negotiation strategies"""
        return {
            "COST_FOCUSED": {
                "name": "Cost Optimization Strategy",
                "tactics": ["volume_discount_requests", "payment_term_negotiation", "fee_reduction"],
                "success_criteria": {"cost_reduction": 0.1, "maintained_service": True}
            },
            "SERVICE_FOCUSED": {
                "name": "Service Enhancement Strategy", 
                "tactics": ["sla_improvements", "additional_services", "performance_guarantees"],
                "success_criteria": {"service_improvement": 0.15, "acceptable_cost": True}
            },
            "BALANCED": {
                "name": "Balanced Value Strategy",
                "tactics": ["value_optimization", "risk_mitigation", "relationship_building"],
                "success_criteria": {"overall_value": 0.12, "risk_reduction": 0.1}
            }
        }
    
    async def initiate_negotiation(self, evaluation_results: List[EvaluationResult], scenario: RFQScenario) -> Dict[str, Any]:
        """Initiate structured negotiation workflow"""
        try:
            # Select top candidates for negotiation
            top_candidates = sorted(evaluation_results, key=lambda x: x.overall_score, reverse=True)[:3]
            
            # Determine negotiation strategy
            strategy = self._select_negotiation_strategy(scenario, top_candidates)
            
            # Choose workflow template
            workflow_type = self._select_workflow_template(scenario)
            workflow = self.workflow_templates[workflow_type]
            
            negotiation_plan = {
                "negotiation_id": str(uuid.uuid4()),
                "scenario_id": scenario.scenario_id,
                "strategy": strategy,
                "workflow": workflow,
                "candidates": [
                    {
                        "response_id": result.response_id,
                        "ranking": idx + 1,
                        "negotiation_priority": self._calculate_negotiation_priority(result),
                        "negotiation_points": self._identify_negotiation_points(result, scenario)
                    }
                    for idx, result in enumerate(top_candidates)
                ],
                "current_stage": workflow["stages"][0],
                "stage_deadline": datetime.now() + timedelta(days=workflow["timeouts"].get(workflow["stages"][0], 3)),
                "negotiation_history": [],
                "status": "INITIATED"
            }
            
            logger.info(f"Initiated negotiation workflow: {negotiation_plan['negotiation_id']}")
            return negotiation_plan
            
        except Exception as e:
            logger.error(f"Error initiating negotiation: {e}")
            raise
    
    def _select_negotiation_strategy(self, scenario: RFQScenario, candidates: List[EvaluationResult]) -> Dict[str, Any]:
        """Select optimal negotiation strategy based on scenario and candidates"""
        try:
            # Analyze scenario priorities
            cost_weight = scenario.evaluation_criteria.get('cost', 0.3)
            service_weight = sum(scenario.evaluation_criteria.get(k, 0) for k in ['reliability', 'service_quality', 'delivery_time'])
            
            if cost_weight >= 0.4:
                strategy_type = "COST_FOCUSED"
            elif service_weight >= 0.5:
                strategy_type = "SERVICE_FOCUSED"
            else:
                strategy_type = "BALANCED"
            
            strategy = self.negotiation_strategies[strategy_type].copy()
            strategy["selected_based_on"] = {"cost_weight": cost_weight, "service_weight": service_weight}
            
            return strategy
            
        except Exception as e:
            logger.error(f"Error selecting negotiation strategy: {e}")
            return self.negotiation_strategies["BALANCED"]
    
    def _select_workflow_template(self, scenario: RFQScenario) -> str:
        """Select appropriate workflow template"""
        try:
            # Check scenario complexity and urgency
            if scenario.scenario_type in [ScenarioType.EMERGENCY_SHIPPING]:
                return "EXPEDITED"
            elif scenario.scenario_type in [ScenarioType.SEASONAL_CONTRACT, ScenarioType.VOLUME_COMMITMENT]:
                return "COMPLEX"
            else:
                return "STANDARD"
                
        except Exception as e:
            logger.error(f"Error selecting workflow template: {e}")
            return "STANDARD"
    
    def _calculate_negotiation_priority(self, result: EvaluationResult) -> str:
        """Calculate negotiation priority for each candidate"""
        try:
            if result.overall_score >= 0.8:
                return "HIGH"
            elif result.overall_score >= 0.6:
                return "MEDIUM"
            else:
                return "LOW"
                
        except Exception as e:
            logger.error(f"Error calculating negotiation priority: {e}")
            return "MEDIUM"
    
    def _identify_negotiation_points(self, result: EvaluationResult, scenario: RFQScenario) -> List[Dict[str, Any]]:
        """Identify key negotiation points based on evaluation results"""
        try:
            negotiation_points = []
            
            # Identify areas for improvement based on criteria scores
            for criterion, score in result.criteria_scores.items():
                if score < 0.7:  # Below threshold
                    negotiation_points.append({
                        "criterion": criterion,
                        "current_score": score,
                        "target_improvement": 0.8 - score,
                        "negotiation_type": "improvement_request",
                        "priority": "high" if score < 0.5 else "medium"
                    })
            
            # Cost negotiation opportunities
            if result.cost_analysis.get("total_cost", 0) > scenario.budget_range.get("target", scenario.budget_range.get("max", 0)):
                negotiation_points.append({
                    "criterion": "cost_reduction",
                    "current_cost": result.cost_analysis.get("total_cost"),
                    "target_cost": scenario.budget_range.get("target"),
                    "negotiation_type": "cost_negotiation",
                    "priority": "high"
                })
            
            # Service enhancement opportunities
            if result.overall_score >= 0.7:  # Good candidate worth enhancing
                negotiation_points.append({
                    "criterion": "service_enhancement",
                    "negotiation_type": "value_add_request",
                    "priority": "medium",
                    "opportunities": ["extended_warranty", "additional_services", "performance_guarantees"]
                })
            
            return negotiation_points
            
        except Exception as e:
            logger.error(f"Error identifying negotiation points: {e}")
            return []

class RFQAutomationEngine:
    """Main RFQ automation orchestration engine"""
    
    def __init__(self):
        self.scenario_modeler = RFQScenarioModeler()
        self.response_evaluator = AIResponseEvaluator()
        self.negotiation_manager = NegotiationWorkflowManager()
        self.active_rfqs = {}
        self.evaluation_cache = {}
    
    async def create_rfq_scenario(self, scenario_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create comprehensive RFQ scenario with AI assistance"""
        try:
            # Create main scenario
            scenario = await self.scenario_modeler.create_scenario(scenario_data)
            
            # Generate scenario variations for comparison
            variations = await self.scenario_modeler.generate_scenario_variations(scenario)
            
            # Store in active RFQs
            self.active_rfqs[scenario.scenario_id] = {
                "scenario": scenario,
                "variations": variations,
                "responses": [],
                "evaluations": [],
                "status": RFQStatus.DRAFT,
                "created_at": datetime.now()
            }
            
            return {
                "scenario_id": scenario.scenario_id,
                "scenario": scenario.__dict__,
                "variations": [v.__dict__ for v in variations],
                "status": "created",
                "next_steps": ["review_scenario", "add_target_providers", "publish_rfq"]
            }
            
        except Exception as e:
            logger.error(f"Error creating RFQ scenario: {e}")
            raise
    
    async def submit_rfq_response(self, response_data: Dict[str, Any]) -> Dict[str, Any]:
        """Submit and process RFQ response"""
        try:
            response = RFQResponse(
                response_id=str(uuid.uuid4()),
                rfq_id=response_data['rfq_id'],
                provider_id=response_data['provider_id'],
                provider_name=response_data['provider_name'],
                total_cost=float(response_data['total_cost']),
                delivery_time=int(response_data['delivery_time']),
                proposal_details=response_data.get('proposal_details', {}),
                compliance_score=float(response_data.get('compliance_score', 0.8)),
                response_date=datetime.now(),
                validity_period=int(response_data.get('validity_period', 30)),
                special_terms=response_data.get('special_terms', [])
            )
            
            # Add to active RFQ
            if response.rfq_id in self.active_rfqs:
                self.active_rfqs[response.rfq_id]["responses"].append(response)
                self.active_rfqs[response.rfq_id]["status"] = RFQStatus.RESPONSES_RECEIVED
            
            return {
                "response_id": response.response_id,
                "status": "submitted",
                "acknowledgment": f"Response received from {response.provider_name}",
                "next_evaluation": "automatic_evaluation_initiated"
            }
            
        except Exception as e:
            logger.error(f"Error submitting RFQ response: {e}")
            raise
    
    async def evaluate_rfq_responses(self, rfq_id: str) -> Dict[str, Any]:
        """Comprehensively evaluate all RFQ responses"""
        try:
            if rfq_id not in self.active_rfqs:
                raise ValueError(f"RFQ {rfq_id} not found")
            
            rfq_data = self.active_rfqs[rfq_id]
            scenario = rfq_data["scenario"]
            responses = rfq_data["responses"]
            
            if not responses:
                return {"status": "no_responses", "message": "No responses to evaluate"}
            
            # Evaluate each response
            evaluations = []
            for response in responses:
                evaluation = await self.response_evaluator.evaluate_response(response, scenario)
                evaluations.append(evaluation)
            
            # Rank responses
            evaluations.sort(key=lambda x: x.overall_score, reverse=True)
            for idx, evaluation in enumerate(evaluations):
                evaluation.ranking = idx + 1
            
            # Update RFQ data
            self.active_rfqs[rfq_id]["evaluations"] = evaluations
            self.active_rfqs[rfq_id]["status"] = RFQStatus.EVALUATION_COMPLETE
            
            # Cache evaluations
            self.evaluation_cache[rfq_id] = evaluations
            
            return {
                "rfq_id": rfq_id,
                "status": "evaluation_complete",
                "total_responses": len(responses),
                "evaluations": [
                    {
                        "response_id": eval.response_id,
                        "ranking": eval.ranking,
                        "overall_score": eval.overall_score,
                        "recommendation": eval.recommendation,
                        "criteria_scores": eval.criteria_scores
                    }
                    for eval in evaluations
                ],
                "top_candidate": evaluations[0].__dict__ if evaluations else None,
                "ready_for_negotiation": len(evaluations) > 0
            }
            
        except Exception as e:
            logger.error(f"Error evaluating RFQ responses: {e}")
            raise
    
    async def initiate_negotiation_workflow(self, rfq_id: str) -> Dict[str, Any]:
        """Initiate structured negotiation workflow"""
        try:
            if rfq_id not in self.active_rfqs:
                raise ValueError(f"RFQ {rfq_id} not found")
            
            rfq_data = self.active_rfqs[rfq_id]
            evaluations = rfq_data.get("evaluations", [])
            scenario = rfq_data["scenario"]
            
            if not evaluations:
                raise ValueError("No evaluations available for negotiation")
            
            # Initiate negotiation workflow
            negotiation_plan = await self.negotiation_manager.initiate_negotiation(evaluations, scenario)
            
            # Update RFQ status
            self.active_rfqs[rfq_id]["negotiation_plan"] = negotiation_plan
            self.active_rfqs[rfq_id]["status"] = RFQStatus.NEGOTIATION_IN_PROGRESS
            
            return {
                "rfq_id": rfq_id,
                "negotiation_id": negotiation_plan["negotiation_id"],
                "status": "negotiation_initiated",
                "strategy": negotiation_plan["strategy"]["name"],
                "workflow": negotiation_plan["workflow"]["name"],
                "candidates": negotiation_plan["candidates"],
                "current_stage": negotiation_plan["current_stage"],
                "stage_deadline": negotiation_plan["stage_deadline"].isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error initiating negotiation workflow: {e}")
            raise
    
    async def get_rfq_dashboard(self, user_id: str = None) -> Dict[str, Any]:
        """Get comprehensive RFQ dashboard data"""
        try:
            dashboard_data = {
                "total_rfqs": len(self.active_rfqs),
                "rfq_by_status": {},
                "recent_activity": [],
                "performance_metrics": {},
                "active_negotiations": 0
            }
            
            # Count RFQs by status
            for rfq_data in self.active_rfqs.values():
                status = rfq_data["status"].value
                dashboard_data["rfq_by_status"][status] = dashboard_data["rfq_by_status"].get(status, 0) + 1
            
            # Count active negotiations
            dashboard_data["active_negotiations"] = sum(
                1 for rfq_data in self.active_rfqs.values() 
                if rfq_data["status"] == RFQStatus.NEGOTIATION_IN_PROGRESS
            )
            
            # Recent activity (last 10 RFQs)
            recent_rfqs = sorted(
                self.active_rfqs.items(),
                key=lambda x: x[1]["created_at"],
                reverse=True
            )[:10]
            
            dashboard_data["recent_activity"] = [
                {
                    "rfq_id": rfq_id,
                    "scenario_name": rfq_data["scenario"].name,
                    "status": rfq_data["status"].value,
                    "created_at": rfq_data["created_at"].isoformat(),
                    "responses_count": len(rfq_data["responses"])
                }
                for rfq_id, rfq_data in recent_rfqs
            ]
            
            # Performance metrics
            total_responses = sum(len(rfq_data["responses"]) for rfq_data in self.active_rfqs.values())
            completed_rfqs = sum(1 for rfq_data in self.active_rfqs.values() if rfq_data["status"] in [RFQStatus.AWARDED, RFQStatus.EVALUATION_COMPLETE])
            
            dashboard_data["performance_metrics"] = {
                "average_responses_per_rfq": total_responses / max(1, len(self.active_rfqs)),
                "completion_rate": completed_rfqs / max(1, len(self.active_rfqs)),
                "total_responses_received": total_responses
            }
            
            return dashboard_data
            
        except Exception as e:
            logger.error(f"Error generating RFQ dashboard: {e}")
            return {"error": "Unable to generate dashboard"}

# Global RFQ automation instance
rfq_automation_engine = RFQAutomationEngine()

# Utility functions for external integration
async def create_rfq_scenario(scenario_data: Dict[str, Any]) -> Dict[str, Any]:
    """Create new RFQ scenario"""
    return await rfq_automation_engine.create_rfq_scenario(scenario_data)

async def submit_provider_response(response_data: Dict[str, Any]) -> Dict[str, Any]:
    """Submit provider response to RFQ"""
    return await rfq_automation_engine.submit_rfq_response(response_data)

async def evaluate_responses(rfq_id: str) -> Dict[str, Any]:
    """Evaluate all responses for an RFQ"""
    return await rfq_automation_engine.evaluate_rfq_responses(rfq_id)

async def start_negotiation(rfq_id: str) -> Dict[str, Any]:
    """Start negotiation workflow for RFQ"""
    return await rfq_automation_engine.initiate_negotiation_workflow(rfq_id)

async def get_dashboard_data(user_id: str = None) -> Dict[str, Any]:
    """Get RFQ management dashboard data"""
    return await rfq_automation_engine.get_rfq_dashboard(user_id)