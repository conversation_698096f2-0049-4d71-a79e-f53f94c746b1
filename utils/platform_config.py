"""
Platform Configuration for Optional Features
Manages customs and insurance requirement settings
"""

class PlatformConfig:
    """Configuration settings for platform features"""
    
    # Feature enforcement flags
    ENFORCE_CUSTOMS_DECLARATION = False
    ENFORCE_INSURANCE_REQUIREMENT = False
    
    # UI messaging
    CUSTOMS_OPTIONAL_MESSAGE = "Customs declaration is optional. You may download and submit it manually if needed."
    INSURANCE_OPTIONAL_MESSAGE = "You may skip this step if you already have your own insurance."
    
    # Feature availability (keep functionality but make optional)
    CUSTOMS_AVAILABLE = True
    INSURANCE_AVAILABLE = True
    
    @classmethod
    def is_customs_required(cls) -> bool:
        """Check if customs declaration is required"""
        return cls.ENFORCE_CUSTOMS_DECLARATION
    
    @classmethod
    def is_insurance_required(cls) -> bool:
        """Check if insurance is required"""
        return cls.ENFORCE_INSURANCE_REQUIREMENT
    
    @classmethod
    def get_customs_message(cls) -> str:
        """Get customs optional message"""
        return cls.CUSTOMS_OPTIONAL_MESSAGE
    
    @classmethod
    def get_insurance_message(cls) -> str:
        """Get insurance optional message"""
        return cls.INSURANCE_OPTIONAL_MESSAGE
    
    @classmethod
    def enable_customs_requirement(cls):
        """Enable customs requirement (for future use)"""
        cls.ENFORCE_CUSTOMS_DECLARATION = True
    
    @classmethod
    def disable_customs_requirement(cls):
        """Disable customs requirement"""
        cls.ENFORCE_CUSTOMS_DECLARATION = False
    
    @classmethod
    def enable_insurance_requirement(cls):
        """Enable insurance requirement (for future use)"""
        cls.ENFORCE_INSURANCE_REQUIREMENT = True
    
    @classmethod
    def disable_insurance_requirement(cls):
        """Disable insurance requirement"""
        cls.ENFORCE_INSURANCE_REQUIREMENT = False