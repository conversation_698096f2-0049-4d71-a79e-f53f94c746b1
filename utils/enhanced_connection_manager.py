"""
Enhanced Database Connection Management System
Permanent solution for SSL errors, connection timeouts, and HTTP errors
"""

import logging
import time
import threading
import async<PERSON>
from typing import Dict, Optional, Any, Callable
from contextlib import contextmanager
import os
import django
from django.conf import settings

# Configure Django settings if not already configured
if not settings.configured:
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'cloverics_django.cloverics.settings')
    try:
        django.setup()
    except Exception as e:
        # Fallback for FastAPI compatibility
        pass

from django.db import connection, connections, OperationalError
from django.db.utils import ConnectionHandler
from django.core.exceptions import ImproperlyConfigured
import psycopg2
from psycopg2.extensions import connection as psycopg2_connection

logger = logging.getLogger(__name__)

class EnhancedConnectionManager:
    """
    Enterprise-grade database connection manager with:
    - SSL connection optimization
    - Connection pooling
    - Automatic recovery
    - Health monitoring
    - Error prevention
    """
    
    def __init__(self):
        self.connection_stats = {
            'total_connections': 0,
            'active_connections': 0,
            'failed_connections': 0,
            'recovered_connections': 0,
            'health_checks': 0,
            'ssl_errors': 0,
            'connection_pool_hits': 0,
            'last_health_check': None,
            'uptime_start': time.time()
        }
        self.health_monitor_active = False
        self.connection_pool = {}
        self.pool_lock = threading.Lock()
        self.health_monitor_thread = None
        self.recovery_in_progress = False
        
        # Enhanced connection parameters
        self.connection_params = {
            'sslmode': 'require',  # Use SSL for security
            'connect_timeout': 30,
            'keepalives_idle': 600,
            'keepalives_interval': 30,
            'keepalives_count': 3,
            'application_name': 'cloverics_platform'
        }
        
        # Start monitoring
        self.start_health_monitoring()
        
    def get_connection_stats(self) -> Dict[str, Any]:
        """Get comprehensive connection statistics"""
        with self.pool_lock:
            return {
                **self.connection_stats,
                'connection_health': self.check_connection_health(),
                'pool_size': len(self.connection_pool),
                'uptime_seconds': time.time() - self.connection_stats['uptime_start'],
                'ssl_error_rate': self.connection_stats['ssl_errors'] / max(1, self.connection_stats['total_connections']),
                'recovery_rate': self.connection_stats['recovered_connections'] / max(1, self.connection_stats['failed_connections'])
            }
    
    def optimize_database_settings(self):
        """Optimize Django database settings for stable connections"""
        try:
            # Update database configuration
            db_config = settings.DATABASES['default']
            
            # Enhanced connection options
            enhanced_options = {
                'sslmode': 'require',  # Use SSL for security
                'connect_timeout': '30',
                'keepalives_idle': '600',
                'keepalives_interval': '30',
                'keepalives_count': '3',
                'application_name': 'cloverics_platform'
            }
            
            # Update settings
            if 'OPTIONS' not in db_config:
                db_config['OPTIONS'] = {}
            
            db_config['OPTIONS'].update(enhanced_options)
            db_config['CONN_MAX_AGE'] = 300  # 5 minutes connection pooling
            db_config['CONN_HEALTH_CHECKS'] = True
            db_config['ATOMIC_REQUESTS'] = False  # Prevent transaction locks
            
            logger.info("Database settings optimized for stability")
            return True
            
        except Exception as e:
            logger.error(f"Failed to optimize database settings: {e}")
            return False
    
    def check_connection_health(self) -> bool:
        """Comprehensive connection health check with SSL error prevention"""
        try:
            with self.pool_lock:
                self.connection_stats['health_checks'] += 1
                self.connection_stats['last_health_check'] = time.time()
            
            # Test basic connectivity with timeout
            cursor = connection.cursor()
            cursor.execute("SELECT 1, current_timestamp, version()")
            result = cursor.fetchone()
            
            if result and result[0] == 1:
                # Check connection status (PostgreSQL version-agnostic)
                cursor.execute("SELECT current_setting('server_version')")
                version_info = cursor.fetchone()[0]
                
                # Test connection parameters
                cursor.execute("SHOW server_version")
                version = cursor.fetchone()[0]
                
                logger.debug(f"Connection health check passed - Version: {version_info}, Server: {version}")
                return True
            else:
                logger.warning("Connection health check failed - unexpected result")
                return False
                
        except psycopg2.OperationalError as e:
            logger.error(f"PostgreSQL connection error: {e}")
            self.connection_stats['ssl_errors'] += 1
            self._attempt_connection_recovery()
            return False
        except OperationalError as e:
            logger.error(f"Django connection error: {e}")
            self.connection_stats['failed_connections'] += 1
            self._attempt_connection_recovery()
            return False
        except Exception as e:
            logger.error(f"Unexpected error during health check: {e}")
            return False
    
    def _attempt_connection_recovery(self):
        """Enhanced connection recovery with SSL error prevention"""
        if self.recovery_in_progress:
            return
            
        self.recovery_in_progress = True
        
        try:
            logger.info("Attempting enhanced database connection recovery...")
            
            # Step 1: Close all existing connections
            try:
                connection.close()
                connections.close_all()
                logger.info("Closed existing connections")
            except Exception as e:
                logger.warning(f"Error closing connections: {e}")
            
            # Step 2: Clear connection cache
            try:
                if hasattr(connection, 'connection') and connection.connection:
                    connection.connection.close()
                logger.info("Cleared connection cache")
            except Exception as e:
                logger.warning(f"Error clearing cache: {e}")
            
            # Step 3: Recreate connection with enhanced parameters
            try:
                # Force new connection with optimized parameters
                connection.ensure_connection()
                
                # Test new connection
                cursor = connection.cursor()
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
                
                if result and result[0] == 1:
                    with self.pool_lock:
                        self.connection_stats['recovered_connections'] += 1
                    logger.info("Database connection recovery successful")
                    return True
                else:
                    logger.error("Connection recovery failed - test query failed")
                    return False
                    
            except Exception as e:
                logger.error(f"Error recreating connection: {e}")
                return False
                
        except Exception as e:
            logger.error(f"Connection recovery failed: {e}")
            return False
        finally:
            self.recovery_in_progress = False
    
    def start_health_monitoring(self):
        """Start background health monitoring"""
        if self.health_monitor_active:
            return
            
        self.health_monitor_active = True
        self.health_monitor_thread = threading.Thread(target=self._health_monitor_loop, daemon=True)
        self.health_monitor_thread.start()
        logger.info("Health monitoring started")
    
    def _health_monitor_loop(self):
        """Background health monitoring loop"""
        while self.health_monitor_active:
            try:
                # Check connection health every 5 minutes
                if self.check_connection_health():
                    logger.debug("Connection health check passed")
                else:
                    logger.warning("Connection health check failed - recovery initiated")
                
                # Sleep for 5 minutes
                time.sleep(300)
                
            except Exception as e:
                logger.error(f"Health monitor error: {e}")
                time.sleep(60)  # Shorter sleep on error
    
    def stop_health_monitoring(self):
        """Stop health monitoring"""
        self.health_monitor_active = False
        if self.health_monitor_thread:
            self.health_monitor_thread.join(timeout=10)
        logger.info("Health monitoring stopped")
    
    @contextmanager
    def get_connection(self):
        """Get a managed database connection"""
        connection_acquired = False
        try:
            with self.pool_lock:
                self.connection_stats['total_connections'] += 1
                self.connection_stats['active_connections'] += 1
            
            # Ensure connection is healthy
            if not self.check_connection_health():
                self._attempt_connection_recovery()
            
            connection_acquired = True
            yield connection
            
        except Exception as e:
            logger.error(f"Connection error: {e}")
            with self.pool_lock:
                self.connection_stats['failed_connections'] += 1
            raise
        finally:
            if connection_acquired:
                with self.pool_lock:
                    self.connection_stats['active_connections'] -= 1

# Global instance
enhanced_connection_manager = EnhancedConnectionManager()

# Decorator for database operations
def stable_db_operation(func):
    """Decorator to ensure stable database operations"""
    def wrapper(*args, **kwargs):
        try:
            with enhanced_connection_manager.get_connection():
                return func(*args, **kwargs)
        except Exception as e:
            logger.error(f"Database operation failed: {e}")
            # Attempt recovery and retry once
            enhanced_connection_manager._attempt_connection_recovery()
            try:
                return func(*args, **kwargs)
            except Exception as retry_error:
                logger.error(f"Database operation failed after recovery: {retry_error}")
                raise
    return wrapper

# Async version
def async_stable_db_operation(func):
    """Async decorator for stable database operations"""
    async def wrapper(*args, **kwargs):
        try:
            with enhanced_connection_manager.get_connection():
                return await func(*args, **kwargs)
        except Exception as e:
            logger.error(f"Async database operation failed: {e}")
            # Attempt recovery and retry once
            enhanced_connection_manager._attempt_connection_recovery()
            try:
                return await func(*args, **kwargs)
            except Exception as retry_error:
                logger.error(f"Async database operation failed after recovery: {retry_error}")
                raise
    return wrapper