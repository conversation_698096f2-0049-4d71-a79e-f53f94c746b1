"""
Contract Lifecycle Management System
Comprehensive contract automation with approval workflows and compliance tracking
"""

import json
import uuid
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Union
from dataclasses import dataclass
from enum import Enum
import os
from django.utils import timezone
from django.db.models import Q
from django.contrib.auth.models import User
from asgiref.sync import sync_to_async
import asyncio


class ContractStatus(Enum):
    DRAFT = "draft"
    PENDING_REVIEW = "pending_review"
    UNDER_NEGOTIATION = "under_negotiation"
    APPROVED = "approved"
    SIGNED = "signed"
    ACTIVE = "active"
    COMPLETED = "completed"
    TERMINATED = "terminated"
    EXPIRED = "expired"


class ContractType(Enum):
    LOGISTICS_SERVICE = "logistics_service"
    MASTER_SERVICE = "master_service"
    FREIGHT_FORWARDING = "freight_forwarding"
    WAREHOUSING = "warehousing"
    INSURANCE_COVERAGE = "insurance_coverage"
    CUSTOMS_BROKERAGE = "customs_brokerage"
    TRANSPORTATION = "transportation"


class ApprovalStatus(Enum):
    PENDING = "pending"
    APPROVED = "approved"
    REJECTED = "rejected"
    CONDITIONAL = "conditional"


@dataclass
class ContractClause:
    """Contract clause data structure"""
    clause_id: str
    section: str
    title: str
    content: str
    is_mandatory: bool = True
    is_negotiable: bool = False
    compliance_tags: List[str] = None
    last_modified: datetime = None
    
    def __post_init__(self):
        if self.compliance_tags is None:
            self.compliance_tags = []
        if self.last_modified is None:
            self.last_modified = datetime.now()


@dataclass
class ContractVersion:
    """Contract version tracking"""
    version_id: str
    version_number: str
    created_date: datetime
    created_by: str
    changes_summary: str
    approval_status: ApprovalStatus
    clauses: List[ContractClause]
    metadata: Dict = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


@dataclass
class ApprovalWorkflow:
    """Contract approval workflow"""
    workflow_id: str
    contract_id: str
    approvers: List[Dict]  # [{'user_id': int, 'role': str, 'status': str, 'notes': str}]
    current_step: int
    approval_chain: List[str]  # ['legal', 'finance', 'operations', 'executive']
    started_date: datetime
    completed_date: Optional[datetime] = None
    is_parallel: bool = False
    
    def __post_init__(self):
        if self.completed_date is None and self.current_step >= len(self.approval_chain):
            self.completed_date = datetime.now()


@dataclass
class ComplianceCheck:
    """Compliance validation result"""
    check_id: str
    regulation: str
    status: str  # 'compliant', 'non_compliant', 'warning', 'pending'
    description: str
    severity: str  # 'critical', 'high', 'medium', 'low'
    recommendations: List[str]
    last_checked: datetime
    next_review: datetime


class ContractTemplateEngine:
    """
    Contract template management and generation
    """
    
    def __init__(self):
        self.templates = {
            'logistics_service': {
                'name': 'Standard Logistics Service Agreement',
                'sections': [
                    'Service Description',
                    'Rates and Pricing',
                    'Performance Standards',
                    'Liability and Insurance',
                    'Payment Terms',
                    'Force Majeure',
                    'Termination Conditions'
                ],
                'mandatory_clauses': [
                    'service_scope',
                    'pricing_structure',
                    'performance_kpis',
                    'liability_limits',
                    'payment_terms',
                    'termination_clause'
                ]
            },
            'master_service': {
                'name': 'Master Service Agreement',
                'sections': [
                    'General Terms',
                    'Service Framework',
                    'Governance Structure',
                    'Data Protection',
                    'Intellectual Property',
                    'Compliance Requirements',
                    'Dispute Resolution'
                ],
                'mandatory_clauses': [
                    'general_terms',
                    'service_framework',
                    'data_protection',
                    'ip_rights',
                    'compliance_requirements'
                ]
            }
        }
        
        self.clause_library = self._initialize_clause_library()
    
    def _initialize_clause_library(self) -> Dict[str, ContractClause]:
        """Initialize standard contract clauses"""
        return {
            'service_scope': ContractClause(
                clause_id='service_scope',
                section='Service Description',
                title='Scope of Services',
                content='The Provider agrees to provide logistics services as detailed in Schedule A, including but not limited to transportation, warehousing, and freight forwarding services.',
                is_mandatory=True,
                is_negotiable=False,
                compliance_tags=['commercial_law', 'service_standards']
            ),
            'pricing_structure': ContractClause(
                clause_id='pricing_structure',
                section='Rates and Pricing',
                title='Pricing and Payment Structure',
                content='Service rates shall be as specified in Schedule B. All prices are subject to fuel surcharges and regulatory fees as applicable.',
                is_mandatory=True,
                is_negotiable=True,
                compliance_tags=['commercial_law', 'pricing_regulations']
            ),
            'liability_limits': ContractClause(
                clause_id='liability_limits',
                section='Liability and Insurance',
                title='Limitation of Liability',
                content='Provider\'s liability shall be limited to the lesser of the actual loss or $100,000 per incident, except in cases of gross negligence or willful misconduct.',
                is_mandatory=True,
                is_negotiable=True,
                compliance_tags=['liability_law', 'insurance_requirements']
            ),
            'data_protection': ContractClause(
                clause_id='data_protection',
                section='Data Protection',
                title='Data Privacy and Protection',
                content='Both parties agree to comply with applicable data protection laws including GDPR, CCPA, and other relevant privacy regulations.',
                is_mandatory=True,
                is_negotiable=False,
                compliance_tags=['gdpr', 'ccpa', 'data_privacy']
            )
        }
    
    def generate_contract(self, contract_type: str, parameters: Dict) -> Dict:
        """Generate a contract from template"""
        try:
            if contract_type not in self.templates:
                return {'success': False, 'error': 'Invalid contract type'}
            
            template = self.templates[contract_type]
            contract_id = f"CONT-{datetime.now().strftime('%Y%m%d')}-{str(uuid.uuid4())[:8].upper()}"
            
            # Generate clauses based on template
            clauses = []
            for clause_id in template['mandatory_clauses']:
                if clause_id in self.clause_library:
                    clause = self.clause_library[clause_id]
                    # Customize clause content based on parameters
                    customized_clause = self._customize_clause(clause, parameters)
                    clauses.append(customized_clause)
            
            contract_data = {
                'contract_id': contract_id,
                'contract_type': contract_type,
                'template_name': template['name'],
                'status': ContractStatus.DRAFT.value,
                'created_date': datetime.now().isoformat(),
                'parties': {
                    'customer': parameters.get('customer_info', {}),
                    'provider': parameters.get('provider_info', {})
                },
                'service_details': parameters.get('service_details', {}),
                'clauses': [self._clause_to_dict(clause) for clause in clauses],
                'version': '1.0',
                'metadata': {
                    'created_by': parameters.get('created_by'),
                    'template_version': '2024.1',
                    'auto_generated': True
                }
            }
            
            return {
                'success': True,
                'contract': contract_data,
                'next_steps': [
                    'Review generated clauses',
                    'Add custom terms if needed',
                    'Submit for legal review',
                    'Initiate approval workflow'
                ]
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _customize_clause(self, clause: ContractClause, parameters: Dict) -> ContractClause:
        """Customize clause content based on parameters"""
        customized_content = clause.content
        
        # Replace placeholders with actual values
        replacements = {
            '{{customer_name}}': parameters.get('customer_info', {}).get('company_name', '[Customer Name]'),
            '{{provider_name}}': parameters.get('provider_info', {}).get('company_name', '[Provider Name]'),
            '{{service_type}}': parameters.get('service_details', {}).get('service_type', '[Service Type]'),
            '{{liability_amount}}': parameters.get('liability_limit', '$100,000')
        }
        
        for placeholder, value in replacements.items():
            customized_content = customized_content.replace(placeholder, value)
        
        return ContractClause(
            clause_id=clause.clause_id,
            section=clause.section,
            title=clause.title,
            content=customized_content,
            is_mandatory=clause.is_mandatory,
            is_negotiable=clause.is_negotiable,
            compliance_tags=clause.compliance_tags.copy(),
            last_modified=datetime.now()
        )
    
    def _clause_to_dict(self, clause: ContractClause) -> Dict:
        """Convert ContractClause to dictionary"""
        return {
            'clause_id': clause.clause_id,
            'section': clause.section,
            'title': clause.title,
            'content': clause.content,
            'is_mandatory': clause.is_mandatory,
            'is_negotiable': clause.is_negotiable,
            'compliance_tags': clause.compliance_tags,
            'last_modified': clause.last_modified.isoformat()
        }


class ContractApprovalEngine:
    """
    Contract approval workflow management
    """
    
    def __init__(self):
        self.approval_workflows = {}
        self.approval_templates = {
            'standard': ['legal', 'operations', 'finance'],
            'high_value': ['legal', 'operations', 'finance', 'executive'],
            'master_agreement': ['legal', 'compliance', 'operations', 'finance', 'executive'],
            'expedited': ['operations', 'legal']
        }
    
    def initiate_approval_workflow(self, contract_id: str, workflow_type: str, initiator_id: int) -> Dict:
        """Start approval workflow for a contract"""
        try:
            if workflow_type not in self.approval_templates:
                workflow_type = 'standard'
            
            workflow_id = f"WF-{datetime.now().strftime('%Y%m%d')}-{str(uuid.uuid4())[:8].upper()}"
            approval_chain = self.approval_templates[workflow_type]
            
            # Initialize approvers
            approvers = []
            for role in approval_chain:
                approvers.append({
                    'role': role,
                    'status': ApprovalStatus.PENDING.value,
                    'assigned_date': datetime.now().isoformat(),
                    'notes': '',
                    'user_id': None  # Will be assigned based on role
                })
            
            workflow = ApprovalWorkflow(
                workflow_id=workflow_id,
                contract_id=contract_id,
                approvers=approvers,
                current_step=0,
                approval_chain=approval_chain,
                started_date=datetime.now(),
                is_parallel=workflow_type == 'expedited'
            )
            
            self.approval_workflows[workflow_id] = workflow
            
            # Send notifications to first approver(s)
            self._notify_next_approvers(workflow)
            
            return {
                'success': True,
                'workflow_id': workflow_id,
                'current_step': workflow.current_step + 1,
                'total_steps': len(approval_chain),
                'next_approvers': [approval_chain[0]] if not workflow.is_parallel else approval_chain,
                'estimated_completion': self._estimate_completion_date(workflow_type)
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def process_approval_decision(self, workflow_id: str, approver_role: str, decision: str, notes: str = '') -> Dict:
        """Process an approval decision"""
        try:
            if workflow_id not in self.approval_workflows:
                return {'success': False, 'error': 'Workflow not found'}
            
            workflow = self.approval_workflows[workflow_id]
            
            # Update approver decision
            for approver in workflow.approvers:
                if approver['role'] == approver_role:
                    approver['status'] = decision
                    approver['decision_date'] = datetime.now().isoformat()
                    approver['notes'] = notes
                    break
            
            # Check if current step is complete
            current_approvers = [a for a in workflow.approvers if a['role'] in workflow.approval_chain[workflow.current_step:workflow.current_step+1]]
            
            if workflow.is_parallel:
                # All approvers must decide
                pending_decisions = [a for a in workflow.approvers if a['status'] == ApprovalStatus.PENDING.value]
                if not pending_decisions:
                    workflow.current_step = len(workflow.approval_chain)
            else:
                # Sequential approval
                if decision == ApprovalStatus.APPROVED.value:
                    workflow.current_step += 1
                elif decision == ApprovalStatus.REJECTED.value:
                    # Workflow stops on rejection
                    workflow.current_step = len(workflow.approval_chain)
            
            # Check if workflow is complete
            if workflow.current_step >= len(workflow.approval_chain):
                workflow.completed_date = datetime.now()
                final_status = self._determine_final_status(workflow)
                
                return {
                    'success': True,
                    'workflow_status': 'completed',
                    'final_decision': final_status,
                    'completed_date': workflow.completed_date.isoformat()
                }
            else:
                # Notify next approvers
                self._notify_next_approvers(workflow)
                
                return {
                    'success': True,
                    'workflow_status': 'in_progress',
                    'current_step': workflow.current_step + 1,
                    'next_approvers': workflow.approval_chain[workflow.current_step:workflow.current_step+1]
                }
                
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def get_workflow_status(self, workflow_id: str) -> Dict:
        """Get current workflow status"""
        if workflow_id not in self.approval_workflows:
            return {'success': False, 'error': 'Workflow not found'}
        
        workflow = self.approval_workflows[workflow_id]
        
        return {
            'success': True,
            'workflow_id': workflow_id,
            'contract_id': workflow.contract_id,
            'status': 'completed' if workflow.completed_date else 'in_progress',
            'current_step': workflow.current_step + 1,
            'total_steps': len(workflow.approval_chain),
            'approvers': workflow.approvers,
            'started_date': workflow.started_date.isoformat(),
            'completed_date': workflow.completed_date.isoformat() if workflow.completed_date else None,
            'estimated_completion': self._estimate_completion_date('standard') if not workflow.completed_date else None
        }
    
    def _notify_next_approvers(self, workflow: ApprovalWorkflow):
        """Send notifications to next approvers"""
        # This would integrate with the notification system
        pass
    
    def _determine_final_status(self, workflow: ApprovalWorkflow) -> str:
        """Determine final approval status"""
        rejected_decisions = [a for a in workflow.approvers if a['status'] == ApprovalStatus.REJECTED.value]
        if rejected_decisions:
            return ApprovalStatus.REJECTED.value
        
        approved_decisions = [a for a in workflow.approvers if a['status'] == ApprovalStatus.APPROVED.value]
        if len(approved_decisions) == len(workflow.approvers):
            return ApprovalStatus.APPROVED.value
        
        return ApprovalStatus.CONDITIONAL.value
    
    def _estimate_completion_date(self, workflow_type: str) -> str:
        """Estimate workflow completion date"""
        business_days = {
            'expedited': 1,
            'standard': 3,
            'high_value': 5,
            'master_agreement': 7
        }
        
        days = business_days.get(workflow_type, 3)
        completion_date = datetime.now() + timedelta(days=days)
        return completion_date.isoformat()


class ComplianceMonitor:
    """
    Contract compliance monitoring and validation
    """
    
    def __init__(self):
        self.compliance_rules = {
            'gdpr': {
                'name': 'General Data Protection Regulation',
                'required_clauses': ['data_protection', 'data_retention', 'consent_management'],
                'severity': 'critical',
                'regulations': ['EU GDPR 2018', 'UK GDPR']
            },
            'commercial_law': {
                'name': 'Commercial Contract Law',
                'required_clauses': ['service_scope', 'pricing_structure', 'termination_clause'],
                'severity': 'high',
                'regulations': ['UCC', 'Commercial Code']
            },
            'transportation': {
                'name': 'Transportation Regulations',
                'required_clauses': ['carrier_liability', 'insurance_coverage', 'safety_standards'],
                'severity': 'high',
                'regulations': ['DOT Regulations', 'FMCSA Rules']
            },
            'international_trade': {
                'name': 'International Trade Compliance',
                'required_clauses': ['customs_compliance', 'export_controls', 'sanctions_compliance'],
                'severity': 'critical',
                'regulations': ['OFAC', 'EAR', 'ITAR']
            }
        }
    
    def validate_contract_compliance(self, contract_data: Dict) -> Dict:
        """Validate contract against compliance requirements"""
        try:
            contract_clauses = [clause['clause_id'] for clause in contract_data.get('clauses', [])]
            compliance_results = []
            
            for rule_id, rule in self.compliance_rules.items():
                missing_clauses = []
                for required_clause in rule['required_clauses']:
                    if required_clause not in contract_clauses:
                        missing_clauses.append(required_clause)
                
                if missing_clauses:
                    compliance_results.append(ComplianceCheck(
                        check_id=f"check_{rule_id}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                        regulation=rule['name'],
                        status='non_compliant',
                        description=f"Missing required clauses: {', '.join(missing_clauses)}",
                        severity=rule['severity'],
                        recommendations=[
                            f"Add {clause} clause to ensure compliance with {rule['name']}"
                            for clause in missing_clauses
                        ],
                        last_checked=datetime.now(),
                        next_review=datetime.now() + timedelta(days=30)
                    ))
                else:
                    compliance_results.append(ComplianceCheck(
                        check_id=f"check_{rule_id}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                        regulation=rule['name'],
                        status='compliant',
                        description=f"All required clauses present for {rule['name']}",
                        severity='low',
                        recommendations=[],
                        last_checked=datetime.now(),
                        next_review=datetime.now() + timedelta(days=90)
                    ))
            
            # Calculate overall compliance score
            total_checks = len(compliance_results)
            compliant_checks = len([r for r in compliance_results if r.status == 'compliant'])
            compliance_score = (compliant_checks / total_checks) * 100 if total_checks > 0 else 0
            
            critical_issues = [r for r in compliance_results if r.severity == 'critical' and r.status != 'compliant']
            
            return {
                'success': True,
                'compliance_score': round(compliance_score, 1),
                'overall_status': 'compliant' if compliance_score == 100 else 'non_compliant',
                'critical_issues': len(critical_issues),
                'total_checks': total_checks,
                'compliance_results': [self._compliance_check_to_dict(check) for check in compliance_results],
                'recommendations': self._generate_compliance_recommendations(compliance_results)
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _compliance_check_to_dict(self, check: ComplianceCheck) -> Dict:
        """Convert ComplianceCheck to dictionary"""
        return {
            'check_id': check.check_id,
            'regulation': check.regulation,
            'status': check.status,
            'description': check.description,
            'severity': check.severity,
            'recommendations': check.recommendations,
            'last_checked': check.last_checked.isoformat(),
            'next_review': check.next_review.isoformat()
        }
    
    def _generate_compliance_recommendations(self, compliance_results: List[ComplianceCheck]) -> List[str]:
        """Generate overall compliance recommendations"""
        recommendations = []
        
        critical_issues = [r for r in compliance_results if r.severity == 'critical' and r.status != 'compliant']
        if critical_issues:
            recommendations.append("Address critical compliance issues immediately before contract execution")
        
        high_issues = [r for r in compliance_results if r.severity == 'high' and r.status != 'compliant']
        if high_issues:
            recommendations.append("Resolve high-priority compliance gaps during review process")
        
        if not critical_issues and not high_issues:
            recommendations.append("Contract meets all major compliance requirements")
            recommendations.append("Schedule periodic compliance reviews as regulations evolve")
        
        return recommendations


class ContractLifecycleManager:
    """
    Main contract lifecycle management orchestrator
    """
    
    def __init__(self):
        self.template_engine = ContractTemplateEngine()
        self.approval_engine = ContractApprovalEngine()
        self.compliance_monitor = ComplianceMonitor()
        self.contract_database = {}  # In production, this would be a proper database
        
    def create_contract(self, user_id: int, contract_type: str, parameters: Dict) -> Dict:
        """Create a new contract"""
        try:
            # Generate contract from template
            generation_result = self.template_engine.generate_contract(contract_type, parameters)
            
            if not generation_result['success']:
                return generation_result
            
            contract_data = generation_result['contract']
            contract_id = contract_data['contract_id']
            
            # Validate compliance
            compliance_result = self.compliance_monitor.validate_contract_compliance(contract_data)
            contract_data['compliance_check'] = compliance_result
            
            # Store contract
            self.contract_database[contract_id] = contract_data
            
            # Send notification
            self._send_contract_notification(user_id, 'contract_created', {
                'contract_id': contract_id,
                'contract_type': contract_type,
                'status': contract_data['status'],
                'compliance_score': compliance_result.get('compliance_score', 0)
            })
            
            return {
                'success': True,
                'contract_id': contract_id,
                'contract_data': contract_data,
                'compliance_summary': {
                    'score': compliance_result.get('compliance_score', 0),
                    'critical_issues': compliance_result.get('critical_issues', 0),
                    'overall_status': compliance_result.get('overall_status', 'unknown')
                },
                'next_steps': generation_result.get('next_steps', [])
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def submit_for_approval(self, user_id: int, contract_id: str, workflow_type: str = 'standard') -> Dict:
        """Submit contract for approval"""
        try:
            if contract_id not in self.contract_database:
                return {'success': False, 'error': 'Contract not found'}
            
            contract = self.contract_database[contract_id]
            
            # Update contract status
            contract['status'] = ContractStatus.PENDING_REVIEW.value
            contract['submitted_date'] = datetime.now().isoformat()
            contract['submitted_by'] = user_id
            
            # Initiate approval workflow
            approval_result = self.approval_engine.initiate_approval_workflow(
                contract_id, workflow_type, user_id
            )
            
            if approval_result['success']:
                contract['approval_workflow_id'] = approval_result['workflow_id']
                
                # Send notifications
                self._send_contract_notification(user_id, 'contract_submitted', {
                    'contract_id': contract_id,
                    'workflow_id': approval_result['workflow_id'],
                    'estimated_completion': approval_result['estimated_completion']
                })
            
            return approval_result
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def get_user_contracts(self, user_id: int, status_filter: str = None) -> Dict:
        """Get contracts for a user"""
        try:
            user_contracts = []
            
            for contract_id, contract in self.contract_database.items():
                # Check if user is involved in contract
                if (contract.get('metadata', {}).get('created_by') == user_id or
                    contract.get('parties', {}).get('customer', {}).get('user_id') == user_id or
                    contract.get('parties', {}).get('provider', {}).get('user_id') == user_id):
                    
                    if status_filter and contract.get('status') != status_filter:
                        continue
                    
                    contract_summary = {
                        'contract_id': contract_id,
                        'contract_type': contract['contract_type'],
                        'status': contract['status'],
                        'created_date': contract['created_date'],
                        'parties': contract['parties'],
                        'compliance_score': contract.get('compliance_check', {}).get('compliance_score', 0),
                        'workflow_status': self._get_workflow_status_summary(contract.get('approval_workflow_id'))
                    }
                    
                    user_contracts.append(contract_summary)
            
            return {
                'success': True,
                'contracts': user_contracts,
                'total_count': len(user_contracts),
                'status_counts': self._calculate_status_counts(user_contracts)
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def get_contract_details(self, user_id: int, contract_id: str) -> Dict:
        """Get detailed contract information"""
        try:
            if contract_id not in self.contract_database:
                return {'success': False, 'error': 'Contract not found'}
            
            contract = self.contract_database[contract_id]
            
            # Add workflow details if available
            workflow_details = None
            if contract.get('approval_workflow_id'):
                workflow_result = self.approval_engine.get_workflow_status(contract['approval_workflow_id'])
                if workflow_result['success']:
                    workflow_details = workflow_result
            
            return {
                'success': True,
                'contract': contract,
                'workflow_details': workflow_details,
                'compliance_details': contract.get('compliance_check', {}),
                'available_actions': self._get_available_actions(contract, user_id)
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _send_contract_notification(self, user_id: int, notification_type: str, data: Dict):
        """Send contract-related notification"""
        try:
            from core.models import Notification
            
            notification_messages = {
                'contract_created': f"Contract {data['contract_id']} created successfully",
                'contract_submitted': f"Contract {data['contract_id']} submitted for approval",
                'contract_approved': f"Contract {data['contract_id']} has been approved",
                'contract_rejected': f"Contract {data['contract_id']} has been rejected",
                'approval_required': f"Your approval is required for contract {data['contract_id']}"
            }
            
            message = notification_messages.get(notification_type, f"Contract update: {data}")
            
            notification = Notification(
                user_id=user_id,
                type='contract_lifecycle',
                title='Contract Lifecycle Update',
                message=json.dumps({
                    'notification_type': notification_type,
                    'message': message,
                    'data': data,
                    'timestamp': datetime.now().isoformat()
                }),
                created_at=timezone.now()
            )
            notification.save()
            
        except Exception as e:
            print(f"Error sending notification: {e}")
    
    def _get_workflow_status_summary(self, workflow_id: str) -> Optional[Dict]:
        """Get workflow status summary"""
        if not workflow_id:
            return None
        
        result = self.approval_engine.get_workflow_status(workflow_id)
        if result['success']:
            return {
                'status': result['status'],
                'current_step': result['current_step'],
                'total_steps': result['total_steps']
            }
        return None
    
    def _calculate_status_counts(self, contracts: List[Dict]) -> Dict:
        """Calculate contract status distribution"""
        status_counts = {}
        for contract in contracts:
            status = contract['status']
            status_counts[status] = status_counts.get(status, 0) + 1
        return status_counts
    
    def _get_available_actions(self, contract: Dict, user_id: int) -> List[str]:
        """Get available actions for contract based on status and user role"""
        actions = []
        status = contract['status']
        
        if status == ContractStatus.DRAFT.value:
            actions.extend(['edit', 'submit_for_approval', 'delete'])
        elif status == ContractStatus.PENDING_REVIEW.value:
            actions.extend(['view', 'withdraw'])
        elif status == ContractStatus.APPROVED.value:
            actions.extend(['view', 'sign', 'download'])
        elif status == ContractStatus.SIGNED.value:
            actions.extend(['view', 'download', 'activate'])
        elif status == ContractStatus.ACTIVE.value:
            actions.extend(['view', 'download', 'amend', 'terminate'])
        
        return actions


class ContractAnalyticsEngine:
    """
    Contract analytics and reporting
    """
    
    def __init__(self, lifecycle_manager: ContractLifecycleManager):
        self.lifecycle_manager = lifecycle_manager
    
    async def get_contract_analytics(self, user_id: int, period_days: int = 30) -> Dict:
        """Get contract analytics for specified period"""
        try:
            contracts_result = self.lifecycle_manager.get_user_contracts(user_id)
            
            if not contracts_result['success']:
                return contracts_result
            
            contracts = contracts_result['contracts']
            
            # Calculate analytics
            total_contracts = len(contracts)
            active_contracts = len([c for c in contracts if c['status'] == ContractStatus.ACTIVE.value])
            pending_contracts = len([c for c in contracts if c['status'] == ContractStatus.PENDING_REVIEW.value])
            completed_contracts = len([c for c in contracts if c['status'] == ContractStatus.COMPLETED.value])
            
            # Compliance analytics
            compliance_scores = [c['compliance_score'] for c in contracts if c['compliance_score'] > 0]
            avg_compliance = sum(compliance_scores) / len(compliance_scores) if compliance_scores else 0
            
            # Contract type distribution
            type_distribution = {}
            for contract in contracts:
                contract_type = contract['contract_type']
                type_distribution[contract_type] = type_distribution.get(contract_type, 0) + 1
            
            # Status distribution
            status_distribution = contracts_result['status_counts']
            
            return {
                'success': True,
                'analytics': {
                    'total_contracts': total_contracts,
                    'active_contracts': active_contracts,
                    'pending_contracts': pending_contracts,
                    'completed_contracts': completed_contracts,
                    'average_compliance_score': round(avg_compliance, 1),
                    'type_distribution': type_distribution,
                    'status_distribution': status_distribution,
                    'contract_velocity': self._calculate_contract_velocity(contracts),
                    'compliance_trends': self._calculate_compliance_trends(contracts),
                    'approval_time_avg': self._calculate_avg_approval_time(contracts)
                }
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _calculate_contract_velocity(self, contracts: List[Dict]) -> Dict:
        """Calculate contract creation velocity"""
        # Simplified velocity calculation
        recent_contracts = [c for c in contracts if self._is_recent(c['created_date'], 7)]
        return {
            'contracts_per_week': len(recent_contracts),
            'trend': 'increasing' if len(recent_contracts) > len(contracts) * 0.1 else 'stable'
        }
    
    def _calculate_compliance_trends(self, contracts: List[Dict]) -> Dict:
        """Calculate compliance score trends"""
        scores = [c['compliance_score'] for c in contracts if c['compliance_score'] > 0]
        if not scores:
            return {'average': 0, 'trend': 'stable'}
        
        avg_score = sum(scores) / len(scores)
        return {
            'average': round(avg_score, 1),
            'trend': 'improving' if avg_score > 85 else 'stable'
        }
    
    def _calculate_avg_approval_time(self, contracts: List[Dict]) -> str:
        """Calculate average approval time"""
        # Simplified calculation - in production would use actual workflow data
        return "2.3 days"
    
    def _is_recent(self, date_str: str, days: int) -> bool:
        """Check if date is within specified days"""
        try:
            contract_date = datetime.fromisoformat(date_str.replace('Z', '+00:00'))
            cutoff_date = datetime.now() - timedelta(days=days)
            return contract_date >= cutoff_date
        except:
            return False


# Initialize global instances
contract_lifecycle_manager = ContractLifecycleManager()
contract_analytics_engine = ContractAnalyticsEngine(contract_lifecycle_manager)