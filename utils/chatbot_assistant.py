"""
AI-Powered Chatbot Assistant - Phase 6.1
Intelligent customer support bot with shipment assistance and multilingual support
Modern conversational AI for logistics platform
"""

import asyncio
import json
import re
from typing import Dict, List, Optional, Union, Tuple
from datetime import datetime, timed<PERSON>ta
from decimal import Decimal
import uuid
from dataclasses import dataclass, asdict
from enum import Enum

from django.utils import timezone
from asgiref.sync import sync_to_async


class IntentType(Enum):
    """Chatbot intent types"""
    GREETING = "greeting"
    SHIPMENT_TRACKING = "shipment_tracking"
    QUOTE_REQUEST = "quote_request"
    PRICING_INQUIRY = "pricing_inquiry"
    DOCUMENTATION_HELP = "documentation_help"
    CUSTOMS_HELP = "customs_help"
    PAYMENT_HELP = "payment_help"
    COMPLAINT = "complaint"
    COMPLIMENT = "compliment"
    GOODBYE = "goodbye"
    UNKNOWN = "unknown"


@dataclass
class ChatMessage:
    """Chat message data structure"""
    message_id: str
    session_id: str
    user_id: Optional[str]
    user_type: str
    message: str
    intent: str
    confidence: float
    response: str
    timestamp: datetime
    language: str
    context: Dict
    resolved: bool


@dataclass
class ChatSession:
    """Chat session data structure"""
    session_id: str
    user_id: Optional[str]
    user_type: str
    language: str
    started_at: datetime
    last_activity: datetime
    messages_count: int
    context: Dict
    status: str  # 'active', 'resolved', 'escalated'


class IntentClassifier:
    """Natural language intent classification system"""
    
    def __init__(self):
        self.intent_patterns = {
            IntentType.GREETING: [
                r'\b(hello|hi|hey|good\s+(morning|afternoon|evening)|howdy)\b',
                r'\b(help|assistance|support)\b',
                r'^(start|begin)',
            ],
            IntentType.SHIPMENT_TRACKING: [
                r'\b(track|tracking|status|where\s+is|locate|find)\b.*\b(shipment|package|order|delivery)\b',
                r'\b(tracking\s+number|track\s+number|shipment\s+id)\b',
                r'\bCL[A-Z0-9]+\b',  # Cloverics tracking number pattern
                r'\b(delivery\s+status|shipping\s+status)\b',
            ],
            IntentType.QUOTE_REQUEST: [
                r'\b(quote|price|cost|rate|estimate)\b.*\b(shipping|transport|delivery)\b',
                r'\b(how\s+much|what.*cost|pricing)\b',
                r'\b(from\s+.+\s+to\s+.+|ship\s+from|send\s+to)\b',
                r'\b(get\s+quote|request\s+quote)\b',
            ],
            IntentType.PRICING_INQUIRY: [
                r'\b(pricing|rates|fees|charges|tariff)\b',
                r'\b(how\s+much\s+does|what\s+does.*cost)\b',
                r'\b(price\s+list|rate\s+card)\b',
                r'\b(discount|promotion|offer)\b',
            ],
            IntentType.DOCUMENTATION_HELP: [
                r'\b(document|paperwork|forms|invoice|bill\s+of\s+lading)\b',
                r'\b(customs\s+forms|commercial\s+invoice|packing\s+list)\b',
                r'\b(certificate\s+of\s+origin|export\s+documents)\b',
                r'\b(how\s+to.*document|need\s+documents)\b',
            ],
            IntentType.CUSTOMS_HELP: [
                r'\b(customs|duty|tariff|import|export)\b',
                r'\b(customs\s+clearance|customs\s+declaration)\b',
                r'\b(hs\s+code|harmonized\s+code|commodity\s+code)\b',
                r'\b(restricted\s+items|prohibited\s+goods)\b',
            ],
            IntentType.PAYMENT_HELP: [
                r'\b(payment|pay|billing|invoice|charge)\b',
                r'\b(credit\s+card|bank\s+transfer|paypal)\b',
                r'\b(refund|cancellation|cancel)\b',
                r'\b(payment\s+failed|payment\s+error)\b',
            ],
            IntentType.COMPLAINT: [
                r'\b(problem|issue|complaint|wrong|error|mistake)\b',
                r'\b(damaged|lost|delayed|late)\b',
                r'\b(not\s+working|broken|failed)\b',
                r'\b(disappointed|frustrated|angry|upset)\b',
            ],
            IntentType.COMPLIMENT: [
                r'\b(thanks|thank\s+you|appreciate|great|excellent|good)\b',
                r'\b(amazing|wonderful|fantastic|perfect|satisfied)\b',
                r'\b(love|like|impressed|happy)\b',
            ],
            IntentType.GOODBYE: [
                r'\b(goodbye|bye|farewell|see\s+you|talk\s+later)\b',
                r'\b(thanks.*bye|thank\s+you.*bye)\b',
                r'^(exit|quit|end|stop)$',
            ],
        }
    
    def classify_intent(self, message: str) -> Tuple[IntentType, float]:
        """Classify user message intent"""
        
        message_lower = message.lower().strip()
        intent_scores = {}
        
        for intent, patterns in self.intent_patterns.items():
            score = 0
            pattern_count = len(patterns)
            
            for pattern in patterns:
                if re.search(pattern, message_lower):
                    score += 1
            
            # Calculate confidence based on pattern matches
            confidence = score / pattern_count if pattern_count > 0 else 0
            intent_scores[intent] = confidence
        
        # Find best intent
        if intent_scores:
            best_intent = max(intent_scores, key=intent_scores.get)
            best_confidence = intent_scores[best_intent]
            
            # Require minimum confidence threshold
            if best_confidence >= 0.3:
                return best_intent, best_confidence
        
        return IntentType.UNKNOWN, 0.0
    
    def extract_entities(self, message: str, intent: IntentType) -> Dict:
        """Extract entities from message based on intent"""
        
        entities = {}
        message_lower = message.lower()
        
        if intent == IntentType.SHIPMENT_TRACKING:
            # Extract tracking numbers
            tracking_patterns = [
                r'\bCL[A-Z0-9]{6,12}\b',
                r'\b[A-Z0-9]{8,15}\b'  # Generic tracking number
            ]
            for pattern in tracking_patterns:
                matches = re.findall(pattern, message.upper())
                if matches:
                    entities['tracking_number'] = matches[0]
                    break
        
        elif intent == IntentType.QUOTE_REQUEST:
            # Extract locations
            location_pattern = r'\b(from|origin)\s+([A-Za-z\s,]+?)\s+(to|destination)\s+([A-Za-z\s,]+)\b'
            location_match = re.search(location_pattern, message_lower)
            if location_match:
                entities['origin'] = location_match.group(2).strip()
                entities['destination'] = location_match.group(4).strip()
            
            # Extract weight
            weight_pattern = r'\b(\d+(?:\.\d+)?)\s*(kg|kilogram|lb|pound|ton)\b'
            weight_match = re.search(weight_pattern, message_lower)
            if weight_match:
                entities['weight'] = weight_match.group(1)
                entities['weight_unit'] = weight_match.group(2)
        
        elif intent == IntentType.CUSTOMS_HELP:
            # Extract HS codes
            hs_pattern = r'\b(\d{4}(?:\.\d{2}){0,2})\b'
            hs_match = re.search(hs_pattern, message)
            if hs_match:
                entities['hs_code'] = hs_match.group(1)
        
        return entities


class ResponseGenerator:
    """Generates contextual responses for different intents"""
    
    def __init__(self):
        self.response_templates = self._load_response_templates()
        self.knowledge_base = self._load_knowledge_base()
    
    def _load_response_templates(self) -> Dict:
        """Load response templates for different intents"""
        
        return {
            IntentType.GREETING: [
                "Hello! I'm Clover, your AI logistics assistant. How can I help you today?",
                "Hi there! Welcome to Cloverics. I'm here to help with all your shipping needs.",
                "Good day! I'm Clover, ready to assist with tracking, quotes, and logistics questions.",
            ],
            IntentType.SHIPMENT_TRACKING: [
                "I'll help you track your shipment. Let me look that up for you.",
                "Tracking your package right away. Please give me a moment.",
                "I can help with shipment tracking. Let me check the latest status.",
            ],
            IntentType.QUOTE_REQUEST: [
                "I'd be happy to help you get a shipping quote. Let me gather some details.",
                "Great! I can help you find the best shipping rates. Let me ask a few questions.",
                "I'll help you get competitive quotes from our logistics providers.",
            ],
            IntentType.PRICING_INQUIRY: [
                "I can provide information about our pricing structure. What specific rates are you interested in?",
                "Our pricing varies based on several factors. Let me explain our rate system.",
                "I'd be happy to discuss pricing options. What type of shipping are you considering?",
            ],
            IntentType.DOCUMENTATION_HELP: [
                "I can guide you through the documentation process. What documents do you need help with?",
                "Document preparation is important for smooth shipping. I'm here to help you understand the requirements.",
                "I'll help you with shipping documents. Let me know what specific documentation you need.",
            ],
            IntentType.CUSTOMS_HELP: [
                "Customs procedures can be complex. I'm here to help you navigate them smoothly.",
                "I can assist with customs-related questions. What specific information do you need?",
                "Customs compliance is crucial for international shipping. How can I help you today?",
            ],
            IntentType.PAYMENT_HELP: [
                "I can help with payment-related questions. What specific issue are you experiencing?",
                "Payment processing is secure and straightforward. Let me assist you with any concerns.",
                "I'm here to help resolve any payment issues. Please describe what's happening.",
            ],
            IntentType.COMPLAINT: [
                "I'm sorry to hear you're experiencing an issue. Let me help resolve this for you right away.",
                "I understand your concern and I'm here to help. Please tell me more about the problem.",
                "I apologize for any inconvenience. Let's work together to fix this issue promptly.",
            ],
            IntentType.COMPLIMENT: [
                "Thank you so much! I'm glad I could help. Is there anything else you need assistance with?",
                "I really appreciate your kind words! It's my pleasure to help you with your logistics needs.",
                "Thank you for the feedback! I'm here whenever you need logistics support.",
            ],
            IntentType.GOODBYE: [
                "Thank you for using Cloverics! Have a great day and don't hesitate to reach out anytime.",
                "Goodbye! I'm always here when you need logistics assistance. Take care!",
                "It was great helping you today. Safe shipping and see you next time!",
            ],
            IntentType.UNKNOWN: [
                "I'm not sure I understand. Could you rephrase your question? I can help with tracking, quotes, customs, and more.",
                "I'd like to help, but I need a bit more clarity. Are you looking for tracking, pricing, or something else?",
                "I didn't quite catch that. I can assist with shipments, quotes, documentation, and general logistics questions.",
            ],
        }
    
    def _load_knowledge_base(self) -> Dict:
        """Load knowledge base for detailed responses"""
        
        return {
            'shipping_times': {
                'domestic': '2-5 business days',
                'international': '5-15 business days',
                'express': '1-3 business days',
            },
            'documentation': {
                'bill_of_lading': 'Legal document between shipper and carrier detailing cargo information',
                'commercial_invoice': 'Document used for customs clearance showing transaction details',
                'packing_list': 'Detailed list of package contents and specifications',
                'certificate_of_origin': 'Document certifying the country where goods were manufactured',
            },
            'payment_methods': [
                'Credit/Debit Cards',
                'Bank Transfer',
                'Digital Wallets',
                'Corporate Accounts',
            ],
            'transport_modes': {
                'truck': 'Road transport for domestic and regional shipments',
                'ship': 'Ocean freight for international cargo',
                'air': 'Fast air freight for urgent shipments',
                'rail': 'Eco-friendly rail transport for bulk cargo',
            },
        }
    
    async def generate_response(self, intent: IntentType, entities: Dict, context: Dict, user_type: str) -> str:
        """Generate contextual response based on intent and entities"""
        
        if intent == IntentType.SHIPMENT_TRACKING and 'tracking_number' in entities:
            return await self._generate_tracking_response(entities['tracking_number'], context)
        
        elif intent == IntentType.QUOTE_REQUEST and 'origin' in entities and 'destination' in entities:
            return await self._generate_quote_response(entities, context)
        
        elif intent == IntentType.DOCUMENTATION_HELP:
            return self._generate_documentation_response(entities, context)
        
        elif intent == IntentType.CUSTOMS_HELP:
            return self._generate_customs_response(entities, context)
        
        else:
            # Use template response
            templates = self.response_templates.get(intent, self.response_templates[IntentType.UNKNOWN])
            import random
            return random.choice(templates)
    
    async def _generate_tracking_response(self, tracking_number: str, context: Dict) -> str:
        """Generate tracking-specific response"""
        
        # Mock tracking data (would fetch from real database)
        tracking_info = {
            'status': 'In Transit',
            'location': 'Hamburg, Germany',
            'estimated_delivery': '2025-07-08',
            'last_update': '2025-07-03 14:30'
        }
        
        response = f"✅ **Tracking #{tracking_number}**\n\n"
        response += f"**Status:** {tracking_info['status']}\n"
        response += f"**Current Location:** {tracking_info['location']}\n"
        response += f"**Estimated Delivery:** {tracking_info['estimated_delivery']}\n"
        response += f"**Last Updated:** {tracking_info['last_update']}\n\n"
        response += "Your shipment is progressing well! I'll keep monitoring it for you. 📦"
        
        return response
    
    async def _generate_quote_response(self, entities: Dict, context: Dict) -> str:
        """Generate quote request response"""
        
        origin = entities.get('origin', 'your origin')
        destination = entities.get('destination', 'your destination')
        weight = entities.get('weight', 'the specified weight')
        
        response = f"🚛 **Quote Request: {origin.title()} → {destination.title()}**\n\n"
        response += f"I'm searching for the best rates for shipping {weight}kg from {origin} to {destination}.\n\n"
        response += "**Estimated Options:**\n"
        response += "• **Truck Transport:** $2.45/kg (5-7 days)\n"
        response += "• **Air Freight:** $8.50/kg (2-3 days)\n"
        response += "• **Ocean Freight:** $1.20/kg (10-15 days)\n\n"
        response += "💡 *These are estimated rates. Would you like me to request official quotes from our providers?*"
        
        return response
    
    def _generate_documentation_response(self, entities: Dict, context: Dict) -> str:
        """Generate documentation help response"""
        
        response = "📋 **Shipping Documentation Guide**\n\n"
        response += "**Required Documents:**\n"
        response += "• **Bill of Lading** - Legal contract with carrier\n"
        response += "• **Commercial Invoice** - For customs clearance\n"
        response += "• **Packing List** - Detailed cargo description\n"
        response += "• **Certificate of Origin** - For international shipments\n\n"
        response += "✨ *Good news! Cloverics can automatically generate all these documents for you.*\n\n"
        response += "Would you like me to help you prepare any specific documents?"
        
        return response
    
    def _generate_customs_response(self, entities: Dict, context: Dict) -> str:
        """Generate customs help response"""
        
        if 'hs_code' in entities:
            hs_code = entities['hs_code']
            response = f"🛃 **HS Code: {hs_code}**\n\n"
            response += "I can help you verify this Harmonized System code for customs classification.\n\n"
        else:
            response = "🛃 **Customs Assistance**\n\n"
            response += "**Common Customs Requirements:**\n"
            response += "• HS Code classification for your goods\n"
            response += "• Accurate declared value\n"
            response += "• Proper documentation (invoices, certificates)\n"
            response += "• Compliance with import/export regulations\n\n"
        
        response += "💡 *I can help you with customs declarations and documentation. What specific assistance do you need?*"
        
        return response


class ChatbotAssistant:
    """Main chatbot assistant class"""
    
    def __init__(self):
        self.intent_classifier = IntentClassifier()
        self.response_generator = ResponseGenerator()
        self.active_sessions = {}
        self.multilingual_support = {
            'en': 'English',
            'es': 'Spanish',
            'fr': 'French',
            'de': 'German',
            'tr': 'Turkish',
            'zh': 'Chinese',
        }
    
    async def process_message(self, session_id: str, user_message: str, user_id: Optional[str] = None, 
                            user_type: str = 'customer', language: str = 'en') -> Dict:
        """Process incoming chat message and generate response"""
        
        try:
            # Get or create session
            session = await self._get_or_create_session(session_id, user_id, user_type, language)
            
            # Classify intent
            intent, confidence = self.intent_classifier.classify_intent(user_message)
            
            # Extract entities
            entities = self.intent_classifier.extract_entities(user_message, intent)
            
            # Generate response
            response = await self.response_generator.generate_response(
                intent, entities, session.context, user_type
            )
            
            # Create chat message
            chat_message = ChatMessage(
                message_id=str(uuid.uuid4()),
                session_id=session_id,
                user_id=user_id,
                user_type=user_type,
                message=user_message,
                intent=intent.value,
                confidence=confidence,
                response=response,
                timestamp=datetime.now(),
                language=language,
                context=session.context,
                resolved=intent in [IntentType.GOODBYE, IntentType.COMPLIMENT]
            )
            
            # Update session
            session.messages_count += 1
            session.last_activity = datetime.now()
            if intent == IntentType.GOODBYE:
                session.status = 'resolved'
            
            # Save message and session
            await self._save_chat_message(chat_message)
            await self._update_session(session)
            
            # Prepare response
            return {
                'success': True,
                'message_id': chat_message.message_id,
                'response': response,
                'intent': intent.value,
                'confidence': confidence,
                'entities': entities,
                'session_status': session.status,
                'suggested_actions': await self._get_suggested_actions(intent, entities, user_type),
                'escalation_available': confidence < 0.5 or intent == IntentType.COMPLAINT,
                'timestamp': chat_message.timestamp.isoformat()
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'response': "I'm experiencing some technical difficulties. Please try again or contact our support team.",
                'escalation_available': True
            }
    
    async def get_session_history(self, session_id: str) -> Dict:
        """Get chat session history"""
        
        try:
            session = self.active_sessions.get(session_id)
            if not session:
                return {'success': False, 'error': 'Session not found'}
            
            # Get messages (would fetch from database)
            messages = await self._get_session_messages(session_id)
            
            return {
                'success': True,
                'session': asdict(session),
                'messages': [asdict(msg) for msg in messages],
                'total_messages': len(messages)
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def escalate_to_human(self, session_id: str, reason: str) -> Dict:
        """Escalate chat session to human support"""
        
        try:
            session = self.active_sessions.get(session_id)
            if not session:
                return {'success': False, 'error': 'Session not found'}
            
            session.status = 'escalated'
            await self._update_session(session)
            
            # Create support ticket (would integrate with support system)
            ticket_id = f"TICKET-{datetime.now().strftime('%Y%m%d')}-{uuid.uuid4().hex[:8].upper()}"
            
            return {
                'success': True,
                'ticket_id': ticket_id,
                'message': 'Your conversation has been escalated to our support team. They will be with you shortly.',
                'estimated_wait_time': '5-10 minutes',
                'session_status': 'escalated'
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def get_chatbot_analytics(self) -> Dict:
        """Get chatbot performance analytics"""
        
        try:
            # Mock analytics data (would fetch from database)
            analytics = {
                'total_sessions': 1247,
                'active_sessions': 23,
                'messages_today': 3891,
                'resolution_rate': 87.5,
                'average_session_length': 4.2,
                'escalation_rate': 8.2,
                'top_intents': [
                    {'intent': 'shipment_tracking', 'count': 1456, 'percentage': 42.3},
                    {'intent': 'quote_request', 'count': 987, 'percentage': 28.7},
                    {'intent': 'pricing_inquiry', 'count': 543, 'percentage': 15.8},
                    {'intent': 'documentation_help', 'count': 234, 'percentage': 6.8},
                    {'intent': 'customs_help', 'count': 123, 'percentage': 3.6}
                ],
                'user_satisfaction': 4.6,
                'languages_used': [
                    {'language': 'English', 'percentage': 78.2},
                    {'language': 'Spanish', 'percentage': 12.5},
                    {'language': 'Turkish', 'percentage': 6.3},
                    {'language': 'German', 'percentage': 3.0}
                ],
                'peak_hours': [9, 10, 11, 14, 15, 16],
                'response_time_avg': 0.8  # seconds
            }
            
            return {
                'success': True,
                'analytics': analytics,
                'generated_at': datetime.now().isoformat()
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    # Internal methods
    
    async def _get_or_create_session(self, session_id: str, user_id: Optional[str], 
                                   user_type: str, language: str) -> ChatSession:
        """Get existing session or create new one"""
        
        if session_id in self.active_sessions:
            session = self.active_sessions[session_id]
            session.last_activity = datetime.now()
            return session
        
        # Create new session
        session = ChatSession(
            session_id=session_id,
            user_id=user_id,
            user_type=user_type,
            language=language,
            started_at=datetime.now(),
            last_activity=datetime.now(),
            messages_count=0,
            context={},
            status='active'
        )
        
        self.active_sessions[session_id] = session
        return session
    
    async def _save_chat_message(self, message: ChatMessage):
        """Save chat message to database"""
        # This would save to actual database
        pass
    
    async def _update_session(self, session: ChatSession):
        """Update session in database"""
        # This would update actual database
        self.active_sessions[session.session_id] = session
    
    async def _get_session_messages(self, session_id: str) -> List[ChatMessage]:
        """Get all messages for a session"""
        # This would fetch from database
        return []
    
    async def _get_suggested_actions(self, intent: IntentType, entities: Dict, user_type: str) -> List[Dict]:
        """Get suggested actions based on intent"""
        
        suggestions = []
        
        if intent == IntentType.SHIPMENT_TRACKING:
            suggestions = [
                {'action': 'view_full_tracking', 'label': 'View Full Tracking Details'},
                {'action': 'delivery_notifications', 'label': 'Set Delivery Notifications'},
                {'action': 'contact_carrier', 'label': 'Contact Carrier Directly'}
            ]
        
        elif intent == IntentType.QUOTE_REQUEST:
            suggestions = [
                {'action': 'get_official_quotes', 'label': 'Get Official Quotes'},
                {'action': 'compare_services', 'label': 'Compare Service Options'},
                {'action': 'book_shipment', 'label': 'Book Shipment Now'}
            ]
        
        elif intent == IntentType.DOCUMENTATION_HELP:
            suggestions = [
                {'action': 'generate_documents', 'label': 'Generate Documents'},
                {'action': 'document_templates', 'label': 'View Document Templates'},
                {'action': 'customs_guidance', 'label': 'Get Customs Guidance'}
            ]
        
        return suggestions


# Convenience class for API integration
class ChatbotAPI:
    """API wrapper for chatbot functionality"""
    
    def __init__(self):
        self.chatbot = ChatbotAssistant()
    
    async def chat(self, session_id: str, message: str, user_id: str = None, 
                  user_type: str = 'customer', language: str = 'en') -> Dict:
        """Process chat message"""
        return await self.chatbot.process_message(session_id, message, user_id, user_type, language)
    
    async def get_history(self, session_id: str) -> Dict:
        """Get chat history"""
        return await self.chatbot.get_session_history(session_id)
    
    async def escalate(self, session_id: str, reason: str) -> Dict:
        """Escalate to human support"""
        return await self.chatbot.escalate_to_human(session_id, reason)
    
    async def get_analytics(self) -> Dict:
        """Get chatbot analytics"""
        return await self.chatbot.get_chatbot_analytics()