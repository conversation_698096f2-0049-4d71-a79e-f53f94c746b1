"""
Advanced Payment Processor for LogistiLink
Handles hybrid Stripe + Bank Transfer payment system
"""

import stripe
import os
from decimal import Decimal
from django.conf import settings
from django.utils import timezone
from django.db import transaction
from core.models import User
from core.notification_models import Notification
from shipments.models import Shipment
from payments.advanced_payment_models import (
    AdvancedPayment, LogisticsWallet, BankTransferApproval, 
    PlatformFeeCollection, WalletTransaction
)

# Configure Stripe
stripe.api_key = os.environ.get('STRIPE_SECRET_KEY')

class AdvancedPaymentProcessor:
    """Main payment processor for hybrid system"""
    
    def __init__(self):
        self.platform_fee_percentage = Decimal('5.0')  # 5% platform fee
        self.refund_penalty_percentage = Decimal('20.0')  # 20% penalty on refunds
    
    def create_payment_for_shipment(self, shipment_id, customer_id, payment_method, 
                                   shipment_amount, platform_fee_percentage=None):
        """Create a new payment record for a shipment"""
        try:
            with transaction.atomic():
                shipment = Shipment.objects.get(id=shipment_id)
                customer = User.objects.get(id=customer_id)
                
                if platform_fee_percentage:
                    self.platform_fee_percentage = Decimal(str(platform_fee_percentage))
                
                # Calculate fees
                platform_fee = (Decimal(str(shipment_amount)) * self.platform_fee_percentage) / Decimal('100')
                total_amount = Decimal(str(shipment_amount)) + platform_fee
                
                # Create payment record
                payment = AdvancedPayment.objects.create(
                    shipment=shipment,
                    customer=customer,
                    logistics_provider=shipment.logistics_provider,
                    shipment_amount=Decimal(str(shipment_amount)),
                    platform_fee_amount=platform_fee,
                    total_amount=total_amount,
                    payment_method=payment_method,
                    payment_status='pending'
                )
                
                # Handle different payment methods
                if payment_method == 'stripe':
                    return self._process_stripe_payment(payment)
                elif payment_method == 'bank_transfer':
                    return self._setup_bank_transfer(payment)
                
                return {'success': True, 'payment_id': payment.payment_id}
                
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _process_stripe_payment(self, payment):
        """Process Stripe payment for shipment amount only"""
        try:
            # Create payment intent for shipment amount
            intent = stripe.PaymentIntent.create(
                amount=int(payment.shipment_amount * 100),  # Convert to cents
                currency='usd',
                customer=self._get_or_create_stripe_customer(payment.customer),
                metadata={
                    'payment_id': str(payment.payment_id),
                    'shipment_id': payment.shipment.id,
                    'type': 'shipment_payment'
                }
            )
            
            payment.stripe_payment_intent_id = intent.id
            payment.save()
            
            # Setup platform fee collection separately
            self._setup_platform_fee_collection(payment)
            
            return {
                'success': True,
                'payment_id': payment.payment_id,
                'client_secret': intent.client_secret,
                'requires_platform_fee': True
            }
            
        except stripe.error.StripeError as e:
            payment.payment_status = 'failed'
            payment.save()
            return {'success': False, 'error': str(e)}
    
    def _setup_bank_transfer(self, payment):
        """Setup bank transfer payment tracking"""
        try:
            # Create bank transfer approval record
            approval = BankTransferApproval.objects.create(
                payment=payment,
                approval_status='pending'
            )
            
            # Setup platform fee collection for later
            self._setup_platform_fee_collection(payment)
            
            # Notify logistics provider
            Notification.objects.create(
                user=payment.logistics_provider,
                title="New Bank Transfer Payment Pending",
                message=f"Customer {payment.customer.company_name} has initiated a bank transfer payment for shipment {payment.shipment.tracking_number}. Please verify payment receipt.",
                notification_type='payment_pending',
                priority='high'
            )
            
            return {
                'success': True,
                'payment_id': payment.payment_id,
                'bank_details': self._get_bank_details(),
                'requires_verification': True
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _setup_platform_fee_collection(self, payment):
        """Setup platform fee collection via Stripe"""
        try:
            # Create payment intent for platform fee
            intent = stripe.PaymentIntent.create(
                amount=int(payment.platform_fee_amount * 100),
                currency='usd',
                customer=self._get_or_create_stripe_customer(payment.customer),
                metadata={
                    'payment_id': str(payment.payment_id),
                    'type': 'platform_fee'
                }
            )
            
            PlatformFeeCollection.objects.create(
                payment=payment,
                customer=payment.customer,
                amount=payment.platform_fee_amount,
                stripe_payment_intent_id=intent.id
            )
            
            return intent.client_secret
            
        except stripe.error.StripeError as e:
            return None
    
    def verify_bank_transfer(self, payment_id, logistics_provider_id, 
                           reference_number, notes=""):
        """Verify bank transfer payment"""
        try:
            with transaction.atomic():
                payment = AdvancedPayment.objects.get(
                    payment_id=payment_id,
                    logistics_provider_id=logistics_provider_id
                )
                
                approval = payment.bank_approval
                approval.approval_status = 'approved'
                approval.reviewed_at = timezone.now()
                approval.reviewed_by_id = logistics_provider_id
                approval.reference_number = reference_number
                approval.notes = notes
                approval.save()
                
                payment.bank_transfer_verified = True
                payment.bank_transfer_verified_by_id = logistics_provider_id
                payment.bank_transfer_verified_at = timezone.now()
                payment.bank_transfer_reference = reference_number
                payment.payment_status = 'completed'
                payment.save()
                
                # Update shipment status
                payment.shipment.is_paid = True
                payment.shipment.status = 'CONFIRMED'
                payment.shipment.save()
                
                # Notify customer
                Notification.objects.create(
                    user=payment.customer,
                    title="Payment Verified",
                    message=f"Your bank transfer payment for shipment {payment.shipment.tracking_number} has been verified. Your shipment is now active.",
                    notification_type='payment_verified',
                    priority='medium'
                )
                
                return {'success': True, 'message': 'Bank transfer verified successfully'}
                
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def collect_platform_fee(self, payment_id):
        """Collect platform fee via Stripe"""
        try:
            payment = AdvancedPayment.objects.get(payment_id=payment_id)
            fee_collection = payment.platformfeecollection_set.first()
            
            if not fee_collection:
                return {'success': False, 'error': 'Platform fee collection not found'}
            
            # Confirm payment intent
            intent = stripe.PaymentIntent.confirm(fee_collection.stripe_payment_intent_id)
            
            if intent.status == 'succeeded':
                fee_collection.collection_status = 'collected'
                fee_collection.save()
                
                payment.platform_fee_paid = True
                payment.platform_fee_stripe_intent_id = intent.id
                payment.save()
                
                # If both shipment and platform fee are paid, activate shipment
                if payment.payment_status == 'completed':
                    payment.shipment.status = 'CONFIRMED'
                    payment.shipment.save()
                
                return {'success': True, 'message': 'Platform fee collected successfully'}
            else:
                return {'success': False, 'error': 'Platform fee payment failed'}
                
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def request_refund(self, payment_id, customer_id, reason=""):
        """Customer requests refund"""
        try:
            payment = AdvancedPayment.objects.get(
                payment_id=payment_id,
                customer_id=customer_id
            )
            
            if payment.payment_status != 'completed':
                return {'success': False, 'error': 'Payment not completed'}
            
            payment.refund_requested = True
            payment.refund_requested_at = timezone.now()
            payment.save()
            
            # Notify logistics provider
            Notification.objects.create(
                user=payment.logistics_provider,
                title="Refund Request",
                message=f"Customer {payment.customer.company_name} has requested a refund for shipment {payment.shipment.tracking_number}. Reason: {reason}",
                notification_type='refund_request',
                priority='high'
            )
            
            return {'success': True, 'message': 'Refund request submitted'}
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def process_refund(self, payment_id, logistics_provider_id, approve=True):
        """Logistics provider approves/denies refund"""
        try:
            with transaction.atomic():
                payment = AdvancedPayment.objects.get(
                    payment_id=payment_id,
                    logistics_provider_id=logistics_provider_id
                )
                
                if not payment.refund_requested:
                    return {'success': False, 'error': 'No refund request found'}
                
                if approve:
                    # Calculate penalty
                    penalty = (payment.platform_fee_amount * self.refund_penalty_percentage) / Decimal('100')
                    
                    # Get or create logistics wallet
                    wallet, created = LogisticsWallet.objects.get_or_create(
                        logistics_provider=payment.logistics_provider
                    )
                    
                    # Record penalty
                    wallet.record_penalty(penalty, f"Refund penalty for {payment.shipment.tracking_number}")
                    
                    # Process Stripe refund for shipment amount only
                    if payment.stripe_payment_intent_id:
                        stripe.Refund.create(
                            payment_intent=payment.stripe_payment_intent_id,
                            amount=int(payment.shipment_amount * 100)
                        )
                    
                    payment.refund_approved = True
                    payment.refund_approved_at = timezone.now()
                    payment.refund_amount = payment.shipment_amount
                    payment.penalty_amount = penalty
                    payment.payment_status = 'refunded'
                    payment.save()
                    
                    # Update shipment
                    payment.shipment.status = 'CANCELLED'
                    payment.shipment.save()
                    
                    # Notify customer
                    Notification.objects.create(
                        user=payment.customer,
                        title="Refund Approved",
                        message=f"Your refund request for shipment {payment.shipment.tracking_number} has been approved. Amount: ${payment.refund_amount}",
                        notification_type='refund_approved',
                        priority='medium'
                    )
                    
                    return {'success': True, 'message': f'Refund processed. Penalty: ${penalty}'}
                else:
                    # Deny refund
                    payment.refund_requested = False
                    payment.save()
                    
                    Notification.objects.create(
                        user=payment.customer,
                        title="Refund Denied",
                        message=f"Your refund request for shipment {payment.shipment.tracking_number} has been denied.",
                        notification_type='refund_denied',
                        priority='medium'
                    )
                    
                    return {'success': True, 'message': 'Refund request denied'}
                
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def get_wallet_balance(self, logistics_provider_id):
        """Get logistics provider wallet balance"""
        try:
            wallet = LogisticsWallet.objects.get(logistics_provider_id=logistics_provider_id)
            return {
                'success': True,
                'balance': float(wallet.balance),
                'total_earned': float(wallet.total_earned),
                'total_penalties': float(wallet.total_penalties),
                'total_refunds_given': float(wallet.total_refunds_given)
            }
        except LogisticsWallet.DoesNotExist:
            return {
                'success': True,
                'balance': 0.0,
                'total_earned': 0.0,
                'total_penalties': 0.0,
                'total_refunds_given': 0.0
            }
    
    def _get_or_create_stripe_customer(self, user):
        """Get or create Stripe customer"""
        # This would typically check if user has stripe_customer_id
        # For now, create a new customer each time
        customer = stripe.Customer.create(
            email=user.email,
            name=user.company_name,
            metadata={'user_id': user.id}
        )
        return customer.id
    
    def _get_bank_details(self):
        """Get bank transfer details"""
        return {
            'bank_name': 'LogistiLink Banking Partner',
            'account_number': '**********',
            'routing_number': '*********',
            'account_name': 'LogistiLink Holdings',
            'reference': 'Include your payment ID in transfer description'
        }