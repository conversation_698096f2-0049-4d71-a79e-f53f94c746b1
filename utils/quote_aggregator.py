"""
Instant Multi-Modal Quote Aggregation System
Core component for achieving Freightos-level instant quoting functionality
"""

import asyncio
import json
import time
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple
from asgiref.sync import sync_to_async
from django.db import transaction
from django.utils import timezone
import logging

logger = logging.getLogger(__name__)

class QuoteAggregator:
    """
    Advanced quote aggregation engine that sends parallel requests to all active providers
    and normalizes responses for instant comparison - Freightos-style functionality
    """
    
    def __init__(self):
        self.active_providers = []
        self.response_timeout = 30  # seconds
        self.max_concurrent_requests = 50
        
    async def get_instant_quotes(self, quote_request_data: Dict) -> Dict:
        """
        Send parallel quote requests to all active providers and aggregate responses
        This is the core Freightos-competing functionality
        """
        
        try:
            # Create quote batch for tracking
            batch_id = await self._create_quote_batch(quote_request_data)
            
            # Get all active providers for the route
            providers = await self._get_active_providers(quote_request_data)
            
            if not providers:
                return {
                    'batch_id': batch_id,
                    'quotes': [],
                    'message': 'No active providers found for this route',
                    'total_providers': 0,
                    'responded_providers': 0
                }
            
            # Send parallel requests to all providers
            quote_tasks = []
            for provider in providers:
                task = self._request_provider_quote(provider, quote_request_data, batch_id)
                quote_tasks.append(task)
            
            # Wait for all responses with timeout
            start_time = time.time()
            quotes = await asyncio.gather(*quote_tasks, return_exceptions=True)
            response_time = time.time() - start_time
            
            # Process and normalize responses
            normalized_quotes = await self._normalize_quote_responses(quotes, providers)
            
            # Update batch with results
            await self._update_quote_batch(batch_id, normalized_quotes, response_time)
            
            # Sort quotes by price (default) - can be changed to other criteria
            sorted_quotes = sorted(
                normalized_quotes, 
                key=lambda x: x.get('total_price', float('inf'))
            )
            
            return {
                'batch_id': batch_id,
                'quotes': sorted_quotes,
                'total_providers': len(providers),
                'responded_providers': len([q for q in normalized_quotes if q.get('status') == 'success']),
                'response_time_seconds': round(response_time, 2),
                'timestamp': datetime.now().isoformat(),
                'market_insights': await self._generate_market_insights(sorted_quotes)
            }
            
        except Exception as e:
            logger.error(f"Quote aggregation failed: {str(e)}")
            return {
                'batch_id': None,
                'quotes': [],
                'error': str(e),
                'total_providers': 0,
                'responded_providers': 0
            }
    
    async def _create_quote_batch(self, request_data: Dict) -> str:
        """Create a new quote batch for tracking purposes"""
        from shipments.models import QuoteBatch
        
        @sync_to_async
        def create_batch():
            batch = QuoteBatch.objects.create(
                customer_id=request_data.get('customer_id'),
                origin_country=request_data.get('origin_country'),
                origin_city=request_data.get('origin_city'),
                destination_country=request_data.get('destination_country'),
                destination_city=request_data.get('destination_city'),
                cargo_weight=request_data.get('weight_kg'),
                cargo_type=request_data.get('cargo_type'),
                transport_mode=request_data.get('transport_type'),
                urgency=request_data.get('urgency', 'standard'),
                status='processing',
                created_at=timezone.now()
            )
            return f"QB{batch.id:08d}"
        
        return await create_batch()
    
    async def _get_active_providers(self, request_data: Dict) -> List[Dict]:
        """Get all active logistics providers that can serve the requested route"""
        from core.models import User
        from shipments.models import ShippingRate, Route
        
        @sync_to_async
        def get_providers():
            try:
                # Find route or similar routes
                origin_country = request_data.get('origin_country')
                destination_country = request_data.get('destination_country')
                transport_type = request_data.get('transport_type', 'truck')
                
                # Get providers with rates for this route or similar routes
                providers = User.objects.filter(
                    user_type='LOGISTICS',
                    is_active=True,
                    shippingrate__route__origin_country__icontains=origin_country,
                    shippingrate__route__destination_country__icontains=destination_country,
                    shippingrate__transport_type__type=transport_type,
                    shippingrate__is_active=True
                ).distinct().values(
                    'id', 'company_name', 'email', 'phone_number',
                    'logistics_provider_profile__rating',
                    'logistics_provider_profile__total_shipments'
                )
                
                provider_list = []
                for provider in providers:
                    # Get best rate for this provider
                    best_rate = ShippingRate.objects.filter(
                        logistics_provider_id=provider['id'],
                        route__origin_country__icontains=origin_country,
                        route__destination_country__icontains=destination_country,
                        transport_type__type=transport_type,
                        is_active=True
                    ).order_by('base_price').first()
                    
                    if best_rate:
                        provider_list.append({
                            'id': provider['id'],
                            'company_name': provider['company_name'],
                            'email': provider['email'],
                            'phone_number': provider['phone_number'],
                            'rating': provider.get('logistics_provider_profile__rating', 0),
                            'total_shipments': provider.get('logistics_provider_profile__total_shipments', 0),
                            'base_rate': float(best_rate.base_price),
                            'price_per_kg': float(best_rate.price_per_kg),
                            'estimated_days': best_rate.estimated_days,
                            'rate_id': best_rate.id
                        })
                
                return provider_list
                
            except Exception as e:
                logger.error(f"Error getting providers: {str(e)}")
                return []
        
        return await get_providers()
    
    async def _request_provider_quote(self, provider: Dict, request_data: Dict, batch_id: str) -> Dict:
        """
        Request quote from individual provider
        In real implementation, this would make API calls to provider systems
        """
        try:
            # Simulate API call delay (in real implementation, this would be actual API calls)
            await asyncio.sleep(0.5)  # Remove in production
            
            # Calculate pricing using provider's rates
            weight_kg = float(request_data.get('weight_kg', 100))
            urgency = request_data.get('urgency', 'standard')
            cargo_type = request_data.get('cargo_type', 'general')
            
            # Base calculation
            base_cost = provider['base_rate']
            weight_cost = weight_kg * provider['price_per_kg']
            
            # Apply urgency multiplier
            urgency_multipliers = {
                'standard': 1.0,
                'express': 1.3,
                'urgent': 1.6
            }
            urgency_multiplier = urgency_multipliers.get(urgency, 1.0)
            
            # Apply cargo type risk factor
            cargo_risk_factors = {
                'general': 1.0,
                'fragile': 1.2,
                'hazardous': 1.5,
                'refrigerated': 1.4,
                'oversized': 1.3
            }
            cargo_factor = cargo_risk_factors.get(cargo_type, 1.0)
            
            # Calculate total
            subtotal = (base_cost + weight_cost) * urgency_multiplier * cargo_factor
            
            # Add fuel surcharge and fees
            fuel_surcharge = subtotal * 0.08  # 8% fuel surcharge
            handling_fee = 50.0
            total_price = subtotal + fuel_surcharge + handling_fee
            
            # Estimated delivery days with urgency adjustment
            base_days = provider['estimated_days']
            if urgency == 'express':
                delivery_days = max(1, base_days - 2)
            elif urgency == 'urgent':
                delivery_days = max(1, base_days - 4)
            else:
                delivery_days = base_days
            
            return {
                'provider_id': provider['id'],
                'provider_name': provider['company_name'],
                'provider_rating': provider['rating'],
                'provider_experience': provider['total_shipments'],
                'total_price': round(total_price, 2),
                'breakdown': {
                    'base_cost': round(base_cost, 2),
                    'weight_cost': round(weight_cost, 2),
                    'fuel_surcharge': round(fuel_surcharge, 2),
                    'handling_fee': round(handling_fee, 2),
                    'urgency_premium': round((subtotal * (urgency_multiplier - 1)), 2),
                    'cargo_premium': round((subtotal * (cargo_factor - 1)), 2)
                },
                'estimated_delivery_days': delivery_days,
                'pickup_availability': self._calculate_pickup_availability(),
                'service_features': self._get_service_features(provider),
                'status': 'success',
                'response_time': 0.5,
                'quote_valid_until': (datetime.now() + timedelta(hours=24)).isoformat(),
                'rate_id': provider['rate_id']
            }
            
        except Exception as e:
            logger.error(f"Provider quote request failed for {provider.get('company_name', 'Unknown')}: {str(e)}")
            return {
                'provider_id': provider.get('id'),
                'provider_name': provider.get('company_name', 'Unknown Provider'),
                'status': 'error',
                'error_message': str(e),
                'total_price': None
            }
    
    def _calculate_pickup_availability(self) -> List[str]:
        """Calculate available pickup dates"""
        available_dates = []
        for i in range(1, 8):  # Next 7 days
            date = (datetime.now() + timedelta(days=i)).strftime('%Y-%m-%d')
            available_dates.append(date)
        return available_dates
    
    def _get_service_features(self, provider: Dict) -> List[str]:
        """Get provider service features based on their profile"""
        features = ['Real-time tracking', 'Insurance coverage']
        
        if provider['rating'] >= 4.5:
            features.append('Premium service')
        if provider['total_shipments'] > 100:
            features.append('Experienced provider')
        if provider['rating'] >= 4.0:
            features.append('Customer protection')
            
        return features
    
    async def _normalize_quote_responses(self, quotes: List, providers: List[Dict]) -> List[Dict]:
        """Normalize all quote responses into standard format"""
        normalized = []
        
        for i, quote in enumerate(quotes):
            if isinstance(quote, Exception):
                # Handle failed requests
                provider = providers[i] if i < len(providers) else {}
                normalized.append({
                    'provider_id': provider.get('id'),
                    'provider_name': provider.get('company_name', 'Unknown'),
                    'status': 'error',
                    'error_message': str(quote),
                    'total_price': None
                })
            elif isinstance(quote, dict) and quote.get('status') == 'success':
                normalized.append(quote)
            else:
                # Handle unexpected response format
                provider = providers[i] if i < len(providers) else {}
                normalized.append({
                    'provider_id': provider.get('id'),
                    'provider_name': provider.get('company_name', 'Unknown'),
                    'status': 'error',
                    'error_message': 'Invalid response format',
                    'total_price': None
                })
        
        return normalized
    
    async def _update_quote_batch(self, batch_id: str, quotes: List[Dict], response_time: float):
        """Update quote batch with final results"""
        from shipments.models import QuoteBatch
        
        @sync_to_async
        def update_batch():
            try:
                batch_id_num = int(batch_id.replace('QB', ''))
                batch = QuoteBatch.objects.get(id=batch_id_num)
                batch.total_quotes = len(quotes)
                batch.successful_quotes = len([q for q in quotes if q.get('status') == 'success'])
                batch.response_time_seconds = response_time
                batch.status = 'completed'
                batch.completed_at = timezone.now()
                batch.quote_data = json.dumps(quotes)
                batch.save()
            except Exception as e:
                logger.error(f"Failed to update quote batch {batch_id}: {str(e)}")
        
        await update_batch()
    
    async def _generate_market_insights(self, quotes: List[Dict]) -> Dict:
        """Generate market insights from quote responses - Freightos-style analytics"""
        successful_quotes = [q for q in quotes if q.get('status') == 'success' and q.get('total_price')]
        
        if not successful_quotes:
            return {
                'average_price': None,
                'price_range': None,
                'recommendations': ['No quotes available for comparison']
            }
        
        prices = [q['total_price'] for q in successful_quotes]
        
        insights = {
            'average_price': round(sum(prices) / len(prices), 2),
            'lowest_price': min(prices),
            'highest_price': max(prices),
            'price_range': {
                'min': min(prices),
                'max': max(prices),
                'spread_percentage': round(((max(prices) - min(prices)) / min(prices)) * 100, 1)
            },
            'total_options': len(successful_quotes),
            'recommendations': []
        }
        
        # Generate recommendations
        if len(successful_quotes) >= 3:
            insights['recommendations'].append('Good market competition with multiple options')
        if insights['price_range']['spread_percentage'] > 20:
            insights['recommendations'].append('Significant price variation - compare service levels')
        
        # Find best value option
        best_value = min(successful_quotes, key=lambda x: x['total_price'] / (x.get('provider_rating', 3) + 1))
        insights['best_value_provider'] = best_value['provider_name']
        
        return insights

class QuoteComparison:
    """
    Advanced quote comparison engine for side-by-side analysis
    """
    
    @staticmethod
    def compare_quotes(quotes: List[Dict], sort_by: str = 'price') -> Dict:
        """Compare quotes with advanced sorting and filtering"""
        
        valid_quotes = [q for q in quotes if q.get('status') == 'success' and q.get('total_price')]
        
        if not valid_quotes:
            return {
                'sorted_quotes': [],
                'comparison_matrix': {},
                'recommendations': []
            }
        
        # Sort quotes based on criteria
        if sort_by == 'price':
            sorted_quotes = sorted(valid_quotes, key=lambda x: x['total_price'])
        elif sort_by == 'delivery_time':
            sorted_quotes = sorted(valid_quotes, key=lambda x: x.get('estimated_delivery_days', 999))
        elif sort_by == 'rating':
            sorted_quotes = sorted(valid_quotes, key=lambda x: x.get('provider_rating', 0), reverse=True)
        elif sort_by == 'value':
            # Combined score: price vs rating vs delivery time
            def value_score(quote):
                price_score = 1 / (quote['total_price'] / 1000)  # Lower price = higher score
                rating_score = quote.get('provider_rating', 3)
                time_score = 1 / (quote.get('estimated_delivery_days', 7) / 7)
                return (price_score + rating_score + time_score) / 3
            
            sorted_quotes = sorted(valid_quotes, key=value_score, reverse=True)
        else:
            sorted_quotes = valid_quotes
        
        # Generate comparison matrix
        comparison_matrix = QuoteComparison._generate_comparison_matrix(sorted_quotes)
        
        # Generate recommendations
        recommendations = QuoteComparison._generate_recommendations(sorted_quotes)
        
        return {
            'sorted_quotes': sorted_quotes,
            'comparison_matrix': comparison_matrix,
            'recommendations': recommendations,
            'sort_criteria': sort_by
        }
    
    @staticmethod
    def _generate_comparison_matrix(quotes: List[Dict]) -> Dict:
        """Generate detailed comparison matrix"""
        if not quotes:
            return {}
        
        matrix = {
            'price_comparison': {
                'cheapest': min(quotes, key=lambda x: x['total_price']),
                'most_expensive': max(quotes, key=lambda x: x['total_price']),
                'average': sum(q['total_price'] for q in quotes) / len(quotes)
            },
            'delivery_comparison': {
                'fastest': min(quotes, key=lambda x: x.get('estimated_delivery_days', 999)),
                'slowest': max(quotes, key=lambda x: x.get('estimated_delivery_days', 0))
            },
            'rating_comparison': {
                'highest_rated': max(quotes, key=lambda x: x.get('provider_rating', 0)),
                'lowest_rated': min(quotes, key=lambda x: x.get('provider_rating', 5))
            }
        }
        
        return matrix
    
    @staticmethod
    def _generate_recommendations(quotes: List[Dict]) -> List[str]:
        """Generate intelligent recommendations based on quote analysis"""
        recommendations = []
        
        if not quotes:
            return recommendations
        
        # Price recommendations
        prices = [q['total_price'] for q in quotes]
        price_range = max(prices) - min(prices)
        if price_range > 500:
            recommendations.append("Significant price difference detected - review service levels carefully")
        
        # Rating recommendations
        high_rated = [q for q in quotes if q.get('provider_rating', 0) >= 4.5]
        if high_rated:
            cheapest_high_rated = min(high_rated, key=lambda x: x['total_price'])
            recommendations.append(f"Best rated option: {cheapest_high_rated['provider_name']} at ${cheapest_high_rated['total_price']}")
        
        # Delivery time recommendations
        fast_delivery = [q for q in quotes if q.get('estimated_delivery_days', 999) <= 3]
        if fast_delivery:
            cheapest_fast = min(fast_delivery, key=lambda x: x['total_price'])
            recommendations.append(f"Fastest delivery: {cheapest_fast['provider_name']} in {cheapest_fast['estimated_delivery_days']} days")
        
        return recommendations