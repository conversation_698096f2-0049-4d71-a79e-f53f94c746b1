import os
import json
import uuid
import datetime
from decimal import Decimal
import django
import secrets
import base64
from django.utils import timezone

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'cloverics_django.cloverics.settings')
django.setup()

from payments.models import Payment, UserSubscription, LogisticsCompanySettings
from core.models import User
from shipments.models import Shipment
from core.notification_models import Notification

# Check if Stripe API key is available
STRIPE_AVAILABLE = False
try:
    import stripe
    stripe_key = os.environ.get('STRIPE_SECRET_KEY')
    if stripe_key:
        stripe.api_key = stripe_key
        STRIPE_AVAILABLE = True
except (ImportError, Exception):
    STRIPE_AVAILABLE = False

# Constants
PLATFORM_COMMISSION_RATE = Decimal('0.001')  # 0.1%
INSURANCE_COMMISSION_RATE = Decimal('0.01')  # 1%
BANK_ACCOUNT_DETAILS = {
    'bank_name': 'LogistiLink Global Bank',
    'account_name': 'LogistiLink Ltd.',
    'account_number': '**********',
    'routing_number': '*********',
    'swift_code': 'LGBANKXX',
    'iban': '********************',
    'reference_prefix': 'LGL-'
}


def generate_unique_id(prefix=''):
    """Generate a unique ID with a prefix"""
    unique_id = secrets.token_hex(6).upper()
    timestamp = int(datetime.datetime.now().timestamp())
    return f"{prefix}{timestamp}-{unique_id}"


def generate_invoice_number():
    """Generate a unique invoice number"""
    return generate_unique_id('INV-')


def generate_transaction_id():
    """Generate a unique transaction ID"""
    return generate_unique_id('TXN-')


def calculate_platform_fee(amount):
    """Calculate platform fee as 0.1% of the amount"""
    return Decimal(amount) * PLATFORM_COMMISSION_RATE


def calculate_insurance_commission(amount):
    """Calculate insurance commission as 1% of the amount"""
    return Decimal(amount) * INSURANCE_COMMISSION_RATE


def create_payment(
    payer_id, 
    payee_id, 
    amount, 
    payment_type, 
    payment_method='BANK_TRANSFER', 
    shipment_id=None, 
    status='PENDING',
    currency='USD',
    note='',
    bank_details=None
):
    """Create a payment record"""
    try:
        # Generate invoice number and transaction ID
        invoice_number = generate_invoice_number()
        transaction_id = generate_transaction_id() if payment_method != 'BANK_TRANSFER' else None
        
        # Calculate platform fee
        platform_fee = calculate_platform_fee(amount)
        
        # Calculate insurance commission if applicable
        insurance_commission = Decimal('0')
        if payment_type == 'INSURANCE':
            insurance_commission = calculate_insurance_commission(amount)
        
        # Validate shipment_id for shipment-related payments
        if payment_type == 'SHIPMENT' and shipment_id is None:
            raise ValueError("Shipment ID is required for shipment payments")
            
        # Create payment record
        payment = Payment(
            shipment_id=shipment_id,
            payer_id=payer_id,
            payee_id=payee_id,
            amount=amount,
            currency=currency,
            payment_type=payment_type,
            payment_method=payment_method,
            transaction_id=transaction_id,
            platform_fee=platform_fee,
            insurance_commission=insurance_commission,
            status=status,
            invoice_number=invoice_number,
            note=note,
            bank_details=bank_details or {}
        )
        
        # If it's a bank transfer, set status to awaiting proof
        if payment_method == 'BANK_TRANSFER' and status == 'PENDING':
            payment.status = 'AWAITING_PROOF'
            payment.bank_details = BANK_ACCOUNT_DETAILS.copy()
            payment.bank_details['reference'] = f"{BANK_ACCOUNT_DETAILS['reference_prefix']}{invoice_number}"
        
        payment.save()
        
        # Create notification for the payer
        notification_title = f"New Payment: {invoice_number}"
        notification_message = f"A new payment of {currency} {amount} has been created. Please complete the payment."
        
        Notification.objects.create(
            user_id=payer_id,
            title=notification_title,
            message=notification_message,
            notification_type='PAYMENT',
            priority='HIGH',
            related_object_type='payment',
            related_object_id=str(payment.id)
        )
        
        # Create notification for the payee
        payee_notification_title = f"Upcoming Payment: {invoice_number}"
        payee_notification_message = f"A new payment of {currency} {amount} is pending from a customer."
        
        Notification.objects.create(
            user_id=payee_id,
            title=payee_notification_title,
            message=payee_notification_message,
            notification_type='PAYMENT',
            priority='MEDIUM',
            related_object_type='payment',
            related_object_id=str(payment.id)
        )
        
        return payment
    
    except Exception as e:
        print(f"Error creating payment: {str(e)}")
        return None


def process_bank_transfer_proof(payment_id, proof_file_path, user_id):
    """Process uploaded payment proof for bank transfer"""
    try:
        payment = Payment.objects.get(id=payment_id)
        payment.payment_proof = proof_file_path
        payment.status = 'PROOF_SUBMITTED'
        payment.proof_submitted_date = timezone.now()
        payment.save()
        
        # Create notification for admin/payee to verify the proof
        Notification.objects.create(
            user_id=payment.payee_id,
            title=f"Payment Proof Submitted: {payment.invoice_number}",
            message=f"A payment proof has been submitted for invoice {payment.invoice_number}. Please verify the payment.",
            notification_type='PAYMENT',
            priority='HIGH',
            related_object_type='payment',
            related_object_id=str(payment.id)
        )
        
        # Create notification for payer
        Notification.objects.create(
            user_id=payment.payer_id,
            title=f"Payment Proof Received: {payment.invoice_number}",
            message=f"Your payment proof for invoice {payment.invoice_number} has been received and is under review.",
            notification_type='PAYMENT',
            priority='MEDIUM',
            related_object_type='payment',
            related_object_id=str(payment.id)
        )
        
        return True
    
    except Exception as e:
        print(f"Error processing payment proof: {str(e)}")
        return False


def verify_bank_transfer_payment(payment_id, verified=True, verification_notes='', verified_by_id=None):
    """Verify a bank transfer payment"""
    try:
        payment = Payment.objects.get(id=payment_id)
        
        if verified:
            payment.status = 'COMPLETED'
            payment.payment_date = timezone.now()
            payment.proof_verified_date = timezone.now()
            
            # Notify payer
            Notification.objects.create(
                user_id=payment.payer_id,
                title=f"Payment Verified: {payment.invoice_number}",
                message=f"Your payment for invoice {payment.invoice_number} has been verified and marked as completed.",
                notification_type='PAYMENT',
                priority='HIGH',
                related_object_type='payment',
                related_object_id=str(payment.id)
            )
            
            # Notify payee
            Notification.objects.create(
                user_id=payment.payee_id,
                title=f"Payment Completed: {payment.invoice_number}",
                message=f"Payment for invoice {payment.invoice_number} has been verified and completed.",
                notification_type='PAYMENT',
                priority='HIGH',
                related_object_type='payment',
                related_object_id=str(payment.id)
            )
            
            # Update shipment status if applicable
            if payment.shipment:
                if payment.payment_type == 'SHIPMENT':
                    payment.shipment.payment_status = 'PAID'
                    payment.shipment.save()
        else:
            payment.status = 'PROOF_REJECTED'
            
            # Notify payer
            Notification.objects.create(
                user_id=payment.payer_id,
                title=f"Payment Proof Rejected: {payment.invoice_number}",
                message=f"Your payment proof for invoice {payment.invoice_number} has been rejected. Reason: {verification_notes}",
                notification_type='PAYMENT',
                priority='HIGH',
                related_object_type='payment',
                related_object_id=str(payment.id)
            )
        
        payment.note = payment.note + "\n" + verification_notes if payment.note else verification_notes
        
        if verified_by_id:
            payment.verified_by_id = verified_by_id
            
        payment.save()
        return True
    
    except Exception as e:
        print(f"Error verifying payment: {str(e)}")
        return False


def handle_logistics_commission(shipment_id, payment_id=None):
    """Calculate and process logistics company commission for a shipment"""
    try:
        shipment = Shipment.objects.get(id=shipment_id)
        logistics_company = shipment.logistics_provider
        
        # Get logistics company settings
        try:
            lc_settings = LogisticsCompanySettings.objects.get(logistics_company=logistics_company)
        except LogisticsCompanySettings.DoesNotExist:
            # Create default settings
            lc_settings = LogisticsCompanySettings.objects.create(
                logistics_company=logistics_company,
                commission_model='PER_ORDER',
                deposit_amount=Decimal('0')
            )
        
        # Calculate commission amount (0.1% of shipment price)
        commission_amount = calculate_platform_fee(shipment.total_price)
        
        # Check if the logistics company has to pay deposit and hasn't yet
        if not lc_settings.deposit_paid:
            deposit_required = 10 if lc_settings.commission_model == 'PER_ORDER' else 100
            lc_settings.deposit_amount = Decimal(deposit_required)
            lc_settings.save()
            
            # Create notification about required deposit
            Notification.objects.create(
                user=logistics_company,
                title="Security Deposit Required",
                message=f"A security deposit of ${deposit_required} is required to process commissions for your shipments. "
                        f"This is a one-time deposit for {lc_settings.get_commission_model_display()}.",
                notification_type='PAYMENT',
                priority='HIGH'
            )
        
        # Process commission based on commission model
        if lc_settings.commission_model == 'PER_ORDER':
            # Per-order automatic payment via card
            if lc_settings.has_payment_method:
                # Create commission payment
                commission_payment = create_payment(
                    payer_id=logistics_company.id,
                    payee_id=User.objects.filter(user_type='ADMIN').first().id,  # Platform admin
                    amount=commission_amount,
                    payment_type='COMMISSION',
                    payment_method='CREDIT_CARD',
                    shipment_id=shipment_id,
                    status='PROCESSING',
                    note=f"Commission for shipment {shipment.tracking_number}"
                )
                
                # Process card payment via Stripe if available
                if STRIPE_AVAILABLE and commission_payment:
                    # TODO: Implement Stripe payment processing
                    commission_payment.status = 'COMPLETED'
                    commission_payment.payment_date = timezone.now()
                    commission_payment.save()
            else:
                # No payment method available, create pending payment
                commission_payment = create_payment(
                    payer_id=logistics_company.id,
                    payee_id=User.objects.filter(user_type='ADMIN').first().id,  # Platform admin
                    amount=commission_amount,
                    payment_type='COMMISSION',
                    payment_method='BANK_TRANSFER',
                    shipment_id=shipment_id,
                    status='AWAITING_PROOF',
                    note=f"Commission for shipment {shipment.tracking_number}"
                )
        
        return True
    
    except Exception as e:
        print(f"Error handling logistics commission: {str(e)}")
        return False


def create_or_update_subscription(user_id, plan_type='BASIC', auto_renew=False, payment_method_id=None):
    """Create or update a user subscription"""
    try:
        user = User.objects.get(id=user_id)
        
        # Check if user already has a subscription
        try:
            subscription = UserSubscription.objects.get(user=user)
            
            # Update existing subscription
            subscription.plan_type = plan_type
            subscription.auto_renew = auto_renew
            
            if payment_method_id:
                subscription.payment_method_id = payment_method_id
            
            # If upgrading from BASIC or changing from expired/cancelled
            if subscription.plan_type != plan_type or subscription.status in ['EXPIRED', 'CANCELLED']:
                subscription.status = 'ACTIVE'
                subscription.subscription_start_date = timezone.now()
                
                # Set end date 1 month from now
                subscription.subscription_end_date = subscription.subscription_start_date + datetime.timedelta(days=30)
            
            subscription.save()
            
        except UserSubscription.DoesNotExist:
            # Create new subscription
            # All users start with 3-month free trial
            subscription = UserSubscription.objects.create(
                user=user,
                plan_type=plan_type,
                status='TRIAL',
                auto_renew=auto_renew,
                payment_method_id=payment_method_id,
                trial_start_date=timezone.now(),
                trial_end_date=timezone.now() + datetime.timedelta(days=90)  # 3 months trial
            )
            
            # Create notification
            Notification.objects.create(
                user=user,
                title="Welcome to Your Free Trial!",
                message="You've been enrolled in a 3-month free trial of our Premium Features. "
                        "Explore advanced analytics, priority visibility, and extended document management.",
                notification_type='SYSTEM',
                priority='HIGH'
            )
        
        return subscription
    
    except Exception as e:
        print(f"Error creating/updating subscription: {str(e)}")
        return None


def check_subscription_status(user_id):
    """Check and update a user's subscription status"""
    try:
        subscription = UserSubscription.objects.get(user_id=user_id)
        
        # Check if trial has expired
        if subscription.status == 'TRIAL' and subscription.trial_end_date and subscription.trial_end_date < timezone.now():
            subscription.status = 'EXPIRED'
            subscription.save()
            
            # Create notification
            Notification.objects.create(
                user_id=user_id,
                title="Your Free Trial Has Ended",
                message="Your 3-month free trial of premium features has ended. "
                        "Upgrade now to continue enjoying premium benefits.",
                notification_type='SYSTEM',
                priority='HIGH'
            )
        
        # Check if paid subscription has expired
        elif subscription.status == 'ACTIVE' and subscription.subscription_end_date and subscription.subscription_end_date < timezone.now():
            # Check if auto-renew is enabled and process renewal
            if subscription.auto_renew and subscription.payment_method_id:
                # TODO: Process automatic renewal
                # For now, just extend by 30 days
                subscription.subscription_end_date = timezone.now() + datetime.timedelta(days=30)
                subscription.save()
                
                # Create notification
                Notification.objects.create(
                    user_id=user_id,
                    title="Subscription Renewed",
                    message=f"Your {subscription.get_plan_type_display()} subscription has been automatically renewed.",
                    notification_type='SYSTEM',
                    priority='MEDIUM'
                )
            else:
                subscription.status = 'EXPIRED'
                subscription.save()
                
                # Create notification
                Notification.objects.create(
                    user_id=user_id,
                    title="Your Subscription Has Expired",
                    message=f"Your {subscription.get_plan_type_display()} subscription has expired. "
                            f"Renew now to continue enjoying premium benefits.",
                    notification_type='SYSTEM',
                    priority='HIGH'
                )
        
        return subscription.status
    
    except UserSubscription.DoesNotExist:
        # Create a new trial subscription
        create_or_update_subscription(user_id)
        return 'TRIAL'
    
    except Exception as e:
        print(f"Error checking subscription status: {str(e)}")
        return None


def check_upcoming_subscription_expirations():
    """Check for subscriptions expiring in the next 7 days and send notifications"""
    try:
        now = timezone.now()
        week_from_now = now + datetime.timedelta(days=7)
        
        # Find trial subscriptions expiring in the next week
        ending_trials = UserSubscription.objects.filter(
            status='TRIAL',
            trial_end_date__lt=week_from_now,
            trial_end_date__gt=now
        )
        
        for subscription in ending_trials:
            days_left = (subscription.trial_end_date - now).days
            
            # Create notification
            Notification.objects.create(
                user=subscription.user,
                title=f"Trial Ending in {days_left} Days",
                message=f"Your free trial of premium features will end in {days_left} days. "
                        f"Upgrade now to continue enjoying premium benefits without interruption.",
                notification_type='SYSTEM',
                priority='MEDIUM'
            )
        
        # Find paid subscriptions expiring in the next week
        ending_subscriptions = UserSubscription.objects.filter(
            status='ACTIVE',
            subscription_end_date__lt=week_from_now,
            subscription_end_date__gt=now,
            auto_renew=False  # Only notify those without auto-renew
        )
        
        for subscription in ending_subscriptions:
            days_left = (subscription.subscription_end_date - now).days
            
            # Create notification
            Notification.objects.create(
                user=subscription.user,
                title=f"Subscription Ending in {days_left} Days",
                message=f"Your {subscription.get_plan_type_display()} subscription will end in {days_left} days. "
                        f"Renew now or enable auto-renewal to avoid interruption.",
                notification_type='SYSTEM',
                priority='MEDIUM'
            )
        
        return len(ending_trials) + len(ending_subscriptions)
    
    except Exception as e:
        print(f"Error checking upcoming expirations: {str(e)}")
        return 0


# Stripe Integration Functions
def create_stripe_checkout_session(payment_id, success_url, cancel_url):
    """Create a Stripe checkout session for a payment"""
    if not STRIPE_AVAILABLE:
        return None
    
    try:
        payment = Payment.objects.get(id=payment_id)
        
        # Convert amount to cents
        amount_cents = int(payment.amount * 100)
        
        # Create Stripe checkout session
        checkout_session = stripe.checkout.Session.create(
            payment_method_types=['card'],
            line_items=[
                {
                    'price_data': {
                        'currency': payment.currency.lower(),
                        'unit_amount': amount_cents,
                        'product_data': {
                            'name': f"Payment for {payment.get_payment_type_display()}",
                            'description': f"Invoice: {payment.invoice_number}",
                        },
                    },
                    'quantity': 1,
                },
            ],
            metadata={
                'payment_id': str(payment.id),
                'invoice_number': payment.invoice_number,
                'payment_type': payment.payment_type,
            },
            mode='payment',
            success_url=success_url,
            cancel_url=cancel_url,
        )
        
        # Update payment record
        payment.stripe_checkout_session_id = checkout_session.id
        payment.save()
        
        return checkout_session
    
    except Exception as e:
        print(f"Error creating Stripe checkout session: {str(e)}")
        return None


def handle_stripe_webhook_event(event_json):
    """Process Stripe webhook events"""
    if not STRIPE_AVAILABLE:
        return None
    
    try:
        event = json.loads(event_json)
        event_type = event['type']
        
        if event_type == 'checkout.session.completed':
            session = event['data']['object']
            
            # Get payment ID from metadata
            payment_id = session.get('metadata', {}).get('payment_id')
            
            if payment_id:
                try:
                    payment = Payment.objects.get(id=payment_id)
                    payment.status = 'COMPLETED'
                    payment.payment_date = timezone.now()
                    payment.transaction_id = session.get('payment_intent')
                    payment.save()
                    
                    # Update shipment status if applicable
                    if payment.shipment and payment.payment_type == 'SHIPMENT':
                        payment.shipment.payment_status = 'PAID'
                        payment.shipment.save()
                    
                    # Create notifications
                    Notification.objects.create(
                        user_id=payment.payer_id,
                        title="Payment Successful",
                        message=f"Your payment for invoice {payment.invoice_number} has been successfully processed.",
                        notification_type='PAYMENT',
                        priority='HIGH',
                        related_object_type='payment',
                        related_object_id=payment_id
                    )
                    
                    Notification.objects.create(
                        user_id=payment.payee_id,
                        title="Payment Received",
                        message=f"Payment for invoice {payment.invoice_number} has been successfully received.",
                        notification_type='PAYMENT',
                        priority='HIGH',
                        related_object_type='payment',
                        related_object_id=payment_id
                    )
                    
                    return payment
                
                except Payment.DoesNotExist:
                    print(f"Payment with ID {payment_id} not found")
                    return None
        
        elif event_type == 'payment_intent.payment_failed':
            payment_intent = event['data']['object']
            
            # Search for payment with this payment intent
            try:
                payment = Payment.objects.get(stripe_payment_intent_id=payment_intent.get('id'))
                payment.status = 'FAILED'
                payment.note = payment.note + "\n" + f"Payment failed: {payment_intent.get('last_payment_error', {}).get('message', 'Unknown error')}"
                payment.save()
                
                # Create notification
                Notification.objects.create(
                    user_id=payment.payer_id,
                    title="Payment Failed",
                    message=f"Your payment for invoice {payment.invoice_number} has failed. "
                            f"Reason: {payment_intent.get('last_payment_error', {}).get('message', 'Unknown error')}",
                    notification_type='PAYMENT',
                    priority='HIGH',
                    related_object_type='payment',
                    related_object_id=str(payment.id)
                )
                
                return payment
            
            except Payment.DoesNotExist:
                print(f"Payment with intent ID {payment_intent.get('id')} not found")
                return None
        
        return None
    
    except Exception as e:
        print(f"Error handling Stripe webhook event: {str(e)}")
        return None