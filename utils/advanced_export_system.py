"""
Advanced Export System for Cloverics Platform
Comprehensive export capabilities supporting PDF, Excel, CSV, and custom formats
with enterprise-grade formatting and data visualization
"""

import io
import json
import csv
import xlsxwriter
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass
from enum import Enum
import base64
import zipfile
import tempfile
import os

from reportlab.lib import colors
from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, Image
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.graphics.shapes import Drawing
from reportlab.graphics.charts.lineplots import LinePlot
from reportlab.graphics.charts.barcharts import VerticalBarChart
from reportlab.graphics.charts.piecharts import Pie
from reportlab.graphics.widgets.markers import makeMarker
from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT

class ExportFormat(Enum):
    PDF = "pdf"
    EXCEL = "excel"
    CSV = "csv"
    JSON = "json"
    XML = "xml"
    ZIP = "zip"

class ExportType(Enum):
    SHIPMENT_REPORT = "shipment_report"
    ANALYTICS_DASHBOARD = "analytics_dashboard"
    INVOICE_RECONCILIATION = "invoice_reconciliation"
    PERFORMANCE_SCORECARD = "performance_scorecard"
    CONTRACT_PACKAGE = "contract_package"
    COMPLIANCE_AUDIT = "compliance_audit"
    MARKET_INTELLIGENCE = "market_intelligence"
    CUSTOMS_DOCUMENTATION = "customs_documentation"

@dataclass
class ExportConfiguration:
    """Configuration for export operations"""
    format: ExportFormat
    export_type: ExportType
    include_charts: bool = True
    include_summary: bool = True
    include_details: bool = True
    date_range: Optional[tuple] = None
    filters: Optional[Dict[str, Any]] = None
    branding: bool = True
    page_orientation: str = "portrait"
    chart_style: str = "professional"
    color_scheme: str = "cloverics"

class AdvancedExportEngine:
    """Advanced export engine with comprehensive formatting capabilities"""
    
    def __init__(self):
        self.color_schemes = {
            'cloverics': {
                'primary': colors.HexColor('#2E7D32'),
                'secondary': colors.HexColor('#388E3C'),
                'accent': colors.HexColor('#66BB6A'),
                'text': colors.HexColor('#1B5E20'),
                'background': colors.HexColor('#F1F8E9')
            },
            'professional': {
                'primary': colors.HexColor('#1976D2'),
                'secondary': colors.HexColor('#1565C0'),
                'accent': colors.HexColor('#42A5F5'),
                'text': colors.HexColor('#0D47A1'),
                'background': colors.HexColor('#E3F2FD')
            },
            'executive': {
                'primary': colors.HexColor('#424242'),
                'secondary': colors.HexColor('#616161'),
                'accent': colors.HexColor('#9E9E9E'),
                'text': colors.HexColor('#212121'),
                'background': colors.HexColor('#FAFAFA')
            }
        }
        
        self.export_stats = {
            'total_exports': 0,
            'exports_by_type': {},
            'exports_by_format': {},
            'last_export': None
        }
    
    def create_export(self, data: Dict[str, Any], config: ExportConfiguration) -> Dict[str, Any]:
        """Create export based on configuration"""
        try:
            # Update statistics
            self.export_stats['total_exports'] += 1
            self.export_stats['exports_by_type'][config.export_type.value] = \
                self.export_stats['exports_by_type'].get(config.export_type.value, 0) + 1
            self.export_stats['exports_by_format'][config.format.value] = \
                self.export_stats['exports_by_format'].get(config.format.value, 0) + 1
            self.export_stats['last_export'] = datetime.now().isoformat()
            
            # Route to appropriate export method
            if config.format == ExportFormat.PDF:
                return self._create_pdf_export(data, config)
            elif config.format == ExportFormat.EXCEL:
                return self._create_excel_export(data, config)
            elif config.format == ExportFormat.CSV:
                return self._create_csv_export(data, config)
            elif config.format == ExportFormat.JSON:
                return self._create_json_export(data, config)
            elif config.format == ExportFormat.XML:
                return self._create_xml_export(data, config)
            elif config.format == ExportFormat.ZIP:
                return self._create_zip_export(data, config)
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def _create_pdf_export(self, data: Dict[str, Any], config: ExportConfiguration) -> Dict[str, Any]:
        """Create professional PDF export with charts and formatting"""
        buffer = io.BytesIO()
        
        # Create PDF document
        if config.page_orientation == "landscape":
            pagesize = (A4[1], A4[0])
        else:
            pagesize = A4
            
        doc = SimpleDocTemplate(buffer, pagesize=pagesize)
        elements = []
        styles = getSampleStyleSheet()
        
        # Color scheme
        colors_theme = self.color_schemes.get(config.color_scheme, self.color_schemes['cloverics'])
        
        # Title style
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=24,
            spaceAfter=30,
            alignment=TA_CENTER,
            textColor=colors_theme['primary']
        )
        
        # Header
        if config.branding:
            elements.append(Paragraph("Cloverics Platform", title_style))
            elements.append(Paragraph(f"Export Report - {config.export_type.value.replace('_', ' ').title()}", 
                                    styles['Heading2']))
            elements.append(Spacer(1, 12))
        
        # Summary section
        if config.include_summary and 'summary' in data:
            elements.append(Paragraph("Executive Summary", styles['Heading3']))
            summary_data = data['summary']
            
            # Create summary table
            summary_table_data = []
            for key, value in summary_data.items():
                summary_table_data.append([key.replace('_', ' ').title(), str(value)])
            
            summary_table = Table(summary_table_data, colWidths=[3*inch, 2*inch])
            summary_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors_theme['secondary']),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 12),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors_theme['background']),
                ('GRID', (0, 0), (-1, -1), 1, colors_theme['primary'])
            ]))
            elements.append(summary_table)
            elements.append(Spacer(1, 12))
        
        # Charts section
        if config.include_charts and 'charts' in data:
            elements.append(Paragraph("Analytics & Insights", styles['Heading3']))
            
            # Create charts based on data
            chart_data = data['charts']
            for chart_info in chart_data:
                if chart_info['type'] == 'bar':
                    chart = self._create_bar_chart(chart_info, colors_theme)
                elif chart_info['type'] == 'pie':
                    chart = self._create_pie_chart(chart_info, colors_theme)
                elif chart_info['type'] == 'line':
                    chart = self._create_line_chart(chart_info, colors_theme)
                
                if chart:
                    elements.append(chart)
                    elements.append(Spacer(1, 12))
        
        # Details section
        if config.include_details and 'details' in data:
            elements.append(Paragraph("Detailed Data", styles['Heading3']))
            
            # Create detailed data table
            details_data = data['details']
            if isinstance(details_data, list) and details_data:
                headers = list(details_data[0].keys())
                table_data = [headers]
                
                for row in details_data:
                    table_data.append([str(row.get(header, '')) for header in headers])
                
                details_table = Table(table_data)
                details_table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors_theme['primary']),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, 0), 10),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black)
                ]))
                elements.append(details_table)
        
        # Footer
        elements.append(Spacer(1, 20))
        footer_text = f"Generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} | Cloverics Platform"
        elements.append(Paragraph(footer_text, styles['Normal']))
        
        # Build PDF
        doc.build(elements)
        
        # Get PDF content
        pdf_content = buffer.getvalue()
        buffer.close()
        
        return {
            'success': True,
            'content': base64.b64encode(pdf_content).decode(),
            'filename': f"cloverics_export_{config.export_type.value}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf",
            'content_type': 'application/pdf',
            'size': len(pdf_content)
        }
    
    def _create_excel_export(self, data: Dict[str, Any], config: ExportConfiguration) -> Dict[str, Any]:
        """Create comprehensive Excel export with multiple sheets and formatting"""
        buffer = io.BytesIO()
        
        # Create workbook
        workbook = xlsxwriter.Workbook(buffer)
        
        # Define formats
        header_format = workbook.add_format({
            'bold': True,
            'font_size': 14,
            'bg_color': '#2E7D32',
            'font_color': 'white',
            'align': 'center',
            'valign': 'vcenter'
        })
        
        title_format = workbook.add_format({
            'bold': True,
            'font_size': 18,
            'align': 'center',
            'valign': 'vcenter'
        })
        
        data_format = workbook.add_format({
            'font_size': 10,
            'align': 'left',
            'valign': 'vcenter'
        })
        
        # Summary sheet
        if config.include_summary and 'summary' in data:
            summary_sheet = workbook.add_worksheet('Summary')
            summary_sheet.write(0, 0, f"Cloverics Export - {config.export_type.value.replace('_', ' ').title()}", title_format)
            
            row = 2
            summary_data = data['summary']
            summary_sheet.write(row, 0, 'Metric', header_format)
            summary_sheet.write(row, 1, 'Value', header_format)
            row += 1
            
            for key, value in summary_data.items():
                summary_sheet.write(row, 0, key.replace('_', ' ').title(), data_format)
                summary_sheet.write(row, 1, str(value), data_format)
                row += 1
            
            # Auto-fit columns
            summary_sheet.set_column(0, 0, 25)
            summary_sheet.set_column(1, 1, 20)
        
        # Details sheet
        if config.include_details and 'details' in data:
            details_sheet = workbook.add_worksheet('Details')
            details_data = data['details']
            
            if isinstance(details_data, list) and details_data:
                headers = list(details_data[0].keys())
                
                # Write headers
                for col, header in enumerate(headers):
                    details_sheet.write(0, col, header.replace('_', ' ').title(), header_format)
                
                # Write data
                for row, item in enumerate(details_data, 1):
                    for col, header in enumerate(headers):
                        details_sheet.write(row, col, str(item.get(header, '')), data_format)
                
                # Auto-fit columns
                for col in range(len(headers)):
                    details_sheet.set_column(col, col, 15)
        
        # Charts sheet
        if config.include_charts and 'charts' in data:
            charts_sheet = workbook.add_worksheet('Charts')
            chart_data = data['charts']
            
            row = 0
            for chart_info in chart_data:
                charts_sheet.write(row, 0, chart_info.get('title', 'Chart'), title_format)
                row += 2
                
                # Create chart data
                if chart_info['type'] == 'bar':
                    chart = workbook.add_chart({'type': 'column'})
                elif chart_info['type'] == 'pie':
                    chart = workbook.add_chart({'type': 'pie'})
                elif chart_info['type'] == 'line':
                    chart = workbook.add_chart({'type': 'line'})
                
                # Add data to chart (simplified example)
                chart.add_series({
                    'name': chart_info.get('title', 'Data'),
                    'categories': ['A', 'B', 'C'],
                    'values': [1, 2, 3]
                })
                
                chart.set_title({'name': chart_info.get('title', 'Chart')})
                charts_sheet.insert_chart(row, 0, chart)
                row += 15
        
        # Close workbook
        workbook.close()
        
        # Get Excel content
        excel_content = buffer.getvalue()
        buffer.close()
        
        return {
            'success': True,
            'content': base64.b64encode(excel_content).decode(),
            'filename': f"cloverics_export_{config.export_type.value}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx",
            'content_type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'size': len(excel_content)
        }
    
    def _create_csv_export(self, data: Dict[str, Any], config: ExportConfiguration) -> Dict[str, Any]:
        """Create CSV export with proper formatting"""
        buffer = io.StringIO()
        
        # Get details data
        if 'details' in data and isinstance(data['details'], list) and data['details']:
            details_data = data['details']
            headers = list(details_data[0].keys())
            
            writer = csv.DictWriter(buffer, fieldnames=headers)
            writer.writeheader()
            writer.writerows(details_data)
        
        csv_content = buffer.getvalue()
        buffer.close()
        
        return {
            'success': True,
            'content': base64.b64encode(csv_content.encode()).decode(),
            'filename': f"cloverics_export_{config.export_type.value}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
            'content_type': 'text/csv',
            'size': len(csv_content.encode())
        }
    
    def _create_json_export(self, data: Dict[str, Any], config: ExportConfiguration) -> Dict[str, Any]:
        """Create JSON export with metadata"""
        export_data = {
            'metadata': {
                'export_type': config.export_type.value,
                'export_format': config.format.value,
                'generated_at': datetime.now().isoformat(),
                'platform': 'Cloverics',
                'version': '1.0'
            },
            'data': data
        }
        
        json_content = json.dumps(export_data, indent=2, default=str)
        
        return {
            'success': True,
            'content': base64.b64encode(json_content.encode()).decode(),
            'filename': f"cloverics_export_{config.export_type.value}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
            'content_type': 'application/json',
            'size': len(json_content.encode())
        }
    
    def _create_xml_export(self, data: Dict[str, Any], config: ExportConfiguration) -> Dict[str, Any]:
        """Create XML export with proper structure"""
        xml_content = f"""<?xml version="1.0" encoding="UTF-8"?>
<cloverics_export>
    <metadata>
        <export_type>{config.export_type.value}</export_type>
        <export_format>{config.format.value}</export_format>
        <generated_at>{datetime.now().isoformat()}</generated_at>
        <platform>Cloverics</platform>
    </metadata>
    <data>
        {self._dict_to_xml(data)}
    </data>
</cloverics_export>"""
        
        return {
            'success': True,
            'content': base64.b64encode(xml_content.encode()).decode(),
            'filename': f"cloverics_export_{config.export_type.value}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xml",
            'content_type': 'application/xml',
            'size': len(xml_content.encode())
        }
    
    def _create_zip_export(self, data: Dict[str, Any], config: ExportConfiguration) -> Dict[str, Any]:
        """Create ZIP export with multiple formats"""
        buffer = io.BytesIO()
        
        with zipfile.ZipFile(buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
            # Add PDF
            pdf_config = ExportConfiguration(ExportFormat.PDF, config.export_type)
            pdf_result = self._create_pdf_export(data, pdf_config)
            if pdf_result['success']:
                zip_file.writestr(pdf_result['filename'], base64.b64decode(pdf_result['content']))
            
            # Add Excel
            excel_config = ExportConfiguration(ExportFormat.EXCEL, config.export_type)
            excel_result = self._create_excel_export(data, excel_config)
            if excel_result['success']:
                zip_file.writestr(excel_result['filename'], base64.b64decode(excel_result['content']))
            
            # Add CSV
            csv_config = ExportConfiguration(ExportFormat.CSV, config.export_type)
            csv_result = self._create_csv_export(data, csv_config)
            if csv_result['success']:
                zip_file.writestr(csv_result['filename'], base64.b64decode(csv_result['content']))
            
            # Add JSON
            json_config = ExportConfiguration(ExportFormat.JSON, config.export_type)
            json_result = self._create_json_export(data, json_config)
            if json_result['success']:
                zip_file.writestr(json_result['filename'], base64.b64decode(json_result['content']))
        
        zip_content = buffer.getvalue()
        buffer.close()
        
        return {
            'success': True,
            'content': base64.b64encode(zip_content).decode(),
            'filename': f"cloverics_export_package_{config.export_type.value}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.zip",
            'content_type': 'application/zip',
            'size': len(zip_content)
        }
    
    def _create_bar_chart(self, chart_info: Dict, colors_theme: Dict) -> Drawing:
        """Create bar chart for PDF"""
        drawing = Drawing(400, 200)
        chart = VerticalBarChart()
        chart.x = 50
        chart.y = 50
        chart.height = 125
        chart.width = 300
        
        # Sample data (would be replaced with actual chart data)
        chart.data = [[1, 2, 3, 4, 5]]
        chart.categoryAxis.categoryNames = ['A', 'B', 'C', 'D', 'E']
        
        # Styling
        chart.bars[0].fillColor = colors_theme['primary']
        chart.valueAxis.valueMin = 0
        chart.valueAxis.valueMax = 6
        
        drawing.add(chart)
        return drawing
    
    def _create_pie_chart(self, chart_info: Dict, colors_theme: Dict) -> Drawing:
        """Create pie chart for PDF"""
        drawing = Drawing(400, 200)
        chart = Pie()
        chart.x = 150
        chart.y = 65
        chart.width = 100
        chart.height = 100
        
        # Sample data
        chart.data = [1, 2, 3, 4]
        chart.labels = ['A', 'B', 'C', 'D']
        
        # Styling
        chart.slices.strokeWidth = 0.5
        chart.slices[0].fillColor = colors_theme['primary']
        chart.slices[1].fillColor = colors_theme['secondary']
        chart.slices[2].fillColor = colors_theme['accent']
        chart.slices[3].fillColor = colors_theme['background']
        
        drawing.add(chart)
        return drawing
    
    def _create_line_chart(self, chart_info: Dict, colors_theme: Dict) -> Drawing:
        """Create line chart for PDF"""
        drawing = Drawing(400, 200)
        chart = LinePlot()
        chart.x = 50
        chart.y = 50
        chart.height = 125
        chart.width = 300
        
        # Sample data
        chart.data = [[(1, 1), (2, 2), (3, 3), (4, 4), (5, 5)]]
        
        # Styling
        chart.lines[0].strokeColor = colors_theme['primary']
        chart.lines[0].strokeWidth = 2
        
        drawing.add(chart)
        return drawing
    
    def _dict_to_xml(self, data: Dict, indent: int = 2) -> str:
        """Convert dictionary to XML format"""
        xml_parts = []
        spaces = ' ' * indent
        
        for key, value in data.items():
            if isinstance(value, dict):
                xml_parts.append(f"{spaces}<{key}>")
                xml_parts.append(self._dict_to_xml(value, indent + 2))
                xml_parts.append(f"{spaces}</{key}>")
            elif isinstance(value, list):
                xml_parts.append(f"{spaces}<{key}>")
                for item in value:
                    if isinstance(item, dict):
                        xml_parts.append(f"{spaces}  <item>")
                        xml_parts.append(self._dict_to_xml(item, indent + 4))
                        xml_parts.append(f"{spaces}  </item>")
                    else:
                        xml_parts.append(f"{spaces}  <item>{str(item)}</item>")
                xml_parts.append(f"{spaces}</{key}>")
            else:
                xml_parts.append(f"{spaces}<{key}>{str(value)}</{key}>")
        
        return '\n'.join(xml_parts)
    
    def get_export_statistics(self) -> Dict[str, Any]:
        """Get export system statistics"""
        return {
            'total_exports': self.export_stats['total_exports'],
            'exports_by_type': self.export_stats['exports_by_type'],
            'exports_by_format': self.export_stats['exports_by_format'],
            'last_export': self.export_stats['last_export'],
            'available_formats': [fmt.value for fmt in ExportFormat],
            'available_types': [typ.value for typ in ExportType],
            'supported_features': [
                'professional_pdf_with_charts',
                'multi_sheet_excel',
                'csv_with_headers',
                'structured_json',
                'xml_with_metadata',
                'zip_packages',
                'custom_branding',
                'multiple_color_schemes',
                'chart_generation',
                'advanced_formatting'
            ]
        }

# Export system instance
export_engine = AdvancedExportEngine()

# Helper functions for easy integration
def create_shipment_report_export(shipment_data: List[Dict], format: str = "pdf") -> Dict[str, Any]:
    """Create shipment report export"""
    config = ExportConfiguration(
        format=ExportFormat(format),
        export_type=ExportType.SHIPMENT_REPORT,
        include_charts=True,
        include_summary=True
    )
    
    export_data = {
        'summary': {
            'total_shipments': len(shipment_data),
            'active_shipments': len([s for s in shipment_data if s.get('status') == 'active']),
            'completed_shipments': len([s for s in shipment_data if s.get('status') == 'completed']),
            'total_value': sum(float(s.get('total_price', 0)) for s in shipment_data)
        },
        'details': shipment_data,
        'charts': [
            {
                'type': 'bar',
                'title': 'Shipments by Status',
                'data': {'active': 10, 'completed': 15, 'pending': 5}
            },
            {
                'type': 'pie',
                'title': 'Transport Mode Distribution',
                'data': {'truck': 60, 'ship': 25, 'air': 15}
            }
        ]
    }
    
    return export_engine.create_export(export_data, config)

def create_analytics_dashboard_export(analytics_data: Dict, format: str = "excel") -> Dict[str, Any]:
    """Create analytics dashboard export"""
    config = ExportConfiguration(
        format=ExportFormat(format),
        export_type=ExportType.ANALYTICS_DASHBOARD,
        include_charts=True,
        include_summary=True
    )
    
    return export_engine.create_export(analytics_data, config)

def create_performance_scorecard_export(scorecard_data: Dict, format: str = "pdf") -> Dict[str, Any]:
    """Create performance scorecard export"""
    config = ExportConfiguration(
        format=ExportFormat(format),
        export_type=ExportType.PERFORMANCE_SCORECARD,
        include_charts=True,
        include_summary=True,
        color_scheme='professional'
    )
    
    return export_engine.create_export(scorecard_data, config)