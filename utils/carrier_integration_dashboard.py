"""
Carrier Integration Dashboard System
Manages live carrier API connections (DHL, FedEx, UPS) for logistics providers
"""

import json
import datetime
import requests
import asyncio
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from enum import Enum
import logging

logger = logging.getLogger(__name__)

class CarrierType(Enum):
    DHL = "dhl"
    FEDEX = "fedex"
    UPS = "ups"
    MAERSK = "maersk"
    LUFTHANSA_CARGO = "lufthansa_cargo"

class BookingStatus(Enum):
    PENDING = "pending"
    CONFIRMED = "confirmed"
    IN_TRANSIT = "in_transit"
    DELIVERED = "delivered"
    CANCELLED = "cancelled"
    EXCEPTION = "exception"

class DocumentType(Enum):
    SHIPPING_LABEL = "shipping_label"
    BILL_OF_LADING = "bill_of_lading"
    COMMERCIAL_INVOICE = "commercial_invoice"
    CUSTOMS_DECLARATION = "customs_declaration"
    DELIVERY_RECEIPT = "delivery_receipt"

@dataclass
class CarrierCredentials:
    """Carrier API credentials"""
    provider_id: int
    carrier: CarrierType
    api_key: str
    secret_key: str
    account_number: str
    is_active: bool = True
    sandbox_mode: bool = False
    rate_limit: int = 100  # requests per hour
    created_at: datetime.datetime = field(default_factory=datetime.datetime.now)
    last_used: Optional[datetime.datetime] = None

@dataclass
class CarrierBooking:
    """Carrier booking information"""
    booking_id: str
    provider_id: int
    carrier: CarrierType
    carrier_booking_ref: str
    shipment_id: str
    origin: str
    destination: str
    status: BookingStatus
    tracking_number: str
    service_type: str
    estimated_delivery: Optional[datetime.datetime]
    actual_delivery: Optional[datetime.datetime]
    cost: float
    currency: str
    documents: List[str] = field(default_factory=list)
    tracking_events: List[Dict[str, Any]] = field(default_factory=list)
    created_at: datetime.datetime = field(default_factory=datetime.datetime.now)
    updated_at: datetime.datetime = field(default_factory=datetime.datetime.now)

@dataclass
class TrackingEvent:
    """Carrier tracking event"""
    timestamp: datetime.datetime
    location: str
    status: str
    description: str
    event_code: str
    carrier: CarrierType

class CarrierIntegrationDashboard:
    """Comprehensive carrier integration management system"""
    
    def __init__(self):
        self.credentials = {}  # provider_id -> {carrier -> credentials}
        self.bookings = {}  # booking_id -> CarrierBooking
        self.rate_limits = {}  # provider_id:carrier -> usage_count
        self.service_capabilities = self._load_carrier_capabilities()
        
    def _load_carrier_capabilities(self) -> Dict[CarrierType, Dict[str, Any]]:
        """Load carrier service capabilities and configurations"""
        return {
            CarrierType.DHL: {
                "services": [
                    {"code": "EXPRESS_WORLDWIDE", "name": "DHL Express Worldwide", "delivery_time": "1-3 days"},
                    {"code": "EXPRESS_12", "name": "DHL Express 12:00", "delivery_time": "Next business day by 12:00"},
                    {"code": "EXPRESS_10_30", "name": "DHL Express 10:30", "delivery_time": "Next business day by 10:30"},
                    {"code": "FREIGHT", "name": "DHL Freight", "delivery_time": "2-5 days"}
                ],
                "max_weight": 70,  # kg
                "max_dimensions": {"length": 120, "width": 80, "height": 80},  # cm
                "supported_countries": 220,
                "api_version": "v4",
                "documents": ["shipping_label", "commercial_invoice", "customs_declaration"]
            },
            CarrierType.FEDEX: {
                "services": [
                    {"code": "FEDEX_2_DAY", "name": "FedEx 2Day", "delivery_time": "2 business days"},
                    {"code": "STANDARD_OVERNIGHT", "name": "FedEx Standard Overnight", "delivery_time": "Next business day"},
                    {"code": "PRIORITY_OVERNIGHT", "name": "FedEx Priority Overnight", "delivery_time": "Next business day by 10:30 AM"},
                    {"code": "INTERNATIONAL_ECONOMY", "name": "FedEx International Economy", "delivery_time": "2-5 business days"}
                ],
                "max_weight": 68,  # kg
                "max_dimensions": {"length": 119, "width": 79, "height": 79},  # cm
                "supported_countries": 220,
                "api_version": "v1",
                "documents": ["shipping_label", "bill_of_lading", "commercial_invoice"]
            },
            CarrierType.UPS: {
                "services": [
                    {"code": "UPS_GROUND", "name": "UPS Ground", "delivery_time": "1-5 business days"},
                    {"code": "UPS_2ND_DAY_AIR", "name": "UPS 2nd Day Air", "delivery_time": "2 business days"},
                    {"code": "UPS_NEXT_DAY_AIR", "name": "UPS Next Day Air", "delivery_time": "Next business day"},
                    {"code": "UPS_WORLDWIDE_EXPRESS", "name": "UPS Worldwide Express", "delivery_time": "1-3 business days"}
                ],
                "max_weight": 70,  # kg
                "max_dimensions": {"length": 121, "width": 76, "height": 76},  # cm
                "supported_countries": 220,
                "api_version": "v1",
                "documents": ["shipping_label", "commercial_invoice", "delivery_receipt"]
            },
            CarrierType.MAERSK: {
                "services": [
                    {"code": "FCL", "name": "Full Container Load", "delivery_time": "15-45 days"},
                    {"code": "LCL", "name": "Less than Container Load", "delivery_time": "20-50 days"},
                    {"code": "REEFER", "name": "Refrigerated Container", "delivery_time": "15-45 days"}
                ],
                "max_weight": 30000,  # kg (container)
                "container_types": ["20ft", "40ft", "40ft HC", "45ft"],
                "supported_routes": 350,
                "api_version": "v2",
                "documents": ["bill_of_lading", "customs_declaration", "container_manifest"]
            },
            CarrierType.LUFTHANSA_CARGO: {
                "services": [
                    {"code": "EXPRESS", "name": "Lufthansa Express", "delivery_time": "1-2 days"},
                    {"code": "PRIORITY", "name": "Lufthansa Priority", "delivery_time": "2-3 days"},
                    {"code": "STANDARD", "name": "Lufthansa Standard", "delivery_time": "3-5 days"}
                ],
                "max_weight": 1000,  # kg
                "special_cargo": ["dangerous_goods", "perishables", "live_animals"],
                "supported_airports": 300,
                "api_version": "v1",
                "documents": ["air_waybill", "customs_declaration", "dangerous_goods_declaration"]
            }
        }
    
    def register_carrier_credentials(self, provider_id: int, carrier: CarrierType, 
                                   api_key: str, secret_key: str, account_number: str,
                                   sandbox_mode: bool = False) -> Dict[str, Any]:
        """Register carrier API credentials for a logistics provider"""
        try:
            # Validate credentials with carrier API
            is_valid = self._validate_carrier_credentials(carrier, api_key, secret_key, account_number)
            
            if not is_valid:
                return {
                    "success": False,
                    "error": "Invalid carrier credentials"
                }
            
            # Store credentials
            if provider_id not in self.credentials:
                self.credentials[provider_id] = {}
            
            self.credentials[provider_id][carrier] = CarrierCredentials(
                provider_id=provider_id,
                carrier=carrier,
                api_key=api_key,
                secret_key=secret_key,
                account_number=account_number,
                sandbox_mode=sandbox_mode
            )
            
            return {
                "success": True,
                "carrier": carrier.value,
                "capabilities": self.service_capabilities.get(carrier, {}),
                "message": f"{carrier.value.upper()} credentials registered successfully"
            }
            
        except Exception as e:
            logger.error(f"Carrier credential registration error: {str(e)}")
            return {
                "success": False,
                "error": "Failed to register carrier credentials"
            }
    
    def _validate_carrier_credentials(self, carrier: CarrierType, api_key: str, 
                                    secret_key: str, account_number: str) -> bool:
        """Validate carrier credentials with test API call"""
        try:
            # For demo purposes, simulate credential validation
            # In production, this would make actual API calls to each carrier
            
            validation_rules = {
                CarrierType.DHL: len(api_key) >= 20 and len(secret_key) >= 20,
                CarrierType.FEDEX: len(api_key) >= 16 and len(secret_key) >= 25,
                CarrierType.UPS: len(api_key) >= 32 and len(account_number) >= 6,
                CarrierType.MAERSK: len(api_key) >= 20 and len(secret_key) >= 20,
                CarrierType.LUFTHANSA_CARGO: len(api_key) >= 24 and len(secret_key) >= 24
            }
            
            return validation_rules.get(carrier, False)
            
        except Exception as e:
            logger.error(f"Credential validation error: {str(e)}")
            return False
    
    def create_carrier_booking(self, provider_id: int, carrier: CarrierType, 
                             shipment_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create booking with carrier API"""
        try:
            # Check if provider has credentials for this carrier
            if (provider_id not in self.credentials or 
                carrier not in self.credentials[provider_id]):
                return {
                    "success": False,
                    "error": f"No {carrier.value} credentials found for provider"
                }
            
            # Check rate limits
            if not self._check_carrier_rate_limit(provider_id, carrier):
                return {
                    "success": False,
                    "error": "Rate limit exceeded for carrier API"
                }
            
            # Generate booking (simulated for demo)
            booking_id = f"{carrier.value.upper()}-{datetime.datetime.now().strftime('%Y%m%d')}-{provider_id:04d}"
            carrier_ref = f"{carrier.value[:3].upper()}{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}"
            tracking_number = f"1Z{carrier.value[:3].upper()}{datetime.datetime.now().strftime('%y%m%d%H%M%S')}"
            
            # Create booking record
            booking = CarrierBooking(
                booking_id=booking_id,
                provider_id=provider_id,
                carrier=carrier,
                carrier_booking_ref=carrier_ref,
                shipment_id=shipment_data.get("shipment_id", ""),
                origin=shipment_data.get("origin", ""),
                destination=shipment_data.get("destination", ""),
                status=BookingStatus.CONFIRMED,
                tracking_number=tracking_number,
                service_type=shipment_data.get("service_type", "STANDARD"),
                estimated_delivery=datetime.datetime.now() + datetime.timedelta(days=3),
                cost=float(shipment_data.get("cost", 150.00)),
                currency=shipment_data.get("currency", "USD")
            )
            
            self.bookings[booking_id] = booking
            
            # Update rate limit
            self._update_carrier_rate_limit(provider_id, carrier)
            
            return {
                "success": True,
                "booking_id": booking_id,
                "carrier_reference": carrier_ref,
                "tracking_number": tracking_number,
                "estimated_delivery": booking.estimated_delivery.isoformat(),
                "status": booking.status.value,
                "documents_available": len(booking.documents)
            }
            
        except Exception as e:
            logger.error(f"Carrier booking creation error: {str(e)}")
            return {
                "success": False,
                "error": "Failed to create carrier booking"
            }
    
    def get_provider_carrier_bookings(self, provider_id: int, 
                                    carrier: Optional[CarrierType] = None,
                                    status: Optional[BookingStatus] = None) -> List[Dict[str, Any]]:
        """Get carrier bookings for a logistics provider"""
        try:
            bookings = []
            
            for booking in self.bookings.values():
                if booking.provider_id != provider_id:
                    continue
                
                if carrier and booking.carrier != carrier:
                    continue
                
                if status and booking.status != status:
                    continue
                
                bookings.append({
                    "booking_id": booking.booking_id,
                    "carrier": booking.carrier.value,
                    "carrier_reference": booking.carrier_booking_ref,
                    "shipment_id": booking.shipment_id,
                    "origin": booking.origin,
                    "destination": booking.destination,
                    "status": booking.status.value,
                    "tracking_number": booking.tracking_number,
                    "service_type": booking.service_type,
                    "estimated_delivery": booking.estimated_delivery.isoformat() if booking.estimated_delivery else None,
                    "actual_delivery": booking.actual_delivery.isoformat() if booking.actual_delivery else None,
                    "cost": booking.cost,
                    "currency": booking.currency,
                    "documents_count": len(booking.documents),
                    "tracking_events_count": len(booking.tracking_events),
                    "created_at": booking.created_at.isoformat()
                })
            
            return sorted(bookings, key=lambda x: x["created_at"], reverse=True)
            
        except Exception as e:
            logger.error(f"Get carrier bookings error: {str(e)}")
            return []
    
    def get_tracking_updates(self, tracking_number: str) -> Dict[str, Any]:
        """Get real-time tracking updates from carrier"""
        try:
            # Find booking by tracking number
            booking = None
            for b in self.bookings.values():
                if b.tracking_number == tracking_number:
                    booking = b
                    break
            
            if not booking:
                return {
                    "success": False,
                    "error": "Tracking number not found"
                }
            
            # Simulate tracking events (in production, fetch from carrier API)
            tracking_events = [
                {
                    "timestamp": "2025-07-01T10:00:00Z",
                    "location": "Origin Warehouse",
                    "status": "PICKED_UP",
                    "description": "Package picked up by carrier",
                    "event_code": "PU"
                },
                {
                    "timestamp": "2025-07-01T14:30:00Z",
                    "location": "Sorting Facility",
                    "status": "IN_TRANSIT",
                    "description": "Package processed at sorting facility",
                    "event_code": "SF"
                },
                {
                    "timestamp": "2025-07-02T09:15:00Z",
                    "location": "Transportation Hub",
                    "status": "IN_TRANSIT",
                    "description": "Package in transit to destination",
                    "event_code": "IT"
                },
                {
                    "timestamp": "2025-07-03T11:45:00Z",
                    "location": "Destination City",
                    "status": "OUT_FOR_DELIVERY",
                    "description": "Package out for delivery",
                    "event_code": "OFD"
                }
            ]
            
            # Update booking with tracking events
            booking.tracking_events = tracking_events
            booking.updated_at = datetime.datetime.now()
            
            return {
                "success": True,
                "tracking_number": tracking_number,
                "carrier": booking.carrier.value,
                "current_status": booking.status.value,
                "estimated_delivery": booking.estimated_delivery.isoformat() if booking.estimated_delivery else None,
                "tracking_events": tracking_events
            }
            
        except Exception as e:
            logger.error(f"Tracking update error: {str(e)}")
            return {
                "success": False,
                "error": "Failed to get tracking updates"
            }
    
    def generate_carrier_documents(self, booking_id: str, 
                                 document_types: List[DocumentType]) -> Dict[str, Any]:
        """Generate carrier-specific documents"""
        try:
            booking = self.bookings.get(booking_id)
            if not booking:
                return {
                    "success": False,
                    "error": "Booking not found"
                }
            
            generated_docs = []
            
            for doc_type in document_types:
                # Simulate document generation
                doc_id = f"{booking_id}_{doc_type.value}_{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}"
                doc_url = f"/api/logistics/documents/{doc_id}/download"
                
                generated_docs.append({
                    "document_id": doc_id,
                    "type": doc_type.value,
                    "name": f"{doc_type.value.replace('_', ' ').title()}",
                    "download_url": doc_url,
                    "generated_at": datetime.datetime.now().isoformat()
                })
                
                # Add to booking documents
                booking.documents.append(doc_id)
            
            return {
                "success": True,
                "booking_id": booking_id,
                "documents": generated_docs
            }
            
        except Exception as e:
            logger.error(f"Document generation error: {str(e)}")
            return {
                "success": False,
                "error": "Failed to generate documents"
            }
    
    def get_carrier_analytics(self, provider_id: int, days: int = 30) -> Dict[str, Any]:
        """Get carrier performance analytics"""
        try:
            cutoff_date = datetime.datetime.now() - datetime.timedelta(days=days)
            
            # Filter bookings for this provider
            provider_bookings = [b for b in self.bookings.values() 
                               if b.provider_id == provider_id and b.created_at >= cutoff_date]
            
            if not provider_bookings:
                return {
                    "total_bookings": 0,
                    "carriers_used": 0,
                    "avg_cost": 0,
                    "on_time_delivery": 0,
                    "carrier_breakdown": [],
                    "cost_savings": 0
                }
            
            # Calculate metrics
            total_bookings = len(provider_bookings)
            carriers_used = len(set(b.carrier for b in provider_bookings))
            avg_cost = sum(b.cost for b in provider_bookings) / total_bookings
            
            # On-time delivery calculation
            delivered_bookings = [b for b in provider_bookings if b.actual_delivery]
            on_time_count = sum(1 for b in delivered_bookings 
                              if b.actual_delivery <= b.estimated_delivery)
            on_time_delivery = (on_time_count / len(delivered_bookings) * 100) if delivered_bookings else 0
            
            # Carrier breakdown
            carrier_stats = {}
            for booking in provider_bookings:
                carrier = booking.carrier.value
                if carrier not in carrier_stats:
                    carrier_stats[carrier] = {
                        "bookings": 0,
                        "total_cost": 0,
                        "avg_cost": 0,
                        "on_time": 0,
                        "total_delivered": 0
                    }
                
                carrier_stats[carrier]["bookings"] += 1
                carrier_stats[carrier]["total_cost"] += booking.cost
                
                if booking.actual_delivery:
                    carrier_stats[carrier]["total_delivered"] += 1
                    if booking.actual_delivery <= booking.estimated_delivery:
                        carrier_stats[carrier]["on_time"] += 1
            
            # Calculate averages and percentages
            for carrier, stats in carrier_stats.items():
                stats["avg_cost"] = round(stats["total_cost"] / stats["bookings"], 2)
                if stats["total_delivered"] > 0:
                    stats["on_time_percentage"] = round(
                        (stats["on_time"] / stats["total_delivered"]) * 100, 1
                    )
                else:
                    stats["on_time_percentage"] = 0
            
            carrier_breakdown = [
                {
                    "carrier": carrier,
                    "bookings": stats["bookings"],
                    "avg_cost": stats["avg_cost"],
                    "on_time_percentage": stats["on_time_percentage"]
                }
                for carrier, stats in carrier_stats.items()
            ]
            
            # Cost savings calculation (compared to single carrier)
            single_carrier_cost = max(carrier_stats.values(), key=lambda x: x["avg_cost"])["avg_cost"] * total_bookings
            actual_total_cost = sum(b.cost for b in provider_bookings)
            cost_savings = max(0, single_carrier_cost - actual_total_cost)
            
            return {
                "total_bookings": total_bookings,
                "carriers_used": carriers_used,
                "avg_cost": round(avg_cost, 2),
                "on_time_delivery": round(on_time_delivery, 1),
                "carrier_breakdown": carrier_breakdown,
                "cost_savings": round(cost_savings, 2)
            }
            
        except Exception as e:
            logger.error(f"Carrier analytics error: {str(e)}")
            return {
                "total_bookings": 0,
                "carriers_used": 0,
                "avg_cost": 0,
                "on_time_delivery": 0,
                "carrier_breakdown": [],
                "cost_savings": 0
            }
    
    def _check_carrier_rate_limit(self, provider_id: int, carrier: CarrierType) -> bool:
        """Check carrier API rate limit"""
        try:
            key = f"{provider_id}:{carrier.value}"
            current_hour = datetime.datetime.now().replace(minute=0, second=0, microsecond=0)
            
            if key not in self.rate_limits:
                self.rate_limits[key] = {"count": 0, "hour": current_hour}
            
            rate_data = self.rate_limits[key]
            
            # Reset if new hour
            if rate_data["hour"] < current_hour:
                rate_data["count"] = 0
                rate_data["hour"] = current_hour
            
            # Check limit (assuming 100 requests per hour)
            return rate_data["count"] < 100
            
        except Exception as e:
            logger.error(f"Rate limit check error: {str(e)}")
            return False
    
    def _update_carrier_rate_limit(self, provider_id: int, carrier: CarrierType):
        """Update carrier API rate limit counter"""
        try:
            key = f"{provider_id}:{carrier.value}"
            if key in self.rate_limits:
                self.rate_limits[key]["count"] += 1
                
        except Exception as e:
            logger.error(f"Rate limit update error: {str(e)}")

# Global instance
carrier_integration_dashboard = CarrierIntegrationDashboard()