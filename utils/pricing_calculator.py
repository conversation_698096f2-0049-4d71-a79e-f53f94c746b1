"""
Smart Pricing Calculation System for Cloverics Platform
Implements comprehensive pricing algorithm with market-based factors
"""

import math

def calculate_comprehensive_price(weight, origin_country, destination_country, transport_type="truck", urgency="standard", cargo_type="general"):
    """
    Calculate comprehensive shipping price using intelligent market-based algorithm
    """
    
    # Base rates per kg for different transport types
    BASE_RATES_PER_KG = {
        'truck': 2.50,
        'ship': 1.20,
        'air': 8.50,
        'rail': 1.80
    }
    
    # Distance estimation for major routes (km)
    DISTANCE_DATABASE = {
        ('Turkey', 'Azerbaijan'): 1250,
        ('Turkey', 'Germany'): 2100,
        ('Germany', 'Turkey'): 2100,
        ('Turkey', 'United Kingdom'): 2800,
        ('United States', 'Turkey'): 9500,
        ('China', 'Turkey'): 7200,
        ('Turkey', 'France'): 2400,
        ('Turkey', 'Italy'): 1600,
        ('Turkey', 'Russia'): 2200,
        ('Turkey', 'Georgia'): 800,
        ('Germany', 'Azerbaijan'): 3100,
        ('United States', 'Germany'): 8000,
        ('China', 'Germany'): 8300
    }
    
    # Get base rate
    base_rate_per_kg = BASE_RATES_PER_KG.get(transport_type, 2.50)
    
    # Calculate distance-based pricing
    route_key = (origin_country, destination_country)
    reverse_route_key = (destination_country, origin_country)
    
    if route_key in DISTANCE_DATABASE:
        distance = DISTANCE_DATABASE[route_key]
    elif reverse_route_key in DISTANCE_DATABASE:
        distance = DISTANCE_DATABASE[reverse_route_key]
    else:
        # Estimate distance for unknown routes
        distance = 2000  # Default medium distance
    
    # Distance factor (longer routes cost more)
    distance_factor = 1 + (distance / 10000) * 0.5
    
    # Weight factors
    if weight <= 50:
        weight_factor = 1.0
    elif weight <= 500:
        weight_factor = 0.9  # Volume discount
    elif weight <= 1000:
        weight_factor = 0.8
    else:
        weight_factor = 0.75  # Large volume discount
    
    # Transport type multipliers
    transport_multipliers = {
        'truck': 1.0,
        'ship': 0.8,   # Cheaper for bulk
        'air': 2.5,    # Premium for speed
        'rail': 0.9    # Efficient for medium distance
    }
    
    transport_multiplier = transport_multipliers.get(transport_type, 1.0)
    
    # Urgency multipliers
    urgency_multipliers = {
        'standard': 1.0,
        'express': 1.4,
        'urgent': 1.8
    }
    
    urgency_multiplier = urgency_multipliers.get(urgency, 1.0)
    
    # Cargo type risk factors
    cargo_risk_factors = {
        'general': 1.0,
        'fragile': 1.2,
        'hazardous': 1.6,
        'perishable': 1.3,
        'valuable': 1.4
    }
    
    cargo_factor = cargo_risk_factors.get(cargo_type, 1.0)
    
    # Calculate base cost
    base_cost = weight * base_rate_per_kg * weight_factor
    
    # Apply all factors
    total_cost = base_cost * distance_factor * transport_multiplier * urgency_multiplier * cargo_factor
    
    # Add fixed costs
    if distance > 5000:  # International long distance
        customs_fee = 120.0
        total_cost += customs_fee
    
    # Fuel surcharge (8% of base cost)
    fuel_surcharge = total_cost * 0.08
    total_cost += fuel_surcharge
    
    # Insurance (1% of cargo value, minimum $50)
    estimated_cargo_value = weight * 50  # Estimate $50 per kg
    insurance_cost = max(estimated_cargo_value * 0.01, 50)
    total_cost += insurance_cost
    
    # Platform fee (0.1%)
    platform_fee = total_cost * 0.001
    total_cost += platform_fee
    
    # Round to reasonable precision
    return round(total_cost, 2)


def get_price_breakdown(weight, origin_country, destination_country, transport_type="truck", urgency="standard", cargo_type="general"):
    """
    Get detailed price breakdown for transparency
    """
    
    # Recalculate with breakdown
    base_rates = {
        'truck': 2.50,
        'ship': 1.20,
        'air': 8.50,
        'rail': 1.80
    }
    
    base_rate = base_rates.get(transport_type, 2.50)
    base_cost = weight * base_rate
    
    # Distance factor
    distance_database = {
        ('Turkey', 'Azerbaijan'): 1250,
        ('Turkey', 'Germany'): 2100,
        ('Germany', 'Turkey'): 2100,
        ('Turkey', 'United Kingdom'): 2800,
        ('United States', 'Turkey'): 9500,
        ('China', 'Turkey'): 7200
    }
    
    route_key = (origin_country, destination_country)
    distance = distance_database.get(route_key, distance_database.get((destination_country, origin_country), 2000))
    
    breakdown = {
        'base_cost': round(base_cost, 2),
        'distance_km': distance,
        'fuel_surcharge': round(base_cost * 0.08, 2),
        'insurance': round(max(weight * 50 * 0.01, 50), 2),
        'customs_fee': 120.0 if distance > 5000 else 0.0,
        'platform_fee': round(base_cost * 0.001, 2)
    }
    
    total = sum(breakdown.values()) - breakdown['distance_km']  # Remove distance from sum
    breakdown['total'] = round(total, 2)
    
    return breakdown