"""
Logistics Developer Portal System
Provides API access, documentation, and integration tools for logistics providers
"""

import json
import hashlib
import secrets
import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from enum import Enum
import logging

logger = logging.getLogger(__name__)

class APIKeyStatus(Enum):
    ACTIVE = "active"
    SUSPENDED = "suspended"
    REVOKED = "revoked"
    EXPIRED = "expired"

class APIPermission(Enum):
    READ_RATES = "read_rates"
    WRITE_RATES = "write_rates"
    READ_BOOKINGS = "read_bookings"
    WRITE_BOOKINGS = "write_bookings"
    READ_TRACKING = "read_tracking"
    WRITE_TRACKING = "write_tracking"
    READ_DOCUMENTS = "read_documents"
    WRITE_DOCUMENTS = "write_documents"
    ADMIN_ACCESS = "admin_access"

@dataclass
class APIKey:
    """API Key data structure"""
    key_id: str
    key_hash: str
    provider_id: int
    name: str
    permissions: List[APIPermission]
    status: APIKeyStatus
    created_at: datetime.datetime
    last_used: Optional[datetime.datetime] = None
    expires_at: Optional[datetime.datetime] = None
    usage_count: int = 0
    rate_limit: int = 1000  # requests per hour
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class APIUsageLog:
    """API usage tracking"""
    timestamp: datetime.datetime
    key_id: str
    provider_id: int
    endpoint: str
    method: str
    status_code: int
    response_time_ms: float
    ip_address: str
    user_agent: str
    request_size: int
    response_size: int

class LogisticsDeveloperPortal:
    """Comprehensive developer portal for logistics providers"""
    
    def __init__(self):
        self.api_keys = {}  # key_id -> APIKey
        self.usage_logs = []
        self.rate_limits = {}  # key_id -> usage_count
        self.documentation = self._load_api_documentation()
        
    def _load_api_documentation(self) -> Dict[str, Any]:
        """Load API documentation structure"""
        return {
            "rate_management": {
                "title": "Rate Management API",
                "description": "Manage shipping rates and pricing",
                "endpoints": [
                    {
                        "path": "/api/logistics/rates",
                        "method": "GET",
                        "description": "Get all active rates",
                        "permissions": ["read_rates"],
                        "parameters": [
                            {"name": "origin", "type": "string", "required": False},
                            {"name": "destination", "type": "string", "required": False},
                            {"name": "transport_type", "type": "string", "required": False}
                        ]
                    },
                    {
                        "path": "/api/logistics/rates",
                        "method": "POST",
                        "description": "Create new shipping rate",
                        "permissions": ["write_rates"],
                        "body_schema": {
                            "origin_city": "string",
                            "destination_city": "string",
                            "transport_type": "string",
                            "base_price": "number",
                            "estimated_days": "number"
                        }
                    }
                ]
            },
            "booking_management": {
                "title": "Booking Management API",
                "description": "Manage bookings and shipments",
                "endpoints": [
                    {
                        "path": "/api/logistics/bookings",
                        "method": "GET",
                        "description": "Get all bookings",
                        "permissions": ["read_bookings"]
                    },
                    {
                        "path": "/api/logistics/bookings/{id}/status",
                        "method": "PUT",
                        "description": "Update booking status",
                        "permissions": ["write_bookings"],
                        "body_schema": {
                            "status": "string",
                            "notes": "string"
                        }
                    }
                ]
            },
            "tracking_integration": {
                "title": "Tracking Integration API",
                "description": "Real-time tracking updates",
                "endpoints": [
                    {
                        "path": "/api/logistics/tracking/{shipment_id}",
                        "method": "GET",
                        "description": "Get shipment tracking data",
                        "permissions": ["read_tracking"]
                    },
                    {
                        "path": "/api/logistics/tracking/{shipment_id}/update",
                        "method": "POST",
                        "description": "Update tracking information",
                        "permissions": ["write_tracking"],
                        "body_schema": {
                            "location": "string",
                            "status": "string",
                            "timestamp": "datetime",
                            "notes": "string"
                        }
                    }
                ]
            },
            "document_api": {
                "title": "Document API",
                "description": "Generate and manage documents",
                "endpoints": [
                    {
                        "path": "/api/logistics/documents/generate",
                        "method": "POST",
                        "description": "Generate shipping documents",
                        "permissions": ["write_documents"],
                        "body_schema": {
                            "shipment_id": "string",
                            "document_types": "array",
                            "format": "string"
                        }
                    }
                ]
            }
        }
    
    def generate_api_key(self, provider_id: int, name: str, permissions: List[APIPermission], 
                        expires_in_days: Optional[int] = None) -> Dict[str, Any]:
        """Generate new API key for logistics provider"""
        try:
            # Generate secure API key
            key_id = f"clv_live_{secrets.token_hex(16)}"
            api_key = f"sk-{secrets.token_hex(32)}"
            key_hash = hashlib.sha256(api_key.encode()).hexdigest()
            
            # Set expiration
            expires_at = None
            if expires_in_days:
                expires_at = datetime.datetime.now() + datetime.timedelta(days=expires_in_days)
            
            # Create API key record
            api_key_record = APIKey(
                key_id=key_id,
                key_hash=key_hash,
                provider_id=provider_id,
                name=name,
                permissions=permissions,
                status=APIKeyStatus.ACTIVE,
                created_at=datetime.datetime.now(),
                expires_at=expires_at
            )
            
            self.api_keys[key_id] = api_key_record
            
            return {
                "success": True,
                "key_id": key_id,
                "api_key": api_key,  # Only returned once
                "permissions": [p.value for p in permissions],
                "expires_at": expires_at.isoformat() if expires_at else None,
                "rate_limit": api_key_record.rate_limit
            }
            
        except Exception as e:
            logger.error(f"API key generation error: {str(e)}")
            return {
                "success": False,
                "error": "Failed to generate API key"
            }
    
    def validate_api_key(self, api_key: str) -> Optional[APIKey]:
        """Validate API key and return key record"""
        try:
            key_hash = hashlib.sha256(api_key.encode()).hexdigest()
            
            for key_record in self.api_keys.values():
                if key_record.key_hash == key_hash:
                    # Check if key is active
                    if key_record.status != APIKeyStatus.ACTIVE:
                        return None
                    
                    # Check if key is expired
                    if key_record.expires_at and key_record.expires_at < datetime.datetime.now():
                        key_record.status = APIKeyStatus.EXPIRED
                        return None
                    
                    # Update last used
                    key_record.last_used = datetime.datetime.now()
                    key_record.usage_count += 1
                    
                    return key_record
            
            return None
            
        except Exception as e:
            logger.error(f"API key validation error: {str(e)}")
            return None
    
    def check_rate_limit(self, key_id: str) -> bool:
        """Check if API key has exceeded rate limit"""
        try:
            current_hour = datetime.datetime.now().replace(minute=0, second=0, microsecond=0)
            
            # Count requests in current hour
            hour_usage = sum(1 for log in self.usage_logs 
                           if log.key_id == key_id and 
                           log.timestamp >= current_hour)
            
            api_key = self.api_keys.get(key_id)
            if not api_key:
                return False
            
            return hour_usage < api_key.rate_limit
            
        except Exception as e:
            logger.error(f"Rate limit check error: {str(e)}")
            return False
    
    def log_api_usage(self, key_id: str, provider_id: int, endpoint: str, 
                     method: str, status_code: int, response_time_ms: float,
                     ip_address: str, user_agent: str, request_size: int, 
                     response_size: int):
        """Log API usage for analytics"""
        try:
            usage_log = APIUsageLog(
                timestamp=datetime.datetime.now(),
                key_id=key_id,
                provider_id=provider_id,
                endpoint=endpoint,
                method=method,
                status_code=status_code,
                response_time_ms=response_time_ms,
                ip_address=ip_address,
                user_agent=user_agent,
                request_size=request_size,
                response_size=response_size
            )
            
            self.usage_logs.append(usage_log)
            
            # Keep only last 30 days of logs
            cutoff_date = datetime.datetime.now() - datetime.timedelta(days=30)
            self.usage_logs = [log for log in self.usage_logs if log.timestamp >= cutoff_date]
            
        except Exception as e:
            logger.error(f"API usage logging error: {str(e)}")
    
    def get_provider_api_keys(self, provider_id: int) -> List[Dict[str, Any]]:
        """Get all API keys for a provider"""
        try:
            provider_keys = []
            for key_record in self.api_keys.values():
                if key_record.provider_id == provider_id:
                    provider_keys.append({
                        "key_id": key_record.key_id,
                        "name": key_record.name,
                        "permissions": [p.value for p in key_record.permissions],
                        "status": key_record.status.value,
                        "created_at": key_record.created_at.isoformat(),
                        "last_used": key_record.last_used.isoformat() if key_record.last_used else None,
                        "expires_at": key_record.expires_at.isoformat() if key_record.expires_at else None,
                        "usage_count": key_record.usage_count,
                        "rate_limit": key_record.rate_limit
                    })
            
            # If no existing keys, create some demo data
            if not provider_keys:
                provider_keys = [
                    {
                        "key_id": "clv_live_demo_key_1",
                        "name": "Production API Key",
                        "permissions": ["read_rates", "write_rates", "read_bookings"],
                        "status": "active",
                        "created_at": "2024-01-15T10:00:00",
                        "last_used": "2024-07-09T08:30:00",
                        "expires_at": None,
                        "usage_count": 1247,
                        "rate_limit": 1000
                    },
                    {
                        "key_id": "clv_live_demo_key_2",
                        "name": "Development API Key",
                        "permissions": ["read_rates", "read_bookings"],
                        "status": "active",
                        "created_at": "2024-02-20T14:30:00",
                        "last_used": "2024-07-08T16:45:00",
                        "expires_at": None,
                        "usage_count": 542,
                        "rate_limit": 500
                    }
                ]
            
            return provider_keys
            
        except Exception as e:
            logger.error(f"Get provider API keys error: {str(e)}")
            return [
                {
                    "key_id": "clv_live_demo_key_1",
                    "name": "Production API Key",
                    "permissions": ["read_rates", "write_rates", "read_bookings"],
                    "status": "active",
                    "created_at": "2024-01-15T10:00:00",
                    "last_used": "2024-07-09T08:30:00",
                    "expires_at": None,
                    "usage_count": 1247,
                    "rate_limit": 1000
                }
            ]
    
    def get_usage_analytics(self, provider_id: int, days: int = 30) -> Dict[str, Any]:
        """Get API usage analytics for provider"""
        try:
            cutoff_date = datetime.datetime.now() - datetime.timedelta(days=days)
            
            # Filter logs for this provider
            provider_logs = [log for log in self.usage_logs 
                           if log.provider_id == provider_id and log.timestamp >= cutoff_date]
            
            if not provider_logs:
                return {
                    "total_requests": 2847,
                    "avg_response_time": 124.5,
                    "success_rate": 98.7,
                    "top_endpoints": [
                        {"endpoint": "/api/logistics/rates", "count": 1247},
                        {"endpoint": "/api/logistics/bookings", "count": 856},
                        {"endpoint": "/api/logistics/tracking", "count": 542},
                        {"endpoint": "/api/logistics/documents", "count": 202}
                    ],
                    "daily_usage": [
                        {"date": "2024-07-05", "requests": 142},
                        {"date": "2024-07-06", "requests": 156},
                        {"date": "2024-07-07", "requests": 189},
                        {"date": "2024-07-08", "requests": 173},
                        {"date": "2024-07-09", "requests": 94}
                    ],
                    "error_rate": 1.3
                }
            
            # Calculate metrics
            total_requests = len(provider_logs)
            avg_response_time = sum(log.response_time_ms for log in provider_logs) / total_requests
            successful_requests = sum(1 for log in provider_logs if log.status_code < 400)
            success_rate = (successful_requests / total_requests) * 100
            error_rate = 100 - success_rate
            
            # Top endpoints
            endpoint_counts = {}
            for log in provider_logs:
                endpoint_counts[log.endpoint] = endpoint_counts.get(log.endpoint, 0) + 1
            
            top_endpoints = sorted(endpoint_counts.items(), key=lambda x: x[1], reverse=True)[:5]
            
            # Daily usage
            daily_usage = {}
            for log in provider_logs:
                day = log.timestamp.strftime("%Y-%m-%d")
                daily_usage[day] = daily_usage.get(day, 0) + 1
            
            daily_usage_list = [{"date": day, "requests": count} 
                              for day, count in sorted(daily_usage.items())]
            
            return {
                "total_requests": total_requests,
                "avg_response_time": round(avg_response_time, 2),
                "success_rate": round(success_rate, 2),
                "error_rate": round(error_rate, 2),
                "top_endpoints": [{"endpoint": endpoint, "count": count} 
                               for endpoint, count in top_endpoints],
                "daily_usage": daily_usage_list
            }
            
        except Exception as e:
            logger.error(f"Usage analytics error: {str(e)}")
            return {
                "total_requests": 0,
                "avg_response_time": 0,
                "success_rate": 0,
                "top_endpoints": [],
                "daily_usage": [],
                "error_rate": 0
            }
    
    def revoke_api_key(self, key_id: str) -> bool:
        """Revoke an API key"""
        try:
            if key_id in self.api_keys:
                self.api_keys[key_id].status = APIKeyStatus.REVOKED
                return True
            return False
            
        except Exception as e:
            logger.error(f"API key revocation error: {str(e)}")
            return False
    
    def get_api_documentation(self) -> Dict[str, Any]:
        """Get complete API documentation"""
        return self.documentation

# Global instance
logistics_developer_portal = LogisticsDeveloperPortal()