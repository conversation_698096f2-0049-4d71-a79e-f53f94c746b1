"""
API Integration for LogistiLink Tracking Updates

This module handles the synchronization of tracking data from various carrier APIs 
and third-party tracking aggregators.
"""
import os
import logging
import json
import requests
from datetime import datetime, timedelta
import time
import ipaddress
from urllib.parse import quote

from django.conf import settings
from django.utils import timezone

from shipments.models import Shipment, ShipmentTracking
from core.notification_models import Notification

from .carrier_api import get_carrier_api
from .update_service import create_tracking_update
from .status_mappings import map_carrier_status

logger = logging.getLogger(__name__)

class TrackingAggregatorAPI:
    """Base class for tracking aggregator APIs like FourKites, Project44, etc."""
    
    def __init__(self, api_key=None):
        """Initialize with API credentials."""
        self.api_key = api_key or os.environ.get(f"{self.provider_name.upper()}_API_KEY")
        if not self.api_key:
            raise ValueError(f"API key for {self.provider_name} is required")
        
        self.headers = self._build_headers()
    
    @property
    def provider_name(self):
        """Return the provider name."""
        return "aggregator"  # Override in subclasses
    
    @property
    def base_url(self):
        """Return the base URL for the API."""
        return ""  # Override in subclasses
    
    def _build_headers(self):
        """Build headers for API requests."""
        return {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {self.api_key}'
        }
    
    def get_tracking_updates(self, tracking_number=None, carrier_code=None, since=None):
        """
        Get tracking updates for a shipment.
        
        Args:
            tracking_number (str, optional): Tracking number to get updates for
            carrier_code (str, optional): Carrier code (e.g., 'DHL', 'FEDEX')
            since (datetime, optional): Only get updates since this time
            
        Returns:
            list: List of tracking events
        """
        raise NotImplementedError("Subclasses must implement this method")
    
    def register_webhook(self, tracking_number, carrier_code, callback_url):
        """
        Register a webhook for tracking updates.
        
        Args:
            tracking_number (str): Tracking number to register webhook for
            carrier_code (str): Carrier code (e.g., 'DHL', 'FEDEX')
            callback_url (str): URL to receive webhook callbacks
            
        Returns:
            dict: Response from API
        """
        raise NotImplementedError("Subclasses must implement this method")
    
    def process_webhook_data(self, data):
        """
        Process webhook data from the aggregator.
        
        Args:
            data (dict): Webhook data
            
        Returns:
            dict: Processed tracking update
        """
        raise NotImplementedError("Subclasses must implement this method")

class FourKitesAPI(TrackingAggregatorAPI):
    """FourKites API integration for tracking updates."""
    
    @property
    def provider_name(self):
        return "fourkites"
    
    @property
    def base_url(self):
        return "https://api-mock.fourkites.com/v1"  # Mock URL for development
    
    def get_tracking_updates(self, tracking_number=None, carrier_code=None, since=None):
        """Get tracking updates from FourKites."""
        url = f"{self.base_url}/shipments/tracking"
        
        params = {}
        if tracking_number:
            params['trackingNumber'] = tracking_number
        if carrier_code:
            params['carrierCode'] = carrier_code
        if since:
            params['since'] = since.strftime('%Y-%m-%dT%H:%M:%S')
        
        try:
            response = requests.get(url, headers=self.headers, params=params)
            
            if response.status_code == 200:
                data = response.json()
                
                events = []
                for shipment in data.get('shipments', []):
                    tracking_num = shipment.get('trackingNumber')
                    carrier = shipment.get('carrierCode')
                    
                    for event in shipment.get('events', []):
                        events.append({
                            'tracking_number': tracking_num,
                            'carrier': carrier,
                            'timestamp': event.get('timestamp'),
                            'location': event.get('location', {}).get('name', ''),
                            'country': event.get('location', {}).get('countryCode', ''),
                            'latitude': event.get('location', {}).get('latitude'),
                            'longitude': event.get('location', {}).get('longitude'),
                            'status_code': event.get('statusCode', ''),
                            'description': event.get('description', ''),
                            'mapped_status': map_carrier_status(
                                carrier, 
                                event.get('statusCode', ''),
                                event.get('description', '')
                            )
                        })
                
                return events
            else:
                logger.error(f"FourKites API error: {response.status_code} - {response.text}")
                return []
                
        except Exception as e:
            logger.error(f"Error getting tracking updates from FourKites: {str(e)}")
            return []
    
    def register_webhook(self, tracking_number, carrier_code, callback_url):
        """Register a webhook for tracking updates."""
        url = f"{self.base_url}/webhooks"
        
        payload = {
            'trackingNumber': tracking_number,
            'carrierCode': carrier_code,
            'callbackUrl': callback_url,
            'events': ['ALL']  # Subscribe to all event types
        }
        
        try:
            response = requests.post(url, headers=self.headers, json=payload)
            
            if response.status_code in [200, 201]:
                return response.json()
            else:
                logger.error(f"FourKites webhook registration error: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            logger.error(f"Error registering FourKites webhook: {str(e)}")
            return None
    
    def process_webhook_data(self, data):
        """Process webhook data from FourKites."""
        try:
            tracking_number = data.get('trackingNumber')
            carrier = data.get('carrierCode')
            event = data.get('event', {})
            
            return {
                'tracking_number': tracking_number,
                'carrier': carrier,
                'timestamp': event.get('timestamp'),
                'location': event.get('location', {}).get('name', ''),
                'country': event.get('location', {}).get('countryCode', ''),
                'latitude': event.get('location', {}).get('latitude'),
                'longitude': event.get('location', {}).get('longitude'),
                'status_code': event.get('statusCode', ''),
                'description': event.get('description', ''),
                'mapped_status': map_carrier_status(
                    carrier, 
                    event.get('statusCode', ''),
                    event.get('description', '')
                )
            }
            
        except Exception as e:
            logger.error(f"Error processing FourKites webhook data: {str(e)}")
            return None

class Project44API(TrackingAggregatorAPI):
    """Project44 API integration for tracking updates."""
    
    @property
    def provider_name(self):
        return "project44"
    
    @property
    def base_url(self):
        return "https://api-mock.project44.com/v1"  # Mock URL for development
    
    def get_tracking_updates(self, tracking_number=None, carrier_code=None, since=None):
        """Get tracking updates from Project44."""
        url = f"{self.base_url}/shipments/tracking"
        
        params = {}
        if tracking_number:
            params['referenceNumber'] = tracking_number
        if carrier_code:
            params['carrierCode'] = carrier_code
        if since:
            params['fromDateTime'] = since.strftime('%Y-%m-%dT%H:%M:%S')
        
        try:
            response = requests.get(url, headers=self.headers, params=params)
            
            if response.status_code == 200:
                data = response.json()
                
                events = []
                for shipment in data.get('shipments', []):
                    tracking_num = shipment.get('referenceNumber')
                    carrier = shipment.get('carrierCode')
                    
                    for event in shipment.get('events', []):
                        events.append({
                            'tracking_number': tracking_num,
                            'carrier': carrier,
                            'timestamp': event.get('dateTime'),
                            'location': event.get('location', {}).get('name', ''),
                            'country': event.get('location', {}).get('country', ''),
                            'latitude': event.get('location', {}).get('latitude'),
                            'longitude': event.get('location', {}).get('longitude'),
                            'status_code': event.get('eventCode', ''),
                            'description': event.get('eventDescription', ''),
                            'mapped_status': map_carrier_status(
                                carrier, 
                                event.get('eventCode', ''),
                                event.get('eventDescription', '')
                            )
                        })
                
                return events
            else:
                logger.error(f"Project44 API error: {response.status_code} - {response.text}")
                return []
                
        except Exception as e:
            logger.error(f"Error getting tracking updates from Project44: {str(e)}")
            return []
    
    def register_webhook(self, tracking_number, carrier_code, callback_url):
        """Register a webhook for tracking updates."""
        url = f"{self.base_url}/subscriptions"
        
        payload = {
            'referenceNumber': tracking_number,
            'carrierCode': carrier_code,
            'callbackUrl': callback_url,
            'eventTypes': ['STATUS_UPDATE', 'LOCATION_UPDATE', 'DELIVERY_UPDATE']
        }
        
        try:
            response = requests.post(url, headers=self.headers, json=payload)
            
            if response.status_code in [200, 201]:
                return response.json()
            else:
                logger.error(f"Project44 webhook registration error: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            logger.error(f"Error registering Project44 webhook: {str(e)}")
            return None
    
    def process_webhook_data(self, data):
        """Process webhook data from Project44."""
        try:
            tracking_number = data.get('referenceNumber')
            carrier = data.get('carrierCode')
            event = data.get('event', {})
            
            return {
                'tracking_number': tracking_number,
                'carrier': carrier,
                'timestamp': event.get('dateTime'),
                'location': event.get('location', {}).get('name', ''),
                'country': event.get('location', {}).get('country', ''),
                'latitude': event.get('location', {}).get('latitude'),
                'longitude': event.get('location', {}).get('longitude'),
                'status_code': event.get('eventCode', ''),
                'description': event.get('eventDescription', ''),
                'mapped_status': map_carrier_status(
                    carrier, 
                    event.get('eventCode', ''),
                    event.get('eventDescription', '')
                )
            }
            
        except Exception as e:
            logger.error(f"Error processing Project44 webhook data: {str(e)}")
            return None

def get_aggregator_api(provider_name, api_key=None):
    """
    Get aggregator API instance based on provider name.
    
    Args:
        provider_name (str): Provider name (e.g., 'fourkites', 'project44')
        api_key (str, optional): API key for authentication
        
    Returns:
        TrackingAggregatorAPI: Aggregator API instance
    """
    provider_name = provider_name.lower()
    
    if provider_name == 'fourkites':
        return FourKitesAPI(api_key)
    elif provider_name == 'project44':
        return Project44API(api_key)
    else:
        raise ValueError(f"Unsupported tracking aggregator: {provider_name}")

def poll_tracking_updates(shipment, provider_name=None, carrier_code=None, force_update=False):
    """
    Poll for tracking updates for a shipment.
    
    Args:
        shipment (Shipment): The shipment to update
        provider_name (str, optional): Tracking aggregator provider name
        carrier_code (str, optional): Carrier code (e.g., 'DHL', 'FEDEX')
        force_update (bool, optional): Whether to force an update even if recent updates exist
        
    Returns:
        bool: Whether any updates were applied
    """
    # Default to using the carrier code from the shipment transport type
    if not carrier_code and shipment.transport_type:
        carrier_map = {
            'Ocean': 'MAERSK',
            'Air': 'DHL',
            'Truck': 'FEDEX',
            'Rail': 'RAIL'
        }
        carrier_code = carrier_map.get(shipment.transport_type.type, 'GENERIC')
    
    # Check if we should poll for updates (only if no recent updates)
    last_update = shipment.tracking_details.order_by('-timestamp').first()
    
    if last_update and not force_update:
        # Don't poll if updated in the last 6 hours
        if timezone.now() - last_update.timestamp < timedelta(hours=6):
            logger.info(f"Skipping tracking update for {shipment.tracking_number} - recent update exists")
            return False
    
    # Determine how to get updates
    if provider_name:
        # Use aggregator API
        try:
            api = get_aggregator_api(provider_name)
            events = api.get_tracking_updates(
                tracking_number=shipment.tracking_number,
                carrier_code=carrier_code
            )
        except Exception as e:
            logger.error(f"Error getting tracking updates from {provider_name}: {str(e)}")
            events = []
    else:
        # Use direct carrier API
        try:
            api = get_carrier_api(carrier_code)
            response = api.track_shipment(shipment.tracking_number)
            events = response.get('events', [])
        except Exception as e:
            logger.error(f"Error getting tracking updates from {carrier_code}: {str(e)}")
            events = []
    
    # Process events
    updates_applied = False
    
    for event in events:
        # Skip event if timestamp is older than the last update
        if last_update and 'timestamp' in event:
            event_time = datetime.fromisoformat(event['timestamp'].replace('Z', '+00:00'))
            if event_time <= last_update.timestamp.replace(tzinfo=None):
                continue
        
        try:
            # Create tracking update
            create_tracking_update(
                shipment=shipment,
                status=event.get('mapped_status', 'IN_TRANSIT'),
                location_name=event.get('location', ''),
                location_country=event.get('country', ''),
                description=event.get('description', ''),
                latitude=event.get('latitude'),
                longitude=event.get('longitude'),
                updated_by=None,  # System update
                update_source='API',
                carrier_code=event.get('status_code', ''),
                carrier_description=event.get('description', '')
            )
            
            updates_applied = True
            
        except Exception as e:
            logger.error(f"Error creating tracking update: {str(e)}")
    
    return updates_applied

def register_tracking_webhooks(shipment, provider_name, carrier_code=None, callback_url=None):
    """
    Register tracking webhooks for a shipment.
    
    Args:
        shipment (Shipment): The shipment to register webhooks for
        provider_name (str): Tracking aggregator provider name
        carrier_code (str, optional): Carrier code (e.g., 'DHL', 'FEDEX')
        callback_url (str, optional): URL to receive webhook callbacks
        
    Returns:
        bool: Whether the webhook was registered successfully
    """
    if not callback_url:
        # Use the default webhook URL
        callback_url = f"{settings.WEBHOOK_BASE_URL}/api/tracking/webhook/{provider_name.lower()}"
    
    # Default to using the carrier code from the shipment transport type
    if not carrier_code and shipment.transport_type:
        carrier_map = {
            'Ocean': 'MAERSK',
            'Air': 'DHL',
            'Truck': 'FEDEX',
            'Rail': 'RAIL'
        }
        carrier_code = carrier_map.get(shipment.transport_type.type, 'GENERIC')
    
    # Register webhook
    try:
        api = get_aggregator_api(provider_name)
        response = api.register_webhook(
            tracking_number=shipment.tracking_number,
            carrier_code=carrier_code,
            callback_url=callback_url
        )
        
        return response is not None
        
    except Exception as e:
        logger.error(f"Error registering tracking webhook: {str(e)}")
        return False

def process_webhook_callback(provider_name, data):
    """
    Process a webhook callback from a tracking aggregator.
    
    Args:
        provider_name (str): Tracking aggregator provider name
        data (dict): Webhook data
        
    Returns:
        bool: Whether the webhook was processed successfully
    """
    try:
        # Process webhook data
        api = get_aggregator_api(provider_name)
        event = api.process_webhook_data(data)
        
        if not event:
            logger.error(f"Error processing webhook data from {provider_name}")
            return False
        
        # Get the shipment
        try:
            shipment = Shipment.objects.get(tracking_number=event.get('tracking_number'))
        except Shipment.DoesNotExist:
            logger.error(f"Shipment not found for tracking number: {event.get('tracking_number')}")
            return False
        
        # Create tracking update
        create_tracking_update(
            shipment=shipment,
            status=event.get('mapped_status', 'IN_TRANSIT'),
            location_name=event.get('location', ''),
            location_country=event.get('country', ''),
            description=event.get('description', ''),
            latitude=event.get('latitude'),
            longitude=event.get('longitude'),
            updated_by=None,  # System update
            update_source='API',
            carrier_code=event.get('status_code', ''),
            carrier_description=event.get('description', '')
        )
        
        return True
        
    except Exception as e:
        logger.error(f"Error processing webhook callback: {str(e)}")
        return False