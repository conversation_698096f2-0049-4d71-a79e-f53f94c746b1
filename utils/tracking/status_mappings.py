"""
Status mapping utilities for carrier integrations.
This module provides functions to map carrier-specific status codes
to Cloverics standardized status codes.
"""

# Cloverics standardized status codes (for reference)
CLOVERICS_STATUSES = {
    # Initial statuses
    'BOOKING_RECEIVED': 'Booking Received',
    'BOOKING_CONFIRMED': 'Booking Confirmed',
    'PAYMENT_RECEIVED': 'Payment Received',
    
    # Origin statuses
    'PICKUP_SCHEDULED': 'Pickup Scheduled',
    'PICKED_UP': 'Picked Up from Origin',
    'ARRIVED_ORIGIN_WAREHOUSE': 'Arrived at Origin Warehouse',
    'PROCESSING_AT_ORIGIN': 'Processing at Origin',
    'DEPARTED_ORIGIN': 'Departed Origin',
    
    # Transit statuses
    'IN_TRANSIT': 'In Transit',
    'ARRIVED_MIDDLE_PORT': 'Arrived at Transit Port',
    'DEPARTED_MIDDLE_PORT': 'Departed Transit Port',
    
    # Customs statuses
    'CUSTOMS_PROCESSING': 'Customs Processing',
    'CUSTOMS_INSPECTION': 'Customs Inspection',
    'CUSTOMS_CLEARANCE': 'Customs Cleared',
    'CUSTOMS_HOLD': 'Customs Hold',
    
    # Final delivery statuses
    'ARRIVED_DESTINATION_COUNTRY': 'Arrived at Destination Country',
    'OUT_FOR_DELIVERY': 'Out for Delivery',
    'DELIVERY_ATTEMPT': 'Delivery Attempted',
    'DELIVERED': 'Delivered',
    
    # Exception statuses
    'DELAYED': 'Delayed',
    'ON_HOLD': 'On Hold',
    'CANCELLED': 'Cancelled',
    'RETURNED': 'Returned to Sender',
}

# DHL status mapping
DHL_STATUS_MAPPING = {
    'PU': 'PICKED_UP',                              # Picked up
    'DF': 'DEPARTED_ORIGIN',                        # Departed facility
    'AF': 'ARRIVED_ORIGIN_WAREHOUSE',               # Arrived at facility
    'OC': 'CUSTOMS_PROCESSING',                     # Origin customs processing
    'IT': 'IN_TRANSIT',                             # In transit
    'AP': 'ARRIVED_MIDDLE_PORT',                    # Arrived at transit port
    'DP': 'DEPARTED_MIDDLE_PORT',                   # Departed transit port
    'WC': 'CUSTOMS_PROCESSING',                     # With customs
    'CA': 'CUSTOMS_CLEARANCE',                      # Customs clearance approved
    'CH': 'CUSTOMS_HOLD',                           # Customs hold
    'DL': 'DELIVERED',                              # Delivered
    'OD': 'OUT_FOR_DELIVERY',                       # Out for delivery
    'DD': 'DELAYED',                                # Delivery delayed
    'DI': 'CUSTOMS_INSPECTION',                     # Document inspection
    'NH': 'DELIVERY_ATTEMPT',                       # Delivery attempted - not home
    'RT': 'RETURNED',                               # Returned to sender
    'OH': 'ON_HOLD',                                # On hold
}

# FedEx status mapping
FEDEX_STATUS_MAPPING = {
    'PU': 'PICKED_UP',                              # Picked up
    'OC': 'DEPARTED_ORIGIN',                        # Origin scan
    'AR': 'ARRIVED_ORIGIN_WAREHOUSE',               # Arrived at FedEx location
    'DP': 'DEPARTED_ORIGIN',                        # Departed FedEx location
    'IT': 'IN_TRANSIT',                             # In transit
    'AP': 'ARRIVED_MIDDLE_PORT',                    # At pickup 
    'CD': 'CUSTOMS_PROCESSING',                     # Clearance delay
    'CC': 'CUSTOMS_CLEARANCE',                      # Clearance completed
    'DL': 'DELIVERED',                              # Delivered
    'OD': 'OUT_FOR_DELIVERY',                       # On FedEx vehicle for delivery
    'DE': 'DELAYED',                                # Delivery exception
    'DY': 'DELAYED',                                # Delayed
    'CH': 'CUSTOMS_HOLD',                           # Customs hold
    'DA': 'DELIVERY_ATTEMPT',                       # Delivery attempt
    'RS': 'RETURNED',                               # Return to shipper
    'CA': 'CANCELLED',                              # Shipment cancelled
}

# Maersk (ocean shipping) status mapping
MAERSK_STATUS_MAPPING = {
    'BOOKING_CONFIRMED': 'BOOKING_CONFIRMED',       # Booking confirmed 
    'CONTAINER_LOADED': 'PICKED_UP',                # Container loaded
    'GATE_IN': 'ARRIVED_ORIGIN_WAREHOUSE',          # Gate in at origin terminal
    'LOADED_ON_VESSEL': 'DEPARTED_ORIGIN',          # Loaded on vessel
    'VESSEL_DEPARTED': 'DEPARTED_ORIGIN',           # Vessel departed from origin
    'ON_VESSEL': 'IN_TRANSIT',                      # On vessel (in transit)
    'VESSEL_ARRIVED': 'ARRIVED_MIDDLE_PORT',        # Vessel arrived at port
    'TRANSSHIPMENT': 'ARRIVED_MIDDLE_PORT',         # Transshipment
    'DISCHARGED': 'ARRIVED_DESTINATION_COUNTRY',    # Discharged from vessel
    'GATE_OUT': 'OUT_FOR_DELIVERY',                 # Gate out at destination
    'CUSTOM_CLEARANCE': 'CUSTOMS_CLEARANCE',        # Customs clearance
    'CUSTOM_HOLD': 'CUSTOMS_HOLD',                  # Customs hold
    'DELAY': 'DELAYED',                             # Delayed
    'DELIVERED': 'DELIVERED',                       # Delivered
    'BOOKING_CANCELLED': 'CANCELLED',               # Booking cancelled
}

# Generic carrier status mapping (for carriers without specific mapping)
GENERIC_STATUS_MAPPING = {
    'PICKUP': 'PICKED_UP',
    'ORIGIN': 'DEPARTED_ORIGIN',
    'TRANSIT': 'IN_TRANSIT',
    'CUSTOMS': 'CUSTOMS_PROCESSING',
    'DELIVERY': 'OUT_FOR_DELIVERY',
    'DELIVERED': 'DELIVERED',
    'EXCEPTION': 'DELAYED',
}

def map_carrier_status(carrier, status_code, description=""):
    """
    Maps carrier-specific status code to LogistiLink status.
    
    Args:
        carrier (str): Carrier code (e.g., 'DHL', 'FEDEX', 'MAERSK')
        status_code (str): Carrier's status code
        description (str): Status description (used for fallback mapping)
        
    Returns:
        str: Corresponding LogistiLink status code
    """
    carrier = carrier.upper()
    
    if carrier == 'DHL':
        return DHL_STATUS_MAPPING.get(status_code, map_by_description(description))
    elif carrier == 'FEDEX':
        return FEDEX_STATUS_MAPPING.get(status_code, map_by_description(description))
    elif carrier == 'MAERSK':
        return MAERSK_STATUS_MAPPING.get(status_code, map_by_description(description))
    else:
        # For unknown carriers, try to map by description
        return map_by_description(description)
        
def map_by_description(description):
    """
    Tries to determine status from description text when no mapping exists.
    
    Args:
        description (str): Status description from carrier
        
    Returns:
        str: Best-matching Cloverics status code
    """
    description = description.lower()
    
    # Check for key terms in the description
    if any(term in description for term in ['pickup', 'collected', 'pick up']):
        return 'PICKED_UP'
    elif any(term in description for term in ['customs', 'clearance']):
        if 'hold' in description:
            return 'CUSTOMS_HOLD'
        elif 'inspect' in description:
            return 'CUSTOMS_INSPECTION'
        elif 'clear' in description:
            return 'CUSTOMS_CLEARANCE'
        else:
            return 'CUSTOMS_PROCESSING'
    elif any(term in description for term in ['transit', 'departed', 'en route']):
        return 'IN_TRANSIT'
    elif any(term in description for term in ['arrived', 'arrival']):
        if any(term in description for term in ['destination', 'final']):
            return 'ARRIVED_DESTINATION_COUNTRY'
        else:
            return 'ARRIVED_MIDDLE_PORT'
    elif any(term in description for term in ['out for delivery', 'delivery attempt']):
        return 'OUT_FOR_DELIVERY'
    elif any(term in description for term in ['delivered', 'delivery completed']):
        return 'DELIVERED'
    elif any(term in description for term in ['delay', 'exception', 'hold']):
        return 'DELAYED'
    else:
        # Default fallback status
        return 'IN_TRANSIT'