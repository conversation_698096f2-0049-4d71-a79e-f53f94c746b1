"""
Carrier API integration for shipment tracking.
This module provides base functionality for integrating with various carrier APIs
for real-time shipment tracking updates.
"""
import os
import requests
import json
import logging
from abc import ABC, abstractmethod
from datetime import datetime

from .status_mappings import map_carrier_status

logger = logging.getLogger(__name__)

class CarrierAPI(ABC):
    """Base class for carrier API integrations."""
    
    def __init__(self, api_key=None):
        """
        Initialize the carrier API integration.
        
        Args:
            api_key (str, optional): API key for carrier authentication
        """
        self.api_key = api_key or os.environ.get(f"{self.carrier_code}_API_KEY")
        self.headers = self._build_headers()
        
    @property
    @abstractmethod
    def carrier_code(self):
        """Return the carrier code (e.g., 'DHL', 'FEDEX')."""
        pass
    
    @property
    @abstractmethod
    def base_url(self):
        """Return the base URL for the carrier API."""
        pass
    
    def _build_headers(self):
        """Build headers for API requests."""
        return {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {self.api_key}' if self.api_key else None
        }
    
    @abstractmethod
    def track_shipment(self, tracking_number):
        """
        Track a shipment by tracking number.
        
        Args:
            tracking_number (str): Carrier tracking number
            
        Returns:
            dict: Tracking information
        """
        pass
    
    def map_status(self, status_code, description=""):
        """
        Map carrier status to LogistiLink status.
        
        Args:
            status_code (str): Carrier status code
            description (str): Status description
            
        Returns:
            str: LogistiLink status code
        """
        return map_carrier_status(self.carrier_code, status_code, description)
    
    def handle_response(self, response):
        """
        Handle API response and extract tracking information.
        
        Args:
            response (requests.Response): API response
            
        Returns:
            dict: Parsed tracking information
        """
        if response.status_code == 200:
            return response.json()
        else:
            logger.error(f"API error: {response.status_code} - {response.text}")
            raise Exception(f"API error: {response.status_code}")
            
class DHLTrackingAPI(CarrierAPI):
    """DHL shipment tracking API integration."""
    
    @property
    def carrier_code(self):
        return "DHL"
    
    @property
    def base_url(self):
        return "https://api-mock.dhl.com/tracking"  # Mock URL for development
    
    def track_shipment(self, tracking_number):
        """Track a DHL shipment."""
        url = f"{self.base_url}/shipments"
        params = {"trackingNumber": tracking_number}
        
        try:
            response = requests.get(url, headers=self.headers, params=params)
            data = self.handle_response(response)
            
            # Extract and transform tracking events
            events = []
            for event in data.get('shipmentEvents', []):
                events.append({
                    'timestamp': event.get('timestamp'),
                    'location': event.get('location', {}).get('name', ''),
                    'country': event.get('location', {}).get('countryCode', ''),
                    'status_code': event.get('statusCode', ''),
                    'description': event.get('description', ''),
                    'mapped_status': self.map_status(
                        event.get('statusCode', ''), 
                        event.get('description', '')
                    )
                })
            
            return {
                'carrier': self.carrier_code,
                'tracking_number': tracking_number,
                'events': events,
                'current_status': events[0]['mapped_status'] if events else None
            }
            
        except Exception as e:
            logger.error(f"Error tracking DHL shipment: {str(e)}")
            return {
                'carrier': self.carrier_code,
                'tracking_number': tracking_number,
                'events': [],
                'current_status': None,
                'error': str(e)
            }

class FedExTrackingAPI(CarrierAPI):
    """FedEx shipment tracking API integration."""
    
    @property
    def carrier_code(self):
        return "FEDEX"
    
    @property
    def base_url(self):
        return "https://api-mock.fedex.com/track"  # Mock URL for development
    
    def track_shipment(self, tracking_number):
        """Track a FedEx shipment."""
        url = f"{self.base_url}/v1/trackingdetails"
        payload = {
            "trackingInfo": [
                {"trackingNumberInfo": {"trackingNumber": tracking_number}}
            ]
        }
        
        try:
            response = requests.post(url, headers=self.headers, json=payload)
            data = self.handle_response(response)
            
            # Extract and transform tracking events
            events = []
            track_results = data.get('output', {}).get('completeTrackResults', [])
            if track_results:
                for event in track_results[0].get('trackResults', [])[0].get('scanEvents', []):
                    events.append({
                        'timestamp': event.get('date'),
                        'location': event.get('scanLocation', {}).get('city', ''),
                        'country': event.get('scanLocation', {}).get('countryCode', ''),
                        'status_code': event.get('eventType', ''),
                        'description': event.get('eventDescription', ''),
                        'mapped_status': self.map_status(
                            event.get('eventType', ''), 
                            event.get('eventDescription', '')
                        )
                    })
            
            return {
                'carrier': self.carrier_code,
                'tracking_number': tracking_number,
                'events': events,
                'current_status': events[0]['mapped_status'] if events else None
            }
            
        except Exception as e:
            logger.error(f"Error tracking FedEx shipment: {str(e)}")
            return {
                'carrier': self.carrier_code,
                'tracking_number': tracking_number,
                'events': [],
                'current_status': None,
                'error': str(e)
            }

class MaerskTrackingAPI(CarrierAPI):
    """Maersk shipment tracking API integration."""
    
    @property
    def carrier_code(self):
        return "MAERSK"
    
    @property
    def base_url(self):
        return "https://api-mock.maersk.com/tracking"  # Mock URL for development
    
    def track_shipment(self, tracking_number):
        """Track a Maersk shipment."""
        url = f"{self.base_url}/container/{tracking_number}"
        
        try:
            response = requests.get(url, headers=self.headers)
            data = self.handle_response(response)
            
            # Extract and transform tracking events
            events = []
            for event in data.get('events', []):
                events.append({
                    'timestamp': event.get('eventDate'),
                    'location': event.get('location', {}).get('terminal', ''),
                    'country': event.get('location', {}).get('country', ''),
                    'status_code': event.get('eventType', ''),
                    'description': event.get('eventDescription', ''),
                    'mapped_status': self.map_status(
                        event.get('eventType', ''), 
                        event.get('eventDescription', '')
                    )
                })
            
            return {
                'carrier': self.carrier_code,
                'tracking_number': tracking_number,
                'events': events,
                'current_status': events[0]['mapped_status'] if events else None
            }
            
        except Exception as e:
            logger.error(f"Error tracking Maersk shipment: {str(e)}")
            return {
                'carrier': self.carrier_code,
                'tracking_number': tracking_number,
                'events': [],
                'current_status': None,
                'error': str(e)
            }

# Factory method to get the appropriate carrier API
def get_carrier_api(carrier_code, api_key=None):
    """
    Get carrier API instance based on carrier code.
    
    Args:
        carrier_code (str): Carrier code (e.g., 'DHL', 'FEDEX', 'MAERSK')
        api_key (str, optional): API key for carrier authentication
        
    Returns:
        CarrierAPI: Carrier API instance
    """
    carrier_code = carrier_code.upper()
    
    if carrier_code == 'DHL':
        return DHLTrackingAPI(api_key)
    elif carrier_code == 'FEDEX':
        return FedExTrackingAPI(api_key)
    elif carrier_code == 'MAERSK':
        return MaerskTrackingAPI(api_key)
    else:
        raise ValueError(f"Unsupported carrier: {carrier_code}")