"""
Tracking update service for LogistiLink shipments.
This module provides utilities for updating shipment tracking information,
both manually and via API integrations.
"""
import os
import logging
from datetime import datetime
import ipaddress

from django.conf import settings
from django.utils import timezone

from shipments.models import Shipment, ShipmentTracking
from core.notification_models import Notification

logger = logging.getLogger(__name__)

def create_tracking_update(shipment, status, location_name, description="", 
                          latitude=None, longitude=None, location_country="",
                          updated_by=None, update_source="MANUAL",
                          carrier_code="", carrier_description="",
                          ip_address=None):
    """
    Create a tracking update for a shipment.
    
    Args:
        shipment (Shipment): The shipment to update
        status (str): Status code from TRACKING_STATUS_CHOICES
        location_name (str): Name of the location
        description (str, optional): Additional details about the status
        latitude (float, optional): Location latitude
        longitude (float, optional): Location longitude
        location_country (str, optional): Country code for the location
        updated_by (User, optional): User who created the update
        update_source (str, optional): Source of the update ('MANUAL', 'API', 'SYSTEM')
        carrier_code (str, optional): Original carrier status code
        carrier_description (str, optional): Original carrier status description
        ip_address (str, optional): IP address for manual updates
        
    Returns:
        ShipmentTracking: The created tracking update
    """
    try:
        # Create tracking update
        tracking_update = ShipmentTracking.objects.create(
            shipment=shipment,
            status=status,
            location_name=location_name,
            location_country=location_country,
            latitude=latitude,
            longitude=longitude,
            description=description,
            updated_by=updated_by,
            update_source=update_source,
            carrier_code=carrier_code,
            carrier_description=carrier_description,
            ip_address=ip_address
        )
        
        # Update the main shipment status if this is a significant status change
        update_shipment_status(shipment, status)
        
        # Create notifications for the customer and logistics provider
        create_tracking_notifications(shipment, tracking_update)
        
        logger.info(f"Created tracking update for shipment {shipment.tracking_number}: {status}")
        return tracking_update
        
    except Exception as e:
        logger.error(f"Error creating tracking update: {str(e)}")
        raise

def update_shipment_status(shipment, tracking_status):
    """
    Update the main shipment status based on tracking status.
    
    Args:
        shipment (Shipment): The shipment to update
        tracking_status (str): The tracking status code
        
    Returns:
        bool: True if the shipment status was updated, False otherwise
    """
    # Map detailed tracking status to main shipment status
    if tracking_status in ['BOOKING_RECEIVED', 'BOOKING_CONFIRMED', 'PAYMENT_RECEIVED']:
        new_status = 'CONFIRMED'
    elif tracking_status in ['CUSTOMS_PROCESSING', 'CUSTOMS_INSPECTION', 'CUSTOMS_HOLD']:
        new_status = 'CUSTOMS'
    elif tracking_status == 'DELIVERED':
        new_status = 'DELIVERED'
    elif tracking_status == 'CANCELLED':
        new_status = 'CANCELLED'
    else:
        new_status = 'IN_TRANSIT'
    
    # Only update if the status has changed
    if shipment.status != new_status:
        shipment.status = new_status
        
        # If delivered, set actual delivery date
        if new_status == 'DELIVERED':
            shipment.actual_delivery_date = timezone.now()
            
        shipment.save(update_fields=['status', 'actual_delivery_date'])
        return True
    
    return False

def create_tracking_notifications(shipment, tracking_update):
    """
    Create notifications for tracking updates.
    
    Args:
        shipment (Shipment): The shipment that was updated
        tracking_update (ShipmentTracking): The tracking update
        
    Returns:
        tuple: Created notifications (customer_notification, lc_notification)
    """
    status_display = dict(Shipment.TRACKING_STATUS_CHOICES).get(tracking_update.status)
    
    # Create notification for customer
    customer_notification = Notification.objects.create(
        user=shipment.customer,
        title=f"Shipment Update: {status_display}",
        message=f"""Your shipment ({shipment.tracking_number}) has been updated:
        
        Status: {status_display}
        Location: {tracking_update.location_name}
        Time: {tracking_update.timestamp.strftime('%Y-%m-%d %H:%M')}
        
        {tracking_update.description}
        """,
        notification_type='SHIPMENT',
        priority='MEDIUM',
        related_object_type='shipment',
        related_object_id=str(shipment.id)
    )
    
    # Create notification for logistics provider
    lc_notification = Notification.objects.create(
        user=shipment.logistics_provider,
        title=f"Shipment Status Updated: {status_display}",
        message=f"""Shipment ({shipment.tracking_number}) status has been updated:
        
        Status: {status_display}
        Location: {tracking_update.location_name}
        Time: {tracking_update.timestamp.strftime('%Y-%m-%d %H:%M')}
        Updated by: {tracking_update.updated_by.company_name if tracking_update.updated_by else 'System'}
        
        {tracking_update.description}
        """,
        notification_type='SHIPMENT',
        priority='LOW',
        related_object_type='shipment',
        related_object_id=str(shipment.id)
    )
    
    return (customer_notification, lc_notification)

def get_client_ip(request):
    """
    Get client IP address from request.
    
    Args:
        request: Django/Streamlit HTTP request
        
    Returns:
        str: IP address
    """
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip