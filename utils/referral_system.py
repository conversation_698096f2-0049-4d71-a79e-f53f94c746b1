"""
LogistiLink Referral System Utilities

This module provides utility functions for handling referrals,
bonus calculations, and client relationship management.
"""

from django.db import transaction
from django.utils import timezone
from core.models import User
from core.referral_models import Referral, ClientRelationship, ReferralSettings
from utils.security import log_user_action
import logging

# Configure referral system logging
referral_logger = logging.getLogger('logistilink.referral_system')

class ReferralSystemManager:
    """Manages referral operations and bonus calculations"""
    
    def __init__(self):
        self.settings = ReferralSettings.get_settings()
    
    def process_referral_registration(self, customer_user, referral_code):
        """
        Process referral during customer registration
        
        Args:
            customer_user: The newly registered customer
            referral_code: The referral code entered during registration
            
        Returns:
            dict: Processing result
        """
        try:
            with transaction.atomic():
                # Validate referral code
                validation_result = self.validate_referral_code(referral_code)
                if not validation_result['valid']:
                    return {
                        'success': False,
                        'error': validation_result['error']
                    }
                
                logistics_user = validation_result['logistics_user']
                
                # Prevent self-referral
                if logistics_user == customer_user:
                    return {
                        'success': False,
                        'error': 'Users cannot refer themselves'
                    }
                
                # Check if customer already has a locked referral code
                if customer_user.referral_code_locked and customer_user.referred_by_code:
                    return {
                        'success': False,
                        'error': 'Customer already has a referral code assigned'
                    }
                
                # Determine bonus eligibility based on payment history
                has_made_payments = customer_user.first_payment_done
                bonus_eligible = not has_made_payments
                
                # Update customer with referral information
                customer_user.referred_by_code = referral_code
                customer_user.referred_by_user = logistics_user
                customer_user.referral_bonus_eligible = bonus_eligible
                customer_user.referral_code_locked = True  # Lock the code once set
                customer_user.save()
                
                # Check if this is the logistics provider's first referral
                existing_referrals = Referral.objects.filter(
                    logistics_provider=logistics_user,
                    bonus_awarded=True
                ).count()
                
                is_first_referral = existing_referrals == 0
                
                # Create referral record
                referral = Referral.objects.create(
                    logistics_provider=logistics_user,
                    referred_customer=customer_user,
                    referral_code_used=referral_code,
                    is_first_referral=is_first_referral
                )
                
                # Create client relationship
                ClientRelationship.objects.create(
                    logistics_provider=logistics_user,
                    customer=customer_user,
                    origin='referral'
                )
                
                # Log referral creation
                log_user_action("referral_registered", customer_user.id, {
                    "referred_by": logistics_user.company_name,
                    "referral_code": referral_code,
                    "is_first_referral": is_first_referral
                })
                
                return {
                    'success': True,
                    'referral_id': referral.id,
                    'logistics_provider': logistics_user.company_name,
                    'is_first_referral': is_first_referral
                }
                
        except Exception as e:
            referral_logger.error(f"Error processing referral registration: {str(e)}")
            return {
                'success': False,
                'error': 'An error occurred while processing the referral'
            }
    
    def process_payment_bonus(self, customer_user, payment_amount):
        """
        Process bonus award when customer makes first payment
        
        Args:
            customer_user: Customer who made the payment
            payment_amount: Amount of the payment
            
        Returns:
            dict: Processing result
        """
        try:
            with transaction.atomic():
                # Check if customer was referred
                if not customer_user.referred_by_user:
                    return {'success': False, 'reason': 'Customer was not referred'}
                
                # Find the referral record
                try:
                    referral = Referral.objects.get(
                        logistics_provider=customer_user.referred_by_user,
                        referred_customer=customer_user,
                        bonus_awarded=False
                    )
                except Referral.DoesNotExist:
                    return {'success': False, 'reason': 'Referral record not found'}
                
                # Check minimum payment amount
                if payment_amount < self.settings.minimum_payment_for_bonus:
                    return {
                        'success': False, 
                        'reason': f'Payment amount must be at least ${self.settings.minimum_payment_for_bonus}'
                    }
                
                # Calculate bonus months
                bonus_months = (self.settings.first_referral_bonus_months 
                               if referral.is_first_referral 
                               else self.settings.subsequent_referral_bonus_months)
                
                # Update referral record
                referral.first_payment_at = timezone.now()
                referral.bonus_awarded = True
                referral.bonus_months_given = bonus_months
                referral.save()
                
                # Award bonus to logistics provider
                logistics_user = referral.logistics_provider
                logistics_user.premium_bonus_months += bonus_months
                logistics_user.save()
                
                # Log bonus award
                log_user_action("referral_bonus_awarded", logistics_user.id, {
                    "customer": customer_user.company_name,
                    "bonus_months": bonus_months,
                    "payment_amount": float(payment_amount),
                    "is_first_referral": referral.is_first_referral
                })
                
                return {
                    'success': True,
                    'bonus_months': bonus_months,
                    'logistics_provider': logistics_user.company_name,
                    'total_bonus_months': logistics_user.premium_bonus_months
                }
                
        except Exception as e:
            referral_logger.error(f"Error processing payment bonus: {str(e)}")
            return {
                'success': False,
                'error': 'An error occurred while processing the bonus'
            }
    
    def create_shipment_relationship(self, logistics_user, customer_user, shipment_value=0):
        """
        Create client relationship when shipment is completed
        
        Args:
            logistics_user: Logistics provider
            customer_user: Customer
            shipment_value: Value of the shipment
            
        Returns:
            dict: Creation result
        """
        try:
            # Get or create relationship
            relationship, created = ClientRelationship.objects.get_or_create(
                logistics_provider=logistics_user,
                customer=customer_user,
                defaults={'origin': 'shipment'}
            )
            
            # Update shipment statistics
            relationship.update_shipment_stats(shipment_value)
            
            # Grant customer premium membership on first connection with any logistics provider
            self.grant_customer_connection_bonus(customer_user, created)
            
            # Log relationship creation/update
            log_user_action("shipment_relationship_updated", logistics_user.id, {
                "customer": customer_user.company_name,
                "shipment_value": float(shipment_value),
                "total_shipments": relationship.total_shipments,
                "relationship_created": created
            })
            
            return {
                'success': True,
                'relationship_created': created,
                'total_shipments': relationship.total_shipments
            }
            
        except Exception as e:
            referral_logger.error(f"Error creating shipment relationship: {str(e)}")
            return {
                'success': False,
                'error': 'An error occurred while updating the relationship'
            }
    
    def grant_customer_connection_bonus(self, customer_user, is_new_relationship):
        """
        Grant 3-month premium membership to customer on first logistics connection
        
        Args:
            customer_user: Customer who connected with logistics
            is_new_relationship: Whether this is a new relationship
            
        Returns:
            dict: Bonus grant result
        """
        try:
            # Only grant bonus if this is a new relationship and customer hasn't received it before
            if not is_new_relationship:
                return {'success': False, 'reason': 'Not a new relationship'}
            
            # Check if customer already received connection bonus
            if customer_user.connection_bonus_granted:
                return {'success': False, 'reason': 'Customer already received connection bonus'}
            
            # Check if customer has any existing relationships (this should be their first)
            existing_relationships = ClientRelationship.objects.filter(customer=customer_user).count()
            
            if existing_relationships > 1:  # More than the one just created
                return {'success': False, 'reason': 'Customer already has other logistics connections'}
            
            # Grant 3-month premium membership to customer
            customer_user.premium_bonus_months = getattr(customer_user, 'premium_bonus_months', 0) + 3
            customer_user.connection_bonus_granted = True
            customer_user.save()
            
            # Log bonus grant
            log_user_action("customer_connection_bonus_granted", customer_user.id, {
                "bonus_months": 3,
                "total_premium_months": customer_user.premium_bonus_months
            })
            
            return {
                'success': True,
                'bonus_months': 3,
                'total_premium_months': customer_user.premium_bonus_months
            }
            
        except Exception as e:
            referral_logger.error(f"Error granting customer connection bonus: {str(e)}")
            return {
                'success': False,
                'error': 'An error occurred while granting the bonus'
            }
    
    def validate_referral_code(self, referral_code):
        """
        Validate referral code and return logistics provider
        
        Args:
            referral_code: The code to validate
            
        Returns:
            dict: Validation result
        """
        if not referral_code:
            return {'valid': False, 'error': 'Referral code is required'}
        
        # Clean the code
        referral_code = referral_code.strip().lower()
        
        try:
            # Find logistics provider by referral code
            logistics_user = User.objects.get(
                referral_code=referral_code,
                user_type='LOGISTICS',
                is_active=True
            )
            
            return {
                'valid': True,
                'logistics_user': logistics_user,
                'referral_code': referral_code
            }
            
        except User.DoesNotExist:
            return {'valid': False, 'error': 'Invalid referral code'}
    
    def get_client_list(self, logistics_user):
        """
        Get list of clients for a logistics provider
        
        Args:
            logistics_user: Logistics provider
            
        Returns:
            QuerySet: Client relationships
        """
        return ClientRelationship.objects.filter(
            logistics_provider=logistics_user
        ).select_related('customer').order_by('-last_interaction')
    
    def get_logistics_partners(self, customer_user):
        """
        Get list of logistics partners for a customer
        
        Args:
            customer_user: Customer
            
        Returns:
            QuerySet: Logistics relationships
        """
        return ClientRelationship.objects.filter(
            customer=customer_user
        ).select_related('logistics_provider').order_by('-last_interaction')
    
    def get_referral_stats(self, logistics_user):
        """
        Get referral statistics for a logistics provider
        
        Args:
            logistics_user: Logistics provider
            
        Returns:
            dict: Referral statistics
        """
        referrals = Referral.objects.filter(logistics_provider=logistics_user)
        
        return {
            'total_referrals': referrals.count(),
            'successful_referrals': referrals.filter(bonus_awarded=True).count(),
            'pending_referrals': referrals.filter(bonus_awarded=False).count(),
            'total_bonus_months': logistics_user.premium_bonus_months,
            'first_referrals': referrals.filter(is_first_referral=True, bonus_awarded=True).count()
        }

# Global instance
    def process_first_payment_bonus(self, customer_user):
        """
        Process referral bonus when customer makes their first successful payment
        
        Args:
            customer_user: Customer who made the payment
            
        Returns:
            dict: Result of bonus processing
        """
        try:
            # Check if customer is eligible for referral bonus
            if not customer_user.referral_bonus_eligible or not customer_user.referred_by_user:
                return {
                    'success': False,
                    'message': 'Customer not eligible for referral bonus'
                }
            
            # Check if customer has already been marked as having made first payment
            if customer_user.first_payment_done:
                return {
                    'success': False,
                    'message': 'Referral bonus already processed for this customer'
                }
            
            # Check if any referral bonus has already been granted for this customer
            if customer_user.referral_bonus_granted:
                return {
                    'success': False,
                    'message': 'Referral bonus has already been granted for this customer'
                }
            
            referring_user = customer_user.referred_by_user
            
            # Find the referral record
            referral = Referral.objects.filter(
                logistics_provider=referring_user,
                referred_customer=customer_user
            ).first()
            
            if not referral:
                return {
                    'success': False,
                    'message': 'Referral record not found'
                }
            
            # Determine bonus amount
            settings = self.settings
            if referral.is_first_referral:
                bonus_months = settings.first_referral_bonus_months
            else:
                bonus_months = settings.subsequent_referral_bonus_months
            
            # Award bonus to referring logistics provider
            referring_user.premium_bonus_months += bonus_months
            referring_user.save()
            
            # Mark referral as bonus awarded
            referral.bonus_awarded = True
            referral.save()
            
            # Mark customer as having made first payment and bonus granted
            customer_user.first_payment_done = True
            customer_user.referral_bonus_granted = True
            customer_user.save()
            
            return {
                'success': True,
                'message': f'Referral bonus of {bonus_months} months awarded to {referring_user.company_name}',
                'bonus_months': bonus_months,
                'referring_user': referring_user
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'Error processing bonus: {str(e)}'
            }

referral_manager = ReferralSystemManager()