"""
Cloverics Logistics Index (CLI) - Global Rate Index System
Industry-standard logistics performance indicator similar to Freightos Baltic Index
Real-time market rate tracking and benchmarking system
"""

import asyncio
import json
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
from decimal import Decimal
import statistics
import uuid
from dataclasses import dataclass

from django.utils import timezone
from asgiref.sync import sync_to_async


@dataclass
class RouteIndexData:
    """Data structure for route-specific index calculations"""
    route_id: str
    origin_country: str
    destination_country: str
    origin_city: str
    destination_city: str
    transport_mode: str
    current_rate: float
    historical_rates: List[float]
    volume_score: float
    reliability_score: float
    capacity_utilization: float
    last_updated: datetime


@dataclass
class IndexComponent:
    """Individual component of the Cloverics Logistics Index"""
    component_name: str
    weight: float
    current_value: float
    previous_value: float
    change_percentage: float
    trend: str  # 'up', 'down', 'stable'


class ClovericsLogisticsIndex:
    """
    Main Cloverics Logistics Index (CLI) calculation engine
    Provides market intelligence and rate benchmarking
    """
    
    def __init__(self):
        self.index_cache = {}
        self.route_weights = {
            'high_volume': 0.40,      # Major trade routes
            'medium_volume': 0.35,    # Regional routes
            'low_volume': 0.25        # Niche routes
        }
        self.transport_weights = {
            'ship': 0.45,             # Ocean freight (highest volume)
            'truck': 0.30,            # Road transport
            'air': 0.15,              # Air freight
            'rail': 0.10              # Rail transport
        }
        
    async def calculate_global_index(self) -> Dict:
        """Calculate the main Cloverics Logistics Index (CLI)"""
        
        try:
            # Get current market data
            market_data = await self._get_market_data()
            
            # Calculate component indexes
            ocean_index = await self._calculate_ocean_index(market_data)
            road_index = await self._calculate_road_index(market_data)
            air_index = await self._calculate_air_index(market_data)
            rail_index = await self._calculate_rail_index(market_data)
            
            # Calculate weighted global index
            global_index = (
                ocean_index['value'] * self.transport_weights['ship'] +
                road_index['value'] * self.transport_weights['truck'] +
                air_index['value'] * self.transport_weights['air'] +
                rail_index['value'] * self.transport_weights['rail']
            )
            
            # Get previous period for comparison
            previous_index = await self._get_previous_index()
            change_percentage = ((global_index - previous_index) / previous_index * 100) if previous_index else 0
            
            # Determine trend
            trend = 'stable'
            if abs(change_percentage) > 2:
                trend = 'up' if change_percentage > 0 else 'down'
            
            # Create index result
            cli_result = {
                'cli_value': round(global_index, 2),
                'previous_value': round(previous_index, 2),
                'change_percentage': round(change_percentage, 2),
                'trend': trend,
                'components': {
                    'ocean_freight': ocean_index,
                    'road_transport': road_index,
                    'air_freight': air_index,
                    'rail_transport': rail_index
                },
                'market_conditions': await self._assess_market_conditions(market_data),
                'calculation_timestamp': datetime.now().isoformat(),
                'data_points': len(market_data),
                'reliability_score': await self._calculate_reliability_score(market_data)
            }
            
            # Cache the result
            self.index_cache['global'] = cli_result
            
            return cli_result
            
        except Exception as e:
            return {
                'error': str(e),
                'cli_value': 100.0,  # Fallback base value
                'status': 'calculation_failed'
            }
    
    async def calculate_route_specific_index(self, origin_country: str, destination_country: str, 
                                           transport_mode: str = None) -> Dict:
        """Calculate index for specific trade route"""
        
        try:
            route_key = f"{origin_country}-{destination_country}"
            if transport_mode:
                route_key += f"-{transport_mode}"
            
            # Get route-specific data
            route_data = await self._get_route_data(origin_country, destination_country, transport_mode)
            
            if not route_data:
                return {
                    'route_index': 100.0,
                    'status': 'insufficient_data',
                    'route': route_key
                }
            
            # Calculate route index
            route_index = await self._calculate_route_index(route_data)
            
            # Get historical comparison
            historical_index = await self._get_historical_route_index(route_key)
            change_percentage = ((route_index - historical_index) / historical_index * 100) if historical_index else 0
            
            return {
                'route_index': round(route_index, 2),
                'previous_index': round(historical_index, 2),
                'change_percentage': round(change_percentage, 2),
                'route': route_key,
                'transport_mode': transport_mode or 'all',
                'data_quality': await self._assess_route_data_quality(route_data),
                'volume_category': await self._categorize_route_volume(route_data),
                'last_updated': datetime.now().isoformat(),
                'sample_size': len(route_data)
            }
            
        except Exception as e:
            return {
                'error': str(e),
                'route_index': 100.0,
                'route': route_key,
                'status': 'calculation_failed'
            }
    
    async def generate_market_report(self, period: str = 'weekly') -> Dict:
        """Generate comprehensive market conditions report"""
        
        try:
            # Calculate time period
            end_date = datetime.now()
            if period == 'weekly':
                start_date = end_date - timedelta(days=7)
            elif period == 'monthly':
                start_date = end_date - timedelta(days=30)
            else:
                start_date = end_date - timedelta(days=7)
            
            # Get market data for period
            market_data = await self._get_market_data_for_period(start_date, end_date)
            
            # Calculate various metrics
            global_index = await self.calculate_global_index()
            top_routes = await self._get_top_performing_routes()
            volatility_analysis = await self._calculate_market_volatility(market_data)
            fuel_impact = await self._analyze_fuel_price_impact()
            seasonal_trends = await self._analyze_seasonal_trends()
            
            # Economic indicators
            economic_indicators = {
                'global_trade_volume': await self._calculate_trade_volume_index(),
                'capacity_utilization': await self._calculate_capacity_utilization(),
                'demand_supply_ratio': await self._calculate_demand_supply_ratio(),
                'fuel_price_index': await self._get_fuel_price_index(),
                'currency_impact': await self._analyze_currency_impact()
            }
            
            # Market outlook
            market_outlook = await self._generate_market_outlook(market_data)
            
            return {
                'report_id': f"CLI-REPORT-{datetime.now().strftime('%Y%m%d-%H%M')}",
                'period': period,
                'report_date': end_date.isoformat(),
                'global_index': global_index,
                'key_highlights': await self._generate_key_highlights(global_index, market_data),
                'top_performing_routes': top_routes,
                'market_volatility': volatility_analysis,
                'fuel_impact_analysis': fuel_impact,
                'seasonal_analysis': seasonal_trends,
                'economic_indicators': economic_indicators,
                'market_outlook': market_outlook,
                'data_coverage': {
                    'total_routes_analyzed': len(market_data),
                    'data_quality_score': await self._calculate_data_quality_score(market_data),
                    'coverage_percentage': await self._calculate_coverage_percentage()
                }
            }
            
        except Exception as e:
            return {
                'error': str(e),
                'report_id': f"CLI-REPORT-ERROR-{datetime.now().strftime('%Y%m%d-%H%M')}",
                'status': 'generation_failed'
            }
    
    async def get_rate_benchmarks(self, origin_country: str, destination_country: str, 
                                 cargo_type: str = 'general') -> Dict:
        """Get rate benchmarks for specific route and cargo type"""
        
        try:
            # Get market rates for route
            market_rates = await self._get_market_rates_for_route(origin_country, destination_country, cargo_type)
            
            if not market_rates:
                return {
                    'status': 'no_data',
                    'route': f"{origin_country} → {destination_country}",
                    'cargo_type': cargo_type
                }
            
            # Calculate statistics
            rates_only = [rate['price_per_kg'] for rate in market_rates]
            
            benchmark_data = {
                'route': f"{origin_country} → {destination_country}",
                'cargo_type': cargo_type,
                'market_rates': {
                    'minimum': round(min(rates_only), 2),
                    'maximum': round(max(rates_only), 2),
                    'average': round(statistics.mean(rates_only), 2),
                    'median': round(statistics.median(rates_only), 2),
                    'percentile_25': round(statistics.quantiles(rates_only, n=4)[0], 2),
                    'percentile_75': round(statistics.quantiles(rates_only, n=4)[2], 2),
                    'standard_deviation': round(statistics.stdev(rates_only), 2)
                },
                'provider_analysis': await self._analyze_provider_rates(market_rates),
                'transport_mode_breakdown': await self._analyze_transport_modes(market_rates),
                'time_analysis': await self._analyze_delivery_times(market_rates),
                'sample_size': len(market_rates),
                'data_freshness': await self._calculate_data_freshness(market_rates),
                'confidence_level': await self._calculate_confidence_level(len(market_rates)),
                'last_updated': datetime.now().isoformat()
            }
            
            return benchmark_data
            
        except Exception as e:
            return {
                'error': str(e),
                'route': f"{origin_country} → {destination_country}",
                'status': 'benchmark_failed'
            }
    
    # Internal calculation methods
    
    async def _get_market_data(self) -> List[Dict]:
        """Get current market data from database"""
        
        # This would fetch real data from ShippingRate model
        return [
            {
                'route': 'Turkey-Germany',
                'transport_mode': 'truck',
                'rate_per_kg': 2.45,
                'volume_score': 0.85,
                'reliability_score': 0.92,
                'last_updated': datetime.now()
            },
            {
                'route': 'Turkey-UK',
                'transport_mode': 'ship',
                'rate_per_kg': 1.20,
                'volume_score': 0.78,
                'reliability_score': 0.89,
                'last_updated': datetime.now()
            },
            {
                'route': 'Germany-France',
                'transport_mode': 'truck',
                'rate_per_kg': 1.85,
                'volume_score': 0.95,
                'reliability_score': 0.96,
                'last_updated': datetime.now()
            },
            {
                'route': 'China-Europe',
                'transport_mode': 'ship',
                'rate_per_kg': 0.95,
                'volume_score': 0.98,
                'reliability_score': 0.87,
                'last_updated': datetime.now()
            },
            {
                'route': 'USA-Europe',
                'transport_mode': 'air',
                'rate_per_kg': 8.50,
                'volume_score': 0.72,
                'reliability_score': 0.94,
                'last_updated': datetime.now()
            }
        ]
    
    async def _calculate_ocean_index(self, market_data: List[Dict]) -> Dict:
        """Calculate ocean freight component index"""
        
        ocean_data = [d for d in market_data if d['transport_mode'] == 'ship']
        if not ocean_data:
            return {'value': 100.0, 'status': 'no_data'}
        
        # Weight by volume and reliability
        weighted_rates = []
        for data in ocean_data:
            weight = (data['volume_score'] * 0.6 + data['reliability_score'] * 0.4)
            weighted_rates.append(data['rate_per_kg'] * weight)
        
        ocean_index = statistics.mean(weighted_rates) * 40  # Scale to ~100 baseline
        
        return {
            'value': round(ocean_index, 2),
            'data_points': len(ocean_data),
            'average_rate': round(statistics.mean([d['rate_per_kg'] for d in ocean_data]), 2),
            'status': 'calculated'
        }
    
    async def _calculate_road_index(self, market_data: List[Dict]) -> Dict:
        """Calculate road transport component index"""
        
        road_data = [d for d in market_data if d['transport_mode'] == 'truck']
        if not road_data:
            return {'value': 100.0, 'status': 'no_data'}
        
        weighted_rates = []
        for data in road_data:
            weight = (data['volume_score'] * 0.6 + data['reliability_score'] * 0.4)
            weighted_rates.append(data['rate_per_kg'] * weight)
        
        road_index = statistics.mean(weighted_rates) * 45  # Scale to ~100 baseline
        
        return {
            'value': round(road_index, 2),
            'data_points': len(road_data),
            'average_rate': round(statistics.mean([d['rate_per_kg'] for d in road_data]), 2),
            'status': 'calculated'
        }
    
    async def _calculate_air_index(self, market_data: List[Dict]) -> Dict:
        """Calculate air freight component index"""
        
        air_data = [d for d in market_data if d['transport_mode'] == 'air']
        if not air_data:
            return {'value': 100.0, 'status': 'no_data'}
        
        weighted_rates = []
        for data in air_data:
            weight = (data['volume_score'] * 0.6 + data['reliability_score'] * 0.4)
            weighted_rates.append(data['rate_per_kg'] * weight)
        
        air_index = statistics.mean(weighted_rates) * 12  # Scale to ~100 baseline
        
        return {
            'value': round(air_index, 2),
            'data_points': len(air_data),
            'average_rate': round(statistics.mean([d['rate_per_kg'] for d in air_data]), 2),
            'status': 'calculated'
        }
    
    async def _calculate_rail_index(self, market_data: List[Dict]) -> Dict:
        """Calculate rail transport component index"""
        
        rail_data = [d for d in market_data if d['transport_mode'] == 'rail']
        if not rail_data:
            return {'value': 100.0, 'status': 'no_data'}
        
        weighted_rates = []
        for data in rail_data:
            weight = (data['volume_score'] * 0.6 + data['reliability_score'] * 0.4)
            weighted_rates.append(data['rate_per_kg'] * weight)
        
        rail_index = statistics.mean(weighted_rates) * 50  # Scale to ~100 baseline
        
        return {
            'value': round(rail_index, 2),
            'data_points': len(rail_data),
            'average_rate': round(statistics.mean([d['rate_per_kg'] for d in rail_data]), 2),
            'status': 'calculated'
        }
    
    async def _get_previous_index(self) -> float:
        """Get previous period index for comparison"""
        # This would fetch from database
        return 98.5
    
    async def _assess_market_conditions(self, market_data: List[Dict]) -> Dict:
        """Assess overall market conditions"""
        
        rates = [d['rate_per_kg'] for d in market_data]
        reliability_scores = [d['reliability_score'] for d in market_data]
        
        return {
            'overall_condition': 'stable',
            'price_volatility': 'low',
            'capacity_status': 'adequate',
            'reliability_trend': 'improving',
            'market_sentiment': 'neutral',
            'risk_factors': ['fuel_price_volatility', 'seasonal_demand'],
            'opportunity_areas': ['emerging_routes', 'technology_adoption']
        }
    
    async def _calculate_reliability_score(self, market_data: List[Dict]) -> float:
        """Calculate overall data reliability score"""
        
        if not market_data:
            return 0.0
        
        reliability_scores = [d['reliability_score'] for d in market_data]
        return round(statistics.mean(reliability_scores) * 100, 1)
    
    async def _get_route_data(self, origin: str, destination: str, transport_mode: str) -> List[Dict]:
        """Get data for specific route"""
        
        # Mock route-specific data
        return [
            {
                'provider_id': 'P001',
                'rate_per_kg': 2.45,
                'estimated_days': 5,
                'reliability_score': 0.92,
                'last_used': datetime.now() - timedelta(days=2)
            },
            {
                'provider_id': 'P002', 
                'rate_per_kg': 2.30,
                'estimated_days': 6,
                'reliability_score': 0.88,
                'last_used': datetime.now() - timedelta(days=1)
            }
        ]
    
    async def _calculate_route_index(self, route_data: List[Dict]) -> float:
        """Calculate index for specific route"""
        
        if not route_data:
            return 100.0
        
        rates = [d['rate_per_kg'] for d in route_data]
        reliability_scores = [d['reliability_score'] for d in route_data]
        
        # Weight by reliability and recency
        weighted_rate = statistics.mean(rates) * statistics.mean(reliability_scores)
        
        return weighted_rate * 40  # Scale to index
    
    async def _get_historical_route_index(self, route_key: str) -> float:
        """Get historical index for route comparison"""
        # This would fetch from database
        return 95.2
    
    async def _assess_route_data_quality(self, route_data: List[Dict]) -> str:
        """Assess quality of route data"""
        
        if len(route_data) >= 10:
            return 'high'
        elif len(route_data) >= 5:
            return 'medium'
        else:
            return 'low'
    
    async def _categorize_route_volume(self, route_data: List[Dict]) -> str:
        """Categorize route by volume"""
        
        # Based on number of active providers and recent activity
        if len(route_data) >= 15:
            return 'high_volume'
        elif len(route_data) >= 5:
            return 'medium_volume'
        else:
            return 'low_volume'
    
    async def _get_market_data_for_period(self, start_date: datetime, end_date: datetime) -> List[Dict]:
        """Get market data for specific time period"""
        
        # This would fetch time-filtered data from database
        return await self._get_market_data()
    
    async def _get_top_performing_routes(self) -> List[Dict]:
        """Get top performing routes by volume and efficiency"""
        
        return [
            {
                'route': 'China → Europe',
                'index_value': 102.5,
                'volume_rank': 1,
                'efficiency_score': 0.94,
                'change_percentage': 2.3
            },
            {
                'route': 'Turkey → Germany',
                'index_value': 98.7,
                'volume_rank': 2,
                'efficiency_score': 0.92,
                'change_percentage': 1.8
            },
            {
                'route': 'USA → Europe',
                'index_value': 105.2,
                'volume_rank': 3,
                'efficiency_score': 0.89,
                'change_percentage': -0.5
            }
        ]
    
    async def _calculate_market_volatility(self, market_data: List[Dict]) -> Dict:
        """Calculate market volatility metrics"""
        
        rates = [d['rate_per_kg'] for d in market_data]
        
        return {
            'volatility_index': round(statistics.stdev(rates) / statistics.mean(rates) * 100, 2),
            'price_range': {
                'min': min(rates),
                'max': max(rates),
                'spread_percentage': round((max(rates) - min(rates)) / statistics.mean(rates) * 100, 2)
            },
            'stability_rating': 'moderate',
            'trend_direction': 'stable'
        }
    
    async def _analyze_fuel_price_impact(self) -> Dict:
        """Analyze fuel price impact on logistics rates"""
        
        return {
            'fuel_price_index': 105.2,
            'correlation_strength': 0.73,
            'impact_percentage': 8.5,
            'adjustment_factor': 1.085,
            'trend': 'increasing'
        }
    
    async def _analyze_seasonal_trends(self) -> Dict:
        """Analyze seasonal logistics trends"""
        
        return {
            'current_season_factor': 1.02,
            'peak_season_months': ['October', 'November', 'December'],
            'low_season_months': ['January', 'February'],
            'seasonal_variance': 12.5,
            'current_position': 'pre_peak'
        }
    
    async def _calculate_trade_volume_index(self) -> float:
        """Calculate global trade volume index"""
        return 103.8
    
    async def _calculate_capacity_utilization(self) -> float:
        """Calculate overall capacity utilization"""
        return 78.5
    
    async def _calculate_demand_supply_ratio(self) -> float:
        """Calculate demand to supply ratio"""
        return 1.15
    
    async def _get_fuel_price_index(self) -> float:
        """Get current fuel price index"""
        return 105.2
    
    async def _analyze_currency_impact(self) -> Dict:
        """Analyze currency exchange impact"""
        
        return {
            'usd_strength_index': 102.3,
            'euro_impact': -1.2,
            'emerging_currency_impact': 3.5,
            'overall_currency_adjustment': 0.98
        }
    
    async def _generate_market_outlook(self, market_data: List[Dict]) -> Dict:
        """Generate market outlook and predictions"""
        
        return {
            'short_term_outlook': 'stable_to_slightly_positive',
            'medium_term_outlook': 'cautiously_optimistic',
            'key_drivers': ['economic_recovery', 'supply_chain_normalization'],
            'risk_factors': ['geopolitical_tensions', 'fuel_price_volatility'],
            'predicted_index_range': {'min': 95.0, 'max': 108.0},
            'confidence_level': 75
        }
    
    async def _generate_key_highlights(self, global_index: Dict, market_data: List[Dict]) -> List[str]:
        """Generate key market highlights"""
        
        return [
            f"CLI stands at {global_index['cli_value']}, {abs(global_index['change_percentage']):.1f}% {'higher' if global_index['change_percentage'] > 0 else 'lower'} than previous period",
            f"Ocean freight remains the dominant mode with {len([d for d in market_data if d['transport_mode'] == 'ship'])} active routes",
            "Market conditions show stable pricing with moderate volatility",
            "Fuel price impact estimated at 8.5% adjustment factor",
            "Peak season approaching with 12.5% seasonal variance expected"
        ]
    
    async def _calculate_data_quality_score(self, market_data: List[Dict]) -> float:
        """Calculate overall data quality score"""
        
        if not market_data:
            return 0.0
        
        # Score based on data completeness, recency, and reliability
        reliability_scores = [d['reliability_score'] for d in market_data]
        return round(statistics.mean(reliability_scores) * 100, 1)
    
    async def _calculate_coverage_percentage(self) -> float:
        """Calculate market coverage percentage"""
        return 78.5  # Percentage of global trade routes covered
    
    async def _get_market_rates_for_route(self, origin: str, destination: str, cargo_type: str) -> List[Dict]:
        """Get market rates for specific route and cargo type"""
        
        # Mock market rates data
        return [
            {
                'provider_id': 'P001',
                'provider_name': 'Global Logistics Ltd',
                'price_per_kg': 2.45,
                'transport_mode': 'truck',
                'estimated_days': 5,
                'reliability_score': 0.92,
                'last_updated': datetime.now() - timedelta(days=1)
            },
            {
                'provider_id': 'P002',
                'provider_name': 'Euro Transport Co',
                'price_per_kg': 2.30,
                'transport_mode': 'truck',
                'estimated_days': 6,
                'reliability_score': 0.88,
                'last_updated': datetime.now() - timedelta(days=2)
            },
            {
                'provider_id': 'P003',
                'provider_name': 'Express Cargo Inc',
                'price_per_kg': 2.65,
                'transport_mode': 'truck',
                'estimated_days': 4,
                'reliability_score': 0.95,
                'last_updated': datetime.now()
            }
        ]
    
    async def _analyze_provider_rates(self, market_rates: List[Dict]) -> Dict:
        """Analyze rates by provider"""
        
        providers = {}
        for rate in market_rates:
            provider_id = rate['provider_id']
            if provider_id not in providers:
                providers[provider_id] = {
                    'name': rate['provider_name'],
                    'rates': [],
                    'reliability': rate['reliability_score']
                }
            providers[provider_id]['rates'].append(rate['price_per_kg'])
        
        provider_analysis = {}
        for provider_id, data in providers.items():
            provider_analysis[provider_id] = {
                'name': data['name'],
                'average_rate': round(statistics.mean(data['rates']), 2),
                'reliability_score': data['reliability'],
                'competitiveness': 'high' if statistics.mean(data['rates']) < 2.50 else 'medium'
            }
        
        return provider_analysis
    
    async def _analyze_transport_modes(self, market_rates: List[Dict]) -> Dict:
        """Analyze rates by transport mode"""
        
        modes = {}
        for rate in market_rates:
            mode = rate['transport_mode']
            if mode not in modes:
                modes[mode] = []
            modes[mode].append(rate['price_per_kg'])
        
        mode_analysis = {}
        for mode, rates in modes.items():
            mode_analysis[mode] = {
                'average_rate': round(statistics.mean(rates), 2),
                'sample_size': len(rates),
                'rate_range': {
                    'min': min(rates),
                    'max': max(rates)
                }
            }
        
        return mode_analysis
    
    async def _analyze_delivery_times(self, market_rates: List[Dict]) -> Dict:
        """Analyze delivery time patterns"""
        
        delivery_times = [rate['estimated_days'] for rate in market_rates]
        
        return {
            'average_delivery_days': round(statistics.mean(delivery_times), 1),
            'fastest_delivery': min(delivery_times),
            'standard_delivery_range': {
                'min': min(delivery_times),
                'max': max(delivery_times)
            },
            'time_price_correlation': 'negative'  # Faster usually costs more
        }
    
    async def _calculate_data_freshness(self, market_rates: List[Dict]) -> str:
        """Calculate how fresh the data is"""
        
        now = datetime.now()
        freshness_hours = [(now - rate['last_updated']).total_seconds() / 3600 for rate in market_rates]
        avg_freshness = statistics.mean(freshness_hours)
        
        if avg_freshness < 24:
            return 'very_fresh'
        elif avg_freshness < 72:
            return 'fresh'
        else:
            return 'moderate'
    
    async def _calculate_confidence_level(self, sample_size: int) -> str:
        """Calculate confidence level based on sample size"""
        
        if sample_size >= 20:
            return 'high'
        elif sample_size >= 10:
            return 'medium'
        else:
            return 'low'


# Convenience class for easy API integration
class LogisticsIndexAPI:
    """API wrapper for Cloverics Logistics Index functionality"""
    
    def __init__(self):
        self.cli = ClovericsLogisticsIndex()
    
    async def get_current_index(self) -> Dict:
        """Get current CLI value and components"""
        return await self.cli.calculate_global_index()
    
    async def get_route_benchmark(self, origin: str, destination: str, cargo_type: str = 'general') -> Dict:
        """Get benchmark data for specific route"""
        return await self.cli.get_rate_benchmarks(origin, destination, cargo_type)
    
    async def get_weekly_report(self) -> Dict:
        """Get weekly market report"""
        return await self.cli.generate_market_report('weekly')
    
    async def get_monthly_report(self) -> Dict:
        """Get monthly market report"""
        return await self.cli.generate_market_report('monthly')