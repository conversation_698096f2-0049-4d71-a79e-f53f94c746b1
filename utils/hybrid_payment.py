"""
Cloverics Hybrid Payment Flow

This module handles the dual-payment system for shipments:
1. Stripe payments (automatic)
2. Bank transfers (manual with document upload and platform fee protection)
"""

import os
import datetime
from decimal import Decimal
from django.utils import timezone
from django.conf import settings
from core.models import User
from core.notification_models import Notification
from shipments.models import Shipment
import stripe

# Configure Stripe
stripe.api_key = os.environ.get('STRIPE_SECRET_KEY')

def calculate_platform_fee(total_amount, fee_percentage=5.0):
    """
    Calculate platform fee based on total shipment amount.
    
    Args:
        total_amount (Decimal): Total shipment cost
        fee_percentage (float): Platform fee percentage (default 5%)
    
    Returns:
        Decimal: Platform fee amount
    """
    if not isinstance(total_amount, Decimal):
        total_amount = Decimal(str(total_amount))
    
    fee_percentage = Decimal(str(fee_percentage))
    platform_fee = (total_amount * fee_percentage) / Decimal('100')
    
    # Round to 2 decimal places
    return platform_fee.quantize(Decimal('0.01'))

def create_stripe_customer(user):
    """
    Create or retrieve Stripe customer for user.
    
    Args:
        user (User): User instance
    
    Returns:
        str: Stripe customer ID
    """
    if user.stripe_customer_id:
        return user.stripe_customer_id
    
    try:
        customer = stripe.Customer.create(
            email=user.email,
            name=user.company_name,
            metadata={
                'user_id': user.id,
                'user_type': user.user_type,
                'company_name': user.company_name
            }
        )
        
        user.stripe_customer_id = customer.id
        user.save(update_fields=['stripe_customer_id'])
        
        return customer.id
        
    except stripe.error.StripeError as e:
        raise Exception(f"Failed to create Stripe customer: {str(e)}")

def charge_platform_fee(shipment):
    """
    Charge platform fee to customer's registered card.
    
    Args:
        shipment (Shipment): Shipment instance
    
    Returns:
        dict: Result with success status and details
    """
    if shipment.platform_fee_paid:
        return {
            'success': True,
            'message': 'Platform fee already paid',
            'already_paid': True
        }
    
    customer = shipment.customer
    
    if not customer.has_registered_card or not customer.stripe_customer_id:
        return {
            'success': False,
            'message': 'No registered payment card found',
            'error_code': 'NO_CARD'
        }
    
    platform_fee = calculate_platform_fee(shipment.total_price)
    
    try:
        # Get customer's default payment method
        payment_methods = stripe.PaymentMethod.list(
            customer=customer.stripe_customer_id,
            type="card"
        )
        
        if not payment_methods.data:
            return {
                'success': False,
                'message': 'No payment method available',
                'error_code': 'NO_PAYMENT_METHOD'
            }
        
        # Create payment intent for platform fee
        payment_intent = stripe.PaymentIntent.create(
            amount=int(platform_fee * 100),  # Convert to cents
            currency='usd',
            customer=customer.stripe_customer_id,
            payment_method=payment_methods.data[0].id,
            confirm=True,
            automatic_payment_methods={
                'enabled': True,
                'allow_redirects': 'never'
            },
            metadata={
                'shipment_id': shipment.id,
                'tracking_number': shipment.tracking_number,
                'fee_type': 'platform_fee',
                'payment_method': 'bank_transfer'
            }
        )
        
        if payment_intent.status == 'succeeded':
            # Update shipment
            shipment.platform_fee_paid = True
            shipment.platform_fee_charged_at = timezone.now()
            shipment.suspension_status = False
            shipment.save(update_fields=[
                'platform_fee_paid', 
                'platform_fee_charged_at', 
                'suspension_status'
            ])
            
            # Create notification for customer
            Notification.objects.create(
                user=customer,
                title="Platform Fee Charged",
                message=f"Platform fee of ${platform_fee} has been charged for shipment {shipment.tracking_number}.",
                notification_type='PAYMENT',
                priority='MEDIUM'
            )
            
            # Create notification for logistics provider
            Notification.objects.create(
                user=shipment.logistics_provider,
                title="Platform Fee Collected",
                message=f"Platform fee has been successfully collected for shipment {shipment.tracking_number}. Payment is now complete.",
                notification_type='PAYMENT',
                priority='MEDIUM'
            )
            
            return {
                'success': True,
                'message': f'Platform fee of ${platform_fee} charged successfully',
                'amount': platform_fee,
                'payment_intent_id': payment_intent.id
            }
        else:
            shipment.platform_fee_attempts += 1
            shipment.save(update_fields=['platform_fee_attempts'])
            
            return {
                'success': False,
                'message': f'Payment failed with status: {payment_intent.status}',
                'error_code': 'PAYMENT_FAILED'
            }
            
    except stripe.error.CardError as e:
        # Card was declined
        shipment.platform_fee_attempts += 1
        shipment.suspension_status = True
        shipment.save(update_fields=['platform_fee_attempts', 'suspension_status'])
        
        return {
            'success': False,
            'message': f'Card declined: {e.user_message}',
            'error_code': 'CARD_DECLINED'
        }
        
    except stripe.error.StripeError as e:
        shipment.platform_fee_attempts += 1
        shipment.save(update_fields=['platform_fee_attempts'])
        
        return {
            'success': False,
            'message': f'Payment processing error: {str(e)}',
            'error_code': 'STRIPE_ERROR'
        }

def approve_bank_transfer(shipment, approving_user):
    """
    Approve bank transfer and trigger platform fee collection.
    
    Args:
        shipment (Shipment): Shipment instance
        approving_user (User): Logistics provider approving the transfer
    
    Returns:
        dict: Result with success status and details
    """
    if shipment.bank_transfer_status == 'approved':
        return {
            'success': False,
            'message': 'Bank transfer already approved'
        }
    
    if shipment.payment_method != 'bank_transfer':
        return {
            'success': False,
            'message': 'This shipment does not use bank transfer payment'
        }
    
    # Update bank transfer status
    shipment.bank_transfer_status = 'approved'
    shipment.bank_transfer_approved_at = timezone.now()
    shipment.save(update_fields=['bank_transfer_status', 'bank_transfer_approved_at'])
    
    # Notify customer of approval
    Notification.objects.create(
        user=shipment.customer,
        title="Bank Transfer Approved",
        message=f"Your bank transfer for shipment {shipment.tracking_number} has been approved by {approving_user.company_name}.",
        notification_type='PAYMENT',
        priority='HIGH'
    )
    
    # Attempt to charge platform fee
    fee_result = charge_platform_fee(shipment)
    
    if fee_result['success']:
        return {
            'success': True,
            'message': 'Bank transfer approved and platform fee collected successfully',
            'fee_result': fee_result
        }
    else:
        # Bank transfer approved but platform fee failed
        shipment.suspension_status = True
        shipment.save(update_fields=['suspension_status'])
        
        # Notify customer of payment issue
        Notification.objects.create(
            user=shipment.customer,
            title="Platform Fee Payment Failed",
            message=f"Your bank transfer was approved, but platform fee payment failed for shipment {shipment.tracking_number}. Please update your payment method.",
            notification_type='PAYMENT',
            priority='HIGH'
        )
        
        # Notify admin
        admin_users = User.objects.filter(is_staff=True)
        for admin in admin_users:
            Notification.objects.create(
                user=admin,
                title="Suspended Booking - Payment Issue",
                message=f"Shipment {shipment.tracking_number} suspended due to platform fee payment failure.",
                notification_type='SYSTEM',
                priority='HIGH'
            )
        
        return {
            'success': False,
            'message': 'Bank transfer approved but platform fee payment failed',
            'fee_result': fee_result
        }

def reject_bank_transfer(shipment, rejecting_user, reason=""):
    """
    Reject bank transfer.
    
    Args:
        shipment (Shipment): Shipment instance
        rejecting_user (User): Logistics provider rejecting the transfer
        reason (str): Reason for rejection
    
    Returns:
        dict: Result with success status and details
    """
    if shipment.payment_method != 'bank_transfer':
        return {
            'success': False,
            'message': 'This shipment does not use bank transfer payment'
        }
    
    # Update bank transfer status
    shipment.bank_transfer_status = 'rejected'
    shipment.save(update_fields=['bank_transfer_status'])
    
    # Notify customer of rejection
    rejection_message = f"Your bank transfer for shipment {shipment.tracking_number} has been rejected by {rejecting_user.company_name}."
    if reason:
        rejection_message += f" Reason: {reason}"
    
    Notification.objects.create(
        user=shipment.customer,
        title="Bank Transfer Rejected",
        message=rejection_message,
        notification_type='PAYMENT',
        priority='HIGH'
    )
    
    return {
        'success': True,
        'message': 'Bank transfer rejected successfully'
    }

def get_suspended_bookings():
    """
    Get all suspended bookings due to payment issues.
    
    Returns:
        QuerySet: Suspended shipments
    """
    return Shipment.objects.filter(
        suspension_status=True,
        payment_method='bank_transfer'
    ).select_related('customer', 'logistics_provider')

def retry_platform_fee_payment(shipment):
    """
    Retry platform fee payment for suspended booking.
    
    Args:
        shipment (Shipment): Shipment instance
    
    Returns:
        dict: Result with success status and details
    """
    if not shipment.suspension_status:
        return {
            'success': False,
            'message': 'Booking is not suspended'
        }
    
    if shipment.bank_transfer_status != 'approved':
        return {
            'success': False,
            'message': 'Bank transfer must be approved first'
        }
    
    return charge_platform_fee(shipment)

def get_pending_bank_transfers(logistics_provider=None):
    """
    Get bank transfers pending approval.
    
    Args:
        logistics_provider (User): Filter by logistics provider
    
    Returns:
        QuerySet: Shipments with pending bank transfers
    """
    queryset = Shipment.objects.filter(
        payment_method='bank_transfer',
        bank_transfer_status='pending',
        bank_transfer_proof__isnull=False
    ).select_related('customer', 'logistics_provider')
    
    if logistics_provider:
        queryset = queryset.filter(logistics_provider=logistics_provider)
    
    return queryset