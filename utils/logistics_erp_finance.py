"""
Logistics ERP & Finance Management System
Comprehensive financial tools for logistics providers including invoicing, 
payment tracking, AR/AP management, and financial analytics
"""

import json
import datetime
import uuid
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from enum import Enum
import logging

logger = logging.getLogger(__name__)

class InvoiceStatus(Enum):
    DRAFT = "draft"
    SENT = "sent"
    VIEWED = "viewed"
    PARTIAL_PAID = "partial_paid"
    PAID = "paid"
    OVERDUE = "overdue"
    CANCELLED = "cancelled"
    DISPUTED = "disputed"

class PaymentStatus(Enum):
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    REFUNDED = "refunded"
    DISPUTED = "disputed"

class PaymentMethod(Enum):
    BANK_TRANSFER = "bank_transfer"
    CREDIT_CARD = "credit_card"
    CHECK = "check"
    CASH = "cash"
    DIGITAL_WALLET = "digital_wallet"

class RefundRequestStatus(Enum):
    PENDING = "pending"
    APPROVED = "approved"
    REJECTED = "rejected"
    PROCESSED = "processed"

@dataclass
class Invoice:
    """Invoice data structure"""
    invoice_id: str
    provider_id: int
    customer_id: int
    shipment_id: str
    invoice_number: str
    amount: float
    currency: str
    tax_amount: float
    total_amount: float
    status: InvoiceStatus
    issue_date: datetime.datetime
    due_date: datetime.datetime
    payment_terms: str
    line_items: List[Dict[str, Any]] = field(default_factory=list)
    notes: str = ""
    paid_amount: float = 0.0
    payment_date: Optional[datetime.datetime] = None
    created_at: datetime.datetime = field(default_factory=datetime.datetime.now)
    updated_at: datetime.datetime = field(default_factory=datetime.datetime.now)

@dataclass
class PaymentRecord:
    """Payment tracking record"""
    payment_id: str
    invoice_id: str
    provider_id: int
    customer_id: int
    amount: float
    currency: str
    method: PaymentMethod
    status: PaymentStatus
    transaction_id: str
    processed_at: Optional[datetime.datetime] = None
    notes: str = ""
    created_at: datetime.datetime = field(default_factory=datetime.datetime.now)

@dataclass
class RefundRequest:
    """Refund request management"""
    refund_id: str
    invoice_id: str
    payment_id: str
    provider_id: int
    customer_id: int
    requested_amount: float
    reason: str
    status: RefundRequestStatus
    admin_notes: str = ""
    processed_amount: float = 0.0
    processed_at: Optional[datetime.datetime] = None
    created_at: datetime.datetime = field(default_factory=datetime.datetime.now)

@dataclass
class FinancialSummary:
    """Financial summary data"""
    provider_id: int
    period_start: datetime.datetime
    period_end: datetime.datetime
    total_revenue: float
    total_expenses: float
    net_profit: float
    outstanding_receivables: float
    overdue_amount: float
    average_payment_days: float
    invoice_count: int
    paid_invoice_count: int

class LogisticsERPFinance:
    """Comprehensive ERP and finance management system for logistics providers"""
    
    def __init__(self):
        self.invoices = {}  # invoice_id -> Invoice
        self.payments = {}  # payment_id -> PaymentRecord
        self.refund_requests = {}  # refund_id -> RefundRequest
        self.financial_summaries = {}  # provider_id -> FinancialSummary
        self.payment_reminders = {}  # invoice_id -> reminder_data
        
    def create_invoice(self, provider_id: int, customer_id: int, shipment_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create new invoice for shipment"""
        try:
            # Generate invoice ID and number
            invoice_id = str(uuid.uuid4())
            invoice_number = f"INV-{datetime.datetime.now().strftime('%Y%m')}-{len(self.invoices) + 1:04d}"
            
            # Calculate amounts
            base_amount = float(shipment_data.get("amount", 0))
            tax_rate = float(shipment_data.get("tax_rate", 0.1))  # 10% default tax
            tax_amount = base_amount * tax_rate
            total_amount = base_amount + tax_amount
            
            # Set payment terms
            payment_terms = shipment_data.get("payment_terms", "Net 30")
            due_date = datetime.datetime.now() + datetime.timedelta(days=30)
            
            # Create line items
            line_items = [
                {
                    "description": f"Shipping service: {shipment_data.get('origin', 'Origin')} to {shipment_data.get('destination', 'Destination')}",
                    "quantity": 1,
                    "unit_price": base_amount,
                    "total": base_amount
                }
            ]
            
            # Create invoice
            invoice = Invoice(
                invoice_id=invoice_id,
                provider_id=provider_id,
                customer_id=customer_id,
                shipment_id=shipment_data.get("shipment_id", ""),
                invoice_number=invoice_number,
                amount=base_amount,
                currency=shipment_data.get("currency", "USD"),
                tax_amount=tax_amount,
                total_amount=total_amount,
                status=InvoiceStatus.DRAFT,
                issue_date=datetime.datetime.now(),
                due_date=due_date,
                payment_terms=payment_terms,
                line_items=line_items,
                notes=shipment_data.get("notes", "")
            )
            
            self.invoices[invoice_id] = invoice
            
            return {
                "success": True,
                "invoice_id": invoice_id,
                "invoice_number": invoice_number,
                "total_amount": total_amount,
                "currency": invoice.currency,
                "due_date": due_date.isoformat(),
                "status": invoice.status.value
            }
            
        except Exception as e:
            logger.error(f"Invoice creation error: {str(e)}")
            return {
                "success": False,
                "error": "Failed to create invoice"
            }
    
    def send_invoice(self, invoice_id: str) -> Dict[str, Any]:
        """Send invoice to customer"""
        try:
            invoice = self.invoices.get(invoice_id)
            if not invoice:
                return {
                    "success": False,
                    "error": "Invoice not found"
                }
            
            if invoice.status != InvoiceStatus.DRAFT:
                return {
                    "success": False,
                    "error": "Invoice has already been sent"
                }
            
            # Update invoice status
            invoice.status = InvoiceStatus.SENT
            invoice.updated_at = datetime.datetime.now()
            
            # Schedule payment reminder
            self._schedule_payment_reminder(invoice_id)
            
            return {
                "success": True,
                "invoice_id": invoice_id,
                "status": invoice.status.value,
                "sent_at": invoice.updated_at.isoformat()
            }
            
        except Exception as e:
            logger.error(f"Send invoice error: {str(e)}")
            return {
                "success": False,
                "error": "Failed to send invoice"
            }
    
    def record_payment(self, invoice_id: str, amount: float, method: PaymentMethod, 
                      transaction_id: str, notes: str = "") -> Dict[str, Any]:
        """Record payment for invoice"""
        try:
            invoice = self.invoices.get(invoice_id)
            if not invoice:
                return {
                    "success": False,
                    "error": "Invoice not found"
                }
            
            # Generate payment ID
            payment_id = str(uuid.uuid4())
            
            # Create payment record
            payment = PaymentRecord(
                payment_id=payment_id,
                invoice_id=invoice_id,
                provider_id=invoice.provider_id,
                customer_id=invoice.customer_id,
                amount=amount,
                currency=invoice.currency,
                method=method,
                status=PaymentStatus.COMPLETED,
                transaction_id=transaction_id,
                processed_at=datetime.datetime.now(),
                notes=notes
            )
            
            self.payments[payment_id] = payment
            
            # Update invoice
            invoice.paid_amount += amount
            invoice.payment_date = datetime.datetime.now()
            invoice.updated_at = datetime.datetime.now()
            
            # Update invoice status
            if invoice.paid_amount >= invoice.total_amount:
                invoice.status = InvoiceStatus.PAID
            elif invoice.paid_amount > 0:
                invoice.status = InvoiceStatus.PARTIAL_PAID
            
            return {
                "success": True,
                "payment_id": payment_id,
                "invoice_id": invoice_id,
                "amount_paid": amount,
                "remaining_balance": max(0, invoice.total_amount - invoice.paid_amount),
                "invoice_status": invoice.status.value
            }
            
        except Exception as e:
            logger.error(f"Payment recording error: {str(e)}")
            return {
                "success": False,
                "error": "Failed to record payment"
            }
    
    def get_provider_invoices(self, provider_id: int, status: Optional[InvoiceStatus] = None,
                            days: int = 90) -> List[Dict[str, Any]]:
        """Get invoices for logistics provider"""
        try:
            cutoff_date = datetime.datetime.now() - datetime.timedelta(days=days)
            
            invoices = []
            for invoice in self.invoices.values():
                if invoice.provider_id != provider_id:
                    continue
                
                if invoice.created_at < cutoff_date:
                    continue
                
                if status and invoice.status != status:
                    continue
                
                # Calculate aging
                days_outstanding = (datetime.datetime.now() - invoice.issue_date).days
                
                invoices.append({
                    "invoice_id": invoice.invoice_id,
                    "invoice_number": invoice.invoice_number,
                    "customer_id": invoice.customer_id,
                    "shipment_id": invoice.shipment_id,
                    "amount": invoice.amount,
                    "tax_amount": invoice.tax_amount,
                    "total_amount": invoice.total_amount,
                    "paid_amount": invoice.paid_amount,
                    "remaining_balance": invoice.total_amount - invoice.paid_amount,
                    "currency": invoice.currency,
                    "status": invoice.status.value,
                    "issue_date": invoice.issue_date.isoformat(),
                    "due_date": invoice.due_date.isoformat(),
                    "payment_terms": invoice.payment_terms,
                    "days_outstanding": days_outstanding,
                    "is_overdue": datetime.datetime.now() > invoice.due_date and invoice.status not in [InvoiceStatus.PAID, InvoiceStatus.CANCELLED]
                })
            
            return sorted(invoices, key=lambda x: x["issue_date"], reverse=True)
            
        except Exception as e:
            logger.error(f"Get provider invoices error: {str(e)}")
            return []
    
    def get_payment_history(self, provider_id: int, days: int = 90) -> List[Dict[str, Any]]:
        """Get payment history for provider"""
        try:
            cutoff_date = datetime.datetime.now() - datetime.timedelta(days=days)
            
            payments = []
            for payment in self.payments.values():
                if payment.provider_id != provider_id:
                    continue
                
                if payment.created_at < cutoff_date:
                    continue
                
                # Get related invoice
                invoice = self.invoices.get(payment.invoice_id)
                
                payments.append({
                    "payment_id": payment.payment_id,
                    "invoice_id": payment.invoice_id,
                    "invoice_number": invoice.invoice_number if invoice else "N/A",
                    "amount": payment.amount,
                    "currency": payment.currency,
                    "method": payment.method.value,
                    "status": payment.status.value,
                    "transaction_id": payment.transaction_id,
                    "processed_at": payment.processed_at.isoformat() if payment.processed_at else None,
                    "notes": payment.notes,
                    "created_at": payment.created_at.isoformat()
                })
            
            return sorted(payments, key=lambda x: x["created_at"], reverse=True)
            
        except Exception as e:
            logger.error(f"Get payment history error: {str(e)}")
            return []
    
    def generate_financial_summary(self, provider_id: int, days: int = 30) -> Dict[str, Any]:
        """Generate comprehensive financial summary"""
        try:
            end_date = datetime.datetime.now()
            start_date = end_date - datetime.timedelta(days=days)
            
            # Filter invoices for period
            period_invoices = [
                inv for inv in self.invoices.values()
                if (inv.provider_id == provider_id and 
                    start_date <= inv.issue_date <= end_date)
            ]
            
            # Filter payments for period
            period_payments = [
                pay for pay in self.payments.values()
                if (pay.provider_id == provider_id and 
                    pay.processed_at and
                    start_date <= pay.processed_at <= end_date)
            ]
            
            # Calculate metrics
            total_revenue = sum(inv.total_amount for inv in period_invoices)
            total_paid = sum(pay.amount for pay in period_payments)
            
            # Outstanding receivables (all unpaid invoices)
            all_unpaid_invoices = [
                inv for inv in self.invoices.values()
                if (inv.provider_id == provider_id and 
                    inv.status not in [InvoiceStatus.PAID, InvoiceStatus.CANCELLED])
            ]
            outstanding_receivables = sum(
                inv.total_amount - inv.paid_amount for inv in all_unpaid_invoices
            )
            
            # Overdue amount
            now = datetime.datetime.now()
            overdue_invoices = [
                inv for inv in all_unpaid_invoices
                if inv.due_date < now
            ]
            overdue_amount = sum(
                inv.total_amount - inv.paid_amount for inv in overdue_invoices
            )
            
            # Average payment days
            paid_invoices = [
                inv for inv in self.invoices.values()
                if (inv.provider_id == provider_id and 
                    inv.status == InvoiceStatus.PAID and
                    inv.payment_date)
            ]
            
            if paid_invoices:
                payment_days = [
                    (inv.payment_date - inv.issue_date).days 
                    for inv in paid_invoices
                ]
                average_payment_days = sum(payment_days) / len(payment_days)
            else:
                average_payment_days = 0
            
            # Invoice counts
            invoice_count = len(period_invoices)
            paid_invoice_count = len([
                inv for inv in period_invoices
                if inv.status == InvoiceStatus.PAID
            ])
            
            # Collection efficiency
            collection_rate = (paid_invoice_count / invoice_count * 100) if invoice_count > 0 else 0
            
            # Cash flow analysis
            monthly_revenue = total_revenue * (30 / days) if days != 30 else total_revenue
            
            return {
                "period": {
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat(),
                    "days": days
                },
                "revenue": {
                    "total_revenue": round(total_revenue, 2),
                    "total_paid": round(total_paid, 2),
                    "monthly_revenue_projection": round(monthly_revenue, 2)
                },
                "receivables": {
                    "outstanding_receivables": round(outstanding_receivables, 2),
                    "overdue_amount": round(overdue_amount, 2),
                    "overdue_count": len(overdue_invoices)
                },
                "performance": {
                    "average_payment_days": round(average_payment_days, 1),
                    "collection_rate": round(collection_rate, 1),
                    "invoice_count": invoice_count,
                    "paid_invoice_count": paid_invoice_count
                },
                "aging_analysis": self._get_aging_analysis(provider_id),
                "payment_methods": self._get_payment_method_breakdown(provider_id, days)
            }
            
        except Exception as e:
            logger.error(f"Financial summary generation error: {str(e)}")
            return {
                "period": {},
                "revenue": {},
                "receivables": {},
                "performance": {},
                "aging_analysis": {},
                "payment_methods": {}
            }
    
    def _get_aging_analysis(self, provider_id: int) -> Dict[str, Any]:
        """Get accounts receivable aging analysis"""
        try:
            unpaid_invoices = [
                inv for inv in self.invoices.values()
                if (inv.provider_id == provider_id and 
                    inv.status not in [InvoiceStatus.PAID, InvoiceStatus.CANCELLED])
            ]
            
            aging_buckets = {
                "current": 0,      # 0-30 days
                "30_days": 0,      # 31-60 days
                "60_days": 0,      # 61-90 days
                "90_days_plus": 0  # 90+ days
            }
            
            now = datetime.datetime.now()
            
            for invoice in unpaid_invoices:
                days_outstanding = (now - invoice.issue_date).days
                outstanding_amount = invoice.total_amount - invoice.paid_amount
                
                if days_outstanding <= 30:
                    aging_buckets["current"] += outstanding_amount
                elif days_outstanding <= 60:
                    aging_buckets["30_days"] += outstanding_amount
                elif days_outstanding <= 90:
                    aging_buckets["60_days"] += outstanding_amount
                else:
                    aging_buckets["90_days_plus"] += outstanding_amount
            
            # Round values
            for key in aging_buckets:
                aging_buckets[key] = round(aging_buckets[key], 2)
            
            return aging_buckets
            
        except Exception as e:
            logger.error(f"Aging analysis error: {str(e)}")
            return {}
    
    def _get_payment_method_breakdown(self, provider_id: int, days: int) -> Dict[str, Any]:
        """Get payment method breakdown"""
        try:
            cutoff_date = datetime.datetime.now() - datetime.timedelta(days=days)
            
            recent_payments = [
                pay for pay in self.payments.values()
                if (pay.provider_id == provider_id and 
                    pay.processed_at and
                    pay.processed_at >= cutoff_date)
            ]
            
            method_breakdown = {}
            for payment in recent_payments:
                method = payment.method.value
                if method not in method_breakdown:
                    method_breakdown[method] = {
                        "count": 0,
                        "total_amount": 0
                    }
                
                method_breakdown[method]["count"] += 1
                method_breakdown[method]["total_amount"] += payment.amount
            
            # Round amounts
            for method_data in method_breakdown.values():
                method_data["total_amount"] = round(method_data["total_amount"], 2)
            
            return method_breakdown
            
        except Exception as e:
            logger.error(f"Payment method breakdown error: {str(e)}")
            return {}
    
    def create_refund_request(self, payment_id: str, amount: float, reason: str) -> Dict[str, Any]:
        """Create refund request"""
        try:
            payment = self.payments.get(payment_id)
            if not payment:
                return {
                    "success": False,
                    "error": "Payment not found"
                }
            
            if amount > payment.amount:
                return {
                    "success": False,
                    "error": "Refund amount cannot exceed payment amount"
                }
            
            refund_id = str(uuid.uuid4())
            
            refund_request = RefundRequest(
                refund_id=refund_id,
                invoice_id=payment.invoice_id,
                payment_id=payment_id,
                provider_id=payment.provider_id,
                customer_id=payment.customer_id,
                requested_amount=amount,
                reason=reason,
                status=RefundRequestStatus.PENDING
            )
            
            self.refund_requests[refund_id] = refund_request
            
            return {
                "success": True,
                "refund_id": refund_id,
                "requested_amount": amount,
                "status": refund_request.status.value
            }
            
        except Exception as e:
            logger.error(f"Refund request creation error: {str(e)}")
            return {
                "success": False,
                "error": "Failed to create refund request"
            }
    
    def _schedule_payment_reminder(self, invoice_id: str):
        """Schedule payment reminder for invoice"""
        try:
            invoice = self.invoices.get(invoice_id)
            if not invoice:
                return
            
            # Schedule reminders at 7 days before due date, due date, and 7 days after
            reminder_dates = [
                invoice.due_date - datetime.timedelta(days=7),
                invoice.due_date,
                invoice.due_date + datetime.timedelta(days=7)
            ]
            
            self.payment_reminders[invoice_id] = {
                "reminder_dates": [d.isoformat() for d in reminder_dates],
                "sent_count": 0,
                "last_sent": None
            }
            
        except Exception as e:
            logger.error(f"Payment reminder scheduling error: {str(e)}")

# Global instance
logistics_erp_finance = LogisticsERPFinance()