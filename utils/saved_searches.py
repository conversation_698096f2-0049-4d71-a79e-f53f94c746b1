"""
Saved Searches & Price Alerts System
Comprehensive search management and price monitoring for customers
"""

import os
import django
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from decimal import Decimal
import json
import asyncio
from dataclasses import dataclass
from asgiref.sync import sync_to_async

# Configure Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'cloverics_django.cloverics.settings')
django.setup()

from core.models import User, Notification
from shipments.models import Shipment, ShippingRate, QuoteRequest

@dataclass
class SearchCriteria:
    """Search criteria data structure"""
    origin_country: str
    destination_country: str
    origin_state: Optional[str] = None
    destination_state: Optional[str] = None
    transport_type: Optional[str] = None
    weight_min: Optional[float] = None
    weight_max: Optional[float] = None
    cargo_type: Optional[str] = None
    max_price: Optional[float] = None
    max_transit_days: Optional[int] = None

class SavedSearchManager:
    """
    Manages saved searches for customers with intelligent matching
    """
    
    def __init__(self):
        self.search_weights = {
            'location_match': 0.4,
            'transport_match': 0.2,
            'weight_compatibility': 0.15,
            'price_competitiveness': 0.15,
            'cargo_compatibility': 0.1
        }
    
    @sync_to_async
    def create_saved_search(self, user_id: int, search_data: Dict) -> Dict:
        """Create a new saved search for user"""
        try:
            from django.core.exceptions import ValidationError
            
            # Validate required fields
            required_fields = ['name', 'origin_country', 'destination_country']
            for field in required_fields:
                if not search_data.get(field):
                    return {'success': False, 'error': f'Missing required field: {field}'}
            
            # Create search criteria object
            criteria = SearchCriteria(
                origin_country=search_data['origin_country'],
                destination_country=search_data['destination_country'],
                origin_state=search_data.get('origin_state'),
                destination_state=search_data.get('destination_state'),
                transport_type=search_data.get('transport_type'),
                weight_min=float(search_data['weight_min']) if search_data.get('weight_min') else None,
                weight_max=float(search_data['weight_max']) if search_data.get('weight_max') else None,
                cargo_type=search_data.get('cargo_type'),
                max_price=float(search_data['max_price']) if search_data.get('max_price') else None,
                max_transit_days=int(search_data['max_transit_days']) if search_data.get('max_transit_days') else None
            )
            
            # Create database record
            from django.db import models
            
            # Using a simple approach since we don't have a dedicated SavedSearch model
            # Store in user's notification system as a special type
            user = User.objects.get(id=user_id)
            
            search_id = f"search_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{user_id}"
            
            notification_data = {
                'type': 'saved_search',
                'search_id': search_id,
                'name': search_data['name'],
                'criteria': criteria.__dict__,
                'created_at': datetime.now().isoformat(),
                'is_active': True,
                'alert_frequency': search_data.get('alert_frequency', 'daily'),
                'last_alert_sent': None,
                'match_count': 0
            }
            
            # Store as notification with special type
            Notification.objects.create(
                user=user,
                title=f"Saved Search: {search_data['name']}",
                message=json.dumps(notification_data),
                type='saved_search',
                read=True  # Mark as read since it's not a traditional notification
            )
            
            return {
                'success': True,
                'search_id': search_id,
                'message': 'Saved search created successfully'
            }
            
        except Exception as e:
            return {'success': False, 'error': f'Error creating saved search: {str(e)}'}
    
    @sync_to_async
    def get_user_saved_searches(self, user_id: int) -> Dict:
        """Get all saved searches for a user"""
        try:
            user = User.objects.get(id=user_id)
            
            # Get saved search notifications
            saved_searches = Notification.objects.filter(
                user=user,
                type='saved_search'
            ).order_by('-created_at')
            
            searches = []
            for notification in saved_searches:
                try:
                    search_data = json.loads(notification.message)
                    searches.append({
                        'id': notification.id,
                        'search_id': search_data.get('search_id'),
                        'name': search_data.get('name'),
                        'criteria': search_data.get('criteria'),
                        'created_at': search_data.get('created_at'),
                        'is_active': search_data.get('is_active', True),
                        'alert_frequency': search_data.get('alert_frequency', 'daily'),
                        'last_alert_sent': search_data.get('last_alert_sent'),
                        'match_count': search_data.get('match_count', 0)
                    })
                except:
                    continue
            
            return {
                'success': True,
                'searches': searches,
                'total_count': len(searches)
            }
            
        except Exception as e:
            return {'success': False, 'error': f'Error fetching saved searches: {str(e)}'}
    
    @sync_to_async
    def delete_saved_search(self, user_id: int, search_id: str) -> Dict:
        """Delete a saved search"""
        try:
            user = User.objects.get(id=user_id)
            
            # Find and delete the saved search notification
            notifications = Notification.objects.filter(
                user=user,
                type='saved_search'
            )
            
            for notification in notifications:
                try:
                    search_data = json.loads(notification.message)
                    if search_data.get('search_id') == search_id:
                        notification.delete()
                        return {'success': True, 'message': 'Saved search deleted successfully'}
                except:
                    continue
            
            return {'success': False, 'error': 'Saved search not found'}
            
        except Exception as e:
            return {'success': False, 'error': f'Error deleting saved search: {str(e)}'}
    
    @sync_to_async
    def update_saved_search(self, user_id: int, search_id: str, update_data: Dict) -> Dict:
        """Update a saved search"""
        try:
            user = User.objects.get(id=user_id)
            
            # Find the saved search notification
            notifications = Notification.objects.filter(
                user=user,
                type='saved_search'
            )
            
            for notification in notifications:
                try:
                    search_data = json.loads(notification.message)
                    if search_data.get('search_id') == search_id:
                        # Update the data
                        search_data.update(update_data)
                        search_data['updated_at'] = datetime.now().isoformat()
                        
                        # Save back to notification
                        notification.message = json.dumps(search_data)
                        notification.save()
                        
                        return {'success': True, 'message': 'Saved search updated successfully'}
                except:
                    continue
            
            return {'success': False, 'error': 'Saved search not found'}
            
        except Exception as e:
            return {'success': False, 'error': f'Error updating saved search: {str(e)}'}
    
    @sync_to_async
    def find_matching_routes(self, criteria: SearchCriteria, limit: int = 20) -> List[Dict]:
        """Find routes matching search criteria"""
        try:
            # Base query for shipping rates
            routes = ShippingRate.objects.filter(
                route__origin_country__icontains=criteria.origin_country,
                route__destination_country__icontains=criteria.destination_country
            )
            
            # Apply additional filters
            if criteria.transport_type:
                routes = routes.filter(transport_type__type=criteria.transport_type)
            
            if criteria.weight_min:
                routes = routes.filter(base_price_per_kg__gte=0)  # Basic weight compatibility
            
            if criteria.max_price:
                routes = routes.filter(base_price_per_kg__lte=criteria.max_price)
            
            if criteria.cargo_type:
                routes = routes.filter(cargo_type__name__icontains=criteria.cargo_type)
            
            # Get route data
            matching_routes = []
            for route in routes[:limit]:
                try:
                    route_data = {
                        'id': route.id,
                        'provider_name': route.logistics_provider.company_name if hasattr(route.logistics_provider, 'company_name') else 'Provider',
                        'origin': f"{route.route.origin_city}, {route.route.origin_country}",
                        'destination': f"{route.route.destination_city}, {route.route.destination_country}",
                        'transport_type': route.transport_type.type if hasattr(route.transport_type, 'type') else 'truck',
                        'base_price': float(route.base_price_per_kg),
                        'estimated_days': route.estimated_delivery_days,
                        'cargo_types': [route.cargo_type.name] if route.cargo_type else ['General'],
                        'match_score': self._calculate_match_score(route, criteria)
                    }
                    matching_routes.append(route_data)
                except Exception as e:
                    continue
            
            # Sort by match score
            matching_routes.sort(key=lambda x: x['match_score'], reverse=True)
            
            return matching_routes
            
        except Exception as e:
            return []
    
    def _calculate_match_score(self, route, criteria: SearchCriteria) -> float:
        """Calculate how well a route matches search criteria"""
        score = 0.0
        
        # Location match (exact country match gets full points)
        if (route.route.origin_country.lower() == criteria.origin_country.lower() and
            route.route.destination_country.lower() == criteria.destination_country.lower()):
            score += self.search_weights['location_match']
        
        # Transport type match
        if criteria.transport_type:
            if hasattr(route.transport_type, 'type') and route.transport_type.type == criteria.transport_type:
                score += self.search_weights['transport_match']
        else:
            score += self.search_weights['transport_match'] * 0.5  # Partial credit for flexibility
        
        # Price competitiveness
        if criteria.max_price:
            price_ratio = float(route.base_price_per_kg) / criteria.max_price
            if price_ratio <= 1.0:
                score += self.search_weights['price_competitiveness'] * (1.0 - price_ratio + 0.1)
        else:
            score += self.search_weights['price_competitiveness'] * 0.5
        
        # Weight compatibility (simplified)
        score += self.search_weights['weight_compatibility']
        
        # Cargo compatibility
        if criteria.cargo_type and route.cargo_type:
            if criteria.cargo_type.lower() in route.cargo_type.name.lower():
                score += self.search_weights['cargo_compatibility']
        else:
            score += self.search_weights['cargo_compatibility'] * 0.5
        
        return min(score, 1.0)

class PriceAlertManager:
    """
    Manages price alerts and monitoring for saved searches
    """
    
    def __init__(self):
        self.alert_thresholds = {
            'price_drop': 0.1,  # 10% price drop
            'new_route': True,
            'capacity_available': True,
            'better_provider': 0.15  # 15% better rating
        }
    
    @sync_to_async
    def create_price_alert(self, user_id: int, alert_data: Dict) -> Dict:
        """Create a price alert for specific criteria"""
        try:
            user = User.objects.get(id=user_id)
            
            alert_id = f"alert_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{user_id}"
            
            notification_data = {
                'type': 'price_alert',
                'alert_id': alert_id,
                'name': alert_data['name'],
                'criteria': alert_data['criteria'],
                'alert_type': alert_data.get('alert_type', 'price_drop'),
                'threshold_value': alert_data.get('threshold_value'),
                'notification_method': alert_data.get('notification_method', 'platform'),
                'created_at': datetime.now().isoformat(),
                'is_active': True,
                'last_checked': None,
                'trigger_count': 0
            }
            
            # Store as notification
            Notification.objects.create(
                user=user,
                title=f"Price Alert: {alert_data['name']}",
                message=json.dumps(notification_data),
                type='price_alert',
                read=True
            )
            
            return {
                'success': True,
                'alert_id': alert_id,
                'message': 'Price alert created successfully'
            }
            
        except Exception as e:
            return {'success': False, 'error': f'Error creating price alert: {str(e)}'}
    
    @sync_to_async
    def check_price_alerts(self, user_id: int) -> Dict:
        """Check and trigger price alerts for user"""
        try:
            user = User.objects.get(id=user_id)
            
            # Get active price alerts
            alert_notifications = Notification.objects.filter(
                user=user,
                type='price_alert'
            )
            
            triggered_alerts = []
            
            for notification in alert_notifications:
                try:
                    alert_data = json.loads(notification.message)
                    if not alert_data.get('is_active', True):
                        continue
                    
                    # Check if alert should trigger
                    alert_result = self._check_individual_alert(alert_data)
                    
                    if alert_result['should_trigger']:
                        triggered_alerts.append(alert_result)
                        
                        # Update trigger count
                        alert_data['trigger_count'] = alert_data.get('trigger_count', 0) + 1
                        alert_data['last_triggered'] = datetime.now().isoformat()
                        
                        # Save updated data
                        notification.message = json.dumps(alert_data)
                        notification.save()
                        
                        # Create alert notification
                        self._create_alert_notification(user, alert_result)
                        
                except Exception as e:
                    continue
            
            return {
                'success': True,
                'triggered_alerts': triggered_alerts,
                'total_triggered': len(triggered_alerts)
            }
            
        except Exception as e:
            return {'success': False, 'error': f'Error checking price alerts: {str(e)}'}
    
    def _check_individual_alert(self, alert_data: Dict) -> Dict:
        """Check if individual alert should trigger"""
        alert_type = alert_data.get('alert_type', 'price_drop')
        criteria = alert_data.get('criteria', {})
        
        result = {
            'alert_id': alert_data.get('alert_id'),
            'alert_name': alert_data.get('name'),
            'alert_type': alert_type,
            'should_trigger': False,
            'trigger_reason': '',
            'details': {}
        }
        
        try:
            if alert_type == 'price_drop':
                # Check for price drops
                current_routes = self._get_current_route_prices(criteria)
                threshold = alert_data.get('threshold_value', 0.1)
                
                for route in current_routes:
                    # Simplified price drop detection
                    if route.get('price_changed', False):
                        result['should_trigger'] = True
                        result['trigger_reason'] = f"Price drop detected for {route['provider_name']}"
                        result['details'] = route
                        break
            
            elif alert_type == 'new_route':
                # Check for new routes
                new_routes = self._check_for_new_routes(criteria)
                if new_routes:
                    result['should_trigger'] = True
                    result['trigger_reason'] = f"New route available: {new_routes[0]['provider_name']}"
                    result['details'] = new_routes[0]
            
            elif alert_type == 'capacity_available':
                # Check for available capacity
                available_capacity = self._check_capacity_availability(criteria)
                if available_capacity:
                    result['should_trigger'] = True
                    result['trigger_reason'] = "Capacity now available for your criteria"
                    result['details'] = available_capacity
        
        except Exception as e:
            result['error'] = str(e)
        
        return result
    
    def _get_current_route_prices(self, criteria: Dict) -> List[Dict]:
        """Get current prices for routes matching criteria"""
        try:
            # Simplified price checking
            routes = ShippingRate.objects.filter(
                route__origin_country__icontains=criteria.get('origin_country', ''),
                route__destination_country__icontains=criteria.get('destination_country', '')
            )[:5]
            
            route_data = []
            for route in routes:
                try:
                    route_info = {
                        'id': route.id,
                        'provider_name': route.logistics_provider.company_name if hasattr(route.logistics_provider, 'company_name') else 'Provider',
                        'current_price': float(route.base_price_per_kg),
                        'price_changed': False  # Would need historical data for real comparison
                    }
                    route_data.append(route_info)
                except:
                    continue
            
            return route_data
            
        except Exception as e:
            return []
    
    def _check_for_new_routes(self, criteria: Dict) -> List[Dict]:
        """Check for new routes (simplified implementation)"""
        try:
            # In a real implementation, this would check against historical data
            # For now, return empty list
            return []
        except:
            return []
    
    def _check_capacity_availability(self, criteria: Dict) -> Optional[Dict]:
        """Check for available capacity"""
        try:
            # Simplified capacity check
            return None
        except:
            return None
    
    @sync_to_async
    def _create_alert_notification(self, user: User, alert_result: Dict):
        """Create notification for triggered alert"""
        try:
            Notification.objects.create(
                user=user,
                title=f"Price Alert: {alert_result['alert_name']}",
                message=alert_result['trigger_reason'],
                type='alert_notification',
                read=False
            )
        except Exception as e:
            pass

class SearchAnalytics:
    """
    Analytics and insights for saved searches and price alerts
    """
    
    @sync_to_async
    def get_search_analytics(self, user_id: int) -> Dict:
        """Get analytics for user's saved searches"""
        try:
            user = User.objects.get(id=user_id)
            
            # Get saved searches
            saved_searches = Notification.objects.filter(
                user=user,
                type='saved_search'
            )
            
            # Get price alerts
            price_alerts = Notification.objects.filter(
                user=user,
                type='price_alert'
            )
            
            analytics = {
                'total_saved_searches': saved_searches.count(),
                'total_price_alerts': price_alerts.count(),
                'active_searches': 0,
                'active_alerts': 0,
                'most_searched_routes': [],
                'average_price_range': 0,
                'popular_transport_types': [],
                'alert_trigger_frequency': {
                    'daily': 0,
                    'weekly': 0,
                    'monthly': 0
                }
            }
            
            # Count active searches and alerts
            for notification in saved_searches:
                try:
                    data = json.loads(notification.message)
                    if data.get('is_active', True):
                        analytics['active_searches'] += 1
                except:
                    continue
            
            for notification in price_alerts:
                try:
                    data = json.loads(notification.message)
                    if data.get('is_active', True):
                        analytics['active_alerts'] += 1
                except:
                    continue
            
            return {
                'success': True,
                'analytics': analytics
            }
            
        except Exception as e:
            return {'success': False, 'error': f'Error getting analytics: {str(e)}'}

# Global instances
saved_search_manager = SavedSearchManager()
price_alert_manager = PriceAlertManager()
search_analytics = SearchAnalytics()