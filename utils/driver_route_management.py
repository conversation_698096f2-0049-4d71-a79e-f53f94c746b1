"""
Individual Driver Route Management System
Enables verified independent drivers to create and manage routes
like logistics providers, implementing ChatGPT's strategic differentiation plan.
"""

import logging
from typing import Dict, List, Optional
from django.db import connection
from django.utils import timezone
from asgiref.sync import sync_to_async

logger = logging.getLogger(__name__)

class DriverRouteManager:
    """Manages route creation and operations for individual drivers"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    async def check_driver_eligibility(self, user_id: int) -> Dict:
        """Check if driver can operate independently"""
        try:
            def _check_eligibility():
                with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT 
                        tdp.can_operate_independently,
                        tdp.verification_status,
                        tdp.is_verified,
                        tdp.full_name,
                        tdp.truck_type,
                        tdp.capacity_kg,
                        u.user_type
                    FROM truck_driver_profiles tdp
                    JOIN core_user u ON tdp.user_id = u.id
                    WHERE tdp.user_id = %s
                """, [user_id])
                
                result = cursor.fetchone()
                if not result:
                    return {
                        'eligible': False,
                        'reason': 'Driver profile not found'
                    }
                
                can_operate, verification_status, is_verified, full_name, truck_type, capacity_kg, user_type = result
                
                if user_type not in ['INDEPENDENT_DRIVER', 'TRUCK_DRIVER']:
                    return {
                        'eligible': False,
                        'reason': 'User must be registered as an independent driver'
                    }
                
                if not can_operate:
                    return {
                        'eligible': False,
                        'reason': 'Driver not approved for independent operation. Contact admin for approval.'
                    }
                
                if verification_status != 'approved' or not is_verified:
                    return {
                        'eligible': False,
                        'reason': 'Driver verification not complete. Please complete verification process.'
                    }
                
                return {
                    'eligible': True,
                    'driver_info': {
                        'full_name': full_name,
                        'truck_type': truck_type,
                        'capacity_kg': capacity_kg,
                        'verification_status': verification_status
                    }
                }
                
        except Exception as e:
            self.logger.error(f"Error checking driver eligibility: {e}")
            return {
                'eligible': False,
                'reason': f'System error: {str(e)}'
            }
    
    async def create_driver_route(self, user_id: int, route_data: Dict) -> Dict:
        """Create a new route for an individual driver"""
        try:
            # Check eligibility first
            eligibility = await self.check_driver_eligibility(user_id)
            if not eligibility['eligible']:
                return {
                    'success': False,
                    'error': eligibility['reason']
                }
            
            # Extract route data
            origin_country = route_data.get('origin_country')
            destination_country = route_data.get('destination_country')
            origin_city = route_data.get('origin_city', '')
            destination_city = route_data.get('destination_city', '')
            transport_type = route_data.get('transport_type', 'truck')
            base_rate = float(route_data.get('base_rate', 0))
            estimated_days = int(route_data.get('estimated_days', 7))
            max_capacity = int(route_data.get('max_capacity', 1000))
            
            # Validate required fields
            if not all([origin_country, destination_country, base_rate > 0]):
                return {
                    'success': False,
                    'error': 'Missing required fields: origin country, destination country, and base rate'
                }
            
            # Check for duplicate routes
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT COUNT(*) FROM shipments_route 
                    WHERE logistics_provider_id = %s 
                    AND origin_country = %s 
                    AND destination_country = %s
                    AND transport_type = %s
                """, [user_id, origin_country, destination_country, transport_type])
                
                if cursor.fetchone()[0] > 0:
                    return {
                        'success': False,
                        'error': 'You already have a route for this origin-destination combination with this transport type.'
                    }
                
                # Create route
                cursor.execute("""
                    INSERT INTO shipments_route (
                        origin_country, destination_country, origin_city, destination_city,
                        transport_type, logistics_provider_id, is_active, last_used,
                        created_at, updated_at
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    RETURNING id
                """, [
                    origin_country, destination_country, origin_city, destination_city,
                    transport_type, user_id, True, timezone.now(),
                    timezone.now(), timezone.now()
                ])
                
                route_id = cursor.fetchone()[0]
                
                # Create shipping rate for the route
                cursor.execute("""
                    INSERT INTO shipments_shippingrate (
                        logistics_provider_id, route_id, transport_type, container_type,
                        base_price, price_per_kg, price_per_km, estimated_days,
                        available_space, is_active, created_at, updated_at
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, [
                    user_id, route_id, transport_type, 'Standard Container',
                    base_rate, base_rate, 1.0, estimated_days,
                    max_capacity, True, timezone.now(), timezone.now()
                ])
                
                self.logger.info(f"Driver route created successfully: Route ID {route_id} for user {user_id}")
                
                return {
                    'success': True,
                    'route_id': route_id,
                    'message': f'Route from {origin_city or origin_country} to {destination_city or destination_country} created successfully!'
                }
                
        except Exception as e:
            self.logger.error(f"Error creating driver route: {e}")
            return {
                'success': False,
                'error': f'Failed to create route: {str(e)}'
            }
    
    async def get_driver_routes(self, user_id: int) -> List[Dict]:
        """Get all routes created by a driver"""
        try:
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT 
                        r.id, r.origin_country, r.destination_country,
                        r.origin_city, r.destination_city, r.transport_type,
                        r.is_active, r.last_used, r.created_at,
                        sr.base_price, sr.estimated_days, sr.available_space
                    FROM shipments_route r
                    LEFT JOIN shipments_shippingrate sr ON r.id = sr.route_id
                    WHERE r.logistics_provider_id = %s
                    ORDER BY r.created_at DESC
                """, [user_id])
                
                routes = []
                for row in cursor.fetchall():
                    route_id, origin_country, dest_country, origin_city, dest_city, transport_type, is_active, last_used, created_at, base_price, estimated_days, available_space = row
                    
                    routes.append({
                        'id': route_id,
                        'origin_country': origin_country,
                        'destination_country': dest_country,
                        'origin_city': origin_city or origin_country,
                        'destination_city': dest_city or dest_country,
                        'transport_type': transport_type,
                        'base_price': float(base_price) if base_price else 0.0,
                        'estimated_days': estimated_days or 7,
                        'available_space': available_space or 0,
                        'is_active': is_active,
                        'last_used': last_used,
                        'created_at': created_at
                    })
                
                return routes
                
        except Exception as e:
            self.logger.error(f"Error fetching driver routes: {e}")
            return []
    
    async def get_driver_statistics(self, user_id: int) -> Dict:
        """Get driver performance statistics"""
        try:
            with connection.cursor() as cursor:
                # Get basic route statistics
                cursor.execute("""
                    SELECT COUNT(*) as total_routes,
                           COUNT(CASE WHEN is_active = true THEN 1 END) as active_routes
                    FROM shipments_route
                    WHERE logistics_provider_id = %s
                """, [user_id])
                
                route_stats = cursor.fetchone()
                
                # Get driver profile info
                cursor.execute("""
                    SELECT driver_rating, truck_type, capacity_kg, verification_status
                    FROM truck_driver_profiles
                    WHERE user_id = %s
                """, [user_id])
                
                profile_info = cursor.fetchone()
                
                # Get quote requests (if any)
                cursor.execute("""
                    SELECT COUNT(*) as total_quotes
                    FROM shipments_quoterequest
                    WHERE logistics_provider_id = %s
                """, [user_id])
                
                quote_stats = cursor.fetchone()
                
                return {
                    'total_routes': route_stats[0] if route_stats else 0,
                    'active_routes': route_stats[1] if route_stats else 0,
                    'driver_rating': float(profile_info[0]) if profile_info and profile_info[0] else 5.0,
                    'truck_type': profile_info[1] if profile_info else 'Unknown',
                    'capacity_kg': profile_info[2] if profile_info else 0,
                    'verification_status': profile_info[3] if profile_info else 'pending',
                    'total_quotes': quote_stats[0] if quote_stats else 0
                }
                
        except Exception as e:
            self.logger.error(f"Error fetching driver statistics: {e}")
            return {
                'total_routes': 0,
                'active_routes': 0,
                'driver_rating': 5.0,
                'truck_type': 'Unknown',
                'capacity_kg': 0,
                'verification_status': 'pending',
                'total_quotes': 0
            }

class DriverSearchEnhancer:
    """Enhances search results to distinguish between logistics companies and individual drivers"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    async def get_enhanced_search_results(self, search_params: Dict) -> List[Dict]:
        """Get search results with provider type indicators"""
        try:
            # Base search query
            where_conditions = []
            params = []
            
            if search_params.get('origin_country'):
                where_conditions.append("r.origin_country = %s")
                params.append(search_params['origin_country'])
            
            if search_params.get('destination_country'):
                where_conditions.append("r.destination_country = %s")
                params.append(search_params['destination_country'])
            
            if search_params.get('transport_type'):
                where_conditions.append("r.transport_type = %s")
                params.append(search_params['transport_type'])
            
            # Provider type filter
            provider_type_filter = search_params.get('provider_type', 'all')  # 'logistics', 'driver', 'all'
            
            where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"
            
            with connection.cursor() as cursor:
                cursor.execute(f"""
                    SELECT 
                        r.id, r.origin_country, r.destination_country,
                        r.origin_city, r.destination_city, r.transport_type,
                        sr.base_price, sr.estimated_days, sr.available_space,
                        u.id as provider_id, u.email, u.user_type,
                        COALESCE(cp.company_name, tdp.full_name) as provider_name,
                        CASE 
                            WHEN u.user_type IN ('INDEPENDENT_DRIVER', 'TRUCK_DRIVER') THEN 'driver'
                            ELSE 'logistics'
                        END as provider_type,
                        COALESCE(tdp.driver_rating, 5.0) as rating,
                        tdp.truck_type,
                        tdp.verification_status
                    FROM shipments_route r
                    JOIN shipments_shippingrate sr ON r.id = sr.route_id
                    JOIN core_user u ON r.logistics_provider_id = u.id
                    LEFT JOIN core_companyprofile cp ON u.id = cp.user_id
                    LEFT JOIN truck_driver_profiles tdp ON u.id = tdp.user_id
                    WHERE {where_clause} 
                    AND r.is_active = true 
                    AND sr.is_active = true
                    ORDER BY sr.base_price ASC
                """, params)
                
                results = []
                for row in cursor.fetchall():
                    route_id, origin_country, dest_country, origin_city, dest_city, transport_type, base_price, estimated_days, available_space, provider_id, email, user_type, provider_name, provider_type, rating, truck_type, verification_status = row
                    
                    # Apply provider type filter
                    if provider_type_filter != 'all':
                        if provider_type_filter == 'logistics' and provider_type == 'driver':
                            continue
                        if provider_type_filter == 'driver' and provider_type == 'logistics':
                            continue
                    
                    result = {
                        'route_id': route_id,
                        'origin_country': origin_country,
                        'destination_country': dest_country,
                        'origin_city': origin_city or origin_country,
                        'destination_city': dest_city or dest_country,
                        'transport_type': transport_type,
                        'base_price': float(base_price) if base_price else 0.0,
                        'estimated_days': estimated_days or 7,
                        'available_space': available_space or 0,
                        'provider_id': provider_id,
                        'provider_name': provider_name or email,
                        'provider_type': provider_type,
                        'provider_type_label': '🚛 Logistics Company' if provider_type == 'logistics' else '👤 Individual Driver',
                        'provider_description': 'Professional logistics service' if provider_type == 'logistics' else 'Verified independent driver offering direct transport',
                        'rating': float(rating) if rating else 5.0,
                        'truck_type': truck_type,
                        'verification_status': verification_status
                    }
                    
                    results.append(result)
                
                return results
                
        except Exception as e:
            self.logger.error(f"Error in enhanced search: {e}")
            return []

# Demo data setup for testing
async def setup_demo_driver_routes():
    """Set up demo routes for individual drivers"""
    try:
        # Enable independent operation for existing demo drivers
        with connection.cursor() as cursor:
            # Update demo drivers to allow independent operation
            cursor.execute("""
                UPDATE truck_driver_profiles 
                SET can_operate_independently = true,
                    verification_status = 'approved',
                    is_verified = true
                WHERE user_id IN (
                    SELECT id FROM core_user 
                    WHERE email IN ('<EMAIL>') 
                    AND user_type IN ('INDEPENDENT_DRIVER', 'TRUCK_DRIVER')
                )
            """)
            
            print("Demo driver setup completed - drivers can now operate independently")
            
    except Exception as e:
        print(f"Error setting up demo drivers: {e}")