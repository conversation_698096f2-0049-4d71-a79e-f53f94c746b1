"""
LogistiLink API Security Module

This module provides API authentication, request validation, and rate limiting
for API endpoints and external integrations.
"""

import hashlib
import hmac
import time
import json
import logging
from datetime import datetime, timedelta
from utils.security import check_rate_limit, log_user_action

# Configure API security logging
api_security_logger = logging.getLogger('logistilink.api_security')

class APISecurityManager:
    """Manages API security and authentication"""
    
    def __init__(self):
        self.api_keys = {}  # In production, store in secure database
        self.request_signatures = {}
    
    def generate_api_key(self, user_id, purpose="general"):
        """
        Generate secure API key for user
        
        Args:
            user_id: ID of the user requesting API access
            purpose: Purpose of the API key (tracking, payments, etc.)
            
        Returns:
            dict: API key details
        """
        timestamp = str(int(time.time()))
        key_data = f"{user_id}:{purpose}:{timestamp}"
        api_key = hashlib.sha256(key_data.encode()).hexdigest()
        
        key_info = {
            "api_key": api_key,
            "user_id": user_id,
            "purpose": purpose,
            "created_at": datetime.now(),
            "last_used": None,
            "usage_count": 0,
            "rate_limit": 1000,  # requests per hour
            "active": True
        }
        
        self.api_keys[api_key] = key_info
        
        log_user_action("api_key_generated", user_id, {
            "purpose": purpose,
            "key_prefix": api_key[:8]
        })
        
        return key_info
    
    def validate_api_key(self, api_key):
        """
        Validate API key and check permissions
        
        Args:
            api_key: The API key to validate
            
        Returns:
            dict: User info if valid, None if invalid
        """
        if not api_key or api_key not in self.api_keys:
            api_security_logger.warning(f"Invalid API key attempt: {api_key[:8] if api_key else 'None'}")
            return None
        
        key_info = self.api_keys[api_key]
        
        if not key_info["active"]:
            api_security_logger.warning(f"Inactive API key used: {api_key[:8]}")
            return None
        
        # Check rate limit
        rate_limit_key = f"api_key_{api_key}"
        if not check_rate_limit(rate_limit_key, max_attempts=key_info["rate_limit"], window_minutes=60):
            api_security_logger.warning(f"API key rate limit exceeded: {api_key[:8]}")
            return None
        
        # Update usage statistics
        key_info["last_used"] = datetime.now()
        key_info["usage_count"] += 1
        
        return key_info
    
    def validate_request_signature(self, request_data, signature, secret_key):
        """
        Validate HMAC signature for API requests
        
        Args:
            request_data: The request payload
            signature: The provided signature
            secret_key: The secret key for validation
            
        Returns:
            bool: True if signature is valid
        """
        if not signature or not secret_key:
            return False
        
        # Create expected signature
        if isinstance(request_data, dict):
            payload = json.dumps(request_data, sort_keys=True)
        else:
            payload = str(request_data)
        
        expected_signature = hmac.new(
            secret_key.encode(),
            payload.encode(),
            hashlib.sha256
        ).hexdigest()
        
        # Compare signatures securely
        return hmac.compare_digest(signature, expected_signature)
    
    def validate_tracking_api_request(self, tracking_number, api_key):
        """
        Validate tracking API request
        
        Args:
            tracking_number: The tracking number being queried
            api_key: The API key for authentication
            
        Returns:
            dict: Validation result
        """
        key_info = self.validate_api_key(api_key)
        if not key_info:
            return {"valid": False, "error": "Invalid API key"}
        
        # Validate tracking number format
        if not tracking_number or len(tracking_number) < 10:
            return {"valid": False, "error": "Invalid tracking number format"}
        
        # Check if user has permission to access this tracking number
        from utils.database_security import DatabaseSecurityManager
        db_manager = DatabaseSecurityManager()
        
        # Log the API request
        log_user_action("api_tracking_request", key_info["user_id"], {
            "tracking_number": tracking_number,
            "api_key_prefix": api_key[:8]
        })
        
        return {"valid": True, "user_id": key_info["user_id"]}
    
    def validate_payment_api_request(self, payment_data, api_key, signature):
        """
        Validate payment API request with enhanced security
        
        Args:
            payment_data: Payment information
            api_key: API key for authentication
            signature: Request signature for validation
            
        Returns:
            dict: Validation result
        """
        key_info = self.validate_api_key(api_key)
        if not key_info:
            return {"valid": False, "error": "Invalid API key"}
        
        # Payment APIs require specific purpose
        if key_info["purpose"] not in ["payments", "general"]:
            return {"valid": False, "error": "API key not authorized for payments"}
        
        # Validate signature for payment requests
        if not self.validate_request_signature(payment_data, signature, api_key):
            api_security_logger.warning(f"Invalid payment API signature from user {key_info['user_id']}")
            return {"valid": False, "error": "Invalid request signature"}
        
        # Additional payment validation
        required_fields = ["amount", "currency", "shipment_id"]
        missing_fields = [field for field in required_fields if field not in payment_data]
        
        if missing_fields:
            return {"valid": False, "error": f"Missing required fields: {missing_fields}"}
        
        # Log the payment API request
        log_user_action("api_payment_request", key_info["user_id"], {
            "amount": payment_data.get("amount"),
            "shipment_id": payment_data.get("shipment_id"),
            "api_key_prefix": api_key[:8]
        })
        
        return {"valid": True, "user_id": key_info["user_id"]}

class RequestValidator:
    """Validates API request parameters and content"""
    
    @staticmethod
    def validate_pagination_params(page, limit):
        """
        Validate pagination parameters
        
        Args:
            page: Page number
            limit: Items per page
            
        Returns:
            dict: Validated parameters
        """
        try:
            page = max(1, int(page) if page else 1)
            limit = min(100, max(1, int(limit) if limit else 20))
            return {"page": page, "limit": limit, "valid": True}
        except (ValueError, TypeError):
            return {"valid": False, "error": "Invalid pagination parameters"}
    
    @staticmethod
    def validate_search_params(query, filters):
        """
        Validate search parameters to prevent injection
        
        Args:
            query: Search query string
            filters: Additional filters
            
        Returns:
            dict: Validated parameters
        """
        if query:
            # Remove dangerous characters
            query = query.replace("<", "").replace(">", "").replace("'", "").replace('"', "")
            query = query[:200]  # Limit length
        
        # Validate filters
        allowed_filters = ["status", "type", "date_from", "date_to", "country"]
        safe_filters = {}
        
        if filters:
            for key, value in filters.items():
                if key in allowed_filters and value:
                    safe_filters[key] = str(value)[:50]  # Limit length
        
        return {"query": query, "filters": safe_filters, "valid": True}
    
    @staticmethod
    def validate_webhook_payload(payload, headers):
        """
        Validate webhook payload for external integrations
        
        Args:
            payload: The webhook payload
            headers: Request headers
            
        Returns:
            dict: Validation result
        """
        # Check content type
        content_type = headers.get("Content-Type", "")
        if "application/json" not in content_type:
            return {"valid": False, "error": "Invalid content type"}
        
        # Validate payload size
        payload_str = json.dumps(payload) if isinstance(payload, dict) else str(payload)
        if len(payload_str) > 1024 * 1024:  # 1MB limit
            return {"valid": False, "error": "Payload too large"}
        
        # Check for required webhook fields
        if isinstance(payload, dict):
            required_fields = ["event_type", "timestamp"]
            missing_fields = [field for field in required_fields if field not in payload]
            
            if missing_fields:
                return {"valid": False, "error": f"Missing webhook fields: {missing_fields}"}
        
        return {"valid": True}

class APIRateLimiter:
    """Advanced rate limiting for API endpoints"""
    
    def __init__(self):
        self.rate_limits = {
            "tracking": {"requests": 100, "window": 60},  # 100 requests per minute
            "search": {"requests": 50, "window": 60},     # 50 requests per minute
            "payments": {"requests": 10, "window": 60},   # 10 requests per minute
            "webhooks": {"requests": 200, "window": 60}   # 200 requests per minute
        }
    
    def check_endpoint_rate_limit(self, endpoint, identifier):
        """
        Check rate limit for specific API endpoint
        
        Args:
            endpoint: The API endpoint being accessed
            identifier: User or IP identifier
            
        Returns:
            bool: True if request allowed
        """
        if endpoint not in self.rate_limits:
            endpoint = "default"
        
        limits = self.rate_limits.get(endpoint, {"requests": 60, "window": 60})
        rate_limit_key = f"api_{endpoint}_{identifier}"
        
        return check_rate_limit(
            rate_limit_key,
            max_attempts=limits["requests"],
            window_minutes=limits["window"] // 60
        )

class APISecurityHeaders:
    """Manages security headers for API responses"""
    
    @staticmethod
    def get_security_headers():
        """
        Get standard security headers for API responses
        
        Returns:
            dict: Security headers
        """
        return {
            "X-Content-Type-Options": "nosniff",
            "X-Frame-Options": "DENY",
            "X-XSS-Protection": "1; mode=block",
            "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
            "Content-Security-Policy": "default-src 'self'",
            "X-API-Version": "1.0",
            "Cache-Control": "no-store, no-cache, must-revalidate, max-age=0"
        }
    
    @staticmethod
    def add_cors_headers(allowed_origins=None):
        """
        Add CORS headers for API responses
        
        Args:
            allowed_origins: List of allowed origins
            
        Returns:
            dict: CORS headers
        """
        if allowed_origins is None:
            allowed_origins = ["https://logistilink.com"]
        
        return {
            "Access-Control-Allow-Origin": ",".join(allowed_origins),
            "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
            "Access-Control-Allow-Headers": "Authorization, Content-Type, X-API-Key",
            "Access-Control-Max-Age": "86400"
        }

def secure_api_endpoint(endpoint_type="general"):
    """
    Decorator for securing API endpoints
    
    Args:
        endpoint_type: Type of endpoint for specific rate limiting
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            # Add API security validation here
            api_manager = APISecurityManager()
            rate_limiter = APIRateLimiter()
            
            # Extract API key from request (implementation depends on framework)
            api_key = kwargs.get("api_key") or args[0] if args else None
            
            if api_key:
                key_info = api_manager.validate_api_key(api_key)
                if not key_info:
                    return {"error": "Invalid API key", "status": 401}
                
                # Check rate limit
                if not rate_limiter.check_endpoint_rate_limit(endpoint_type, key_info["user_id"]):
                    return {"error": "Rate limit exceeded", "status": 429}
            
            return func(*args, **kwargs)
        return wrapper
    return decorator