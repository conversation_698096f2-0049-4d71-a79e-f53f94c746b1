"""
Invoice Audit & Reconciliation System
Comprehensive invoice management with automated reconciliation and audit trails
"""

import os
import django
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from decimal import Decimal
import json
import asyncio
from dataclasses import dataclass
from asgiref.sync import sync_to_async
import uuid

# Configure Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'cloverics_django.cloverics.settings')
django.setup()

from core.models import User, Notification
from shipments.models import Shipment, ShippingRate, QuoteRequest
from payments.models import Payment

@dataclass
class InvoiceDiscrepancy:
    """Invoice discrepancy data structure"""
    discrepancy_type: str  # 'amount', 'service', 'date', 'carrier'
    expected_value: str
    actual_value: str
    severity: str  # 'critical', 'high', 'medium', 'low'
    description: str
    auto_resolvable: bool = False

@dataclass
class ReconciliationResult:
    """Reconciliation result data structure"""
    status: str  # 'matched', 'discrepancy', 'missing', 'duplicate'
    confidence_score: float
    discrepancies: List[InvoiceDiscrepancy]
    recommendations: List[str]
    auto_approval_eligible: bool = False

class InvoiceReconciliationEngine:
    """
    Advanced invoice reconciliation with machine learning-based matching
    """
    
    def __init__(self):
        self.matching_thresholds = {
            'amount_tolerance': 0.02,  # 2% tolerance
            'date_tolerance': 3,  # 3 days
            'exact_match_bonus': 0.3,
            'partial_match_penalty': 0.15
        }
        
        self.severity_weights = {
            'critical': 1.0,
            'high': 0.8,
            'medium': 0.5,
            'low': 0.2
        }
    
    @sync_to_async
    def create_invoice_record(self, user_id: int, invoice_data: Dict) -> Dict:
        """Create a new invoice record for reconciliation"""
        try:
            from django.core.exceptions import ValidationError
            
            # Validate required fields
            required_fields = ['invoice_number', 'amount', 'carrier_name', 'service_date']
            for field in required_fields:
                if not invoice_data.get(field):
                    return {'success': False, 'error': f'Missing required field: {field}'}
            
            # Generate invoice ID
            invoice_id = f"INV_{datetime.now().strftime('%Y%m%d')}_{str(uuid.uuid4())[:8].upper()}"
            
            # Create invoice record
            user = User.objects.get(id=user_id)
            
            invoice_record = {
                'type': 'invoice_record',
                'invoice_id': invoice_id,
                'invoice_number': invoice_data['invoice_number'],
                'amount': float(invoice_data['amount']),
                'currency': invoice_data.get('currency', 'USD'),
                'carrier_name': invoice_data['carrier_name'],
                'service_date': invoice_data['service_date'],
                'due_date': invoice_data.get('due_date'),
                'tracking_number': invoice_data.get('tracking_number'),
                'shipment_reference': invoice_data.get('shipment_reference'),
                'service_type': invoice_data.get('service_type'),
                'origin': invoice_data.get('origin'),
                'destination': invoice_data.get('destination'),
                'weight': float(invoice_data.get('weight', 0)) if invoice_data.get('weight') else None,
                'dimensions': invoice_data.get('dimensions'),
                'created_at': datetime.now().isoformat(),
                'status': 'pending_reconciliation',
                'reconciliation_status': 'unmatched',
                'audit_trail': [],
                'discrepancies': [],
                'auto_matched': False,
                'approval_required': True
            }
            
            # Store as notification with special type
            Notification.objects.create(
                user=user,
                title=f"Invoice Record: {invoice_data['invoice_number']}",
                message=json.dumps(invoice_record),
                type='invoice_record',
                read=True  # Mark as read since it's not a traditional notification
            )
            
            return {
                'success': True,
                'invoice_id': invoice_id,
                'message': 'Invoice record created successfully'
            }
            
        except Exception as e:
            return {'success': False, 'error': f'Error creating invoice record: {str(e)}'}
    
    @sync_to_async
    def get_user_invoices(self, user_id: int, status_filter: str = None) -> Dict:
        """Get user's invoice records with optional status filtering"""
        try:
            user = User.objects.get(id=user_id)
            
            # Get invoice notifications
            invoice_notifications = Notification.objects.filter(
                user=user,
                type='invoice_record'
            ).order_by('-created_at')
            
            invoices = []
            for notification in invoice_notifications:
                try:
                    invoice_data = json.loads(notification.message)
                    if status_filter and invoice_data.get('status') != status_filter:
                        continue
                    
                    invoices.append({
                        'id': notification.id,
                        'invoice_id': invoice_data.get('invoice_id'),
                        'invoice_number': invoice_data.get('invoice_number'),
                        'amount': invoice_data.get('amount'),
                        'currency': invoice_data.get('currency', 'USD'),
                        'carrier_name': invoice_data.get('carrier_name'),
                        'service_date': invoice_data.get('service_date'),
                        'status': invoice_data.get('status'),
                        'reconciliation_status': invoice_data.get('reconciliation_status'),
                        'discrepancies': invoice_data.get('discrepancies', []),
                        'auto_matched': invoice_data.get('auto_matched', False),
                        'created_at': invoice_data.get('created_at')
                    })
                except:
                    continue
            
            return {
                'success': True,
                'invoices': invoices,
                'total_count': len(invoices),
                'status_counts': self._calculate_status_counts(invoices)
            }
            
        except Exception as e:
            return {'success': False, 'error': f'Error fetching invoices: {str(e)}'}
    
    def _calculate_status_counts(self, invoices: List[Dict]) -> Dict:
        """Calculate status distribution for invoices"""
        counts = {
            'pending_reconciliation': 0,
            'reconciled': 0,
            'discrepancy': 0,
            'approved': 0,
            'disputed': 0
        }
        
        for invoice in invoices:
            status = invoice.get('status', 'pending_reconciliation')
            if status in counts:
                counts[status] += 1
        
        return counts
    
    @sync_to_async
    def perform_automated_reconciliation(self, user_id: int, invoice_id: str) -> Dict:
        """Perform automated reconciliation for an invoice"""
        try:
            user = User.objects.get(id=user_id)
            
            # Find the invoice record
            target_invoice = None
            invoice_notification = None
            
            notifications = Notification.objects.filter(
                user=user,
                type='invoice_record'
            )
            
            for notification in notifications:
                try:
                    invoice_data = json.loads(notification.message)
                    if invoice_data.get('invoice_id') == invoice_id:
                        target_invoice = invoice_data
                        invoice_notification = notification
                        break
                except:
                    continue
            
            if not target_invoice:
                return {'success': False, 'error': 'Invoice not found'}
            
            # Find matching shipments and payments
            potential_matches = self._find_potential_matches(target_invoice)
            
            # Perform reconciliation analysis
            reconciliation_result = self._analyze_matches(target_invoice, potential_matches)
            
            # Update invoice record with reconciliation results
            target_invoice['reconciliation_result'] = {
                'status': reconciliation_result.status,
                'confidence_score': reconciliation_result.confidence_score,
                'discrepancies': [
                    {
                        'type': d.discrepancy_type,
                        'expected': d.expected_value,
                        'actual': d.actual_value,
                        'severity': d.severity,
                        'description': d.description,
                        'auto_resolvable': d.auto_resolvable
                    }
                    for d in reconciliation_result.discrepancies
                ],
                'recommendations': reconciliation_result.recommendations,
                'auto_approval_eligible': reconciliation_result.auto_approval_eligible,
                'reconciled_at': datetime.now().isoformat()
            }
            
            # Update status based on reconciliation result
            if reconciliation_result.status == 'matched' and reconciliation_result.auto_approval_eligible:
                target_invoice['status'] = 'reconciled'
                target_invoice['reconciliation_status'] = 'auto_matched'
                target_invoice['auto_matched'] = True
            elif len(reconciliation_result.discrepancies) > 0:
                target_invoice['status'] = 'discrepancy'
                target_invoice['reconciliation_status'] = 'requires_review'
            else:
                target_invoice['status'] = 'reconciled'
                target_invoice['reconciliation_status'] = 'matched'
            
            # Add audit trail entry
            audit_entry = {
                'timestamp': datetime.now().isoformat(),
                'action': 'automated_reconciliation',
                'user_id': user_id,
                'result': reconciliation_result.status,
                'confidence_score': reconciliation_result.confidence_score,
                'discrepancy_count': len(reconciliation_result.discrepancies)
            }
            target_invoice['audit_trail'].append(audit_entry)
            
            # Save updated invoice record
            invoice_notification.message = json.dumps(target_invoice)
            invoice_notification.save()
            
            return {
                'success': True,
                'reconciliation_result': {
                    'status': reconciliation_result.status,
                    'confidence_score': reconciliation_result.confidence_score,
                    'discrepancies': reconciliation_result.discrepancies,
                    'recommendations': reconciliation_result.recommendations,
                    'auto_approval_eligible': reconciliation_result.auto_approval_eligible
                }
            }
            
        except Exception as e:
            return {'success': False, 'error': f'Error performing reconciliation: {str(e)}'}
    
    @sync_to_async
    def _find_potential_matches(self, invoice_data: Dict) -> List[Dict]:
        """Find potential matches for invoice reconciliation"""
        try:
            matches = []
            
            # Search shipments
            shipments = Shipment.objects.all()[:50]  # Limit for performance
            
            for shipment in shipments:
                try:
                    match_score = self._calculate_shipment_match_score(invoice_data, shipment)
                    if match_score > 0.3:  # Minimum threshold
                        matches.append({
                            'type': 'shipment',
                            'id': shipment.id,
                            'tracking_number': shipment.tracking_number,
                            'origin': f"{shipment.origin_city}, {shipment.origin_country}",
                            'destination': f"{shipment.destination_city}, {shipment.destination_country}",
                            'total_price': float(shipment.total_price) if shipment.total_price else 0,
                            'created_at': shipment.created_at.isoformat() if shipment.created_at else None,
                            'match_score': match_score
                        })
                except:
                    continue
            
            # Search payments
            payments = Payment.objects.all()[:50]  # Limit for performance
            
            for payment in payments:
                try:
                    match_score = self._calculate_payment_match_score(invoice_data, payment)
                    if match_score > 0.3:  # Minimum threshold
                        matches.append({
                            'type': 'payment',
                            'id': payment.id,
                            'amount': float(payment.amount),
                            'created_at': payment.created_at.isoformat() if payment.created_at else None,
                            'status': payment.status,
                            'match_score': match_score
                        })
                except:
                    continue
            
            # Sort by match score
            matches.sort(key=lambda x: x['match_score'], reverse=True)
            
            return matches[:10]  # Return top 10 matches
            
        except Exception as e:
            return []
    
    def _calculate_shipment_match_score(self, invoice_data: Dict, shipment) -> float:
        """Calculate match score between invoice and shipment"""
        score = 0.0
        
        # Amount matching
        invoice_amount = invoice_data.get('amount', 0)
        shipment_amount = float(shipment.total_price) if shipment.total_price else 0
        
        if shipment_amount > 0:
            amount_diff = abs(invoice_amount - shipment_amount) / shipment_amount
            if amount_diff <= self.matching_thresholds['amount_tolerance']:
                score += 0.4
            elif amount_diff <= 0.1:  # 10% tolerance
                score += 0.2
        
        # Tracking number matching
        invoice_tracking = invoice_data.get('tracking_number', '').lower()
        shipment_tracking = getattr(shipment, 'tracking_number', '').lower()
        
        if invoice_tracking and shipment_tracking:
            if invoice_tracking == shipment_tracking:
                score += 0.3
            elif invoice_tracking in shipment_tracking or shipment_tracking in invoice_tracking:
                score += 0.15
        
        # Date matching
        invoice_date = invoice_data.get('service_date')
        if invoice_date and hasattr(shipment, 'created_at') and shipment.created_at:
            try:
                invoice_dt = datetime.fromisoformat(invoice_date.replace('Z', '+00:00'))
                shipment_dt = shipment.created_at
                date_diff = abs((invoice_dt.date() - shipment_dt.date()).days)
                
                if date_diff <= self.matching_thresholds['date_tolerance']:
                    score += 0.2
                elif date_diff <= 7:  # 1 week tolerance
                    score += 0.1
            except:
                pass
        
        # Reference matching
        invoice_ref = invoice_data.get('shipment_reference', '').lower()
        if invoice_ref and hasattr(shipment, 'id'):
            if str(shipment.id) in invoice_ref or invoice_ref in str(shipment.id):
                score += 0.1
        
        return min(score, 1.0)
    
    def _calculate_payment_match_score(self, invoice_data: Dict, payment) -> float:
        """Calculate match score between invoice and payment"""
        score = 0.0
        
        # Amount matching
        invoice_amount = invoice_data.get('amount', 0)
        payment_amount = float(payment.amount) if payment.amount else 0
        
        if payment_amount > 0:
            amount_diff = abs(invoice_amount - payment_amount) / payment_amount
            if amount_diff <= self.matching_thresholds['amount_tolerance']:
                score += 0.5
            elif amount_diff <= 0.05:  # 5% tolerance
                score += 0.3
        
        # Date matching
        invoice_date = invoice_data.get('service_date')
        if invoice_date and hasattr(payment, 'created_at') and payment.created_at:
            try:
                invoice_dt = datetime.fromisoformat(invoice_date.replace('Z', '+00:00'))
                payment_dt = payment.created_at
                date_diff = abs((invoice_dt.date() - payment_dt.date()).days)
                
                if date_diff <= self.matching_thresholds['date_tolerance']:
                    score += 0.3
                elif date_diff <= 14:  # 2 weeks tolerance
                    score += 0.15
            except:
                pass
        
        # Status consideration
        if hasattr(payment, 'status') and payment.status == 'completed':
            score += 0.2
        
        return min(score, 1.0)
    
    def _analyze_matches(self, invoice_data: Dict, matches: List[Dict]) -> ReconciliationResult:
        """Analyze matches and generate reconciliation result"""
        if not matches:
            return ReconciliationResult(
                status='missing',
                confidence_score=0.0,
                discrepancies=[InvoiceDiscrepancy(
                    discrepancy_type='missing',
                    expected_value='Matching record',
                    actual_value='No matches found',
                    severity='high',
                    description='No matching shipments or payments found for this invoice'
                )],
                recommendations=['Manual review required', 'Check for data entry errors', 'Verify invoice authenticity'],
                auto_approval_eligible=False
            )
        
        best_match = matches[0]
        confidence_score = best_match['match_score']
        discrepancies = []
        recommendations = []
        
        # Analyze amount discrepancies
        invoice_amount = invoice_data.get('amount', 0)
        if best_match['type'] == 'shipment':
            match_amount = best_match.get('total_price', 0)
        else:
            match_amount = best_match.get('amount', 0)
        
        if match_amount > 0:
            amount_diff_pct = abs(invoice_amount - match_amount) / match_amount
            if amount_diff_pct > self.matching_thresholds['amount_tolerance']:
                severity = 'critical' if amount_diff_pct > 0.1 else 'medium'
                discrepancies.append(InvoiceDiscrepancy(
                    discrepancy_type='amount',
                    expected_value=f'${match_amount:.2f}',
                    actual_value=f'${invoice_amount:.2f}',
                    severity=severity,
                    description=f'Amount difference of {amount_diff_pct*100:.1f}%',
                    auto_resolvable=amount_diff_pct <= 0.05
                ))
        
        # Generate recommendations based on analysis
        if confidence_score >= 0.8:
            recommendations.append('High confidence match - consider auto-approval')
        elif confidence_score >= 0.6:
            recommendations.append('Good match - review discrepancies before approval')
        else:
            recommendations.append('Low confidence match - manual review required')
        
        if len(discrepancies) == 0:
            recommendations.append('No discrepancies found - approve for payment')
        elif all(d.auto_resolvable for d in discrepancies):
            recommendations.append('Minor discrepancies detected - auto-resolution possible')
        
        # Determine status
        if confidence_score >= 0.8 and len(discrepancies) == 0:
            status = 'matched'
        elif len(discrepancies) > 0:
            status = 'discrepancy'
        else:
            status = 'matched'
        
        # Auto-approval eligibility
        auto_approval_eligible = (
            confidence_score >= 0.9 and
            len(discrepancies) == 0 and
            invoice_amount <= 10000  # Amount threshold for auto-approval
        )
        
        return ReconciliationResult(
            status=status,
            confidence_score=confidence_score,
            discrepancies=discrepancies,
            recommendations=recommendations,
            auto_approval_eligible=auto_approval_eligible
        )
    
    @sync_to_async
    def approve_invoice(self, user_id: int, invoice_id: str, approval_notes: str = None) -> Dict:
        """Approve an invoice for payment"""
        try:
            user = User.objects.get(id=user_id)
            
            # Find and update invoice
            notifications = Notification.objects.filter(
                user=user,
                type='invoice_record'
            )
            
            for notification in notifications:
                try:
                    invoice_data = json.loads(notification.message)
                    if invoice_data.get('invoice_id') == invoice_id:
                        # Update status
                        invoice_data['status'] = 'approved'
                        invoice_data['approval_date'] = datetime.now().isoformat()
                        invoice_data['approval_notes'] = approval_notes or ''
                        
                        # Add audit trail
                        audit_entry = {
                            'timestamp': datetime.now().isoformat(),
                            'action': 'manual_approval',
                            'user_id': user_id,
                            'notes': approval_notes or ''
                        }
                        invoice_data['audit_trail'].append(audit_entry)
                        
                        # Save
                        notification.message = json.dumps(invoice_data)
                        notification.save()
                        
                        return {'success': True, 'message': 'Invoice approved successfully'}
                except:
                    continue
            
            return {'success': False, 'error': 'Invoice not found'}
            
        except Exception as e:
            return {'success': False, 'error': f'Error approving invoice: {str(e)}'}
    
    @sync_to_async
    def dispute_invoice(self, user_id: int, invoice_id: str, dispute_reason: str) -> Dict:
        """Mark an invoice as disputed"""
        try:
            user = User.objects.get(id=user_id)
            
            # Find and update invoice
            notifications = Notification.objects.filter(
                user=user,
                type='invoice_record'
            )
            
            for notification in notifications:
                try:
                    invoice_data = json.loads(notification.message)
                    if invoice_data.get('invoice_id') == invoice_id:
                        # Update status
                        invoice_data['status'] = 'disputed'
                        invoice_data['dispute_date'] = datetime.now().isoformat()
                        invoice_data['dispute_reason'] = dispute_reason
                        
                        # Add audit trail
                        audit_entry = {
                            'timestamp': datetime.now().isoformat(),
                            'action': 'dispute_raised',
                            'user_id': user_id,
                            'reason': dispute_reason
                        }
                        invoice_data['audit_trail'].append(audit_entry)
                        
                        # Save
                        notification.message = json.dumps(invoice_data)
                        notification.save()
                        
                        return {'success': True, 'message': 'Invoice dispute raised successfully'}
                except:
                    continue
            
            return {'success': False, 'error': 'Invoice not found'}
            
        except Exception as e:
            return {'success': False, 'error': f'Error disputing invoice: {str(e)}'}

class InvoiceAnalyticsEngine:
    """
    Analytics and insights for invoice reconciliation
    """
    
    @sync_to_async
    def get_reconciliation_analytics(self, user_id: int, period_days: int = 30) -> Dict:
        """Get reconciliation analytics for specified period"""
        try:
            user = User.objects.get(id=user_id)
            
            # Get invoice records from specified period
            cutoff_date = datetime.now() - timedelta(days=period_days)
            
            invoice_notifications = Notification.objects.filter(
                user=user,
                type='invoice_record',
                created_at__gte=cutoff_date
            )
            
            analytics = {
                'total_invoices': 0,
                'auto_matched': 0,
                'manual_review': 0,
                'approved': 0,
                'disputed': 0,
                'pending': 0,
                'total_amount': 0,
                'average_confidence_score': 0,
                'discrepancy_rate': 0,
                'auto_approval_rate': 0,
                'top_discrepancy_types': {},
                'carrier_performance': {},
                'monthly_trends': [],
                'processing_time_avg': 0
            }
            
            confidence_scores = []
            discrepancy_count = 0
            
            for notification in invoice_notifications:
                try:
                    invoice_data = json.loads(notification.message)
                    analytics['total_invoices'] += 1
                    
                    # Amount tracking
                    amount = invoice_data.get('amount', 0)
                    analytics['total_amount'] += amount
                    
                    # Status tracking
                    status = invoice_data.get('status', 'pending')
                    if status == 'approved':
                        analytics['approved'] += 1
                    elif status == 'disputed':
                        analytics['disputed'] += 1
                    elif status == 'pending_reconciliation':
                        analytics['pending'] += 1
                    
                    # Auto-matching tracking
                    if invoice_data.get('auto_matched', False):
                        analytics['auto_matched'] += 1
                    
                    # Reconciliation analysis
                    reconciliation_result = invoice_data.get('reconciliation_result', {})
                    if reconciliation_result:
                        confidence_score = reconciliation_result.get('confidence_score', 0)
                        confidence_scores.append(confidence_score)
                        
                        discrepancies = reconciliation_result.get('discrepancies', [])
                        if discrepancies:
                            discrepancy_count += 1
                            
                            # Track discrepancy types
                            for discrepancy in discrepancies:
                                disc_type = discrepancy.get('type', 'unknown')
                                analytics['top_discrepancy_types'][disc_type] = \
                                    analytics['top_discrepancy_types'].get(disc_type, 0) + 1
                    
                    # Carrier performance
                    carrier = invoice_data.get('carrier_name', 'Unknown')
                    if carrier not in analytics['carrier_performance']:
                        analytics['carrier_performance'][carrier] = {
                            'total_invoices': 0,
                            'auto_matched': 0,
                            'discrepancies': 0,
                            'total_amount': 0
                        }
                    
                    analytics['carrier_performance'][carrier]['total_invoices'] += 1
                    analytics['carrier_performance'][carrier]['total_amount'] += amount
                    
                    if invoice_data.get('auto_matched', False):
                        analytics['carrier_performance'][carrier]['auto_matched'] += 1
                    
                    if reconciliation_result.get('discrepancies'):
                        analytics['carrier_performance'][carrier]['discrepancies'] += 1
                
                except:
                    continue
            
            # Calculate rates and averages
            if analytics['total_invoices'] > 0:
                analytics['auto_approval_rate'] = (analytics['auto_matched'] / analytics['total_invoices']) * 100
                analytics['discrepancy_rate'] = (discrepancy_count / analytics['total_invoices']) * 100
                analytics['manual_review'] = analytics['total_invoices'] - analytics['auto_matched']
            
            if confidence_scores:
                analytics['average_confidence_score'] = sum(confidence_scores) / len(confidence_scores)
            
            return {
                'success': True,
                'analytics': analytics,
                'period_days': period_days
            }
            
        except Exception as e:
            return {'success': False, 'error': f'Error getting analytics: {str(e)}'}

# Global instances
invoice_reconciliation_engine = InvoiceReconciliationEngine()
invoice_analytics_engine = InvoiceAnalyticsEngine()