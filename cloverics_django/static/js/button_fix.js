// Button Clickability Fix for Landing Page
// Ensures Login and Register buttons are clickable

document.addEventListener('DOMContentLoaded', function() {
    console.log('🔧 Fixing button clickability...');
    
    // Get all buttons
    const buttons = document.querySelectorAll('.btn');
    
    buttons.forEach(function(btn) {
        // Ensure buttons are clickable
        btn.style.cursor = 'pointer';
        btn.style.pointerEvents = 'auto';
        btn.style.zIndex = '1000';
        btn.style.position = 'relative';
        btn.style.display = 'inline-block';
        
        // Add hover effects
        btn.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
            this.style.transition = 'all 0.3s ease';
        });
        
        btn.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
        
        // Ensure click events work
        btn.addEventListener('click', function(e) {
            console.log('Button clicked:', this.textContent, 'URL:', this.href);
            // Let the default navigation happen
            return true;
        });
    });
    
    console.log('✅ Button clickability fixed for', buttons.length, 'buttons');
});