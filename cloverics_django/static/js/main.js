// Cloverics Django Main JavaScript

document.addEventListener('DOMContentLoaded', function() {
    
    // Debug sidebar layout
    console.log('Initializing sidebar layout...');
    const sidebarElement = document.getElementById('sidebar');
    const contentElement = document.getElementById('content');
    
    if (sidebarElement) {
        console.log('Sidebar found:', sidebarElement);
        console.log('Sidebar classes:', sidebarElement.className);
        console.log('Sidebar computed style:', window.getComputedStyle(sidebarElement));
    }
    
    if (contentElement) {
        console.log('Content found:', contentElement);
        console.log('Content classes:', contentElement.className);
        console.log('Content computed style:', window.getComputedStyle(contentElement));
    }
    
    // Fix button clickability issues
    console.log('Fixing button clickability...');
    
    // Find all buttons and make sure they're clickable
    const buttons = document.querySelectorAll('button, .btn, a[role="button"]');
    buttons.forEach(button => {
        button.style.pointerEvents = 'auto';
        button.style.zIndex = '10';
    });
    
    console.log('Button clickability fixed for', buttons.length, 'buttons');
    
    // Sidebar toggle functionality
    const sidebarToggle = document.getElementById('sidebarCollapse');
    const sidebar = document.getElementById('sidebar');
    const content = document.getElementById('content');
    
    if (sidebarToggle && sidebar && content) {
        sidebarToggle.addEventListener('click', function() {
            sidebar.classList.toggle('collapsed');
            content.classList.toggle('expanded');
            
            // Update toggle button icon
            const icon = sidebarToggle.querySelector('i');
            if (sidebar.classList.contains('collapsed')) {
                icon.classList.remove('fa-align-left');
                icon.classList.add('fa-align-right');
            } else {
                icon.classList.remove('fa-align-right');
                icon.classList.add('fa-align-left');
            }
        });
    }
    
    // Bootstrap dropdown toggle functionality
    const dropdownToggles = document.querySelectorAll('.dropdown-toggle');
    dropdownToggles.forEach(toggle => {
        // Listen for Bootstrap's collapse events instead of custom click handling
        const targetId = toggle.getAttribute('data-bs-target');
        const target = document.querySelector(targetId);
        
        if (target) {
            // Listen for show.bs.collapse event
            target.addEventListener('show.bs.collapse', function() {
                // Close other open dropdowns
                dropdownToggles.forEach(otherToggle => {
                    if (otherToggle !== toggle) {
                        const otherTarget = document.querySelector(otherToggle.getAttribute('data-bs-target'));
                        if (otherTarget && otherTarget.classList.contains('show')) {
                            // Use Bootstrap's collapse method to close
                            const bsCollapse = new bootstrap.Collapse(otherTarget, {
                                toggle: false
                            });
                            bsCollapse.hide();
                        }
                    }
                });
                
                // Update chevron for current dropdown
                const chevron = toggle.querySelector('.fa-chevron-down, .fa-chevron-up');
                if (chevron) {
                    chevron.classList.remove('fa-chevron-down');
                    chevron.classList.add('fa-chevron-up');
                }
                toggle.setAttribute('aria-expanded', 'true');
            });
            
            // Listen for hide.bs.collapse event
            target.addEventListener('hide.bs.collapse', function() {
                // Update chevron for current dropdown
                const chevron = toggle.querySelector('.fa-chevron-down, .fa-chevron-up');
                if (chevron) {
                    chevron.classList.remove('fa-chevron-up');
                    chevron.classList.add('fa-chevron-down');
                }
                toggle.setAttribute('aria-expanded', 'false');
            });
        }
    });
    
    // Form validation enhancement
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const requiredFields = form.querySelectorAll('[required]');
            let isValid = true;
            
            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    field.classList.add('is-invalid');
                    isValid = false;
                } else {
                    field.classList.remove('is-invalid');
                    field.classList.add('is-valid');
                }
            });
            
            if (!isValid) {
                e.preventDefault();
                alert('Please fill in all required fields.');
            }
        });
    });
    
    // Auto-dismiss alerts after 5 seconds
    const alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
    alerts.forEach(alert => {
        setTimeout(() => {
            if (alert.parentNode) {
                alert.style.transition = 'opacity 0.5s ease';
                alert.style.opacity = '0';
                setTimeout(() => {
                    if (alert.parentNode) {
                        alert.remove();
                    }
                }, 500);
            }
        }, 5000);
    });
    
    // Initialize tooltips if Bootstrap tooltips are available
    if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function(tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }
    
    // Initialize popovers if Bootstrap popovers are available
    if (typeof bootstrap !== 'undefined' && bootstrap.Popover) {
        const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
        popoverTriggerList.map(function(popoverTriggerEl) {
            return new bootstrap.Popover(popoverTriggerEl);
        });
    }
    
    // Add smooth scrolling to anchor links
    const anchorLinks = document.querySelectorAll('a[href^="#"]');
    anchorLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                e.preventDefault();
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
    
    // Loading states for forms
    const submitButtons = document.querySelectorAll('button[type="submit"], input[type="submit"]');
    submitButtons.forEach(button => {
        button.addEventListener('click', function() {
            const form = this.closest('form');
            if (form && form.checkValidity()) {
                this.disabled = true;
                this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
                
                // Re-enable after 5 seconds to prevent permanent disable on errors
                setTimeout(() => {
                    this.disabled = false;
                    this.innerHTML = this.getAttribute('data-original-text') || 'Submit';
                }, 5000);
            }
        });
        
        // Store original text
        button.setAttribute('data-original-text', button.innerHTML);
    });
});

// Utility functions
function showToast(message, type = 'info', duration = 3000) {
    // Create toast element
    const toast = document.createElement('div');
    toast.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(toast);
    
    // Auto remove after duration
    setTimeout(() => {
        if (toast.parentNode) {
            toast.remove();
        }
    }, duration);
}

function formatCurrency(amount, currency = 'USD') {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currency
    }).format(amount);
}

function formatDate(date, options = {}) {
    const defaultOptions = {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    };
    return new Date(date).toLocaleDateString('en-US', { ...defaultOptions, ...options });
}

// Export functions for global use
window.showToast = showToast;
window.formatCurrency = formatCurrency;
window.formatDate = formatDate;