/* Cloverics Django Main CSS */

/* Custom Bootstrap variables and base styles */
:root {
    --primary-color: #0056b3;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
}

/* Global styles */
html, body {
    height: 100%;
    margin: 0;
    padding: 0;
}

body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    overflow-x: hidden;
}

/* Navigation styles */
.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

.navbar-brand i {
    margin-right: 0.5rem;
}

/* Card styles */
.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: box-shadow 0.15s ease-in-out;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

/* Form styles */
.form-control {
    border-radius: 5px;
    border: 1px solid #dee2e6;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 86, 179, 0.25);
}

.btn {
    border-radius: 5px;
    font-weight: 500;
    transition: all 0.15s ease-in-out;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: #004085;
    border-color: #004085;
}

/* Dashboard styles */
.dashboard-stat-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 1rem;
}

.dashboard-stat-card h3 {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    font-weight: bold;
}

.dashboard-stat-card p {
    margin-bottom: 0;
    opacity: 0.9;
}

/* Table styles */
.table {
    background-color: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.table thead th {
    background-color: var(--primary-color);
    color: white;
    border: none;
    font-weight: 500;
}

.table tbody tr:hover {
    background-color: rgba(0, 86, 179, 0.05);
}

/* Status badges */
.badge-pending {
    background-color: var(--warning-color);
    color: #212529;
}

.badge-quoted {
    background-color: var(--info-color);
    color: white;
}

.badge-accepted {
    background-color: var(--success-color);
    color: white;
}

.badge-declined {
    background-color: var(--danger-color);
    color: white;
}

.badge-expired {
    background-color: var(--secondary-color);
    color: white;
}

/* Sidebar layout system */
.wrapper {
    display: flex;
    min-height: 100vh;
}

/* Sidebar styles */
.sidebar {
    position: fixed;
    top: 0;
    left: 0;
    width: 280px;
    height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0;
    overflow-y: auto;
    transition: margin-left 0.3s ease;
    z-index: 1000;
    box-shadow: 2px 0 5px rgba(0,0,0,0.1);
}

.sidebar.collapsed {
    margin-left: -280px;
}

.sidebar-collapsed {
    margin-left: 0 !important;
    width: 100% !important;
}

.sidebar-collapsed {
    margin-left: 0 !important;
    width: 100% !important;
}

.sidebar-header {
    padding: 2rem 1.5rem 1rem;
    text-align: center;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.sidebar-header h3 {
    margin: 0;
    font-size: 1.8rem;
    font-weight: bold;
}

.sidebar-subtitle {
    margin: 0.5rem 0 0;
    font-size: 0.9rem;
    opacity: 0.8;
}

.sidebar .components {
    padding: 1rem 0;
}

.sidebar .nav-item {
    margin: 0.25rem 0;
}

.sidebar .nav-link {
    color: rgba(255,255,255,0.9);
    padding: 0.75rem 1.5rem;
    margin: 0;
    border-radius: 0;
    transition: all 0.3s ease;
    text-decoration: none;
    display: block;
    border: none;
    background: none;
    width: 100%;
    text-align: left;
}

.sidebar .nav-link:hover,
.sidebar .nav-link.active {
    background-color: rgba(255,255,255,0.1);
    color: white;
    transform: translateX(5px);
}

.sidebar .nav-link i {
    margin-right: 0.75rem;
    width: 1.25rem;
    text-align: center;
}

/* Hide Bootstrap's default dropdown chevron */
.sidebar .dropdown-toggle::after {
    display: none !important;
}

/* Style the custom FontAwesome chevron */
.sidebar .dropdown-toggle .fa-chevron-down,
.sidebar .dropdown-toggle .fa-chevron-up {
    transition: transform 0.3s ease;
}

.sidebar .dropdown-toggle[aria-expanded="true"] .fa-chevron-down,
.sidebar .dropdown-toggle[aria-expanded="true"] .fa-chevron-up {
    transform: rotate(180deg);
}

.sidebar .sub-link {
    padding-left: 3rem;
    font-size: 0.9rem;
    opacity: 0.8;
}

.sidebar .sub-link:hover {
    opacity: 1;
}

/* Content area */
#content {
    margin-left: 280px;
    width: calc(100% - 280px);
    min-height: 100vh;
    transition: margin-left 0.3s ease, width 0.3s ease;
}

#content.expanded {
    margin-left: 0;
    width: 100%;
}

.content-full {
    margin-left: 0 !important;
    width: 100% !important;
}

/* Top navigation bar */
.top-navbar {
    background-color: white !important;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    padding: 0.75rem 1.5rem;
    margin-bottom: 1rem;
}

.main-content {
    padding: 1.5rem;
}

/* Responsive design */
@media (max-width: 768px) {
    .sidebar {
        margin-left: -280px;
        z-index: 1050;
    }
    
    .sidebar.show {
        margin-left: 0;
    }
    
    #content {
        margin-left: 0 !important;
        width: 100% !important;
    }
    
    .content-with-sidebar {
        margin-left: 0 !important;
        width: 100% !important;
    }
    
    .main-content {
        padding: 1rem;
    }
}

/* Footer styles */
footer {
    margin-top: auto;
}

/* Animation utilities */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Loading spinner */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Custom utilities */
.text-primary-dark {
    color: #004085 !important;
}

.bg-primary-light {
    background-color: rgba(0, 86, 179, 0.1) !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .dashboard-stat-card {
        margin-bottom: 1rem;
        padding: 1rem;
    }
    
    .dashboard-stat-card h3 {
        font-size: 1.5rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
}

/* Form validation styles */
.is-invalid {
    border-color: var(--danger-color);
}

.is-valid {
    border-color: var(--success-color);
}

.invalid-feedback {
    color: var(--danger-color);
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

.valid-feedback {
    color: var(--success-color);
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

/* Alert styles */
.alert {
    border: none;
    border-radius: 10px;
    border-left: 4px solid;
}

.alert-primary {
    border-left-color: var(--primary-color);
}

.alert-success {
    border-left-color: var(--success-color);
}

.alert-danger {
    border-left-color: var(--danger-color);
}

.alert-warning {
    border-left-color: var(--warning-color);
}

.alert-info {
    border-left-color: var(--info-color);
}