{% extends "base_public.html" %}
{% load translation_tags %}

{% block content %}
<div class="landing-page">
    <!-- Language Selector for Public Pages -->
    <div class="position-fixed top-0 end-0 p-3" style="z-index: 1000;">
        {% include 'components/language_selector.html' %}
    </div>

    <div class="hero-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <h1 class="hero-title">🍀 {% translate 'app_name' %}</h1>
                    <h2 class="hero-subtitle">{% translate 'tagline' %}</h2>
                    <p class="hero-description">{% translate 'welcome_message' %}</p>
                    <div class="hero-buttons">
                        <a href="/register" class="btn btn-primary btn-lg me-3">{% translate 'register' %}</a>
                        <a href="/login" class="btn btn-outline-light btn-lg">{% translate 'login' %}</a>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="hero-stats">
                        <div class="stat-card">
                            <h3 class="stat-number">{{ stats.total_users }}</h3>
                            <p class="stat-label">{% translate 'active_users' %}</p>
                        </div>
                        <div class="stat-card">
                            <h3 class="stat-number">{{ stats.completed_shipments }}</h3>
                            <p class="stat-label">{% translate 'completed_shipments' %}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Features Section -->
    <div class="features-section py-5">
        <div class="container">
            <div class="row text-center mb-5">
                <div class="col-12">
                    <h2>{% translate 'why_choose_cloverics' %}</h2>
                    <p class="lead">{% translate 'comprehensive_logistics_solutions' %}</p>
                </div>
            </div>
            <div class="row">
                <div class="col-md-4 mb-4">
                    <div class="feature-card">
                        <i class="fas fa-shipping-fast feature-icon"></i>
                        <h4>{% translate 'global_shipping' %}</h4>
                        <p>{% translate 'global_shipping_description' %}</p>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="feature-card">
                        <i class="fas fa-map-marked-alt feature-icon"></i>
                        <h4>{% translate 'real_time_tracking' %}</h4>
                        <p>{% translate 'real_time_tracking_description' %}</p>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="feature-card">
                        <i class="fas fa-shield-alt feature-icon"></i>
                        <h4>{% translate 'secure_platform' %}</h4>
                        <p>{% translate 'secure_platform_description' %}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Clean navigation for public homepage
document.addEventListener('DOMContentLoaded', function() {
    // Simple button navigation
    const registerBtn = document.querySelector('a[href="/register"]');
    const loginBtn = document.querySelector('a[href="/login"]');
    
    if (registerBtn) {
        registerBtn.addEventListener('click', function(e) {
            window.location.href = '/register';
        });
    }
    
    if (loginBtn) {
        loginBtn.addEventListener('click', function(e) {
            window.location.href = '/login';
        });
    }
});
</script>
{% endblock %}