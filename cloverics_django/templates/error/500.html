{% extends "base.html" %}
{% load translation_tags %}

{% block title %}System Error - Cloverics{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-body text-center">
                    <div class="error-icon mb-4">
                        <i class="fas fa-exclamation-triangle fa-4x text-warning"></i>
                    </div>
                    <h2 class="card-title">{% translate 'system_error' %}</h2>
                    <p class="card-text text-muted">
                        {% translate 'system_error_message' %}
                    </p>
                    <div class="mt-4">
                        <a href="/dashboard" class="btn btn-primary">
                            <i class="fas fa-home"></i> {% translate 'return_to_dashboard' %}
                        </a>
                        <a href="/" class="btn btn-outline-secondary">
                            <i class="fas fa-globe"></i> {% translate 'go_to_homepage' %}
                        </a>
                    </div>
                    {% if error %}
                    <div class="mt-4">
                        <details>
                            <summary>{% translate 'technical_details' %}</summary>
                            <pre class="text-left mt-2">{{ error }}</pre>
                        </details>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}