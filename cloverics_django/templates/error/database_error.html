{% extends "base.html" %}

{% block title %}Database Connection Error - Cloverics{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-exclamation-triangle"></i>
                        Database Connection Error
                    </h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <h5>Connection Temporarily Unavailable</h5>
                        <p class="mb-3">
                            We're experiencing a temporary database connection issue. 
                            This is usually resolved automatically within a few seconds.
                        </p>
                        
                        <h6>What you can do:</h6>
                        <ul class="mb-3">
                            <li>Wait 10-15 seconds and try again</li>
                            <li>Refresh this page</li>
                            <li>Clear your browser cache if the issue persists</li>
                        </ul>
                        
                        <div class="mt-3">
                            <button onclick="location.reload()" class="btn btn-primary">
                                <i class="fas fa-sync-alt"></i> Try Again
                            </button>
                            <a href="/" class="btn btn-secondary ml-2">
                                <i class="fas fa-home"></i> Go Home
                            </a>
                        </div>
                    </div>
                    
                    <div class="mt-4">
                        <h6>Technical Information:</h6>
                        <small class="text-muted">
                            Error Code: DB_CONNECTION_LOST<br>
                            Time: <span id="error-time"></span><br>
                            Status: Automatic recovery in progress
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.getElementById('error-time').textContent = new Date().toLocaleString();

// Auto refresh after 10 seconds
setTimeout(function() {
    if (confirm('Would you like to try connecting again?')) {
        location.href = '/';
    }
}, 10000);


// Auto-generated function implementations

function location.reload() {
    // Refresh functionality
    location.reload();
}
</script>
{% endblock %}