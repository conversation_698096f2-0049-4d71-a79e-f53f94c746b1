<!-- Language Selector Component -->
<div class="language-selector" id="languageSelector">
    <div class="dropdown">
        <button class="btn btn-outline-secondary dropdown-toggle" type="button" id="languageDropdown" data-bs-toggle="dropdown" aria-expanded="false">
            <i class="fas fa-globe"></i>
            <span id="currentLanguageName">{{ language_data.current_language_name }}</span>
        </button>
        <ul class="dropdown-menu" aria-labelledby="languageDropdown">
            {% for code, name in language_data.supported_languages.items() %}
            <li>
                <a class="dropdown-item language-option" href="javascript:void(0)" data-language="{{ code }}" 
                   {% if code == language_data.current_language %}style="font-weight: bold;"{% endif %}>
                    <span class="language-flag">
                        {% if code == 'en' %}🇺🇸{% elif code == 'ru' %}🇷🇺{% elif code == 'fr' %}🇫🇷{% elif code == 'tr' %}🇹🇷{% elif code == 'ar' %}🇸🇦{% elif code == 'zh' %}🇨🇳{% elif code == 'es' %}🇪🇸{% endif %}
                    </span>
                    {{ name }}
                    {% if code == language_data.current_language %}
                        <i class="fas fa-check text-success ms-2"></i>
                    {% endif %}
                </a>
            </li>
            {% endfor %}
        </ul>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Language switching functionality
    const languageOptions = document.querySelectorAll('.language-option');
    
    languageOptions.forEach(option => {
        option.addEventListener('click', function(e) {
            e.preventDefault();
            const languageCode = this.getAttribute('data-language');
            changeLanguage(languageCode);
        });
    });
    
    function changeLanguage(languageCode) {
        fetch('/api/language/change', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ language: languageCode })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update current language display
                document.getElementById('currentLanguageName').textContent = data.language_name;
                
                // Update document direction for RTL languages
                if (languageCode === 'ar') {
                    document.documentElement.setAttribute('dir', 'rtl');
                    document.documentElement.setAttribute('lang', 'ar');
                } else {
                    document.documentElement.setAttribute('dir', 'ltr');
                    document.documentElement.setAttribute('lang', languageCode);
                }
                
                // Show success message
                showNotification('Language changed successfully', 'success');
                
                // Immediately reload page to apply translations
                setTimeout(() => {
                    window.location.reload();
                }, 500);
            } else {
                showNotification('Error changing language: ' + data.error, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('Error changing language', 'error');
        });
    }
    
    function showNotification(message, type) {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        // Add to body
        document.body.appendChild(notification);
        
        // Remove after 3 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 3000);
    }
});
</script>

<style>
.language-selector {
    position: relative;
}

.language-selector .dropdown-menu {
    min-width: 200px;
}

.language-selector .dropdown-item {
    padding: 8px 16px;
    display: flex;
    align-items: center;
}

.language-selector .language-flag {
    font-size: 16px;
    margin-right: 8px;
    width: 20px;
    text-align: center;
}

.language-selector .dropdown-item:hover {
    background-color: #f8f9fa;
}

/* RTL support */
[dir="rtl"] .language-selector .dropdown-item {
    flex-direction: row-reverse;
}

[dir="rtl"] .language-selector .language-flag {
    margin-right: 0;
    margin-left: 8px;
}

[dir="rtl"] .language-selector .dropdown-toggle {
    flex-direction: row-reverse;
}

[dir="rtl"] .language-selector .dropdown-toggle i {
    margin-left: 8px;
    margin-right: 0;
}
</style>