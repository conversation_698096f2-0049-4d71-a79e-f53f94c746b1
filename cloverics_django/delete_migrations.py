#!/usr/bin/env python3
"""
Django Migration Cleanup Script

This script deletes all Django migration files (*.py) in the project while preserving
the __init__.py files in migration directories.

Usage:
    python delete_migrations.py [--dry-run] [--confirm]

Options:
    --dry-run    Show what would be deleted without actually deleting
    --confirm    Skip confirmation prompt and delete immediately
"""

import os
import sys
import argparse
from pathlib import Path
from typing import List, <PERSON>ple


def find_migration_files(project_root: str) -> List[Tuple[str, str]]:
    """
    Find all migration files in the Django project.
    
    Args:
        project_root: Root directory of the Django project
        
    Returns:
        List of tuples containing (file_path, relative_path)
    """
    migration_files = []
    project_path = Path(project_root)
    
    # Find all migration directories
    for migration_dir in project_path.rglob("migrations"):
        if migration_dir.is_dir():
            # Find all .py files in migration directories (excluding __init__.py)
            for py_file in migration_dir.glob("*.py"):
                if py_file.name != "__init__.py":
                    relative_path = py_file.relative_to(project_path)
                    migration_files.append((str(py_file), str(relative_path)))
    
    return migration_files


def delete_migration_files(migration_files: List[Tuple[str, str]], dry_run: bool = False) -> None:
    """
    Delete migration files.
    
    Args:
        migration_files: List of migration files to delete
        dry_run: If True, only show what would be deleted
    """
    if not migration_files:
        print("No migration files found to delete.")
        return
    
    print(f"{'DRY RUN: ' if dry_run else ''}Found {len(migration_files)} migration files:")
    print("-" * 60)
    
    for file_path, relative_path in migration_files:
        if dry_run:
            print(f"Would delete: {relative_path}")
        else:
            try:
                os.remove(file_path)
                print(f"Deleted: {relative_path}")
            except OSError as e:
                print(f"Error deleting {relative_path}: {e}")
    
    print("-" * 60)
    if dry_run:
        print(f"DRY RUN: Would delete {len(migration_files)} migration files")
    else:
        print(f"Successfully deleted {len(migration_files)} migration files")


def main():
    parser = argparse.ArgumentParser(
        description="Delete all Django migration files in the project",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
    python delete_migrations.py --dry-run          # Show what would be deleted
    python delete_migrations.py --confirm          # Delete without confirmation
    python delete_migrations.py                    # Delete with confirmation
        """
    )
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Show what would be deleted without actually deleting"
    )
    parser.add_argument(
        "--confirm",
        action="store_true",
        help="Skip confirmation prompt and delete immediately"
    )
    
    args = parser.parse_args()
    
    # Get the project root (assuming script is in project root)
    project_root = os.getcwd()
    
    # Check if we're in a Django project
    if not os.path.exists(os.path.join(project_root, "manage.py")):
        print("Error: manage.py not found. Please run this script from the Django project root.")
        sys.exit(1)
    
    # Find migration files
    migration_files = find_migration_files(project_root)
    
    if not migration_files:
        print("No migration files found in the project.")
        return
    
    # Show what will be deleted
    delete_migration_files(migration_files, dry_run=True)
    
    # Confirm deletion unless --confirm is used
    if not args.confirm and not args.dry_run:
        print("\n⚠️  WARNING: This will permanently delete all migration files!")
        print("Make sure you have a backup or are ready to recreate migrations.")
        response = input("\nAre you sure you want to continue? (yes/no): ").lower().strip()
        
        if response not in ['yes', 'y']:
            print("Operation cancelled.")
            return
    
    # Perform deletion
    if not args.dry_run:
        delete_migration_files(migration_files, dry_run=False)
        print("\n✅ Migration cleanup completed!")
        print("\nNext steps:")
        print("1. Run 'python manage.py makemigrations' to create fresh migrations")
        print("2. Run 'python manage.py migrate' to apply the new migrations")
        print("3. If you have existing data, you may need to handle data migration manually")


if __name__ == "__main__":
    main() 