from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from .models import (
    ProcessFlowStage, ShipmentProcessFlow, ProcessStageAction,
    ProcessFlowNotification, DocumentGeneration
)

@admin.register(ProcessFlowStage)
class ProcessFlowStageAdmin(admin.ModelAdmin):
    list_display = [
        'stage_name', 'stage_order', 'stage_description_short', 
        'is_mandatory', 'is_automated', 'estimated_duration_hours'
    ]
    list_filter = ['is_mandatory', 'is_automated', 'requires_approval']
    ordering = ['stage_order']
    search_fields = ['stage_name', 'stage_description']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('stage_name', 'stage_order', 'stage_description')
        }),
        ('Configuration', {
            'fields': ('is_mandatory', 'is_automated', 'requires_approval', 'estimated_duration_hours')
        }),
        ('Stage Flow', {
            'fields': ('available_actions', 'next_stages')
        }),
        ('UI Configuration', {
            'fields': ('icon_class', 'color_class')
        })
    )
    
    def stage_description_short(self, obj):
        return obj.stage_description[:50] + "..." if len(obj.stage_description) > 50 else obj.stage_description
    stage_description_short.short_description = 'Description'

@admin.register(ShipmentProcessFlow)
class ShipmentProcessFlowAdmin(admin.ModelAdmin):
    list_display = [
        'flow_id_short', 'shipment_id', 'customer_name', 'provider_name',
        'current_stage_display', 'process_status', 'completion_percentage', 'created_at'
    ]
    list_filter = ['process_status', 'origin_type', 'current_stage', 'created_at']
    search_fields = ['flow_id', 'shipment_id', 'customer__email', 'logistics_provider__company_name']
    ordering = ['-created_at']
    
    fieldsets = (
        ('Flow Information', {
            'fields': ('flow_id', 'shipment_id', 'origin_type', 'process_status')
        }),
        ('Participants', {
            'fields': ('customer', 'logistics_provider', 'assigned_driver', 'customs_agent', 'insurance_provider')
        }),
        ('Current Stage', {
            'fields': ('current_stage', 'current_stage_started_at', 'completion_percentage')
        }),
        ('Process Data', {
            'fields': ('stage_timestamps', 'stage_actions_log', 'stage_notes'),
            'classes': ['collapse']
        }),
        ('Configuration', {
            'fields': ('skip_optional_stages', 'auto_advance_stages', 'total_estimated_duration_hours')
        })
    )
    
    readonly_fields = ['flow_id', 'completion_percentage', 'actual_duration_hours']
    
    def flow_id_short(self, obj):
        return str(obj.flow_id)[:8] + "..."
    flow_id_short.short_description = 'Flow ID'
    
    def customer_name(self, obj):
        return obj.customer.company_name or obj.customer.get_full_name()
    customer_name.short_description = 'Customer'
    
    def provider_name(self, obj):
        return obj.logistics_provider.company_name or obj.logistics_provider.get_full_name()
    provider_name.short_description = 'Provider'
    
    def current_stage_display(self, obj):
        return format_html(
            '<span class="badge badge-primary">{}</span>',
            obj.current_stage.get_stage_name_display()
        )
    current_stage_display.short_description = 'Current Stage'

@admin.register(ProcessStageAction)
class ProcessStageActionAdmin(admin.ModelAdmin):
    list_display = [
        'action_name', 'stage', 'action_type', 'requires_input', 
        'requires_approval', 'allowed_user_types_display'
    ]
    list_filter = ['action_type', 'requires_input', 'requires_approval']
    search_fields = ['action_name', 'stage__stage_name']
    
    fieldsets = (
        ('Action Information', {
            'fields': ('action_name', 'action_type', 'action_description', 'stage')
        }),
        ('Input Configuration', {
            'fields': ('requires_input', 'input_type', 'input_options')
        }),
        ('Permissions', {
            'fields': ('allowed_user_types', 'requires_approval')
        }),
        ('Flow Control', {
            'fields': ('next_stage_on_success', 'next_stage_on_failure')
        }),
        ('UI Configuration', {
            'fields': ('button_class', 'icon_class')
        })
    )
    
    def allowed_user_types_display(self, obj):
        return ", ".join(obj.allowed_user_types) if obj.allowed_user_types else "None"
    allowed_user_types_display.short_description = 'Allowed Users'

@admin.register(ProcessFlowNotification)
class ProcessFlowNotificationAdmin(admin.ModelAdmin):
    list_display = [
        'title', 'flow_shipment_id', 'recipient_email', 'notification_type',
        'priority', 'is_read', 'created_at'
    ]
    list_filter = ['notification_type', 'priority', 'is_read', 'created_at']
    search_fields = ['title', 'message', 'recipient__email', 'flow__shipment_id']
    ordering = ['-created_at']
    
    fieldsets = (
        ('Notification Information', {
            'fields': ('title', 'message', 'notification_type', 'priority')
        }),
        ('Recipients & Context', {
            'fields': ('flow', 'recipient', 'stage_context', 'action_url')
        }),
        ('Status', {
            'fields': ('is_read', 'is_actioned', 'read_at', 'actioned_at')
        })
    )
    
    readonly_fields = ['notification_id', 'read_at', 'actioned_at']
    
    def flow_shipment_id(self, obj):
        return f"Shipment {obj.flow.shipment_id}"
    flow_shipment_id.short_description = 'Shipment'
    
    def recipient_email(self, obj):
        return obj.recipient.email
    recipient_email.short_description = 'Recipient'

@admin.register(DocumentGeneration)
class DocumentGenerationAdmin(admin.ModelAdmin):
    list_display = [
        'document_name', 'document_type', 'flow_shipment_id', 'generation_status',
        'generated_by_email', 'file_size_mb', 'downloaded_count', 'generated_at'
    ]
    list_filter = ['document_type', 'generation_status', 'is_public', 'generated_at']
    search_fields = ['document_name', 'flow__shipment_id', 'generated_by__email']
    ordering = ['-created_at']
    
    fieldsets = (
        ('Document Information', {
            'fields': ('document_name', 'document_type', 'file_path')
        }),
        ('Generation Details', {
            'fields': ('flow', 'generation_status', 'generated_by', 'template_used')
        }),
        ('Document Data', {
            'fields': ('document_data', 'generation_parameters'),
            'classes': ['collapse']
        }),
        ('Access Control', {
            'fields': ('is_public', 'allowed_user_types')
        }),
        ('Statistics', {
            'fields': ('file_size_bytes', 'downloaded_count', 'last_downloaded_at')
        })
    )
    
    readonly_fields = ['document_id', 'file_size_bytes', 'downloaded_count', 'generated_at', 'last_downloaded_at']
    
    def flow_shipment_id(self, obj):
        return f"Shipment {obj.flow.shipment_id}"
    flow_shipment_id.short_description = 'Shipment'
    
    def generated_by_email(self, obj):
        return obj.generated_by.email
    generated_by_email.short_description = 'Generated By'
    
    def file_size_mb(self, obj):
        return f"{obj.file_size_bytes / (1024*1024):.2f} MB" if obj.file_size_bytes else "0 MB"
    file_size_mb.short_description = 'File Size'

# Admin site customization
admin.site.site_header = "Cloverics Shipment Processing Admin"
admin.site.site_title = "Shipment Processing Admin"
admin.site.index_title = "Shipment Processing & Workflow Management"