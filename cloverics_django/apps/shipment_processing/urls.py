from django.urls import path
from . import views

app_name = 'shipment_processing'

urlpatterns = [
    # Process Flow Dashboard
    path('', views.unified_process_tracker, name='unified_tracker'),
    path('tracker/', views.unified_process_tracker, name='process_tracker'),
    
    # Shipment Process Flow
    path('shipment/<int:shipment_id>/', views.shipment_detail_flow, name='shipment_detail'),
    path('shipment/<int:shipment_id>/advance/', views.advance_shipment_stage, name='advance_stage'),
    path('shipment/<int:shipment_id>/action/', views.perform_stage_action, name='perform_action'),
    
    # API Endpoints
    path('api/shipment/<int:shipment_id>/progress/', views.get_shipment_progress_api, name='progress_api'),
    path('api/analytics/', views.get_process_analytics_api, name='analytics_api'),
    
    # Document Generation
    path('shipment/<int:shipment_id>/document/<str:document_type>/', views.generate_process_document, name='generate_document'),
    
    # Analytics Dashboard
    path('analytics/', views.process_analytics_dashboard, name='analytics_dashboard'),
]