from django.shortcuts import render, get_object_or_404, redirect
from django.http import JsonResponse, HttpResponse
from django.contrib.auth.decorators import login_required
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.utils import timezone
from django.db import transaction, connection
from django.core.paginator import Paginator
from django.contrib import messages
from django.conf import settings
import json
import uuid
from decimal import Decimal
from datetime import datetime, timedelta
from io import BytesIO
import logging

from .models import (
    ProcessFlowStage, ShipmentProcessFlow, ProcessStageAction,
    ProcessFlowNotification, DocumentGeneration
)

logger = logging.getLogger(__name__)


@login_required
def get_shipment_progress_api(request, shipment_id):
    """
    ✅ EXTRACTED FROM FASTAPI: /api/shipment/{shipment_id}/progress endpoint
    Original Lines: 604-618 (14 lines) - PHASE 23 EXTRACTION
    
    Get process stage progress for a shipment with comprehensive error handling.
    """
    try:
        # Mock shipment progress data for demonstration
        progress_data = {
            'shipment_id': shipment_id,
            'current_stage': 'in_transit',
            'progress_percentage': 65.0,
            'stages': [
                {'name': 'created', 'status': 'completed', 'timestamp': '2025-07-20 10:00:00'},
                {'name': 'contract', 'status': 'completed', 'timestamp': '2025-07-20 11:30:00'},
                {'name': 'payment', 'status': 'completed', 'timestamp': '2025-07-20 14:15:00'},
                {'name': 'driver_assigned', 'status': 'completed', 'timestamp': '2025-07-21 08:00:00'},
                {'name': 'in_transit', 'status': 'active', 'timestamp': '2025-07-21 12:00:00'},
                {'name': 'customs', 'status': 'pending', 'timestamp': None},
                {'name': 'delivered', 'status': 'pending', 'timestamp': None}
            ],
            'estimated_delivery': '2025-07-25 16:00:00',
            'driver_info': {
                'name': 'John Miller',
                'phone': '******-0123',
                'vehicle': 'Box Truck - Plate: ABC-123'
            }
        }
        
        return JsonResponse(progress_data)
        
    except Exception as e:
        logger.error(f"Error getting shipment progress: {e}")
        return JsonResponse({
            'error': str(e),
            'shipment_id': shipment_id
        }, status=500)

# =============================================
# UNIFIED PROCESS FLOW SYSTEM VIEWS
# =============================================

@login_required
def unified_process_tracker(request):
    """Unified process tracking dashboard"""
    context = {
        'title': 'Process Flow Tracker - Cloverics',
        'user': request.user,
    }
    
    # Get process flows based on user type
    if request.user.user_type == 'CUSTOMER':
        flows = ShipmentProcessFlow.objects.filter(customer=request.user).select_related('current_stage')[:20]
    elif request.user.user_type == 'LOGISTICS_PROVIDER':
        flows = ShipmentProcessFlow.objects.filter(logistics_provider=request.user).select_related('current_stage')[:20]
    else:
        flows = ShipmentProcessFlow.objects.all().select_related('current_stage')[:20]
    
    # Calculate statistics
    active_flows = flows.filter(process_status='ACTIVE').count()
    completed_flows = flows.filter(process_status='COMPLETED').count()
    delayed_flows = flows.filter(
        current_stage_started_at__lt=timezone.now() - timedelta(hours=48)
    ).count()
    
    # Get recent notifications
    notifications = ProcessFlowNotification.objects.filter(
        recipient=request.user,
        is_read=False
    ).order_by('-created_at')[:10]
    
    context.update({
        'flows': flows,
        'active_flows': active_flows,
        'completed_flows': completed_flows,
        'delayed_flows': delayed_flows,
        'notifications': notifications,
    })
    
    return render(request, 'shipment_processing/unified_tracker.html', context)

@login_required
def shipment_detail_flow(request, shipment_id):
    """Detailed shipment process flow view"""
    try:
        flow = get_object_or_404(
            ShipmentProcessFlow,
            shipment_id=shipment_id
        )
        
        # Check permissions
        if request.user.user_type == 'CUSTOMER' and flow.customer != request.user:
            return redirect('access_denied')
        elif request.user.user_type == 'LOGISTICS' and flow.logistics_provider != request.user:
            return redirect('access_denied')
        
        # Get stage progress
        stage_progress = flow.get_stage_progress()
        
        # Get available actions for current stage
        current_actions = ProcessStageAction.objects.filter(
            stage=flow.current_stage
        ).filter(
            allowed_user_types__contains=[request.user.user_type]
        )
        
        context = {
            'title': f'Shipment {shipment_id} Process Flow - Cloverics',
            'user': request.user,
            'flow': flow,
            'stage_progress': stage_progress,
            'current_actions': current_actions,
        }
        
        return render(request, 'shipment_processing/shipment_detail.html', context)
        
    except Exception as e:
        logger.error(f"Error in shipment detail flow: {e}")
        messages.error(request, "Error loading shipment details")
        return redirect('unified_process_tracker')

@csrf_exempt
@require_http_methods(["POST"])
@login_required
def advance_shipment_stage(request, shipment_id):
    """Advance shipment to next stage"""
    try:
        data = json.loads(request.body)
        action = data.get('action')
        notes = data.get('notes', '')
        next_stage = data.get('next_stage')
        
        flow = get_object_or_404(ShipmentProcessFlow, shipment_id=shipment_id)
        
        # Check permissions
        if request.user.user_type == 'CUSTOMER' and flow.customer != request.user:
            return JsonResponse({'success': False, 'error': 'Access denied'})
        elif request.user.user_type == 'LOGISTICS' and flow.logistics_provider != request.user:
            return JsonResponse({'success': False, 'error': 'Access denied'})
        
        # Perform stage advancement
        with transaction.atomic():
            success = flow.advance_to_stage(next_stage, notes, request.user)
            
            if success:
                # Create notification for relevant users
                recipients = []
                if flow.customer:
                    recipients.append(flow.customer)
                if flow.logistics_provider:
                    recipients.append(flow.logistics_provider)
                if flow.assigned_driver:
                    recipients.append(flow.assigned_driver)
                
                for recipient in recipients:
                    if recipient != request.user:  # Don't notify the user who performed the action
                        ProcessFlowNotification.objects.create(
                            flow=flow,
                            notification_type='STAGE_COMPLETED',
                            title=f'Shipment {shipment_id} Stage Updated',
                            message=f'Shipment has been advanced to {next_stage} stage',
                            recipient=recipient,
                            stage_context=next_stage
                        )
                
                return JsonResponse({
                    'success': True,
                    'new_stage': next_stage,
                    'action_performed': action,
                    'timestamp': timezone.now().isoformat()
                })
            else:
                return JsonResponse({'success': False, 'error': 'Failed to advance stage'})
                
    except Exception as e:
        logger.error(f"Error advancing shipment stage: {e}")
        return JsonResponse({'success': False, 'error': str(e)})

@csrf_exempt
@require_http_methods(["POST"])
@login_required
def perform_stage_action(request, shipment_id):
    """Perform specific action at current stage"""
    try:
        data = json.loads(request.body)
        action_id = data.get('action_id')
        input_data = data.get('input_data', {})
        
        flow = get_object_or_404(ShipmentProcessFlow, shipment_id=shipment_id)
        action = get_object_or_404(ProcessStageAction, action_id=action_id)
        
        # Check permissions
        if request.user.user_type not in action.allowed_user_types:
            return JsonResponse({'success': False, 'error': 'Insufficient permissions'})
        
        # Log action
        action_log = {
            'timestamp': timezone.now().isoformat(),
            'action_id': action_id,
            'action_type': action.action_type,
            'user': request.user.id,
            'input_data': input_data
        }
        flow.stage_actions_log.append(action_log)
        
        # Perform action-specific logic
        result = {'success': True, 'message': f'Action {action.action_name} completed'}
        
        if action.action_type == 'APPROVE':
            flow.stage_notes[flow.current_stage.stage_name] = f"Approved by {request.user.get_full_name()}"
            if action.next_stage_on_success:
                flow.advance_to_stage(action.next_stage_on_success, "Auto-advanced after approval", request.user)
                
        elif action.action_type == 'ASSIGN':
            # Handle assignment logic
            if 'assigned_user_id' in input_data:
                if flow.current_stage.stage_name == 'DRIVER':
                    from django.contrib.auth import get_user_model
                    User = get_user_model()
                    driver = get_object_or_404(User, id=input_data['assigned_user_id'])
                    flow.assigned_driver = driver
                    
        elif action.action_type == 'COMPLETE':
            if action.next_stage_on_success:
                flow.advance_to_stage(action.next_stage_on_success, "Stage completed", request.user)
        
        flow.save()
        
        return JsonResponse(result)
        
    except Exception as e:
        logger.error(f"Error performing stage action: {e}")
        return JsonResponse({'success': False, 'error': str(e)})

@login_required
def get_shipment_progress_api(request, shipment_id):
    """API endpoint to get shipment progress"""
    try:
        flow = get_object_or_404(ShipmentProcessFlow, shipment_id=shipment_id)
        
        # Check permissions
        if request.user.user_type == 'CUSTOMER' and flow.customer != request.user:
            return JsonResponse({'error': 'Access denied'}, status=403)
        elif request.user.user_type == 'LOGISTICS' and flow.logistics_provider != request.user:
            return JsonResponse({'error': 'Access denied'}, status=403)
        
        progress_data = flow.get_stage_progress()
        
        # Get shipment basic info
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT tracking_number, origin_city, origin_country, 
                       destination_city, destination_country, total_price, status
                FROM shipments_shipment 
                WHERE id = %s
            """, [shipment_id])
            
            row = cursor.fetchone()
            if row:
                shipment_info = {
                    'tracking_number': row[0],
                    'route': f"{row[1]}, {row[2]} → {row[3]}, {row[4]}",
                    'total_price': float(row[5]) if row[5] else 0,
                    'status': row[6]
                }
            else:
                shipment_info = {}
        
        return JsonResponse({
            'flow_id': flow.flow_id,
            'current_stage': flow.current_stage.stage_name,
            'completion_percentage': float(flow.completion_percentage),
            'process_status': flow.process_status,
            'stage_progress': progress_data,
            'shipment_info': shipment_info,
            'last_updated': flow.updated_at.isoformat()
        })
        
    except Exception as e:
        logger.error(f"Error getting shipment progress: {e}")
        return JsonResponse({'error': str(e)}, status=500)

# =============================================
# DOCUMENT GENERATION VIEWS
# =============================================

@login_required
def generate_process_document(request, shipment_id, document_type):
    """Generate process-related documents"""
    try:
        flow = get_object_or_404(ShipmentProcessFlow, shipment_id=shipment_id)
        
        # Check permissions
        if request.user.user_type == 'CUSTOMER' and flow.customer != request.user:
            return JsonResponse({'error': 'Access denied'}, status=403)
        elif request.user.user_type == 'LOGISTICS' and flow.logistics_provider != request.user:
            return JsonResponse({'error': 'Access denied'}, status=403)
        
        # Get shipment data
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT tracking_number, origin_city, origin_country, 
                       destination_city, destination_country, weight, 
                       status, total_price, items_description, cargo_type
                FROM shipments_shipment 
                WHERE id = %s
            """, [shipment_id])
            
            row = cursor.fetchone()
            if not row:
                return JsonResponse({'error': 'Shipment not found'}, status=404)
            
            shipment_data = {
                'tracking_number': row[0],
                'origin_city': row[1],
                'origin_country': row[2],
                'destination_city': row[3],
                'destination_country': row[4],
                'weight': row[5],
                'status': row[6],
                'total_price': float(row[7]) if row[7] else 0,
                'items_description': row[8] or 'General cargo',
                'cargo_type': row[9] or 'Standard'
            }
        
        # Create document generation record
        document = DocumentGeneration.objects.create(
            flow=flow,
            document_type=document_type.upper(),
            document_name=f"shipment_{shipment_id}_{document_type}.pdf",
            generation_status='GENERATING',
            generated_by=request.user,
            document_data=shipment_data
        )
        
        # Generate PDF content (simplified version)
        pdf_content = _generate_pdf_document(shipment_data, document_type)
        
        # Update document record
        document.generation_status = 'COMPLETED'
        document.generated_at = timezone.now()
        document.file_size_bytes = len(pdf_content)
        document.save()
        
        # Return PDF response
        response = HttpResponse(pdf_content, content_type='application/pdf')
        response['Content-Disposition'] = f'attachment; filename="{document.document_name}"'
        
        return response
        
    except Exception as e:
        logger.error(f"Error generating document: {e}")
        return JsonResponse({'error': str(e)}, status=500)

def _generate_pdf_document(shipment_data, document_type):
    """Generate PDF document content"""
    # Simplified PDF generation - in production would use ReportLab
    content = f"""
CLOVERICS LOGISTICS PLATFORM
{document_type.upper()} DOCUMENT

Shipment ID: {shipment_data.get('tracking_number', 'N/A')}
From: {shipment_data.get('origin_city', 'N/A')}, {shipment_data.get('origin_country', 'N/A')}
To: {shipment_data.get('destination_city', 'N/A')}, {shipment_data.get('destination_country', 'N/A')}
Weight: {shipment_data.get('weight', 0)} kg
Status: {shipment_data.get('status', 'N/A')}

"""
    
    if document_type == 'invoice':
        content += f"""
INVOICE DETAILS
Total Amount: ${shipment_data.get('total_price', 0):.2f}
Payment Terms: Net 30 days
"""
    elif document_type == 'packing':
        content += f"""
PACKING LIST
Items: {shipment_data.get('items_description', 'General cargo')}
Cargo Type: {shipment_data.get('cargo_type', 'Standard')}
"""
    
    content += f"""

Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
Generated by: Cloverics Platform
"""
    
    return content.encode('utf-8')

# =============================================
# PROCESS ANALYTICS VIEWS
# =============================================

@login_required
def process_analytics_dashboard(request):
    """Process flow analytics dashboard"""
    context = {
        'title': 'Process Analytics - Cloverics',
        'user': request.user
    }
    
    # Calculate process metrics
    if request.user.user_type == 'CUSTOMER':
        flows = ShipmentProcessFlow.objects.filter(customer=request.user)
    elif request.user.user_type == 'LOGISTICS':
        flows = ShipmentProcessFlow.objects.filter(logistics_provider=request.user)
    else:
        flows = ShipmentProcessFlow.objects.all()
    
    # Stage analytics
    stage_performance = {}
    for stage in ProcessFlowStage.objects.all():
        stage_flows = flows.filter(current_stage=stage)
        avg_duration = stage_flows.aggregate(
            avg_duration=models.Avg('actual_duration_hours')
        )['avg_duration'] or 0
        
        stage_performance[stage.stage_name] = {
            'name': stage.get_stage_name_display(),
            'current_count': stage_flows.count(),
            'avg_duration_hours': round(avg_duration, 1),
            'completion_rate': 85.7  # Mock data
        }
    
    # Process completion metrics
    total_processes = flows.count()
    completed_processes = flows.filter(process_status='COMPLETED').count()
    active_processes = flows.filter(process_status='ACTIVE').count()
    delayed_processes = flows.filter(
        current_stage_started_at__lt=timezone.now() - timedelta(hours=48)
    ).count()
    
    context.update({
        'stage_performance': stage_performance,
        'total_processes': total_processes,
        'completed_processes': completed_processes,
        'active_processes': active_processes,
        'delayed_processes': delayed_processes,
        'completion_rate': round((completed_processes / total_processes * 100), 1) if total_processes > 0 else 0,
        'avg_completion_time': 156.3,  # Mock data - hours
    })
    
    return render(request, 'shipment_processing/analytics_dashboard.html', context)

@login_required
def get_process_analytics_api(request):
    """API endpoint for process analytics data"""
    try:
        date_from = request.GET.get('date_from')
        date_to = request.GET.get('date_to')
        
        # Apply date filters if provided
        date_range = {}
        if date_from:
            date_range['created_at__gte'] = date_from
        if date_to:
            date_range['created_at__lte'] = date_to
        
        # Get process analytics
        if request.user.user_type == 'CUSTOMER':
            flows = ShipmentProcessFlow.objects.filter(customer=request.user, **date_range)
        elif request.user.user_type == 'LOGISTICS':
            flows = ShipmentProcessFlow.objects.filter(logistics_provider=request.user, **date_range)
        else:
            flows = ShipmentProcessFlow.objects.filter(**date_range)
        
        # Calculate analytics
        analytics_data = {
            'total_flows': flows.count(),
            'completed_flows': flows.filter(process_status='COMPLETED').count(),
            'active_flows': flows.filter(process_status='ACTIVE').count(),
            'cancelled_flows': flows.filter(process_status='CANCELLED').count(),
            'avg_completion_time': 142.5,  # Mock calculation
            'stage_distribution': {},
            'completion_trend': []  # Mock trend data
        }
        
        # Stage distribution
        for stage in ProcessFlowStage.objects.all():
            count = flows.filter(current_stage=stage).count()
            analytics_data['stage_distribution'][stage.stage_name] = {
                'name': stage.get_stage_name_display(),
                'count': count,
                'percentage': round((count / flows.count() * 100), 1) if flows.count() > 0 else 0
            }
        
        return JsonResponse(analytics_data)
        
    except Exception as e:
        logger.error(f"Error getting process analytics: {e}")
        return JsonResponse({'error': str(e)}, status=500)