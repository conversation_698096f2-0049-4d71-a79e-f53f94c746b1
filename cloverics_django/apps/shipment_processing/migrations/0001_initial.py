# Generated by Django 5.2.1 on 2025-08-07 12:45

import django.db.models.deletion
import django.utils.timezone
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ProcessFlowStage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('stage_id', models.CharField(default=uuid.uuid4, max_length=50, unique=True)),
                ('stage_name', models.CharField(choices=[('CREATED', 'Shipment Created'), ('CONTRACT', 'Contract Stage'), ('PAYMENT', 'Payment Processing'), ('CONTAINER', 'Container Assignment'), ('DRIVER', 'Driver Assignment'), ('CUSTOMS', 'Customs Clearance'), ('INSURANCE', 'Insurance Processing'), ('TRIP', 'In Transit'), ('DELIVERY', 'Delivery'), ('FEEDBACK', 'Feedback Collection'), ('COMPLETED', 'Process Completed')], max_length=20, unique=True)),
                ('stage_order', models.IntegerField(unique=True)),
                ('stage_description', models.TextField()),
                ('is_mandatory', models.BooleanField(default=True)),
                ('is_automated', models.BooleanField(default=False)),
                ('requires_approval', models.BooleanField(default=False)),
                ('estimated_duration_hours', models.IntegerField(default=24)),
                ('available_actions', models.JSONField(default=list)),
                ('next_stages', models.JSONField(default=list)),
                ('icon_class', models.CharField(default='fas fa-circle', max_length=50)),
                ('color_class', models.CharField(default='text-primary', max_length=50)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'shipment_processing_flow_stage',
                'ordering': ['stage_order'],
                'indexes': [models.Index(fields=['stage_name'], name='sp_stage_name_idx'), models.Index(fields=['stage_order'], name='sp_stage_order_idx')],
            },
        ),
        migrations.CreateModel(
            name='ShipmentProcessFlow',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('flow_id', models.CharField(default=uuid.uuid4, max_length=50, unique=True)),
                ('shipment_id', models.IntegerField()),
                ('origin_type', models.CharField(choices=[('DIRECT', 'Direct Booking'), ('QUOTE', 'Quote Acceptance'), ('PRIVATE', 'Private Shipment')], default='DIRECT', max_length=20)),
                ('process_status', models.CharField(choices=[('ACTIVE', 'Active'), ('PAUSED', 'Paused'), ('COMPLETED', 'Completed'), ('CANCELLED', 'Cancelled'), ('ERROR', 'Error State')], default='ACTIVE', max_length=20)),
                ('current_stage_started_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('stage_timestamps', models.JSONField(default=dict)),
                ('stage_actions_log', models.JSONField(default=list)),
                ('stage_notes', models.JSONField(default=dict)),
                ('total_estimated_duration_hours', models.IntegerField(default=168)),
                ('actual_duration_hours', models.IntegerField(blank=True, null=True)),
                ('completion_percentage', models.DecimalField(decimal_places=2, default=0, max_digits=5)),
                ('skip_optional_stages', models.BooleanField(default=False)),
                ('auto_advance_stages', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('assigned_driver', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='driver_flows', to=settings.AUTH_USER_MODEL)),
                ('current_stage', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='current_flows', to='shipment_processing.processflowstage')),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='customer_flows', to=settings.AUTH_USER_MODEL)),
                ('customs_agent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='customs_flows', to=settings.AUTH_USER_MODEL)),
                ('insurance_provider', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='insurance_flows', to=settings.AUTH_USER_MODEL)),
                ('logistics_provider', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='provider_flows', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'shipment_processing_flow',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ProcessFlowNotification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('notification_id', models.CharField(default=uuid.uuid4, max_length=50, unique=True)),
                ('notification_type', models.CharField(choices=[('STAGE_STARTED', 'Stage Started'), ('STAGE_COMPLETED', 'Stage Completed'), ('ACTION_REQUIRED', 'Action Required'), ('PROCESS_COMPLETED', 'Process Completed'), ('PROCESS_DELAYED', 'Process Delayed'), ('ERROR_OCCURRED', 'Error Occurred')], max_length=20)),
                ('title', models.CharField(max_length=200)),
                ('message', models.TextField()),
                ('is_read', models.BooleanField(default=False)),
                ('is_actioned', models.BooleanField(default=False)),
                ('stage_context', models.CharField(blank=True, max_length=20)),
                ('action_url', models.CharField(blank=True, max_length=200)),
                ('priority', models.CharField(choices=[('LOW', 'Low'), ('MEDIUM', 'Medium'), ('HIGH', 'High')], default='MEDIUM', max_length=10)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('read_at', models.DateTimeField(blank=True, null=True)),
                ('actioned_at', models.DateTimeField(blank=True, null=True)),
                ('recipient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='process_notifications', to=settings.AUTH_USER_MODEL)),
                ('flow', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='notifications', to='shipment_processing.shipmentprocessflow')),
            ],
            options={
                'db_table': 'shipment_processing_notification',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='DocumentGeneration',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('document_id', models.CharField(default=uuid.uuid4, max_length=50, unique=True)),
                ('document_type', models.CharField(choices=[('INVOICE', 'Commercial Invoice'), ('PACKING_LIST', 'Packing List'), ('BILL_OF_LADING', 'Bill of Lading'), ('CUSTOMS_DECLARATION', 'Customs Declaration'), ('INSURANCE_CERTIFICATE', 'Insurance Certificate'), ('DELIVERY_RECEIPT', 'Delivery Receipt'), ('CONTRACT', 'Service Contract')], max_length=30)),
                ('document_name', models.CharField(max_length=200)),
                ('file_path', models.CharField(blank=True, max_length=500)),
                ('generation_status', models.CharField(choices=[('PENDING', 'Pending Generation'), ('GENERATING', 'Generating'), ('COMPLETED', 'Generated'), ('FAILED', 'Generation Failed')], default='PENDING', max_length=20)),
                ('template_used', models.CharField(blank=True, max_length=100)),
                ('file_size_bytes', models.BigIntegerField(default=0)),
                ('document_data', models.JSONField(default=dict)),
                ('generation_parameters', models.JSONField(default=dict)),
                ('is_public', models.BooleanField(default=False)),
                ('allowed_user_types', models.JSONField(default=list)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('generated_at', models.DateTimeField(blank=True, null=True)),
                ('downloaded_count', models.IntegerField(default=0)),
                ('last_downloaded_at', models.DateTimeField(blank=True, null=True)),
                ('generated_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='processing_generated_documents', to=settings.AUTH_USER_MODEL)),
                ('flow', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='generated_documents', to='shipment_processing.shipmentprocessflow')),
            ],
            options={
                'db_table': 'shipment_processing_document',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ProcessStageAction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('action_id', models.CharField(default=uuid.uuid4, max_length=50, unique=True)),
                ('action_type', models.CharField(choices=[('APPROVE', 'Approve'), ('REJECT', 'Reject'), ('ASSIGN', 'Assign'), ('UPLOAD', 'Upload Document'), ('VERIFY', 'Verify'), ('COMPLETE', 'Mark Complete'), ('PAUSE', 'Pause Process'), ('RESUME', 'Resume Process'), ('ESCALATE', 'Escalate Issue')], max_length=20)),
                ('action_name', models.CharField(max_length=100)),
                ('action_description', models.TextField()),
                ('requires_input', models.BooleanField(default=False)),
                ('input_type', models.CharField(blank=True, max_length=50)),
                ('input_options', models.JSONField(default=list)),
                ('allowed_user_types', models.JSONField(default=list)),
                ('requires_approval', models.BooleanField(default=False)),
                ('next_stage_on_success', models.CharField(blank=True, max_length=20)),
                ('next_stage_on_failure', models.CharField(blank=True, max_length=20)),
                ('button_class', models.CharField(default='btn-primary', max_length=50)),
                ('icon_class', models.CharField(default='fas fa-check', max_length=50)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('stage', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='stage_actions', to='shipment_processing.processflowstage')),
            ],
            options={
                'db_table': 'shipment_processing_stage_action',
                'indexes': [models.Index(fields=['stage'], name='sp_action_stage_idx'), models.Index(fields=['action_type'], name='sp_action_type_idx')],
                'unique_together': {('stage', 'action_type')},
            },
        ),
        migrations.AddIndex(
            model_name='shipmentprocessflow',
            index=models.Index(fields=['shipment_id'], name='sp_shipment_id_idx'),
        ),
        migrations.AddIndex(
            model_name='shipmentprocessflow',
            index=models.Index(fields=['customer'], name='sp_customer_idx'),
        ),
        migrations.AddIndex(
            model_name='shipmentprocessflow',
            index=models.Index(fields=['logistics_provider'], name='sp_provider_idx'),
        ),
        migrations.AddIndex(
            model_name='shipmentprocessflow',
            index=models.Index(fields=['current_stage'], name='sp_current_stage_idx'),
        ),
        migrations.AddIndex(
            model_name='shipmentprocessflow',
            index=models.Index(fields=['process_status'], name='sp_process_status_idx'),
        ),
        migrations.AddIndex(
            model_name='shipmentprocessflow',
            index=models.Index(fields=['origin_type'], name='sp_origin_type_idx'),
        ),
        migrations.AddIndex(
            model_name='processflownotification',
            index=models.Index(fields=['flow'], name='sp_notif_flow_idx'),
        ),
        migrations.AddIndex(
            model_name='processflownotification',
            index=models.Index(fields=['recipient'], name='sp_notif_recipient_idx'),
        ),
        migrations.AddIndex(
            model_name='processflownotification',
            index=models.Index(fields=['is_read'], name='sp_notif_read_idx'),
        ),
        migrations.AddIndex(
            model_name='processflownotification',
            index=models.Index(fields=['notification_type'], name='sp_notif_type_idx'),
        ),
        migrations.AddIndex(
            model_name='documentgeneration',
            index=models.Index(fields=['flow'], name='sp_doc_flow_idx'),
        ),
        migrations.AddIndex(
            model_name='documentgeneration',
            index=models.Index(fields=['document_type'], name='sp_doc_type_idx'),
        ),
        migrations.AddIndex(
            model_name='documentgeneration',
            index=models.Index(fields=['generation_status'], name='sp_doc_status_idx'),
        ),
        migrations.AddIndex(
            model_name='documentgeneration',
            index=models.Index(fields=['generated_by'], name='sp_doc_generated_by_idx'),
        ),
    ]
