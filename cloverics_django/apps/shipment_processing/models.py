from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.contrib.postgres.fields import <PERSON><PERSON><PERSON><PERSON>
import uuid
from decimal import Decimal

User = get_user_model()

class ProcessFlowStage(models.Model):
    """Process flow stage definitions"""
    
    STAGE_TYPES = [
        ('CREATED', 'Shipment Created'),
        ('CONTRACT', 'Contract Stage'),
        ('PAYMENT', 'Payment Processing'),
        ('CONTAINER', 'Container Assignment'),
        ('DRIVER', 'Driver Assignment'),
        ('CUSTOMS', 'Customs Clearance'),
        ('INSURANCE', 'Insurance Processing'),
        ('TRIP', 'In Transit'),
        ('DELIVERY', 'Delivery'),
        ('FEEDBACK', 'Feedback Collection'),
        ('COMPLETED', 'Process Completed'),
    ]
    
    stage_id = models.CharField(max_length=50, unique=True, default=uuid.uuid4)
    stage_name = models.CharField(max_length=20, choices=STAGE_TYPES, unique=True)
    stage_order = models.IntegerField(unique=True)
    stage_description = models.TextField()
    
    # Stage configuration
    is_mandatory = models.BooleanField(default=True)
    is_automated = models.BooleanField(default=False)
    requires_approval = models.BooleanField(default=False)
    estimated_duration_hours = models.IntegerField(default=24)
    
    # Stage actions
    available_actions = models.JSONField(default=list)  # List of available actions for this stage
    next_stages = models.JSONField(default=list)  # Possible next stages
    
    # Stage metadata
    icon_class = models.CharField(max_length=50, default='fas fa-circle')
    color_class = models.CharField(max_length=50, default='text-primary')
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'shipment_processing_flow_stage'
        ordering = ['stage_order']
        indexes = [
            models.Index(fields=['stage_name'], name='sp_stage_name_idx'),
            models.Index(fields=['stage_order'], name='sp_stage_order_idx'),
        ]
    
    def __str__(self):
        return f"{self.get_stage_name_display()} (Order: {self.stage_order})"


class ShipmentProcessFlow(models.Model):
    """Individual shipment process flow tracking"""
    
    ORIGIN_TYPES = [
        ('DIRECT', 'Direct Booking'),
        ('QUOTE', 'Quote Acceptance'),
        ('PRIVATE', 'Private Shipment'),
    ]
    
    PROCESS_STATUS = [
        ('ACTIVE', 'Active'),
        ('PAUSED', 'Paused'),
        ('COMPLETED', 'Completed'),
        ('CANCELLED', 'Cancelled'),
        ('ERROR', 'Error State'),
    ]
    
    flow_id = models.CharField(max_length=50, unique=True, default=uuid.uuid4)
    shipment_id = models.IntegerField()  # Reference to shipment
    
    # Process metadata
    origin_type = models.CharField(max_length=20, choices=ORIGIN_TYPES, default='DIRECT')
    process_status = models.CharField(max_length=20, choices=PROCESS_STATUS, default='ACTIVE')
    
    # Current stage information
    current_stage = models.ForeignKey(ProcessFlowStage, on_delete=models.CASCADE, related_name='current_flows')
    current_stage_started_at = models.DateTimeField(default=timezone.now)
    
    # Stage progress tracking
    stage_timestamps = models.JSONField(default=dict)  # {stage_name: timestamp}
    stage_actions_log = models.JSONField(default=list)  # List of actions taken
    stage_notes = models.JSONField(default=dict)  # {stage_name: notes}
    
    # Process participants
    customer = models.ForeignKey(User, on_delete=models.CASCADE, related_name='customer_flows')
    logistics_provider = models.ForeignKey(User, on_delete=models.CASCADE, related_name='provider_flows')
    assigned_driver = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='driver_flows')
    customs_agent = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='customs_flows')
    insurance_provider = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='insurance_flows')
    
    # Process metrics
    total_estimated_duration_hours = models.IntegerField(default=168)  # 7 days
    actual_duration_hours = models.IntegerField(null=True, blank=True)
    completion_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    
    # Process configuration
    skip_optional_stages = models.BooleanField(default=False)
    auto_advance_stages = models.BooleanField(default=True)
    
    # Audit trail
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        db_table = 'shipment_processing_flow'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['shipment_id'], name='sp_shipment_id_idx'),
            models.Index(fields=['customer'], name='sp_customer_idx'),
            models.Index(fields=['logistics_provider'], name='sp_provider_idx'),
            models.Index(fields=['current_stage'], name='sp_current_stage_idx'),
            models.Index(fields=['process_status'], name='sp_process_status_idx'),
            models.Index(fields=['origin_type'], name='sp_origin_type_idx'),
        ]
    
    def __str__(self):
        return f"Flow {self.flow_id} - Shipment {self.shipment_id}"
    
    def advance_to_stage(self, stage_name, notes="", user=None):
        """Advance process to next stage"""
        try:
            next_stage = ProcessFlowStage.objects.get(stage_name=stage_name)
            
            # Record current stage completion
            self.stage_timestamps[self.current_stage.stage_name] = timezone.now().isoformat()
            
            # Log action
            action_log = {
                'timestamp': timezone.now().isoformat(),
                'from_stage': self.current_stage.stage_name,
                'to_stage': stage_name,
                'user': user.id if user else None,
                'notes': notes
            }
            self.stage_actions_log.append(action_log)
            
            # Update current stage
            self.current_stage = next_stage
            self.current_stage_started_at = timezone.now()
            
            # Update completion percentage
            total_stages = ProcessFlowStage.objects.count()
            completed_stages = len(self.stage_timestamps)
            self.completion_percentage = (completed_stages / total_stages) * 100
            
            self.save()
            return True
            
        except ProcessFlowStage.DoesNotExist:
            return False
    
    def get_stage_progress(self):
        """Get detailed stage progress information"""
        stages = ProcessFlowStage.objects.all().order_by('stage_order')
        progress = []
        
        for stage in stages:
            is_completed = stage.stage_name in self.stage_timestamps
            is_current = stage.id == self.current_stage.id
            
            progress.append({
                'stage_name': stage.stage_name,
                'stage_display': stage.get_stage_name_display(),
                'stage_order': stage.stage_order,
                'is_completed': is_completed,
                'is_current': is_current,
                'icon_class': stage.icon_class,
                'color_class': stage.color_class,
                'completed_at': self.stage_timestamps.get(stage.stage_name),
                'notes': self.stage_notes.get(stage.stage_name, ''),
                'available_actions': stage.available_actions if is_current else []
            })
        
        return progress


class ProcessStageAction(models.Model):
    """Actions that can be performed at each stage"""
    
    ACTION_TYPES = [
        ('APPROVE', 'Approve'),
        ('REJECT', 'Reject'),
        ('ASSIGN', 'Assign'),
        ('UPLOAD', 'Upload Document'),
        ('VERIFY', 'Verify'),
        ('COMPLETE', 'Mark Complete'),
        ('PAUSE', 'Pause Process'),
        ('RESUME', 'Resume Process'),
        ('ESCALATE', 'Escalate Issue'),
    ]
    
    action_id = models.CharField(max_length=50, unique=True, default=uuid.uuid4)
    stage = models.ForeignKey(ProcessFlowStage, on_delete=models.CASCADE, related_name='stage_actions')
    action_type = models.CharField(max_length=20, choices=ACTION_TYPES)
    action_name = models.CharField(max_length=100)
    action_description = models.TextField()
    
    # Action configuration
    requires_input = models.BooleanField(default=False)
    input_type = models.CharField(max_length=50, blank=True)  # text, file, select, etc.
    input_options = models.JSONField(default=list)  # Options for select inputs
    
    # Permissions
    allowed_user_types = models.JSONField(default=list)  # List of user types who can perform this action
    requires_approval = models.BooleanField(default=False)
    
    # Action outcome
    next_stage_on_success = models.CharField(max_length=20, blank=True)
    next_stage_on_failure = models.CharField(max_length=20, blank=True)
    
    # UI configuration
    button_class = models.CharField(max_length=50, default='btn-primary')
    icon_class = models.CharField(max_length=50, default='fas fa-check')
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'shipment_processing_stage_action'
        unique_together = ['stage', 'action_type']
        indexes = [
            models.Index(fields=['stage'], name='sp_action_stage_idx'),
            models.Index(fields=['action_type'], name='sp_action_type_idx'),
        ]
    
    def __str__(self):
        return f"{self.stage.get_stage_name_display()} - {self.action_name}"


class ProcessFlowNotification(models.Model):
    """Notifications for process flow events"""
    
    NOTIFICATION_TYPES = [
        ('STAGE_STARTED', 'Stage Started'),
        ('STAGE_COMPLETED', 'Stage Completed'),
        ('ACTION_REQUIRED', 'Action Required'),
        ('PROCESS_COMPLETED', 'Process Completed'),
        ('PROCESS_DELAYED', 'Process Delayed'),
        ('ERROR_OCCURRED', 'Error Occurred'),
    ]
    
    notification_id = models.CharField(max_length=50, unique=True, default=uuid.uuid4)
    flow = models.ForeignKey(ShipmentProcessFlow, on_delete=models.CASCADE, related_name='notifications')
    
    notification_type = models.CharField(max_length=20, choices=NOTIFICATION_TYPES)
    title = models.CharField(max_length=200)
    message = models.TextField()
    
    # Recipients
    recipient = models.ForeignKey(User, on_delete=models.CASCADE, related_name='process_notifications')
    
    # Notification status
    is_read = models.BooleanField(default=False)
    is_actioned = models.BooleanField(default=False)
    
    # Notification metadata
    stage_context = models.CharField(max_length=20, blank=True)
    action_url = models.CharField(max_length=200, blank=True)
    priority = models.CharField(max_length=10, choices=[('LOW', 'Low'), ('MEDIUM', 'Medium'), ('HIGH', 'High')], default='MEDIUM')
    
    created_at = models.DateTimeField(auto_now_add=True)
    read_at = models.DateTimeField(null=True, blank=True)
    actioned_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        db_table = 'shipment_processing_notification'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['flow'], name='sp_notif_flow_idx'),
            models.Index(fields=['recipient'], name='sp_notif_recipient_idx'),
            models.Index(fields=['is_read'], name='sp_notif_read_idx'),
            models.Index(fields=['notification_type'], name='sp_notif_type_idx'),
        ]
    
    def __str__(self):
        return f"{self.title} - {self.recipient.email}"
    
    def mark_as_read(self):
        """Mark notification as read"""
        if not self.is_read:
            self.is_read = True
            self.read_at = timezone.now()
            self.save()


class DocumentGeneration(models.Model):
    """Generated documents for shipment processes"""
    
    DOCUMENT_TYPES = [
        ('INVOICE', 'Commercial Invoice'),
        ('PACKING_LIST', 'Packing List'),
        ('BILL_OF_LADING', 'Bill of Lading'),
        ('CUSTOMS_DECLARATION', 'Customs Declaration'),
        ('INSURANCE_CERTIFICATE', 'Insurance Certificate'),
        ('DELIVERY_RECEIPT', 'Delivery Receipt'),
        ('CONTRACT', 'Service Contract'),
    ]
    
    GENERATION_STATUS = [
        ('PENDING', 'Pending Generation'),
        ('GENERATING', 'Generating'),
        ('COMPLETED', 'Generated'),
        ('FAILED', 'Generation Failed'),
    ]
    
    document_id = models.CharField(max_length=50, unique=True, default=uuid.uuid4)
    flow = models.ForeignKey(ShipmentProcessFlow, on_delete=models.CASCADE, related_name='generated_documents')
    
    document_type = models.CharField(max_length=30, choices=DOCUMENT_TYPES)
    document_name = models.CharField(max_length=200)
    file_path = models.CharField(max_length=500, blank=True)
    
    # Generation details
    generation_status = models.CharField(max_length=20, choices=GENERATION_STATUS, default='PENDING')
    generated_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='processing_generated_documents')
    template_used = models.CharField(max_length=100, blank=True)
    
    # Document metadata
    file_size_bytes = models.BigIntegerField(default=0)
    document_data = models.JSONField(default=dict)  # Data used to generate document
    generation_parameters = models.JSONField(default=dict)
    
    # Access control
    is_public = models.BooleanField(default=False)
    allowed_user_types = models.JSONField(default=list)
    
    # Audit trail
    created_at = models.DateTimeField(auto_now_add=True)
    generated_at = models.DateTimeField(null=True, blank=True)
    downloaded_count = models.IntegerField(default=0)
    last_downloaded_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        db_table = 'shipment_processing_document'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['flow'], name='sp_doc_flow_idx'),
            models.Index(fields=['document_type'], name='sp_doc_type_idx'),
            models.Index(fields=['generation_status'], name='sp_doc_status_idx'),
            models.Index(fields=['generated_by'], name='sp_doc_generated_by_idx'),
        ]
    
    def __str__(self):
        return f"{self.document_name} - {self.get_document_type_display()}"