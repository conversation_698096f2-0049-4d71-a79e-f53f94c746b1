"""
Invoice Reconciliation Admin Configuration
"""

from django.contrib import admin
from .models import InvoiceRecord, ReconciliationMatch, ReconciliationDiscrepancy

@admin.register(InvoiceRecord)
class InvoiceRecordAdmin(admin.ModelAdmin):
    list_display = ['invoice_number', 'carrier_name', 'amount', 'currency', 'reconciliation_status', 'confidence_score', 'created_at']
    list_filter = ['reconciliation_status', 'carrier_name', 'currency', 'created_at']
    search_fields = ['invoice_number', 'carrier_name', 'tracking_number', 'user__email']
    readonly_fields = ['invoice_id', 'created_at', 'updated_at']
    ordering = ['-created_at']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('invoice_id', 'user', 'invoice_number', 'amount', 'currency', 'carrier_name')
        }),
        ('Dates', {
            'fields': ('service_date', 'due_date', 'invoice_date')
        }),
        ('Shipment Details', {
            'fields': ('tracking_number', 'shipment_reference', 'service_type', 'origin', 'destination', 'weight', 'dimensions')
        }),
        ('Reconciliation', {
            'fields': ('reconciliation_status', 'confidence_score', 'matched_shipment_id', 'match_date', 'reconciled_by', 'reconciled_at')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at')
        }),
    )

@admin.register(ReconciliationMatch)
class ReconciliationMatchAdmin(admin.ModelAdmin):
    list_display = ['invoice', 'shipment_id', 'confidence_score', 'is_selected', 'match_algorithm', 'created_at']
    list_filter = ['is_selected', 'match_algorithm', 'amount_match', 'carrier_match', 'created_at']
    search_fields = ['invoice__invoice_number', 'shipment_id']
    readonly_fields = ['match_id', 'created_at']
    ordering = ['-confidence_score', '-created_at']

@admin.register(ReconciliationDiscrepancy)
class ReconciliationDiscrepancyAdmin(admin.ModelAdmin):
    list_display = ['invoice', 'discrepancy_type', 'severity', 'financial_impact', 'is_resolved', 'created_at']
    list_filter = ['discrepancy_type', 'severity', 'is_resolved', 'created_at']
    search_fields = ['invoice__invoice_number', 'description']
    readonly_fields = ['discrepancy_id', 'created_at', 'updated_at']
    ordering = ['-severity', '-created_at']