"""
Invoice Reconciliation Models
Extracted from FastAPI - Lines 9207-9400+ (Invoice reconciliation system)
"""

from django.db import models
from django.contrib.auth import get_user_model
import uuid
from decimal import Decimal

User = get_user_model()

class InvoiceRecord(models.Model):
    """
    Invoice records for reconciliation
    Extracted from FastAPI invoice reconciliation functionality
    """
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='invoice_records')
    invoice_id = models.UUIDField(default=uuid.uuid4, unique=True)
    
    # Basic invoice details
    invoice_number = models.CharField(max_length=100, unique=True)
    amount = models.DecimalField(max_digits=12, decimal_places=2)
    currency = models.CharField(max_length=3, default='USD')
    carrier_name = models.CharField(max_length=200)
    
    # Dates
    service_date = models.DateField()
    due_date = models.DateField(blank=True, null=True)
    invoice_date = models.DateField(blank=True, null=True)
    
    # Shipment details
    tracking_number = models.CharField(max_length=100, blank=True, null=True)
    shipment_reference = models.CharField(max_length=100, blank=True, null=True)
    service_type = models.CharField(max_length=100, blank=True, null=True)
    origin = models.CharField(max_length=200, blank=True, null=True)
    destination = models.CharField(max_length=200, blank=True, null=True)
    weight = models.DecimalField(max_digits=10, decimal_places=3, blank=True, null=True)
    dimensions = models.CharField(max_length=100, blank=True, null=True)
    
    # Reconciliation status
    reconciliation_status = models.CharField(
        max_length=20,
        choices=[
            ('pending', 'Pending'),
            ('matched', 'Matched'),
            ('disputed', 'Disputed'),
            ('approved', 'Approved'),
            ('rejected', 'Rejected')
        ],
        default='pending'
    )
    
    # Matching details
    confidence_score = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    matched_shipment_id = models.IntegerField(blank=True, null=True)
    match_date = models.DateTimeField(blank=True, null=True)
    
    # Audit trail
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    reconciled_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='reconciled_invoices')
    reconciled_at = models.DateTimeField(blank=True, null=True)

    class Meta:
        db_table = 'invoice_records'
        indexes = [
            models.Index(fields=['user', 'reconciliation_status']),
            models.Index(fields=['invoice_number']),
            models.Index(fields=['tracking_number']),
            models.Index(fields=['carrier_name']),
            models.Index(fields=['service_date']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f"Invoice {self.invoice_number} - {self.carrier_name} (${self.amount})"

class ReconciliationMatch(models.Model):
    """
    Potential matches between invoices and shipments
    Extracted from FastAPI reconciliation matching system
    """
    invoice = models.ForeignKey(InvoiceRecord, on_delete=models.CASCADE, related_name='potential_matches')
    match_id = models.UUIDField(default=uuid.uuid4, unique=True)
    
    # Match details
    shipment_id = models.IntegerField()
    confidence_score = models.DecimalField(max_digits=5, decimal_places=2)
    
    # Matching factors
    amount_match = models.BooleanField(default=False)
    date_match = models.BooleanField(default=False)
    carrier_match = models.BooleanField(default=False)
    tracking_match = models.BooleanField(default=False)
    route_match = models.BooleanField(default=False)
    
    # Match quality indicators
    amount_difference = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    date_difference_days = models.IntegerField(default=0)
    
    # Matching algorithm details
    match_algorithm = models.CharField(max_length=100, default='ml_based')
    match_factors = models.JSONField(default=dict)
    
    is_selected = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'reconciliation_matches'
        indexes = [
            models.Index(fields=['invoice', 'confidence_score']),
            models.Index(fields=['shipment_id']),
            models.Index(fields=['is_selected']),
            models.Index(fields=['created_at']),
        ]
        unique_together = ['invoice', 'shipment_id']

    def __str__(self):
        return f"Match for {self.invoice.invoice_number} - Shipment {self.shipment_id} ({self.confidence_score}%)"

class ReconciliationDiscrepancy(models.Model):
    """
    Identified discrepancies during reconciliation
    Extracted from FastAPI discrepancy detection system
    """
    invoice = models.ForeignKey(InvoiceRecord, on_delete=models.CASCADE, related_name='discrepancies')
    discrepancy_id = models.UUIDField(default=uuid.uuid4, unique=True)
    
    discrepancy_type = models.CharField(
        max_length=50,
        choices=[
            ('amount_mismatch', 'Amount Mismatch'),
            ('service_mismatch', 'Service Type Mismatch'),
            ('date_discrepancy', 'Date Discrepancy'),
            ('carrier_mismatch', 'Carrier Mismatch'),
            ('missing_shipment', 'Missing Shipment'),
            ('duplicate_invoice', 'Duplicate Invoice'),
            ('invalid_charges', 'Invalid Charges')
        ]
    )
    
    severity = models.CharField(
        max_length=20,
        choices=[
            ('critical', 'Critical'),
            ('high', 'High'),
            ('medium', 'Medium'),
            ('low', 'Low')
        ],
        default='medium'
    )
    
    description = models.TextField()
    expected_value = models.CharField(max_length=200, blank=True, null=True)
    actual_value = models.CharField(max_length=200, blank=True, null=True)
    financial_impact = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    
    # Resolution
    is_resolved = models.BooleanField(default=False)
    resolution_notes = models.TextField(blank=True, null=True)
    resolved_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='resolved_discrepancies')
    resolved_at = models.DateTimeField(blank=True, null=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'reconciliation_discrepancies'
        indexes = [
            models.Index(fields=['invoice', 'discrepancy_type']),
            models.Index(fields=['severity', 'is_resolved']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f"{self.discrepancy_type} - {self.invoice.invoice_number} ({self.severity})"