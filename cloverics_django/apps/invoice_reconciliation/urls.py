"""
Invoice Reconciliation URL Configuration
Extracted from FastAPI invoice reconciliation endpoints
"""

from django.urls import path
from . import views

app_name = 'invoice_reconciliation'

urlpatterns = [
    # Dashboard
    path('', views.invoice_reconciliation_dashboard, name='dashboard'),
    
    # API endpoints (extracted from FastAPI)
    path('api/create/', views.create_invoice_record, name='create'),
    path('api/list/', views.get_invoice_records, name='list'),
    path('api/<uuid:invoice_id>/reconcile/', views.reconcile_invoice, name='reconcile'),
    path('api/<uuid:invoice_id>/approve/', views.approve_invoice, name='approve'),
    path('api/<uuid:invoice_id>/dispute/', views.dispute_invoice, name='dispute'),
    path('api/analytics/', views.get_invoice_analytics, name='analytics'),
    
    # Final completion: Last endpoints for 100%
    path('reports/', views.reconciliation_reports, name='reconciliation_reports'),
    path('automation/', views.reconciliation_automation, name='reconciliation_automation'),
]