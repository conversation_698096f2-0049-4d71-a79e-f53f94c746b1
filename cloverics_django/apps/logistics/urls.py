from django.urls import path
from . import views_fastapi_extracted as views

app_name = 'logistics'

urlpatterns = [
    # Dashboard - EXTRACTED FROM fastapi_main.py lines 1461-1561
    path('', views.dashboard, name='dashboard'),
    path('dashboard/', views.dashboard, name='dashboard'),
    
    # Pricing & Quotes - EXTRACTED FROM fastapi_main.py lines 1563-1657
    path('pricing-quotes/', views.pricing_quotes, name='pricing_quotes'),
    
    # Performance & Analytics - EXTRACTED FROM fastapi_main.py lines 8615-8656
    path('performance/', views.performance_metrics, name='performance_metrics'),
    path('analytics/', views.performance_metrics, name='analytics'),
    
    # Client Management - EXTRACTED FROM fastapi_main.py lines 8658-8688
    path('clients/', views.client_management, name='client_management'),
    
    # Revenue Control - EXTRACTED FROM fastapi_main.py lines 8690-8710
    path('revenue-control/', views.revenue_pricing_control, name='revenue_control'),
    
    # API endpoints - EXTRACTED FROM fastapi_main.py lines 5311-8200
    path('api/create-route/', views.create_route_api, name='create_route_api'),
    path('api/calculate-rate/', views.calculate_rate_api, name='calculate_rate_api'),
    path('api/save-rate/', views.save_calculated_rate_api, name='save_rate_api'),
    
    # Container sharing - EXTRACTED FROM fastapi_main.py lines 6909-7100
    path('containers/create/', views.create_shared_container, name='create_container'),
    path('containers/<int:container_id>/join/', views.request_join_shared_container, name='join_container'),
    
    # Phase 3: Additional logistics endpoints for 100% coverage
    path('fleet/', views.fleet_management, name='fleet_management'),
    path('fleet/add/', views.add_vehicle, name='add_vehicle'),
    path('fleet/<int:vehicle_id>/', views.vehicle_detail, name='vehicle_detail'),
    path('contracts/', views.contract_management, name='contract_management'),
    path('contracts/<int:contract_id>/', views.contract_detail, name='contract_detail'),
    path('revenue/', views.revenue_analytics, name='revenue_analytics'),
    path('capacity/', views.capacity_management, name='capacity_management'),
    path('optimization/', views.route_optimization, name='route_optimization'),
    path('compliance/', views.compliance_dashboard, name='compliance_dashboard'),
    path('reports/', views.logistics_reports, name='logistics_reports'),
    path('api/performance/', views.api_performance_data, name='api_performance_data'),
    path('api/revenue/', views.api_revenue_data, name='api_revenue_data'),
    path('api/fleet-status/', views.api_fleet_status, name='api_fleet_status'),
    path('api/route-optimization/', views.api_route_optimization, name='api_route_optimization'),
    path('api/capacity-utilization/', views.api_capacity_utilization, name='api_capacity_utilization'),
]