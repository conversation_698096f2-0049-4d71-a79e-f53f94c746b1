from django import forms
from django.core.validators import MinValueValidator
from .models import LogisticsProvider, Route, Quote, Fleet


class LogisticsProviderForm(forms.ModelForm):
    """Form for logistics provider profile management"""
    
    class Meta:
        model = LogisticsProvider
        fields = [
            'company_registration_number',
            'license_number', 
            'insurance_policy_number',
            'tax_id',
            'service_types',
            'coverage_areas',
            'transport_modes',
            'subscription_tier',
            'auto_quote_enabled',
            'instant_booking_enabled',
            'min_shipment_value',
            'max_shipment_value',
        ]
        
        widgets = {
            'company_registration_number': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Company Registration Number'
            }),
            'license_number': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'License Number'
            }),
            'insurance_policy_number': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Insurance Policy Number'
            }),
            'tax_id': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Tax ID'
            }),
            'service_types': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'Enter service types (JSON format)'
            }),
            'coverage_areas': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'Enter coverage areas (JSON format)'
            }),
            'transport_modes': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'Enter transport modes (JSON format)'
            }),
            'subscription_tier': forms.Select(attrs={
                'class': 'form-select'
            }),
            'auto_quote_enabled': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
            'instant_booking_enabled': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
            'min_shipment_value': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0'
            }),
            'max_shipment_value': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0'
            }),
        }


class RouteForm(forms.ModelForm):
    """Form for creating and editing shipping routes"""
    
    class Meta:
        model = Route
        fields = [
            'name',
            'description',
            'origin_country',
            'origin_city',
            'origin_port',
            'destination_country',
            'destination_city',
            'destination_port',
            'transport_mode',
            'estimated_transit_days',
            'max_weight_kg',
            'max_volume_cbm',
            'base_price_per_kg',
            'base_price_per_cbm',
            'minimum_charge',
            'fuel_surcharge_percent',
            'available_days',
            'departure_frequency',
            'is_active',
        ]
        
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Route Name'
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'Route Description'
            }),
            'origin_country': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Origin Country'
            }),
            'origin_city': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Origin City'
            }),
            'origin_port': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Origin Port (optional)'
            }),
            'destination_country': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Destination Country'
            }),
            'destination_city': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Destination City'
            }),
            'destination_port': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Destination Port (optional)'
            }),
            'transport_mode': forms.Select(attrs={
                'class': 'form-select'
            }),
            'estimated_transit_days': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '1'
            }),
            'max_weight_kg': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0'
            }),
            'max_volume_cbm': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0'
            }),
            'base_price_per_kg': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0'
            }),
            'base_price_per_cbm': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0'
            }),
            'minimum_charge': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0'
            }),
            'fuel_surcharge_percent': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0',
                'max': '100'
            }),
            'available_days': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 2,
                'placeholder': 'Available days (JSON format: ["monday", "tuesday", ...])'
            }),
            'departure_frequency': forms.Select(attrs={
                'class': 'form-select'
            }),
            'is_active': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
        }
    
    def clean_max_weight_kg(self):
        weight = self.cleaned_data.get('max_weight_kg')
        if weight and weight <= 0:
            raise forms.ValidationError("Maximum weight must be greater than 0")
        return weight
    
    def clean_max_volume_cbm(self):
        volume = self.cleaned_data.get('max_volume_cbm')
        if volume and volume <= 0:
            raise forms.ValidationError("Maximum volume must be greater than 0")
        return volume


class QuoteResponseForm(forms.ModelForm):
    """Form for responding to quote requests"""
    
    class Meta:
        model = Quote
        fields = [
            'quoted_price',
            'estimated_transit_days',
            'quote_notes',
        ]
        
        widgets = {
            'quoted_price': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0',
                'placeholder': 'Enter quoted price'
            }),
            'estimated_transit_days': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '1',
                'placeholder': 'Estimated transit days'
            }),
            'quote_notes': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': 'Additional notes for the customer'
            }),
        }
    
    def clean_quoted_price(self):
        price = self.cleaned_data.get('quoted_price')
        if price and price <= 0:
            raise forms.ValidationError("Quoted price must be greater than 0")
        return price
    
    def clean_estimated_transit_days(self):
        days = self.cleaned_data.get('estimated_transit_days')
        if days and days <= 0:
            raise forms.ValidationError("Transit days must be greater than 0")
        return days


class FleetForm(forms.ModelForm):
    """Form for fleet vehicle management"""
    
    class Meta:
        model = Fleet
        fields = [
            'license_plate',
            'vehicle_type',
            'max_weight_capacity_kg',
            'max_volume_capacity_cbm',
            'year_manufactured',
            'status',
            'current_location',
            'driver_assigned',
            'fuel_efficiency_kmpl',
            'last_maintenance_date',
            'next_maintenance_due',
            'insurance_expiry',
        ]
        
        widgets = {
            'license_plate': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'License Plate Number'
            }),
            'vehicle_type': forms.Select(attrs={
                'class': 'form-select'
            }),
            'max_weight_capacity_kg': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0'
            }),
            'max_volume_capacity_cbm': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0'
            }),
            'year_manufactured': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '1900',
                'max': '2030'
            }),
            'status': forms.Select(attrs={
                'class': 'form-select'
            }),
            'current_location': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Current Location'
            }),
            'driver_assigned': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Assigned Driver'
            }),
            'fuel_efficiency_kmpl': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0'
            }),
            'last_maintenance_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'next_maintenance_due': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'insurance_expiry': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
        }
    
    def clean_year_manufactured(self):
        year = self.cleaned_data.get('year_manufactured')
        current_year = 2025  # You might want to use timezone.now().year
        if year and (year < 1900 or year > current_year + 5):
            raise forms.ValidationError(f"Year must be between 1900 and {current_year + 5}")
        return year


class QuoteSearchForm(forms.Form):
    """Form for searching and filtering quotes"""
    
    STATUS_CHOICES = [
        ('', 'All Statuses'),
        ('pending', 'Pending'),
        ('quoted', 'Quoted'),
        ('accepted', 'Accepted'),
        ('declined', 'Declined'),
        ('expired', 'Expired'),
    ]
    
    DATE_RANGE_CHOICES = [
        ('', 'All Time'),
        ('today', 'Today'),
        ('week', 'This Week'),
        ('month', 'This Month'),
    ]
    
    search = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Search quotes...'
        })
    )
    
    status = forms.ChoiceField(
        choices=STATUS_CHOICES,
        required=False,
        widget=forms.Select(attrs={
            'class': 'form-select'
        })
    )
    
    date_range = forms.ChoiceField(
        choices=DATE_RANGE_CHOICES,
        required=False,
        widget=forms.Select(attrs={
            'class': 'form-select'
        })
    )


class RouteSearchForm(forms.Form):
    """Form for searching and filtering routes"""
    
    TRANSPORT_MODE_CHOICES = [
        ('', 'All Transport Modes'),
        ('road', 'Road Transport'),
        ('rail', 'Rail Transport'),
        ('sea', 'Sea Freight'),
        ('air', 'Air Freight'),
        ('multimodal', 'Multimodal'),
    ]
    
    STATUS_CHOICES = [
        ('', 'All Routes'),
        ('active', 'Active'),
        ('inactive', 'Inactive'),
    ]
    
    search = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Search routes...'
        })
    )
    
    transport_mode = forms.ChoiceField(
        choices=TRANSPORT_MODE_CHOICES,
        required=False,
        widget=forms.Select(attrs={
            'class': 'form-select'
        })
    )
    
    status = forms.ChoiceField(
        choices=STATUS_CHOICES,
        required=False,
        widget=forms.Select(attrs={
            'class': 'form-select'
        })
    )