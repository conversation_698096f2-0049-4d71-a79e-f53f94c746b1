from django.shortcuts import render

def dashboard(request):
    """
    Logistics Provider Dashboard
    Displays provider statistics, routes, and quick actions
    """
    # Create mock user object with proper user_type for sidebar navigation
    class MockUser:
        def __init__(self):
            self.user_type = 'LOGISTICS_PROVIDER'
            self.id = 89
            self.email = '<EMAIL>'
            self.company_name = 'Demo Logistics Provider'
    
    # Comprehensive dashboard context data
    context = {
        'title': 'Logistics Provider Dashboard - Cloverics',
        'user_type': 'LOGISTICS_PROVIDER',
        'user': MockUser(),
        'company_stats': {
            'active_routes': 12,
            'total_shipments': 89,
            'completed_deliveries': 78,
            'pending_quotes': 6,
            'monthly_revenue': 24500.00,
            'customer_rating': 4.7
        },
        'recent_shipments': [
            {
                'id': 'CL718F825C',
                'customer': 'Demo Customer Company',
                'route': 'Istanbul → Ankara',
                'status': 'In Transit',
                'value': 2500.00
            },
            {
                'id': 'CL829G934D',
                'customer': 'International Corp',
                'route': 'Izmir → Bursa',
                'status': 'Delivered',
                'value': 1800.00
            }
        ],
        'recent_quotes': [
            {
                'id': 'QT0190018773',
                'customer': 'Demo Customer Company',
                'route': 'Baku → Kayseri',
                'status': 'pending',
                'amount': 5300.00
            }
        ]
    }
    
    return render(request, 'logistics/dashboard.html', context)