{% extends "base.html" %}

{% block title %}Performance Analytics - Cloverics{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-2 sidebar">
            {% include 'components/sidebar.html' %}
        </div>
        
        <!-- Main Content -->
        <div class="col-md-10 main-content">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>Performance Analytics</h2>
                <div class="btn-group" role="group">
                    <button class="btn btn-outline-primary" onclick="exportReport()">
                        <i class="fas fa-download"></i> Export Report
                    </button>
                    <button class="btn btn-outline-secondary" onclick="refreshData()">
                        <i class="fas fa-sync-alt"></i> Refresh
                    </button>
                </div>
            </div>

            <!-- Key Performance Indicators -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h5 class="card-title">Total Revenue</h5>
                                    <h3>${{ performance_data.total_revenue }}</h3>
                                </div>
                                <i class="fas fa-dollar-sign fa-2x"></i>
                            </div>
                            <small>+{{ performance_data.revenue_growth }}% from last month</small>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h5 class="card-title">Completed Shipments</h5>
                                    <h3>{{ performance_data.completed_shipments }}</h3>
                                </div>
                                <i class="fas fa-shipping-fast fa-2x"></i>
                            </div>
                            <small>{{ performance_data.completion_rate }}% completion rate</small>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h5 class="card-title">Active Routes</h5>
                                    <h3>{{ performance_data.active_routes }}</h3>
                                </div>
                                <i class="fas fa-route fa-2x"></i>
                            </div>
                            <small>{{ performance_data.new_routes }} new this month</small>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h5 class="card-title">Customer Rating</h5>
                                    <h3>{{ performance_data.avg_rating }}/5</h3>
                                </div>
                                <i class="fas fa-star fa-2x"></i>
                            </div>
                            <small>Based on {{ performance_data.total_reviews }} reviews</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Charts and Analytics -->
            <div class="row mb-4">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title">Revenue Trend (Last 12 Months)</h5>
                        </div>
                        <div class="card-body">
                            <canvas id="revenueChart" width="400" height="200"></canvas>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title">Transport Type Distribution</h5>
                        </div>
                        <div class="card-body">
                            <canvas id="transportChart" width="200" height="200"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Performance Metrics Table -->
            <div class="row mb-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title">Route Performance</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Route</th>
                                            <th>Transport Type</th>
                                            <th>Shipments</th>
                                            <th>Revenue</th>
                                            <th>Avg. Duration</th>
                                            <th>Rating</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for route in performance_data.route_metrics %}
                                        <tr>
                                            <td>{{ route.origin }} → {{ route.destination }}</td>
                                            <td>
                                                <span class="badge badge-info">{{ route.transport_type }}</span>
                                            </td>
                                            <td>{{ route.shipment_count }}</td>
                                            <td>${{ route.revenue }}</td>
                                            <td>{{ route.avg_duration }} days</td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <span class="mr-1">{{ route.rating }}</span>
                                                    <div class="star-rating">
                                                        {% for i in range(5) %}
                                                            {% if i < route.rating %}
                                                                <i class="fas fa-star text-warning"></i>
                                                            {% else %}
                                                                <i class="far fa-star text-muted"></i>
                                                            {% endif %}
                                                        {% endfor %}
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                {% if route.is_active %}
                                                    <span class="badge badge-success">Active</span>
                                                {% else %}
                                                    <span class="badge badge-secondary">Inactive</span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Client Performance -->
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title">Top Clients</h5>
                        </div>
                        <div class="card-body">
                            <div class="list-group list-group-flush">
                                {% for client in performance_data.top_clients %}
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="mb-1">{{ client.name }}</h6>
                                        <small class="text-muted">{{ client.shipments }} shipments</small>
                                    </div>
                                    <span class="badge badge-primary badge-pill">${{ client.revenue }}</span>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title">Recent Achievements</h5>
                        </div>
                        <div class="card-body">
                            <div class="timeline">
                                {% for achievement in performance_data.achievements %}
                                <div class="timeline-item">
                                    <div class="timeline-marker bg-success"></div>
                                    <div class="timeline-content">
                                        <h6 class="timeline-title">{{ achievement.title }}</h6>
                                        <p class="timeline-text">{{ achievement.description }}</p>
                                        <small class="text-muted">{{ achievement.date }}</small>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Revenue Chart
const revenueCtx = document.getElementById('revenueChart').getContext('2d');
const revenueChart = new Chart(revenueCtx, {
    type: 'line',
    data: {
        labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
        datasets: [{
            label: 'Revenue ($)',
            data: {{ performance_data.monthly_revenue | safe }},
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.2)',
            tension: 0.1
        }]
    },
    options: {
        responsive: true,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// Transport Type Chart
const transportCtx = document.getElementById('transportChart').getContext('2d');
const transportChart = new Chart(transportCtx, {
    type: 'doughnut',
    data: {
        labels: {{ performance_data.transport_types | safe }},
        datasets: [{
            data: {{ performance_data.transport_percentages | safe }},
            backgroundColor: [
                '#FF6384',
                '#36A2EB',
                '#FFCE56',
                '#4BC0C0',
                '#9966FF'
            ]
        }]
    },
    options: {
        responsive: true,
        legend: {
            position: 'bottom'
        }
    }
});

function exportReport() {
    alert('Export functionality will be implemented with PDF generation');
}

function refreshData() {
    location.reload();
}
</script>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -35px;
    top: 0;
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.timeline-content {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border-left: 3px solid #28a745;
}

.timeline-title {
    margin-bottom: 5px;
    color: #495057;
}

.timeline-text {
    margin-bottom: 5px;
    color: #6c757d;
}
</style>
{% endblock %}