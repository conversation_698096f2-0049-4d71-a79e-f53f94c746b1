{% extends "base.html" %}

{% block title %}RFQ Automation - Cloverics{% endblock %}

{% block extra_css %}
<style>
    .rfq-card {
        background: white;
        border-radius: 12px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
        padding: 20px;
    }
    .response-card {
        border: 2px solid #e0e6ed;
        border-radius: 10px;
        padding: 15px;
        margin: 10px 0;
        transition: all 0.3s ease;
    }
    .response-card:hover {
        border-color: #28a745;
        box-shadow: 0 4px 12px rgba(40, 167, 69, 0.15);
    }
    .evaluation-result {
        border-left: 4px solid #007bff;
        padding: 15px;
        margin: 10px 0;
        background: #f8f9fa;
    }
    .score-meter {
        width: 100%;
        height: 20px;
        background: #e9ecef;
        border-radius: 10px;
        overflow: hidden;
        margin: 5px 0;
    }
    .score-fill {
        height: 100%;
        background: linear-gradient(90deg, #dc3545, #ffc107, #28a745);
        transition: width 0.5s ease;
    }
    .provider-badge {
        display: inline-block;
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 500;
        margin: 2px;
    }
    .badge-premium { background: #17a2b8; color: white; }
    .badge-verified { background: #28a745; color: white; }
    .badge-new { background: #6f42c1; color: white; }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="page-header">
                <h2><i class="fas fa-cogs me-2"></i>RFQ Automation</h2>
                <p class="text-muted">Respond to customer RFQs with AI-powered matching and automated quote generation</p>
            </div>

            <!-- Navigation Tabs -->
            <ul class="nav nav-tabs" id="rfqTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="dashboard-tab" data-bs-toggle="tab" data-bs-target="#dashboard" type="button" role="tab">
                        <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="available-rfqs-tab" data-bs-toggle="tab" data-bs-target="#available-rfqs" type="button" role="tab">
                        <i class="fas fa-clipboard-list me-2"></i>Available RFQs
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="my-responses-tab" data-bs-toggle="tab" data-bs-target="#my-responses" type="button" role="tab">
                        <i class="fas fa-reply me-2"></i>My Responses
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="evaluations-tab" data-bs-toggle="tab" data-bs-target="#evaluations" type="button" role="tab">
                        <i class="fas fa-star me-2"></i>Evaluations
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="negotiations-tab" data-bs-toggle="tab" data-bs-target="#negotiations" type="button" role="tab">
                        <i class="fas fa-handshake me-2"></i>Negotiations
                    </button>
                </li>
            </ul>

            <!-- Tab Content -->
            <div class="tab-content" id="rfqTabContent">
                <!-- Dashboard Tab -->
                <div class="tab-pane fade show active" id="dashboard" role="tabpanel">
                    <div class="rfq-card">
                        <h4><i class="fas fa-chart-bar me-2"></i>RFQ Performance Overview</h4>
                        <div class="row" id="dashboardCards">
                            <div class="col-md-3">
                                <div class="card bg-info text-white">
                                    <div class="card-body">
                                        <h5 class="card-title">Available RFQs</h5>
                                        <h2 id="availableRfqs">0</h2>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-primary text-white">
                                    <div class="card-body">
                                        <h5 class="card-title">My Responses</h5>
                                        <h2 id="myResponses">0</h2>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-success text-white">
                                    <div class="card-body">
                                        <h5 class="card-title">Win Rate</h5>
                                        <h2 id="winRate">0%</h2>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-warning text-white">
                                    <div class="card-body">
                                        <h5 class="card-title">Avg Score</h5>
                                        <h2 id="avgScore">0.0</h2>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-4">
                            <div class="col-md-12">
                                <h5>Recent RFQ Activity</h5>
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>RFQ ID</th>
                                                <th>Customer</th>
                                                <th>Route</th>
                                                <th>My Response</th>
                                                <th>Evaluation Score</th>
                                                <th>Status</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody id="recentRfqActivity">
                                            <tr>
                                                <td colspan="7" class="text-center">Loading recent activity...</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Available RFQs Tab -->
                <div class="tab-pane fade" id="available-rfqs" role="tabpanel">
                    <div class="rfq-card">
                        <h4><i class="fas fa-clipboard-list me-2"></i>Available RFQs</h4>
                        
                        <div class="row mb-3">
                            <div class="col-md-3">
                                <label for="scenarioFilter" class="form-label">Filter by Scenario</label>
                                <select class="form-select" id="scenarioFilter">
                                    <option value="">All Scenarios</option>
                                    <option value="SINGLE_ROUTE">Single Route</option>
                                    <option value="MULTI_ROUTE">Multi Route</option>
                                    <option value="SEASONAL_CONTRACT">Seasonal Contract</option>
                                    <option value="VOLUME_COMMITMENT">Volume Commitment</option>
                                    <option value="EMERGENCY_SHIPPING">Emergency Shipping</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="priorityFilter" class="form-label">Filter by Priority</label>
                                <select class="form-select" id="priorityFilter">
                                    <option value="">All Priorities</option>
                                    <option value="URGENT">Urgent</option>
                                    <option value="HIGH">High</option>
                                    <option value="MEDIUM">Medium</option>
                                    <option value="LOW">Low</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">&nbsp;</label>
                                <button type="button" class="btn btn-primary d-block" onclick="loadAvailableRfqs()">
                                    <i class="fas fa-sync-alt me-2"></i>Refresh RFQs
                                </button>
                            </div>
                        </div>

                        <div id="availableRfqsList">
                            <div class="text-center py-4">
                                <p class="text-muted">Loading available RFQs...</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- My Responses Tab -->
                <div class="tab-pane fade" id="my-responses" role="tabpanel">
                    <div class="rfq-card">
                        <h4><i class="fas fa-reply me-2"></i>My RFQ Responses</h4>
                        
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label for="responseStatusFilter" class="form-label">Filter by Status</label>
                                <select class="form-select" id="responseStatusFilter">
                                    <option value="">All Statuses</option>
                                    <option value="SUBMITTED">Submitted</option>
                                    <option value="UNDER_EVALUATION">Under Evaluation</option>
                                    <option value="SHORTLISTED">Shortlisted</option>
                                    <option value="AWARDED">Awarded</option>
                                    <option value="REJECTED">Rejected</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">&nbsp;</label>
                                <button type="button" class="btn btn-success d-block" onclick="loadMyResponses()">
                                    <i class="fas fa-list me-2"></i>Load My Responses
                                </button>
                            </div>
                        </div>

                        <div id="myResponsesList">
                            <div class="text-center py-4">
                                <p class="text-muted">No responses submitted yet</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Evaluations Tab -->
                <div class="tab-pane fade" id="evaluations" role="tabpanel">
                    <div class="rfq-card">
                        <h4><i class="fas fa-star me-2"></i>AI Evaluation Results</h4>
                        
                        <div id="evaluationResults">
                            <div class="text-center py-4">
                                <p class="text-muted">No evaluation results available</p>
                                <p class="small text-muted">Submit responses to RFQs to see AI-powered evaluation results</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Negotiations Tab -->
                <div class="tab-pane fade" id="negotiations" role="tabpanel">
                    <div class="rfq-card">
                        <h4><i class="fas fa-handshake me-2"></i>Active Negotiations</h4>
                        
                        <div id="negotiationsList">
                            <div class="text-center py-4">
                                <p class="text-muted">No active negotiations</p>
                                <p class="small text-muted">Win RFQ evaluations to participate in structured negotiations</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- RFQ Response Modal -->
<div class="modal fade" id="responseModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Submit RFQ Response</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="responseForm">
                    <input type="hidden" id="responseRfqId" name="rfq_id">
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="totalCost" class="form-label">Total Cost ($)</label>
                                <input type="number" class="form-control" id="totalCost" name="total_cost" min="0" step="0.01" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="deliveryTime" class="form-label">Delivery Time (days)</label>
                                <input type="number" class="form-control" id="deliveryTime" name="delivery_time" min="1" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="validityPeriod" class="form-label">Quote Validity (days)</label>
                                <input type="number" class="form-control" id="validityPeriod" name="validity_period" min="1" value="30" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="complianceScore" class="form-label">Compliance Score (0-1)</label>
                                <input type="number" class="form-control" id="complianceScore" name="compliance_score" min="0" max="1" step="0.1" value="0.9">
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="proposalDetails" class="form-label">Service Capabilities (JSON)</label>
                        <textarea class="form-control" id="proposalDetails" name="proposal_details" rows="3" placeholder='{"tracking_capability": 1.0, "insurance_coverage": 0.9, "express_service": true}'></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="specialTerms" class="form-label">Special Terms & Value Propositions</label>
                        <textarea class="form-control" id="specialTerms" name="special_terms" rows="2" placeholder="Premium tracking, 24/7 support, guaranteed delivery..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="submitResponse()">Submit Response</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let availableRfqs = [];
let myResponses = [];

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    loadDashboardData();
    loadAvailableRfqs();
});

// Load dashboard data
async function loadDashboardData() {
    try {
        const response = await fetch('/api/rfq/dashboard');
        const result = await response.json();
        
        if (result.success) {
            updateLogisticsDashboard(result.dashboard);
        }
    } catch (error) {
        console.error('Error loading dashboard:', error);
    }
}

// Update logistics provider dashboard
function updateLogisticsDashboard(data) {
    document.getElementById('availableRfqs').textContent = data.available_rfqs || 0;
    document.getElementById('myResponses').textContent = data.my_responses || 0;
    document.getElementById('winRate').textContent = Math.round((data.win_rate || 0) * 100) + '%';
    document.getElementById('avgScore').textContent = (data.average_score || 0).toFixed(1);
    
    // Update recent activity
    const tbody = document.getElementById('recentRfqActivity');
    if (data.recent_responses && data.recent_responses.length > 0) {
        tbody.innerHTML = data.recent_responses.map(response => `
            <tr>
                <td>${response.rfq_id.substring(0, 8)}...</td>
                <td>${response.customer_name}</td>
                <td>${response.route_description}</td>
                <td>$${response.my_cost.toLocaleString()}</td>
                <td>
                    <div class="score-meter">
                        <div class="score-fill" style="width: ${response.evaluation_score * 100}%"></div>
                    </div>
                    ${response.evaluation_score.toFixed(2)}
                </td>
                <td><span class="badge bg-info">${response.status}</span></td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="viewResponseDetails('${response.response_id}')">
                        <i class="fas fa-eye"></i>
                    </button>
                </td>
            </tr>
        `).join('');
    } else {
        tbody.innerHTML = '<tr><td colspan="7" class="text-center text-muted">No RFQ responses yet</td></tr>';
    }
}

// Load available RFQs
async function loadAvailableRfqs() {
    try {
        const scenarioFilter = document.getElementById('scenarioFilter').value;
        const priorityFilter = document.getElementById('priorityFilter').value;
        
        const params = new URLSearchParams();
        if (scenarioFilter) params.append('scenario_type', scenarioFilter);
        if (priorityFilter) params.append('priority', priorityFilter);
        
        const response = await fetch(`/api/rfq/available?${params}`);
        const result = await response.json();
        
        if (result.success) {
            availableRfqs = result.rfqs;
            displayAvailableRfqs();
        }
    } catch (error) {
        console.error('Error loading available RFQs:', error);
    }
}

// Display available RFQs
function displayAvailableRfqs() {
    const container = document.getElementById('availableRfqsList');
    
    if (availableRfqs.length === 0) {
        container.innerHTML = '<div class="text-center py-4"><p class="text-muted">No RFQs match your criteria</p></div>';
        return;
    }
    
    container.innerHTML = availableRfqs.map(rfq => `
        <div class="response-card">
            <div class="row">
                <div class="col-md-8">
                    <h6>${rfq.scenario_name}</h6>
                    <p class="text-muted mb-2">${rfq.description}</p>
                    <div class="mb-2">
                        <span class="provider-badge badge-${rfq.priority.toLowerCase()}">${rfq.priority}</span>
                        <span class="provider-badge badge-verified">${rfq.scenario_type.replace('_', ' ')}</span>
                    </div>
                    <p class="small mb-0">
                        <strong>Budget:</strong> $${rfq.budget_range.min.toLocaleString()} - $${rfq.budget_range.max.toLocaleString()}<br>
                        <strong>Deadline:</strong> ${new Date(rfq.deadline).toLocaleDateString()}<br>
                        <strong>Responses:</strong> ${rfq.responses_count || 0} received
                    </p>
                </div>
                <div class="col-md-4 text-end">
                    <button class="btn btn-primary btn-sm mb-2" onclick="respondToRfq('${rfq.rfq_id}')">
                        <i class="fas fa-reply me-1"></i>Submit Response
                    </button><br>
                    <button class="btn btn-outline-info btn-sm" onclick="viewRfqDetails('${rfq.rfq_id}')">
                        <i class="fas fa-eye me-1"></i>View Details
                    </button>
                </div>
            </div>
        </div>
    `).join('');
}

// Load my responses
async function loadMyResponses() {
    try {
        const statusFilter = document.getElementById('responseStatusFilter').value;
        
        const params = new URLSearchParams();
        if (statusFilter) params.append('status', statusFilter);
        
        const response = await fetch(`/api/rfq/my-responses?${params}`);
        const result = await response.json();
        
        if (result.success) {
            myResponses = result.responses;
            displayMyResponses();
        }
    } catch (error) {
        console.error('Error loading my responses:', error);
    }
}

// Display my responses
function displayMyResponses() {
    const container = document.getElementById('myResponsesList');
    
    if (myResponses.length === 0) {
        container.innerHTML = '<div class="text-center py-4"><p class="text-muted">No responses found</p></div>';
        return;
    }
    
    container.innerHTML = myResponses.map(response => `
        <div class="evaluation-result">
            <div class="row">
                <div class="col-md-8">
                    <h6>RFQ: ${response.rfq_scenario_name}</h6>
                    <p class="mb-2">
                        <strong>My Quote:</strong> $${response.total_cost.toLocaleString()}<br>
                        <strong>Delivery Time:</strong> ${response.delivery_time} days<br>
                        <strong>Submitted:</strong> ${new Date(response.response_date).toLocaleDateString()}
                    </p>
                    ${response.evaluation_result ? `
                        <div class="score-meter">
                            <div class="score-fill" style="width: ${response.evaluation_result.overall_score * 100}%"></div>
                        </div>
                        <p class="small"><strong>AI Score:</strong> ${response.evaluation_result.overall_score.toFixed(2)} | <strong>Rank:</strong> #${response.evaluation_result.ranking}</p>
                    ` : ''}
                </div>
                <div class="col-md-4 text-end">
                    <span class="badge bg-${getStatusColor(response.status)} mb-2">${response.status}</span><br>
                    <button class="btn btn-outline-primary btn-sm" onclick="viewResponseDetails('${response.response_id}')">
                        <i class="fas fa-eye me-1"></i>View Details
                    </button>
                </div>
            </div>
        </div>
    `).join('');
}

// Get status color
function getStatusColor(status) {
    const colors = {
        'SUBMITTED': 'primary',
        'UNDER_EVALUATION': 'warning',
        'SHORTLISTED': 'info',
        'AWARDED': 'success',
        'REJECTED': 'danger'
    };
    return colors[status] || 'secondary';
}

// Respond to RFQ
function respondToRfq(rfqId) {
    document.getElementById('responseRfqId').value = rfqId;
    
    // Find the RFQ details
    const rfq = availableRfqs.find(r => r.rfq_id === rfqId);
    if (rfq) {
        // Pre-fill some reasonable defaults based on RFQ
        const midBudget = (rfq.budget_range.min + rfq.budget_range.max) / 2;
        document.getElementById('totalCost').value = midBudget;
    }
    
    const modal = new bootstrap.Modal(document.getElementById('responseModal'));
    modal.show();
}

// Submit response
async function submitResponse() {
    const form = document.getElementById('responseForm');
    const formData = new FormData(form);
    
    // Add provider information
    formData.append('provider_name', 'Demo Logistics Provider');
    formData.append('provider_id', 'PROVIDER_001');
    
    try {
        const response = await fetch('/api/rfq/submit-response', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        if (result.success) {
            Swal.fire({
                title: 'Response Submitted!',
                text: 'Your RFQ response has been submitted successfully',
                icon: 'success',
                confirmButtonText: 'OK'
            });
            
            // Close modal and refresh
            const modal = bootstrap.Modal.getInstance(document.getElementById('responseModal'));
            modal.hide();
            
            // Refresh dashboard and available RFQs
            loadDashboardData();
            loadAvailableRfqs();
            
            // Reset form
            form.reset();
        } else {
            Swal.fire({
                title: 'Error',
                text: result.message || 'Failed to submit response',
                icon: 'error'
            });
        }
    } catch (error) {
        console.error('Error submitting response:', error);
        Swal.fire({
            title: 'Error',
            text: 'An error occurred while submitting your response',
            icon: 'error'
        });
    }
}

// View RFQ details
function viewRfqDetails(rfqId) {
    const rfq = availableRfqs.find(r => r.rfq_id === rfqId);
    if (!rfq) return;
    
    Swal.fire({
        title: 'RFQ Details',
        html: `
            <div class="text-start">
                <h6>${rfq.scenario_name}</h6>
                <p><strong>Type:</strong> ${rfq.scenario_type.replace('_', ' ')}</p>
                <p><strong>Priority:</strong> ${rfq.priority}</p>
                <p><strong>Description:</strong> ${rfq.description}</p>
                <p><strong>Budget Range:</strong> $${rfq.budget_range.min.toLocaleString()} - $${rfq.budget_range.max.toLocaleString()}</p>
                <p><strong>Deadline:</strong> ${new Date(rfq.deadline).toLocaleDateString()}</p>
                <p><strong>Responses Received:</strong> ${rfq.responses_count || 0}</p>
            </div>
        `,
        showCancelButton: true,
        confirmButtonText: 'Submit Response',
        cancelButtonText: 'Close'
    }).then((result) => {
        if (result.isConfirmed) {
            respondToRfq(rfqId);
        }
    });
}

// View response details
function viewResponseDetails(responseId) {
    const response = myResponses.find(r => r.response_id === responseId);
    if (!response) return;
    
    let evaluationHtml = '';
    if (response.evaluation_result) {
        evaluationHtml = `
            <div class="mt-3">
                <h6>AI Evaluation Results</h6>
                <p><strong>Overall Score:</strong> ${response.evaluation_result.overall_score.toFixed(2)}</p>
                <p><strong>Ranking:</strong> #${response.evaluation_result.ranking}</p>
                <p><strong>Recommendation:</strong> ${response.evaluation_result.recommendation}</p>
            </div>
        `;
    }
    
    Swal.fire({
        title: 'Response Details',
        html: `
            <div class="text-start">
                <p><strong>RFQ:</strong> ${response.rfq_scenario_name}</p>
                <p><strong>My Quote:</strong> $${response.total_cost.toLocaleString()}</p>
                <p><strong>Delivery Time:</strong> ${response.delivery_time} days</p>
                <p><strong>Validity Period:</strong> ${response.validity_period} days</p>
                <p><strong>Status:</strong> <span class="badge bg-${getStatusColor(response.status)}">${response.status}</span></p>
                <p><strong>Submitted:</strong> ${new Date(response.response_date).toLocaleDateString()}</p>
                ${evaluationHtml}
            </div>
        `,
        confirmButtonText: 'Close'
    });
}

// Initialize SweetAlert2 if not already loaded
if (typeof Swal === 'undefined') {
    const script = document.createElement('script');
    script.src = 'https://cdn.jsdelivr.net/npm/sweetalert2@11';
    document.head.appendChild(script);
}

// Filter event listeners
document.getElementById('scenarioFilter').addEventListener('change', loadAvailableRfqs);
document.getElementById('priorityFilter').addEventListener('change', loadAvailableRfqs);
document.getElementById('responseStatusFilter').addEventListener('change', loadMyResponses);
</script>
{% endblock %}