{% extends "base.html" %}

{% block content %}
<div class="dashboard-container">
    <div class="dashboard-header">
        <h1>Revenue & Pricing Control</h1>
        <p>Optimize your commission rates, payment schedules, and earnings strategy</p>
    </div>

    <!-- Commission Overview -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-primary">
                    <i class="fas fa-percentage"></i>
                </div>
                <div class="stat-content">
                    <h3>12.5%</h3>
                    <p>Base Commission</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-success">
                    <i class="fas fa-dollar-sign"></i>
                </div>
                <div class="stat-content">
                    <h3>$3,240</h3>
                    <p>This Month</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-warning">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="stat-content">
                    <h3>$890</h3>
                    <p>Pending</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-info">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="stat-content">
                    <h3>$18,650</h3>
                    <p>Total Earned</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Commission Rate Settings -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5>Commission Rate Configuration</h5>
                </div>
                <div class="card-body">
                    <form>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Base Commission Rate (%)</label>
                                    <input type="number" class="form-control" value="12.5" step="0.1">
                                    <small class="text-muted">Standard rate for all shipments</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Express Service Rate (%)</label>
                                    <input type="number" class="form-control" value="15.0" step="0.1">
                                    <small class="text-muted">Additional rate for express deliveries</small>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">International Rate (%)</label>
                                    <input type="number" class="form-control" value="18.0" step="0.1">
                                    <small class="text-muted">Rate for international shipments</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Minimum Commission ($)</label>
                                    <input type="number" class="form-control" value="25.00" step="0.01">
                                    <small class="text-muted">Minimum commission per shipment</small>
                                </div>
                            </div>
                        </div>
                        <button type="submit" class="btn btn-primary" onclick="updateCommissionRates()">Update Commission Rates</button>
                    </form>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5>Payment Schedule</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label">Payment Frequency</label>
                        <select class="form-select">
                            <option>Weekly</option>
                            <option selected>Bi-weekly</option>
                            <option>Monthly</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Payment Method</label>
                        <select class="form-select">
                            <option>Bank Transfer</option>
                            <option selected>Direct Deposit</option>
                            <option>Check</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Next Payment Date</label>
                        <input type="date" class="form-control" value="2025-02-01">
                    </div>
                    <button class="btn btn-outline-primary" onclick="updatePaymentSettings()">Update Payment Settings</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Commission History -->
    <div class="card">
        <div class="card-header">
            <h5>Recent Commission Payments</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Payment Date</th>
                            <th>Period</th>
                            <th>Shipments</th>
                            <th>Gross Revenue</th>
                            <th>Commission Rate</th>
                            <th>Commission Amount</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>2025-01-15</td>
                            <td>Jan 1-15, 2025</td>
                            <td>24</td>
                            <td>$18,500</td>
                            <td>12.5%</td>
                            <td>$2,312.50</td>
                            <td><span class="badge bg-success">Paid</span></td>
                        </tr>
                        <tr>
                            <td>2025-01-01</td>
                            <td>Dec 15-31, 2024</td>
                            <td>18</td>
                            <td>$14,200</td>
                            <td>12.5%</td>
                            <td>$1,775.00</td>
                            <td><span class="badge bg-success">Paid</span></td>
                        </tr>
                        <tr>
                            <td>2024-12-15</td>
                            <td>Dec 1-15, 2024</td>
                            <td>22</td>
                            <td>$16,800</td>
                            <td>12.5%</td>
                            <td>$2,100.00</td>
                            <td><span class="badge bg-success">Paid</span></td>
                        </tr>
                        <tr>
                            <td>Pending</td>
                            <td>Jan 16-31, 2025</td>
                            <td>16</td>
                            <td>$12,400</td>
                            <td>12.5%</td>
                            <td>$1,550.00</td>
                            <td><span class="badge bg-warning">Pending</span></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}