{% extends 'base.html' %}
{% load translation_tags %}

{% block title %}Logistics Provider Dashboard - Cloverics{% endblock %}

{% block content %}
<div class="main-content">
    <div class="container-fluid">
        <!-- Dashboard Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1>{% translate 'logistics_provider_dashboard' %}</h1>
                        <p class="text-muted">{% translate 'welcome_back' %}, {{ user.company_name }}</p>
                    </div>
                    <div class="text-end">
                        <span class="badge bg-success fs-6">{{ user.user_type }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- KPI Cards -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-grow-1">
                                <h4 class="mb-0">{{ stats.active_routes }}</h4>
                                <p class="mb-0">{% translate 'active_routes' %}</p>
                            </div>
                            <div class="ms-3">
                                <i class="fas fa-route fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-grow-1">
                                <h4 class="mb-0">{{ stats.pending_quotes }}</h4>
                                <p class="mb-0">{% translate 'pending_quotes' %}</p>
                            </div>
                            <div class="ms-3">
                                <i class="fas fa-quote-left fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-grow-1">
                                <h4 class="mb-0">${{ stats.monthly_revenue }}</h4>
                                <p class="mb-0">{% translate 'monthly_revenue' %}</p>
                            </div>
                            <div class="ms-3">
                                <i class="fas fa-dollar-sign fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-grow-1">
                                <h4 class="mb-0">{{ stats.customer_rating }}/5</h4>
                                <p class="mb-0">{% translate 'customer_rating' %}</p>
                            </div>
                            <div class="ms-3">
                                <i class="fas fa-star fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">{% translate 'quick_actions' %}</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 mb-2">
                                <a href="/logistics/routes" class="btn btn-primary w-100">
                                    <i class="fas fa-plus-circle me-2"></i>{% translate 'add_route' %}
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="/logistics/pricing-quotes" class="btn btn-success w-100">
                                    <i class="fas fa-quote-left me-2"></i>{% translate 'manage_quotes' %}
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="/logistics/driver-management" class="btn btn-info w-100">
                                    <i class="fas fa-users me-2"></i>{% translate 'driver_management' %}
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="/logistics/performance-metrics" class="btn btn-warning w-100">
                                    <i class="fas fa-chart-bar me-2"></i>{% translate 'view_analytics' %}
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Shipments & Quotes -->
        <div class="row">
            <div class="col-lg-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">{% translate 'recent_shipments' %}</h5>
                    </div>
                    <div class="card-body">
                        {% for shipment in recent_shipments %}
                        <div class="d-flex justify-content-between align-items-center border-bottom pb-2 mb-2">
                            <div>
                                <strong>{{ shipment.id }}</strong><br>
                                <small class="text-muted">{{ shipment.customer }}</small><br>
                                <small>{{ shipment.route }}</small>
                            </div>
                            <div class="text-end">
                                <span class="badge bg-primary">{{ shipment.status }}</span><br>
                                <strong>${{ shipment.value }}</strong>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>

            <div class="col-lg-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">{% translate 'recent_quotes' %}</h5>
                    </div>
                    <div class="card-body">
                        {% for quote in recent_quotes %}
                        <div class="d-flex justify-content-between align-items-center border-bottom pb-2 mb-2">
                            <div>
                                <strong>{{ quote.id }}</strong><br>
                                <small class="text-muted">{{ quote.customer }}</small><br>
                                <small>{{ quote.route }}</small>
                            </div>
                            <div class="text-end">
                                <span class="badge bg-warning">{{ quote.status }}</span><br>
                                <strong>${{ quote.amount }}</strong>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}