{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1><i class="fas fa-calculator"></i> Enhanced ERP & Finance Hub</h1>
                <div class="btn-group">
                    <button class="btn btn-primary" onclick="createInvoice()">
                        <i class="fas fa-plus"></i> Create Invoice
                    </button>
                    <button class="btn btn-outline-secondary" onclick="exportFinancialReport()">
                        <i class="fas fa-download"></i> Export Report
                    </button>
                </div>
            </div>

            <!-- Financial Summary Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>${{ financial_summary.revenue.total_revenue or 0 }}</h4>
                                    <p class="mb-0">Total Revenue</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-chart-line fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>${{ financial_summary.receivables.outstanding_receivables or 0 }}</h4>
                                    <p class="mb-0">Outstanding A/R</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-money-bill-wave fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>${{ financial_summary.receivables.overdue_amount or 0 }}</h4>
                                    <p class="mb-0">Overdue Amount</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-exclamation-triangle fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>{{ financial_summary.performance.collection_rate or 0 }}%</h4>
                                    <p class="mb-0">Collection Rate</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-percentage fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Content Tabs -->
            <ul class="nav nav-tabs" id="erpTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="invoices-tab" data-bs-toggle="tab" data-bs-target="#invoices" type="button" role="tab">
                        <i class="fas fa-file-invoice"></i> Invoices
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="payments-tab" data-bs-toggle="tab" data-bs-target="#payments" type="button" role="tab">
                        <i class="fas fa-credit-card"></i> Payments
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="analytics-tab" data-bs-toggle="tab" data-bs-target="#analytics" type="button" role="tab">
                        <i class="fas fa-chart-pie"></i> Financial Analytics
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="ar-management-tab" data-bs-toggle="tab" data-bs-target="#ar-management" type="button" role="tab">
                        <i class="fas fa-clock"></i> A/R Management
                    </button>
                </li>
            </ul>

            <div class="tab-content" id="erpTabsContent">
                <!-- Invoices Tab -->
                <div class="tab-pane fade show active" id="invoices" role="tabpanel">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0"><i class="fas fa-file-invoice"></i> Invoice Management</h5>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary" onclick="filterInvoices('all')">All</button>
                                <button class="btn btn-outline-warning" onclick="filterInvoices('draft')">Draft</button>
                                <button class="btn btn-outline-info" onclick="filterInvoices('sent')">Sent</button>
                                <button class="btn btn-outline-success" onclick="filterInvoices('paid')">Paid</button>
                                <button class="btn btn-outline-danger" onclick="filterInvoices('overdue')">Overdue</button>
                            </div>
                        </div>
                        <div class="card-body">
                            {% if invoices %}
                            <div class="table-responsive">
                                <table class="table table-hover" id="invoicesTable">
                                    <thead>
                                        <tr>
                                            <th>Invoice #</th>
                                            <th>Customer</th>
                                            <th>Amount</th>
                                            <th>Status</th>
                                            <th>Issue Date</th>
                                            <th>Due Date</th>
                                            <th>Days Outstanding</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for invoice in invoices %}
                                        <tr class="invoice-row" data-status="{{ invoice.status }}">
                                            <td><strong>{{ invoice.invoice_number }}</strong></td>
                                            <td>Customer #{{ invoice.customer_id }}</td>
                                            <td>${{ invoice.total_amount }} {{ invoice.currency }}</td>
                                            <td>
                                                {% if invoice.status == 'paid' %}
                                                <span class="badge bg-success">Paid</span>
                                                {% elif invoice.status == 'overdue' or invoice.is_overdue %}
                                                <span class="badge bg-danger">Overdue</span>
                                                {% elif invoice.status == 'sent' %}
                                                <span class="badge bg-info">Sent</span>
                                                {% elif invoice.status == 'partial_paid' %}
                                                <span class="badge bg-warning">Partial</span>
                                                {% else %}
                                                <span class="badge bg-secondary">{{ invoice.status|title }}</span>
                                                {% endif %}
                                            </td>
                                            <td>{{ invoice.issue_date[:10] }}</td>
                                            <td>{{ invoice.due_date[:10] }}</td>
                                            <td>
                                                {% if invoice.days_outstanding > 30 %}
                                                <span class="text-danger">{{ invoice.days_outstanding }} days</span>
                                                {% elif invoice.days_outstanding > 0 %}
                                                <span class="text-warning">{{ invoice.days_outstanding }} days</span>
                                                {% else %}
                                                <span class="text-muted">{{ invoice.days_outstanding }} days</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-primary" onclick="viewInvoice('{{ invoice.invoice_id }}')">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    {% if invoice.status == 'draft' %}
                                                    <button class="btn btn-outline-success" onclick="sendInvoice('{{ invoice.invoice_id }}')">
                                                        <i class="fas fa-paper-plane"></i>
                                                    </button>
                                                    {% endif %}
                                                    {% if invoice.status != 'paid' %}
                                                    <button class="btn btn-outline-info" onclick="recordPayment('{{ invoice.invoice_id }}')">
                                                        <i class="fas fa-dollar-sign"></i>
                                                    </button>
                                                    {% endif %}
                                                </div>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            {% else %}
                            <div class="text-center py-5">
                                <i class="fas fa-file-invoice fa-3x text-muted mb-3"></i>
                                <h5>No Invoices Yet</h5>
                                <p class="text-muted">Start by creating your first invoice for completed shipments.</p>
                                <button class="btn btn-primary" onclick="createInvoice()">
                                    <i class="fas fa-plus"></i> Create First Invoice
                                </button>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Payments Tab -->
                <div class="tab-pane fade" id="payments" role="tabpanel">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-credit-card"></i> Payment History</h5>
                        </div>
                        <div class="card-body">
                            {% if payment_history %}
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Payment ID</th>
                                            <th>Invoice #</th>
                                            <th>Amount</th>
                                            <th>Method</th>
                                            <th>Status</th>
                                            <th>Transaction ID</th>
                                            <th>Date</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for payment in payment_history %}
                                        <tr>
                                            <td><code>{{ payment.payment_id[:8] }}...</code></td>
                                            <td>{{ payment.invoice_number }}</td>
                                            <td>${{ payment.amount }} {{ payment.currency }}</td>
                                            <td>
                                                {% if payment.method == 'credit_card' %}
                                                <i class="fas fa-credit-card"></i> Credit Card
                                                {% elif payment.method == 'bank_transfer' %}
                                                <i class="fas fa-university"></i> Bank Transfer
                                                {% elif payment.method == 'digital_wallet' %}
                                                <i class="fas fa-wallet"></i> Digital Wallet
                                                {% else %}
                                                <i class="fas fa-money-bill"></i> {{ payment.method|title }}
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if payment.status == 'completed' %}
                                                <span class="badge bg-success">Completed</span>
                                                {% elif payment.status == 'processing' %}
                                                <span class="badge bg-warning">Processing</span>
                                                {% elif payment.status == 'failed' %}
                                                <span class="badge bg-danger">Failed</span>
                                                {% else %}
                                                <span class="badge bg-secondary">{{ payment.status|title }}</span>
                                                {% endif %}
                                            </td>
                                            <td><code>{{ payment.transaction_id }}</code></td>
                                            <td>{{ payment.processed_at[:10] if payment.processed_at else 'Pending' }}</td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-primary" onclick="viewPaymentDetails('{{ payment.payment_id }}')">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    {% if payment.status == 'completed' %}
                                                    <button class="btn btn-outline-warning" onclick="requestRefund('{{ payment.payment_id }}')">
                                                        <i class="fas fa-undo"></i>
                                                    </button>
                                                    {% endif %}
                                                </div>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            {% else %}
                            <div class="text-center py-5">
                                <i class="fas fa-credit-card fa-3x text-muted mb-3"></i>
                                <h5>No Payments Recorded</h5>
                                <p class="text-muted">Payment history will appear here once customers start paying invoices.</p>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Financial Analytics Tab -->
                <div class="tab-pane fade" id="analytics" role="tabpanel">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-chart-pie"></i> Financial Analytics Dashboard</h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <canvas id="revenueChart" width="400" height="200"></canvas>
                                </div>
                                <div class="col-md-6">
                                    <canvas id="paymentMethodChart" width="400" height="200"></canvas>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="card bg-light">
                                        <div class="card-header">
                                            <h6 class="mb-0">Key Metrics (30 days)</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between mb-2">
                                                <span>Average Payment Days:</span>
                                                <strong>{{ financial_summary.performance.average_payment_days or 0 }} days</strong>
                                            </div>
                                            <div class="d-flex justify-content-between mb-2">
                                                <span>Collection Rate:</span>
                                                <strong>{{ financial_summary.performance.collection_rate or 0 }}%</strong>
                                            </div>
                                            <div class="d-flex justify-content-between mb-2">
                                                <span>Total Invoices:</span>
                                                <strong>{{ financial_summary.performance.invoice_count or 0 }}</strong>
                                            </div>
                                            <div class="d-flex justify-content-between">
                                                <span>Paid Invoices:</span>
                                                <strong>{{ financial_summary.performance.paid_invoice_count or 0 }}</strong>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-8">
                                    {% if financial_summary.aging_analysis %}
                                    <div class="card bg-light">
                                        <div class="card-header">
                                            <h6 class="mb-0">Accounts Receivable Aging</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="row text-center">
                                                <div class="col-3">
                                                    <div class="aging-bucket">
                                                        <h5 class="text-success">${{ financial_summary.aging_analysis.current or 0 }}</h5>
                                                        <small class="text-muted">Current (0-30)</small>
                                                    </div>
                                                </div>
                                                <div class="col-3">
                                                    <div class="aging-bucket">
                                                        <h5 class="text-warning">${{ financial_summary.aging_analysis.30_days or 0 }}</h5>
                                                        <small class="text-muted">31-60 Days</small>
                                                    </div>
                                                </div>
                                                <div class="col-3">
                                                    <div class="aging-bucket">
                                                        <h5 class="text-danger">${{ financial_summary.aging_analysis.60_days or 0 }}</h5>
                                                        <small class="text-muted">61-90 Days</small>
                                                    </div>
                                                </div>
                                                <div class="col-3">
                                                    <div class="aging-bucket">
                                                        <h5 class="text-dark">${{ financial_summary.aging_analysis.90_days_plus or 0 }}</h5>
                                                        <small class="text-muted">90+ Days</small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- A/R Management Tab -->
                <div class="tab-pane fade" id="ar-management" role="tabpanel">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-clock"></i> Accounts Receivable Management</h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-4">
                                <div class="col-md-8">
                                    <h6>Overdue Invoices Requiring Action</h6>
                                </div>
                                <div class="col-md-4 text-end">
                                    <button class="btn btn-warning btn-sm" onclick="sendRemindersBulk()">
                                        <i class="fas fa-envelope"></i> Send Reminders
                                    </button>
                                </div>
                            </div>
                            
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Invoice #</th>
                                            <th>Customer</th>
                                            <th>Amount</th>
                                            <th>Days Overdue</th>
                                            <th>Risk Level</th>
                                            <th>Last Contact</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><strong>INV-202507-001</strong></td>
                                            <td>Global Shipping Co.</td>
                                            <td>$2,450.00</td>
                                            <td><span class="text-danger">15 days</span></td>
                                            <td><span class="badge bg-warning">Medium</span></td>
                                            <td>2025-06-20</td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-primary" onclick="sendReminder('INV-202507-001')">
                                                        <i class="fas fa-envelope"></i>
                                                    </button>
                                                    <button class="btn btn-outline-success" onclick="scheduleCall('INV-202507-001')">
                                                        <i class="fas fa-phone"></i>
                                                    </button>
                                                    <button class="btn btn-outline-warning" onclick="setPaymentPlan('INV-202507-001')">
                                                        <i class="fas fa-calendar"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>INV-202507-002</strong></td>
                                            <td>Logistics Solutions Inc.</td>
                                            <td>$1,875.50</td>
                                            <td><span class="text-warning">8 days</span></td>
                                            <td><span class="badge bg-success">Low</span></td>
                                            <td>2025-06-25</td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-primary" onclick="sendReminder('INV-202507-002')">
                                                        <i class="fas fa-envelope"></i>
                                                    </button>
                                                    <button class="btn btn-outline-success" onclick="scheduleCall('INV-202507-002')">
                                                        <i class="fas fa-phone"></i>
                                                    </button>
                                                    <button class="btn btn-outline-warning" onclick="setPaymentPlan('INV-202507-002')">
                                                        <i class="fas fa-calendar"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create Invoice Modal -->
<div class="modal fade" id="createInvoiceModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Create New Invoice</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="createInvoiceForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Customer</label>
                                <select class="form-select" id="customerId" required>
                                    <option value="">Select Customer</option>
                                    <option value="1">Global Shipping Co.</option>
                                    <option value="2">Logistics Solutions Inc.</option>
                                    <option value="3">International Freight Ltd.</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Shipment ID</label>
                                <input type="text" class="form-control" id="shipmentId" placeholder="Enter shipment ID">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Amount</label>
                                <input type="number" class="form-control" id="invoiceAmount" step="0.01" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Currency</label>
                                <select class="form-select" id="currency">
                                    <option value="USD">USD</option>
                                    <option value="EUR">EUR</option>
                                    <option value="GBP">GBP</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Tax Rate (%)</label>
                                <input type="number" class="form-control" id="taxRate" value="10" step="0.1">
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Payment Terms</label>
                        <select class="form-select" id="paymentTerms">
                            <option value="Net 30">Net 30</option>
                            <option value="Net 15">Net 15</option>
                            <option value="Net 60">Net 60</option>
                            <option value="Due on Receipt">Due on Receipt</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Notes</label>
                        <textarea class="form-control" id="invoiceNotes" rows="3" placeholder="Additional notes or payment instructions"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-outline-primary" onclick="saveDraftInvoice()">Save Draft</button>
                <button type="button" class="btn btn-primary" onclick="createAndSendInvoice()">Create & Send</button>
            </div>
        </div>
    </div>
</div>

<script>
function createInvoice() {
    new bootstrap.Modal(document.getElementById('createInvoiceModal')).show();
}

function saveDraftInvoice() {
    const customer = document.getElementById('customerId').value;
    const amount = document.getElementById('invoiceAmount').value;
    
    if (!customer || !amount) {
        alert('Please fill in customer and amount fields');
        return;
    }
    
    alert('Invoice draft saved successfully!');
    bootstrap.Modal.getInstance(document.getElementById('createInvoiceModal')).hide();
    location.reload();
}

function createAndSendInvoice() {
    saveDraftInvoice();
    // In real implementation, this would also send the invoice
}

function filterInvoices(status) {
    const rows = document.querySelectorAll('.invoice-row');
    
    rows.forEach(row => {
        if (status === 'all' || row.dataset.status === status) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}

function viewInvoice(invoiceId) {
    alert('Viewing invoice: ' + invoiceId + '\n\nThis would open a detailed invoice view with all line items, payment history, and customer information.');
}

function sendInvoice(invoiceId) {
    if (confirm('Send this invoice to the customer?')) {
        alert('Invoice ' + invoiceId + ' sent successfully!\n\nCustomer will receive email notification with payment instructions.');
        location.reload();
    }
}

function recordPayment(invoiceId) {
    const amount = prompt('Enter payment amount:');
    if (amount) {
        alert('Payment of $' + amount + ' recorded for invoice ' + invoiceId);
        location.reload();
    }
}

function viewPaymentDetails(paymentId) {
    alert('Payment Details:\n\nPayment ID: ' + paymentId + '\nStatus: Completed\nProcessor: Stripe\nFee: $2.50');
}

function requestRefund(paymentId) {
    const reason = prompt('Enter refund reason:');
    if (reason) {
        alert('Refund request submitted for payment: ' + paymentId + '\n\nReason: ' + reason + '\n\nRefund will be processed within 3-5 business days.');
    }
}

function sendReminder(invoiceNumber) {
    alert('Payment reminder sent for invoice: ' + invoiceNumber + '\n\nCustomer will receive email notification about overdue payment.');
}

function sendRemindersBulk() {
    if (confirm('Send payment reminders to all customers with overdue invoices?')) {
        alert('Bulk payment reminders sent successfully!\n\n2 reminder emails sent to customers with overdue payments.');
    }
}

function scheduleCall(invoiceNumber) {
    alert('Call scheduled for invoice: ' + invoiceNumber + '\n\nReminder set for tomorrow at 10:00 AM to follow up on payment.');
}

function setPaymentPlan(invoiceNumber) {
    alert('Payment plan options for invoice: ' + invoiceNumber + '\n\n• 3 monthly installments\n• 6 monthly installments\n\nContact customer to discuss preferred option.');
}

function exportFinancialReport() {
    alert('Exporting financial report...\n\nReport includes:\n• Invoice summary\n• Payment history\n• A/R aging\n• Collection metrics\n\nDownload will start shortly.');
}
</script>

<style>
.aging-bucket {
    padding: 15px;
    border-radius: 8px;
    background-color: #f8f9fa;
    margin-bottom: 10px;
}

.aging-bucket h5 {
    margin-bottom: 5px;
    font-weight: bold;
}

.invoice-row[data-status="overdue"] {
    background-color: #fff5f5;
}

.invoice-row[data-status="paid"] {
    background-color: #f0fff4;
}
</style>
{% endblock %}