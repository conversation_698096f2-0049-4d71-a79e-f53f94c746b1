{% extends "base.html" %}
{% load translation_tags %}

{% block title %}Quote Details - {{ quote.quote_reference }} - Cloverics{% endblock %}

{% block content %}
<div class="container mt-4">
    <!-- Success/Error Messages -->
    <script>
        // Check URL parameters for success/error messages
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.get('sent') === 'true') {
            alert('Quote sent successfully to customer!');
        } else if (urlParams.get('updated') === 'true') {
            alert('Quote updated successfully!');
        } else if (urlParams.get('error') === 'send_failed') {
            alert('Failed to send quote. Please try again.');
        } else if (urlParams.get('error') === 'update_failed') {
            alert('Failed to update quote. Please try again.');
        }
    </script>
    
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">{% translate 'quote_details' %} - {{ quote.quote_reference }}</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                                                <h5>{% translate 'customer_information' %}</h5>
                    <p><strong>{% translate 'customer' %}:</strong> {{ quote.customer.company_name or quote.customer.email }}</p>
                    <p><strong>{% translate 'contact_info' %}:</strong> {{ quote.customer.email }}</p>
                        </div>
                        <div class="col-md-6">
                            <h5>{% translate 'quote_information' %}</h5>
                            <p><strong>{% translate 'quote_reference' %}:</strong> {{ quote.quote_reference }}</p>
                            <p><strong>{% translate 'status' %}:</strong> 
                                <span class="badge {% if quote.status == 'pending' %}badge-warning{% elif quote.status == 'quoted' %}badge-info{% elif quote.status == 'sent' %}badge-success{% elif quote.status == 'cancelled' %}badge-danger{% endif %}">
                                    {{ quote.status|title }}
                                </span>
                            </p>
                            <p><strong>{% translate 'quoted_price' %}:</strong> ${{ quote.quoted_price or 'TBD' }}</p>
                            <p><strong>{% translate 'created' %}:</strong> {{ quote.created_at.strftime('%Y-%m-%d %H:%M') }}</p>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h5>{% translate 'cargo_details' %}</h5>
                            <p><strong>{% translate 'weight' %}:</strong> {{ quote.cargo_weight }} kg</p>
                            <p><strong>{% translate 'dimensions' %}:</strong> {{ quote.cargo_dimensions }}</p>
                        </div>
                        <div class="col-md-6">
                            <h5>{% translate 'schedule' %}</h5>
                            <p><strong>{% translate 'pickup_date' %}:</strong> {{ quote.pickup_date }}</p>
                            <p><strong>{% translate 'delivery_date' %}:</strong> {{ quote.delivery_date }}</p>
                        </div>
                    </div>
                    
                    {% if quote.special_requirements %}
                    <hr>
                    <h5>{% translate 'special_requirements' %}</h5>
                    <p>{{ quote.special_requirements }}</p>
                    {% endif %}
                    
                    {% if quote.provider_response %}
                    <hr>
                    <h5>{% translate 'provider_response' %}</h5>
                    <p>{{ quote.provider_response }}</p>
                    {% endif %}
                    
                    <hr>
                    
                    <div class="text-right">
                        <a href="/logistics/quote-management" class="btn btn-secondary">{% translate 'back_to_quote_management' %}</a>
                        {% if quote.status != 'cancelled' %}
                        <button class="btn btn-primary" onclick="editQuote({{ quote.id }})">{% translate 'edit_quote' %}</button>
                        {% if quote.status != 'sent' %}
                        <button class="btn btn-success" onclick="sendQuote({{ quote.id }})">{% translate 'send_quote' %}</button>
                        {% endif %}
                        <button class="btn btn-danger" onclick="cancelQuote({{ quote.id }})">{% translate 'cancel_quote' %}</button>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function editQuote(quoteId) {
    // Redirect to edit form or show modal
    window.location.href = `/logistics/edit-quote/${quoteId}`;
}

function sendQuote(quoteId) {
    if (confirm('Are you sure you want to send this quote to the customer?')) {
        // Use GET request for better browser compatibility
        window.location.href = `/logistics/send-quote/${quoteId}/action`;
    }
}

function cancelQuote(quoteId) {
    const reason = prompt('Please provide a reason for cancelling this quote:');
    if (reason !== null) {
        const formData = new FormData();
        formData.append('reason', reason);
        
        fetch(`/logistics/cancel-quote/${quoteId}`, {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Quote cancelled successfully!');
                location.reload();
            } else {
                alert('Failed to cancel quote: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while cancelling the quote.');
        });
    }
}
</script>
{% endblock %}