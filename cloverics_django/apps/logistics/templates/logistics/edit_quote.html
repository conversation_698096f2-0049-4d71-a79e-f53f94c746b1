{% extends "base.html" %}

{% block title %}Edit Quote - {{ quote.quote_reference }} - Cloverics{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-warning text-white">
                    <h4 class="mb-0">Edit Quote - {{ quote.quote_reference }}</h4>
                </div>
                <div class="card-body">
                    <!-- Current Quote Information -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h5>Current Quote Details</h5>
                            <p><strong>Customer:</strong> {{ quote.customer.company_name or quote.customer.email }}</p>
                            <p><strong>Current Price:</strong> ${{ quote.quoted_price or 'TBD' }}</p>
                            <p><strong>Status:</strong> 
                                <span class="badge {% if quote.status == 'pending' %}badge-warning{% elif quote.status == 'quoted' %}badge-info{% elif quote.status == 'sent' %}badge-success{% elif quote.status == 'cancelled' %}badge-danger{% endif %}">
                                    {{ quote.status|title }}
                                </span>
                            </p>
                        </div>
                        <div class="col-md-6">
                            <h5>Shipment Details</h5>
                            <p><strong>Weight:</strong> {{ quote.cargo_weight }} kg</p>
                            <p><strong>Dimensions:</strong> {{ quote.cargo_dimensions }}</p>
                            <p><strong>Pickup Date:</strong> {{ quote.pickup_date }}</p>
                            <p><strong>Delivery Date:</strong> {{ quote.delivery_date }}</p>
                        </div>
                    </div>

                    <!-- Edit Form -->
                    <form action="/logistics/edit-quote/{{ quote.id }}" method="POST" id="editQuoteForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="quote_amount" class="form-label">Quote Amount ($) *</label>
                                    <input type="number" class="form-control" id="quote_amount" name="quote_amount" 
                                           value="{{ quote.quoted_price or '' }}" step="0.01" min="0" required>
                                    <div class="form-text">Enter the updated quote price</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="notes" class="form-label">Provider Notes</label>
                                    <textarea class="form-control" id="notes" name="notes" rows="3" 
                                              placeholder="Optional notes about the quote update">{{ quote.provider_response or '' }}</textarea>
                                </div>
                            </div>
                        </div>

                        {% if quote.special_requirements %}
                        <div class="row">
                            <div class="col-12">
                                <div class="alert alert-info">
                                    <strong>Customer Requirements:</strong> {{ quote.special_requirements }}
                                </div>
                            </div>
                        </div>
                        {% endif %}

                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <a href="/logistics/quote/{{ quote.id }}" class="btn btn-secondary">
                                        <i class="fas fa-arrow-left"></i> Cancel
                                    </a>
                                    <div>
                                        <button type="submit" class="btn btn-success">
                                            <i class="fas fa-save"></i> Update Quote
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Form validation
document.getElementById('editQuoteForm').addEventListener('submit', function(e) {
    const quoteAmount = document.getElementById('quote_amount').value;
    
    if (!quoteAmount || parseFloat(quoteAmount) <= 0) {
        e.preventDefault();
        alert('Please enter a valid quote amount greater than 0.');
        return false;
    }
    
    if (confirm('Are you sure you want to update this quote with the new pricing?')) {
        return true;
    } else {
        e.preventDefault();
        return false;
    }
});

// Auto-format currency input
document.getElementById('quote_amount').addEventListener('blur', function() {
    const value = parseFloat(this.value);
    if (!isNaN(value)) {
        this.value = value.toFixed(2);
    }
});
</script>
{% endblock %}