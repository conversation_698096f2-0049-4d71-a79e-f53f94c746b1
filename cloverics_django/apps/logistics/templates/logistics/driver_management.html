{% extends "base.html" %}

{% block title %}Driver Management - Cloverics{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Unified Driver Management</h1>
            <p class="text-muted">Create new drivers or assign existing unassigned drivers to your logistics company</p>
        </div>
        <div>
            <button class="btn btn-primary" onclick="refreshDriverData()">
                <i class="fas fa-sync-alt"></i> Refresh Data
            </button>
        </div>
    </div>

    <!-- Driver Management Tabs -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <ul class="nav nav-tabs card-header-tabs" id="driverTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="create-new-tab" data-bs-toggle="tab" data-bs-target="#create-new" type="button" role="tab" aria-controls="create-new" aria-selected="true">
                        <i class="fas fa-user-plus"></i> Create New Driver
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="assign-existing-tab" data-bs-toggle="tab" data-bs-target="#assign-existing" type="button" role="tab" aria-controls="assign-existing" aria-selected="false">
                        <i class="fas fa-users"></i> Assign Existing Driver
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="my-drivers-tab" data-bs-toggle="tab" data-bs-target="#my-drivers" type="button" role="tab" aria-controls="my-drivers" aria-selected="false">
                        <i class="fas fa-truck"></i> My Drivers
                    </button>
                </li>
            </ul>
        </div>
        <div class="card-body">
            <div class="tab-content" id="driverTabContent">
                        <!-- Create New Driver Tab -->
                        <div class="tab-pane fade show active" id="create-new" role="tabpanel">
                            <form id="createDriverForm">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h5 class="mb-3"><i class="fas fa-user"></i> Personal Information</h5>
                                        
                                        <div class="form-group mb-3">
                                            <label for="firstName" class="form-label">First Name *</label>
                                            <input type="text" class="form-control" id="firstName" name="first_name" required>
                                        </div>
                                        
                                        <div class="form-group mb-3">
                                            <label for="lastName" class="form-label">Last Name *</label>
                                            <input type="text" class="form-control" id="lastName" name="last_name" required>
                                        </div>
                                        
                                        <div class="form-group mb-3">
                                            <label for="email" class="form-label">Email Address *</label>
                                            <input type="email" class="form-control" id="email" name="email" required>
                                        </div>
                                        
                                        <div class="form-group mb-3">
                                            <label for="phoneNumber" class="form-label">Phone Number *</label>
                                            <input type="tel" class="form-control" id="phoneNumber" name="phone_number" required>
                                        </div>
                                        
                                        <div class="form-group mb-3">
                                            <label for="region" class="form-label">Region/Location</label>
                                            <input type="text" class="form-control" id="region" name="region" placeholder="City, Country">
                                        </div>
                                        
                                        <div class="form-group mb-3">
                                            <label for="emergencyContact" class="form-label">Emergency Contact</label>
                                            <input type="text" class="form-control" id="emergencyContact" name="emergency_contact" placeholder="Name and phone number">
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <h5 class="mb-3"><i class="fas fa-truck"></i> Vehicle Information</h5>
                                        
                                        <div class="form-group mb-3">
                                            <label for="vehicleType" class="form-label">Vehicle Type *</label>
                                            <select class="form-control" id="vehicleType" name="vehicle_type" required>
                                                <option value="">Select Vehicle Type</option>
                                                <option value="BOX_TRUCK">Box Truck</option>
                                                <option value="FLATBED">Flatbed Truck</option>
                                                <option value="REFRIGERATED">Refrigerated Truck</option>
                                                <option value="TANKER">Tanker Truck</option>
                                                <option value="VAN">Cargo Van</option>
                                            </select>
                                        </div>
                                        
                                        <div class="form-group mb-3">
                                            <label for="maxCapacityKg" class="form-label">Maximum Capacity (kg) *</label>
                                            <input type="number" class="form-control" id="maxCapacityKg" name="max_capacity_kg" min="100" max="50000" required>
                                        </div>
                                        
                                        <div class="form-group mb-3">
                                            <label for="licensePlate" class="form-label">License Plate Number *</label>
                                            <input type="text" class="form-control" id="licensePlate" name="license_plate" required>
                                        </div>
                                        
                                        <div class="form-group mb-3">
                                            <label for="driverLicense" class="form-label">Driver License Number</label>
                                            <input type="text" class="form-control" id="driverLicense" name="driver_license">
                                        </div>
                                        
                                        <div class="form-group mb-3">
                                            <label for="insuranceInfo" class="form-label">Insurance Information</label>
                                            <textarea class="form-control" id="insuranceInfo" name="insurance_info" rows="3" placeholder="Insurance provider and policy details"></textarea>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row mt-4">
                                    <div class="col-12">
                                        <div class="alert alert-info">
                                            <i class="fas fa-info-circle"></i> 
                                            <strong>Note:</strong> The driver will receive account credentials via email and can complete their profile setup including document verification.
                                        </div>
                                        
                                        <button type="button" class="btn btn-secondary me-2" onclick="resetCreateForm()">
                                            <i class="fas fa-undo"></i> Reset Form
                                        </button>
                                        <button type="submit" class="btn btn-success">
                                            <i class="fas fa-user-plus"></i> Create Driver Account
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>

                        <!-- Assign Existing Driver Tab -->
                        <div class="tab-pane fade" id="assign-existing" role="tabpanel">
                            <!-- Advanced Search & Filter Panel -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h6 class="mb-0"><i class="fas fa-search"></i> Search & Filter Drivers</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="form-group mb-3">
                                                <label for="searchDriverName" class="form-label">Driver Name</label>
                                                <input type="text" class="form-control" id="searchDriverName" placeholder="Search by name..." onkeyup="filterUnassignedDrivers()">
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group mb-3">
                                                <label for="filterVehicleType" class="form-label">Vehicle Type</label>
                                                <select class="form-control" id="filterVehicleType" onchange="filterUnassignedDrivers()">
                                                    <option value="">All Vehicle Types</option>
                                                    <option value="BOX_TRUCK">Box Truck</option>
                                                    <option value="FLATBED">Flatbed Truck</option>
                                                    <option value="REFRIGERATED">Refrigerated Truck</option>
                                                    <option value="TANKER">Tanker Truck</option>
                                                    <option value="VAN">Cargo Van</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group mb-3">
                                                <label for="filterVerificationStatus" class="form-label">Verification Status</label>
                                                <select class="form-control" id="filterVerificationStatus" onchange="filterUnassignedDrivers()">
                                                    <option value="">All Statuses</option>
                                                    <option value="approved">Approved</option>
                                                    <option value="pending">Pending</option>
                                                    <option value="rejected">Rejected</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="form-group mb-3">
                                                <label for="filterCapacityMin" class="form-label">Min Capacity (kg)</label>
                                                <input type="number" class="form-control" id="filterCapacityMin" placeholder="0" onchange="filterUnassignedDrivers()">
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group mb-3">
                                                <label for="filterRegion" class="form-label">Region</label>
                                                <input type="text" class="form-control" id="filterRegion" placeholder="City, Country" onkeyup="filterUnassignedDrivers()">
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group mb-3">
                                                <label for="sortBy" class="form-label">Sort By</label>
                                                <select class="form-control" id="sortBy" onchange="sortUnassignedDrivers()">
                                                    <option value="name">Name (A-Z)</option>
                                                    <option value="rating">Rating (High to Low)</option>
                                                    <option value="capacity">Capacity (High to Low)</option>
                                                    <option value="verification">Verification Status</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <button class="btn btn-secondary w-100" onclick="clearAllFilters()">
                                                <i class="fas fa-times"></i> Clear Filters
                                            </button>
                                        </div>
                                        <div class="col-md-6">
                                            <button class="btn btn-info w-100" onclick="loadUnassignedDrivers()">
                                                <i class="fas fa-refresh"></i> Refresh List
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Results Summary -->
                            <div class="alert alert-info mb-3" id="searchResultsSummary" style="display: none;">
                                <i class="fas fa-info-circle"></i> <span id="resultsText">0 drivers found</span>
                            </div>

                            <!-- Driver Selection View Toggle -->
                            <div class="btn-group mb-3" role="group">
                                <button type="button" class="btn btn-outline-primary active" id="cardViewBtn" onclick="toggleView('cards')">
                                    <i class="fas fa-th-large"></i> Card View
                                </button>
                                <button type="button" class="btn btn-outline-primary" id="listViewBtn" onclick="toggleView('list')">
                                    <i class="fas fa-list"></i> List View
                                </button>
                            </div>

                            <!-- Unassigned Drivers List -->
                            <div id="unassignedDriversList">
                                <div class="text-center py-4">
                                    <i class="fas fa-spinner fa-spin fa-2x text-muted"></i>
                                    <p class="mt-2 text-muted">Loading unassigned drivers...</p>
                                </div>
                            </div>
                        </div>

                        <!-- My Drivers Tab -->
                        <div class="tab-pane fade" id="my-drivers" role="tabpanel">
                            <div class="card">
                                <div class="card-header">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <h6 class="mb-0"><i class="fas fa-truck"></i> My Assigned Drivers</h6>
                                        <button class="btn btn-sm btn-primary" onclick="loadAssignedDrivers()">
                                            <i class="fas fa-refresh"></i> Refresh
                                        </button>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div id="assignedDriversList">
                                        <div class="text-center py-4">
                                            <i class="fas fa-spinner fa-spin fa-2x text-muted"></i>
                                            <p class="mt-2 text-muted">Loading assigned drivers...</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Driver Details Modal -->
        <div class="modal fade" id="driverDetailsModal" tabindex="-1" aria-labelledby="driverDetailsModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="driverDetailsModalLabel">Driver Details</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body" id="driverDetailsContent">
                        <!-- Driver details will be loaded here -->
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        <button type="button" class="btn btn-success" id="assignDriverBtn" onclick="assignSelectedDriver()">
                            <i class="fas fa-user-check"></i> Assign Driver
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let allUnassignedDrivers = [];
        let filteredDrivers = [];
        let selectedDriverId = null;
        let currentView = 'cards';

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadUnassignedDrivers();
            loadAssignedDrivers();
            
            // Set up form submission
            document.getElementById('createDriverForm').addEventListener('submit', handleCreateDriver);
        });

        // Driver Creation Functions
        async function handleCreateDriver(e) {
            e.preventDefault();
            
            const formData = new FormData(e.target);
            const driverData = Object.fromEntries(formData.entries());
            
            try {
                const response = await fetch('/api/driver/create-new', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(driverData)
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Driver Created Successfully!',
                        text: `Driver account created for ${driverData.first_name} ${driverData.last_name}. Login credentials sent via email.`,
                        confirmButtonColor: '#28a745'
                    });
                    resetCreateForm();
                    // Refresh the assigned drivers list
                    loadAssignedDrivers();
                } else {
                    throw new Error(result.detail || 'Failed to create driver');
                }
            } catch (error) {
                console.error('Error creating driver:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: error.message || 'Failed to create driver account. Please try again.',
                    confirmButtonColor: '#dc3545'
                });
            }
        }

        function resetCreateForm() {
            document.getElementById('createDriverForm').reset();
        }

        // Unassigned Drivers Functions
        async function loadUnassignedDrivers() {
            try {
                const response = await fetch('/api/driver/unassigned');
                const drivers = await response.json();
                
                allUnassignedDrivers = drivers;
                filteredDrivers = [...drivers];
                renderUnassignedDrivers();
                updateSearchSummary();
            } catch (error) {
                console.error('Error loading unassigned drivers:', error);
                document.getElementById('unassignedDriversList').innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i> Failed to load unassigned drivers. Please try again.
                    </div>
                `;
            }
        }

        function renderUnassignedDrivers() {
            const container = document.getElementById('unassignedDriversList');
            
            if (filteredDrivers.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-5">
                        <i class="fas fa-user-slash fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No drivers found</h5>
                        <p class="text-muted">Try adjusting your search criteria or refresh the list.</p>
                    </div>
                `;
                return;
            }

            if (currentView === 'cards') {
                renderDriverCards(container);
            } else {
                renderDriverList(container);
            }
        }

        function renderDriverCards(container) {
            const cardsHtml = filteredDrivers.map(driver => `
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card h-100 driver-card" onclick="selectDriver(${driver.id})">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <h6 class="card-title mb-0">${driver.first_name} ${driver.last_name}</h6>
                                <span class="badge ${getVerificationBadgeClass(driver.verification_status)}">
                                    ${driver.verification_status.charAt(0).toUpperCase() + driver.verification_status.slice(1)}
                                </span>
                            </div>
                            
                            <div class="mb-2">
                                <div class="d-flex align-items-center mb-1">
                                    <i class="fas fa-star text-warning me-1"></i>
                                    <span>${renderStarRating(driver.rating)}</span>
                                    <small class="text-muted ms-1">(${driver.completed_deliveries || 0} deliveries)</small>
                                </div>
                            </div>
                            
                            <div class="mb-2">
                                <small class="text-muted d-block">
                                    <i class="fas fa-truck me-1"></i> ${formatVehicleType(driver.vehicle_type)}
                                </small>
                                <small class="text-muted d-block">
                                    <i class="fas fa-weight me-1"></i> ${driver.max_capacity_kg.toLocaleString()} kg capacity
                                </small>
                                <small class="text-muted d-block">
                                    <i class="fas fa-map-marker-alt me-1"></i> ${driver.region || 'Location not specified'}
                                </small>
                            </div>
                            
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="badge ${driver.is_available ? 'bg-success' : 'bg-warning'}">
                                    <i class="fas ${driver.is_available ? 'fa-check-circle' : 'fa-clock'}"></i>
                                    ${driver.is_available ? 'Available' : 'Busy'}
                                </span>
                                <button class="btn btn-sm btn-outline-primary" onclick="event.stopPropagation(); showDriverDetails(${driver.id})">
                                    <i class="fas fa-eye"></i> Details
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `).join('');

            container.innerHTML = `<div class="row">${cardsHtml}</div>`;
        }

        function renderDriverList(container) {
            const listHtml = `
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Driver</th>
                                <th>Vehicle Type</th>
                                <th>Capacity</th>
                                <th>Rating</th>
                                <th>Status</th>
                                <th>Verification</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${filteredDrivers.map(driver => `
                                <tr onclick="selectDriver(${driver.id})" style="cursor: pointer;">
                                    <td>
                                        <div>
                                            <strong>${driver.first_name} ${driver.last_name}</strong>
                                            <br><small class="text-muted">${driver.region || 'Location not specified'}</small>
                                        </div>
                                    </td>
                                    <td>${formatVehicleType(driver.vehicle_type)}</td>
                                    <td>${driver.max_capacity_kg.toLocaleString()} kg</td>
                                    <td>
                                        ${renderStarRating(driver.rating)}
                                        <br><small class="text-muted">${driver.completed_deliveries || 0} deliveries</small>
                                    </td>
                                    <td>
                                        <span class="badge ${driver.is_available ? 'bg-success' : 'bg-warning'}">
                                            ${driver.is_available ? 'Available' : 'Busy'}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge ${getVerificationBadgeClass(driver.verification_status)}">
                                            ${driver.verification_status.charAt(0).toUpperCase() + driver.verification_status.slice(1)}
                                        </span>
                                    </td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-primary me-1" onclick="event.stopPropagation(); showDriverDetails(${driver.id})">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-sm btn-success" onclick="event.stopPropagation(); assignDriver(${driver.id})">
                                            <i class="fas fa-user-check"></i>
                                        </button>
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            `;

            container.innerHTML = listHtml;
        }

        // Filtering and Sorting Functions
        function filterUnassignedDrivers() {
            const nameFilter = document.getElementById('searchDriverName').value.toLowerCase();
            const vehicleFilter = document.getElementById('filterVehicleType').value;
            const statusFilter = document.getElementById('filterVerificationStatus').value;
            const capacityMin = parseInt(document.getElementById('filterCapacityMin').value) || 0;
            const regionFilter = document.getElementById('filterRegion').value.toLowerCase();

            filteredDrivers = allUnassignedDrivers.filter(driver => {
                const matchesName = !nameFilter || 
                    `${driver.first_name} ${driver.last_name}`.toLowerCase().includes(nameFilter);
                const matchesVehicle = !vehicleFilter || driver.vehicle_type === vehicleFilter;
                const matchesStatus = !statusFilter || driver.verification_status === statusFilter;
                const matchesCapacity = driver.max_capacity_kg >= capacityMin;
                const matchesRegion = !regionFilter || 
                    (driver.region && driver.region.toLowerCase().includes(regionFilter));

                return matchesName && matchesVehicle && matchesStatus && matchesCapacity && matchesRegion;
            });

            sortUnassignedDrivers();
            renderUnassignedDrivers();
            updateSearchSummary();
        }

        function sortUnassignedDrivers() {
            const sortBy = document.getElementById('sortBy').value;
            
            filteredDrivers.sort((a, b) => {
                switch (sortBy) {
                    case 'name':
                        return `${a.first_name} ${a.last_name}`.localeCompare(`${b.first_name} ${b.last_name}`);
                    case 'rating':
                        return (b.rating || 0) - (a.rating || 0);
                    case 'capacity':
                        return b.max_capacity_kg - a.max_capacity_kg;
                    case 'verification':
                        const statusOrder = { 'approved': 3, 'pending': 2, 'rejected': 1 };
                        return (statusOrder[b.verification_status] || 0) - (statusOrder[a.verification_status] || 0);
                    default:
                        return 0;
                }
            });
        }

        function clearAllFilters() {
            document.getElementById('searchDriverName').value = '';
            document.getElementById('filterVehicleType').value = '';
            document.getElementById('filterVerificationStatus').value = '';
            document.getElementById('filterCapacityMin').value = '';
            document.getElementById('filterRegion').value = '';
            document.getElementById('sortBy').value = 'name';
            
            filterUnassignedDrivers();
        }

        function updateSearchSummary() {
            const summaryElement = document.getElementById('searchResultsSummary');
            const resultsText = document.getElementById('resultsText');
            
            if (filteredDrivers.length !== allUnassignedDrivers.length) {
                summaryElement.style.display = 'block';
                resultsText.textContent = `${filteredDrivers.length} of ${allUnassignedDrivers.length} drivers found`;
            } else {
                summaryElement.style.display = 'none';
            }
        }

        function toggleView(view) {
            currentView = view;
            
            const cardBtn = document.getElementById('cardViewBtn');
            const listBtn = document.getElementById('listViewBtn');
            
            if (view === 'cards') {
                cardBtn.classList.add('active');
                listBtn.classList.remove('active');
            } else {
                listBtn.classList.add('active');
                cardBtn.classList.remove('active');
            }
            
            renderUnassignedDrivers();
        }

        // Driver Selection and Assignment
        function selectDriver(driverId) {
            selectedDriverId = driverId;
            
            // Update visual selection
            document.querySelectorAll('.driver-card').forEach(card => {
                card.classList.remove('border-primary');
            });
            
            // Find and highlight selected card
            event.currentTarget.classList.add('border-primary');
        }

        async function showDriverDetails(driverId) {
            try {
                const driver = allUnassignedDrivers.find(d => d.id === driverId);
                if (!driver) return;

                const detailsHtml = `
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="fas fa-user"></i> Personal Information</h6>
                            <table class="table table-sm">
                                <tr><td><strong>Name:</strong></td><td>${driver.first_name} ${driver.last_name}</td></tr>
                                <tr><td><strong>Email:</strong></td><td>${driver.email}</td></tr>
                                <tr><td><strong>Phone:</strong></td><td>${driver.phone_number}</td></tr>
                                <tr><td><strong>Region:</strong></td><td>${driver.region || 'Not specified'}</td></tr>
                                <tr><td><strong>Emergency Contact:</strong></td><td>${driver.emergency_contact || 'Not specified'}</td></tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-truck"></i> Vehicle Information</h6>
                            <table class="table table-sm">
                                <tr><td><strong>Vehicle Type:</strong></td><td>${formatVehicleType(driver.vehicle_type)}</td></tr>
                                <tr><td><strong>Max Capacity:</strong></td><td>${driver.max_capacity_kg.toLocaleString()} kg</td></tr>
                                <tr><td><strong>License Plate:</strong></td><td>${driver.license_plate}</td></tr>
                                <tr><td><strong>Driver License:</strong></td><td>${driver.driver_license || 'Not specified'}</td></tr>
                                <tr><td><strong>Insurance:</strong></td><td>${driver.insurance_info || 'Not specified'}</td></tr>
                            </table>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-12">
                            <h6><i class="fas fa-chart-line"></i> Performance Metrics</h6>
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <div class="h4 text-primary">${renderStarRating(driver.rating)}</div>
                                        <small class="text-muted">Rating</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <div class="h4 text-success">${driver.completed_deliveries || 0}</div>
                                        <small class="text-muted">Deliveries</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <span class="badge ${getVerificationBadgeClass(driver.verification_status)} h6">
                                            ${driver.verification_status.charAt(0).toUpperCase() + driver.verification_status.slice(1)}
                                        </span>
                                        <br><small class="text-muted">Status</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <span class="badge ${driver.is_available ? 'bg-success' : 'bg-warning'} h6">
                                            ${driver.is_available ? 'Available' : 'Busy'}
                                        </span>
                                        <br><small class="text-muted">Availability</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                document.getElementById('driverDetailsContent').innerHTML = detailsHtml;
                selectedDriverId = driverId;
                
                const modal = new bootstrap.Modal(document.getElementById('driverDetailsModal'));
                modal.show();
            } catch (error) {
                console.error('Error showing driver details:', error);
            }
        }

        async function assignSelectedDriver() {
            if (!selectedDriverId) {
                Swal.fire({
                    icon: 'warning',
                    title: 'No Driver Selected',
                    text: 'Please select a driver first.',
                    confirmButtonColor: '#ffc107'
                });
                return;
            }

            try {
                const response = await fetch('/api/driver/assign-existing', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ driver_id: selectedDriverId })
                });

                const result = await response.json();

                if (response.ok) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Driver Assigned Successfully!',
                        text: 'The driver has been assigned to your logistics company.',
                        confirmButtonColor: '#28a745'
                    });

                    // Close modal and refresh lists
                    bootstrap.Modal.getInstance(document.getElementById('driverDetailsModal')).hide();
                    loadUnassignedDrivers();
                    loadAssignedDrivers();
                    selectedDriverId = null;
                } else {
                    throw new Error(result.detail || 'Failed to assign driver');
                }
            } catch (error) {
                console.error('Error assigning driver:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'Assignment Failed',
                    text: error.message || 'Failed to assign driver. Please try again.',
                    confirmButtonColor: '#dc3545'
                });
            }
        }

        async function assignDriver(driverId) {
            selectedDriverId = driverId;
            await assignSelectedDriver();
        }

        // Assigned Drivers Functions
        async function loadAssignedDrivers() {
            try {
                const response = await fetch('/api/driver/assigned');
                const drivers = await response.json();
                
                renderAssignedDrivers(drivers);
            } catch (error) {
                console.error('Error loading assigned drivers:', error);
                document.getElementById('assignedDriversList').innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i> Failed to load assigned drivers. Please try again.
                    </div>
                `;
            }
        }

        function renderAssignedDrivers(drivers) {
            const container = document.getElementById('assignedDriversList');
            
            if (drivers.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-5">
                        <i class="fas fa-user-slash fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No Assigned Drivers</h5>
                        <p class="text-muted">You haven't assigned any drivers yet. Use the "Create New Driver" or "Assign Existing Driver" tabs to add drivers to your fleet.</p>
                    </div>
                `;
                return;
            }

            const driversHtml = `
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Driver</th>
                                <th>Vehicle</th>
                                <th>Status</th>
                                <th>Performance</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${drivers.map(driver => `
                                <tr>
                                    <td>
                                        <div>
                                            <strong>${driver.first_name} ${driver.last_name}</strong>
                                            <br><small class="text-muted">${driver.email}</small>
                                            <br><small class="text-muted">${driver.region || 'Location not specified'}</small>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            <strong>${formatVehicleType(driver.vehicle_type)}</strong>
                                            <br><small class="text-muted">${driver.max_capacity_kg.toLocaleString()} kg capacity</small>
                                            <br><small class="text-muted">${driver.license_plate}</small>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge ${driver.is_available ? 'bg-success' : 'bg-warning'} mb-1">
                                            ${driver.is_available ? 'Available' : 'On Delivery'}
                                        </span>
                                        <br>
                                        <span class="badge ${getVerificationBadgeClass(driver.verification_status)}">
                                            ${driver.verification_status.charAt(0).toUpperCase() + driver.verification_status.slice(1)}
                                        </span>
                                    </td>
                                    <td>
                                        <div>
                                            <div>${renderStarRating(driver.rating)}</div>
                                            <small class="text-muted">${driver.completed_deliveries || 0} completed deliveries</small>
                                        </div>
                                    </td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-primary me-1" onclick="viewDriverPerformance(${driver.id})">
                                            <i class="fas fa-chart-line"></i> Performance
                                        </button>
                                        <button class="btn btn-sm btn-outline-secondary" onclick="editDriver(${driver.id})">
                                            <i class="fas fa-edit"></i> Edit
                                        </button>
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            `;

            container.innerHTML = driversHtml;
        }

        // Utility Functions
        function formatVehicleType(type) {
            const types = {
                'BOX_TRUCK': 'Box Truck',
                'FLATBED': 'Flatbed Truck',
                'REFRIGERATED': 'Refrigerated Truck',
                'TANKER': 'Tanker Truck',
                'VAN': 'Cargo Van'
            };
            return types[type] || type;
        }

        function getVerificationBadgeClass(status) {
            const classes = {
                'approved': 'bg-success',
                'pending': 'bg-warning',
                'rejected': 'bg-danger'
            };
            return classes[status] || 'bg-secondary';
        }

        function renderStarRating(rating) {
            const stars = Math.round((rating || 0) * 2) / 2; // Round to nearest half
            let html = '';
            
            for (let i = 1; i <= 5; i++) {
                if (i <= stars) {
                    html += '<i class="fas fa-star text-warning"></i>';
                } else if (i - 0.5 <= stars) {
                    html += '<i class="fas fa-star-half-alt text-warning"></i>';
                } else {
                    html += '<i class="far fa-star text-warning"></i>';
                }
            }
            
            return html + ` <span class="text-muted">${(rating || 0).toFixed(1)}</span>`;
        }

        // Additional Functions
        async function viewDriverPerformance(driverId) {
            try {
                const response = await fetch(`/api/driver/performance-unified/${driverId}`);
                const performance = await response.json();
                
                Swal.fire({
                    title: 'Driver Performance',
                    html: `
                        <div class="text-left">
                            <div class="row">
                                <div class="col-6">
                                    <strong>Total Deliveries:</strong> ${performance.total_deliveries || 0}
                                </div>
                                <div class="col-6">
                                    <strong>Success Rate:</strong> ${performance.success_rate || 0}%
                                </div>
                            </div>
                            <div class="row mt-2">
                                <div class="col-6">
                                    <strong>Average Rating:</strong> ${(performance.average_rating || 0).toFixed(1)}/5
                                </div>
                                <div class="col-6">
                                    <strong>On-Time Rate:</strong> ${performance.on_time_rate || 0}%
                                </div>
                            </div>
                        </div>
                    `,
                    confirmButtonText: 'Close',
                    confirmButtonColor: '#6c757d'
                });
            } catch (error) {
                console.error('Error loading driver performance:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'Failed to load driver performance data.',
                    confirmButtonColor: '#dc3545'
                });
            }
        }

        async function editDriver(driverId) {
            Swal.fire({
                icon: 'info',
                title: 'Edit Driver',
                text: 'Driver editing functionality will be available in the next update.',
                confirmButtonColor: '#17a2b8'
            });
        }

        function refreshDriverData() {
            loadUnassignedDrivers();
            loadAssignedDrivers();
            
            Swal.fire({
                icon: 'success',
                title: 'Data Refreshed',
                text: 'Driver information has been updated.',
                timer: 1500,
                showConfirmButton: false
            });
        }
    

// Missing function implementations

// Reset Form Function
function resetForm() {
    document.getElementById('createDriverForm').reset();
    Swal.fire({
        icon: 'success',
        title: 'Form Reset',
        text: 'All form fields have been cleared.',
        timer: 1500,
        showConfirmButton: false
    });
}

// Create New Driver Function
async function createNewDriver() {
    const form = document.getElementById('createDriverForm');
    const formData = new FormData(form);
    
    const submitBtn = document.querySelector('#create-new .btn-primary');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Creating...';
    submitBtn.disabled = true;
    
    try {
        const response = await fetch('/api/driver/create-new', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        if (result.status === 'success') {
            Swal.fire({
                icon: 'success',
                title: 'Driver Created Successfully',
                text: `Driver ${result.driver_name} has been added to your fleet.`,
                confirmButtonColor: '#28a745'
            }).then(() => {
                form.reset();
                loadAssignedDrivers(); // Refresh assigned drivers list
            });
        } else {
            Swal.fire({
                icon: 'error',
                title: 'Error Creating Driver',
                text: result.message || 'Failed to create driver.',
                confirmButtonColor: '#dc3545'
            });
        }
    } catch (error) {
        console.error('Create driver error:', error);
        Swal.fire({
            icon: 'error',
            title: 'Network Error',
            text: 'Failed to create driver. Please try again.',
            confirmButtonColor: '#dc3545'
        });
    } finally {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }
}

// Filter Functions
function filterUnassignedDrivers() {
    const nameFilter = document.getElementById('searchDriverName').value.toLowerCase();
    const vehicleFilter = document.getElementById('filterVehicleType').value;
    const statusFilter = document.getElementById('filterVerificationStatus').value;
    const capacityMin = parseInt(document.getElementById('filterCapacityMin').value) || 0;
    const regionFilter = document.getElementById('filterRegion').value.toLowerCase();
    
    if (window.unassignedDriversData) {
        const filtered = window.unassignedDriversData.filter(driver => {
            const nameMatch = `${driver.first_name} ${driver.last_name}`.toLowerCase().includes(nameFilter);
            const vehicleMatch = !vehicleFilter || driver.vehicle_type === vehicleFilter;
            const statusMatch = !statusFilter || driver.verification_status === statusFilter;
            const capacityMatch = driver.max_capacity_kg >= capacityMin;
            const regionMatch = !regionFilter || driver.region.toLowerCase().includes(regionFilter);
            
            return nameMatch && vehicleMatch && statusMatch && capacityMatch && regionMatch;
        });
        
        renderUnassignedDrivers(filtered);
        updateSearchSummary(filtered.length, window.unassignedDriversData.length);
    }
}

// Clear Filters Function
function clearAllFilters() {
    document.getElementById('searchDriverName').value = '';
    document.getElementById('filterVehicleType').value = '';
    document.getElementById('filterVerificationStatus').value = '';
    document.getElementById('filterCapacityMin').value = '';
    document.getElementById('filterRegion').value = '';
    document.getElementById('sortBy').value = 'name';
    
    if (window.unassignedDriversData) {
        renderUnassignedDrivers(window.unassignedDriversData);
        updateSearchSummary(window.unassignedDriversData.length, window.unassignedDriversData.length);
    }
    
    Swal.fire({
        icon: 'success',
        title: 'Filters Cleared',
        text: 'All search filters have been reset.',
        timer: 1500,
        showConfirmButton: false
    });
}

// View Toggle Functions
function toggleView(viewType) {
    const cardView = document.getElementById('cardView');
    const listView = document.getElementById('listView');
    const cardBtn = document.querySelector('[onclick="toggleView(\'card\')"]');
    const listBtn = document.querySelector('[onclick="toggleView(\'list\')"]');
    
    if (viewType === 'card') {
        cardView.style.display = 'block';
        listView.style.display = 'none';
        cardBtn.classList.add('active');
        listBtn.classList.remove('active');
    } else {
        cardView.style.display = 'none';
        listView.style.display = 'block';
        cardBtn.classList.remove('active');
        listBtn.classList.add('active');
    }
}

// Update Search Summary
function updateSearchSummary(filtered, total) {
    const summaryElement = document.getElementById('searchSummary');
    if (summaryElement) {
        summaryElement.textContent = `Showing ${filtered} of ${total} drivers`;
    }
}
</script>

    <style>
        .driver-card {
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .driver-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .driver-card.border-primary {
            border-width: 2px !important;
        }
        
        .table tbody tr {
            transition: background-color 0.15s ease-in-out;
        }
        
        .table tbody tr:hover {
            background-color: rgba(0,123,255,0.05);
        }
        
        .badge {
            font-size: 0.75em;
        }
        
        .nav-tabs .nav-link {
            color: #495057;
        }
        
        .nav-tabs .nav-link.active {
            color: #007bff;
            font-weight: 500;
        }
    </style>
{% endblock %}