from django.db import models
from django.conf import settings
from django.core.validators import MinV<PERSON>ueValidator, MaxValueValidator
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
import uuid


class LogisticsProvider(models.Model):
    """Logistics Provider Profile extending the User model"""
    
    # Relationship to User
    user = models.OneToOneField(
        settings.AUTH_USER_MODEL, 
        on_delete=models.CASCADE,
        related_name='logistics_provider'
    )
    
    # Company Information
    company_registration_number = models.CharField(max_length=100, unique=True)
    license_number = models.CharField(max_length=100, unique=True)
    insurance_policy_number = models.CharField(max_length=100)
    tax_id = models.CharField(max_length=50)
    
    # Service Capabilities
    service_types = models.JSONField(default=list, help_text="List of service types offered")
    coverage_areas = models.JSONField(default=list, help_text="Geographic coverage areas")
    transport_modes = models.JSONField(default=list, help_text="Available transport modes")
    
    # Business Metrics
    total_shipments = models.PositiveIntegerField(default=0)
    completed_shipments = models.PositiveIntegerField(default=0)
    average_rating = models.DecimalField(max_digits=3, decimal_places=2, default=0.00)
    total_revenue = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)
    
    # Verification Status
    is_verified = models.BooleanField(default=False)
    verification_date = models.DateTimeField(null=True, blank=True)
    verification_documents = models.JSONField(default=list)
    
    # Subscription & Service Level
    subscription_tier = models.CharField(
        max_length=20,
        choices=[
            ('basic', _('Basic')),
            ('premium', _('Premium')),
            ('enterprise', _('Enterprise')),
        ],
        default='basic'
    )
    
    # Operational Settings
    auto_quote_enabled = models.BooleanField(default=False)
    instant_booking_enabled = models.BooleanField(default=False)
    min_shipment_value = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    max_shipment_value = models.DecimalField(max_digits=10, decimal_places=2, default=100000.00)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    last_active = models.DateTimeField(default=timezone.now)
    
    class Meta:
        db_table = 'logistics_providers'
        verbose_name = _('Logistics Provider')
        verbose_name_plural = _('Logistics Providers')
        indexes = [
            models.Index(fields=['is_verified', 'subscription_tier']),
            models.Index(fields=['average_rating', 'total_shipments']),
            models.Index(fields=['created_at']),
        ]
    
    def __str__(self):
        return f"{self.user.company_name or 'Unknown Company'} - {self.user.email}"
    
    @property
    def completion_rate(self):
        """Calculate completion rate percentage"""
        if self.total_shipments == 0:
            return 0
        return round((float(self.completed_shipments) / float(self.total_shipments)) * 100, 2)
    
    @property
    def is_premium(self):
        """Check if provider has premium subscription"""
        return self.subscription_tier in ['premium', 'enterprise']


class Route(models.Model):
    """Shipping routes offered by logistics providers"""
    
    provider = models.ForeignKey(
        LogisticsProvider,
        on_delete=models.CASCADE,
        related_name='routes'
    )
    
    # Route Information
    route_id = models.CharField(max_length=20, unique=True, default='')
    name = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    
    # Geographic Details
    origin_country = models.CharField(max_length=100)
    origin_city = models.CharField(max_length=100)
    origin_port = models.CharField(max_length=100, blank=True)
    
    destination_country = models.CharField(max_length=100)
    destination_city = models.CharField(max_length=100)
    destination_port = models.CharField(max_length=100, blank=True)
    
    # Transport Details
    transport_mode = models.CharField(
        max_length=20,
        choices=[
            ('road', _('Road Transport')),
            ('rail', _('Rail Transport')),
            ('sea', _('Sea Freight')),
            ('air', _('Air Freight')),
            ('multimodal', _('Multimodal')),
        ]
    )
    
    # Service Parameters
    estimated_transit_days = models.PositiveIntegerField()
    max_weight_kg = models.DecimalField(max_digits=10, decimal_places=2)
    max_volume_cbm = models.DecimalField(max_digits=10, decimal_places=2)
    
    # Pricing
    base_price_per_kg = models.DecimalField(max_digits=8, decimal_places=2)
    base_price_per_cbm = models.DecimalField(max_digits=8, decimal_places=2)
    minimum_charge = models.DecimalField(max_digits=10, decimal_places=2)
    fuel_surcharge_percent = models.DecimalField(max_digits=5, decimal_places=2, default=0.00)
    
    # Schedule
    available_days = models.JSONField(default=list, help_text="Days of week available")
    departure_frequency = models.CharField(
        max_length=20,
        choices=[
            ('daily', _('Daily')),
            ('weekly', _('Weekly')),
            ('biweekly', _('Bi-weekly')),
            ('monthly', _('Monthly')),
            ('on_demand', _('On Demand')),
        ],
        default='weekly'
    )
    
    # Status and Performance
    is_active = models.BooleanField(default=True)
    total_bookings = models.PositiveIntegerField(default=0)
    average_rating = models.DecimalField(max_digits=3, decimal_places=2, default=0.00)
    last_used = models.DateTimeField(null=True, blank=True)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'logistics_routes'
        verbose_name = _('Shipping Route')
        verbose_name_plural = _('Shipping Routes')
        unique_together = [
            ('provider', 'origin_city', 'destination_city', 'transport_mode')
        ]
        indexes = [
            models.Index(fields=['origin_country', 'destination_country']),
            models.Index(fields=['transport_mode', 'is_active']),
            models.Index(fields=['provider', 'is_active']),
            models.Index(fields=['average_rating', 'total_bookings']),
        ]
    
    def save(self, *args, **kwargs):
        if not self.route_id:
            self.route_id = f"RT{uuid.uuid4().hex[:8].upper()}"
        super().save(*args, **kwargs)
    
    def __str__(self):
        return f"{self.origin_city} → {self.destination_city} ({self.transport_mode})"
    
    @property
    def route_summary(self):
        """Get a summary of the route"""
        return f"{self.origin_city}, {self.origin_country} → {self.destination_city}, {self.destination_country}"


class Quote(models.Model):
    """Quote requests and responses between customers and logistics providers"""
    
    # Relationships
    customer = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='sent_quotes'
    )
    provider = models.ForeignKey(
        LogisticsProvider,
        on_delete=models.CASCADE,
        related_name='received_quotes'
    )
    route = models.ForeignKey(
        Route,
        on_delete=models.CASCADE,
        related_name='quotes',
        null=True, blank=True
    )
    
    # Quote Information
    quote_id = models.CharField(max_length=20, unique=True, default='')
    
    # Shipment Details
    origin_city = models.CharField(max_length=100)
    origin_country = models.CharField(max_length=100)
    destination_city = models.CharField(max_length=100)
    destination_country = models.CharField(max_length=100)
    
    cargo_type = models.CharField(max_length=100)
    cargo_description = models.TextField()
    total_weight_kg = models.DecimalField(max_digits=10, decimal_places=2)
    total_volume_cbm = models.DecimalField(max_digits=10, decimal_places=2)
    cargo_value = models.DecimalField(max_digits=12, decimal_places=2)
    
    # Service Requirements
    transport_mode = models.CharField(max_length=20)
    urgency_level = models.CharField(
        max_length=20,
        choices=[
            ('standard', _('Standard')),
            ('express', _('Express')),
            ('urgent', _('Urgent')),
        ],
        default='standard'
    )
    special_requirements = models.TextField(blank=True)
    insurance_required = models.BooleanField(default=False)
    
    # Dates
    preferred_pickup_date = models.DateField()
    latest_delivery_date = models.DateField()
    
    # Quote Response
    quoted_price = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True)
    estimated_transit_days = models.PositiveIntegerField(null=True, blank=True)
    valid_until = models.DateTimeField(null=True, blank=True)
    quote_notes = models.TextField(blank=True)
    
    # Status Management
    status = models.CharField(
        max_length=20,
        choices=[
            ('pending', _('Pending')),
            ('quoted', _('Quoted')),
            ('accepted', _('Accepted')),
            ('declined', _('Declined')),
            ('expired', _('Expired')),
        ],
        default='pending'
    )
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    quoted_at = models.DateTimeField(null=True, blank=True)
    response_deadline = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        db_table = 'logistics_quotes'
        verbose_name = _('Quote')
        verbose_name_plural = _('Quotes')
        indexes = [
            models.Index(fields=['customer', 'status']),
            models.Index(fields=['provider', 'status']),
            models.Index(fields=['status', 'created_at']),
            models.Index(fields=['valid_until']),
        ]
    
    def save(self, *args, **kwargs):
        if not self.quote_id:
            self.quote_id = f"QT{uuid.uuid4().hex[:8].upper()}"
        super().save(*args, **kwargs)
    
    def __str__(self):
        return f"Quote {self.quote_id} - {self.origin_city} → {self.destination_city}"
    
    @property
    def is_expired(self):
        """Check if quote has expired"""
        if self.valid_until:
            return timezone.now() > self.valid_until
        return False


class Fleet(models.Model):
    """Fleet vehicles managed by logistics providers"""
    
    provider = models.ForeignKey(
        LogisticsProvider,
        on_delete=models.CASCADE,
        related_name='fleet'
    )
    
    # Vehicle Information
    vehicle_id = models.CharField(max_length=20, unique=True, default='')
    license_plate = models.CharField(max_length=20, unique=True)
    vehicle_type = models.CharField(
        max_length=30,
        choices=[
            ('truck', _('Truck')),
            ('van', _('Van')),
            ('trailer', _('Trailer')),
            ('container', _('Container')),
            ('tanker', _('Tanker')),
        ]
    )
    
    # Specifications
    max_weight_capacity_kg = models.DecimalField(max_digits=10, decimal_places=2)
    max_volume_capacity_cbm = models.DecimalField(max_digits=10, decimal_places=2)
    year_manufactured = models.PositiveIntegerField()
    
    # Current Status
    status = models.CharField(
        max_length=20,
        choices=[
            ('available', _('Available')),
            ('in_transit', _('In Transit')),
            ('maintenance', _('Maintenance')),
            ('retired', _('Retired')),
        ],
        default='available'
    )
    
    current_location = models.CharField(max_length=200, blank=True)
    driver_assigned = models.CharField(max_length=100, blank=True)
    
    # Performance Metrics
    total_trips = models.PositiveIntegerField(default=0)
    total_distance_km = models.DecimalField(max_digits=12, decimal_places=2, default=0.00)
    fuel_efficiency_kmpl = models.DecimalField(max_digits=5, decimal_places=2, default=0.00)
    
    # Maintenance
    last_maintenance_date = models.DateField(null=True, blank=True)
    next_maintenance_due = models.DateField(null=True, blank=True)
    insurance_expiry = models.DateField()
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'logistics_fleet'
        verbose_name = _('Fleet Vehicle')
        verbose_name_plural = _('Fleet Vehicles')
        indexes = [
            models.Index(fields=['provider', 'status']),
            models.Index(fields=['vehicle_type', 'status']),
            models.Index(fields=['next_maintenance_due']),
        ]
    
    def save(self, *args, **kwargs):
        if not self.vehicle_id:
            self.vehicle_id = f"VH{uuid.uuid4().hex[:8].upper()}"
        super().save(*args, **kwargs)
    
    def __str__(self):
        return f"{self.vehicle_type} - {self.license_plate}"
    
    @property
    def maintenance_due_soon(self):
        """Check if maintenance is due within 30 days"""
        if self.next_maintenance_due:
            from datetime import timedelta
            days_until_maintenance = (self.next_maintenance_due - timezone.now().date()).days
            return days_until_maintenance <= 30
        return False