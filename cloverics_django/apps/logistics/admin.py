from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from .models import LogisticsProvider, Route, Quote, Fleet


@admin.register(LogisticsProvider)
class LogisticsProviderAdmin(admin.ModelAdmin):
    list_display = [
        'user', 'company_name', 'verification_status', 'subscription_tier', 
        'completion_rate_display', 'average_rating', 'total_shipments', 'created_at'
    ]
    list_filter = [
        'is_verified', 'subscription_tier', 'auto_quote_enabled', 
        'instant_booking_enabled', 'created_at'
    ]
    search_fields = [
        'user__email', 'user__company_name', 'company_registration_number', 
        'license_number', 'insurance_policy_number'
    ]
    readonly_fields = [
        'total_shipments', 'completed_shipments', 'average_rating', 
        'total_revenue', 'created_at', 'updated_at', 'verification_date'
    ]
    fieldsets = (
        ('Basic Information', {
            'fields': ('user', 'company_registration_number', 'license_number')
        }),
        ('Legal & Compliance', {
            'fields': ('insurance_policy_number', 'tax_id', 'verification_documents')
        }),
        ('Service Configuration', {
            'fields': ('service_types', 'coverage_areas', 'transport_modes')
        }),
        ('Business Metrics', {
            'fields': ('total_shipments', 'completed_shipments', 'average_rating', 'total_revenue'),
            'classes': ('collapse',)
        }),
        ('Verification Status', {
            'fields': ('is_verified', 'verification_date')
        }),
        ('Subscription & Settings', {
            'fields': (
                'subscription_tier', 'auto_quote_enabled', 'instant_booking_enabled',
                'min_shipment_value', 'max_shipment_value'
            )
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at', 'last_active'),
            'classes': ('collapse',)
        }),
    )
    
    def company_name(self, obj):
        return obj.user.company_name or 'N/A'
    company_name.short_description = 'Company Name'
    
    def verification_status(self, obj):
        if obj.is_verified:
            return format_html(
                '<span style="color: green; font-weight: bold;">✓ Verified</span>'
            )
        else:
            return format_html(
                '<span style="color: red; font-weight: bold;">✗ Unverified</span>'
            )
    verification_status.short_description = 'Status'
    
    def completion_rate_display(self, obj):
        rate = obj.completion_rate
        color = 'green' if rate >= 95 else 'orange' if rate >= 80 else 'red'
        return format_html(
            '<span style="color: {}; font-weight: bold;">{:.1f}%</span>',
            color, rate
        )
    completion_rate_display.short_description = 'Completion Rate'
    
    actions = ['verify_providers', 'unverify_providers']
    
    def verify_providers(self, request, queryset):
        count = queryset.update(is_verified=True)
        self.message_user(request, f'{count} providers verified successfully.')
    verify_providers.short_description = 'Mark selected providers as verified'
    
    def unverify_providers(self, request, queryset):
        count = queryset.update(is_verified=False)
        self.message_user(request, f'{count} providers unverified.')
    unverify_providers.short_description = 'Mark selected providers as unverified'


@admin.register(Route)
class RouteAdmin(admin.ModelAdmin):
    list_display = [
        'route_id', 'provider', 'route_summary', 'transport_mode', 
        'status_display', 'price_per_kg', 'total_bookings', 'rating_display', 'created_at'
    ]
    list_filter = [
        'transport_mode', 'is_active', 'departure_frequency', 
        'origin_country', 'destination_country', 'created_at'
    ]
    search_fields = [
        'route_id', 'name', 'origin_city', 'destination_city', 
        'origin_country', 'destination_country', 'provider__user__company_name'
    ]
    readonly_fields = [
        'route_id', 'total_bookings', 'average_rating', 'last_used', 
        'created_at', 'updated_at'
    ]
    fieldsets = (
        ('Basic Information', {
            'fields': ('provider', 'route_id', 'name', 'description')
        }),
        ('Geographic Details', {
            'fields': (
                ('origin_country', 'origin_city', 'origin_port'),
                ('destination_country', 'destination_city', 'destination_port')
            )
        }),
        ('Transport & Service', {
            'fields': (
                'transport_mode', 'estimated_transit_days', 
                ('max_weight_kg', 'max_volume_cbm')
            )
        }),
        ('Pricing', {
            'fields': (
                ('base_price_per_kg', 'base_price_per_cbm'),
                ('minimum_charge', 'fuel_surcharge_percent')
            )
        }),
        ('Schedule & Availability', {
            'fields': ('available_days', 'departure_frequency', 'is_active')
        }),
        ('Performance Metrics', {
            'fields': ('total_bookings', 'average_rating', 'last_used'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def status_display(self, obj):
        if obj.is_active:
            return format_html(
                '<span style="color: green; font-weight: bold;">● Active</span>'
            )
        else:
            return format_html(
                '<span style="color: gray; font-weight: bold;">● Inactive</span>'
            )
    status_display.short_description = 'Status'
    
    def price_per_kg(self, obj):
        return f"${obj.base_price_per_kg:.2f}"
    price_per_kg.short_description = 'Price/kg'
    
    def rating_display(self, obj):
        rating = obj.average_rating
        stars = '★' * int(rating) + '☆' * (5 - int(rating))
        return format_html(
            '<span title="{:.2f}/5.0">{}</span>',
            rating, stars
        )
    rating_display.short_description = 'Rating'
    
    actions = ['activate_routes', 'deactivate_routes']
    
    def activate_routes(self, request, queryset):
        count = queryset.update(is_active=True)
        self.message_user(request, f'{count} routes activated.')
    activate_routes.short_description = 'Activate selected routes'
    
    def deactivate_routes(self, request, queryset):
        count = queryset.update(is_active=False)
        self.message_user(request, f'{count} routes deactivated.')
    deactivate_routes.short_description = 'Deactivate selected routes'


@admin.register(Quote)
class QuoteAdmin(admin.ModelAdmin):
    list_display = [
        'quote_id', 'customer', 'provider', 'route_summary', 
        'status_display', 'quoted_price', 'urgency_level', 'created_at'
    ]
    list_filter = [
        'status', 'urgency_level', 'transport_mode', 'insurance_required',
        'created_at', 'quoted_at'
    ]
    search_fields = [
        'quote_id', 'customer__email', 'provider__user__company_name',
        'origin_city', 'destination_city', 'cargo_type'
    ]
    readonly_fields = [
        'quote_id', 'created_at', 'updated_at', 'quoted_at', 
        'response_deadline', 'is_expired'
    ]
    fieldsets = (
        ('Quote Information', {
            'fields': ('quote_id', 'customer', 'provider', 'route')
        }),
        ('Shipment Details', {
            'fields': (
                ('origin_city', 'origin_country'),
                ('destination_city', 'destination_country'),
                'cargo_type', 'cargo_description',
                ('total_weight_kg', 'total_volume_cbm', 'cargo_value')
            )
        }),
        ('Service Requirements', {
            'fields': (
                'transport_mode', 'urgency_level', 'insurance_required',
                'special_requirements'
            )
        }),
        ('Dates', {
            'fields': ('preferred_pickup_date', 'latest_delivery_date')
        }),
        ('Quote Response', {
            'fields': (
                'quoted_price', 'estimated_transit_days', 
                'valid_until', 'quote_notes'
            )
        }),
        ('Status & Tracking', {
            'fields': ('status', 'created_at', 'quoted_at', 'response_deadline')
        }),
    )
    
    def route_summary(self, obj):
        return f"{obj.origin_city} → {obj.destination_city}"
    route_summary.short_description = 'Route'
    
    def status_display(self, obj):
        status_colors = {
            'pending': 'orange',
            'quoted': 'blue',
            'accepted': 'green',
            'declined': 'red',
            'expired': 'gray'
        }
        color = status_colors.get(obj.status, 'black')
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color, obj.get_status_display()
        )
    status_display.short_description = 'Status'
    
    def is_expired(self, obj):
        return obj.is_expired
    is_expired.boolean = True
    is_expired.short_description = 'Expired'


@admin.register(Fleet)
class FleetAdmin(admin.ModelAdmin):
    list_display = [
        'vehicle_id', 'provider', 'vehicle_type', 'license_plate',
        'status_display', 'capacity_summary', 'maintenance_status', 'created_at'
    ]
    list_filter = [
        'vehicle_type', 'status', 'year_manufactured', 'created_at'
    ]
    search_fields = [
        'vehicle_id', 'license_plate', 'provider__user__company_name',
        'driver_assigned', 'current_location'
    ]
    readonly_fields = [
        'vehicle_id', 'total_trips', 'total_distance_km', 
        'created_at', 'updated_at', 'maintenance_due_soon'
    ]
    fieldsets = (
        ('Basic Information', {
            'fields': ('provider', 'vehicle_id', 'license_plate', 'vehicle_type')
        }),
        ('Specifications', {
            'fields': (
                ('max_weight_capacity_kg', 'max_volume_capacity_cbm'),
                'year_manufactured'
            )
        }),
        ('Current Status', {
            'fields': ('status', 'current_location', 'driver_assigned')
        }),
        ('Performance Metrics', {
            'fields': (
                ('total_trips', 'total_distance_km', 'fuel_efficiency_kmpl')
            ),
            'classes': ('collapse',)
        }),
        ('Maintenance', {
            'fields': (
                'last_maintenance_date', 'next_maintenance_due', 'insurance_expiry'
            )
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def status_display(self, obj):
        status_colors = {
            'available': 'green',
            'in_transit': 'blue',
            'maintenance': 'orange',
            'retired': 'gray'
        }
        color = status_colors.get(obj.status, 'black')
        return format_html(
            '<span style="color: {}; font-weight: bold;">● {}</span>',
            color, obj.get_status_display()
        )
    status_display.short_description = 'Status'
    
    def capacity_summary(self, obj):
        return f"{obj.max_weight_capacity_kg}kg / {obj.max_volume_capacity_cbm}m³"
    capacity_summary.short_description = 'Capacity'
    
    def maintenance_status(self, obj):
        if obj.maintenance_due_soon:
            return format_html(
                '<span style="color: red; font-weight: bold;">⚠ Due Soon</span>'
            )
        else:
            return format_html(
                '<span style="color: green;">✓ OK</span>'
            )
    maintenance_status.short_description = 'Maintenance'
    
    def maintenance_due_soon(self, obj):
        return obj.maintenance_due_soon
    maintenance_due_soon.boolean = True
    maintenance_due_soon.short_description = 'Maintenance Due'


# Customize admin site headers
admin.site.site_header = 'Cloverics Logistics Platform Administration'
admin.site.site_title = 'Cloverics Admin'
admin.site.index_title = 'Logistics Platform Management'