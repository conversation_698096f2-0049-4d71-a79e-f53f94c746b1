# Generated by Django 5.2.1 on 2025-08-07 12:45

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='LogisticsProvider',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('company_registration_number', models.CharField(max_length=100, unique=True)),
                ('license_number', models.CharField(max_length=100, unique=True)),
                ('insurance_policy_number', models.CharField(max_length=100)),
                ('tax_id', models.CharField(max_length=50)),
                ('service_types', models.JSONField(default=list, help_text='List of service types offered')),
                ('coverage_areas', models.JSONField(default=list, help_text='Geographic coverage areas')),
                ('transport_modes', models.J<PERSON><PERSON>ield(default=list, help_text='Available transport modes')),
                ('total_shipments', models.PositiveIntegerField(default=0)),
                ('completed_shipments', models.PositiveIntegerField(default=0)),
                ('average_rating', models.DecimalField(decimal_places=2, default=0.0, max_digits=3)),
                ('total_revenue', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('is_verified', models.BooleanField(default=False)),
                ('verification_date', models.DateTimeField(blank=True, null=True)),
                ('verification_documents', models.JSONField(default=list)),
                ('subscription_tier', models.CharField(choices=[('basic', 'Basic'), ('premium', 'Premium'), ('enterprise', 'Enterprise')], default='basic', max_length=20)),
                ('auto_quote_enabled', models.BooleanField(default=False)),
                ('instant_booking_enabled', models.BooleanField(default=False)),
                ('min_shipment_value', models.DecimalField(decimal_places=2, default=0.0, max_digits=10)),
                ('max_shipment_value', models.DecimalField(decimal_places=2, default=100000.0, max_digits=10)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('last_active', models.DateTimeField(default=django.utils.timezone.now)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='logistics_provider', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Logistics Provider',
                'verbose_name_plural': 'Logistics Providers',
                'db_table': 'logistics_providers',
            },
        ),
        migrations.CreateModel(
            name='Fleet',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('vehicle_id', models.CharField(default='', max_length=20, unique=True)),
                ('license_plate', models.CharField(max_length=20, unique=True)),
                ('vehicle_type', models.CharField(choices=[('truck', 'Truck'), ('van', 'Van'), ('trailer', 'Trailer'), ('container', 'Container'), ('tanker', 'Tanker')], max_length=30)),
                ('max_weight_capacity_kg', models.DecimalField(decimal_places=2, max_digits=10)),
                ('max_volume_capacity_cbm', models.DecimalField(decimal_places=2, max_digits=10)),
                ('year_manufactured', models.PositiveIntegerField()),
                ('status', models.CharField(choices=[('available', 'Available'), ('in_transit', 'In Transit'), ('maintenance', 'Maintenance'), ('retired', 'Retired')], default='available', max_length=20)),
                ('current_location', models.CharField(blank=True, max_length=200)),
                ('driver_assigned', models.CharField(blank=True, max_length=100)),
                ('total_trips', models.PositiveIntegerField(default=0)),
                ('total_distance_km', models.DecimalField(decimal_places=2, default=0.0, max_digits=12)),
                ('fuel_efficiency_kmpl', models.DecimalField(decimal_places=2, default=0.0, max_digits=5)),
                ('last_maintenance_date', models.DateField(blank=True, null=True)),
                ('next_maintenance_due', models.DateField(blank=True, null=True)),
                ('insurance_expiry', models.DateField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('provider', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='fleet', to='logistics.logisticsprovider')),
            ],
            options={
                'verbose_name': 'Fleet Vehicle',
                'verbose_name_plural': 'Fleet Vehicles',
                'db_table': 'logistics_fleet',
            },
        ),
        migrations.CreateModel(
            name='Route',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('route_id', models.CharField(default='', max_length=20, unique=True)),
                ('name', models.CharField(max_length=200)),
                ('description', models.TextField(blank=True)),
                ('origin_country', models.CharField(max_length=100)),
                ('origin_city', models.CharField(max_length=100)),
                ('origin_port', models.CharField(blank=True, max_length=100)),
                ('destination_country', models.CharField(max_length=100)),
                ('destination_city', models.CharField(max_length=100)),
                ('destination_port', models.CharField(blank=True, max_length=100)),
                ('transport_mode', models.CharField(choices=[('road', 'Road Transport'), ('rail', 'Rail Transport'), ('sea', 'Sea Freight'), ('air', 'Air Freight'), ('multimodal', 'Multimodal')], max_length=20)),
                ('estimated_transit_days', models.PositiveIntegerField()),
                ('max_weight_kg', models.DecimalField(decimal_places=2, max_digits=10)),
                ('max_volume_cbm', models.DecimalField(decimal_places=2, max_digits=10)),
                ('base_price_per_kg', models.DecimalField(decimal_places=2, max_digits=8)),
                ('base_price_per_cbm', models.DecimalField(decimal_places=2, max_digits=8)),
                ('minimum_charge', models.DecimalField(decimal_places=2, max_digits=10)),
                ('fuel_surcharge_percent', models.DecimalField(decimal_places=2, default=0.0, max_digits=5)),
                ('available_days', models.JSONField(default=list, help_text='Days of week available')),
                ('departure_frequency', models.CharField(choices=[('daily', 'Daily'), ('weekly', 'Weekly'), ('biweekly', 'Bi-weekly'), ('monthly', 'Monthly'), ('on_demand', 'On Demand')], default='weekly', max_length=20)),
                ('is_active', models.BooleanField(default=True)),
                ('total_bookings', models.PositiveIntegerField(default=0)),
                ('average_rating', models.DecimalField(decimal_places=2, default=0.0, max_digits=3)),
                ('last_used', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('provider', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='routes', to='logistics.logisticsprovider')),
            ],
            options={
                'verbose_name': 'Shipping Route',
                'verbose_name_plural': 'Shipping Routes',
                'db_table': 'logistics_routes',
            },
        ),
        migrations.CreateModel(
            name='Quote',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quote_id', models.CharField(default='', max_length=20, unique=True)),
                ('origin_city', models.CharField(max_length=100)),
                ('origin_country', models.CharField(max_length=100)),
                ('destination_city', models.CharField(max_length=100)),
                ('destination_country', models.CharField(max_length=100)),
                ('cargo_type', models.CharField(max_length=100)),
                ('cargo_description', models.TextField()),
                ('total_weight_kg', models.DecimalField(decimal_places=2, max_digits=10)),
                ('total_volume_cbm', models.DecimalField(decimal_places=2, max_digits=10)),
                ('cargo_value', models.DecimalField(decimal_places=2, max_digits=12)),
                ('transport_mode', models.CharField(max_length=20)),
                ('urgency_level', models.CharField(choices=[('standard', 'Standard'), ('express', 'Express'), ('urgent', 'Urgent')], default='standard', max_length=20)),
                ('special_requirements', models.TextField(blank=True)),
                ('insurance_required', models.BooleanField(default=False)),
                ('preferred_pickup_date', models.DateField()),
                ('latest_delivery_date', models.DateField()),
                ('quoted_price', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True)),
                ('estimated_transit_days', models.PositiveIntegerField(blank=True, null=True)),
                ('valid_until', models.DateTimeField(blank=True, null=True)),
                ('quote_notes', models.TextField(blank=True)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('quoted', 'Quoted'), ('accepted', 'Accepted'), ('declined', 'Declined'), ('expired', 'Expired')], default='pending', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('quoted_at', models.DateTimeField(blank=True, null=True)),
                ('response_deadline', models.DateTimeField(blank=True, null=True)),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sent_quotes', to=settings.AUTH_USER_MODEL)),
                ('provider', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='received_quotes', to='logistics.logisticsprovider')),
                ('route', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='quotes', to='logistics.route')),
            ],
            options={
                'verbose_name': 'Quote',
                'verbose_name_plural': 'Quotes',
                'db_table': 'logistics_quotes',
            },
        ),
        migrations.AddIndex(
            model_name='logisticsprovider',
            index=models.Index(fields=['is_verified', 'subscription_tier'], name='logistics_p_is_veri_f9e625_idx'),
        ),
        migrations.AddIndex(
            model_name='logisticsprovider',
            index=models.Index(fields=['average_rating', 'total_shipments'], name='logistics_p_average_64c698_idx'),
        ),
        migrations.AddIndex(
            model_name='logisticsprovider',
            index=models.Index(fields=['created_at'], name='logistics_p_created_a23e68_idx'),
        ),
        migrations.AddIndex(
            model_name='fleet',
            index=models.Index(fields=['provider', 'status'], name='logistics_f_provide_e44040_idx'),
        ),
        migrations.AddIndex(
            model_name='fleet',
            index=models.Index(fields=['vehicle_type', 'status'], name='logistics_f_vehicle_c4d2bb_idx'),
        ),
        migrations.AddIndex(
            model_name='fleet',
            index=models.Index(fields=['next_maintenance_due'], name='logistics_f_next_ma_d8800a_idx'),
        ),
        migrations.AddIndex(
            model_name='route',
            index=models.Index(fields=['origin_country', 'destination_country'], name='logistics_r_origin__767d52_idx'),
        ),
        migrations.AddIndex(
            model_name='route',
            index=models.Index(fields=['transport_mode', 'is_active'], name='logistics_r_transpo_ffd97f_idx'),
        ),
        migrations.AddIndex(
            model_name='route',
            index=models.Index(fields=['provider', 'is_active'], name='logistics_r_provide_271405_idx'),
        ),
        migrations.AddIndex(
            model_name='route',
            index=models.Index(fields=['average_rating', 'total_bookings'], name='logistics_r_average_5d7bbc_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='route',
            unique_together={('provider', 'origin_city', 'destination_city', 'transport_mode')},
        ),
        migrations.AddIndex(
            model_name='quote',
            index=models.Index(fields=['customer', 'status'], name='logistics_q_custome_eed140_idx'),
        ),
        migrations.AddIndex(
            model_name='quote',
            index=models.Index(fields=['provider', 'status'], name='logistics_q_provide_ff30f3_idx'),
        ),
        migrations.AddIndex(
            model_name='quote',
            index=models.Index(fields=['status', 'created_at'], name='logistics_q_status_9d3289_idx'),
        ),
        migrations.AddIndex(
            model_name='quote',
            index=models.Index(fields=['valid_until'], name='logistics_q_valid_u_9f10bb_idx'),
        ),
    ]
