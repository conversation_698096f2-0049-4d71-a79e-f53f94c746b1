# Generated by Django 5.2.1 on 2025-08-07 12:45

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('analytics', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='providerperformance',
            name='logistics_provider',
            field=models.ForeignKey(limit_choices_to={'user_type': 'LOGISTICS_PROVIDER'}, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
    ]
