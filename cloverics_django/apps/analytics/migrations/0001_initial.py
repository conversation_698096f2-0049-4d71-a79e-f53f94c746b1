# Generated by Django 5.2.1 on 2025-08-07 12:45

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='MarketInsight',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('insight_type', models.CharField(choices=[('cost_optimization', 'Cost Optimization'), ('demand_prediction', 'Demand Prediction'), ('market_intelligence', 'Market Intelligence'), ('competitive_analysis', 'Competitive Analysis'), ('capacity_planning', 'Capacity Planning')], max_length=30)),
                ('title', models.Char<PERSON>ield(max_length=255)),
                ('description', models.TextField()),
                ('impact_level', models.CharField(choices=[('low', 'Low'), ('medium', 'Medium'), ('high', 'High')], max_length=10)),
                ('confidence_score', models.DecimalField(decimal_places=2, max_digits=3)),
                ('recommended_action', models.TextField()),
                ('affected_routes', models.J<PERSON><PERSON>ield(default=list)),
                ('market_data', models.J<PERSON><PERSON>ield(default=dict)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('expires_at', models.DateTimeField()),
            ],
            options={
                'db_table': 'analytics_market_insights',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='PricePrediction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('route_origin', models.CharField(max_length=100)),
                ('route_destination', models.CharField(max_length=100)),
                ('cargo_type', models.CharField(choices=[('general', 'General Cargo'), ('hazardous', 'Hazardous Materials'), ('fragile', 'Fragile Items'), ('oversized', 'Oversized Cargo'), ('temperature_controlled', 'Temperature Controlled')], max_length=50)),
                ('current_price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('predicted_price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('price_change_percentage', models.DecimalField(decimal_places=3, max_digits=5)),
                ('confidence_score', models.DecimalField(decimal_places=2, max_digits=3)),
                ('prediction_factors', models.JSONField(default=list)),
                ('recommendations', models.JSONField(default=list)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('valid_until', models.DateTimeField()),
            ],
            options={
                'db_table': 'analytics_price_predictions',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ProviderPerformance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('performance_score', models.DecimalField(decimal_places=2, max_digits=4)),
                ('on_time_delivery_rate', models.DecimalField(decimal_places=2, max_digits=5)),
                ('customer_satisfaction', models.DecimalField(decimal_places=2, max_digits=3)),
                ('response_time_hours', models.DecimalField(decimal_places=2, max_digits=6)),
                ('completion_rate', models.DecimalField(decimal_places=2, max_digits=5)),
                ('pricing_competitiveness', models.DecimalField(decimal_places=2, max_digits=3)),
                ('total_shipments', models.IntegerField()),
                ('active_routes', models.IntegerField()),
                ('performance_trends', models.JSONField(default=dict)),
                ('analysis_date', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'db_table': 'analytics_provider_performance',
                'ordering': ['-analysis_date'],
            },
        ),
        migrations.CreateModel(
            name='RouteOptimization',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('route_origin', models.CharField(max_length=100)),
                ('route_destination', models.CharField(max_length=100)),
                ('optimization_method', models.CharField(choices=[('multi_modal', 'Multi-Modal'), ('consolidation', 'Consolidation'), ('direct', 'Direct Route'), ('hybrid', 'Hybrid Approach')], max_length=20)),
                ('efficiency_gain', models.DecimalField(decimal_places=2, max_digits=5)),
                ('cost_savings', models.DecimalField(decimal_places=2, max_digits=10)),
                ('time_savings_hours', models.DecimalField(decimal_places=2, max_digits=6)),
                ('fuel_savings_liters', models.DecimalField(decimal_places=2, max_digits=8)),
                ('optimization_details', models.JSONField(default=dict)),
                ('recommendations', models.JSONField(default=list)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'db_table': 'analytics_route_optimizations',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='MarketBenchmark',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('route_origin', models.CharField(max_length=100)),
                ('route_destination', models.CharField(max_length=100)),
                ('transport_type', models.CharField(max_length=50)),
                ('average_price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('median_price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('price_range_min', models.DecimalField(decimal_places=2, max_digits=10)),
                ('price_range_max', models.DecimalField(decimal_places=2, max_digits=10)),
                ('market_competitiveness', models.CharField(max_length=50)),
                ('trending_direction', models.CharField(max_length=20)),
                ('sample_size', models.IntegerField()),
                ('data_collection_date', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'db_table': 'analytics_market_benchmarks',
                'ordering': ['-data_collection_date'],
                'unique_together': {('route_origin', 'route_destination', 'transport_type', 'data_collection_date')},
            },
        ),
    ]
