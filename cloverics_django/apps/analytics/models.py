from django.db import models
from django.contrib.auth import get_user_model

User = get_user_model()

class PricePrediction(models.Model):
    CARGO_TYPES = [
        ('general', 'General Cargo'),
        ('hazardous', 'Hazardous Materials'),
        ('fragile', 'Fragile Items'),
        ('oversized', 'Oversized Cargo'),
        ('temperature_controlled', 'Temperature Controlled'),
    ]
    
    route_origin = models.CharField(max_length=100)
    route_destination = models.CharField(max_length=100)
    cargo_type = models.CharField(max_length=50, choices=CARGO_TYPES)
    current_price = models.DecimalField(max_digits=10, decimal_places=2)
    predicted_price = models.DecimalField(max_digits=10, decimal_places=2)
    price_change_percentage = models.DecimalField(max_digits=5, decimal_places=3)
    confidence_score = models.DecimalField(max_digits=3, decimal_places=2)
    prediction_factors = models.JSONField(default=list)  # List of factors affecting price
    recommendations = models.JSONField(default=list)  # AI recommendations
    created_at = models.DateTimeField(auto_now_add=True)
    valid_until = models.DateTimeField()
    
    class Meta:
        db_table = 'analytics_price_predictions'
        ordering = ['-created_at']

class RouteOptimization(models.Model):
    OPTIMIZATION_METHODS = [
        ('multi_modal', 'Multi-Modal'),
        ('consolidation', 'Consolidation'),
        ('direct', 'Direct Route'),
        ('hybrid', 'Hybrid Approach'),
    ]
    
    route_origin = models.CharField(max_length=100)
    route_destination = models.CharField(max_length=100)
    optimization_method = models.CharField(max_length=20, choices=OPTIMIZATION_METHODS)
    efficiency_gain = models.DecimalField(max_digits=5, decimal_places=2)  # Percentage
    cost_savings = models.DecimalField(max_digits=10, decimal_places=2)
    time_savings_hours = models.DecimalField(max_digits=6, decimal_places=2)
    fuel_savings_liters = models.DecimalField(max_digits=8, decimal_places=2)
    optimization_details = models.JSONField(default=dict)
    recommendations = models.JSONField(default=list)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'analytics_route_optimizations'
        ordering = ['-created_at']

class MarketInsight(models.Model):
    INSIGHT_TYPES = [
        ('cost_optimization', 'Cost Optimization'),
        ('demand_prediction', 'Demand Prediction'),
        ('market_intelligence', 'Market Intelligence'),
        ('competitive_analysis', 'Competitive Analysis'),
        ('capacity_planning', 'Capacity Planning'),
    ]
    
    IMPACT_LEVELS = [
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
    ]
    
    insight_type = models.CharField(max_length=30, choices=INSIGHT_TYPES)
    title = models.CharField(max_length=255)
    description = models.TextField()
    impact_level = models.CharField(max_length=10, choices=IMPACT_LEVELS)
    confidence_score = models.DecimalField(max_digits=3, decimal_places=2)
    recommended_action = models.TextField()
    affected_routes = models.JSONField(default=list)
    market_data = models.JSONField(default=dict)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField()
    
    class Meta:
        db_table = 'analytics_market_insights'
        ordering = ['-created_at']

class MarketBenchmark(models.Model):
    route_origin = models.CharField(max_length=100)
    route_destination = models.CharField(max_length=100)
    transport_type = models.CharField(max_length=50)
    average_price = models.DecimalField(max_digits=10, decimal_places=2)
    median_price = models.DecimalField(max_digits=10, decimal_places=2)
    price_range_min = models.DecimalField(max_digits=10, decimal_places=2)
    price_range_max = models.DecimalField(max_digits=10, decimal_places=2)
    market_competitiveness = models.CharField(max_length=50)
    trending_direction = models.CharField(max_length=20)  # up, down, stable
    sample_size = models.IntegerField()
    data_collection_date = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'analytics_market_benchmarks'
        ordering = ['-data_collection_date']
        unique_together = ['route_origin', 'route_destination', 'transport_type', 'data_collection_date']

class ProviderPerformance(models.Model):
    logistics_provider = models.ForeignKey(User, on_delete=models.CASCADE, limit_choices_to={'user_type': 'LOGISTICS_PROVIDER'})
    performance_score = models.DecimalField(max_digits=4, decimal_places=2)  # Out of 100
    on_time_delivery_rate = models.DecimalField(max_digits=5, decimal_places=2)
    customer_satisfaction = models.DecimalField(max_digits=3, decimal_places=2)
    response_time_hours = models.DecimalField(max_digits=6, decimal_places=2)
    completion_rate = models.DecimalField(max_digits=5, decimal_places=2)
    pricing_competitiveness = models.DecimalField(max_digits=3, decimal_places=2)
    total_shipments = models.IntegerField()
    active_routes = models.IntegerField()
    performance_trends = models.JSONField(default=dict)
    analysis_date = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'analytics_provider_performance'
        ordering = ['-analysis_date']