from django.contrib import admin
from .models import PricePrediction, RouteOptimization, MarketInsight, MarketBenchmark, ProviderPerformance

@admin.register(PricePrediction)
class PricePredictionAdmin(admin.ModelAdmin):
    list_display = ['route_origin', 'route_destination', 'cargo_type', 'current_price', 'predicted_price', 'confidence_score', 'created_at']
    list_filter = ['cargo_type', 'created_at', 'confidence_score']
    search_fields = ['route_origin', 'route_destination', 'cargo_type']
    ordering = ['-created_at']

@admin.register(RouteOptimization)
class RouteOptimizationAdmin(admin.ModelAdmin):
    list_display = ['route_origin', 'route_destination', 'optimization_method', 'efficiency_gain', 'cost_savings', 'created_at']
    list_filter = ['optimization_method', 'created_at']
    search_fields = ['route_origin', 'route_destination']
    ordering = ['-created_at']

@admin.register(MarketInsight)
class MarketInsightAdmin(admin.ModelAdmin):
    list_display = ['title', 'insight_type', 'impact_level', 'confidence_score', 'is_active', 'created_at', 'expires_at']
    list_filter = ['insight_type', 'impact_level', 'is_active', 'created_at']
    search_fields = ['title', 'description']
    ordering = ['-created_at']

@admin.register(MarketBenchmark)
class MarketBenchmarkAdmin(admin.ModelAdmin):
    list_display = ['route_origin', 'route_destination', 'transport_type', 'average_price', 'market_competitiveness', 'data_collection_date']
    list_filter = ['transport_type', 'market_competitiveness', 'data_collection_date']
    search_fields = ['route_origin', 'route_destination']
    ordering = ['-data_collection_date']

@admin.register(ProviderPerformance)
class ProviderPerformanceAdmin(admin.ModelAdmin):
    list_display = ['logistics_provider', 'performance_score', 'on_time_delivery_rate', 'customer_satisfaction', 'total_shipments', 'analysis_date']
    list_filter = ['performance_score', 'analysis_date']
    search_fields = ['logistics_provider__company_name', 'logistics_provider__email']
    ordering = ['-analysis_date']