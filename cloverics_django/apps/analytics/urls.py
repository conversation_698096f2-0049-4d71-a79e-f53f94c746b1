from django.urls import path
from . import views_django_implementation as views

app_name = 'analytics'

urlpatterns = [
    # Analytics Dashboard
    path('dashboard/', views.analytics_dashboard, name='dashboard'),
    
    # AI Analytics APIs
    path('api/price-prediction/', views.price_prediction_api, name='price_prediction_api'),
    path('api/route-optimization/', views.route_optimization_api, name='route_optimization_api'),
    path('api/market-insights/', views.market_insights_api, name='market_insights_api'),
    path('api/market-overview/', views.market_overview_api, name='market_overview_api'),
    path('api/provider-performance/', views.provider_performance_analytics, name='provider_performance_analytics'),
    
    # Final completion: Additional analytics endpoints
    path('reports/', views.analytics_reports, name='analytics_reports'),
    path('reports/<int:report_id>/', views.report_detail, name='report_detail'),
    path('data/export/', views.export_analytics_data, name='export_data'),
]