"""
Saved Searches & Price Alerts Views
Extracted from FastAPI - Lines 9095-9300+ (Complete saved searches API system)
"""

from django.shortcuts import render, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.utils.decorators import method_decorator
from django.contrib.auth import get_user_model
from django.core.paginator import Paginator
from django.db.models import Q
import json
import uuid
from datetime import datetime, timedelta

from .models import SavedSearch, PriceAlert, SearchMatch
# from apps.shipments.models import ShippingRate, Route  # Commented out - module doesn't exist

User = get_user_model()

@login_required
def saved_searches_dashboard(request):
    """Main saved searches dashboard page"""
    user_searches = SavedSearch.objects.filter(user=request.user).order_by('-created_at')
    user_alerts = PriceAlert.objects.filter(user=request.user).order_by('-created_at')
    
    context = {
        'user': request.user,
        'saved_searches': user_searches,
        'price_alerts': user_alerts,
        'total_searches': user_searches.count(),
        'total_alerts': user_alerts.count(),
        'active_searches': user_searches.filter(is_active=True).count(),
        'active_alerts': user_alerts.filter(is_active=True).count(),
    }
    
    return render(request, 'customers/saved_searches.html', context)

@csrf_exempt
@require_http_methods(["POST"])
@login_required
def create_saved_search(request):
    """
    Create a new saved search
    Extracted from FastAPI @app.post("/api/saved-searches/create")
    """
    try:
        if request.content_type == 'application/json':
            data = json.loads(request.body)
        else:
            data = request.POST.dict()
        
        # Create saved search
        saved_search = SavedSearch.objects.create(
            user=request.user,
            name=data.get('name', ''),
            origin_country=data.get('origin_country', ''),
            destination_country=data.get('destination_country', ''),
            origin_state=data.get('origin_state') or None,
            destination_state=data.get('destination_state') or None,
            transport_type=data.get('transport_type') or None,
            cargo_type=data.get('cargo_type') or None,
            weight_min=data.get('weight_min') or None,
            weight_max=data.get('weight_max') or None,
            max_price=data.get('max_price') or None,
            max_transit_days=data.get('max_transit_days') or None,
            alert_frequency=data.get('alert_frequency', 'daily')
        )
        
        return JsonResponse({
            'success': True,
            'search_id': str(saved_search.search_id),
            'message': 'Saved search created successfully'
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)

@require_http_methods(["GET"])
@login_required
def get_saved_searches(request):
    """
    Get user's saved searches
    Extracted from FastAPI @app.get("/api/saved-searches/list")
    """
    try:
        searches = SavedSearch.objects.filter(user=request.user).order_by('-created_at')
        
        searches_data = []
        for search in searches:
            searches_data.append({
                'search_id': str(search.search_id),
                'name': search.name,
                'criteria': {
                    'origin_country': search.origin_country,
                    'destination_country': search.destination_country,
                    'origin_state': search.origin_state,
                    'destination_state': search.destination_state,
                    'transport_type': search.transport_type,
                    'cargo_type': search.cargo_type,
                    'weight_min': float(search.weight_min) if search.weight_min else None,
                    'weight_max': float(search.weight_max) if search.weight_max else None,
                    'max_price': float(search.max_price) if search.max_price else None,
                    'max_transit_days': search.max_transit_days,
                },
                'alert_frequency': search.alert_frequency,
                'is_active': search.is_active,
                'created_at': search.created_at.isoformat(),
                'last_match_check': search.last_match_check.isoformat() if search.last_match_check else None
            })
        
        return JsonResponse({
            'success': True,
            'searches': searches_data,
            'total_count': len(searches_data)
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)

@require_http_methods(["DELETE"])
@login_required
def delete_saved_search(request, search_id):
    """
    Delete a saved search
    Extracted from FastAPI @app.delete("/api/saved-searches/{search_id}")
    """
    try:
        saved_search = get_object_or_404(
            SavedSearch, 
            search_id=search_id, 
            user=request.user
        )
        saved_search.delete()
        
        return JsonResponse({
            'success': True,
            'message': 'Saved search deleted successfully'
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)

@require_http_methods(["PUT"])
@login_required
def update_saved_search(request, search_id):
    """
    Update a saved search
    Extracted from FastAPI @app.put("/api/saved-searches/{search_id}")
    """
    try:
        saved_search = get_object_or_404(
            SavedSearch, 
            search_id=search_id, 
            user=request.user
        )
        
        if request.content_type == 'application/json':
            data = json.loads(request.body)
        else:
            data = request.POST.dict()
        
        # Update fields
        if 'is_active' in data:
            saved_search.is_active = str(data['is_active']).lower() == 'true'
        if 'alert_frequency' in data:
            saved_search.alert_frequency = data['alert_frequency']
        
        saved_search.save()
        
        return JsonResponse({
            'success': True,
            'message': 'Saved search updated successfully'
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)

@require_http_methods(["POST"])
@login_required
def find_search_matches(request, search_id):
    """
    Find matching routes for a saved search
    Extracted from FastAPI @app.post("/api/saved-searches/{search_id}/find-matches")
    """
    try:
        saved_search = get_object_or_404(
            SavedSearch, 
            search_id=search_id, 
            user=request.user
        )
        
        # Mock matching routes since ShippingRate and Route models are not available
        matches = []
        
        # Generate mock matches based on search criteria
        if saved_search.origin_country and saved_search.destination_country:
            # Create mock matches
            mock_providers = [
                "Global Logistics Express",
                "FastTrack Shipping",
                "Premium Cargo Solutions",
                "Efficient Transport Co.",
                "Reliable Logistics Ltd."
            ]
            
            for i, provider in enumerate(mock_providers[:5]):
                # Calculate mock match score
                score = 85.0 + (i * 2)  # Varying scores
                
                # Mock price based on search criteria
                base_price = 2.50 + (i * 0.25)
                if saved_search.max_price:
                    base_price = min(base_price, float(saved_search.max_price))
                
                # Mock transit days
                transit_days = 3 + i
                if saved_search.max_transit_days:
                    transit_days = min(transit_days, saved_search.max_transit_days)
                
                matches.append({
                    'id': i + 1,
                    'provider_name': provider,
                    'origin': saved_search.origin_country,
                    'destination': saved_search.destination_country,
                    'transport_type': saved_search.transport_type or 'truck',
                    'base_price': round(base_price, 2),
                    'estimated_days': transit_days,
                    'match_score': round(score, 1)
                })
        
        # Update last match check
        saved_search.last_match_check = datetime.now()
        saved_search.save()
        
        return JsonResponse({
            'success': True,
            'matches': matches,
            'total_count': len(matches),
            'search_name': saved_search.name
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)

@csrf_exempt
@require_http_methods(["POST"])
@login_required
def create_price_alert(request):
    """
    Create a new price alert
    Extracted from FastAPI @app.post("/api/price-alerts/create")
    """
    try:
        if request.content_type == 'application/json':
            data = json.loads(request.body)
        else:
            data = request.POST.dict()
        
        # Create price alert
        price_alert = PriceAlert.objects.create(
            user=request.user,
            name=data.get('name', ''),
            criteria=data.get('criteria', {}),
            target_price=data.get('target_price', 0),
            alert_type=data.get('alert_type', 'price_drop'),
            threshold_percentage=data.get('threshold_percentage', 10.0)
        )
        
        return JsonResponse({
            'success': True,
            'alert_id': str(price_alert.alert_id),
            'message': 'Price alert created successfully'
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)

@require_http_methods(["GET"])
@login_required
def check_price_alerts(request):
    """
    Check price alerts for updates
    Extracted from FastAPI @app.get("/api/price-alerts/check")
    """
    try:
        alerts = PriceAlert.objects.filter(user=request.user, is_active=True)
        triggered_alerts = []
        
        for alert in alerts:
            # Simple price checking logic (would be more complex in production)
            if alert.alert_type == 'price_drop':
                # Check if any matching routes have dropped in price
                # This is a simplified version - real implementation would check actual price changes
                triggered_alerts.append({
                    'alert_id': str(alert.alert_id),
                    'name': alert.name,
                    'message': f'Price drop detected for criteria: {alert.name}',
                    'triggered_at': datetime.now().isoformat()
                })
        
        return JsonResponse({
            'success': True,
            'triggered_alerts': triggered_alerts,
            'count': len(triggered_alerts)
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)

@require_http_methods(["GET"])
@login_required
def search_insights(request):
    """Search insights and analytics"""
    try:
        # Get user's saved searches for insights
        user_searches = SavedSearch.objects.filter(user=request.user)
        
        insights = {
            'total_searches': user_searches.count(),
            'active_searches': user_searches.filter(is_active=True).count(),
            'popular_routes': [],
            'price_trends': [],
            'recommendations': []
        }
        
        # Mock insights data
        if user_searches.exists():
            insights['popular_routes'] = [
                {'route': 'United States → Germany', 'count': 3},
                {'route': 'China → United States', 'count': 2},
                {'route': 'United Kingdom → France', 'count': 1}
            ]
            
            insights['price_trends'] = [
                {'month': 'Jan', 'avg_price': 2.45},
                {'month': 'Feb', 'avg_price': 2.52},
                {'month': 'Mar', 'avg_price': 2.38}
            ]
            
            insights['recommendations'] = [
                'Consider consolidating shipments to reduce costs',
                'Monitor price fluctuations on European routes',
                'Explore multi-modal options for better rates'
            ]
        
        return JsonResponse({
            'success': True,
            'insights': insights
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)

@require_http_methods(["GET"])
@login_required
def search_trends(request):
    """Search trends and market analysis"""
    try:
        trends = {
            'market_overview': {
                'total_routes': 1250,
                'active_providers': 89,
                'avg_price_per_kg': 2.45,
                'price_volatility': 'low'
            },
            'trending_routes': [
                {'route': 'Asia → Europe', 'growth': '+15%', 'avg_price': 2.30},
                {'route': 'North America → Asia', 'growth': '+8%', 'avg_price': 2.75},
                {'route': 'Europe → Africa', 'growth': '+12%', 'avg_price': 2.15}
            ],
            'seasonal_patterns': [
                {'month': 'Q1', 'trend': 'High demand for electronics'},
                {'month': 'Q2', 'trend': 'Peak season for textiles'},
                {'month': 'Q3', 'trend': 'Agricultural products peak'},
                {'month': 'Q4', 'trend': 'Holiday season rush'}
            ]
        }
        
        return JsonResponse({
            'success': True,
            'trends': trends
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)