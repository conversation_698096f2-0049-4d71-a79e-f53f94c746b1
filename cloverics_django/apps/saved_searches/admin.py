"""
Saved Searches & Price Alerts Admin Configuration
"""

from django.contrib import admin
from .models import SavedSearch, PriceAlert, SearchMatch

@admin.register(SavedSearch)
class SavedSearchAdmin(admin.ModelAdmin):
    list_display = ['name', 'user', 'origin_country', 'destination_country', 'is_active', 'alert_frequency', 'created_at']
    list_filter = ['is_active', 'alert_frequency', 'transport_type', 'created_at']
    search_fields = ['name', 'user__email', 'origin_country', 'destination_country']
    readonly_fields = ['search_id', 'created_at', 'updated_at']
    ordering = ['-created_at']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('search_id', 'user', 'name', 'is_active')
        }),
        ('Search Criteria', {
            'fields': ('origin_country', 'destination_country', 'origin_state', 'destination_state', 
                      'transport_type', 'cargo_type')
        }),
        ('Filters', {
            'fields': ('weight_min', 'weight_max', 'max_price', 'max_transit_days')
        }),
        ('Alert Settings', {
            'fields': ('alert_frequency', 'last_match_check')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at')
        }),
    )

@admin.register(PriceAlert)
class PriceAlertAdmin(admin.ModelAdmin):
    list_display = ['name', 'user', 'alert_type', 'target_price', 'is_active', 'triggered_count', 'created_at']
    list_filter = ['is_active', 'alert_type', 'created_at']
    search_fields = ['name', 'user__email']
    readonly_fields = ['alert_id', 'triggered_count', 'last_triggered', 'created_at', 'updated_at']
    ordering = ['-created_at']

@admin.register(SearchMatch)
class SearchMatchAdmin(admin.ModelAdmin):
    list_display = ['saved_search', 'provider_name', 'origin_city', 'destination_city', 'match_score', 'is_new', 'match_date']
    list_filter = ['is_new', 'user_viewed', 'transport_type', 'match_date']
    search_fields = ['saved_search__name', 'provider_name', 'origin_city', 'destination_city']
    readonly_fields = ['match_date']
    ordering = ['-match_date']