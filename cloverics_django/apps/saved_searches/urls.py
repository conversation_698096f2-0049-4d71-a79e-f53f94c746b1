"""
Saved Searches & Price Alerts URL Configuration
Extracted from FastAPI saved searches API endpoints
"""

from django.urls import path
from . import views

app_name = 'saved_searches'

urlpatterns = [
    # Dashboard
    path('dashboard/', views.saved_searches_dashboard, name='dashboard'),
    
    # Saved Searches API (extracted from FastAPI)
    path('api/create/', views.create_saved_search, name='create'),
    path('api/list/', views.get_saved_searches, name='list'),
    path('api/<uuid:search_id>/delete/', views.delete_saved_search, name='delete'),
    path('api/<uuid:search_id>/update/', views.update_saved_search, name='update'),
    path('api/<uuid:search_id>/find-matches/', views.find_search_matches, name='find_matches'),
    
    # Price Alerts API (extracted from FastAPI)
    path('api/alerts/create/', views.create_price_alert, name='create_alert'),
    path('api/alerts/check/', views.check_price_alerts, name='check_alerts'),
    
    # Final completion: Additional search endpoints
    path('insights/', views.search_insights, name='search_insights'),
    path('trends/', views.search_trends, name='search_trends'),
]