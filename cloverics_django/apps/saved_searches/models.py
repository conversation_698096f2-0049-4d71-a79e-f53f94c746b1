"""
Saved Searches & Price Alerts Models
Extracted from FastAPI - Lines 9095-9300+ (Saved searches system)
"""

from django.db import models
from django.contrib.auth import get_user_model
import uuid
import json

User = get_user_model()

class SavedSearch(models.Model):
    """
    Customer saved search criteria with automated alerts
    Extracted from FastAPI saved_search_manager functionality
    """
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='saved_searches')
    search_id = models.UUIDField(default=uuid.uuid4, unique=True)
    name = models.CharField(max_length=200)
    
    # Search criteria
    origin_country = models.CharField(max_length=100)
    destination_country = models.CharField(max_length=100)
    origin_state = models.Char<PERSON>ield(max_length=100, blank=True, null=True)
    destination_state = models.CharField(max_length=100, blank=True, null=True)
    transport_type = models.CharField(max_length=50, blank=True, null=True)
    cargo_type = models.Char<PERSON>ield(max_length=100, blank=True, null=True)
    
    # Weight and price filters
    weight_min = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True)
    weight_max = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True)
    max_price = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True)
    max_transit_days = models.IntegerField(blank=True, null=True)
    
    # Alert settings
    alert_frequency = models.CharField(
        max_length=20,
        choices=[
            ('instant', 'Instant'),
            ('daily', 'Daily'),
            ('weekly', 'Weekly'),
            ('monthly', 'Monthly')
        ],
        default='daily'
    )
    is_active = models.BooleanField(default=True)
    last_match_check = models.DateTimeField(blank=True, null=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'saved_searches'
        indexes = [
            models.Index(fields=['user', 'is_active']),
            models.Index(fields=['origin_country', 'destination_country']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f"{self.name} - {self.origin_country} → {self.destination_country}"

class PriceAlert(models.Model):
    """
    Price monitoring alerts for specific routes or criteria
    Extracted from FastAPI price_alert_manager functionality
    """
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='price_alerts')
    alert_id = models.UUIDField(default=uuid.uuid4, unique=True)
    name = models.CharField(max_length=200)
    
    # Alert criteria (stored as JSON for flexibility)
    criteria = models.JSONField(default=dict)
    target_price = models.DecimalField(max_digits=10, decimal_places=2)
    current_price = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True)
    
    # Alert settings
    alert_type = models.CharField(
        max_length=20,
        choices=[
            ('price_drop', 'Price Drop'),
            ('new_route', 'New Route'),
            ('capacity_available', 'Capacity Available')
        ],
        default='price_drop'
    )
    threshold_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=10.0)
    is_active = models.BooleanField(default=True)
    
    # Tracking
    last_checked = models.DateTimeField(blank=True, null=True)
    triggered_count = models.IntegerField(default=0)
    last_triggered = models.DateTimeField(blank=True, null=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'price_alerts'
        indexes = [
            models.Index(fields=['user', 'is_active']),
            models.Index(fields=['alert_type']),
            models.Index(fields=['last_checked']),
        ]

    def __str__(self):
        return f"{self.name} - Target: ${self.target_price}"

class SearchMatch(models.Model):
    """
    Matching routes found for saved searches
    Extracted from FastAPI search matching functionality
    """
    saved_search = models.ForeignKey(SavedSearch, on_delete=models.CASCADE, related_name='matches')
    route_id = models.IntegerField()  # Reference to shipping route
    match_score = models.DecimalField(max_digits=5, decimal_places=2)  # 0-100 scoring
    
    # Route details (denormalized for performance)
    provider_name = models.CharField(max_length=200)
    origin_city = models.CharField(max_length=100)
    destination_city = models.CharField(max_length=100)
    transport_type = models.CharField(max_length=50)
    price_per_kg = models.DecimalField(max_digits=8, decimal_places=2)
    estimated_days = models.IntegerField()
    
    # Match tracking
    is_new = models.BooleanField(default=True)
    user_viewed = models.BooleanField(default=False)
    match_date = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'search_matches'
        indexes = [
            models.Index(fields=['saved_search', 'is_new']),
            models.Index(fields=['match_score']),
            models.Index(fields=['match_date']),
        ]
        unique_together = ['saved_search', 'route_id']

    def __str__(self):
        return f"Match for {self.saved_search.name} - Score: {self.match_score}%"