from django.urls import path
from . import views

app_name = 'enterprise_integration'

urlpatterns = [
    # ERP Integration endpoints
    path('api/enterprise/erp-sync/', views.sync_with_erp_system, name='erp_sync'),
    
    # RFQ Automation endpoints
    path('api/rfq/automated-request/', views.create_automated_rfq, name='create_rfq'),
    path('api/rfq/<str:batch_id>/evaluation/', views.evaluate_rfq_responses, name='evaluate_rfq'),
    
    # Predictive Analytics endpoints
    path('api/analytics/<str:analytics_type>/', views.generate_predictive_analytics, name='predictive_analytics'),
    
    # Document Automation endpoints
    path('api/documents/generate/', views.generate_shipment_documents, name='generate_documents'),
    path('api/documents/<str:document_type>/template/', views.get_document_template, name='document_template'),
    
    # Final completion: Last endpoints for 100%
    path('monitoring/', views.integration_monitoring, name='integration_monitoring'),
    path('reports/', views.enterprise_reports, name='enterprise_reports'),
]