from django.db import models
from django.utils import timezone
from django.conf import settings

class ERPIntegration(models.Model):
    """Enterprise Resource Planning system integrations"""
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='erp_integrations')
    erp_system = models.CharField(max_length=100, choices=[
        ('SAP', 'SAP'),
        ('ORACLE', 'Oracle'),
        ('MICROSOFT_DYNAMICS', 'Microsoft Dynamics'),
        ('NETSUITE', 'NetSuite'),
        ('SAGE', 'Sage'),
        ('QUICKBOOKS', 'QuickBooks')
    ])
    integration_status = models.CharField(max_length=50, choices=[
        ('ACTIVE', 'Active'),
        ('PENDING', 'Pending'),
        ('ERROR', 'Error'),
        ('DISABLED', 'Disabled')
    ], default='PENDING')
    api_endpoint = models.URLField(max_length=500)
    api_key = models.Char<PERSON><PERSON>(max_length=255, blank=True)
    last_sync = models.DateTimeField(null=True, blank=True)
    sync_frequency = models.CharField(max_length=50, choices=[
        ('REAL_TIME', 'Real Time'),
        ('HOURLY', 'Hourly'),
        ('DAILY', 'Daily'),
        ('WEEKLY', 'Weekly')
    ], default='DAILY')
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        unique_together = ['user', 'erp_system']
        indexes = [
            models.Index(fields=['user', 'integration_status']),
            models.Index(fields=['last_sync']),
        ]

class ERPSyncLog(models.Model):
    """Log of ERP synchronization activities"""
    integration = models.ForeignKey(ERPIntegration, on_delete=models.CASCADE, related_name='sync_logs')
    sync_type = models.CharField(max_length=100, choices=[
        ('SHIPMENT_DATA', 'Shipment Data'),
        ('PAYMENT_DATA', 'Payment Data'),
        ('CUSTOMER_DATA', 'Customer Data'),
        ('INVOICE_DATA', 'Invoice Data'),
        ('INVENTORY_DATA', 'Inventory Data')
    ])
    status = models.CharField(max_length=50, choices=[
        ('SUCCESS', 'Success'),
        ('PARTIAL', 'Partial'),
        ('FAILED', 'Failed'),
        ('RETRY', 'Retry')
    ])
    records_processed = models.IntegerField(default=0)
    records_successful = models.IntegerField(default=0)
    records_failed = models.IntegerField(default=0)
    error_message = models.TextField(blank=True)
    sync_duration = models.DurationField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    
    class Meta:
        indexes = [
            models.Index(fields=['integration', 'created_at']),
            models.Index(fields=['status', 'created_at']),
        ]

class RFQBatch(models.Model):
    """Request for Quote batch processing"""
    batch_id = models.CharField(max_length=100, unique=True)
    customer = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='rfq_batches')
    origin_country = models.CharField(max_length=100)
    destination_country = models.CharField(max_length=100)
    weight_kg = models.FloatField()
    cargo_type = models.CharField(max_length=100)
    urgency = models.CharField(max_length=50, choices=[
        ('standard', 'Standard'),
        ('express', 'Express'),
        ('urgent', 'Urgent')
    ])
    pickup_date = models.DateTimeField()
    delivery_date = models.DateTimeField()
    status = models.CharField(max_length=50, choices=[
        ('CREATED', 'Created'),
        ('SENT', 'Sent to Providers'),
        ('RESPONSES_RECEIVED', 'Responses Received'),
        ('EVALUATED', 'Evaluated'),
        ('COMPLETED', 'Completed'),
        ('CANCELLED', 'Cancelled')
    ], default='CREATED')
    providers_contacted = models.IntegerField(default=0)
    responses_received = models.IntegerField(default=0)
    evaluation_completed = models.BooleanField(default=False)
    best_quote_score = models.FloatField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['customer', 'status']),
            models.Index(fields=['created_at']),
        ]

class RFQResponse(models.Model):
    """Individual responses to RFQ batch"""
    batch = models.ForeignKey(RFQBatch, on_delete=models.CASCADE, related_name='responses')
    provider = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='rfq_responses')
    quoted_price = models.DecimalField(max_digits=10, decimal_places=2)
    estimated_days = models.IntegerField()
    transport_mode = models.CharField(max_length=50)
    service_level = models.CharField(max_length=100)
    additional_services = models.JSONField(default=list)
    terms_conditions = models.TextField(blank=True)
    validity_period = models.IntegerField(help_text="Validity in days")
    response_score = models.FloatField(null=True, blank=True, help_text="Automated evaluation score")
    response_ranking = models.IntegerField(null=True, blank=True)
    is_selected = models.BooleanField(default=False)
    submitted_at = models.DateTimeField(default=timezone.now)
    
    class Meta:
        unique_together = ['batch', 'provider']
        indexes = [
            models.Index(fields=['batch', 'response_score']),
            models.Index(fields=['provider', 'submitted_at']),
        ]

class PredictiveAnalytics(models.Model):
    """Predictive analytics and forecasting results"""
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='analytics_results')
    analytics_type = models.CharField(max_length=100, choices=[
        ('DEMAND_FORECASTING', 'Demand Forecasting'),
        ('PRICE_PREDICTION', 'Price Prediction'),
        ('ROUTE_OPTIMIZATION', 'Route Optimization'),
        ('RISK_ASSESSMENT', 'Risk Assessment'),
        ('CAPACITY_PLANNING', 'Capacity Planning')
    ])
    origin = models.CharField(max_length=200, blank=True)
    destination = models.CharField(max_length=200, blank=True)
    current_volume = models.IntegerField(null=True, blank=True)
    current_price = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    prediction_period = models.CharField(max_length=50, choices=[
        ('7_DAYS', '7 Days'),
        ('30_DAYS', '30 Days'),
        ('90_DAYS', '90 Days'),
        ('1_YEAR', '1 Year')
    ], default='30_DAYS')
    prediction_results = models.JSONField(default=dict)
    confidence_score = models.FloatField(help_text="Confidence percentage (0-100)")
    accuracy_metrics = models.JSONField(default=dict)
    recommendations = models.JSONField(default=list)
    generated_at = models.DateTimeField(default=timezone.now)
    
    class Meta:
        indexes = [
            models.Index(fields=['user', 'analytics_type']),
            models.Index(fields=['generated_at']),
        ]

class DocumentTemplate(models.Model):
    """Document automation templates"""
    template_name = models.CharField(max_length=200)
    document_type = models.CharField(max_length=100, choices=[
        ('BILL_OF_LADING', 'Bill of Lading'),
        ('COMMERCIAL_INVOICE', 'Commercial Invoice'),
        ('PACKING_LIST', 'Packing List'),
        ('CERTIFICATE_OF_ORIGIN', 'Certificate of Origin'),
        ('INSURANCE_CERTIFICATE', 'Insurance Certificate'),
        ('CUSTOMS_DECLARATION', 'Customs Declaration')
    ])
    template_content = models.TextField()
    required_fields = models.JSONField(default=list)
    optional_fields = models.JSONField(default=list)
    file_format = models.CharField(max_length=50, choices=[
        ('PDF', 'PDF'),
        ('DOCX', 'Word Document'),
        ('HTML', 'HTML'),
        ('XML', 'XML')
    ], default='PDF')
    is_active = models.BooleanField(default=True)
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='document_templates')
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        unique_together = ['template_name', 'document_type']
        indexes = [
            models.Index(fields=['document_type', 'is_active']),
        ]

class GeneratedDocument(models.Model):
    """Generated documents log and tracking"""
    document_id = models.CharField(max_length=100, unique=True)
    template = models.ForeignKey(DocumentTemplate, on_delete=models.CASCADE, related_name='generated_documents')
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='enterprise_generated_documents')
    shipment_id = models.CharField(max_length=100, blank=True)
    document_data = models.JSONField(default=dict)
    file_path = models.CharField(max_length=500)
    file_size = models.IntegerField()
    generation_status = models.CharField(max_length=50, choices=[
        ('PROCESSING', 'Processing'),
        ('COMPLETED', 'Completed'),
        ('FAILED', 'Failed'),
        ('ARCHIVED', 'Archived')
    ], default='PROCESSING')
    download_count = models.IntegerField(default=0)
    last_downloaded = models.DateTimeField(null=True, blank=True)
    expires_at = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    
    class Meta:
        indexes = [
            models.Index(fields=['user', 'generation_status']),
            models.Index(fields=['created_at']),
        ]