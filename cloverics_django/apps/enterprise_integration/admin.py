from django.contrib import admin
from .models import (
    ERPIntegration, ERPSyncLog, RFQBatch, RFQResponse, 
    PredictiveAnalytics, DocumentTemplate, GeneratedDocument
)

@admin.register(ERPIntegration)
class ERPIntegrationAdmin(admin.ModelAdmin):
    list_display = ['user', 'erp_system', 'integration_status', 'last_sync', 'sync_frequency', 'created_at']
    list_filter = ['erp_system', 'integration_status', 'sync_frequency', 'created_at']
    search_fields = ['user__company_name', 'user__email', 'erp_system']
    ordering = ['-created_at']
    readonly_fields = ['created_at', 'updated_at']

@admin.register(ERPSyncLog)
class ERPSyncLogAdmin(admin.ModelAdmin):
    list_display = ['integration', 'sync_type', 'status', 'records_processed', 'records_successful', 'records_failed', 'created_at']
    list_filter = ['sync_type', 'status', 'created_at']
    search_fields = ['integration__user__company_name', 'integration__erp_system']
    ordering = ['-created_at']
    readonly_fields = ['created_at']

@admin.register(RFQBatch)
class RFQBatchAdmin(admin.ModelAdmin):
    list_display = ['batch_id', 'customer', 'origin_country', 'destination_country', 'status', 'providers_contacted', 'responses_received', 'created_at']
    list_filter = ['status', 'urgency', 'cargo_type', 'created_at']
    search_fields = ['batch_id', 'customer__company_name', 'origin_country', 'destination_country']
    ordering = ['-created_at']
    readonly_fields = ['created_at', 'updated_at']

@admin.register(RFQResponse)
class RFQResponseAdmin(admin.ModelAdmin):
    list_display = ['batch', 'provider', 'quoted_price', 'estimated_days', 'response_score', 'response_ranking', 'is_selected', 'submitted_at']
    list_filter = ['transport_mode', 'is_selected', 'submitted_at']
    search_fields = ['batch__batch_id', 'provider__company_name']
    ordering = ['-response_score', '-submitted_at']
    readonly_fields = ['submitted_at']

@admin.register(PredictiveAnalytics)
class PredictiveAnalyticsAdmin(admin.ModelAdmin):
    list_display = ['user', 'analytics_type', 'origin', 'destination', 'confidence_score', 'prediction_period', 'generated_at']
    list_filter = ['analytics_type', 'prediction_period', 'generated_at']
    search_fields = ['user__company_name', 'origin', 'destination']
    ordering = ['-generated_at']
    readonly_fields = ['generated_at']

@admin.register(DocumentTemplate)
class DocumentTemplateAdmin(admin.ModelAdmin):
    list_display = ['template_name', 'document_type', 'file_format', 'is_active', 'created_by', 'created_at']
    list_filter = ['document_type', 'file_format', 'is_active', 'created_at']
    search_fields = ['template_name', 'document_type', 'created_by__company_name']
    ordering = ['-created_at']
    readonly_fields = ['created_at', 'updated_at']

@admin.register(GeneratedDocument)
class GeneratedDocumentAdmin(admin.ModelAdmin):
    list_display = ['document_id', 'template', 'user', 'shipment_id', 'generation_status', 'download_count', 'created_at']
    list_filter = ['generation_status', 'template__document_type', 'created_at']
    search_fields = ['document_id', 'user__company_name', 'shipment_id']
    ordering = ['-created_at']
    readonly_fields = ['created_at', 'file_size', 'download_count']