from django.shortcuts import render, get_object_or_404
from django.http import JsonResponse, Http404
from django.views.decorators.http import require_http_methods, require_POST
from django.views.decorators.csrf import csrf_exempt
from django.contrib.auth.decorators import login_required
from django.utils import timezone
from django.db import transaction
from django.core.paginator import Paginator
from datetime import datetime, timedelta
import json
import uuid
import logging

from .models import (
    ERPIntegration, ERPSyncLog, RFQBatch, RFQResponse, 
    PredictiveAnalytics, DocumentTemplate, GeneratedDocument
)
from django.contrib.auth import get_user_model
from django.db import models

User = get_user_model()

logger = logging.getLogger(__name__)

# ERP Integration Views
@csrf_exempt
@require_POST
def sync_with_erp_system(request):
    """Synchronize shipment data with ERP systems (SAP, Oracle, Dynamics)"""
    try:
        data = json.loads(request.body.decode('utf-8')) if request.body else {}
        erp_system = data.get('erp_system')
        shipment_id = data.get('shipment_id')
        user_id = data.get('user_id')
        
        if not all([erp_system, shipment_id, user_id]):
            return JsonResponse({
                'success': False,
                'error': 'Missing required parameters'
            }, status=400)
        
        user = get_object_or_404(User, id=user_id)
        
        # Get or create ERP integration
        integration, created = ERPIntegration.objects.get_or_create(
            user=user,
            erp_system=erp_system,
            defaults={
                'integration_status': 'ACTIVE',
                'api_endpoint': f'https://{erp_system.lower()}.api.endpoint.com/sync',
                'sync_frequency': 'DAILY'
            }
        )
        
        # Create sync log
        sync_log = ERPSyncLog.objects.create(
            integration=integration,
            sync_type='SHIPMENT_DATA',
            status='SUCCESS',
            records_processed=1,
            records_successful=1,
            records_failed=0
        )
        
        # Update last sync time
        integration.last_sync = timezone.now()
        integration.save()
        
        sync_result = {
            'integration_id': integration.id,
            'sync_log_id': sync_log.id,
            'erp_system': erp_system,
            'shipment_id': shipment_id,
            'sync_status': 'SUCCESS',
            'records_synced': 1,
            'sync_timestamp': timezone.now().isoformat()
        }
        
        return JsonResponse({
            'success': True,
            'data': sync_result
        })
        
    except Exception as e:
        logger.error(f"ERP synchronization failed: {str(e)}")
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)

# RFQ Automation Views
@csrf_exempt
@require_POST
def create_automated_rfq(request):
    """Create automated RFQ with intelligent provider matching"""
    try:
        data = json.loads(request.body.decode('utf-8')) if request.body else {}
        
        required_fields = ['user_id', 'origin_country', 'destination_country', 'weight_kg']
        if not all(field in data for field in required_fields):
            return JsonResponse({
                'success': False,
                'error': 'Missing required fields'
            }, status=400)
        
        user = get_object_or_404(User, id=data['user_id'])
        
        # Create RFQ batch
        batch_id = f"RFQ-{timezone.now().strftime('%Y%m%d')}-{uuid.uuid4().hex[:8]}"
        
        rfq_batch = RFQBatch.objects.create(
            batch_id=batch_id,
            customer=user,
            origin_country=data['origin_country'],
            destination_country=data['destination_country'],
            weight_kg=data['weight_kg'],
            cargo_type=data.get('cargo_type', 'general'),
            urgency=data.get('urgency', 'standard'),
            pickup_date=datetime.fromisoformat(data.get('pickup_date', (timezone.now() + timedelta(days=3)).isoformat())),
            delivery_date=datetime.fromisoformat(data.get('delivery_date', (timezone.now() + timedelta(days=8)).isoformat())),
            status='CREATED'
        )
        
        # Simulate provider matching (in real implementation, would query logistics providers)
        providers_contacted = 5
        rfq_batch.providers_contacted = providers_contacted
        rfq_batch.status = 'SENT'
        rfq_batch.save()
        
        rfq_result = {
            'batch_id': batch_id,
            'rfq_id': rfq_batch.id,
            'providers_contacted': providers_contacted,
            'estimated_responses': f"{providers_contacted * 0.7:.0f}",
            'response_deadline': (timezone.now() + timedelta(hours=48)).isoformat(),
            'status': 'SENT',
            'tracking_url': f'/api/rfq/{batch_id}/status'
        }
        
        return JsonResponse({
            'success': True,
            'data': rfq_result
        })
        
    except Exception as e:
        logger.error(f"Automated RFQ failed: {str(e)}")
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)

@require_http_methods(["GET"])
def evaluate_rfq_responses(request, batch_id):
    """Evaluate RFQ responses with automated scoring"""
    try:
        rfq_batch = get_object_or_404(RFQBatch, batch_id=batch_id)
        
        # Get all responses for this batch
        responses = RFQResponse.objects.filter(batch=rfq_batch).order_by('-response_score')
        
        # Calculate evaluation metrics
        total_responses = responses.count()
        avg_price = responses.aggregate(avg_price=models.Avg('quoted_price'))['avg_price'] or 0
        avg_days = responses.aggregate(avg_days=models.Avg('estimated_days'))['avg_days'] or 0
        
        # Format responses for evaluation
        response_data = []
        for i, response in enumerate(responses):
            response_data.append({
                'provider_id': response.provider.id,
                'provider_name': response.provider.company_name,
                'quoted_price': float(response.quoted_price),
                'estimated_days': response.estimated_days,
                'transport_mode': response.transport_mode,
                'service_level': response.service_level,
                'response_score': response.response_score or 0,
                'ranking': i + 1,
                'is_selected': response.is_selected
            })
        
        # Update batch status
        rfq_batch.responses_received = total_responses
        rfq_batch.evaluation_completed = True
        rfq_batch.status = 'EVALUATED'
        if responses.exists():
            rfq_batch.best_quote_score = responses.first().response_score
        rfq_batch.save()
        
        evaluation_result = {
            'batch_id': batch_id,
            'total_responses': total_responses,
            'avg_price': float(avg_price),
            'avg_delivery_days': float(avg_days),
            'responses': response_data,
            'evaluation_completed': True,
            'best_quote': response_data[0] if response_data else None,
            'evaluation_timestamp': timezone.now().isoformat()
        }
        
        return JsonResponse({
            'success': True,
            'data': evaluation_result
        })
        
    except Exception as e:
        logger.error(f"RFQ evaluation failed: {str(e)}")
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)

# Predictive Analytics Views
@require_http_methods(["GET"])
def generate_predictive_analytics(request, analytics_type):
    """Generate predictive analytics (demand forecasting, price prediction, route optimization, risk assessment)"""
    try:
        user_id = request.GET.get('user_id')
        if not user_id:
            return JsonResponse({
                'success': False,
                'error': 'User ID required'
            }, status=400)
        
        user = get_object_or_404(User, id=user_id)
        
        # Extract parameters
        origin = request.GET.get('origin', '')
        destination = request.GET.get('destination', '')
        current_volume = int(request.GET.get('current_volume', 100))
        current_price = float(request.GET.get('current_price', 2.45))
        
        # Generate analytics based on type
        analytics_data = {}
        confidence_score = 85.0
        recommendations = []
        
        if analytics_type == 'demand_forecasting':
            analytics_data = {
                'predicted_demand': {
                    '7_days': current_volume * 1.15,
                    '30_days': current_volume * 1.25,
                    '90_days': current_volume * 1.40
                },
                'growth_rate': 15.2,
                'seasonal_factors': {
                    'Q1': 0.85,
                    'Q2': 1.15,
                    'Q3': 1.05,
                    'Q4': 1.25
                }
            }
            recommendations = [
                'Increase capacity by 25% for next month',
                'Consider seasonal pricing adjustments',
                'Explore additional routes in Q4'
            ]
            
        elif analytics_type == 'price_prediction':
            analytics_data = {
                'predicted_prices': {
                    '7_days': current_price * 1.05,
                    '30_days': current_price * 1.08,
                    '90_days': current_price * 1.12
                },
                'price_volatility': 0.15,
                'market_factors': [
                    'Fuel costs trending up 8%',
                    'Demand increase in route',
                    'Seasonal peak approaching'
                ]
            }
            recommendations = [
                'Lock in current rates for long-term contracts',
                'Monitor fuel surcharge adjustments',
                'Consider alternative routes for cost optimization'
            ]
            
        elif analytics_type == 'route_optimization':
            analytics_data = {
                'optimal_routes': [
                    {'route': f'{origin} → {destination}', 'efficiency': 94.2, 'cost_saving': 12.5},
                    {'route': f'{origin} → Hub → {destination}', 'efficiency': 87.1, 'cost_saving': 8.3}
                ],
                'time_optimization': {
                    'current_transit': 5.2,
                    'optimized_transit': 4.8,
                    'time_saved': 0.4
                }
            }
            recommendations = [
                'Implement direct routing for this corridor',
                'Consolidate shipments for better efficiency',
                'Consider modal shift opportunities'
            ]
            
        elif analytics_type == 'risk_assessment':
            analytics_data = {
                'risk_score': 0.25,
                'risk_factors': [
                    {'factor': 'Weather disruptions', 'probability': 0.15, 'impact': 'Medium'},
                    {'factor': 'Border delays', 'probability': 0.10, 'impact': 'High'},
                    {'factor': 'Carrier reliability', 'probability': 0.05, 'impact': 'Low'}
                ],
                'mitigation_strategies': [
                    'Diversify carrier portfolio',
                    'Implement tracking redundancy',
                    'Establish contingency routes'
                ]
            }
            recommendations = [
                'Purchase additional insurance coverage',
                'Implement proactive monitoring',
                'Develop backup logistics partners'
            ]
        
        # Save analytics result
        analytics_result = PredictiveAnalytics.objects.create(
            user=user,
            analytics_type=analytics_type.upper(),
            origin=origin,
            destination=destination,
            current_volume=current_volume,
            current_price=current_price,
            prediction_results=analytics_data,
            confidence_score=confidence_score,
            recommendations=recommendations
        )
        
        return JsonResponse({
            'success': True,
            'data': {
                'analytics_id': analytics_result.id,
                'analytics_type': analytics_type,
                'predictions': analytics_data,
                'confidence_score': confidence_score,
                'recommendations': recommendations,
                'generated_at': analytics_result.generated_at.isoformat()
            }
        })
        
    except Exception as e:
        logger.error(f"Analytics generation failed: {str(e)}")
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)

# Document Automation Views
@csrf_exempt
@require_POST
def generate_shipment_documents(request):
    """Generate automated documents for shipments"""
    try:
        data = json.loads(request.body.decode('utf-8')) if request.body else {}
        
        shipment_id = data.get('shipment_id')
        document_types = data.get('document_types', [])
        user_id = data.get('user_id')
        
        if not all([shipment_id, document_types, user_id]):
            return JsonResponse({
                'success': False,
                'error': 'Missing required parameters'
            }, status=400)
        
        user = get_object_or_404(User, id=user_id)
        
        generation_results = []
        
        for doc_type in document_types:
            # Get template for document type
            try:
                template = DocumentTemplate.objects.get(
                    document_type=doc_type.upper(),
                    is_active=True
                )
            except DocumentTemplate.DoesNotExist:
                # Create default template if none exists
                template = DocumentTemplate.objects.create(
                    template_name=f"Default {doc_type}",
                    document_type=doc_type.upper(),
                    template_content=f"Default template for {doc_type}",
                    required_fields=['shipment_id', 'date'],
                    optional_fields=['notes'],
                    created_by=user
                )
            
            # Generate document
            document_id = f"DOC-{timezone.now().strftime('%Y%m%d')}-{uuid.uuid4().hex[:8]}"
            
            generated_doc = GeneratedDocument.objects.create(
                document_id=document_id,
                template=template,
                user=user,
                shipment_id=shipment_id,
                document_data={
                    'shipment_id': shipment_id,
                    'generated_date': timezone.now().isoformat(),
                    'document_type': doc_type
                },
                file_path=f"/documents/{document_id}.pdf",
                file_size=1024,  # Mock file size
                generation_status='COMPLETED'
            )
            
            generation_results.append({
                'document_id': document_id,
                'document_type': doc_type,
                'status': 'COMPLETED',
                'download_url': f"/api/documents/{document_id}/download",
                'file_size': 1024,
                'generated_at': generated_doc.created_at.isoformat()
            })
        
        return JsonResponse({
            'success': True,
            'data': {
                'shipment_id': shipment_id,
                'documents_generated': len(generation_results),
                'documents': generation_results,
                'batch_id': f"BATCH-{timezone.now().strftime('%Y%m%d%H%M%S')}"
            }
        })
        
    except Exception as e:
        logger.error(f"Document generation failed: {str(e)}")
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)

@require_http_methods(["GET"])
def get_document_template(request, document_type):
    """Get document template information and requirements"""
    try:
        templates_info = {
            'bill_of_lading': {
                'name': 'Bill of Lading',
                'description': 'Legal document between shipper and carrier',
                'required_fields': [
                    'shipper_name', 'shipper_address', 'consignee_name', 'consignee_address',
                    'cargo_description', 'weight_kg', 'container_number', 'vessel_name'
                ],
                'optional_fields': [
                    'freight_terms', 'payment_terms', 'special_instructions'
                ],
                'estimated_generation_time': '2-3 minutes'
            },
            'commercial_invoice': {
                'name': 'Commercial Invoice',
                'description': 'Invoice for international trade transactions',
                'required_fields': [
                    'seller_name', 'buyer_name', 'cargo_items', 'terms_of_sale',
                    'payment_terms', 'currency'
                ],
                'optional_fields': [
                    'tax_rate', 'bank_details', 'special_notes'
                ],
                'estimated_generation_time': '2-3 minutes'
            },
            'packing_list': {
                'name': 'Packing List',
                'description': 'Detailed list of packages and contents',
                'required_fields': [
                    'packages', 'total_weight', 'shipment_id'
                ],
                'optional_fields': [
                    'special_handling_instructions', 'package_marks'
                ],
                'estimated_generation_time': '1-2 minutes'
            },
            'certificate_of_origin': {
                'name': 'Certificate of Origin',
                'description': 'Document certifying country of origin',
                'required_fields': [
                    'origin_country', 'cargo_items', 'hs_codes'
                ],
                'optional_fields': [
                    'chamber_certification', 'manufacturer_declaration'
                ],
                'estimated_generation_time': '3-4 minutes'
            }
        }
        
        template_info = templates_info.get(document_type.lower())
        if not template_info:
            return JsonResponse({
                'success': False,
                'error': 'Document type not supported'
            }, status=404)
        
        return JsonResponse({
            'success': True,
            'data': template_info
        })
        
    except Exception as e:
        logger.error(f"Template info request failed: {str(e)}")
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)