# Generated by Django 5.2.1 on 2025-08-07 12:45

import django.db.models.deletion
import django.utils.timezone
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='CustomsDeclarationReview',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('review_id', models.CharField(default=uuid.uuid4, max_length=50, unique=True)),
                ('declaration_number', models.CharField(max_length=100)),
                ('shipment_id', models.CharField(max_length=100)),
                ('importer_name', models.CharField(max_length=200)),
                ('goods_description', models.TextField()),
                ('goods_value', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('country_of_origin', models.Char<PERSON>ield(max_length=100)),
                ('cargo_type', models.CharField(max_length=100)),
                ('status', models.CharField(choices=[('PENDING', 'Pending Review'), ('UNDER_REVIEW', 'Under Review'), ('APPROVED', 'Approved'), ('REJECTED', 'Rejected'), ('MORE_INFO_REQUIRED', 'More Information Required'), ('HOLD', 'On Hold')], default='PENDING', max_length=30)),
                ('risk_level', models.CharField(choices=[('LOW', 'Low Risk'), ('MEDIUM', 'Medium Risk'), ('HIGH', 'High Risk'), ('CRITICAL', 'Critical Risk')], default='MEDIUM', max_length=20)),
                ('requires_inspection', models.BooleanField(default=False)),
                ('inspection_scheduled', models.DateTimeField(blank=True, null=True)),
                ('inspection_completed', models.DateTimeField(blank=True, null=True)),
                ('inspector_notes', models.TextField(blank=True)),
                ('duties_amount', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('taxes_amount', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('total_fees', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('reviewed_at', models.DateTimeField(blank=True, null=True)),
                ('review_notes', models.TextField(blank=True)),
                ('rejection_reason', models.TextField(blank=True)),
                ('additional_info_requested', models.TextField(blank=True)),
                ('submitted_date', models.DateTimeField(default=django.utils.timezone.now)),
                ('processed_date', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('customs_agent', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='customs_reviews', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'customs_advanced_declaration_review',
                'ordering': ['-submitted_date', '-updated_at'],
            },
        ),
        migrations.CreateModel(
            name='CustomsInspection',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('inspection_id', models.CharField(default=uuid.uuid4, max_length=50, unique=True)),
                ('inspection_type', models.CharField(choices=[('PHYSICAL', 'Physical Inspection'), ('DOCUMENT', 'Document Review'), ('X_RAY', 'X-Ray Scan'), ('CHEMICAL', 'Chemical Analysis'), ('RANDOM', 'Random Check')], default='PHYSICAL', max_length=20)),
                ('status', models.CharField(choices=[('SCHEDULED', 'Scheduled'), ('IN_PROGRESS', 'In Progress'), ('COMPLETED', 'Completed'), ('CANCELLED', 'Cancelled'), ('RESCHEDULED', 'Rescheduled')], default='SCHEDULED', max_length=20)),
                ('result', models.CharField(choices=[('PASS', 'Passed'), ('FAIL', 'Failed'), ('CONDITIONAL', 'Conditional Pass'), ('PENDING', 'Pending')], default='PENDING', max_length=20)),
                ('scheduled_date', models.DateTimeField()),
                ('scheduled_duration_minutes', models.IntegerField(default=60)),
                ('location', models.CharField(max_length=200)),
                ('started_at', models.DateTimeField(blank=True, null=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('actual_duration_minutes', models.IntegerField(blank=True, null=True)),
                ('findings', models.TextField(blank=True)),
                ('issues_found', models.JSONField(default=list)),
                ('photos_taken', models.IntegerField(default=0)),
                ('samples_collected', models.IntegerField(default=0)),
                ('compliance_score', models.DecimalField(decimal_places=2, default=0, max_digits=5)),
                ('violations_found', models.JSONField(default=list)),
                ('corrective_actions', models.TextField(blank=True)),
                ('inspection_notes', models.TextField(blank=True)),
                ('follow_up_required', models.BooleanField(default=False)),
                ('follow_up_date', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('declaration_review', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='inspections', to='customs_advanced.customsdeclarationreview')),
                ('inspector', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='customs_inspections', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'customs_advanced_inspection',
                'ordering': ['-scheduled_date', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='CustomsAlert',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('alert_id', models.CharField(default=uuid.uuid4, max_length=50, unique=True)),
                ('alert_type', models.CharField(choices=[('HIGH_VALUE', 'High Value Declaration'), ('SUSPICIOUS_GOODS', 'Suspicious Goods'), ('REPEAT_OFFENDER', 'Repeat Offender'), ('OVERDUE_REVIEW', 'Overdue Review'), ('INSPECTION_DUE', 'Inspection Due'), ('COMPLIANCE_ISSUE', 'Compliance Issue'), ('DOCUMENT_MISSING', 'Missing Documents'), ('DUTY_CALCULATION', 'Duty Calculation Alert')], max_length=30)),
                ('severity', models.CharField(choices=[('LOW', 'Low Priority'), ('MEDIUM', 'Medium Priority'), ('HIGH', 'High Priority'), ('CRITICAL', 'Critical Alert')], default='MEDIUM', max_length=10)),
                ('title', models.CharField(max_length=200)),
                ('message', models.TextField()),
                ('trigger_conditions', models.JSONField(default=dict)),
                ('recommended_actions', models.JSONField(default=list)),
                ('is_read', models.BooleanField(default=False)),
                ('is_resolved', models.BooleanField(default=False)),
                ('requires_action', models.BooleanField(default=True)),
                ('resolved_at', models.DateTimeField(blank=True, null=True)),
                ('resolution_notes', models.TextField(blank=True)),
                ('action_taken', models.TextField(blank=True)),
                ('expires_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('read_at', models.DateTimeField(blank=True, null=True)),
                ('customs_agent', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='customs_alerts', to=settings.AUTH_USER_MODEL)),
                ('declaration_review', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='alerts', to='customs_advanced.customsdeclarationreview')),
                ('inspection', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='alerts', to='customs_advanced.customsinspection')),
            ],
            options={
                'db_table': 'customs_advanced_alert',
                'ordering': ['-created_at', '-severity'],
            },
        ),
        migrations.CreateModel(
            name='CustomsClearanceReport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('report_id', models.CharField(default=uuid.uuid4, max_length=50, unique=True)),
                ('report_type', models.CharField(choices=[('DAILY', 'Daily Report'), ('WEEKLY', 'Weekly Report'), ('MONTHLY', 'Monthly Report'), ('QUARTERLY', 'Quarterly Report'), ('CUSTOM', 'Custom Date Range')], default='DAILY', max_length=20)),
                ('report_format', models.CharField(choices=[('CSV', 'CSV Export'), ('PDF', 'PDF Document'), ('EXCEL', 'Excel Spreadsheet'), ('JSON', 'JSON Data')], default='CSV', max_length=10)),
                ('start_date', models.DateTimeField()),
                ('end_date', models.DateTimeField()),
                ('total_declarations', models.IntegerField(default=0)),
                ('approved_declarations', models.IntegerField(default=0)),
                ('rejected_declarations', models.IntegerField(default=0)),
                ('pending_declarations', models.IntegerField(default=0)),
                ('total_inspections', models.IntegerField(default=0)),
                ('passed_inspections', models.IntegerField(default=0)),
                ('failed_inspections', models.IntegerField(default=0)),
                ('total_duties_collected', models.DecimalField(decimal_places=2, default=0, max_digits=15)),
                ('total_taxes_collected', models.DecimalField(decimal_places=2, default=0, max_digits=15)),
                ('total_fees_collected', models.DecimalField(decimal_places=2, default=0, max_digits=15)),
                ('avg_processing_time_hours', models.DecimalField(decimal_places=2, default=0, max_digits=8)),
                ('approval_rate', models.DecimalField(decimal_places=2, default=0, max_digits=5)),
                ('inspection_rate', models.DecimalField(decimal_places=2, default=0, max_digits=5)),
                ('file_name', models.CharField(blank=True, max_length=200)),
                ('file_path', models.CharField(blank=True, max_length=500)),
                ('file_size_bytes', models.BigIntegerField(default=0)),
                ('report_data', models.JSONField(default=dict)),
                ('filters_applied', models.JSONField(default=dict)),
                ('export_parameters', models.JSONField(default=dict)),
                ('generated_at', models.DateTimeField(auto_now_add=True)),
                ('downloaded_at', models.DateTimeField(blank=True, null=True)),
                ('download_count', models.IntegerField(default=0)),
                ('generated_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='customs_reports', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'customs_advanced_clearance_report',
                'ordering': ['-generated_at'],
                'indexes': [models.Index(fields=['generated_by'], name='customs_adv_gen_by_idx'), models.Index(fields=['report_type'], name='customs_adv_rep_type_idx'), models.Index(fields=['start_date', 'end_date'], name='customs_adv_date_range_idx'), models.Index(fields=['generated_at'], name='customs_adv_gen_at_idx')],
            },
        ),
        migrations.AddIndex(
            model_name='customsdeclarationreview',
            index=models.Index(fields=['customs_agent'], name='customs_adv_agent_idx'),
        ),
        migrations.AddIndex(
            model_name='customsdeclarationreview',
            index=models.Index(fields=['status'], name='customs_adv_status_idx'),
        ),
        migrations.AddIndex(
            model_name='customsdeclarationreview',
            index=models.Index(fields=['risk_level'], name='customs_adv_risk_idx'),
        ),
        migrations.AddIndex(
            model_name='customsdeclarationreview',
            index=models.Index(fields=['submitted_date'], name='customs_adv_submitted_idx'),
        ),
        migrations.AddIndex(
            model_name='customsdeclarationreview',
            index=models.Index(fields=['declaration_number'], name='customs_adv_decl_num_idx'),
        ),
        migrations.AddIndex(
            model_name='customsinspection',
            index=models.Index(fields=['inspector'], name='customs_adv_inspector_idx'),
        ),
        migrations.AddIndex(
            model_name='customsinspection',
            index=models.Index(fields=['status'], name='customs_adv_insp_status_idx'),
        ),
        migrations.AddIndex(
            model_name='customsinspection',
            index=models.Index(fields=['inspection_type'], name='customs_adv_insp_type_idx'),
        ),
        migrations.AddIndex(
            model_name='customsinspection',
            index=models.Index(fields=['scheduled_date'], name='customs_adv_scheduled_idx'),
        ),
        migrations.AddIndex(
            model_name='customsinspection',
            index=models.Index(fields=['result'], name='customs_adv_result_idx'),
        ),
        migrations.AddIndex(
            model_name='customsalert',
            index=models.Index(fields=['customs_agent'], name='customs_adv_alert_agent_idx'),
        ),
        migrations.AddIndex(
            model_name='customsalert',
            index=models.Index(fields=['alert_type'], name='customs_adv_alert_type_idx'),
        ),
        migrations.AddIndex(
            model_name='customsalert',
            index=models.Index(fields=['severity'], name='customs_adv_alert_sev_idx'),
        ),
        migrations.AddIndex(
            model_name='customsalert',
            index=models.Index(fields=['is_read'], name='customs_adv_alert_read_idx'),
        ),
        migrations.AddIndex(
            model_name='customsalert',
            index=models.Index(fields=['created_at'], name='customs_adv_alert_created_idx'),
        ),
    ]
