from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
import uuid
from decimal import Decimal

User = get_user_model()

class CustomsDeclarationReview(models.Model):
    """Enhanced customs declaration review model"""
    
    REVIEW_STATUS_CHOICES = [
        ('PENDING', 'Pending Review'),
        ('UNDER_REVIEW', 'Under Review'),
        ('APPROVED', 'Approved'),
        ('REJECTED', 'Rejected'),
        ('MORE_INFO_REQUIRED', 'More Information Required'),
        ('HOLD', 'On Hold'),
    ]
    
    RISK_LEVELS = [
        ('LOW', 'Low Risk'),
        ('MEDIUM', 'Medium Risk'),
        ('HIGH', 'High Risk'),
        ('CRITICAL', 'Critical Risk'),
    ]
    
    review_id = models.CharField(max_length=50, unique=True, default=uuid.uuid4)
    declaration_number = models.CharField(max_length=100)
    customs_agent = models.ForeignKey(User, on_delete=models.CASCADE, related_name='customs_reviews')
    shipment_id = models.CharField(max_length=100)
    importer_name = models.CharField(max_length=200)
    goods_description = models.TextField()
    goods_value = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    country_of_origin = models.CharField(max_length=100)
    cargo_type = models.CharField(max_length=100)
    
    status = models.CharField(max_length=30, choices=REVIEW_STATUS_CHOICES, default='PENDING')
    risk_level = models.CharField(max_length=20, choices=RISK_LEVELS, default='MEDIUM')
    
    # Inspection details
    requires_inspection = models.BooleanField(default=False)
    inspection_scheduled = models.DateTimeField(null=True, blank=True)
    inspection_completed = models.DateTimeField(null=True, blank=True)
    inspector_notes = models.TextField(blank=True)
    
    # Duties and taxes
    duties_amount = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    taxes_amount = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    total_fees = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    
    # Review workflow
    reviewed_at = models.DateTimeField(null=True, blank=True)
    review_notes = models.TextField(blank=True)
    rejection_reason = models.TextField(blank=True)
    additional_info_requested = models.TextField(blank=True)
    
    # Timestamps
    submitted_date = models.DateTimeField(default=timezone.now)
    processed_date = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'customs_advanced_declaration_review'
        ordering = ['-submitted_date', '-updated_at']
        indexes = [
            models.Index(fields=['customs_agent'], name='customs_adv_agent_idx'),
            models.Index(fields=['status'], name='customs_adv_status_idx'),
            models.Index(fields=['risk_level'], name='customs_adv_risk_idx'),
            models.Index(fields=['submitted_date'], name='customs_adv_submitted_idx'),
            models.Index(fields=['declaration_number'], name='customs_adv_decl_num_idx'),
        ]
    
    def __str__(self):
        return f"Review {self.declaration_number} - {self.status}"
    
    def save(self, *args, **kwargs):
        if self.status in ['APPROVED', 'REJECTED'] and not self.processed_date:
            self.processed_date = timezone.now()
        super().save(*args, **kwargs)


class CustomsInspection(models.Model):
    """Customs inspection management model"""
    
    INSPECTION_TYPES = [
        ('PHYSICAL', 'Physical Inspection'),
        ('DOCUMENT', 'Document Review'),
        ('X_RAY', 'X-Ray Scan'),
        ('CHEMICAL', 'Chemical Analysis'),
        ('RANDOM', 'Random Check'),
    ]
    
    INSPECTION_STATUS = [
        ('SCHEDULED', 'Scheduled'),
        ('IN_PROGRESS', 'In Progress'),
        ('COMPLETED', 'Completed'),
        ('CANCELLED', 'Cancelled'),
        ('RESCHEDULED', 'Rescheduled'),
    ]
    
    INSPECTION_RESULTS = [
        ('PASS', 'Passed'),
        ('FAIL', 'Failed'),
        ('CONDITIONAL', 'Conditional Pass'),
        ('PENDING', 'Pending'),
    ]
    
    inspection_id = models.CharField(max_length=50, unique=True, default=uuid.uuid4)
    declaration_review = models.ForeignKey(CustomsDeclarationReview, on_delete=models.CASCADE, related_name='inspections')
    inspector = models.ForeignKey(User, on_delete=models.CASCADE, related_name='customs_inspections')
    
    inspection_type = models.CharField(max_length=20, choices=INSPECTION_TYPES, default='PHYSICAL')
    status = models.CharField(max_length=20, choices=INSPECTION_STATUS, default='SCHEDULED')
    result = models.CharField(max_length=20, choices=INSPECTION_RESULTS, default='PENDING')
    
    # Scheduling
    scheduled_date = models.DateTimeField()
    scheduled_duration_minutes = models.IntegerField(default=60)
    location = models.CharField(max_length=200)
    
    # Execution
    started_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    actual_duration_minutes = models.IntegerField(null=True, blank=True)
    
    # Results and findings
    findings = models.TextField(blank=True)
    issues_found = models.JSONField(default=list)
    photos_taken = models.IntegerField(default=0)
    samples_collected = models.IntegerField(default=0)
    
    # Compliance
    compliance_score = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    violations_found = models.JSONField(default=list)
    corrective_actions = models.TextField(blank=True)
    
    # Administrative
    inspection_notes = models.TextField(blank=True)
    follow_up_required = models.BooleanField(default=False)
    follow_up_date = models.DateTimeField(null=True, blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'customs_advanced_inspection'
        ordering = ['-scheduled_date', '-created_at']
        indexes = [
            models.Index(fields=['inspector'], name='customs_adv_inspector_idx'),
            models.Index(fields=['status'], name='customs_adv_insp_status_idx'),
            models.Index(fields=['inspection_type'], name='customs_adv_insp_type_idx'),
            models.Index(fields=['scheduled_date'], name='customs_adv_scheduled_idx'),
            models.Index(fields=['result'], name='customs_adv_result_idx'),
        ]
    
    def __str__(self):
        return f"Inspection {self.inspection_id} - {self.inspection_type}"


class CustomsClearanceReport(models.Model):
    """Customs clearance reporting and analytics model"""
    
    REPORT_TYPES = [
        ('DAILY', 'Daily Report'),
        ('WEEKLY', 'Weekly Report'), 
        ('MONTHLY', 'Monthly Report'),
        ('QUARTERLY', 'Quarterly Report'),
        ('CUSTOM', 'Custom Date Range'),
    ]
    
    REPORT_FORMATS = [
        ('CSV', 'CSV Export'),
        ('PDF', 'PDF Document'),
        ('EXCEL', 'Excel Spreadsheet'),
        ('JSON', 'JSON Data'),
    ]
    
    report_id = models.CharField(max_length=50, unique=True, default=uuid.uuid4)
    generated_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='customs_reports')
    
    report_type = models.CharField(max_length=20, choices=REPORT_TYPES, default='DAILY')
    report_format = models.CharField(max_length=10, choices=REPORT_FORMATS, default='CSV')
    
    # Date range
    start_date = models.DateTimeField()
    end_date = models.DateTimeField()
    
    # Report statistics
    total_declarations = models.IntegerField(default=0)
    approved_declarations = models.IntegerField(default=0)
    rejected_declarations = models.IntegerField(default=0)
    pending_declarations = models.IntegerField(default=0)
    
    total_inspections = models.IntegerField(default=0)
    passed_inspections = models.IntegerField(default=0)
    failed_inspections = models.IntegerField(default=0)
    
    total_duties_collected = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    total_taxes_collected = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    total_fees_collected = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    
    # Performance metrics
    avg_processing_time_hours = models.DecimalField(max_digits=8, decimal_places=2, default=0)
    approval_rate = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    inspection_rate = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    
    # File details
    file_name = models.CharField(max_length=200, blank=True)
    file_path = models.CharField(max_length=500, blank=True)
    file_size_bytes = models.BigIntegerField(default=0)
    
    # Report metadata
    report_data = models.JSONField(default=dict)
    filters_applied = models.JSONField(default=dict)
    export_parameters = models.JSONField(default=dict)
    
    generated_at = models.DateTimeField(auto_now_add=True)
    downloaded_at = models.DateTimeField(null=True, blank=True)
    download_count = models.IntegerField(default=0)
    
    class Meta:
        db_table = 'customs_advanced_clearance_report'
        ordering = ['-generated_at']
        indexes = [
            models.Index(fields=['generated_by'], name='customs_adv_gen_by_idx'),
            models.Index(fields=['report_type'], name='customs_adv_rep_type_idx'),
            models.Index(fields=['start_date', 'end_date'], name='customs_adv_date_range_idx'),
            models.Index(fields=['generated_at'], name='customs_adv_gen_at_idx'),
        ]
    
    def __str__(self):
        return f"Report {self.report_id} - {self.report_type}"
    
    def calculate_approval_rate(self):
        """Calculate approval rate percentage"""
        if self.total_declarations > 0:
            return (self.approved_declarations / self.total_declarations) * 100
        return 0
    
    def calculate_inspection_rate(self):
        """Calculate inspection rate percentage"""
        if self.total_declarations > 0:
            return (self.total_inspections / self.total_declarations) * 100
        return 0


class CustomsAlert(models.Model):
    """Customs-specific alert system"""
    
    ALERT_TYPES = [
        ('HIGH_VALUE', 'High Value Declaration'),
        ('SUSPICIOUS_GOODS', 'Suspicious Goods'),
        ('REPEAT_OFFENDER', 'Repeat Offender'),
        ('OVERDUE_REVIEW', 'Overdue Review'),
        ('INSPECTION_DUE', 'Inspection Due'),
        ('COMPLIANCE_ISSUE', 'Compliance Issue'),
        ('DOCUMENT_MISSING', 'Missing Documents'),
        ('DUTY_CALCULATION', 'Duty Calculation Alert'),
    ]
    
    SEVERITY_LEVELS = [
        ('LOW', 'Low Priority'),
        ('MEDIUM', 'Medium Priority'),
        ('HIGH', 'High Priority'),
        ('CRITICAL', 'Critical Alert'),
    ]
    
    alert_id = models.CharField(max_length=50, unique=True, default=uuid.uuid4)
    alert_type = models.CharField(max_length=30, choices=ALERT_TYPES)
    severity = models.CharField(max_length=10, choices=SEVERITY_LEVELS, default='MEDIUM')
    
    # Target information
    customs_agent = models.ForeignKey(User, on_delete=models.CASCADE, related_name='customs_alerts')
    declaration_review = models.ForeignKey(CustomsDeclarationReview, on_delete=models.CASCADE, related_name='alerts', null=True, blank=True)
    inspection = models.ForeignKey(CustomsInspection, on_delete=models.CASCADE, related_name='alerts', null=True, blank=True)
    
    # Alert content
    title = models.CharField(max_length=200)
    message = models.TextField()
    
    # Alert details
    trigger_conditions = models.JSONField(default=dict)
    recommended_actions = models.JSONField(default=list)
    
    # Status tracking
    is_read = models.BooleanField(default=False)
    is_resolved = models.BooleanField(default=False)
    requires_action = models.BooleanField(default=True)
    
    # Resolution
    resolved_at = models.DateTimeField(null=True, blank=True)
    resolution_notes = models.TextField(blank=True)
    action_taken = models.TextField(blank=True)
    
    # Auto-expire
    expires_at = models.DateTimeField(null=True, blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    read_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        db_table = 'customs_advanced_alert'
        ordering = ['-created_at', '-severity']
        indexes = [
            models.Index(fields=['customs_agent'], name='customs_adv_alert_agent_idx'),
            models.Index(fields=['alert_type'], name='customs_adv_alert_type_idx'),
            models.Index(fields=['severity'], name='customs_adv_alert_sev_idx'),
            models.Index(fields=['is_read'], name='customs_adv_alert_read_idx'),
            models.Index(fields=['created_at'], name='customs_adv_alert_created_idx'),
        ]
    
    def __str__(self):
        return f"Alert {self.alert_id} - {self.title}"
    
    def mark_as_read(self):
        """Mark alert as read"""
        if not self.is_read:
            self.is_read = True
            self.read_at = timezone.now()
            self.save()
    
    def resolve(self, resolution_notes="", action_taken=""):
        """Resolve the alert"""
        self.is_resolved = True
        self.resolved_at = timezone.now()
        self.resolution_notes = resolution_notes
        self.action_taken = action_taken
        self.save()