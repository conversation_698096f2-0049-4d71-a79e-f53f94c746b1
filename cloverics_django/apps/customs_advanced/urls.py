# Customs Advanced URLs - extracted from fastapi_main.py
from django.urls import path
from . import views

app_name = 'customs_advanced'

urlpatterns = [
    # Advanced customs dashboard
    path('dashboard/', views.customs_advanced_dashboard, name='dashboard'),
    path('overview/', views.customs_overview, name='overview'),
    
    # Declaration review system
    path('declarations/review/', views.declaration_review_list, name='declaration_review_list'),
    path('declarations/review/<int:declaration_id>/', views.declaration_review_detail, name='declaration_review_detail'),
    path('declarations/review/<int:declaration_id>/approve/', views.approve_declaration_review, name='approve_declaration_review'),
    path('declarations/review/<int:declaration_id>/reject/', views.reject_declaration_review, name='reject_declaration_review'),
    
    # Inspection management
    path('inspections/', views.inspection_list, name='inspection_list'),
    path('inspections/schedule/', views.schedule_inspection, name='schedule_inspection'),
    path('inspections/<int:inspection_id>/', views.inspection_detail, name='inspection_detail'),
    path('inspections/<int:inspection_id>/complete/', views.complete_inspection, name='complete_inspection'),
    path('inspections/<int:inspection_id>/report/', views.inspection_report, name='inspection_report'),
    
    # Clearance reporting
    path('clearance/reports/', views.clearance_reports, name='clearance_reports'),
    path('clearance/create-report/', views.create_clearance_report, name='create_clearance_report'),
    path('clearance/report/<int:report_id>/', views.clearance_report_detail, name='clearance_report_detail'),
    path('clearance/report/<int:report_id>/download/', views.download_clearance_report, name='download_clearance_report'),
    
    # Alert management
    path('alerts/', views.customs_alerts, name='customs_alerts'),
    path('alerts/create/', views.create_customs_alert, name='create_customs_alert'),
    path('alerts/<int:alert_id>/', views.customs_alert_detail, name='customs_alert_detail'),
    path('alerts/<int:alert_id>/acknowledge/', views.acknowledge_alert, name='acknowledge_alert'),
    path('alerts/<int:alert_id>/resolve/', views.resolve_alert, name='resolve_alert'),
    
    # Advanced reporting and analytics
    path('analytics/', views.customs_analytics, name='customs_analytics'),
    path('analytics/performance/', views.performance_analytics, name='performance_analytics'),
    path('analytics/compliance/', views.compliance_analytics, name='compliance_analytics'),
    path('analytics/export/', views.export_analytics, name='export_analytics'),
    
    # API endpoints for AJAX operations
    path('api/declaration-status/', views.api_declaration_status, name='api_declaration_status'),
    path('api/inspection-schedule/', views.api_inspection_schedule, name='api_inspection_schedule'),
    path('api/alert-summary/', views.api_alert_summary, name='api_alert_summary'),
    path('api/compliance-metrics/', views.api_compliance_metrics, name='api_compliance_metrics'),
    
    # Batch operations
    path('batch/approve/', views.batch_approve_declarations, name='batch_approve_declarations'),
    path('batch/schedule-inspections/', views.batch_schedule_inspections, name='batch_schedule_inspections'),
    path('batch/export/', views.batch_export_data, name='batch_export_data'),
]