from django.shortcuts import render, get_object_or_404, redirect
from django.http import JsonResponse, HttpResponse
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.utils import timezone
from django.db.models import Q, Count, Sum, Avg
from django.core.paginator import Paginator
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from django.contrib.auth import get_user_model
from .models import CustomsDeclarationReview, CustomsInspection, CustomsClearanceReport, CustomsAlert
import json
import csv
from io import StringIO
from datetime import datetime, timedelta
import logging

User = get_user_model()
logger = logging.getLogger(__name__)


@login_required
def customs_inspections_page(request):
    """
    ✅ EXTRACTED FROM FASTAPI: /customs/inspections endpoint
    Original Lines: 284-355 (71 lines) - PHASE 23 EXTRACTION
    
    Customs inspections dashboard with comprehensive statistics and risk analysis.
    """
    try:
        user = request.user
        
        # Mock inspection statistics for demonstration
        scheduled_inspections = 12
        completed_today = 8
        issues_found = 3
        inspection_rate = 85.7
        
        # Physical exam data
        physical_exams = 45
        document_reviews = 78
        xray_scans = 23
        sample_tests = 12
        
        # Inspection results percentages
        passed_percentage = 72.5
        minor_percentage = 18.3
        major_percentage = 7.2
        seized_percentage = 2.0
        
        # Scheduled inspections list
        scheduled_inspections_list = [
            {"id": 1, "shipment": "SH-2025-001", "priority": "High", "time": "09:00"},
            {"id": 2, "shipment": "SH-2025-002", "priority": "Medium", "time": "10:30"},
            {"id": 3, "shipment": "SH-2025-003", "priority": "Low", "time": "14:00"},
        ]
        
        # Recent inspections
        recent_inspections = [
            {"id": 1, "shipment": "SH-2025-004", "result": "Passed", "inspector": "Agent Smith"},
            {"id": 2, "shipment": "SH-2025-005", "result": "Minor Issues", "inspector": "Agent Jones"},
        ]
        
        context = {
            "request": request,
            "user": user,
            "title": "Inspections - Cloverics",
            "stats": {
                "scheduled": scheduled_inspections,
                "completed_today": completed_today,
                "issues_found": issues_found,
                "inspection_rate": f"{inspection_rate:.1f}%"
            },
            "scheduled_inspections": scheduled_inspections_list[:10],
            "recent_inspections": recent_inspections[:8],
            "inspection_categories": {
                "physical_exams": physical_exams,
                "document_reviews": document_reviews,
                "xray_scans": xray_scans,
                "sample_tests": sample_tests
            },
            "inspection_results": {
                "passed_percentage": f"{passed_percentage:.1f}%",
                "minor_percentage": f"{minor_percentage:.1f}%", 
                "major_percentage": f"{major_percentage:.1f}%",
                "seized_percentage": f"{seized_percentage:.1f}%"
            }
        }
        
        return render(request, "customs_advanced/inspections.html", context)
        
    except Exception as e:
        logger.error(f"Error loading customs inspections data: {e}")
        # Fallback context with error message
        context = {
            "request": request,
            "user": user,
            "title": "Inspections - Cloverics",
            "error": "Unable to load inspection data. Please try again.",
            "stats": {
                "scheduled": 0,
                "completed_today": 0,
                "issues_found": 0,
                "inspection_rate": "N/A"
            },
            "scheduled_inspections": [],
            "recent_inspections": [],
            "inspection_categories": {
                "physical_exams": 0,
                "document_reviews": 0,
                "xray_scans": 0,
                "sample_tests": 0
            },
            "inspection_results": {
                "passed_percentage": "0%",
                "minor_percentage": "0%",
                "major_percentage": "0%",
                "seized_percentage": "0%"
            }
        }
        
        return render(request, "customs_advanced/inspections.html", context)

@login_required
def declaration_review_dashboard(request):
    """Main dashboard for customs declaration reviews"""
    if request.user.user_type not in ['CUSTOMS_AGENT', 'ADMIN']:
        messages.error(request, "Access denied.")
        return redirect('/')
    
    # Get filter parameters
    status_filter = request.GET.get('status', '')
    risk_filter = request.GET.get('risk_level', '')
    date_from = request.GET.get('date_from', '')
    date_to = request.GET.get('date_to', '')
    
    # Base queryset
    reviews = CustomsDeclarationReview.objects.select_related('customs_agent').all()
    
    # Apply filters
    if status_filter:
        reviews = reviews.filter(status=status_filter)
    if risk_filter:
        reviews = reviews.filter(risk_level=risk_filter)
    if date_from:
        reviews = reviews.filter(submitted_date__gte=date_from)
    if date_to:
        reviews = reviews.filter(submitted_date__lte=date_to)
    
    # Pagination
    paginator = Paginator(reviews, 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # Statistics
    stats = {
        'total_reviews': CustomsDeclarationReview.objects.count(),
        'pending_reviews': CustomsDeclarationReview.objects.filter(status='PENDING').count(),
        'approved_today': CustomsDeclarationReview.objects.filter(
            status='APPROVED', 
            processed_date__date=timezone.now().date()
        ).count(),
        'high_risk_pending': CustomsDeclarationReview.objects.filter(
            risk_level='HIGH', 
            status__in=['PENDING', 'UNDER_REVIEW']
        ).count(),
    }
    
    context = {
        'page_obj': page_obj,
        'stats': stats,
        'status_choices': CustomsDeclarationReview.REVIEW_STATUS_CHOICES,
        'risk_choices': CustomsDeclarationReview.RISK_LEVELS,
        'filters': {
            'status': status_filter,
            'risk_level': risk_filter,
            'date_from': date_from,
            'date_to': date_to,
        }
    }
    
    return render(request, 'customs_advanced/review_dashboard.html', context)


@login_required
def declaration_detail(request, review_id):
    """View detailed customs declaration review"""
    if request.user.user_type not in ['CUSTOMS_AGENT', 'ADMIN']:
        messages.error(request, "Access denied.")
        return redirect('/')
    
    review = get_object_or_404(CustomsDeclarationReview, id=review_id)
    inspections = review.inspections.all().order_by('-scheduled_date')
    alerts = review.alerts.filter(is_resolved=False).order_by('-created_at')
    
    context = {
        'review': review,
        'inspections': inspections,
        'alerts': alerts,
        'status_choices': CustomsDeclarationReview.REVIEW_STATUS_CHOICES,
    }
    
    return render(request, 'customs_advanced/declaration_detail.html', context)


@login_required 
@require_http_methods(["POST"])
def update_declaration_status(request, review_id):
    """Update declaration review status via AJAX"""
    if request.user.user_type not in ['CUSTOMS_AGENT', 'ADMIN']:
        return JsonResponse({'success': False, 'error': 'Access denied'})
    
    try:
        review = get_object_or_404(CustomsDeclarationReview, id=review_id)
        data = json.loads(request.body)
        
        action = data.get('action')
        notes = data.get('notes', '')
        
        if action == 'approve':
            review.status = 'APPROVED'
            review.review_notes = notes
            review.reviewed_at = timezone.now()
            review.processed_date = timezone.now()
            message = "Declaration approved successfully"
            
        elif action == 'reject':
            review.status = 'REJECTED'  
            review.rejection_reason = notes
            review.reviewed_at = timezone.now()
            review.processed_date = timezone.now()
            message = "Declaration rejected"
            
        elif action == 'request_info':
            review.status = 'MORE_INFO_REQUIRED'
            review.additional_info_requested = notes
            review.reviewed_at = timezone.now()
            message = "Additional information requested"
            
        elif action == 'hold':
            review.status = 'HOLD'
            review.review_notes = notes
            review.reviewed_at = timezone.now()
            message = "Declaration placed on hold"
            
        else:
            return JsonResponse({'success': False, 'error': 'Invalid action'})
        
        review.save()
        
        # Create alert for status change
        CustomsAlert.objects.create(
            alert_type='REVIEW_STATUS_CHANGE',
            severity='MEDIUM',
            customs_agent=request.user,
            declaration_review=review,
            title=f"Declaration {review.declaration_number} - Status Updated",
            message=f"Status changed to {review.get_status_display()}"
        )
        
        return JsonResponse({
            'success': True, 
            'message': message,
            'new_status': review.get_status_display()
        })
        
    except Exception as e:
        logger.error(f"Error updating declaration status: {e}")
        return JsonResponse({'success': False, 'error': str(e)})


@login_required
def inspection_management(request):
    """Inspection scheduling and management dashboard"""
    if request.user.user_type not in ['CUSTOMS_AGENT', 'ADMIN']:
        messages.error(request, "Access denied.")
        return redirect('/')
    
    # Get inspections with filters
    status_filter = request.GET.get('status', '')
    type_filter = request.GET.get('type', '')
    date_filter = request.GET.get('date', '')
    
    inspections = CustomsInspection.objects.select_related(
        'declaration_review', 'inspector'
    ).all()
    
    if status_filter:
        inspections = inspections.filter(status=status_filter)
    if type_filter:
        inspections = inspections.filter(inspection_type=type_filter)
    if date_filter:
        inspections = inspections.filter(scheduled_date__date=date_filter)
    
    # Pagination
    paginator = Paginator(inspections, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # Statistics
    today = timezone.now().date()
    stats = {
        'scheduled_today': CustomsInspection.objects.filter(
            scheduled_date__date=today,
            status='SCHEDULED'
        ).count(),
        'in_progress': CustomsInspection.objects.filter(status='IN_PROGRESS').count(),
        'completed_today': CustomsInspection.objects.filter(
            completed_at__date=today,
            status='COMPLETED'
        ).count(),
        'pending_results': CustomsInspection.objects.filter(
            status='COMPLETED',
            result='PENDING'
        ).count(),
    }
    
    context = {
        'page_obj': page_obj,
        'stats': stats,
        'status_choices': CustomsInspection.INSPECTION_STATUS,
        'type_choices': CustomsInspection.INSPECTION_TYPES,
        'result_choices': CustomsInspection.INSPECTION_RESULTS,
        'filters': {
            'status': status_filter,
            'type': type_filter,
            'date': date_filter,
        }
    }
    
    return render(request, 'customs_advanced/inspection_management.html', context)


@login_required
@require_http_methods(["POST"])
def schedule_inspection(request):
    """Schedule a new inspection"""
    if request.user.user_type not in ['CUSTOMS_AGENT', 'ADMIN']:
        return JsonResponse({'success': False, 'error': 'Access denied'})
    
    try:
        data = json.loads(request.body)
        
        review = get_object_or_404(CustomsDeclarationReview, id=data['review_id'])
        
        inspection = CustomsInspection.objects.create(
            declaration_review=review,
            inspector=request.user,
            inspection_type=data['inspection_type'],
            scheduled_date=datetime.fromisoformat(data['scheduled_date']),
            scheduled_duration_minutes=data.get('duration', 60),
            location=data.get('location', 'Main Inspection Area'),
            inspection_notes=data.get('notes', '')
        )
        
        # Update review to indicate inspection required
        review.requires_inspection = True
        review.inspection_scheduled = inspection.scheduled_date
        review.save()
        
        # Create alert
        CustomsAlert.objects.create(
            alert_type='INSPECTION_DUE',
            severity='MEDIUM',
            customs_agent=request.user,
            declaration_review=review,
            inspection=inspection,
            title=f"Inspection Scheduled",
            message=f"Inspection scheduled for {inspection.scheduled_date.strftime('%Y-%m-%d %H:%M')}"
        )
        
        return JsonResponse({
            'success': True,
            'message': 'Inspection scheduled successfully',
            'inspection_id': inspection.inspection_id
        })
        
    except Exception as e:
        logger.error(f"Error scheduling inspection: {e}")
        return JsonResponse({'success': False, 'error': str(e)})


@login_required
def generate_clearance_report(request):
    """Generate customs clearance reports"""
    if request.user.user_type not in ['CUSTOMS_AGENT', 'ADMIN']:
        messages.error(request, "Access denied.")
        return redirect('/')
    
    if request.method == 'POST':
        try:
            report_type = request.POST.get('report_type', 'DAILY')
            report_format = request.POST.get('format', 'CSV')
            start_date = datetime.fromisoformat(request.POST.get('start_date'))
            end_date = datetime.fromisoformat(request.POST.get('end_date'))
            
            # Get data for the date range
            reviews = CustomsDeclarationReview.objects.filter(
                submitted_date__range=(start_date, end_date)
            )
            
            # Calculate statistics
            total_declarations = reviews.count()
            approved = reviews.filter(status='APPROVED').count()
            rejected = reviews.filter(status='REJECTED').count()
            pending = reviews.filter(status__in=['PENDING', 'UNDER_REVIEW']).count()
            
            inspections = CustomsInspection.objects.filter(
                scheduled_date__range=(start_date, end_date)
            )
            total_inspections = inspections.count()
            passed_inspections = inspections.filter(result='PASS').count()
            failed_inspections = inspections.filter(result='FAIL').count()
            
            # Create report record
            report = CustomsClearanceReport.objects.create(
                generated_by=request.user,
                report_type=report_type,
                report_format=report_format,
                start_date=start_date,
                end_date=end_date,
                total_declarations=total_declarations,
                approved_declarations=approved,
                rejected_declarations=rejected,
                pending_declarations=pending,
                total_inspections=total_inspections,
                passed_inspections=passed_inspections,
                failed_inspections=failed_inspections,
                total_duties_collected=reviews.aggregate(
                    total=Sum('duties_amount')
                )['total'] or 0,
                total_taxes_collected=reviews.aggregate(
                    total=Sum('taxes_amount')
                )['total'] or 0,
                approval_rate=approved / max(total_declarations, 1) * 100,
                inspection_rate=total_inspections / max(total_declarations, 1) * 100
            )
            
            # Generate file based on format
            if report_format == 'CSV':
                return generate_csv_report(report, reviews)
            elif report_format == 'JSON':
                return generate_json_report(report, reviews)
                
        except Exception as e:
            logger.error(f"Error generating report: {e}")
            messages.error(request, f"Error generating report: {str(e)}")
    
    # Show report generation form
    context = {
        'report_types': CustomsClearanceReport.REPORT_TYPES,
        'report_formats': CustomsClearanceReport.REPORT_FORMATS,
    }
    
    return render(request, 'customs_advanced/generate_report.html', context)


def generate_csv_report(report, reviews):
    """Generate CSV format report"""
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = f'attachment; filename="customs_report_{report.report_id}.csv"'
    
    writer = csv.writer(response)
    
    # Header
    writer.writerow([
        'Declaration Number', 'Importer', 'Goods Description', 'Value',
        'Country of Origin', 'Status', 'Submitted Date', 'Processed Date',
        'Duties', 'Taxes', 'Risk Level'
    ])
    
    # Data rows
    for review in reviews:
        writer.writerow([
            review.declaration_number,
            review.importer_name,
            review.goods_description[:100],
            f"${review.goods_value:,.2f}",
            review.country_of_origin,
            review.get_status_display(),
            review.submitted_date.strftime('%Y-%m-%d'),
            review.processed_date.strftime('%Y-%m-%d') if review.processed_date else '',
            f"${review.duties_amount:,.2f}",
            f"${review.taxes_amount:,.2f}",
            review.get_risk_level_display()
        ])
    
    # Update report record
    report.file_name = f"customs_report_{report.report_id}.csv"
    report.download_count += 1
    report.downloaded_at = timezone.now()
    report.save()
    
    return response


def generate_json_report(report, reviews):
    """Generate JSON format report"""
    data = {
        'report_info': {
            'report_id': report.report_id,
            'type': report.report_type,
            'generated_at': report.generated_at.isoformat(),
            'date_range': {
                'start': report.start_date.isoformat(),
                'end': report.end_date.isoformat()
            }
        },
        'statistics': {
            'total_declarations': report.total_declarations,
            'approved': report.approved_declarations,
            'rejected': report.rejected_declarations,
            'pending': report.pending_declarations,
            'approval_rate': float(report.approval_rate),
            'total_duties': float(report.total_duties_collected),
            'total_taxes': float(report.total_taxes_collected)
        },
        'declarations': [
            {
                'declaration_number': review.declaration_number,
                'importer': review.importer_name,
                'goods_description': review.goods_description,
                'value': float(review.goods_value),
                'country_of_origin': review.country_of_origin,
                'status': review.status,
                'risk_level': review.risk_level,
                'submitted_date': review.submitted_date.isoformat(),
                'processed_date': review.processed_date.isoformat() if review.processed_date else None,
                'duties_amount': float(review.duties_amount),
                'taxes_amount': float(review.taxes_amount)
            }
            for review in reviews
        ]
    }
    
    response = JsonResponse(data, json_dumps_params={'indent': 2})
    response['Content-Disposition'] = f'attachment; filename="customs_report_{report.report_id}.json"'
    
    # Update report record
    report.file_name = f"customs_report_{report.report_id}.json"
    report.download_count += 1
    report.downloaded_at = timezone.now()
    report.save()
    
    return response


@login_required
def alerts_dashboard(request):
    """Customs alerts management dashboard"""
    if request.user.user_type not in ['CUSTOMS_AGENT', 'ADMIN']:
        messages.error(request, "Access denied.")
        return redirect('/')
    
    # Get alerts with filters
    severity_filter = request.GET.get('severity', '')
    type_filter = request.GET.get('type', '')
    read_filter = request.GET.get('read', '')
    
    alerts = CustomsAlert.objects.select_related(
        'customs_agent', 'declaration_review', 'inspection'
    ).filter(customs_agent=request.user)
    
    if severity_filter:
        alerts = alerts.filter(severity=severity_filter)
    if type_filter:
        alerts = alerts.filter(alert_type=type_filter)
    if read_filter:
        is_read = read_filter.lower() == 'true'
        alerts = alerts.filter(is_read=is_read)
    
    # Pagination
    paginator = Paginator(alerts, 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # Statistics
    stats = {
        'total_alerts': CustomsAlert.objects.filter(customs_agent=request.user).count(),
        'unread_alerts': CustomsAlert.objects.filter(
            customs_agent=request.user, 
            is_read=False
        ).count(),
        'critical_alerts': CustomsAlert.objects.filter(
            customs_agent=request.user,
            severity='CRITICAL',
            is_resolved=False
        ).count(),
        'action_required': CustomsAlert.objects.filter(
            customs_agent=request.user,
            requires_action=True,
            is_resolved=False
        ).count(),
    }
    
    context = {
        'page_obj': page_obj,
        'stats': stats,
        'severity_choices': CustomsAlert.SEVERITY_LEVELS,
        'type_choices': CustomsAlert.ALERT_TYPES,
        'filters': {
            'severity': severity_filter,
            'type': type_filter,
            'read': read_filter,
        }
    }
    
    return render(request, 'customs_advanced/alerts_dashboard.html', context)


@login_required
@require_http_methods(["POST"])
def mark_alert_read(request, alert_id):
    """Mark alert as read"""
    try:
        alert = get_object_or_404(CustomsAlert, id=alert_id, customs_agent=request.user)
        alert.mark_as_read()
        
        return JsonResponse({
            'success': True,
            'message': 'Alert marked as read'
        })
        
    except Exception as e:
        logger.error(f"Error marking alert as read: {e}")
        return JsonResponse({'success': False, 'error': str(e)})