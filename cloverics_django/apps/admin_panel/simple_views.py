from django.shortcuts import render

def dashboard(request):
    """
    Admin Dashboard
    Displays platform statistics and management tools
    """
    # Create mock user object with proper user_type for sidebar navigation
    class MockUser:
        def __init__(self):
            self.user_type = 'ADMIN'
            self.id = 1
            self.email = '<EMAIL>'
            self.company_name = 'Cloverics Platform'
    
    context = {
        'title': 'Admin Dashboard - Cloverics',
        'user_type': 'ADMIN',
        'user': Mock<PERSON>ser(),
        'platform_stats': {
            'total_users': 247,
            'active_shipments': 89,
            'logistics_providers': 143,
            'monthly_transactions': 156789.50,
            'system_health': 98.5,
            'support_tickets': 12
        },
        'recent_activities': [
            {
                'action': 'New user registration',
                'user': '<EMAIL>',
                'timestamp': '2 hours ago'
            },
            {
                'action': 'Shipment completed',
                'details': 'CL718F825C delivered successfully',
                'timestamp': '4 hours ago'
            }
        ]
    }
    
    return render(request, 'admin_panel/dashboard.html', context)