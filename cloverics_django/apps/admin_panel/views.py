"""
Enhanced admin panel views
Extracted from FastAPI main file with proper Django implementation
"""

from django.shortcuts import render, redirect
from django.http import JsonResponse
from django.contrib.auth.decorators import login_required
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.db import connection
from django.utils import timezone
import json
import logging

logger = logging.getLogger(__name__)

# ============================================================================
# ADMIN PANEL SYSTEM - EXTRACTED FROM FASTAPI
# ============================================================================

@login_required
def admin_users_page(request):
    """Admin users management page - EXTRACTED FROM FASTAPI"""
    if request.user.user_type != "ADMIN":
        return redirect('/login')
    
    try:
        with connection.cursor() as cursor:
            cursor.execute("SELECT COUNT(*) FROM auth_user")
            total_users = cursor.fetchone()[0] or 0
    except Exception as e:
        logger.error(f"Database error: {e}")
        total_users = 0
    
    return render(request, 'admin/users.html', {
        'user': request.user,
        'total_users': total_users,
        'title': "User Management - Cloverics"
    })

@login_required
def admin_system_page(request):
    """Admin system overview page - EXTRACTED FROM FASTAPI"""
    if request.user.user_type != "ADMIN":
        return redirect('/login')
    
    # System metrics
    system_metrics = {
        'cpu_usage': '45.2%',
        'memory_usage': '67.8%',
        'disk_usage': '38.9%',
        'active_connections': 142,
        'response_time': '0.85ms',
        'uptime': '7 days, 14 hours'
    }
    
    # Recent activities
    recent_activities = [
        {
            'timestamp': timezone.now(),
            'type': 'user_registration',
            'description': 'New user registered: <EMAIL>',
            'severity': 'info'
        },
        {
            'timestamp': timezone.now(),
            'type': 'payment_processed',
            'description': 'Payment processed: $1,250.00',
            'severity': 'success'
        },
        {
            'timestamp': timezone.now(),
            'type': 'system_warning',
            'description': 'High memory usage detected',
            'severity': 'warning'
        }
    ]
    
    return render(request, 'admin/system.html', {
        'user': request.user,
        'system_metrics': system_metrics,
        'recent_activities': recent_activities,
        'title': "System Overview - Cloverics"
    })

@login_required
def admin_verification_queue(request):
    """Admin verification queue page - EXTRACTED FROM FASTAPI"""
    if request.user.user_type != "ADMIN":
        return redirect('/login')
    
    # Mock verification queue data
    pending_verifications = [
        {
            'id': 1,
            'user_email': '<EMAIL>',
            'user_type': 'LOGISTICS_PROVIDER',
            'company_name': 'Express Logistics Co.',
            'submitted_at': timezone.now(),
            'documents_count': 3,
            'status': 'pending'
        },
        {
            'id': 2,
            'user_email': '<EMAIL>',
            'user_type': 'INDEPENDENT_DRIVER',
            'company_name': 'John Driver Services',
            'submitted_at': timezone.now(),
            'documents_count': 2,
            'status': 'under_review'
        }
    ]
    
    return render(request, 'admin/verification_queue.html', {
        'user': request.user,
        'pending_verifications': pending_verifications,
        'title': "Verification Queue - Cloverics"
    })

@csrf_exempt
@require_http_methods(["POST"])
@login_required
def admin_approve_verification(request, user_id):
    """Approve user verification - EXTRACTED FROM FASTAPI"""
    if request.user.user_type != "ADMIN":
        return JsonResponse({'success': False, 'error': 'Access denied'}, status=403)
    
    try:
        from django.contrib.auth import get_user_model
        User = get_user_model()
        
        user_to_approve = User.objects.get(id=user_id)
        user_to_approve.is_verified = True
        user_to_approve.save()
        
        # Create notification for approved user
        from core.models import Notification
        Notification.objects.create(
            user=user_to_approve,
            title="Account Verified",
            message="Your account has been verified and approved by our admin team.",
            notification_type='verification',
            is_read=False
        )
        
        return JsonResponse({
            'success': True,
            'message': f'User {user_to_approve.email} has been verified successfully'
        })
        
    except User.DoesNotExist:
        return JsonResponse({
            'success': False,
            'error': 'User not found'
        }, status=404)
    except Exception as e:
        logger.error(f"Error approving verification: {e}")
        return JsonResponse({
            'success': False,
            'error': 'Failed to approve verification'
        }, status=500)

@csrf_exempt
@require_http_methods(["POST"])
@login_required
def admin_reject_verification(request, user_id):
    """Reject user verification - EXTRACTED FROM FASTAPI"""
    if request.user.user_type != "ADMIN":
        return JsonResponse({'success': False, 'error': 'Access denied'}, status=403)
    
    try:
        data = json.loads(request.body)
        rejection_reason = data.get('reason', 'No reason provided')
        
        from django.contrib.auth import get_user_model
        User = get_user_model()
        
        user_to_reject = User.objects.get(id=user_id)
        user_to_reject.is_verified = False
        user_to_reject.save()
        
        # Create notification for rejected user
        from core.models import Notification
        Notification.objects.create(
            user=user_to_reject,
            title="Verification Rejected",
            message=f"Your account verification has been rejected. Reason: {rejection_reason}",
            notification_type='verification',
            is_read=False
        )
        
        return JsonResponse({
            'success': True,
            'message': f'User {user_to_reject.email} verification has been rejected'
        })
        
    except User.DoesNotExist:
        return JsonResponse({
            'success': False,
            'error': 'User not found'
        }, status=404)
    except Exception as e:
        logger.error(f"Error rejecting verification: {e}")
        return JsonResponse({
            'success': False,
            'error': 'Failed to reject verification'
        }, status=500)