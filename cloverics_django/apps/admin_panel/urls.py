# Admin panel URLs extracted from fastapi_main.py
from django.urls import path
from . import views

app_name = 'admin_panel'

urlpatterns = [
    # Main admin pages
    path('dashboard/', views.admin_dashboard, name='dashboard'),
    path('users/', views.admin_users_page, name='admin_users_page'),
    path('system/', views.admin_system_page, name='admin_system_page'),
    
    # Admin actions
    path('support-tickets/<str:ticket_id>/respond/', views.respond_to_ticket, name='respond_to_ticket'),
    
    # API endpoints
    path('api/user-stats/', views.api_user_stats, name='api_user_stats'),
    path('api/system-health/', views.api_system_health, name='api_system_health'),
    
    # Phase 3: Additional admin endpoints for 100% coverage
    path('users/bulk-actions/', views.bulk_user_actions, name='bulk_user_actions'),
    path('users/<int:user_id>/permissions/', views.user_permissions, name='user_permissions'),
    path('users/<int:user_id>/activity/', views.user_activity_log, name='user_activity_log'),
    path('system/backup/', views.system_backup, name='system_backup'),
    path('system/maintenance/', views.system_maintenance, name='system_maintenance'),
    path('system/logs/', views.system_logs, name='system_logs'),
    path('analytics/platform/', views.platform_analytics, name='platform_analytics'),
    path('analytics/revenue/', views.admin_revenue_analytics, name='admin_revenue_analytics'),
    path('security/alerts/', views.security_alerts, name='security_alerts'),
    path('security/audit/', views.security_audit, name='security_audit'),
    path('api/users/search/', views.api_user_search, name='api_user_search'),
    path('api/platform/stats/', views.api_platform_stats, name='api_platform_stats'),
    path('api/security/threats/', views.api_security_threats, name='api_security_threats'),
]