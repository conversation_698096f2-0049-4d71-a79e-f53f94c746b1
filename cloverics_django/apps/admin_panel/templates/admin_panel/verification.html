{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <h1><i class="fas fa-user-check"></i> Verification Requests</h1>
                <p class="text-muted">Review and approve user verification requests</p>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5><i class="fas fa-list"></i> Pending Verifications</h5>
                    <div class="btn-group">
                        <button class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-filter"></i> Filter
                        </button>
                        <button class="btn btn-outline-success btn-sm">
                            <i class="fas fa-check-double"></i> Approve All
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    {% if requests %}
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Email</th>
                                        <th>User Type</th>
                                        <th>Company</th>
                                        <th>Request Date</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for request in requests %}
                                    <tr>
                                        <td>{{ request.id }}</td>
                                        <td>
                                            <strong>{{ request.email }}</strong>
                                        </td>
                                        <td>
                                            {% if request.user_type == 'CUSTOMER' %}
                                                <span class="badge badge-primary">Customer</span>
                                            {% elif request.user_type == 'LOGISTICS_PROVIDER' %}
                                                <span class="badge badge-info">Logistics Provider</span>
                                            {% elif request.user_type == 'INDEPENDENT_DRIVER' %}
                                                <span class="badge badge-warning">Independent Driver</span>
                                            {% elif request.user_type == 'INSURANCE_PROVIDER' %}
                                                <span class="badge badge-success">Insurance Provider</span>
                                            {% elif request.user_type == 'CUSTOMS_AGENT' %}
                                                <span class="badge badge-secondary">Customs Agent</span>
                                            {% else %}
                                                <span class="badge badge-dark">{{ request.user_type }}</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ request.company_name }}</td>
                                        <td>{{ request.request_date }}</td>
                                        <td>
                                            {% if request.status == 'Pending' %}
                                                <span class="badge badge-warning">Pending</span>
                                            {% elif request.status == 'Approved' %}
                                                <span class="badge badge-success">Approved</span>
                                            {% elif request.status == 'Rejected' %}
                                                <span class="badge badge-danger">Rejected</span>
                                            {% else %}
                                                <span class="badge badge-secondary">{{ request.status }}</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-info" title="View Details">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button class="btn btn-outline-success" title="Approve">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                                <button class="btn btn-outline-danger" title="Reject">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="empty-state text-center py-5">
                            <i class="fas fa-user-check fa-3x text-muted mb-3"></i>
                            <h5>No pending verification requests</h5>
                            <p class="text-muted">All users are verified or no new requests</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 class="text-warning">{{ requests|length }}</h3>
                    <p class="card-text">Pending Requests</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 class="text-success">
                        {{ approved_count }}
                    </h3>
                    <p class="card-text">Approved Today</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 class="text-danger">
                        {{ rejected_count }}
                    </h3>
                    <p class="card-text">Rejected</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 class="text-primary">
                        {{ logistics_count }}
                    </h3>
                    <p class="card-text">Logistics Providers</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}