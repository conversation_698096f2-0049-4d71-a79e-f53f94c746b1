{% extends 'base.html' %}

{% block title %}Admin Dashboard - Cloverics{% endblock %}

{% block content %}
<div class="main-content">
    <div class="container-fluid">
        <!-- Dashboard Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1>Admin Dashboard</h1>
                        <p class="text-muted">Platform Administration & System Overview</p>
                    </div>
                    <div class="text-end">
                        <span class="badge bg-danger fs-6">{{ user.user_type }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Platform Statistics -->
        <div class="row mb-4">
            <div class="col-lg-2 col-md-4 mb-3">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-users fa-2x mb-2"></i>
                        <h4>{{ platform_stats.total_users }}</h4>
                        <p class="mb-0">Total Users</p>
                    </div>
                </div>
            </div>

            <div class="col-lg-2 col-md-4 mb-3">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-truck fa-2x mb-2"></i>
                        <h4>{{ platform_stats.logistics_providers }}</h4>
                        <p class="mb-0">Logistics Providers</p>
                    </div>
                </div>
            </div>

            <div class="col-lg-2 col-md-4 mb-3">
                <div class="card bg-info text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-shipping-fast fa-2x mb-2"></i>
                        <h4>{{ platform_stats.active_shipments }}</h4>
                        <p class="mb-0">Active Shipments</p>
                    </div>
                </div>
            </div>

            <div class="col-lg-2 col-md-4 mb-3">
                <div class="card bg-warning text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-dollar-sign fa-2x mb-2"></i>
                        <h4>${{ platform_stats.monthly_transactions }}</h4>
                        <p class="mb-0">Monthly Revenue</p>
                    </div>
                </div>
            </div>

            <div class="col-lg-2 col-md-4 mb-3">
                <div class="card bg-secondary text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-server fa-2x mb-2"></i>
                        <h4>{{ platform_stats.system_health }}%</h4>
                        <p class="mb-0">System Health</p>
                    </div>
                </div>
            </div>

            <div class="col-lg-2 col-md-4 mb-3">
                <div class="card bg-danger text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-headset fa-2x mb-2"></i>
                        <h4>{{ platform_stats.support_tickets }}</h4>
                        <p class="mb-0">Support Tickets</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Administration Tools</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 mb-2">
                                <a href="/admin/users" class="btn btn-primary w-100">
                                    <i class="fas fa-users me-2"></i>User Management
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="/admin/verification" class="btn btn-success w-100">
                                    <i class="fas fa-check-circle me-2"></i>Verification Queue
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="/admin/support-tickets" class="btn btn-info w-100">
                                    <i class="fas fa-headset me-2"></i>Support Tickets
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="/admin/system" class="btn btn-warning w-100">
                                    <i class="fas fa-server me-2"></i>System Management
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activities -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Recent Platform Activities</h5>
                    </div>
                    <div class="card-body">
                        {% for activity in recent_activities %}
                        <div class="d-flex justify-content-between align-items-center border-bottom pb-2 mb-2">
                            <div>
                                <strong>{{ activity.action }}</strong><br>
                                {% if activity.user %}
                                <small class="text-muted">User: {{ activity.user }}</small>
                                {% endif %}
                                {% if activity.details %}
                                <small class="text-muted">{{ activity.details }}</small>
                                {% endif %}
                            </div>
                            <div class="text-end">
                                <small class="text-muted">{{ activity.timestamp }}</small>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}