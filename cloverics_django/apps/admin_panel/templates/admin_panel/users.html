{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <h1><i class="fas fa-users-cog"></i> User Management</h1>
                <p class="text-muted">Manage platform users and account settings</p>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5><i class="fas fa-list"></i> Platform Users</h5>
                    <div class="btn-group">
                        <button class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-filter"></i> Filter
                        </button>
                        <button class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-search"></i> Search
                        </button>
                        <button class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-download"></i> Export
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    {% if users %}
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Email</th>
                                        <th>User Type</th>
                                        <th>Company</th>
                                        <th>Verification</th>
                                        <th>Joined</th>
                                        <th>Last Login</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for user in users %}
                                    <tr>
                                        <td>{{ user.id }}</td>
                                        <td>
                                            <strong>{{ user.email }}</strong>
                                        </td>
                                        <td>
                                            {% if user.user_type == 'CUSTOMER' %}
                                                <span class="badge badge-primary">Customer</span>
                                            {% elif user.user_type == 'LOGISTICS_PROVIDER' %}
                                                <span class="badge badge-success">Logistics</span>
                                            {% elif user.user_type == 'ADMIN' %}
                                                <span class="badge badge-danger">Admin</span>
                                            {% else %}
                                                <span class="badge badge-secondary">{{ user.user_type }}</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ user.company_name or 'N/A' }}</td>
                                        <td>
                                            {% if user.is_verified %}
                                                <span class="badge badge-success">
                                                    <i class="fas fa-check"></i> Verified
                                                </span>
                                            {% else %}
                                                <span class="badge badge-warning">
                                                    <i class="fas fa-clock"></i> Pending
                                                </span>
                                            {% endif %}
                                        </td>
                                        <td>{{ user.created_date }}</td>
                                        <td>{{ user.last_login }}</td>
                                        <td>
                                            {% if user.status == 'Active' %}
                                                <span class="badge badge-success">Active</span>
                                            {% elif user.status == 'Suspended' %}
                                                <span class="badge badge-danger">Suspended</span>
                                            {% else %}
                                                <span class="badge badge-secondary">{{ user.status }}</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary" title="View Profile">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button class="btn btn-outline-warning" title="Edit User">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                {% if user.status == 'Active' %}
                                                <button class="btn btn-outline-danger" title="Suspend User">
                                                    <i class="fas fa-ban"></i>
                                                </button>
                                                {% else %}
                                                <button class="btn btn-outline-success" title="Activate User">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                                {% endif %}
                                                <button class="btn btn-outline-info" title="Send Message">
                                                    <i class="fas fa-envelope"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="empty-state text-center py-5">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <h5>No users found</h5>
                            <p class="text-muted">Users will appear here as they register</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 class="text-primary">{{ users|length }}</h3>
                    <p class="card-text">Total Users</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 class="text-success">
                        {{ verified_count }}
                    </h3>
                    <p class="card-text">Verified</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 class="text-info">
                        {{ customer_count }}
                    </h3>
                    <p class="card-text">Customers</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 class="text-warning">
                        {{ logistics_count }}
                    </h3>
                    <p class="card-text">Logistics Providers</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}