{% extends "base.html" %}

{% block title %}System Overview - Admin - Cloverics{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-chart-line text-primary"></i> System Overview</h2>
                <div class="btn-group" role="group">
                    <button class="btn btn-outline-primary" onclick="refreshData()">
                        <i class="fas fa-sync"></i> Refresh
                    </button>
                    <button class="btn btn-outline-secondary" onclick="exportData()">
                        <i class="fas fa-download"></i> Export
                    </button>
                </div>
            </div>

            <!-- System Status Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>{{ system_stats.uptime }}</h4>
                                    <p class="mb-0">System Uptime</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-server fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>{{ system_stats.active_users }}</h4>
                                    <p class="mb-0">Active Users</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-users fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>{{ system_stats.pending_approvals }}</h4>
                                    <p class="mb-0">Pending Approvals</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-clock fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>{{ system_stats.total_shipments }}</h4>
                                    <p class="mb-0">Total Shipments</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-shipping-fast fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- System Performance Charts -->
            <div class="row mb-4">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">System Performance Metrics</h5>
                        </div>
                        <div class="card-body">
                            <canvas id="performanceChart" height="300"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Resource Usage</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label class="form-label">CPU Usage</label>
                                <div class="progress">
                                    <div class="progress-bar bg-success" style="width: {{ system_stats.cpu_usage }}%">
                                        {{ system_stats.cpu_usage }}%
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Memory Usage</label>
                                <div class="progress">
                                    <div class="progress-bar bg-info" style="width: {{ system_stats.memory_usage }}%">
                                        {{ system_stats.memory_usage }}%
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Disk Usage</label>
                                <div class="progress">
                                    <div class="progress-bar bg-warning" style="width: {{ system_stats.disk_usage }}%">
                                        {{ system_stats.disk_usage }}%
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent System Events -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Recent System Events</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Time</th>
                                            <th>Event Type</th>
                                            <th>Description</th>
                                            <th>Status</th>
                                            <th>User</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for event in recent_events %}
                                        <tr>
                                            <td>{{ event.timestamp|date:"M d, H:i" }}</td>
                                            <td>
                                                <span class="badge bg-{% if event.type == 'error' %}danger{% elif event.type == 'warning' %}warning{% else %}primary{% endif %}">
                                                    {{ event.type|title }}
                                                </span>
                                            </td>
                                            <td>{{ event.description }}</td>
                                            <td>
                                                <span class="badge bg-{% if event.status == 'resolved' %}success{% elif event.status == 'pending' %}warning{% else %}danger{% endif %}">
                                                    {{ event.status|title }}
                                                </span>
                                            </td>
                                            <td>{{ event.user|default:"System" }}</td>
                                        </tr>
                                        {% empty %}
                                        <tr>
                                            <td colspan="5" class="text-center py-4">
                                                <i class="fas fa-check-circle fa-2x text-success mb-2"></i><br>
                                                No recent events - System running smoothly
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Initialize performance chart
const ctx = document.getElementById('performanceChart').getContext('2d');
const performanceChart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00', '24:00'],
        datasets: [{
            label: 'Response Time (ms)',
            data: [120, 110, 95, 140, 160, 130, 105],
            borderColor: 'rgb(75, 192, 192)',
            tension: 0.1
        }, {
            label: 'Active Users',
            data: [45, 30, 55, 85, 95, 70, 50],
            borderColor: 'rgb(255, 99, 132)',
            tension: 0.1
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

function refreshData() {
    Swal.fire({
        title: 'Refreshing Data',
        text: 'Loading latest system metrics...',
        allowOutsideClick: false,
        didOpen: () => {
            Swal.showLoading();
        }
    });
    
    setTimeout(() => {
        Swal.fire('Updated!', 'System data has been refreshed.', 'success');
        location.reload();
    }, 2000);
}

function exportData() {
    Swal.fire({
        title: 'Export System Data',
        text: 'Generating system overview report...',
        icon: 'info'
    });
}
</script>
{% endblock %}