{% extends "base.html" %}

{% block title %}Reports - Admin - Cloverics{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-chart-bar text-primary"></i> Admin Reports</h2>
                <button class="btn btn-primary" onclick="generateCustomReport()">
                    <i class="fas fa-plus"></i> Generate Custom Report
                </button>
            </div>

            <!-- Report Categories -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card border-primary">
                        <div class="card-body text-center">
                            <i class="fas fa-users fa-3x text-primary mb-3"></i>
                            <h5>User Reports</h5>
                            <p class="text-muted">Registration, verification, activity</p>
                            <button class="btn btn-primary" onclick="showUserReports()">View Reports</button>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-success">
                        <div class="card-body text-center">
                            <i class="fas fa-shipping-fast fa-3x text-success mb-3"></i>
                            <h5>Shipment Reports</h5>
                            <p class="text-muted">Volume, revenue, performance</p>
                            <button class="btn btn-success" onclick="showShipmentReports()">View Reports</button>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-info">
                        <div class="card-body text-center">
                            <i class="fas fa-dollar-sign fa-3x text-info mb-3"></i>
                            <h5>Financial Reports</h5>
                            <p class="text-muted">Revenue, fees, payments</p>
                            <button class="btn btn-info" onclick="showFinancialReports()">View Reports</button>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-warning">
                        <div class="card-body text-center">
                            <i class="fas fa-cog fa-3x text-warning mb-3"></i>
                            <h5>System Reports</h5>
                            <p class="text-muted">Performance, errors, usage</p>
                            <button class="btn btn-warning" onclick="showSystemReports()">View Reports</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Stats Dashboard -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Platform Overview - Last 30 Days</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-2">
                                    <div class="text-center">
                                        <h3 class="text-primary">{{ stats.new_users }}</h3>
                                        <p class="mb-0">New Users</p>
                                        <small class="text-success">+{{ stats.user_growth }}%</small>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="text-center">
                                        <h3 class="text-success">{{ stats.completed_shipments }}</h3>
                                        <p class="mb-0">Completed Shipments</p>
                                        <small class="text-success">+{{ stats.shipment_growth }}%</small>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="text-center">
                                        <h3 class="text-info">${{ stats.total_revenue }}</h3>
                                        <p class="mb-0">Revenue</p>
                                        <small class="text-success">+{{ stats.revenue_growth }}%</small>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="text-center">
                                        <h3 class="text-warning">{{ stats.active_providers }}</h3>
                                        <p class="mb-0">Active Providers</p>
                                        <small class="text-success">+{{ stats.provider_growth }}%</small>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="text-center">
                                        <h3 class="text-danger">{{ stats.avg_response_time }}ms</h3>
                                        <p class="mb-0">Avg Response</p>
                                        <small class="text-success">-5%</small>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="text-center">
                                        <h3 class="text-secondary">{{ stats.satisfaction_rate }}%</h3>
                                        <p class="mb-0">Satisfaction</p>
                                        <small class="text-success">+{{ stats.satisfaction_growth }}%</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Reports -->
            <div class="row">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Recent Reports</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Report Name</th>
                                            <th>Type</th>
                                            <th>Generated</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for report in recent_reports %}
                                        <tr>
                                            <td>{{ report.name }}</td>
                                            <td>
                                                <span class="badge bg-{% if report.type == 'financial' %}success{% elif report.type == 'user' %}primary{% elif report.type == 'system' %}warning{% else %}info{% endif %}">
                                                    {{ report.type|title }}
                                                </span>
                                            </td>
                                            <td>{{ report.generated_at|date:"M d, Y H:i" }}</td>
                                            <td>
                                                <span class="badge bg-{% if report.status == 'completed' %}success{% elif report.status == 'processing' %}warning{% else %}danger{% endif %}">
                                                    {{ report.status|title }}
                                                </span>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <button class="btn btn-sm btn-primary" onclick="viewReport({{ report.id }})">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-success" onclick="downloadReport({{ report.id }})">
                                                        <i class="fas fa-download"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-secondary" onclick="shareReport({{ report.id }})">
                                                        <i class="fas fa-share"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        {% empty %}
                                        <tr>
                                            <td colspan="5" class="text-center py-4">
                                                <i class="fas fa-chart-bar fa-2x text-muted mb-2"></i><br>
                                                No reports generated yet
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Quick Actions</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <button class="btn btn-primary" onclick="generateMonthlyReport()">
                                    <i class="fas fa-calendar"></i> Monthly Summary
                                </button>
                                <button class="btn btn-success" onclick="generateRevenueReport()">
                                    <i class="fas fa-dollar-sign"></i> Revenue Analysis
                                </button>
                                <button class="btn btn-info" onclick="generateUserReport()">
                                    <i class="fas fa-users"></i> User Analytics
                                </button>
                                <button class="btn btn-warning" onclick="generatePerformanceReport()">
                                    <i class="fas fa-tachometer-alt"></i> Performance Report
                                </button>
                                <button class="btn btn-secondary" onclick="exportAllData()">
                                    <i class="fas fa-database"></i> Export All Data
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card mt-3">
                        <div class="card-header">
                            <h5 class="mb-0">Report Schedule</h5>
                        </div>
                        <div class="card-body">
                            <ul class="list-group list-group-flush">
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>Weekly Summary</span>
                                    <span class="badge bg-success">Active</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>Monthly Financial</span>
                                    <span class="badge bg-success">Active</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>Quarterly Review</span>
                                    <span class="badge bg-warning">Pending</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function showUserReports() {
    Swal.fire({
        title: 'User Reports',
        html: `
            <div class="text-start">
                <h6>Available User Reports:</h6>
                <ul class="list-unstyled">
                    <li><i class="fas fa-file-alt text-primary"></i> Registration Trends</li>
                    <li><i class="fas fa-file-alt text-primary"></i> User Activity Analysis</li>
                    <li><i class="fas fa-file-alt text-primary"></i> Verification Status Report</li>
                    <li><i class="fas fa-file-alt text-primary"></i> User Type Distribution</li>
                </ul>
            </div>
        `,
        width: 600,
        showCloseButton: true,
        confirmButtonText: 'Generate Report'
    });
}

function showShipmentReports() {
    Swal.fire({
        title: 'Shipment Reports',
        html: `
            <div class="text-start">
                <h6>Available Shipment Reports:</h6>
                <ul class="list-unstyled">
                    <li><i class="fas fa-file-alt text-success"></i> Shipment Volume Analysis</li>
                    <li><i class="fas fa-file-alt text-success"></i> Route Performance</li>
                    <li><i class="fas fa-file-alt text-success"></i> Delivery Time Analysis</li>
                    <li><i class="fas fa-file-alt text-success"></i> Provider Performance</li>
                </ul>
            </div>
        `,
        width: 600,
        showCloseButton: true,
        confirmButtonText: 'Generate Report'
    });
}

function showFinancialReports() {
    Swal.fire({
        title: 'Financial Reports',
        html: `
            <div class="text-start">
                <h6>Available Financial Reports:</h6>
                <ul class="list-unstyled">
                    <li><i class="fas fa-file-alt text-info"></i> Revenue Summary</li>
                    <li><i class="fas fa-file-alt text-info"></i> Platform Fees Analysis</li>
                    <li><i class="fas fa-file-alt text-info"></i> Payment Processing</li>
                    <li><i class="fas fa-file-alt text-info"></i> Financial Forecasting</li>
                </ul>
            </div>
        `,
        width: 600,
        showCloseButton: true,
        confirmButtonText: 'Generate Report'
    });
}

function showSystemReports() {
    Swal.fire({
        title: 'System Reports',
        html: `
            <div class="text-start">
                <h6>Available System Reports:</h6>
                <ul class="list-unstyled">
                    <li><i class="fas fa-file-alt text-warning"></i> Performance Metrics</li>
                    <li><i class="fas fa-file-alt text-warning"></i> Error Log Analysis</li>
                    <li><i class="fas fa-file-alt text-warning"></i> Usage Statistics</li>
                    <li><i class="fas fa-file-alt text-warning"></i> Security Audit</li>
                </ul>
            </div>
        `,
        width: 600,
        showCloseButton: true,
        confirmButtonText: 'Generate Report'
    });
}

function generateCustomReport() {
    Swal.fire({
        title: 'Create Custom Report',
        html: `
            <form id="customReportForm">
                <div class="mb-3 text-start">
                    <label class="form-label">Report Name</label>
                    <input type="text" class="form-control" id="reportName" placeholder="Enter report name">
                </div>
                <div class="mb-3 text-start">
                    <label class="form-label">Report Type</label>
                    <select class="form-select" id="reportType">
                        <option value="user">User Analytics</option>
                        <option value="shipment">Shipment Analysis</option>
                        <option value="financial">Financial Report</option>
                        <option value="system">System Performance</option>
                    </select>
                </div>
                <div class="mb-3 text-start">
                    <label class="form-label">Date Range</label>
                    <select class="form-select" id="dateRange">
                        <option value="7">Last 7 days</option>
                        <option value="30">Last 30 days</option>
                        <option value="90">Last 3 months</option>
                        <option value="365">Last year</option>
                    </select>
                </div>
            </form>
        `,
        width: 500,
        showCancelButton: true,
        confirmButtonText: 'Generate Report',
        preConfirm: () => {
            return {
                name: document.getElementById('reportName').value,
                type: document.getElementById('reportType').value,
                range: document.getElementById('dateRange').value
            }
        }
    }).then((result) => {
        if (result.isConfirmed) {
            Swal.fire('Report Generated!', 'Your custom report is being generated.', 'success');
        }
    });
}

function generateMonthlyReport() {
    Swal.fire({
        title: 'Generating Monthly Report',
        text: 'Please wait while we compile your monthly summary...',
        allowOutsideClick: false,
        didOpen: () => {
            Swal.showLoading();
        }
    });
    
    setTimeout(() => {
        Swal.fire('Report Ready!', 'Monthly summary report has been generated.', 'success');
    }, 2000);
}

function generateRevenueReport() {
    Swal.fire('Revenue Report', 'Revenue analysis report is being generated.', 'info');
}

function generateUserReport() {
    Swal.fire('User Report', 'User analytics report is being generated.', 'info');
}

function generatePerformanceReport() {
    Swal.fire('Performance Report', 'System performance report is being generated.', 'info');
}

function exportAllData() {
    Swal.fire({
        title: 'Export All Data',
        text: 'This will export all platform data. Continue?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'Yes, export all!'
    }).then((result) => {
        if (result.isConfirmed) {
            Swal.fire('Export Started!', 'Data export has begun. You will receive a download link.', 'success');
        }
    });
}

function viewReport(reportId) {
    Swal.fire({
        title: 'Report Viewer',
        html: '<div class="text-center"><i class="fas fa-file-alt fa-3x text-primary mb-3"></i><br>Loading report...</div>',
        width: 800,
        showCloseButton: true,
        showConfirmButton: false
    });
}

function downloadReport(reportId) {
    Swal.fire('Download Started', 'Report download has begun.', 'success');
}

function shareReport(reportId) {
    Swal.fire({
        title: 'Share Report',
        input: 'email',
        inputLabel: 'Email address:',
        inputPlaceholder: 'Enter email to share report',
        showCancelButton: true,
        confirmButtonText: 'Share'
    }).then((result) => {
        if (result.isConfirmed) {
            Swal.fire('Report Shared!', 'Report has been shared successfully.', 'success');
        }
    });
}
</script>
{% endblock %}