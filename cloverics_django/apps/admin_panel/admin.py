# Admin panel admin configuration
from django.contrib import admin
from .models import SystemStatistics, UserVerification, SupportTicket, TicketResponse, SystemAlert, AdminReport

@admin.register(SystemStatistics)
class SystemStatisticsAdmin(admin.ModelAdmin):
    list_display = ['date', 'total_users', 'total_shipments', 'system_uptime', 'cpu_usage', 'memory_usage']
    list_filter = ['date']
    readonly_fields = ['date']
    ordering = ['-date']

@admin.register(UserVerification)
class UserVerificationAdmin(admin.ModelAdmin):
    list_display = ['user', 'status', 'priority', 'submitted_at', 'reviewed_by']
    list_filter = ['status', 'priority', 'submitted_at']
    search_fields = ['user__email', 'user__company_name']
    readonly_fields = ['submitted_at']
    list_editable = ['status', 'priority']
    
    fieldsets = (
        ('User Information', {
            'fields': ('user', 'status', 'priority')
        }),
        ('Documents', {
            'fields': ('business_license', 'insurance_certificate', 'tax_registration', 'identity_document')
        }),
        ('Review Information', {
            'fields': ('reviewed_by', 'reviewed_at', 'notes', 'rejection_reason')
        }),
        ('Timestamps', {
            'fields': ('submitted_at',),
            'classes': ('collapse',)
        })
    )

class TicketResponseInline(admin.TabularInline):
    model = TicketResponse
    extra = 0
    readonly_fields = ['created_at']

@admin.register(SupportTicket)
class SupportTicketAdmin(admin.ModelAdmin):
    list_display = ['ticket_id', 'user', 'category', 'priority', 'status', 'created_at', 'assigned_to']
    list_filter = ['status', 'priority', 'category', 'created_at']
    search_fields = ['ticket_id', 'user__email', 'subject']
    readonly_fields = ['ticket_id', 'created_at', 'updated_at']
    list_editable = ['status', 'priority', 'assigned_to']
    inlines = [TicketResponseInline]
    
    fieldsets = (
        ('Ticket Information', {
            'fields': ('ticket_id', 'user', 'category', 'priority', 'status')
        }),
        ('Content', {
            'fields': ('subject', 'description', 'attachment')
        }),
        ('Assignment', {
            'fields': ('assigned_to', 'resolved_at')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

@admin.register(SystemAlert)
class SystemAlertAdmin(admin.ModelAdmin):
    list_display = ['title', 'alert_type', 'source', 'is_resolved', 'created_at', 'resolved_by']
    list_filter = ['alert_type', 'is_resolved', 'created_at']
    search_fields = ['title', 'description']
    readonly_fields = ['created_at']
    list_editable = ['is_resolved']

@admin.register(AdminReport)
class AdminReportAdmin(admin.ModelAdmin):
    list_display = ['title', 'report_type', 'generated_by', 'generated_at', 'date_from', 'date_to']
    list_filter = ['report_type', 'generated_at']
    search_fields = ['title', 'description']
    readonly_fields = ['generated_at']
    
    fieldsets = (
        ('Report Information', {
            'fields': ('report_type', 'title', 'description')
        }),
        ('Date Range', {
            'fields': ('date_from', 'date_to')
        }),
        ('Generated Info', {
            'fields': ('generated_by', 'generated_at', 'file_path')
        }),
        ('Data', {
            'fields': ('data',),
            'classes': ('collapse',)
        })
    )