# Admin panel models extracted from fastapi_main.py
from django.db import models
from django.conf import settings
from django.contrib.auth import get_user_model
from django.utils import timezone

User = get_user_model()

class SystemStatistics(models.Model):
    """System statistics model - extracted from admin functionality"""
    date = models.DateField(default=timezone.now)
    total_users = models.IntegerField(default=0)
    total_shipments = models.IntegerField(default=0)
    total_payments = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    active_sessions = models.IntegerField(default=0)
    system_uptime = models.DecimalField(max_digits=5, decimal_places=2, default=0)  # percentage
    cpu_usage = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    memory_usage = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    disk_usage = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    
    class Meta:
        db_table = 'admin_system_statistics'
        ordering = ['-date']
        verbose_name = 'System Statistics'
        verbose_name_plural = 'System Statistics'
    
    def __str__(self):
        return f"System Stats - {self.date}"

class UserVerification(models.Model):
    """User verification model - extracted from admin verification queue"""
    STATUS_CHOICES = [
        ('PENDING', 'Pending Review'),
        ('APPROVED', 'Approved'),
        ('REJECTED', 'Rejected'),
        ('UNDER_REVIEW', 'Under Review'),
    ]
    
    PRIORITY_CHOICES = [
        ('LOW', 'Low'),
        ('MEDIUM', 'Medium'),
        ('HIGH', 'High'),
        ('URGENT', 'Urgent'),
    ]
    
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='verification')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='PENDING')
    priority = models.CharField(max_length=10, choices=PRIORITY_CHOICES, default='MEDIUM')
    
    # Documents
    business_license = models.FileField(upload_to='verifications/licenses/', null=True, blank=True)
    insurance_certificate = models.FileField(upload_to='verifications/insurance/', null=True, blank=True)
    tax_registration = models.FileField(upload_to='verifications/tax/', null=True, blank=True)
    identity_document = models.FileField(upload_to='verifications/identity/', null=True, blank=True)
    
    # Verification details
    submitted_at = models.DateTimeField(default=timezone.now)
    reviewed_at = models.DateTimeField(null=True, blank=True)
    reviewed_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True, related_name='verifications_reviewed')
    
    notes = models.TextField(blank=True, help_text="Admin notes about verification")
    rejection_reason = models.TextField(blank=True)
    
    class Meta:
        db_table = 'admin_user_verification'
        ordering = ['-submitted_at']
    
    def __str__(self):
        return f"Verification for {self.user.email} - {self.status}"

class SupportTicket(models.Model):
    """Support ticket model - extracted from admin support functionality"""
    STATUS_CHOICES = [
        ('OPEN', 'Open'),
        ('IN_PROGRESS', 'In Progress'),
        ('PENDING_CUSTOMER', 'Pending Customer Response'),
        ('RESOLVED', 'Resolved'),
        ('CLOSED', 'Closed'),
    ]
    
    PRIORITY_CHOICES = [
        ('LOW', 'Low'),
        ('MEDIUM', 'Medium'),
        ('HIGH', 'High'),
        ('CRITICAL', 'Critical'),
    ]
    
    CATEGORY_CHOICES = [
        ('TECHNICAL', 'Technical Issue'),
        ('BILLING', 'Billing Inquiry'),
        ('ACCOUNT', 'Account Support'),
        ('SHIPPING', 'Shipping Issue'),
        ('GENERAL', 'General Question'),
        ('FEATURE_REQUEST', 'Feature Request'),
        ('BUG_REPORT', 'Bug Report'),
    ]
    
    ticket_id = models.CharField(max_length=20, unique=True)
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='support_tickets')
    category = models.CharField(max_length=20, choices=CATEGORY_CHOICES)
    priority = models.CharField(max_length=10, choices=PRIORITY_CHOICES, default='MEDIUM')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='OPEN')
    
    subject = models.CharField(max_length=200)
    description = models.TextField()
    
    # Assignment
    assigned_to = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True, related_name='assigned_tickets')
    
    # Timestamps
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    resolved_at = models.DateTimeField(null=True, blank=True)
    
    # Attachments
    attachment = models.FileField(upload_to='support/attachments/', null=True, blank=True)
    
    class Meta:
        db_table = 'admin_support_ticket'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"Ticket {self.ticket_id} - {self.subject}"

class TicketResponse(models.Model):
    """Support ticket response model"""
    ticket = models.ForeignKey(SupportTicket, on_delete=models.CASCADE, related_name='responses')
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    message = models.TextField()
    is_staff_response = models.BooleanField(default=False)
    
    created_at = models.DateTimeField(default=timezone.now)
    attachment = models.FileField(upload_to='support/responses/', null=True, blank=True)
    
    class Meta:
        db_table = 'admin_ticket_response'
        ordering = ['created_at']
    
    def __str__(self):
        return f"Response to {self.ticket.ticket_id} by {self.user.email}"

class SystemAlert(models.Model):
    """System alerts model - extracted from admin monitoring"""
    ALERT_TYPE_CHOICES = [
        ('INFO', 'Information'),
        ('WARNING', 'Warning'),
        ('ERROR', 'Error'),
        ('CRITICAL', 'Critical'),
    ]
    
    alert_type = models.CharField(max_length=10, choices=ALERT_TYPE_CHOICES)
    title = models.CharField(max_length=200)
    description = models.TextField()
    source = models.CharField(max_length=100, default='System')
    
    is_resolved = models.BooleanField(default=False)
    resolved_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True, related_name='resolved_alerts')
    resolved_at = models.DateTimeField(null=True, blank=True)
    
    created_at = models.DateTimeField(default=timezone.now)
    
    class Meta:
        db_table = 'admin_system_alert'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.alert_type}: {self.title}"

class AdminReport(models.Model):
    """Admin report model - extracted from admin reports functionality"""
    REPORT_TYPE_CHOICES = [
        ('USER_ACTIVITY', 'User Activity Report'),
        ('FINANCIAL', 'Financial Report'),
        ('SHIPMENT_ANALYTICS', 'Shipment Analytics'),
        ('SYSTEM_PERFORMANCE', 'System Performance'),
        ('SECURITY_AUDIT', 'Security Audit'),
        ('COMPLIANCE', 'Compliance Report'),
    ]
    
    report_type = models.CharField(max_length=30, choices=REPORT_TYPE_CHOICES)
    title = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    
    # Report data
    data = models.JSONField(default=dict)
    file_path = models.FileField(upload_to='admin/reports/', null=True, blank=True)
    
    # Generation info
    generated_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='generated_reports')
    generated_at = models.DateTimeField(default=timezone.now)
    
    # Date range for report
    date_from = models.DateField(null=True, blank=True)
    date_to = models.DateField(null=True, blank=True)
    
    class Meta:
        db_table = 'admin_report'
        ordering = ['-generated_at']
    
    def __str__(self):
        return f"{self.report_type} - {self.generated_at.date()}"