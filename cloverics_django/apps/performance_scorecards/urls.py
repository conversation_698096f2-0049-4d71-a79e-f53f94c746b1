"""
Performance Scorecards URL Configuration
Extracted from FastAPI performance scorecards endpoints
"""

from django.urls import path
from . import views

app_name = 'performance_scorecards'

urlpatterns = [
    # Dashboard page
    path('', views.performance_scorecards_page, name='dashboard'),
    
    # API endpoints (extracted from FastAPI)
    path('api/scorecard/<int:provider_id>/', views.get_provider_scorecard, name='provider_scorecard'),
    path('api/scorecard/my-performance/', views.get_my_scorecard, name='my_scorecard'),
    path('api/scorecard/top-performers/', views.get_top_performers, name='top_performers'),
    
    # Final completion: Additional scorecard endpoints
    path('analytics/', views.scorecard_analytics, name='scorecard_analytics'),
    path('reports/', views.scorecard_reports, name='scorecard_reports'),
    path('api/platform-summary/', views.get_platform_summary, name='platform_summary'),
]