"""
Performance Scorecards Views
Extracted from FastAPI - Lines 9124-9400+ (Complete performance scorecards system)
"""

from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.contrib.auth import get_user_model
from django.db.models import Avg, Count, Q
from datetime import datetime, timedelta
import json

from .models import ProviderScorecard, PerformanceAlert, ScorecardMetric

User = get_user_model()

@login_required
def performance_scorecards_page(request):
    """Display comprehensive performance scorecards for logistics providers"""
    if request.user.user_type != 'LOGISTICS_PROVIDER':
        return redirect('/dashboard')
    
    context = {
        "user": request.user,
        "title": "Performance Scorecards - Cloverics"
    }
    return render(request, 'logistics/performance_scorecards.html', context)

@require_http_methods(["GET"])
@login_required
def get_provider_scorecard(request, provider_id):
    """
    Get comprehensive performance scorecard for a logistics provider
    Extracted from FastAPI @app.get("/api/scorecard/{provider_id}")
    """
    try:
        days_back = int(request.GET.get('days_back', 90))
        
        # Get the most recent scorecard for the provider
        scorecard = ProviderScorecard.objects.filter(
            provider_id=provider_id,
            days_analyzed=days_back
        ).order_by('-created_at').first()
        
        if not scorecard:
            # Generate new scorecard (simplified - in production would use complex engine)
            scorecard = _generate_scorecard(provider_id, days_back)
            if not scorecard:
                return JsonResponse({
                    'success': False,
                    'error': 'Provider not found or insufficient data'
                }, status_code=404)
        
        # Get alerts
        alerts = PerformanceAlert.objects.filter(
            scorecard=scorecard,
            is_acknowledged=False
        ).order_by('-severity', '-created_at')
        
        alerts_data = [{
            'alert_id': str(alert.alert_id),
            'type': alert.alert_type,
            'severity': alert.severity,
            'message': alert.message,
            'metric_value': float(alert.metric_value),
            'threshold_value': float(alert.threshold_value),
            'created_at': alert.created_at.isoformat()
        } for alert in alerts]
        
        scorecard_data = {
            'scorecard_id': str(scorecard.scorecard_id),
            'provider': {
                'id': scorecard.provider.id,
                'email': scorecard.provider.email,
                'company_name': getattr(scorecard.provider, 'company_name', '')
            },
            'period': {
                'start': scorecard.period_start.isoformat(),
                'end': scorecard.period_end.isoformat(),
                'days_analyzed': scorecard.days_analyzed
            },
            'scores': {
                'overall': float(scorecard.overall_score),
                'on_time_delivery': float(scorecard.on_time_delivery_score),
                'customer_rating': float(scorecard.customer_rating_score),
                'response_time': float(scorecard.response_time_score),
                'completion_rate': float(scorecard.completion_rate_score),
                'pricing_competitiveness': float(scorecard.pricing_competitiveness_score),
                'communication_quality': float(scorecard.communication_quality_score),
                'reliability': float(scorecard.reliability_score)
            },
            'metrics': {
                'total_shipments': scorecard.total_shipments,
                'on_time_deliveries': scorecard.on_time_deliveries,
                'avg_customer_rating': float(scorecard.avg_customer_rating),
                'avg_response_time_hours': float(scorecard.avg_response_time_hours),
                'completion_rate_percent': float(scorecard.completion_rate_percent)
            },
            'performance_grade': scorecard.performance_grade,
            'alerts': alerts_data
        }
        
        return JsonResponse({
            'success': True,
            'scorecard': scorecard_data
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': f'Error generating scorecard: {str(e)}'
        }, status_code=500)

@require_http_methods(["GET"])
@login_required
def get_my_scorecard(request):
    """
    Get current logistics provider's performance scorecard
    Extracted from FastAPI @app.get("/api/scorecard/my-performance")
    """
    if request.user.user_type != 'LOGISTICS_PROVIDER':
        return JsonResponse({
            'success': False,
            'error': 'Only logistics providers can access scorecards'
        }, status_code=403)
    
    try:
        days_back = int(request.GET.get('days_back', 90))
        
        # Get user's scorecard
        scorecard = ProviderScorecard.objects.filter(
            provider=request.user,
            days_analyzed=days_back
        ).order_by('-created_at').first()
        
        if not scorecard:
            scorecard = _generate_scorecard(request.user.id, days_back)
            if not scorecard:
                return JsonResponse({
                    'success': False,
                    'error': 'Insufficient data to generate scorecard'
                }, status_code=404)
        
        # Get alerts
        alerts = PerformanceAlert.objects.filter(
            scorecard=scorecard,
            is_acknowledged=False
        )
        
        return JsonResponse({
            'success': True,
            'scorecard': {
                'overall_score': float(scorecard.overall_score),
                'performance_grade': scorecard.performance_grade,
                'total_shipments': scorecard.total_shipments,
                'on_time_delivery_rate': float(scorecard.on_time_deliveries / scorecard.total_shipments * 100) if scorecard.total_shipments > 0 else 0,
                'avg_customer_rating': float(scorecard.avg_customer_rating),
                'alerts_count': alerts.count(),
                'period_days': scorecard.days_analyzed
            }
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': f'Error retrieving scorecard: {str(e)}'
        }, status_code=500)

@require_http_methods(["GET"])
@login_required
def get_top_performers(request):
    """
    Get top performing logistics providers
    Extracted from FastAPI @app.get("/api/scorecard/top-performers")
    """
    try:
        limit = int(request.GET.get('limit', 10))
        days_back = int(request.GET.get('days_back', 90))
        
        # Get top performers based on recent scorecards
        top_scorecards = ProviderScorecard.objects.select_related('provider').filter(
            days_analyzed=days_back
        ).order_by('-overall_score')[:limit]
        
        performers = []
        for scorecard in top_scorecards:
            performers.append({
                'provider_id': scorecard.provider.id,
                'company_name': getattr(scorecard.provider, 'company_name', scorecard.provider.email),
                'overall_score': float(scorecard.overall_score),
                'performance_grade': scorecard.performance_grade,
                'total_shipments': scorecard.total_shipments,
                'on_time_rate': float(scorecard.on_time_deliveries / scorecard.total_shipments * 100) if scorecard.total_shipments > 0 else 0,
                'customer_rating': float(scorecard.avg_customer_rating)
            })
        
        return JsonResponse({
            'success': True,
            'top_performers': performers,
            'analysis_period_days': days_back
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status_code=500)

def _generate_scorecard(provider_id, days_back):
    """Generate a new scorecard for a provider (simplified version)"""
    try:
        provider = User.objects.get(id=provider_id, user_type='LOGISTICS_PROVIDER')
        
        # Calculate period
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days_back)
        
        # Simple scorecard generation (in production would be much more complex)
        scorecard = ProviderScorecard.objects.create(
            provider=provider,
            period_start=start_date,
            period_end=end_date,
            days_analyzed=days_back,
            overall_score=85.0,  # Simplified - would calculate from real data
            on_time_delivery_score=88.0,
            customer_rating_score=82.0,
            response_time_score=90.0,
            completion_rate_score=85.0,
            pricing_competitiveness_score=80.0,
            communication_quality_score=87.0,
            reliability_score=83.0,
            total_shipments=25,
            on_time_deliveries=22,
            avg_customer_rating=4.1,
            avg_response_time_hours=2.5,
            completion_rate_percent=88.0,
            performance_grade='B+'
        )
        
        return scorecard
        
    except Exception:
        return None