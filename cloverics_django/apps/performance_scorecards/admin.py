"""
Performance Scorecards Admin Configuration
"""

from django.contrib import admin
from .models import ProviderScorecard, PerformanceAlert, ScorecardMetric

@admin.register(ProviderScorecard)
class ProviderScorecardAdmin(admin.ModelAdmin):
    list_display = ['provider', 'performance_grade', 'overall_score', 'total_shipments', 'days_analyzed', 'created_at']
    list_filter = ['performance_grade', 'has_alerts', 'days_analyzed', 'created_at']
    search_fields = ['provider__email', 'provider__company_name']
    readonly_fields = ['scorecard_id', 'created_at', 'updated_at']
    ordering = ['-overall_score', '-created_at']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('scorecard_id', 'provider', 'performance_grade', 'overall_score')
        }),
        ('Analysis Period', {
            'fields': ('period_start', 'period_end', 'days_analyzed')
        }),
        ('Score Breakdown', {
            'fields': ('on_time_delivery_score', 'customer_rating_score', 'response_time_score', 
                      'completion_rate_score', 'pricing_competitiveness_score', 'communication_quality_score', 'reliability_score')
        }),
        ('Raw Metrics', {
            'fields': ('total_shipments', 'on_time_deliveries', 'avg_customer_rating', 
                      'avg_response_time_hours', 'completion_rate_percent')
        }),
        ('Alerts', {
            'fields': ('has_alerts', 'alert_count')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at')
        }),
    )

@admin.register(PerformanceAlert)
class PerformanceAlertAdmin(admin.ModelAdmin):
    list_display = ['scorecard', 'alert_type', 'severity', 'metric_value', 'is_acknowledged', 'created_at']
    list_filter = ['alert_type', 'severity', 'is_acknowledged', 'created_at']
    search_fields = ['scorecard__provider__email', 'message']
    readonly_fields = ['alert_id', 'created_at']
    ordering = ['-severity', '-created_at']

@admin.register(ScorecardMetric)
class ScorecardMetricAdmin(admin.ModelAdmin):
    list_display = ['scorecard', 'metric_name', 'metric_value', 'weight', 'score_contribution', 'created_at']
    list_filter = ['metric_name', 'created_at']
    search_fields = ['scorecard__provider__email', 'metric_name']
    readonly_fields = ['created_at']
    ordering = ['-created_at']