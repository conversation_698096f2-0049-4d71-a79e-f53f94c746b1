"""
Performance Scorecards Models
Extracted from FastAPI - Lines 9124-9400+ (Performance scorecards system)
"""

from django.db import models
from django.contrib.auth import get_user_model
import uuid
from decimal import Decimal

User = get_user_model()

class ProviderScorecard(models.Model):
    """
    Performance scorecard for logistics providers
    Extracted from FastAPI PerformanceScorecardEngine functionality
    """
    provider = models.ForeignKey(User, on_delete=models.CASCADE, related_name='scorecards')
    scorecard_id = models.UUIDField(default=uuid.uuid4, unique=True)
    
    # Scoring period
    period_start = models.DateTimeField()
    period_end = models.DateTimeField()
    days_analyzed = models.IntegerField()
    
    # Core metrics (0-100 scores)
    overall_score = models.DecimalField(max_digits=5, decimal_places=2)
    on_time_delivery_score = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    customer_rating_score = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    response_time_score = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    completion_rate_score = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    pricing_competitiveness_score = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    communication_quality_score = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    reliability_score = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    
    # Raw metrics data
    total_shipments = models.IntegerField(default=0)
    on_time_deliveries = models.IntegerField(default=0)
    avg_customer_rating = models.DecimalField(max_digits=3, decimal_places=2, default=0)
    avg_response_time_hours = models.DecimalField(max_digits=6, decimal_places=2, default=0)
    completion_rate_percent = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    
    # Performance grade
    performance_grade = models.CharField(
        max_length=2,
        choices=[
            ('A+', 'Excellent (95-100)'),
            ('A', 'Very Good (90-94)'),
            ('B+', 'Good (85-89)'),
            ('B', 'Above Average (80-84)'),
            ('C+', 'Average (75-79)'),
            ('C', 'Below Average (70-74)'),
            ('D', 'Poor (60-69)'),
            ('F', 'Failing (<60)')
        ],
        default='C'
    )
    
    # Alert flags
    has_alerts = models.BooleanField(default=False)
    alert_count = models.IntegerField(default=0)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'provider_scorecards'
        indexes = [
            models.Index(fields=['provider', 'period_start']),
            models.Index(fields=['overall_score']),
            models.Index(fields=['performance_grade']),
            models.Index(fields=['created_at']),
        ]
        unique_together = ['provider', 'period_start', 'period_end']

    def __str__(self):
        return f"Scorecard for {self.provider.email} - {self.performance_grade} ({self.overall_score}%)"

class PerformanceAlert(models.Model):
    """
    Performance alerts for provider scorecards
    Extracted from FastAPI ScorecardAlertSystem functionality
    """
    scorecard = models.ForeignKey(ProviderScorecard, on_delete=models.CASCADE, related_name='alerts')
    alert_id = models.UUIDField(default=uuid.uuid4, unique=True)
    
    alert_type = models.CharField(
        max_length=50,
        choices=[
            ('score_drop', 'Score Drop'),
            ('poor_rating', 'Poor Customer Rating'),
            ('delayed_delivery', 'Delivery Delays'),
            ('slow_response', 'Slow Response Time'),
            ('low_completion', 'Low Completion Rate'),
            ('poor_communication', 'Poor Communication'),
            ('reliability_issue', 'Reliability Issues')
        ]
    )
    
    severity = models.CharField(
        max_length=20,
        choices=[
            ('critical', 'Critical'),
            ('high', 'High'),
            ('medium', 'Medium'),
            ('low', 'Low')
        ],
        default='medium'
    )
    
    message = models.TextField()
    metric_value = models.DecimalField(max_digits=10, decimal_places=2)
    threshold_value = models.DecimalField(max_digits=10, decimal_places=2)
    
    is_acknowledged = models.BooleanField(default=False)
    acknowledged_at = models.DateTimeField(blank=True, null=True)
    
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'performance_alerts'
        indexes = [
            models.Index(fields=['scorecard', 'severity']),
            models.Index(fields=['alert_type']),
            models.Index(fields=['is_acknowledged']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f"{self.alert_type} - {self.severity} for {self.scorecard.provider.email}"

class ScorecardMetric(models.Model):
    """
    Individual metric tracking for scorecards
    Extracted from FastAPI metric calculation system
    """
    scorecard = models.ForeignKey(ProviderScorecard, on_delete=models.CASCADE, related_name='metrics')
    
    metric_name = models.CharField(max_length=100)
    metric_value = models.DecimalField(max_digits=10, decimal_places=4)
    weight = models.DecimalField(max_digits=5, decimal_places=2)  # Weight in scoring calculation
    score_contribution = models.DecimalField(max_digits=8, decimal_places=4)
    
    # Additional metadata
    data_points = models.IntegerField(default=0)  # Number of data points used
    calculation_method = models.CharField(max_length=100, blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'scorecard_metrics'
        indexes = [
            models.Index(fields=['scorecard', 'metric_name']),
            models.Index(fields=['metric_name']),
        ]
        unique_together = ['scorecard', 'metric_name']

    def __str__(self):
        return f"{self.metric_name}: {self.metric_value} (weight: {self.weight})"