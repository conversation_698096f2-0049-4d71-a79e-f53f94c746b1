{% extends "base.html" %}

{% block content %}
<div class="dashboard-container">
    <div class="dashboard-header">
        <h1>Insurance Policies</h1>
        <p>Manage cargo insurance policies and coverage options</p>
    </div>

    <!-- Policy Statistics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-primary">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <div class="stat-content">
                    <h3>342</h3>
                    <p>Active Policies</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-success">
                    <i class="fas fa-dollar-sign"></i>
                </div>
                <div class="stat-content">
                    <h3>$125M</h3>
                    <p>Total Coverage</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-warning">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="stat-content">
                    <h3>28</h3>
                    <p>Pending Approval</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-info">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="stat-content">
                    <h3>$2.8M</h3>
                    <p>Monthly Premiums</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Policy Management -->
    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5>Active Policies</h5>
            <button class="btn btn-primary" onclick="showNewPolicyModal()">New Policy</button>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Policy Number</th>
                            <th>Client</th>
                            <th>Coverage Type</th>
                            <th>Coverage Amount</th>
                            <th>Premium</th>
                            <th>Effective Date</th>
                            <th>Expiry Date</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>INS-2025-4567</td>
                            <td>Global Import Corp</td>
                            <td>Marine Cargo</td>
                            <td>$2,500,000</td>
                            <td>$12,500/mo</td>
                            <td>2025-01-01</td>
                            <td>2025-12-31</td>
                            <td><span class="badge bg-success">Active</span></td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary" onclick="viewPolicy('INS-2025-4567')">View</button>
                                <button class="btn btn-sm btn-outline-warning" onclick="modifyPolicy('INS-2025-4567')">Modify</button>
                            </td>
                        </tr>
                        <tr>
                            <td>INS-2025-4568</td>
                            <td>TechCorp Solutions</td>
                            <td>All Risk</td>
                            <td>$1,800,000</td>
                            <td>$9,500/mo</td>
                            <td>2025-01-15</td>
                            <td>2026-01-14</td>
                            <td><span class="badge bg-success">Active</span></td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary" onclick="viewPolicy('INS-2025-4568')">View</button>
                                <button class="btn btn-sm btn-outline-warning" onclick="modifyPolicy('INS-2025-4568')">Modify</button>
                            </td>
                        </tr>
                        <tr>
                            <td>INS-2025-4569</td>
                            <td>Industrial Supply Co</td>
                            <td>Transit Insurance</td>
                            <td>$3,200,000</td>
                            <td>$18,000/mo</td>
                            <td>2025-02-01</td>
                            <td>2026-01-31</td>
                            <td><span class="badge bg-warning">Pending</span></td>
                            <td>
                                <button class="btn btn-sm btn-outline-success" onclick="approvePolicy('INS-2025-4569')">Approve</button>
                                <button class="btn btn-sm btn-outline-primary" onclick="reviewPolicy('INS-2025-4569')">Review</button>
                            </td>
                        </tr>
                        <tr>
                            <td>INS-2025-4570</td>
                            <td>Luxury Goods Inc</td>
                            <td>High Value</td>
                            <td>$5,000,000</td>
                            <td>$28,500/mo</td>
                            <td>2025-01-20</td>
                            <td>2025-12-19</td>
                            <td><span class="badge bg-success">Active</span></td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary" onclick="viewPolicy('INS-2025-4570')">View</button>
                                <button class="btn btn-sm btn-outline-warning" onclick="modifyPolicy('INS-2025-4570')">Modify</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Coverage Types -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>Coverage Types</h5>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <h6>Marine Cargo Insurance</h6>
                                <small class="text-muted">Ocean and air freight coverage</small>
                            </div>
                            <span class="badge bg-primary">156 policies</span>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <h6>All Risk Coverage</h6>
                                <small class="text-muted">Comprehensive protection</small>
                            </div>
                            <span class="badge bg-success">98 policies</span>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <h6>Transit Insurance</h6>
                                <small class="text-muted">Land transportation coverage</small>
                            </div>
                            <span class="badge bg-info">64 policies</span>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <h6>High Value Coverage</h6>
                                <small class="text-muted">Premium goods protection</small>
                            </div>
                            <span class="badge bg-warning">24 policies</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>Risk Assessment</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <div class="text-center mb-3">
                                <h4 class="text-success">68%</h4>
                                <small>Low Risk</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center mb-3">
                                <h4 class="text-warning">24%</h4>
                                <small>Medium Risk</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center mb-3">
                                <h4 class="text-danger">8%</h4>
                                <small>High Risk</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center mb-3">
                                <h4 class="text-primary">2.1%</h4>
                                <small>Claim Rate</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Policy Activity -->
    <div class="card">
        <div class="card-header">
            <h5>Recent Policy Activity</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Time</th>
                            <th>Policy Number</th>
                            <th>Client</th>
                            <th>Action</th>
                            <th>Amount</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>15:30</td>
                            <td>INS-2025-4571</td>
                            <td>Fresh Foods Ltd</td>
                            <td>Policy Created</td>
                            <td>$1,200,000</td>
                            <td><span class="badge bg-success">Approved</span></td>
                        </tr>
                        <tr>
                            <td>14:45</td>
                            <td>INS-2025-4567</td>
                            <td>Global Import Corp</td>
                            <td>Premium Payment</td>
                            <td>$12,500</td>
                            <td><span class="badge bg-success">Received</span></td>
                        </tr>
                        <tr>
                            <td>13:20</td>
                            <td>INS-2025-4569</td>
                            <td>Industrial Supply Co</td>
                            <td>Policy Review</td>
                            <td>$3,200,000</td>
                            <td><span class="badge bg-warning">Under Review</span></td>
                        </tr>
                        <tr>
                            <td>11:15</td>
                            <td>INS-2025-4568</td>
                            <td>TechCorp Solutions</td>
                            <td>Coverage Modified</td>
                            <td>$1,800,000</td>
                            <td><span class="badge bg-info">Updated</span></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- New Policy Modal -->
<div class="modal fade" id="newPolicyModal" tabindex="-1" aria-labelledby="newPolicyModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="newPolicyModalLabel">Create New Insurance Policy</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="newPolicyForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="clientName" class="form-label">Client Name</label>
                                <select class="form-select" id="clientName" name="clientName" required>
                                    <option value="">Select Client</option>
                                    <option value="Global Import Corp">Global Import Corp</option>
                                    <option value="TechCorp Solutions">TechCorp Solutions</option>
                                    <option value="Industrial Supply Co">Industrial Supply Co</option>
                                    <option value="Luxury Goods Inc">Luxury Goods Inc</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="coverageType" class="form-label">Coverage Type</label>
                                <select class="form-select" id="coverageType" name="coverageType" required>
                                    <option value="">Select Coverage</option>
                                    <option value="Marine Cargo">Marine Cargo</option>
                                    <option value="All Risk">All Risk</option>
                                    <option value="Transit Insurance">Transit Insurance</option>
                                    <option value="High Value">High Value</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="coverageAmount" class="form-label">Coverage Amount ($)</label>
                                <input type="number" class="form-control" id="coverageAmount" name="coverageAmount" required min="10000" step="1000">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="premiumAmount" class="form-label">Monthly Premium ($)</label>
                                <input type="number" class="form-control" id="premiumAmount" name="premiumAmount" required min="50" step="25">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="deductibleAmount" class="form-label">Deductible Amount ($)</label>
                                <input type="number" class="form-control" id="deductibleAmount" name="deductibleAmount" required min="100" step="100" value="500">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="effectiveDate" class="form-label">Effective Date</label>
                                <input type="date" class="form-control" id="effectiveDate" name="effectiveDate" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="expiryDate" class="form-label">Expiry Date</label>
                                <input type="date" class="form-control" id="expiryDate" name="expiryDate" required>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="createNewPolicy()">Create Policy</button>
            </div>
        </div>
    </div>
</div>

<script>
function showNewPolicyModal() {
    document.getElementById('newPolicyModal').style.display = 'block';
    new bootstrap.Modal(document.getElementById('newPolicyModal')).show();
}

function createNewPolicy() {
    const form = document.getElementById('newPolicyForm');
    const formData = new FormData(form);
    
    // Validate form
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }
    
    // Show loading state
    const button = event.target;
    const originalText = button.textContent;
    button.textContent = 'Creating...';
    button.disabled = true;
    
    // Submit to backend
    fetch('/api/insurance/policies/create', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Policy created successfully!');
            bootstrap.Modal.getInstance(document.getElementById('newPolicyModal')).hide();
            form.reset();
            location.reload(); // Refresh to show new policy
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error creating policy. Please try again.');
    })
    .finally(() => {
        button.textContent = originalText;
        button.disabled = false;
    });
}

function viewPolicy(policyNumber) {
    alert('Viewing policy details for ' + policyNumber + '\n\nThis would open a detailed view showing:\n- Policy terms and conditions\n- Coverage details\n- Payment history\n- Claims history\n- Documents');
}

function modifyPolicy(policyNumber) {
    if (confirm('Do you want to modify policy ' + policyNumber + '?')) {
        alert('Policy modification form would open here.\n\nAllowed modifications:\n- Coverage amount\n- Premium adjustments\n- Policy terms\n- Additional coverage');
    }
}

function approvePolicy(policyNumber) {
    if (confirm('Are you sure you want to approve policy ' + policyNumber + '?\n\nThis will:\n• Activate the insurance policy\n• Begin coverage period\n• Send confirmation to customer\n• Update policy status to Active')) {
        // Create form and submit to backend
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '/insurance/policies/approve';
        form.style.display = 'none';
        
        const policyInput = document.createElement('input');
        policyInput.type = 'hidden';
        policyInput.name = 'policy_number';
        policyInput.value = policyNumber;
        form.appendChild(policyInput);
        
        document.body.appendChild(form);
        
        alert('Policy ' + policyNumber + ' approved successfully!\n\nPolicy Status: Active\nCoverage Start: ' + new Date().toLocaleDateString() + '\nNotification sent to customer');
        form.submit();
    }
}

function reviewPolicy(policyNumber) {
    alert('Opening review interface for policy ' + policyNumber + '\n\nReview includes:\n- Risk assessment verification\n- Documentation completeness\n- Underwriting guidelines compliance\n- Approval recommendation');
}
</script>
{% endblock %}