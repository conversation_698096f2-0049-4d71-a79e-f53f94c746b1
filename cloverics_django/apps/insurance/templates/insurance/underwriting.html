{% extends "base.html" %}

{% block content %}
<div class="dashboard-container">
    <div class="dashboard-header">
        <h1>Underwriting</h1>
        <p>Risk assessment and policy underwriting for cargo insurance</p>
    </div>

    <!-- Underwriting Statistics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-primary">
                    <i class="fas fa-clipboard-check"></i>
                </div>
                <div class="stat-content">
                    <h3>38</h3>
                    <p>Pending Review</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-success">
                    <i class="fas fa-thumbs-up"></i>
                </div>
                <div class="stat-content">
                    <h3>156</h3>
                    <p>Approved Today</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="stat-content">
                    <h3>12</h3>
                    <p>High Risk</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-info">
                    <i class="fas fa-percentage"></i>
                </div>
                <div class="stat-content">
                    <h3>92.1%</h3>
                    <p>Approval Rate</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Pending Applications -->
    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5>Pending Underwriting Applications</h5>
            <button class="btn btn-primary" onclick="openReviewQueue()">Review Queue</button>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Application ID</th>
                            <th>Applicant</th>
                            <th>Coverage Type</th>
                            <th>Requested Amount</th>
                            <th>Risk Score</th>
                            <th>Submitted</th>
                            <th>Underwriter</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>UW-2025-2891</td>
                            <td>ChemTech Industries</td>
                            <td>Hazardous Materials</td>
                            <td>$4,500,000</td>
                            <td><span class="badge bg-danger">8.2</span></td>
                            <td>2025-01-26</td>
                            <td>Sarah Johnson</td>
                            <td><span class="badge bg-warning">Under Review</span></td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary" onclick="reviewApplication('UW-2025-2891')">Review</button>
                                <button class="btn btn-sm btn-outline-info" onclick="viewApplicationDetails('UW-2025-2891')">Details</button>
                            </td>
                        </tr>
                        <tr>
                            <td>UW-2025-2892</td>
                            <td>Global Electronics Co</td>
                            <td>High Value</td>
                            <td>$2,800,000</td>
                            <td><span class="badge bg-warning">6.4</span></td>
                            <td>2025-01-25</td>
                            <td>Michael Chen</td>
                            <td><span class="badge bg-info">Risk Assessment</span></td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary" onclick="reviewApplication('UW-2025-2892')">Review</button>
                                <button class="btn btn-sm btn-outline-info" onclick="viewApplicationDetails('UW-2025-2892')">Details</button>
                            </td>
                        </tr>
                        <tr>
                            <td>UW-2025-2893</td>
                            <td>Fashion Forward Ltd</td>
                            <td>Standard Coverage</td>
                            <td>$850,000</td>
                            <td><span class="badge bg-success">3.2</span></td>
                            <td>2025-01-25</td>
                            <td>Elena Rodriguez</td>
                            <td><span class="badge bg-success">Ready for Approval</span></td>
                            <td>
                                <button class="btn btn-sm btn-success" onclick="approveApplication('UW-2025-2893')">Approve</button>
                                <button class="btn btn-sm btn-outline-info" onclick="viewApplicationDetails('UW-2025-2893')">Details</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Risk Assessment Tools -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>Risk Factors</h5>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <h6>Cargo Type</h6>
                                <small class="text-muted">Nature of goods being shipped</small>
                            </div>
                            <span class="badge bg-primary">Weight: 30%</span>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <h6>Route Risk</h6>
                                <small class="text-muted">Security and weather conditions</small>
                            </div>
                            <span class="badge bg-warning">Weight: 25%</span>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <h6>Carrier History</h6>
                                <small class="text-muted">Track record and reliability</small>
                            </div>
                            <span class="badge bg-info">Weight: 20%</span>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <h6>Client History</h6>
                                <small class="text-muted">Claims history and payment record</small>
                            </div>
                            <span class="badge bg-success">Weight: 15%</span>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <h6>Packaging Quality</h6>
                                <small class="text-muted">Protection and handling standards</small>
                            </div>
                            <span class="badge bg-secondary">Weight: 10%</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>Underwriting Performance</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <div class="text-center mb-3">
                                <h4 class="text-success">92.1%</h4>
                                <small>Approval Rate</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center mb-3">
                                <h4 class="text-primary">2.8 hrs</h4>
                                <small>Avg. Review Time</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center mb-3">
                                <h4 class="text-warning">5.2</h4>
                                <small>Avg. Risk Score</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center mb-3">
                                <h4 class="text-info">1.8%</h4>
                                <small>Loss Ratio</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Risk Assessment Calculator -->
    <div class="card mb-4">
        <div class="card-header">
            <h5>Risk Assessment Calculator</h5>
        </div>
        <div class="card-body">
            <form>
                <div class="row">
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label class="form-label">Cargo Type</label>
                            <select class="form-select">
                                <option>General Cargo</option>
                                <option>Electronics</option>
                                <option>Hazardous Materials</option>
                                <option>Perishables</option>
                                <option>High Value</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label class="form-label">Route Risk Level</label>
                            <select class="form-select">
                                <option>Low Risk</option>
                                <option>Medium Risk</option>
                                <option>High Risk</option>
                                <option>Extreme Risk</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label class="form-label">Coverage Amount</label>
                            <input type="number" class="form-control" placeholder="Enter amount">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label class="form-label">&nbsp;</label>
                            <button type="submit" class="btn btn-primary form-control" onclick="calculateRisk()">Calculate Risk</button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Recent Underwriting Decisions -->
    <div class="card">
        <div class="card-header">
            <h5>Recent Underwriting Decisions</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Time</th>
                            <th>Application ID</th>
                            <th>Applicant</th>
                            <th>Coverage Amount</th>
                            <th>Risk Score</th>
                            <th>Decision</th>
                            <th>Underwriter</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>16:30</td>
                            <td>UW-2025-2890</td>
                            <td>Pacific Trading Co</td>
                            <td>$1,250,000</td>
                            <td>4.2</td>
                            <td><span class="badge bg-success">Approved</span></td>
                            <td>Sarah Johnson</td>
                        </tr>
                        <tr>
                            <td>15:45</td>
                            <td>UW-2025-2889</td>
                            <td>TechFlow Solutions</td>
                            <td>$890,000</td>
                            <td>3.8</td>
                            <td><span class="badge bg-success">Approved</span></td>
                            <td>Michael Chen</td>
                        </tr>
                        <tr>
                            <td>14:20</td>
                            <td>UW-2025-2888</td>
                            <td>Heavy Industries Inc</td>
                            <td>$3,500,000</td>
                            <td>7.8</td>
                            <td><span class="badge bg-warning">Conditional</span></td>
                            <td>Elena Rodriguez</td>
                        </tr>
                        <tr>
                            <td>13:15</td>
                            <td>UW-2025-2887</td>
                            <td>Luxury Imports Ltd</td>
                            <td>$2,100,000</td>
                            <td>5.4</td>
                            <td><span class="badge bg-success">Approved</span></td>
                            <td>David Williams</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
function openReviewQueue() {
    alert('Opening underwriting review queue:\n\n• 12 applications pending review\n• Priority by risk score\n• Automated risk assessment complete\n• Underwriter assignment active\n\nView detailed risk analysis for each application with complete client and cargo evaluation.');
}

function reviewApplication(applicationId) {
    alert('Opening detailed review for application ' + applicationId + '\n\nReview includes:\n• Complete risk assessment\n• Client credit history\n• Cargo and route analysis\n• Recommendation for decision\n• Underwriter notes and approval workflow');
}

function viewApplicationDetails(applicationId) {
    alert('Viewing comprehensive details for application ' + applicationId + '\n\nDetails include:\n• Client company profile\n• Coverage requirements\n• Risk factors analysis\n• Financial background\n• Previous claims history\n• Route security assessment');
}

function approveApplication(applicationId) {
    if (confirm('Approve underwriting application ' + applicationId + '?\n\nThis will:\n• Create insurance policy\n• Send approval notification\n• Generate policy documents\n• Update client account\n• Begin coverage period')) {
        // Create form and submit to backend
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '/insurance/underwriting/approve';
        form.style.display = 'none';
        
        const appInput = document.createElement('input');
        appInput.type = 'hidden';
        appInput.name = 'application_id';
        appInput.value = applicationId;
        form.appendChild(appInput);
        
        document.body.appendChild(form);
        
        const policyNumber = 'POL-' + new Date().getTime();
        alert('Application ' + applicationId + ' approved successfully!\n\nPolicy Number: ' + policyNumber + '\nCoverage Amount: $850,000\nEffective Date: ' + new Date().toLocaleDateString() + '\nDocuments generated and client notified');
        form.submit();
    }
}

function calculateRisk() {
    event.preventDefault();
    
    const cargo = document.querySelector('select[name="cargo_type"]');
    const origin = document.querySelector('select[name="origin"]');
    const destination = document.querySelector('select[name="destination"]');
    const coverage = document.querySelector('input[type="number"]');
    
    if (!cargo || !origin || !destination || !coverage) {
        alert('Please fill in all fields to calculate risk.');
        return;
    }
    
    // Simple risk calculation based on form inputs
    let riskScore = 3.0; // Base risk
    
    // Cargo type modifiers
    const cargoType = cargo.value;
    if (cargoType.includes('Electronics')) riskScore += 1.5;
    if (cargoType.includes('Luxury')) riskScore += 2.0;
    if (cargoType.includes('Hazardous')) riskScore += 3.0;
    if (cargoType.includes('Fragile')) riskScore += 1.0;
    
    // Coverage amount modifier
    const coverageAmount = parseFloat(coverage.value);
    if (coverageAmount > 5000000) riskScore += 2.0;
    else if (coverageAmount > 2000000) riskScore += 1.0;
    else if (coverageAmount > 1000000) riskScore += 0.5;
    
    // Random factor for demonstration
    riskScore += Math.random() * 1.0;
    riskScore = Math.round(riskScore * 10) / 10;
    
    // Determine recommendation
    let recommendation = '';
    let badgeColor = '';
    if (riskScore <= 4.0) {
        recommendation = 'Approved - Low Risk';
        badgeColor = 'success';
    } else if (riskScore <= 6.0) {
        recommendation = 'Review Required - Medium Risk';
        badgeColor = 'warning';
    } else {
        recommendation = 'Declined - High Risk';
        badgeColor = 'danger';
    }
    
    alert('Risk Assessment Complete\n\nRisk Score: ' + riskScore + '/10\nRecommendation: ' + recommendation + '\n\nFactors considered:\n• Cargo type and value\n• Route security\n• Coverage amount\n• Client history\n• Market conditions');
}
</script>
{% endblock %}