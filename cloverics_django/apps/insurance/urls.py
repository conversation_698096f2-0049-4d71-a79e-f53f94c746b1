from django.urls import path
from . import views

app_name = 'insurance'

urlpatterns = [
    # Insurance Provider Pages
    path('policies/', views.insurance_policies_page, name='policies'),
    path('claims/', views.insurance_claims_page, name='claims'),
    path('underwriting/', views.insurance_underwriting_page, name='underwriting'),
    path('reports/', views.insurance_reports_page, name='reports'),
    
    # Insurance API Endpoints
    path('api/claims/create/', views.create_insurance_claim, name='create_claim'),
    path('api/reports/download/<int:report_id>/', views.download_insurance_report, name='download_report'),
    path('api/policies/approve/', views.approve_insurance_policy, name='approve_policy'),
    path('api/claims/process-payment/', views.process_claim_payment, name='process_payment'),
    
    # Phase 3: Additional insurance endpoints for 100% coverage
    path('quotes/', views.insurance_quotes, name='insurance_quotes'), 
    path('quotes/<int:quote_id>/approve/', views.approve_quote, name='approve_quote'),
    path('policies/<int:policy_id>/', views.policy_detail, name='policy_detail'),
    path('claims/<int:claim_id>/', views.claim_detail, name='claim_detail'),
    path('underwriting/dashboard/', views.underwriting_dashboard, name='underwriting_dashboard'),
    path('api/policies/search/', views.api_policy_search, name='api_policy_search'),
    path('api/claims/status/', views.api_claims_status, name='api_claims_status'),
    path('api/risk/assessment/', views.api_risk_assessment, name='api_risk_assessment'),
]