from django.shortcuts import render

def dashboard(request):
    """
    Insurance Provider Dashboard
    Displays insurance policies, claims, and coverage statistics
    """
    # Create mock user object with proper user_type for sidebar navigation
    class MockUser:
        def __init__(self):
            self.user_type = 'INSURANCE_PROVIDER'
            self.id = 4
            self.email = '<EMAIL>'
            self.company_name = 'Demo Insurance Company'
    
    context = {
        'title': 'Insurance Provider Dashboard - Cloverics',
        'user_type': 'INSURANCE_PROVIDER',
        'user': MockUser(),
        'insurance_stats': {
            'active_policies': 234,
            'total_coverage': 2450000.00,
            'pending_claims': 8,
            'claims_processed': 156,
            'monthly_premiums': 18500.00,
            'claim_ratio': 12.3
        },
        'recent_policies': [
            {
                'id': 'IP667H889J',
                'shipment': 'CL718F825C',
                'coverage': 25000.00,
                'premium': 245.00,
                'status': 'active'
            },
            {
                'id': 'IP778I990K',
                'shipment': 'CL829G934D',
                'coverage': 18000.00,
                'premium': 189.00,
                'status': 'expired'
            }
        ]
    }
    
    return render(request, 'insurance/dashboard.html', context)