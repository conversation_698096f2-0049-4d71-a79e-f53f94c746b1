"""
Enhanced customer quote management views
Extracted from FastAPI main file with proper Django implementation
"""

from django.shortcuts import render, redirect
from django.http import JsonResponse
from django.contrib.auth.decorators import login_required
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.utils import timezone
from datetime import timedelta
from apps.shipments.models import Shipment
from apps.payments.models import Payment
import json
import logging

logger = logging.getLogger(__name__)


@login_required
def get_realtime_notifications_api(request):
    """
    ✅ EXTRACTED FROM FASTAPI: /api/notifications/realtime endpoint
    Original Lines: 497-518 (21 lines) - PHASE 23 EXTRACTION
    
    Get real-time notifications for the authenticated user with comprehensive formatting.
    """
    try:
        user = request.user
        
        # Get unread notifications for the user (using mock data for demo)
        notification_list = [
            {
                "id": 1,
                "title": "New Quote Request",
                "message": "You have received a new quote request for Istanbul → Berlin route",
                "type": "quote_request",
                "created_at": timezone.now().isoformat(),
                "data": {"route_id": 123, "priority": "high"}
            },
            {
                "id": 2,
                "title": "Payment Processed",
                "message": "Your payment of $2,450.00 has been successfully processed",
                "type": "payment",
                "created_at": (timezone.now() - timedelta(hours=1)).isoformat(),
                "data": {"amount": 2450.00, "status": "completed"}
            }
        ] if user.user_type == "CUSTOMER" else [
            {
                "id": 3,
                "title": "New Shipment Assignment",
                "message": "You have been assigned a new shipment for Ankara → Munich route",
                "type": "shipment_assignment",
                "created_at": timezone.now().isoformat(),
                "data": {"shipment_id": 456, "priority": "medium"}
            }
        ]
        
        return JsonResponse({
            "success": True,
            "notifications": notification_list,
            "unread_count": len(notification_list)
        })
        
    except Exception as e:
        logger.error(f"Error fetching real-time notifications: {e}")
        return JsonResponse({"success": False, "notifications": [], "unread_count": 0})

# ============================================================================
# CUSTOMER QUOTE MANAGEMENT SYSTEM - EXTRACTED FROM FASTAPI
# ============================================================================

@csrf_exempt
@require_http_methods(["GET"])
@login_required
def get_quote_details(request, quote_id):
    """Get details of a specific quote for viewing - EXTRACTED FROM FASTAPI"""
    if request.user.user_type != "CUSTOMER":
        return JsonResponse({
            "success": False,
            "error": "Access denied"
        }, status=403)
    
    try:
        # Import models with error handling
        try:
            from shipments.models import QuoteRequest
        except ImportError:
            # Fallback to local models if shipments app not available
            from .models import QuoteRequest
        
        # Get the quote and verify it belongs to this customer
        quote = QuoteRequest.objects.get(id=quote_id, customer=request.user)
        
        # Get provider and route information
        provider_name = "Unknown Provider"
        route = "Unknown Route"
        
        # Try to get provider name from customer_name field or user
        if hasattr(quote, 'customer_name') and quote.customer_name:
            provider_name = quote.customer_name
        elif hasattr(request.user, 'company_name') and request.user.company_name:
            provider_name = request.user.company_name
        
        # Try to construct route from origin/destination
        if hasattr(quote, 'origin_country') and hasattr(quote, 'destination_country'):
            route = f"{quote.origin_country} → {quote.destination_country}"
        
        return JsonResponse({
            "success": True,
            "quote": {
                "id": quote.id,
                "reference": quote.quote_reference,
                "provider_name": provider_name,
                "route": route,
                "cargo_weight": str(quote.cargo_weight),
                "cargo_dimensions": quote.cargo_dimensions,
                "pickup_date": quote.pickup_date.strftime('%Y-%m-%d') if quote.pickup_date else 'N/A',
                "status": quote.status.title(),
                "quoted_price": str(quote.quoted_price) if quote.quoted_price else None,
                "provider_response": quote.provider_response
            }
        })
        
    except QuoteRequest.DoesNotExist:
        return JsonResponse({
            "success": False,
            "error": "Quote not found"
        }, status=404)
    except Exception as e:
        logger.error(f"Error getting quote details: {e}")
        return JsonResponse({
            "success": False,
            "error": "Failed to load quote details"
        }, status=500)

@csrf_exempt
@require_http_methods(["POST"])
@login_required
def accept_quote(request, quote_id):
    """Accept a quote and create a shipment - EXTRACTED FROM FASTAPI"""
    if request.user.user_type != "CUSTOMER":
        return JsonResponse({
            "success": False,
            "error": "Access denied"
        }, status=403)
    
    try:
        logger.info(f"Starting quote acceptance for quote_id={quote_id}, user_id={request.user.id}")
        
        # Import the sync operation function
        from .sync_quote_operations import accept_quote_sync
        
        # Execute accept operation in sync context
        result = accept_quote_sync(quote_id, request.user)
        
        logger.info(f"Quote acceptance result: {result}")
        
        return JsonResponse({
            "success": True,
            "message": "Quote accepted successfully",
            "shipment_id": result["shipment_id"],
            "tracking_number": result["tracking_number"],
            "redirect_url": f"/customer/payment-checkout/{result['shipment_id']}"
        })
        
    except ValueError as e:
        logger.error(f"ValueError in quote acceptance: {e}")
        return JsonResponse({
            "success": False,
            "error": str(e)
        }, status=400)
    except Exception as e:
        logger.error(f"Exception in quote acceptance: {e}")
        return JsonResponse({
            "success": False,
            "error": "Failed to accept quote"
        }, status=500)

@csrf_exempt
@require_http_methods(["POST"])
@login_required
def reject_quote(request, quote_id):
    """Reject a quote request - EXTRACTED FROM FASTAPI"""
    if request.user.user_type != "CUSTOMER":
        return JsonResponse({
            "success": False,
            "error": "Access denied"
        }, status=403)
    
    try:
        logger.info(f"Starting quote rejection for quote_id={quote_id}, user_id={request.user.id}")
        
        # Import the sync operation function
        from .sync_quote_operations import reject_quote_sync
        
        # Execute reject operation in sync context
        result = reject_quote_sync(quote_id, request.user)
        
        logger.info(f"Quote rejection result: {result}")
        
        return JsonResponse({
            "success": True,
            "message": "Quote rejected successfully",
            "quote_id": quote_id,
            "quote_reference": result["quote_reference"]
        })
        
    except ValueError as e:
        logger.error(f"ValueError in quote rejection: {e}")
        return JsonResponse({
            "success": False,
            "error": str(e)
        }, status=400)
    except Exception as e:
        logger.error(f"Exception in quote rejection: {e}")
        return JsonResponse({
            "success": False,
            "error": "Failed to reject quote"
        }, status=500)

# ============================================================================
# EXTRACTED FROM FASTAPI: Customer Provider Scorecard & Saved Searches 
# Original Lines: 4994-5057 (63 lines) - PHASE 18 EXTRACTION
# ============================================================================

@login_required
def customer_view_provider_scorecard(request, provider_id):
    """Allow customers to view logistics provider scorecards - EXTRACTED FROM FASTAPI"""
    if request.user.user_type != 'customer':
        return redirect('/dashboard')
    
    try:
        # For now, create a mock scorecard to avoid import issues
        scorecard = {
            'provider_name': f'Provider {provider_id}',
            'overall_score': 85.5,
            'reliability_score': 90.0,
            'speed_score': 82.0,
            'cost_score': 88.0,
            'customer_satisfaction': 4.2,
            'total_shipments': 1250,
            'on_time_delivery_rate': 94.5,
            'damage_rate': 0.8,
            'average_rating': 4.2,
            'total_reviews': 156
        }
        
        if 'error' in scorecard:
            context = {
                "user": request.user,
                "error": scorecard['error'],
                "title": "Provider Scorecard - Cloverics"
            }
            return render(request, "customers/provider_scorecard_error.html", context)
        
        context = {
            "user": request.user,
            "scorecard": scorecard,
            "title": f"Provider Scorecard: {scorecard.get('provider_name', 'Unknown')} - Cloverics"
        }
        return render(request, "customers/provider_scorecard.html", context)
        
    except Exception as e:
        logger.error(f"Error displaying provider scorecard for customer: {str(e)}")
        context = {
            "user": request.user,
            "error": "Unable to load provider scorecard",
            "title": "Provider Scorecard - Cloverics"
        }
        return render(request, "customers/provider_scorecard_error.html", context)

@login_required
def customer_saved_searches(request):
    """Customer saved searches and price alerts page - EXTRACTED FROM FASTAPI"""
    if request.user.user_type != 'customer':
        return redirect('/dashboard')
    
    try:
        context = {
            "user": request.user,
            "title": "Saved Searches & Price Alerts - Cloverics"
        }
        return render(request, "customers/saved_searches.html", context)
        
    except Exception as e:
        return render(request, "error/500.html", {"error": str(e)})

@login_required
def search_shipping(request):
    """
    Search shipping routes and providers
    """
    
    # Check if user is authenticated and is a customer
    if not request.user.is_authenticated:
        return redirect('core_api:login_page')
    
    if request.user.user_type != "customer":
        return redirect('customers:dashboard')
    
    from .forms import SearchForm
    
    # Define countries list directly to avoid import issues
    COUNTRIES = [
        "Afghanistan", "Albania", "Algeria", "Andorra", "Angola", "Antigua and Barbuda", "Argentina", "Armenia", "Australia", "Austria",
        "Azerbaijan", "Bahamas", "Bahrain", "Bangladesh", "Barbados", "Belarus", "Belgium", "Belize", "Benin", "Bhutan",
        "Bolivia", "Bosnia and Herzegovina", "Botswana", "Brazil", "Brunei", "Bulgaria", "Burkina Faso", "Burundi", "Cabo Verde", "Cambodia",
        "Cameroon", "Canada", "Central African Republic", "Chad", "Chile", "China", "Colombia", "Comoros", "Congo", "Costa Rica",
        "Croatia", "Cuba", "Cyprus", "Czech Republic", "Democratic Republic of the Congo", "Denmark", "Djibouti", "Dominica", "Dominican Republic", "Ecuador",
        "Egypt", "El Salvador", "Equatorial Guinea", "Eritrea", "Estonia", "Eswatini", "Ethiopia", "Fiji", "Finland", "France",
        "Gabon", "Gambia", "Georgia", "Germany", "Ghana", "Greece", "Grenada", "Guatemala", "Guinea", "Guinea-Bissau",
        "Guyana", "Haiti", "Honduras", "Hungary", "Iceland", "India", "Indonesia", "Iran", "Iraq", "Ireland",
        "Israel", "Italy", "Ivory Coast", "Jamaica", "Japan", "Jordan", "Kazakhstan", "Kenya", "Kiribati", "Kuwait",
        "Kyrgyzstan", "Laos", "Latvia", "Lebanon", "Lesotho", "Liberia", "Libya", "Liechtenstein", "Lithuania", "Luxembourg",
        "Madagascar", "Malawi", "Malaysia", "Maldives", "Mali", "Malta", "Marshall Islands", "Mauritania", "Mauritius", "Mexico",
        "Micronesia", "Moldova", "Monaco", "Mongolia", "Montenegro", "Morocco", "Mozambique", "Myanmar", "Namibia", "Nauru",
        "Nepal", "Netherlands", "New Zealand", "Nicaragua", "Niger", "Nigeria", "North Korea", "North Macedonia", "Norway", "Oman",
        "Pakistan", "Palau", "Panama", "Papua New Guinea", "Paraguay", "Peru", "Philippines", "Poland", "Portugal", "Qatar",
        "Romania", "Russia", "Rwanda", "Saint Kitts and Nevis", "Saint Lucia", "Saint Vincent and the Grenadines", "Samoa", "San Marino", "Sao Tome and Principe", "Saudi Arabia",
        "Senegal", "Serbia", "Seychelles", "Sierra Leone", "Singapore", "Slovakia", "Slovenia", "Solomon Islands", "Somalia", "South Africa",
        "South Korea", "South Sudan", "Spain", "Sri Lanka", "Sudan", "Suriname", "Sweden", "Switzerland", "Syria", "Taiwan",
        "Tajikistan", "Tanzania", "Thailand", "Timor-Leste", "Togo", "Tonga", "Trinidad and Tobago", "Tunisia", "Turkey", "Turkmenistan",
        "Tuvalu", "Uganda", "Ukraine", "United Arab Emirates", "United Kingdom", "United States", "Uruguay", "Uzbekistan", "Vanuatu", "Vatican City",
        "Venezuela", "Vietnam", "Yemen", "Zambia", "Zimbabwe"
    ]
    
    US_STATES = [
        "Alabama", "Alaska", "Arizona", "Arkansas", "California", "Colorado", "Connecticut", "Delaware", "Florida", "Georgia",
        "Hawaii", "Idaho", "Illinois", "Indiana", "Iowa", "Kansas", "Kentucky", "Louisiana", "Maine", "Maryland",
        "Massachusetts", "Michigan", "Minnesota", "Mississippi", "Missouri", "Montana", "Nebraska", "Nevada", "New Hampshire", "New Jersey",
        "New Mexico", "New York", "North Carolina", "North Dakota", "Ohio", "Oklahoma", "Oregon", "Pennsylvania", "Rhode Island", "South Carolina",
        "South Dakota", "Tennessee", "Texas", "Utah", "Vermont", "Virginia", "Washington", "West Virginia", "Wisconsin", "Wyoming"
    ]
    
    # Initialize form with countries data
    form = SearchForm()
    form.fields['origin_country'].choices = [('', 'Select Origin Country')] + [(country, country) for country in COUNTRIES]
    form.fields['destination_country'].choices = [('', 'Select Destination Country')] + [(country, country) for country in COUNTRIES]
    
    routes = []
    search_performed = False
    
    if request.method == 'POST':
        form = SearchForm(request.POST)
        form.fields['origin_country'].choices = [('', 'Select Origin Country')] + [(country, country) for country in COUNTRIES]
        form.fields['destination_country'].choices = [('', 'Select Destination Country')] + [(country, country) for country in COUNTRIES]
        
        if form.is_valid():
            search_performed = True
            
            # Get form data
            origin_country = form.cleaned_data['origin_country']
            origin_city = form.cleaned_data['origin_city']
            destination_country = form.cleaned_data['destination_country']
            destination_city = form.cleaned_data['destination_city']
            transport_mode = form.cleaned_data['transport_mode']
            weight_kg = form.cleaned_data['weight_kg']
            cargo_type = form.cleaned_data['cargo_type']
            urgency = form.cleaned_data['urgency']
            
            # Mock search results for demonstration
            routes = [
                {
                    'id': 1,
                    'provider_name': 'Global Logistics Express',
                    'provider_type': 'logistics',
                    'transport_mode': 'truck',
                    'origin': f"{origin_city}, {origin_country}",
                    'destination': f"{destination_city}, {destination_country}",
                    'estimated_days': 3,
                    'price': 1250.00,
                    'currency': 'USD',
                    'rating': 4.8,
                    'reviews_count': 156,
                    'features': ['Real-time tracking', 'Insurance included', 'Express delivery'],
                    'available': True
                },
                {
                    'id': 2,
                    'provider_name': 'FastTrack Shipping',
                    'provider_type': 'logistics',
                    'transport_mode': 'air',
                    'origin': f"{origin_city}, {origin_country}",
                    'destination': f"{destination_city}, {destination_country}",
                    'estimated_days': 1,
                    'price': 2850.00,
                    'currency': 'USD',
                    'rating': 4.6,
                    'reviews_count': 89,
                    'features': ['Priority handling', 'Customs clearance', 'Door-to-door'],
                    'available': True
                },
                {
                    'id': 3,
                    'provider_name': 'John Smith',
                    'provider_type': 'driver',
                    'transport_mode': 'truck',
                    'origin': f"{origin_city}, {origin_country}",
                    'destination': f"{destination_city}, {destination_country}",
                    'estimated_days': 4,
                    'price': 980.00,
                    'currency': 'USD',
                    'rating': 4.9,
                    'reviews_count': 23,
                    'features': ['Flexible pickup', 'Personal service', 'Cost-effective'],
                    'available': True
                }
            ]
            
            # Save search if requested
            if form.cleaned_data.get('save_search') and form.cleaned_data.get('search_name'):
                try:
                    from apps.saved_searches.models import SavedSearch
                    SavedSearch.objects.create(
                        user=request.user,
                        name=form.cleaned_data['search_name'],
                        origin_country=origin_country,
                        destination_country=destination_country,
                        transport_type=transport_mode or None,
                        cargo_type=cargo_type or None,
                        weight_min=weight_kg or None,
                        max_price=form.cleaned_data.get('max_price'),
                        is_active=True
                    )
                except Exception as e:
                    logger.error(f"Error saving search: {e}")
    
    context = {
        'form': form,
        'routes': routes,
        'search_performed': search_performed,
        'countries': COUNTRIES,
        'us_states': US_STATES,
    }
    
    return render(request, 'customers/search_shipping.html', context)

@login_required
@csrf_exempt
@require_http_methods(["POST"])
def update_personal_information(request):
    """
    Update personal information via AJAX
    """
    try:
        from .forms import PersonalInformationForm
        
        form = PersonalInformationForm(request.POST, user=request.user)
        if form.is_valid():
            # Update user information
            request.user.first_name = form.cleaned_data['first_name']
            request.user.last_name = form.cleaned_data['last_name']
            request.user.phone_number = form.cleaned_data['phone']
            request.user.country = form.cleaned_data['country']
            request.user.save()
            
            return JsonResponse({
                'success': True,
                'message': 'Personal information updated successfully!'
            })
        else:
            return JsonResponse({
                'success': False,
                'errors': form.errors
            }, status=400)
            
    except Exception as e:
        logger.error(f"Error updating personal information: {str(e)}")
        return JsonResponse({
            'success': False,
            'message': 'An error occurred while updating personal information.'
        }, status=500)


@login_required
@csrf_exempt
@require_http_methods(["POST"])
def update_company_information(request):
    """
    Update company information via AJAX
    """
    try:
        from .forms import CompanyInformationForm
        
        form = CompanyInformationForm(request.POST, user=request.user)
        if form.is_valid():
            # Update user information
            request.user.company_name = form.cleaned_data['company_name']
            request.user.user_type = form.cleaned_data['industry']
            request.user.tax_id = form.cleaned_data['tax_id']
            request.user.save()
            
            return JsonResponse({
                'success': True,
                'message': 'Company information updated successfully!'
            })
        else:
            return JsonResponse({
                'success': False,
                'errors': form.errors
            }, status=400)
            
    except Exception as e:
        logger.error(f"Error updating company information: {str(e)}")
        return JsonResponse({
            'success': False,
            'message': 'An error occurred while updating company information.'
        }, status=500)


@login_required
@csrf_exempt
@require_http_methods(["POST"])
def change_password(request):
    """
    Change user password via AJAX
    """
    try:
        current_password = request.POST.get('current_password')
        new_password = request.POST.get('new_password')
        confirm_password = request.POST.get('confirm_password')
        
        # Validate input
        if not all([current_password, new_password, confirm_password]):
            return JsonResponse({
                'success': False,
                'message': 'All fields are required.'
            }, status=400)
        
        # Check if new password matches confirmation
        if new_password != confirm_password:
            return JsonResponse({
                'success': False,
                'message': 'New password and confirmation password do not match.'
            }, status=400)
        
        # Check if new password is different from current
        if current_password == new_password:
            return JsonResponse({
                'success': False,
                'message': 'New password must be different from current password.'
            }, status=400)
        
        # Validate password strength
        if len(new_password) < 8:
            return JsonResponse({
                'success': False,
                'message': 'Password must be at least 8 characters long.'
            }, status=400)
        
        # Verify current password
        if not request.user.check_password(current_password):
            return JsonResponse({
                'success': False,
                'message': 'Current password is incorrect.'
            }, status=400)
        
        # Set new password
        request.user.set_password(new_password)
        request.user.save()
        
        return JsonResponse({
            'success': True,
            'message': 'Password changed successfully!'
        })
        
    except Exception as e:
        logger.error(f"Error changing password: {str(e)}")
        return JsonResponse({
            'success': False,
            'message': 'An error occurred while changing password.'
        }, status=500)


@login_required
@csrf_exempt
@require_http_methods(["POST"])
def update_preferences(request):
    """
    Update user preferences (language and timezone settings)
    """
    try:
        data = json.loads(request.body)
        
        # Update user profile preferences
        if 'language' in data:
            request.user.language = data['language']
        if 'user_timezone' in data:
            request.user.user_timezone = data['user_timezone']
        
        request.user.save()
        
        return JsonResponse({
            'success': True,
            'message': 'Preferences updated successfully!'
        })
        
    except Exception as e:
        logger.error(f"Error updating preferences: {str(e)}")
        return JsonResponse({
            'success': False,
            'message': 'An error occurred while updating preferences.'
        }, status=500)


@login_required
def profile(request):
    
    try:
        
        if request.user.user_type == 'CUSTOMER':
            total_shipments = Shipment.objects.filter(customer=request.user).count()
            completed_shipments = Shipment.objects.filter(customer=request.user, status='delivered').count()
            user_payments = Payment.objects.filter(payer=request.user)
        elif request.user.user_type == 'LOGISTICS':
            total_shipments = Shipment.objects.filter(provider=request.user).count()
            completed_shipments = Shipment.objects.filter(provider=request.user, status='delivered').count()
            user_payments = Payment.objects.filter(payee=request.user)
        else:
            total_shipments = 0
            completed_shipments = 0
            user_payments = []

        total_spent = sum(payment.amount for payment in user_payments) if user_payments else 0

        # Get language display name
        language_choices = {
            'en': 'English',
            'es': 'Español', 
            'fr': 'Français',
            'de': 'Deutsch',
            'ar': 'العربية'
        }
        language_display = language_choices.get(request.user.language, 'English')

        # Get short verification status name
        verification_status_mapping = {
            'PENDING': 'Pending',
            'IN_REVIEW': 'In Review',
            'VERIFIED': 'Verified',
            'REJECTED': 'Rejected'
        }
        verification_status = verification_status_mapping.get(request.user.verification_status, 'Pending')

        profile_data = {
            "personal_info": {
                "first_name": request.user.first_name or "",
                "last_name": request.user.last_name or "",
                "email": request.user.email,
                "phone": request.user.phone_number or "",
                "country": request.user.country or "",
                "timezone": request.user.user_timezone or "UTC"
            },
            "company_info": {
                "company_name": request.user.company_name or "",
                "company_type": request.user.user_type,
                "tax_id": request.user.tax_id or "",
                "address": request.user.address or "",
                "website": request.user.website or ""
            },
            "account_stats": {
                "member_since": request.user.created_at.strftime("%B %Y") if request.user.created_at else "Unknown",
                "total_shipments": total_shipments,
                "completed_shipments": completed_shipments,
                "total_spent": f"${total_spent:.2f}",
                "verification_status": verification_status
            },
            "preferences": {
                "language": language_display,
                "language_code": request.user.language,
                "user_timezone": request.user.user_timezone
            }
        }
       
        context = {
        "request": request,
        "user": request.user,
        "profile": profile_data,
        "title": "Profile - Cloverics"
        }
        return render(request, 'customers/profile.html', context)

    except Exception as e:
        logger.error(f"Error displaying profile: {str(e)}")
        return render(request, "error/500.html", {"error": str(e)})
        