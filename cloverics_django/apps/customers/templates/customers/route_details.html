{% extends "base.html" %}

{% block content %}
<div class="page-container">
    <div class="page-header">
        <h1>Route Details</h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="/customer/search-shipping">Search Shipping</a></li>
                <li class="breadcrumb-item active">Route Details</li>
            </ol>
        </nav>
    </div>

    <div class="row">
        <div class="col-lg-8 col-md-12 mb-4">
            <div class="route-detail-card">
                <div class="route-header">
                    <div class="provider-info">
                        <h3>{{ route.provider_name }}</h3>
                        <div class="rating-section">
                            {% for i in range(5) %}
                                {% if i < route.provider_rating %}
                                    <i class="fas fa-star text-warning"></i>
                                {% else %}
                                    <i class="far fa-star text-muted"></i>
                                {% endif %}
                            {% endfor %}
                            <span class="rating-text">({{ route.provider_rating }}/5)</span>
                            <span class="experience-badge">{{ route.provider_experience }} experience</span>
                        </div>
                    </div>
                    <div class="price-section">
                        <span class="price">${{ route.price_per_kg }}</span>
                        <span class="unit">per kg</span>
                    </div>
                </div>

                <div class="route-path-section">
                    <div class="route-path">
                        <div class="location origin">
                            <i class="fas fa-map-marker-alt text-success"></i>
                            <span>{{ route.origin_country }}</span>
                        </div>
                        <div class="route-arrow">
                            <i class="fas fa-arrow-right"></i>
                        </div>
                        <div class="location destination">
                            <i class="fas fa-map-marker-alt text-danger"></i>
                            <span>{{ route.destination_country }}</span>
                        </div>
                    </div>
                </div>

                <div class="route-specs-section">
                    <div class="spec-grid">
                        <div class="spec-item">
                            <i class="fas fa-truck"></i>
                            <div>
                                <strong>Transport Type</strong>
                                <span>{{ route.transport_type }}</span>
                            </div>
                        </div>
                        <div class="spec-item">
                            <i class="fas fa-clock"></i>
                            <div>
                                <strong>Transit Time</strong>
                                <span>{{ route.transit_time }} days</span>
                            </div>
                        </div>
                        <div class="spec-item">
                            <i class="fas fa-weight"></i>
                            <div>
                                <strong>Max Weight</strong>
                                <span>{{ route.max_weight }} kg</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="features-section">
                    <h5>Included Features</h5>
                    <div class="features-grid">
                        {% if route.insurance_included %}
                        <div class="feature-item">
                            <i class="fas fa-shield-alt text-success"></i>
                            <span>Insurance Coverage</span>
                        </div>
                        {% endif %}
                        {% if route.tracking_available %}
                        <div class="feature-item">
                            <i class="fas fa-satellite text-primary"></i>
                            <span>Real-time Tracking</span>
                        </div>
                        {% endif %}
                        {% if route.customs_handling %}
                        <div class="feature-item">
                            <i class="fas fa-file-alt text-info"></i>
                            <span>Customs Handling</span>
                        </div>
                        {% endif %}
                    </div>
                </div>

                <div class="description-section">
                    <h5>Service Description</h5>
                    <p>{{ route.description }}</p>
                </div>

                <div class="additional-services-section">
                    <h5>Additional Services</h5>
                    <ul class="services-list">
                        {% for service in route.additional_services %}
                        <li><i class="fas fa-check text-success"></i> {{ service }}</li>
                        {% endfor %}
                    </ul>
                </div>

                <div class="coverage-section">
                    <h5>Coverage Areas</h5>
                    <div class="coverage-tags">
                        {% for area in route.coverage_areas %}
                        <span class="coverage-tag">{{ area }}</span>
                        {% endfor %}
                    </div>
                </div>

                <div class="terms-section">
                    <h5>Terms & Conditions</h5>
                    <p class="terms-text">{{ route.terms }}</p>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="action-card">
                <h5>Request Quote</h5>
                <p>Get a personalized quote for your shipment with this provider.</p>
                <a href="/customer/request-quote/{{ route.id }}" class="btn btn-primary btn-lg w-100">
                    <i class="fas fa-paper-plane"></i> Request Quote
                </a>
                
                <hr>
                
                <h6>Quick Contact</h6>
                <p class="text-muted">Need more information? Contact the provider directly.</p>
                <div class="contact-buttons">
                    <button class="btn btn-outline-secondary btn-sm w-100 mb-2" onclick="callProvider()">
                        <i class="fas fa-phone"></i> Call Provider
                    </button>
                    <button class="btn btn-outline-secondary btn-sm w-100" onclick="sendMessage()">
                        <i class="fas fa-envelope"></i> Send Message
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function callProvider() {
    // In a real implementation, this would initiate a call or show provider's phone number
    alert('Provider Contact: +****************\n\nThis would normally initiate a call to the logistics provider or display their contact information.');
}

function sendMessage() {
    const message = prompt('Enter your message to {{ route.provider_name }}:');
    if (message && message.trim()) {
        // Send message to database via API
        fetch('/api/send-message-to-provider', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                'provider_id': '{{ route.provider_id }}',
                'message_content': message,
                'route_id': '{{ route.id }}'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Message sent successfully to {{ route.provider_name }}!\n\nYour message: "' + message + '"\n\nThey will respond within 24 hours.');
            } else {
                alert('Failed to send message: ' + (data.error || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Error sending message:', error);
            alert('Failed to send message. Please try again.');
        });
    }
}
</script>
{% endblock %}

{% block extra_css %}
<style>
.route-detail-card {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
}

.route-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e9ecef;
}

.provider-info h3 {
    margin: 0;
    color: #2c3e50;
    font-weight: 600;
}

.rating-section {
    margin-top: 0.5rem;
}

.rating-text {
    margin-left: 0.5rem;
    color: #666;
}

.experience-badge {
    background: #e7f3ff;
    color: #0066cc;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.8rem;
    margin-left: 1rem;
}

.price-section {
    text-align: right;
}

.price {
    font-size: 2rem;
    font-weight: 700;
    color: #27ae60;
    display: block;
}

.unit {
    color: #666;
    font-size: 0.9rem;
}

.route-path-section {
    margin-bottom: 2rem;
}

.route-path {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
}

.location {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
}

.route-arrow {
    margin: 0 2rem;
    font-size: 1.2rem;
    color: #666;
}

.spec-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.spec-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
}

.spec-item i {
    font-size: 1.2rem;
    color: #666;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem;
    background: #f8f9fa;
    border-radius: 6px;
}

.services-list {
    list-style: none;
    padding: 0;
}

.services-list li {
    padding: 0.5rem 0;
    border-bottom: 1px solid #f0f0f0;
}

.services-list li:last-child {
    border-bottom: none;
}

.coverage-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.coverage-tag {
    background: #e9ecef;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.9rem;
    color: #495057;
}

.action-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 2rem;
}

.contact-buttons {
    margin-top: 1rem;
}

.terms-text {
    font-size: 0.9rem;
    color: #666;
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 6px;
}

@media (max-width: 768px) {
    .route-header {
        flex-direction: column;
        gap: 1rem;
    }
    
    .route-path {
        flex-direction: column;
        gap: 1rem;
    }
    
    .route-arrow {
        transform: rotate(90deg);
        margin: 1rem 0;
    }
}

/* Additional Responsive Design */
@media (max-width: 1200px) {
    .page-container {
        padding: 1rem;
    }
    
    .route-detail-card {
        margin-bottom: 1rem;
    }
}

@media (max-width: 992px) {
    .page-container {
        padding: 0.75rem;
    }
    
    .page-header h1 {
        font-size: 1.75rem;
    }
    
    .route-header h3 {
        font-size: 1.5rem;
    }
    
    .spec-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
    }
    
    .features-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 10px;
    }
}

@media (max-width: 768px) {
    .page-container {
        padding: 0.5rem;
    }
    
    .page-header h1 {
        font-size: 1.5rem;
    }
    
    .page-header p {
        font-size: 0.9rem;
    }
    
    .route-detail-card {
        padding: 1rem;
    }
    
    .route-header h3 {
        font-size: 1.25rem;
    }
    
    .spec-grid {
        grid-template-columns: 1fr;
        gap: 10px;
    }
    
    .features-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 8px;
    }
    
    .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }
}

@media (max-width: 576px) {
    .page-container {
        padding: 0.25rem;
    }
    
    .page-header h1 {
        font-size: 1.25rem;
    }
    
    .page-header p {
        font-size: 0.8rem;
    }
    
    .route-detail-card {
        padding: 0.75rem;
    }
    
    .route-header h3 {
        font-size: 1.1rem;
    }
    
    .spec-item {
        padding: 0.5rem;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
        gap: 5px;
    }
    
    .btn {
        font-size: 0.9rem;
        padding: 0.75rem 1rem;
    }
    
    .breadcrumb {
        font-size: 0.8rem;
    }
}
</style>
{% endblock %}