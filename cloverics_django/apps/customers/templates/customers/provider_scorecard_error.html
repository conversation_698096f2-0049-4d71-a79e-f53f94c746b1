<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="/static/base.css" rel="stylesheet">
    <style>
        .error-container {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .error-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            padding: 40px;
            text-align: center;
            max-width: 500px;
            width: 100%;
        }
        
        .error-icon {
            font-size: 4rem;
            color: #dc3545;
            margin-bottom: 20px;
        }
        
        .error-title {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 15px;
            color: #343a40;
        }
        
        .error-message {
            color: #6c757d;
            margin-bottom: 30px;
            font-size: 1.1rem;
            line-height: 1.6;
        }
        
        .action-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: 500;
        }
        
        .btn-outline-secondary {
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: 500;
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            .error-container {
                padding: 15px;
            }
            
            .error-card {
                padding: 30px;
            }
        }

        @media (max-width: 992px) {
            .error-container {
                padding: 10px;
            }
            
            .error-card {
                padding: 25px;
                max-width: 450px;
            }
            
            .error-icon {
                font-size: 3.5rem;
            }
            
            .error-title {
                font-size: 1.75rem;
            }
            
            .error-message {
                font-size: 1rem;
            }
        }

        @media (max-width: 768px) {
            .error-container {
                padding: 5px;
            }
            
            .error-card {
                padding: 20px;
                max-width: 400px;
            }
            
            .error-icon {
                font-size: 3rem;
            }
            
            .error-title {
                font-size: 1.5rem;
            }
            
            .error-message {
                font-size: 0.95rem;
            }
            
            .action-buttons {
                flex-direction: column;
                width: 100%;
            }
            
            .action-buttons .btn {
                width: 100%;
                margin-bottom: 10px;
            }
            
            .btn-text {
                display: none;
            }
        }

        @media (max-width: 576px) {
            .error-card {
                padding: 15px;
                max-width: 350px;
            }
            
            .error-icon {
                font-size: 2.5rem;
            }
            
            .error-title {
                font-size: 1.25rem;
            }
            
            .error-message {
                font-size: 0.9rem;
            }
            
            .action-buttons .btn {
                padding: 10px 20px;
                font-size: 0.9rem;
            }
        }

        /* Landscape orientation adjustments for mobile */
        @media (max-width: 768px) and (orientation: landscape) {
            .action-buttons {
                flex-direction: row;
                flex-wrap: wrap;
            }
            
            .action-buttons .btn {
                width: auto;
                margin-bottom: 0;
                flex: 1;
            }
            
            .btn-text {
                display: inline;
            }
        }
    </style>
</head>
<body>
<!-- Authentication Check -->
{% if not user or not user.is_authenticated %}
<script>
    window.location.href = '/login';
</script>
{% endif %}

    <div class="error-container">
        <div class="error-card">
            <div class="error-icon">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            
            <h1 class="error-title">Scorecard Unavailable</h1>
            
            <p class="error-message">
                {{ error }}
            </p>
            
            <div class="action-buttons">
                <a href="/dashboard" class="btn btn-primary">
                    <i class="fas fa-home me-2"></i><span class="btn-text">Back to Dashboard</span>
                </a>
                <a href="/search-shipping" class="btn btn-outline-secondary">
                    <i class="fas fa-search me-2"></i><span class="btn-text">Search Providers</span>
                </a>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
    // Responsive behavior for button text
    document.addEventListener('DOMContentLoaded', function() {
        function handleResize() {
            const isMobile = window.innerWidth <= 576;
            const btnTexts = document.querySelectorAll('.btn-text');
            
            btnTexts.forEach(text => {
                text.style.display = isMobile ? 'none' : 'inline';
            });
        }
        
        // Initial call
        handleResize();
        
        // Listen for window resize
        window.addEventListener('resize', handleResize);
    });
    </script>
</body>
</html>