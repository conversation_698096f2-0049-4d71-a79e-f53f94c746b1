{% extends "base.html" %}
{% load translation_tags %}

{% block content %}
<div class="page-container">
    <div class="page-header">
        <h1>{% translate 'profile' %}</h1>
        <p>{% translate 'manage_your_account_information' %}</p>
    </div>

    <div class="row">
        <!-- Personal Information -->
        <div class="col-lg-8 col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-user"></i> {% translate 'personal_information' %}</h5>
                </div>
                <div class="card-body">
                    <form id="profileForm">
                        {% csrf_token %}
                        <div class="row">
                            <div class="col-lg-6 col-md-6 col-sm-12 mb-3">
                                <div class="form-group">
                                    <label for="first_name">{% translate 'first_name' %}</label>
                                    <input type="text" class="form-control" id="first_name" name="first_name" 
                                           value="{{ profile.personal_info.first_name }}" required>
                                </div>
                            </div>
                            <div class="col-lg-6 col-md-6 col-sm-12 mb-3">
                                <div class="form-group">
                                    <label for="last_name">{% translate 'last_name' %}</label>
                                    <input type="text" class="form-control" id="last_name" name="last_name" 
                                           value="{{ profile.personal_info.last_name }}" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-lg-6 col-md-6 col-sm-12 mb-3">
                                <div class="form-group">
                                    <label for="email">{% translate 'email_address' %}</label>
                                    <input type="email" class="form-control" id="email" name="email" 
                                           value="{{ profile.personal_info.email }}" readonly>
                                    <small class="form-text text-muted">{% translate 'contact_support_to_change_email' %}</small>
                                </div>
                            </div>
                            <div class="col-lg-6 col-md-6 col-sm-12 mb-3">
                                <div class="form-group">
                                    <label for="phone">{% translate 'phone_number' %}</label>
                                    <input type="tel" class="form-control" id="phone" name="phone" 
                                           value="{{ profile.personal_info.phone }}" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-lg-6 col-md-6 col-sm-12 mb-3">
                                <div class="form-group">
                                    <label for="country">{% translate 'country' %}</label>
                                    <select class="form-control" id="country" name="country">
                                        <option value="United States" {% if profile.personal_info.country == "United States" %}selected{% endif %}>United States</option>
                                        <option value="Canada" {% if profile.personal_info.country == "Canada" %}selected{% endif %}>Canada</option>
                                        <option value="United Kingdom" {% if profile.personal_info.country == "United Kingdom" %}selected{% endif %}>United Kingdom</option>
                                        <option value="Germany" {% if profile.personal_info.country == "Germany" %}selected{% endif %}>Germany</option>
                                        <option value="France" {% if profile.personal_info.country == "France" %}selected{% endif %}>France</option>
                                        <option value="Other" {% if profile.personal_info.country and profile.personal_info.country not in "United States,Canada,United Kingdom,Germany,France" %}selected{% endif %}>Other</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-lg-6 col-md-6 col-sm-12 mb-3">
                                <div class="form-group">
                                    <label for="timezone">{% translate 'timezone' %}</label>
                                    <select class="form-control" id="timezone" name="timezone">
                                        <option value="UTC" {% if profile.personal_info.timezone == "UTC" %}selected{% endif %}>UTC</option>
                                        <option value="America/New_York" {% if profile.personal_info.timezone == "America/New_York" %}selected{% endif %}>Eastern Time</option>
                                        <option value="America/Chicago" {% if profile.personal_info.timezone == "America/Chicago" %}selected{% endif %}>Central Time</option>
                                        <option value="America/Denver" {% if profile.personal_info.timezone == "America/Denver" %}selected{% endif %}>Mountain Time</option>
                                        <option value="America/Los_Angeles" {% if profile.personal_info.timezone == "America/Los_Angeles" %}selected{% endif %}>Pacific Time</option>
                                        <option value="Europe/London" {% if profile.personal_info.timezone == "Europe/London" %}selected{% endif %}>London</option>
                                        <option value="Europe/Paris" {% if profile.personal_info.timezone == "Europe/Paris" %}selected{% endif %}>Paris</option>
                                        <option value="Asia/Tokyo" {% if profile.personal_info.timezone == "Asia/Tokyo" %}selected{% endif %}>Tokyo</option>
                                        <option value="Asia/Shanghai" {% if profile.personal_info.timezone == "Asia/Shanghai" %}selected{% endif %}>Shanghai</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <button type="submit" class="btn btn-primary" id="savePersonalBtn">
                            <i class="fas fa-save"></i> {% translate 'save_changes' %}
                        </button>
                        <div id="personalMessage" class="mt-2"></div>
                    </form>
                </div>
            </div>

            <!-- Company Information -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5><i class="fas fa-building"></i> {% translate 'company_information' %}</h5>
                </div>
                <div class="card-body">
                    <form id="companyForm">
                        {% csrf_token %}
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="company_name">{% translate 'company_name' %}</label>
                                    <input type="text" class="form-control" id="company_name" name="company_name" 
                                           value="{{ profile.company_info.company_name }}" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="industry">{% translate 'industry' %}</label>
                                    <select class="form-control" id="industry" name="industry">
                                        <option value="Manufacturing" {% if profile.company_info.company_type == "Manufacturing" %}selected{% endif %}>{% translate 'manufacturing' %}</option>
                                        <option value="Retail" {% if profile.company_info.company_type == "Retail" %}selected{% endif %}>{% translate 'retail' %}</option>
                                        <option value="Technology" {% if profile.company_info.company_type == "Technology" %}selected{% endif %}>{% translate 'technology' %}</option>
                                        <option value="Healthcare" {% if profile.company_info.company_type == "Healthcare" %}selected{% endif %}>{% translate 'healthcare' %}</option>
                                        <option value="Other" {% if profile.company_info.company_type and profile.company_info.company_type not in "Manufacturing,Retail,Technology,Healthcare" %}selected{% endif %}>{% translate 'other' %}</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="tax_id">{% translate 'tax_id' %}</label>
                                    <input type="text" class="form-control" id="tax_id" name="tax_id" 
                                           value="{{ profile.company_info.tax_id }}">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="website">{% translate 'website' %}</label>
                                    <input type="url" class="form-control" id="website" name="website" 
                                           value="{{ profile.company_info.website }}">
                                </div>
                            </div>
                        </div>
                        <button type="submit" class="btn btn-primary" id="saveCompanyBtn">
                            <i class="fas fa-save"></i> {% translate 'save_changes' %}
                        </button>
                        <div id="companyMessage" class="mt-2"></div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Account Settings -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-cog"></i> {% translate 'account_settings' %}</h5>
                </div>
                <div class="card-body">
                    <form id="preferencesForm">
                        {% csrf_token %}
                        <div class="mb-3">
                            <h6>{% translate 'language' %}</h6>
                            <select class="form-control" id="language" name="language">
                                <option value="en" {% if profile.preferences.language_code == "en" %}selected{% endif %}>English</option>
                                <option value="es" {% if profile.preferences.language_code == "es" %}selected{% endif %}>Español</option>
                                <option value="fr" {% if profile.preferences.language_code == "fr" %}selected{% endif %}>Français</option>
                                <option value="de" {% if profile.preferences.language_code == "de" %}selected{% endif %}>Deutsch</option>
                                <option value="ar" {% if profile.preferences.language_code == "ar" %}selected{% endif %}>العربية</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <h6>{% translate 'timezone' %}</h6>
                            <select class="form-control" id="user_timezone" name="user_timezone">
                                <option value="UTC" {% if profile.preferences.user_timezone == "UTC" %}selected{% endif %}>UTC</option>
                                <option value="America/New_York" {% if profile.preferences.user_timezone == "America/New_York" %}selected{% endif %}>Eastern Time</option>
                                <option value="America/Chicago" {% if profile.preferences.user_timezone == "America/Chicago" %}selected{% endif %}>Central Time</option>
                                <option value="America/Denver" {% if profile.preferences.user_timezone == "America/Denver" %}selected{% endif %}>Mountain Time</option>
                                <option value="America/Los_Angeles" {% if profile.preferences.user_timezone == "America/Los_Angeles" %}selected{% endif %}>Pacific Time</option>
                                <option value="Europe/London" {% if profile.preferences.user_timezone == "Europe/London" %}selected{% endif %}>London</option>
                                <option value="Europe/Paris" {% if profile.preferences.user_timezone == "Europe/Paris" %}selected{% endif %}>Paris</option>
                                <option value="Asia/Tokyo" {% if profile.preferences.user_timezone == "Asia/Tokyo" %}selected{% endif %}>Tokyo</option>
                                <option value="Asia/Shanghai" {% if profile.preferences.user_timezone == "Asia/Shanghai" %}selected{% endif %}>Shanghai</option>
                            </select>
                        </div>
                        
                        <button type="submit" class="btn btn-primary w-100" id="savePreferencesBtn">
                            <i class="fas fa-save"></i> {% translate 'save_preferences' %}
                        </button>
                        <div id="preferencesMessage" class="mt-2"></div>
                    </form>
                    
                    <hr class="my-4">
                    
                    <div class="mb-3">
                        <h6 class="mb-3">{% translate 'security' %}</h6>
                        <button class="btn btn-outline-secondary btn-sm w-100 mb-3" onclick="openChangePasswordModal()">
                            <i class="fas fa-key"></i> {% translate 'change_password' %}
                        </button>
                        <button class="btn btn-outline-secondary btn-sm w-100">
                            <i class="fas fa-shield-alt"></i> {% translate 'two_factor_auth' %}
                        </button>
                    </div>
                </div>
            </div>

            <!-- Account Statistics -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5><i class="fas fa-chart-bar"></i> {% translate 'account_statistics' %}</h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <h4 class="text-primary">{{ profile.account_stats.total_shipments }}</h4>
                            <small class="text-muted">{% translate 'total_shipments' %}</small>
                        </div>
                        <div class="col-6">
                            <h4 class="text-success">{{ profile.account_stats.completed_shipments }}</h4>
                            <small class="text-muted">{% translate 'completed_shipments' %}</small>
                        </div>
                    </div>
                    <div class="row text-center mt-3">
                        <div class="col-6">
                            <h4 class="text-info">{{ profile.account_stats.total_spent }}</h4>
                            <small class="text-muted">{% translate 'total_spent' %}</small>
                        </div>
                        <div class="col-6">
                            <h4 class="text-warning">{{ profile.account_stats.verification_status }}</h5>
                            <small class="text-muted">{% translate 'verification_status' %}</small>
                        </div>
                    </div>
                    <div class="row text-center mt-3">
                        <div class="col-12">
                            <small class="text-muted">{% translate 'member_since' %}: {{ profile.account_stats.member_since }}</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Change Password Modal -->
<div class="modal fade" id="changePasswordModal" tabindex="-1" aria-labelledby="changePasswordModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="changePasswordModalLabel">
                    <i class="fas fa-key"></i> {% translate 'change_password' %}
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="changePasswordForm">
                    {% csrf_token %}
                    <div class="form-group mb-3">
                        <label for="current_password">{% translate 'current_password' %}</label>
                        <input type="password" class="form-control" id="current_password" name="current_password" required>
                    </div>
                    <div class="form-group mb-3">
                        <label for="new_password">{% translate 'new_password' %}</label>
                        <input type="password" class="form-control" id="new_password" name="new_password" required>
                        <small class="form-text text-muted">{% translate 'password_minimum_8_characters' %}</small>
                    </div>
                    <div class="form-group mb-3">
                        <label for="confirm_password">{% translate 'confirm_new_password' %}</label>
                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% translate 'cancel' %}</button>
                <button type="button" class="btn btn-primary" onclick="submitPasswordChange()">
                    <i class="fas fa-save"></i> {% translate 'change_password' %}
                </button>
            </div>
        </div>
    </div>
</div>

{% endblock content %}

{% block extra_css %}
<style>
.card {
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border-radius: 8px;
}

.card-header {
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
    padding: 1rem 1.25rem;
}

.card-header h5 {
    margin: 0;
    font-size: 1.1rem;
    color: #495057;
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    font-weight: 500;
    color: #495057;
}

.btn-primary {
    background: #007bff;
    border: none;
    padding: 0.5rem 1.5rem;
}

.btn-primary:hover {
    background: #0056b3;
}

/* Security section styling */
.card-body hr {
    border-color: #e9ecef;
    margin: 1.5rem 0;
}

.card-body h6 {
    color: #495057;
    font-weight: 600;
    margin-bottom: 1rem;
}

.btn-outline-secondary {
    border-color: #6c757d;
    color: #6c757d;
    transition: all 0.2s ease;
}

.btn-outline-secondary:hover {
    background-color: #6c757d;
    border-color: #6c757d;
    color: white;
}

/* Verification status styling */
.verification-status {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .card {
        margin-bottom: 1.5rem;
    }
}

@media (max-width: 992px) {
    .card {
        margin-bottom: 1rem;
    }
    
    .card-header h5 {
        font-size: 1rem;
    }
}

@media (max-width: 768px) {
    .card {
        margin-bottom: 1rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .form-group {
        margin-bottom: 0.75rem;
    }
    
    .form-group label {
        font-size: 0.9rem;
    }
    
    .form-control {
        font-size: 0.9rem;
        padding: 0.5rem 0.75rem;
    }
    
    .btn-primary {
        width: 100%;
        padding: 0.75rem 1rem;
    }
}

@media (max-width: 576px) {
    .card-body {
        padding: 0.75rem;
    }
    
    .card-header {
        padding: 0.75rem;
    }
    
    .card-header h5 {
        font-size: 0.9rem;
    }
    
    .form-group label {
        font-size: 0.85rem;
    }
    
    .form-control {
        font-size: 0.85rem;
        padding: 0.5rem;
    }
    
    .form-text {
        font-size: 0.75rem;
    }
    
    .btn-primary {
        font-size: 0.9rem;
    }
}
</style>
{% endblock %}

{% block extra_js %}
<script>
// Function to show messages
function showMessage(elementId, message, isSuccess = true) {
    const messageElement = document.getElementById(elementId);
    messageElement.innerHTML = `<div class="alert alert-${isSuccess ? 'success' : 'danger'} alert-dismissible fade show" role="alert">
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>`;
    
    // Auto-hide after 5 seconds
    setTimeout(() => {
        messageElement.innerHTML = '';
    }, 5000);
}

// Function to get CSRF token
function getCSRFToken() {
    return document.querySelector('[name=csrfmiddlewaretoken]').value;
}

// Personal Information Form
document.getElementById('profileForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const form = this;
    const submitBtn = document.getElementById('savePersonalBtn');
    const originalText = submitBtn.innerHTML;
    
    // Disable button and show loading
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';
    
    // Get form data
    const formData = new FormData(form);
    
    // Send AJAX request
    fetch('{% url "customers:update_personal_information" %}', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': getCSRFToken(),
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage('personalMessage', data.message, true);
        } else {
            showMessage('personalMessage', data.message || 'An error occurred while updating personal information.', false);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showMessage('personalMessage', 'An error occurred while updating personal information.', false);
    })
    .finally(() => {
        // Re-enable button
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
    });
});

// Company Information Form
document.getElementById('companyForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const form = this;
    const submitBtn = document.getElementById('saveCompanyBtn');
    const originalText = submitBtn.innerHTML;
    
    // Disable button and show loading
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';
    
    // Get form data
    const formData = new FormData(form);
    
    // Send AJAX request
    fetch('{% url "customers:update_company_information" %}', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': getCSRFToken(),
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage('companyMessage', data.message, true);
        } else {
            showMessage('companyMessage', data.message || 'An error occurred while updating company information.', false);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showMessage('companyMessage', 'An error occurred while updating company information.', false);
    })
    .finally(() => {
        // Re-enable button
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
    });
});

// Password Change Functions
function openChangePasswordModal() {
    // Clear the form
    document.getElementById('changePasswordForm').reset();
    // Show the modal
    const modal = new bootstrap.Modal(document.getElementById('changePasswordModal'));
    modal.show();
}

function submitPasswordChange() {
    const form = document.getElementById('changePasswordForm');
    const submitBtn = document.querySelector('#changePasswordModal .btn-primary');
    const originalText = submitBtn.innerHTML;
    
    // Get form data
    const formData = new FormData(form);
    
    // Basic client-side validation
    const currentPassword = formData.get('current_password');
    const newPassword = formData.get('new_password');
    const confirmPassword = formData.get('confirm_password');
    
    if (!currentPassword || !newPassword || !confirmPassword) {
        showMessage('passwordMessage', 'All fields are required.', false);
        return;
    }
    
    if (newPassword !== confirmPassword) {
        showMessage('passwordMessage', 'New password and confirmation password do not match.', false);
        return;
    }
    
    if (newPassword.length < 8) {
        showMessage('passwordMessage', 'Password must be at least 8 characters long.', false);
        return;
    }
    
    // Disable button and show loading
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Changing Password...';
    
    // Send AJAX request
    fetch('{% url "customers:change_password" %}', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': getCSRFToken(),
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage('passwordMessage', data.message, true);
            // Close the modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('changePasswordModal'));
            modal.hide();
            // Clear the form
            form.reset();
        } else {
            showMessage('passwordMessage', data.message || 'An error occurred while changing password.', false);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showMessage('passwordMessage', 'An error occurred while changing password.', false);
    })
    .finally(() => {
        // Re-enable button
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
    });
}

// Preferences Form
document.getElementById('preferencesForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const form = this;
    const submitBtn = document.getElementById('savePreferencesBtn');
    const originalText = submitBtn.innerHTML;
    
    // Disable button and show loading
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';
    
    // Collect form data
    const formData = {
        language: document.getElementById('language').value,
        user_timezone: document.getElementById('user_timezone').value
    };
    
    // Send AJAX request
    fetch('{% url "customers:update_preferences" %}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCSRFToken(),
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage('preferencesMessage', data.message, true);
        } else {
            showMessage('preferencesMessage', data.message || 'An error occurred while updating preferences.', false);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showMessage('preferencesMessage', 'An error occurred while updating preferences.', false);
    })
    .finally(() => {
        // Re-enable button
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
    });
});

// Add message container to modal
document.addEventListener('DOMContentLoaded', function() {
    const modalBody = document.querySelector('#changePasswordModal .modal-body');
    const form = document.getElementById('changePasswordForm');
    
    // Add message container after the form
    const messageDiv = document.createElement('div');
    messageDiv.id = 'passwordMessage';
    messageDiv.className = 'mt-2';
    modalBody.appendChild(messageDiv);
});
</script>
{% endblock %} 