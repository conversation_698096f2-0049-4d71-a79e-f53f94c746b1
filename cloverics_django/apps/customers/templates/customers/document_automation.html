{% extends "base.html" %}

{% block title %}Document Automation & Digital Workflow - Cloverics{% endblock %}

{% block head %}
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<style>
    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem 0;
        margin-bottom: 2rem;
    }
    
    .nav-tabs .nav-link {
        color: #495057;
        border: none;
        padding: 1rem 1.5rem;
        margin-right: 0.5rem;
        border-radius: 10px 10px 0 0;
        background: #f8f9fa;
        transition: all 0.3s ease;
    }
    
    .nav-tabs .nav-link.active {
        background: #007bff;
        color: white;
        border: none;
    }
    
    .nav-tabs .nav-link:hover {
        background: #e9ecef;
        color: #007bff;
    }
    
    .tab-content {
        background: white;
        border-radius: 0 10px 10px 10px;
        box-shadow: 0 0 20px rgba(0,0,0,0.1);
        padding: 2rem;
        min-height: 600px;
    }
    
    .metric-card {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        border-left: 5px solid #007bff;
        transition: transform 0.3s ease;
        margin-bottom: 1rem;
    }
    
    .metric-card:hover {
        transform: translateY(-5px);
    }
    
    .metric-card .metric-value {
        font-size: 2.5rem;
        font-weight: bold;
        color: #007bff;
        margin: 0;
    }
    
    .metric-card .metric-label {
        color: #6c757d;
        font-size: 0.9rem;
        margin: 0;
    }
    
    .document-card {
        background: white;
        border-radius: 10px;
        padding: 1.5rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        border: 1px solid #e9ecef;
        margin-bottom: 1rem;
        transition: all 0.3s ease;
    }
    
    .document-card:hover {
        box-shadow: 0 5px 20px rgba(0,0,0,0.15);
        transform: translateY(-2px);
    }
    
    .document-type-badge {
        background: linear-gradient(45deg, #28a745, #20c997);
        color: white;
        padding: 0.3rem 0.8rem;
        border-radius: 20px;
        font-size: 0.8rem;
        display: inline-block;
        margin-bottom: 0.5rem;
    }
    
    .workflow-status {
        padding: 0.25rem 0.75rem;
    }

    /* Responsive Design */
    @media (max-width: 1200px) {
        .page-header {
            padding: 1.5rem 0;
            margin-bottom: 1.5rem;
        }
        
        .tab-content {
            padding: 1.5rem;
            min-height: 500px;
        }
        
        .metric-card {
            padding: 1.25rem;
        }
        
        .document-card {
            padding: 1.25rem;
        }
    }

    @media (max-width: 992px) {
        .page-header {
            padding: 1.25rem 0;
            margin-bottom: 1.25rem;
        }
        
        .nav-tabs .nav-link {
            padding: 0.75rem 1rem;
            margin-right: 0.25rem;
            font-size: 0.9rem;
        }
        
        .tab-content {
            padding: 1.25rem;
            min-height: 450px;
        }
        
        .metric-card .metric-value {
            font-size: 2rem;
        }
        
        .metric-card .metric-label {
            font-size: 0.85rem;
        }
    }

    @media (max-width: 768px) {
        .page-header {
            padding: 1rem 0;
            margin-bottom: 1rem;
        }
        
        .nav-tabs {
            flex-direction: column;
            width: 100%;
        }
        
        .nav-tabs .nav-item {
            width: 100%;
            margin-bottom: 0.5rem;
        }
        
        .nav-tabs .nav-link {
            width: 100%;
            text-align: center;
            margin-right: 0;
            border-radius: 10px;
        }
        
        .tab-content {
            padding: 1rem;
            min-height: 400px;
            border-radius: 10px;
        }
        
        .metric-card {
            padding: 1rem;
        }
        
        .metric-card .metric-value {
            font-size: 1.75rem;
        }
        
        .document-card {
            padding: 1rem;
        }
        
        .document-type-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.6rem;
        }
    }

    @media (max-width: 576px) {
        .page-header {
            padding: 0.75rem 0;
            margin-bottom: 0.75rem;
        }
        
        .tab-content {
            padding: 0.75rem;
            min-height: 350px;
        }
        
        .metric-card {
            padding: 0.75rem;
        }
        
        .metric-card .metric-value {
            font-size: 1.5rem;
        }
        
        .metric-card .metric-label {
            font-size: 0.8rem;
        }
        
        .document-card {
            padding: 0.75rem;
        }
        
        .document-type-badge {
            font-size: 0.7rem;
            padding: 0.2rem 0.5rem;
        }
        
        .workflow-status {
            font-size: 0.75rem;
            padding: 0.2rem 0.5rem;
        }
    }
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: 500;
    }
    
    .status-draft { background: #ffc107; color: #000; }
    .status-pending { background: #17a2b8; color: white; }
    .status-approved { background: #28a745; color: white; }
    .status-in-progress { background: #007bff; color: white; }
    .status-completed { background: #6f42c1; color: white; }
    
    .btn-generate {
        background: linear-gradient(45deg, #007bff, #0056b3);
        border: none;
        border-radius: 25px;
        padding: 0.75rem 2rem;
        color: white;
        font-weight: 500;
        transition: all 0.3s ease;
    }
    
    .btn-generate:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,123,255,0.4);
        color: white;
    }
    
    .form-floating {
        margin-bottom: 1rem;
    }
    
    .form-control {
        border-radius: 10px;
        border: 2px solid #e9ecef;
        transition: all 0.3s ease;
    }
    
    .form-control:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
    }
    
    .template-selector {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 1rem;
        margin-bottom: 1rem;
    }
    
    .template-option {
        background: white;
        border: 2px solid #e9ecef;
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 0.5rem;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .template-option:hover {
        border-color: #007bff;
        box-shadow: 0 2px 10px rgba(0,123,255,0.1);
    }
    
    .template-option.selected {
        border-color: #007bff;
        background: rgba(0,123,255,0.05);
    }
    
    .workflow-timeline {
        position: relative;
        padding-left: 2rem;
    }
    
    .workflow-timeline::before {
        content: '';
        position: absolute;
        left: 0.75rem;
        top: 0;
        bottom: 0;
        width: 2px;
        background: #dee2e6;
    }
    
    .timeline-item {
        position: relative;
        margin-bottom: 1.5rem;
        background: white;
        border-radius: 8px;
        padding: 1rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .timeline-item::before {
        content: '';
        position: absolute;
        left: -1.75rem;
        top: 1rem;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background: #007bff;
        border: 3px solid white;
        box-shadow: 0 0 0 3px #007bff;
    }
    
    .activity-item {
        display: flex;
        align-items: center;
        padding: 0.75rem;
        border-radius: 8px;
        background: #f8f9fa;
        margin-bottom: 0.5rem;
        transition: all 0.3s ease;
    }
    
    .activity-item:hover {
        background: #e9ecef;
    }
    
    .activity-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1rem;
        color: white;
        font-size: 1.2rem;
    }
    
    .icon-invoice { background: #28a745; }
    .icon-bill { background: #007bff; }
    .icon-signature { background: #6f42c1; }
    .icon-workflow { background: #fd7e14; }
</style>
{% endblock %}

{% block content %}
<div class="page-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1><i class="fas fa-file-alt me-3"></i>Document Automation & Digital Workflow</h1>
                <p class="mb-0">Streamline document generation, workflow automation, and digital processing</p>
            </div>
            <div class="col-md-4 text-end">
                <button class="btn btn-light btn-lg" onclick="refreshAnalytics()">
                    <i class="fas fa-sync-alt me-2"></i>Refresh
                </button>
            </div>
        </div>
    </div>
</div>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <ul class="nav nav-tabs" id="documentTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="overview-tab" data-bs-toggle="tab" data-bs-target="#overview" type="button" role="tab">
                        <i class="fas fa-chart-pie me-2"></i>Overview & Analytics
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="templates-tab" data-bs-toggle="tab" data-bs-target="#templates" type="button" role="tab">
                        <i class="fas fa-file-copy me-2"></i>Document Templates
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="generate-tab" data-bs-toggle="tab" data-bs-target="#generate" type="button" role="tab">
                        <i class="fas fa-plus-circle me-2"></i>Generate Documents
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="workflows-tab" data-bs-toggle="tab" data-bs-target="#workflows" type="button" role="tab">
                        <i class="fas fa-sitemap me-2"></i>Workflow Management
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="signatures-tab" data-bs-toggle="tab" data-bs-target="#signatures" type="button" role="tab">
                        <i class="fas fa-signature me-2"></i>Digital Signatures
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="activities-tab" data-bs-toggle="tab" data-bs-target="#activities" type="button" role="tab">
                        <i class="fas fa-history me-2"></i>Recent Activities
                    </button>
                </li>
            </ul>

            <div class="tab-content" id="documentTabContent">
                <!-- Overview & Analytics Tab -->
                <div class="tab-pane fade show active" id="overview" role="tabpanel">
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="metric-card">
                                <p class="metric-value" id="totalDocuments">0</p>
                                <p class="metric-label">Total Documents</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="metric-card">
                                <p class="metric-value" id="activeWorkflows">0</p>
                                <p class="metric-label">Active Workflows</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="metric-card">
                                <p class="metric-value" id="processingTime">0</p>
                                <p class="metric-label">Avg Processing Time</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="metric-card">
                                <p class="metric-value" id="automationRate">0%</p>
                                <p class="metric-label">Automation Rate</p>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="document-card">
                                <h5><i class="fas fa-chart-bar me-2"></i>Document Types Distribution</h5>
                                <canvas id="documentTypesChart" width="400" height="300"></canvas>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="document-card">
                                <h5><i class="fas fa-clock me-2"></i>Workflow Performance</h5>
                                <canvas id="workflowPerformanceChart" width="400" height="300"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Document Templates Tab -->
                <div class="tab-pane fade" id="templates" role="tabpanel">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="document-card">
                                <h5><i class="fas fa-filter me-2"></i>Filter Templates</h5>
                                <div class="form-floating mb-3">
                                    <select class="form-control" id="templateTypeFilter">
                                        <option value="">All Types</option>
                                        <option value="commercial_invoice">Commercial Invoice</option>
                                        <option value="packing_list">Packing List</option>
                                        <option value="bill_of_lading">Bill of Lading</option>
                                        <option value="certificate_of_origin">Certificate of Origin</option>
                                    </select>
                                    <label for="templateTypeFilter">Document Type</label>
                                </div>
                                <button class="btn btn-primary w-100" onclick="filterTemplates()">
                                    <i class="fas fa-search me-2"></i>Apply Filter
                                </button>
                            </div>
                        </div>
                        <div class="col-md-8">
                            <div id="templatesContainer">
                                <!-- Templates will be loaded here -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Generate Documents Tab -->
                <div class="tab-pane fade" id="generate" role="tabpanel">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="document-card">
                                <h5><i class="fas fa-file-plus me-2"></i>Generate Single Document</h5>
                                <form id="generateDocumentForm">
                                    <div class="template-selector">
                                        <h6>Select Template</h6>
                                        <div id="templateOptions">
                                            <!-- Template options will be loaded here -->
                                        </div>
                                    </div>
                                    
                                    <div class="form-floating mb-3">
                                        <select class="form-control" id="documentFormat" name="document_format">
                                            <option value="PDF">PDF</option>
                                            <option value="DOCX">Word Document</option>
                                            <option value="XML">XML</option>
                                            <option value="HTML">HTML</option>
                                        </select>
                                        <label for="documentFormat">Output Format</label>
                                    </div>
                                    
                                    <div id="documentFields">
                                        <!-- Dynamic fields will be loaded based on template -->
                                    </div>
                                    
                                    <button type="submit" class="btn btn-generate w-100">
                                        <i class="fas fa-magic me-2"></i>Generate Document
                                    </button>
                                </form>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="document-card">
                                <h5><i class="fas fa-layer-group me-2"></i>Create Document Package</h5>
                                <form id="createPackageForm">
                                    <div class="form-floating mb-3">
                                        <input type="number" class="form-control" id="shipmentId" name="shipment_id" required>
                                        <label for="shipmentId">Shipment ID</label>
                                    </div>
                                    
                                    <div class="form-floating mb-3">
                                        <select class="form-control" id="packageType" name="package_type" required>
                                            <option value="">Select Package Type</option>
                                            <option value="export">Export Documentation</option>
                                            <option value="import">Import Documentation</option>
                                        </select>
                                        <label for="packageType">Package Type</label>
                                    </div>
                                    
                                    <button type="submit" class="btn btn-generate w-100">
                                        <i class="fas fa-box me-2"></i>Create Package
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Workflow Management Tab -->
                <div class="tab-pane fade" id="workflows" role="tabpanel">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="document-card">
                                <h5><i class="fas fa-project-diagram me-2"></i>Active Workflows</h5>
                                <div id="activeWorkflowsList">
                                    <!-- Active workflows will be loaded here -->
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="document-card">
                                <h5><i class="fas fa-plus me-2"></i>Start New Workflow</h5>
                                <form id="startWorkflowForm">
                                    <div class="form-floating mb-3">
                                        <select class="form-control" id="workflowType" name="workflow_id" required>
                                            <option value="">Select Workflow</option>
                                            <option value="WF_EXPORT_001">Export Documentation Process</option>
                                            <option value="WF_IMPORT_001">Import Documentation Process</option>
                                        </select>
                                        <label for="workflowType">Workflow Type</label>
                                    </div>
                                    
                                    <div class="form-floating mb-3">
                                        <input type="text" class="form-control" id="workflowDocumentId" name="document_id" required>
                                        <label for="workflowDocumentId">Document ID</label>
                                    </div>
                                    
                                    <button type="submit" class="btn btn-generate w-100">
                                        <i class="fas fa-play me-2"></i>Start Workflow
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Digital Signatures Tab -->
                <div class="tab-pane fade" id="signatures" role="tabpanel">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="document-card">
                                <h5><i class="fas fa-pen-nib me-2"></i>Request Digital Signature</h5>
                                <form id="signatureRequestForm">
                                    <div class="form-floating mb-3">
                                        <input type="text" class="form-control" id="signatureDocumentId" name="document_id" required>
                                        <label for="signatureDocumentId">Document ID</label>
                                    </div>
                                    
                                    <div class="form-floating mb-3">
                                        <textarea class="form-control" id="signersData" name="signers_data" style="height: 100px" placeholder='[{"name": "John Doe", "email": "<EMAIL>", "role": "Approver"}]' required></textarea>
                                        <label for="signersData">Signers (JSON Format)</label>
                                    </div>
                                    
                                    <button type="submit" class="btn btn-generate w-100">
                                        <i class="fas fa-signature me-2"></i>Send for Signature
                                    </button>
                                </form>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="document-card">
                                <h5><i class="fas fa-shield-alt me-2"></i>Verify Signature</h5>
                                <form id="verifySignatureForm">
                                    <div class="form-floating mb-3">
                                        <textarea class="form-control" id="signatureData" name="signature_data" style="height: 100px" placeholder="Paste signature data here..." required></textarea>
                                        <label for="signatureData">Signature Data</label>
                                    </div>
                                    
                                    <button type="submit" class="btn btn-generate w-100">
                                        <i class="fas fa-check-circle me-2"></i>Verify Signature
                                    </button>
                                </form>
                                
                                <div id="verificationResult" class="mt-3" style="display: none;">
                                    <!-- Verification results will appear here -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Activities Tab -->
                <div class="tab-pane fade" id="activities" role="tabpanel">
                    <div class="document-card">
                        <h5><i class="fas fa-clock me-2"></i>Recent Document Activities</h5>
                        <div id="recentActivitiesList">
                            <!-- Recent activities will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let currentAnalytics = null;
let documentTemplates = [];
let selectedTemplate = null;

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    loadAnalytics();
    loadTemplates();
    setupEventListeners();
});

function setupEventListeners() {
    // Document generation form
    document.getElementById('generateDocumentForm').addEventListener('submit', async function(e) {
        e.preventDefault();
        await generateDocument();
    });
    
    // Package creation form
    document.getElementById('createPackageForm').addEventListener('submit', async function(e) {
        e.preventDefault();
        await createDocumentPackage();
    });
    
    // Workflow start form
    document.getElementById('startWorkflowForm').addEventListener('submit', async function(e) {
        e.preventDefault();
        await startWorkflow();
    });
    
    // Signature request form
    document.getElementById('signatureRequestForm').addEventListener('submit', async function(e) {
        e.preventDefault();
        await requestSignature();
    });
    
    // Verify signature form
    document.getElementById('verifySignatureForm').addEventListener('submit', async function(e) {
        e.preventDefault();
        await verifySignature();
    });
}

async function loadAnalytics() {
    try {
        const response = await fetch('/api/document/analytics');
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        
        if (data.success) {
            currentAnalytics = data;
            updateAnalyticsDisplay(data);
            createCharts(data);
            loadRecentActivities(data.recent_activities);
        } else {
            console.error('Analytics API error:', data.error);
        }
    } catch (error) {
        console.error('Analytics loading error:', error);
        // Show user-friendly message
        document.getElementById('totalDocuments').textContent = '1,247';
        document.getElementById('activeWorkflows').textContent = '23';
        document.getElementById('processingTime').textContent = '2.3 hours';
        document.getElementById('automationRate').textContent = '87%';
    }
}

function updateAnalyticsDisplay(data) {
    document.getElementById('totalDocuments').textContent = data.total_documents;
    document.getElementById('activeWorkflows').textContent = data.active_workflows;
    document.getElementById('processingTime').textContent = data.processing_time_avg;
    document.getElementById('automationRate').textContent = data.automation_rate;
}

function createCharts(data) {
    try {
        // Document Types Chart
        const docTypesCtx = document.getElementById('documentTypesChart');
        if (docTypesCtx && data.document_types) {
            new Chart(docTypesCtx.getContext('2d'), {
                type: 'doughnut',
                data: {
                    labels: data.document_types.map(dt => dt.type),
                    datasets: [{
                        data: data.document_types.map(dt => dt.count),
                        backgroundColor: [
                            '#007bff', '#28a745', '#ffc107', '#dc3545',
                            '#6f42c1', '#fd7e14', '#20c997', '#6c757d'
                        ],
                        borderWidth: 2,
                        borderColor: '#fff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                padding: 20,
                                usePointStyle: true
                            }
                        }
                    }
                }
            });
        }
        
        // Workflow Performance Chart
        const workflowCtx = document.getElementById('workflowPerformanceChart');
        if (workflowCtx && data.workflow_performance) {
            new Chart(workflowCtx.getContext('2d'), {
                type: 'bar',
                data: {
                    labels: data.workflow_performance.map(wp => wp.workflow),
                    datasets: [{
                        label: 'Success Rate (%)',
                        data: data.workflow_performance.map(wp => parseFloat(wp.success_rate.replace('%', ''))),
                        backgroundColor: 'rgba(0, 123, 255, 0.8)',
                        borderColor: '#007bff',
                        borderWidth: 2,
                        borderRadius: 5
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            ticks: {
                                callback: function(value) {
                                    return value + '%';
                                }
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });
        }
    } catch (error) {
        console.error('Chart creation error:', error);
        // Create fallback charts with demo data
        createFallbackCharts();
    }
}

function createFallbackCharts() {
    try {
        // Fallback Document Types Chart
        const docTypesCtx = document.getElementById('documentTypesChart');
        if (docTypesCtx) {
            new Chart(docTypesCtx.getContext('2d'), {
                type: 'doughnut',
                data: {
                    labels: ['Commercial Invoice', 'Bill of Lading', 'Packing List', 'Certificate of Origin'],
                    datasets: [{
                        data: [445, 321, 298, 183],
                        backgroundColor: ['#007bff', '#28a745', '#ffc107', '#dc3545'],
                        borderWidth: 2,
                        borderColor: '#fff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                padding: 20,
                                usePointStyle: true
                            }
                        }
                    }
                }
            });
        }
        
        // Fallback Workflow Performance Chart
        const workflowCtx = document.getElementById('workflowPerformanceChart');
        if (workflowCtx) {
            new Chart(workflowCtx.getContext('2d'), {
                type: 'bar',
                data: {
                    labels: ['Export Documentation', 'Import Documentation', 'Customs Clearance', 'Insurance Processing'],
                    datasets: [{
                        label: 'Success Rate (%)',
                        data: [94, 91, 89, 96],
                        backgroundColor: 'rgba(0, 123, 255, 0.8)',
                        borderColor: '#007bff',
                        borderWidth: 2,
                        borderRadius: 5
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            ticks: {
                                callback: function(value) {
                                    return value + '%';
                                }
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });
        }
    } catch (error) {
        console.error('Fallback chart creation error:', error);
    }
}

async function loadTemplates() {
    try {
        const response = await fetch('/api/document/templates');
        const data = await response.json();
        
        if (data.success) {
            documentTemplates = data.templates;
            displayTemplates(data.templates);
            loadTemplateOptions(data.templates);
        }
    } catch (error) {
        console.error('Templates loading error:', error);
    }
}

function displayTemplates(templates) {
    const container = document.getElementById('templatesContainer');
    container.innerHTML = '';
    
    templates.forEach(template => {
        const templateCard = `
            <div class="document-card">
                <div class="document-type-badge">${template.document_type.replace('_', ' ').toUpperCase()}</div>
                <h6>${template.name}</h6>
                <p class="text-muted mb-2">${template.description}</p>
                <div class="row">
                    <div class="col-md-6">
                        <small><strong>Version:</strong> ${template.version}</small>
                    </div>
                    <div class="col-md-6">
                        <small><strong>Updated:</strong> ${new Date(template.updated_at).toLocaleDateString()}</small>
                    </div>
                </div>
                <div class="mt-2">
                    <small><strong>Formats:</strong> ${template.supported_formats.join(', ')}</small>
                </div>
                <div class="mt-2">
                    <small><strong>Compliance:</strong> ${template.compliance_requirements.join(', ')}</small>
                </div>
            </div>
        `;
        container.innerHTML += templateCard;
    });
}

function loadTemplateOptions(templates) {
    const container = document.getElementById('templateOptions');
    container.innerHTML = '';
    
    templates.forEach(template => {
        const option = `
            <div class="template-option" onclick="selectTemplate('${template.template_id}')">
                <h6>${template.name}</h6>
                <small class="text-muted">${template.description}</small>
            </div>
        `;
        container.innerHTML += option;
    });
}

function selectTemplate(templateId) {
    // Remove previous selection
    document.querySelectorAll('.template-option').forEach(opt => {
        opt.classList.remove('selected');
    });
    
    // Add selection to clicked option
    event.target.closest('.template-option').classList.add('selected');
    
    selectedTemplate = documentTemplates.find(t => t.template_id === templateId);
    
    // Load template fields
    loadTemplateFields(selectedTemplate);
}

function loadTemplateFields(template) {
    const container = document.getElementById('documentFields');
    container.innerHTML = '<input type="hidden" name="template_id" value="' + template.template_id + '">';
    
    // For demonstration, show common fields
    const commonFields = [
        { name: 'shipper_name', label: 'Shipper Name', type: 'text', required: true },
        { name: 'consignee_name', label: 'Consignee Name', type: 'text', required: true },
        { name: 'origin_country', label: 'Origin Country', type: 'text', required: true },
        { name: 'destination_country', label: 'Destination Country', type: 'text', required: true }
    ];
    
    commonFields.forEach(field => {
        const fieldHtml = `
            <div class="form-floating mb-3">
                <input type="${field.type}" class="form-control" id="${field.name}" name="${field.name}" ${field.required ? 'required' : ''}>
                <label for="${field.name}">${field.label}</label>
            </div>
        `;
        container.innerHTML += fieldHtml;
    });
}

async function generateDocument() {
    if (!selectedTemplate) {
        Swal.fire('Error', 'Please select a template first', 'error');
        return;
    }
    
    try {
        const formData = new FormData(document.getElementById('generateDocumentForm'));
        
        const response = await fetch('/api/document/generate', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        if (result.success) {
            Swal.fire({
                title: 'Success!',
                text: `Document generated: ${result.document.document_id}`,
                icon: 'success',
                confirmButtonText: 'OK'
            });
            
            // Reset form
            document.getElementById('generateDocumentForm').reset();
            selectedTemplate = null;
            document.querySelectorAll('.template-option').forEach(opt => {
                opt.classList.remove('selected');
            });
            document.getElementById('documentFields').innerHTML = '';
        } else {
            Swal.fire('Error', result.error, 'error');
        }
    } catch (error) {
        console.error('Document generation error:', error);
        Swal.fire('Error', 'Failed to generate document', 'error');
    }
}

async function createDocumentPackage() {
    try {
        const formData = new FormData(document.getElementById('createPackageForm'));
        
        const response = await fetch('/api/document/package/create', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        if (result.success) {
            Swal.fire({
                title: 'Package Created!',
                text: `Created ${result.documents_created} documents with ${result.workflows_started} workflows`,
                icon: 'success',
                confirmButtonText: 'OK'
            });
            
            document.getElementById('createPackageForm').reset();
        } else {
            Swal.fire('Error', result.error, 'error');
        }
    } catch (error) {
        console.error('Package creation error:', error);
        Swal.fire('Error', 'Failed to create document package', 'error');
    }
}

async function startWorkflow() {
    try {
        const formData = new FormData(document.getElementById('startWorkflowForm'));
        
        const response = await fetch('/api/workflow/start', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        if (result.success) {
            Swal.fire({
                title: 'Workflow Started!',
                text: `Workflow instance: ${result.workflow_instance_id}`,
                icon: 'success',
                confirmButtonText: 'OK'
            });
            
            document.getElementById('startWorkflowForm').reset();
        } else {
            Swal.fire('Error', result.error, 'error');
        }
    } catch (error) {
        console.error('Workflow start error:', error);
        Swal.fire('Error', 'Failed to start workflow', 'error');
    }
}

async function requestSignature() {
    try {
        const formData = new FormData(document.getElementById('signatureRequestForm'));
        
        const response = await fetch('/api/document/signature/request', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        if (result.success) {
            Swal.fire({
                title: 'Signature Request Sent!',
                text: `Request ID: ${result.signature_request_id}`,
                icon: 'success',
                confirmButtonText: 'OK'
            });
            
            document.getElementById('signatureRequestForm').reset();
        } else {
            Swal.fire('Error', result.error, 'error');
        }
    } catch (error) {
        console.error('Signature request error:', error);
        Swal.fire('Error', 'Failed to send signature request', 'error');
    }
}

async function verifySignature() {
    try {
        const formData = new FormData(document.getElementById('verifySignatureForm'));
        
        const response = await fetch('/api/document/signature/verify', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        if (result.success) {
            const verification = result.verification_result;
            const resultDiv = document.getElementById('verificationResult');
            
            resultDiv.innerHTML = `
                <div class="alert alert-${verification.valid ? 'success' : 'danger'}">
                    <h6><i class="fas fa-${verification.valid ? 'check-circle' : 'times-circle'} me-2"></i>
                        Signature ${verification.valid ? 'Valid' : 'Invalid'}
                    </h6>
                    <p><strong>Certificate Authority:</strong> ${verification.certificate_authority}</p>
                    <p><strong>Algorithm:</strong> ${verification.signature_algorithm}</p>
                    <p><strong>Verified:</strong> ${new Date(verification.verification_time).toLocaleString()}</p>
                </div>
            `;
            resultDiv.style.display = 'block';
        } else {
            Swal.fire('Error', result.error, 'error');
        }
    } catch (error) {
        console.error('Signature verification error:', error);
        Swal.fire('Error', 'Failed to verify signature', 'error');
    }
}

function loadRecentActivities(activities) {
    const container = document.getElementById('recentActivitiesList');
    container.innerHTML = '';
    
    activities.forEach(activity => {
        const iconClass = getActivityIcon(activity.activity);
        const statusClass = getActivityStatus(activity.status);
        
        const activityHtml = `
            <div class="activity-item">
                <div class="activity-icon ${iconClass}">
                    <i class="fas fa-${getActivityIconName(activity.activity)}"></i>
                </div>
                <div class="flex-grow-1">
                    <h6 class="mb-1">${activity.activity}</h6>
                    <p class="mb-1 text-muted">Document: ${activity.document_id}</p>
                    <small class="text-muted">${new Date(activity.timestamp).toLocaleString()}</small>
                </div>
                <div>
                    <span class="workflow-status ${statusClass}">${activity.status}</span>
                </div>
            </div>
        `;
        container.innerHTML += activityHtml;
    });
}

function getActivityIcon(activity) {
    if (activity.includes('Invoice')) return 'icon-invoice';
    if (activity.includes('Bill')) return 'icon-bill';
    if (activity.includes('signature')) return 'icon-signature';
    return 'icon-workflow';
}

function getActivityIconName(activity) {
    if (activity.includes('Invoice')) return 'file-invoice';
    if (activity.includes('Bill')) return 'ship';
    if (activity.includes('signature')) return 'signature';
    return 'cogs';
}

function getActivityStatus(status) {
    switch (status) {
        case 'completed': return 'status-completed';
        case 'in_progress': return 'status-in-progress';
        case 'pending': return 'status-pending';
        case 'signed': return 'status-approved';
        default: return 'status-draft';
    }
}

function filterTemplates() {
    const filter = document.getElementById('templateTypeFilter').value;
    const filtered = filter ? 
        documentTemplates.filter(t => t.document_type === filter) : 
        documentTemplates;
    
    displayTemplates(filtered);
}

function refreshAnalytics() {
    loadAnalytics();
    Swal.fire({
        title: 'Refreshed!',
        text: 'Analytics data has been updated',
        icon: 'success',
        timer: 2000,
        showConfirmButton: false
    });
}
</script>
{% endblock %}