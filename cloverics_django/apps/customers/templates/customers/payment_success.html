{% extends "base.html" %}

{% block content %}
<div class="page-container">
    <div class="success-container">
        <div class="success-card">
            <div class="success-icon">
                <i class="fas fa-check-circle"></i>
            </div>
            
            <h1>Payment Successful!</h1>
            <p class="success-message">{{ message }}</p>
            
            <div class="payment-details">
                <div class="detail-item">
                    <span class="label">Payment ID:</span>
                    <span class="value">{{ payment_id }}</span>
                </div>
                <div class="detail-item">
                    <span class="label">Transaction Date:</span>
                    <span class="value">{{ transaction_date }}</span>
                </div>
                <div class="detail-item">
                    <span class="label">Status:</span>
                    <span class="value status-success">Completed</span>
                </div>
            </div>
            
            <div class="next-steps">
                <h5>What happens next?</h5>
                <ul>
                    <li>You will receive a confirmation email shortly</li>
                    <li>Your shipment will be processed within 24 hours</li>
                    <li>You can track your shipment using the tracking number</li>
                    <li>Updates will be sent to your email address</li>
                </ul>
            </div>
            
            <div class="action-buttons">
                <a href="/customer/manage-shipments" class="btn btn-primary">
                    <i class="fas fa-boxes"></i> <span class="btn-text">View My Shipments</span>
                </a>
                <a href="/customer/track-shipment" class="btn btn-outline-primary">
                    <i class="fas fa-route"></i> <span class="btn-text">Track Shipment</span>
                </a>
                <a href="/dashboard" class="btn btn-outline-secondary">
                    <i class="fas fa-home"></i> <span class="btn-text">Back to Dashboard</span>
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Responsive behavior for button text
document.addEventListener('DOMContentLoaded', function() {
    function handleResize() {
        const isMobile = window.innerWidth <= 576;
        const btnTexts = document.querySelectorAll('.btn-text');
        
        btnTexts.forEach(text => {
            text.style.display = isMobile ? 'none' : 'inline';
        });
    }
    
    // Initial call
    handleResize();
    
    // Listen for window resize
    window.addEventListener('resize', handleResize);
});
</script>
{% endblock %}

{% block extra_css %}
<style>
.success-container {
    min-height: 80vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem 0;
}

.success-card {
    background: white;
    border-radius: 15px;
    padding: 3rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    text-align: center;
    max-width: 600px;
    width: 100%;
}

.success-icon {
    font-size: 4rem;
    color: #28a745;
    margin-bottom: 1.5rem;
}

.success-card h1 {
    color: #2c3e50;
    font-weight: 700;
    margin-bottom: 1rem;
}

.success-message {
    font-size: 1.1rem;
    color: #666;
    margin-bottom: 2rem;
    line-height: 1.6;
}

.payment-details {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    text-align: left;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
}

.detail-item:last-child {
    margin-bottom: 0;
}

.detail-item .label {
    color: #666;
    font-weight: 500;
}

.detail-item .value {
    font-weight: 600;
    color: #2c3e50;
}

.status-success {
    color: #28a745 !important;
}

.next-steps {
    text-align: left;
    margin-bottom: 2rem;
}

.next-steps h5 {
    color: #2c3e50;
    margin-bottom: 1rem;
}

.next-steps ul {
    list-style: none;
    padding: 0;
}

.next-steps li {
    padding: 0.5rem 0;
    padding-left: 1.5rem;
    position: relative;
    color: #666;
}

.next-steps li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: #28a745;
    font-weight: bold;
}

.action-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.action-buttons .btn {
    min-width: 150px;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .success-container {
        padding: 1.5rem 0;
    }
    
    .success-card {
        padding: 2.5rem;
    }
}

@media (max-width: 992px) {
    .success-container {
        padding: 1rem 0;
    }
    
    .success-card {
        padding: 2rem;
        max-width: 500px;
    }
    
    .success-icon {
        font-size: 3.5rem;
    }
    
    .success-card h1 {
        font-size: 1.75rem;
    }
    
    .success-message {
        font-size: 1rem;
    }
}

@media (max-width: 768px) {
    .success-container {
        padding: 0.5rem 0;
    }
    
    .success-card {
        padding: 1.5rem;
        margin: 0 1rem;
    }
    
    .success-icon {
        font-size: 3rem;
    }
    
    .success-card h1 {
        font-size: 1.5rem;
    }
    
    .success-message {
        font-size: 0.95rem;
    }
    
    .payment-details {
        padding: 1rem;
    }
    
    .action-buttons {
        flex-direction: column;
        width: 100%;
    }
    
    .action-buttons .btn {
        width: 100%;
        min-width: auto;
        margin-bottom: 0.5rem;
    }
    
    .btn-text {
        display: none;
    }
}

@media (max-width: 576px) {
    .success-card {
        padding: 1rem;
        margin: 0 0.5rem;
    }
    
    .success-icon {
        font-size: 2.5rem;
    }
    
    .success-card h1 {
        font-size: 1.25rem;
    }
    
    .success-message {
        font-size: 0.9rem;
    }
    
    .payment-details {
        padding: 0.75rem;
    }
    
    .detail-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }
    
    .action-buttons .btn {
        font-size: 0.9rem;
        padding: 0.75rem 1rem;
    }
}

/* Landscape orientation adjustments for mobile */
@media (max-width: 768px) and (orientation: landscape) {
    .action-buttons {
        flex-direction: row;
        flex-wrap: wrap;
    }
    
    .action-buttons .btn {
        width: auto;
        margin-bottom: 0;
        flex: 1;
    }
    
    .btn-text {
        display: inline;
    }
}

@media (max-width: 768px) {
    .success-card {
        padding: 2rem;
        margin: 1rem;
    }
    
    .action-buttons {
        flex-direction: column;
    }
    
    .action-buttons .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }
}
</style>
{% endblock %}