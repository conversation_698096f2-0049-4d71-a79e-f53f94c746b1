{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <h1><i class="fas fa-calculator"></i> Pricing Calculator</h1>
                <p class="text-muted">Calculate shipping costs for your cargo</p>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-calculator"></i> Calculate Shipping Cost</h5>
                </div>
                <div class="card-body">
                    <form method="post" action="/pricing-calculator">
                        <div class="row">
                            <div class="col-lg-3 col-md-6 col-sm-12 mb-3">
                                <div class="form-group">
                                    <label for="origin_country">Origin Country</label>
                                    <select class="form-control" id="origin_country" name="origin_country" required>
                                        <option value="">Select origin country</option>
                                        {% for country in countries %}
                                        <option value="{{ country }}" {% if search_params and search_params.origin_country == country %}selected{% endif %}>{{ country }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-6 col-sm-12 mb-3">
                                <div class="form-group">
                                    <label for="origin_city">Origin City</label>
                                    <input type="text" class="form-control" id="origin_city" name="origin_city" 
                                           placeholder="Enter origin city" 
                                           value="{% if search_params %}{{ search_params.origin_city }}{% endif %}" required>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-6 col-sm-12 mb-3">
                                <div class="form-group">
                                    <label for="destination_country">Destination Country</label>
                                    <select class="form-control" id="destination_country" name="destination_country" required>
                                        <option value="">Select destination country</option>
                                        {% for country in countries %}
                                        <option value="{{ country }}" {% if search_params and search_params.destination_country == country %}selected{% endif %}>{{ country }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-6 col-sm-12 mb-3">
                                <div class="form-group">
                                    <label for="destination_city">Destination City</label>
                                    <input type="text" class="form-control" id="destination_city" name="destination_city" 
                                           placeholder="Enter destination city" 
                                           value="{% if search_params %}{{ search_params.destination_city }}{% endif %}" required>
                                </div>
                            </div>
                        </div>
                        
                        <!-- US States Row (Hidden by default) -->
                        <div class="row" id="us_states_row" style="display: none;">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="origin_state">Origin State</label>
                                    <select class="form-control" id="origin_state" name="origin_state">
                                        <option value="">Select State</option>
                                        {% for state in us_states %}
                                        <option value="{{ state }}">{{ state }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="destination_state">Destination State</label>
                                    <select class="form-control" id="destination_state" name="destination_state">
                                        <option value="">Select State</option>
                                        {% for state in us_states %}
                                        <option value="{{ state }}">{{ state }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-lg-6 col-md-6 col-sm-12 mb-3">
                                <div class="form-group">
                                    <label for="cargo_weight">Cargo Weight (kg)</label>
                                    <input type="number" class="form-control" id="cargo_weight" name="cargo_weight" step="0.1" min="0.1" 
                                           value="{% if search_params %}{{ search_params.cargo_weight }}{% endif %}" required>
                                </div>
                            </div>
                            <div class="col-lg-6 col-md-6 col-sm-12 mb-3">
                                <div class="form-group">
                                    <label for="transport_type">Transport Type</label>
                                    <select class="form-control" id="transport_type" name="transport_type" required>
                                        <option value="">Select transport type</option>
                                        <option value="AIR" {% if search_params and search_params.transport_type == 'AIR' %}selected{% endif %}>Air Freight</option>
                                        <option value="SEA" {% if search_params and search_params.transport_type == 'SEA' %}selected{% endif %}>Sea Freight</option>
                                        <option value="ROAD" {% if search_params and search_params.transport_type == 'ROAD' %}selected{% endif %}>Road Transport</option>
                                        <option value="RAIL" {% if search_params and search_params.transport_type == 'RAIL' %}selected{% endif %}>Rail Transport</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="cargo_type">Cargo Type</label>
                                    <select class="form-control" id="cargo_type" name="cargo_type">
                                        <option value="GENERAL" {% if search_params and search_params.cargo_type == 'GENERAL' %}selected{% endif %}>General Cargo</option>
                                        <option value="FRAGILE" {% if search_params and search_params.cargo_type == 'FRAGILE' %}selected{% endif %}>Fragile Items</option>
                                        <option value="HAZARDOUS" {% if search_params and search_params.cargo_type == 'HAZARDOUS' %}selected{% endif %}>Hazardous Materials</option>
                                        <option value="PERISHABLE" {% if search_params and search_params.cargo_type == 'PERISHABLE' %}selected{% endif %}>Perishable Goods</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="urgency">Urgency Level</label>
                                    <select class="form-control" id="urgency" name="urgency">
                                        <option value="STANDARD" {% if search_params and search_params.urgency == 'STANDARD' %}selected{% endif %}>Standard (7-14 days)</option>
                                        <option value="EXPRESS" {% if search_params and search_params.urgency == 'EXPRESS' %}selected{% endif %}>Express (3-7 days)</option>
                                        <option value="URGENT" {% if search_params and search_params.urgency == 'URGENT' %}selected{% endif %}>Urgent (1-3 days)</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-calculator"></i> Calculate Price
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-info-circle"></i> Pricing Information</h5>
                </div>
                <div class="card-body">
                    <h6>Rate Factors:</h6>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-check text-success"></i> Distance between locations</li>
                        <li><i class="fas fa-check text-success"></i> Weight and dimensions</li>
                        <li><i class="fas fa-check text-success"></i> Transport type selection</li>
                        <li><i class="fas fa-check text-success"></i> Urgency requirements</li>
                        <li><i class="fas fa-check text-success"></i> Cargo type considerations</li>
                    </ul>

                    <h6 class="mt-3">Additional Services:</h6>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-shield-alt text-primary"></i> Insurance coverage</li>
                        <li><i class="fas fa-map-marked-alt text-primary"></i> Real-time tracking</li>
                        <li><i class="fas fa-file-alt text-primary"></i> Customs handling</li>
                        <li><i class="fas fa-warehouse text-primary"></i> Warehousing options</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    {% if calculation_result %}
    <!-- ChatGPT Smart Calculation Results -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="alert alert-success">
                <div class="row">
                    <div class="col-md-8">
                        <h6><i class="fas fa-calculator"></i> Smart Price Calculation Complete</h6>
                        <p class="mb-1"><strong>Calculated using professional logistics formula with real-time market factors.</strong></p>
                        <p class="mb-0">Includes transport mode rates, distance calculations, customs fees, insurance, and urgency adjustments.</p>
                    </div>
                    <div class="col-md-4">
                        <div class="pricing-summary">
                            <h4 class="text-success mb-0">${{ calculation_result.final_price }}</h4>
                            <p class="mb-0">Estimated Cost • {{ market_stats.estimated_days }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-receipt"></i> Detailed Cost Breakdown</h5>
                    <p class="text-muted mb-0">Route: {{ search_params.origin_city }}, {{ search_params.origin_country }} → {{ search_params.destination_city }}, {{ search_params.destination_country }} ({{ market_stats.distance_km }}km)</p>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-sm">
                                <tbody>
                                    <tr>
                                        <td><i class="fas fa-truck text-primary"></i> Base Fee ({{ market_stats.transport_mode }})</td>
                                        <td class="text-right">${{ calculation_result.base_fee }}</td>
                                    </tr>
                                    <tr>
                                        <td><i class="fas fa-route text-info"></i> Distance Cost ({{ market_stats.distance_km }}km)</td>
                                        <td class="text-right">${{ calculation_result.distance_cost }}</td>
                                    </tr>
                                    <tr>
                                        <td><i class="fas fa-weight text-warning"></i> Weight Cost ({{ search_params.cargo_weight }}kg)</td>
                                        <td class="text-right">${{ calculation_result.weight_cost }}</td>
                                    </tr>
                                    {% if calculation_result.customs_fee > 0 %}
                                    <tr>
                                        <td><i class="fas fa-passport text-danger"></i> Customs Fee</td>
                                        <td class="text-right">${{ calculation_result.customs_fee }}</td>
                                    </tr>
                                    {% endif %}
                                    <tr>
                                        <td><i class="fas fa-shield-alt text-success"></i> Insurance</td>
                                        <td class="text-right">${{ calculation_result.insurance_fee }}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-sm">
                                <tbody>
                                    <tr>
                                        <td><i class="fas fa-plus text-secondary"></i> Subtotal</td>
                                        <td class="text-right">${{ calculation_result.subtotal }}</td>
                                    </tr>
                                    <tr>
                                        <td><i class="fas fa-clock text-warning"></i> Urgency ({{ calculation_result.urgency_multiplier }}x)</td>
                                        <td class="text-right">${{ calculation_result.total_price }}</td>
                                    </tr>
                                    {% if calculation_result.cargo_adjustment > 0 %}
                                    <tr>
                                        <td><i class="fas fa-box text-info"></i> Cargo Risk ({{ market_stats.cargo_multiplier }}x)</td>
                                        <td class="text-right">+${{ calculation_result.cargo_adjustment }}</td>
                                    </tr>
                                    {% endif %}
                                    <tr>
                                        <td><i class="fas fa-percentage text-primary"></i> Platform Fee (0.1%)</td>
                                        <td class="text-right">${{ calculation_result.platform_fee }}</td>
                                    </tr>
                                    <tr class="table-success font-weight-bold">
                                        <td><i class="fas fa-dollar-sign"></i> <strong>Total Price</strong></td>
                                        <td class="text-right"><strong>${{ calculation_result.final_price }}</strong></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-line"></i> Market Analysis</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <p><strong>Price per kg:</strong> <span class="badge badge-info">${{ market_stats.avg_price_per_kg }}</span></p>
                        <p><strong>Distance:</strong> {{ market_stats.distance_km }}km</p>
                        <p><strong>Transport Mode:</strong> {{ market_stats.transport_mode|title }}</p>
                        <p><strong>International:</strong> {% if market_stats.customs_required %}Yes{% else %}No{% endif %}</p>
                        <p><strong>Estimated Transit:</strong> {{ market_stats.estimated_days }}</p>
                    </div>
                    
                    <div class="price-range">
                        <h6>Market Price Range:</h6>
                        <div class="d-flex justify-content-between">
                            <span class="badge badge-success">${{ market_stats.price_range_low }}</span>
                            <span class="text-muted">to</span>
                            <span class="badge badge-warning">${{ market_stats.price_range_high }}</span>
                        </div>
                        <p class="text-muted mt-2 small">±15% variance based on provider, season, and additional services</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Next Steps -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-arrow-right"></i> Next Steps</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="text-center">
                                <i class="fas fa-search fa-2x text-primary mb-2"></i>
                                <h6>Search Providers</h6>
                                <p class="text-muted small">Find specific logistics providers for this route in our Search Shipping section</p>
                                <a href="/customer/search-shipping" class="btn btn-primary btn-sm">
                                    <i class="fas fa-search"></i> Search Routes
                                </a>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <i class="fas fa-quote-right fa-2x text-success mb-2"></i>
                                <h6>Request Quote</h6>
                                <p class="text-muted small">Get official quotes from providers for this specific shipment</p>
                                <button class="btn btn-success btn-sm" onclick="requestQuote()">
                                    <i class="fas fa-envelope"></i> Request Quote
                                </button>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <i class="fas fa-calculator fa-2x text-info mb-2"></i>
                                <h6>Try Different Options</h6>
                                <p class="text-muted small">Calculate prices for different transport modes or urgency levels</p>
                                <button class="btn btn-info btn-sm" onclick="scrollToForm()">
                                    <i class="fas fa-redo"></i> Recalculate
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <hr class="my-4">
                    
                    <div class="alert alert-light">
                        <h6><i class="fas fa-lightbulb text-warning"></i> Tips to Save Money:</h6>
                        <ul class="mb-0">
                            <li><strong>Standard vs Express:</strong> Standard delivery can save 20-50% compared to express</li>
                            <li><strong>Sea vs Air:</strong> Sea freight is often 70% cheaper for non-urgent shipments</li>
                            <li><strong>Consolidation:</strong> Combine multiple shipments to reduce per-kg costs</li>
                            <li><strong>Flexible Dates:</strong> Off-peak periods may offer better rates</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    {% if error_message %}
    <div class="row mt-4">
        <div class="col-12">
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i> {{ error_message }}
            </div>
        </div>
    </div>
    {% endif %}
</div>

<script>
function requestQuote() {
    {% if calculation_result %}
    const routeData = {
        origin: '{{ search_params.origin_city }}, {{ search_params.origin_country }}',
        destination: '{{ search_params.destination_city }}, {{ search_params.destination_country }}',
        weight: '{{ search_params.cargo_weight }}',
        transport: '{{ search_params.transport_type }}',
        urgency: '{{ search_params.urgency }}',
        price: '{{ calculation_result.final_price }}'
    };
    
    alert(`Quote Request Details:\n\nRoute: ${routeData.origin} → ${routeData.destination}\nWeight: ${routeData.weight}kg\nTransport: ${routeData.transport}\nUrgency: ${routeData.urgency}\nEstimated Price: $${routeData.price}\n\nRedirecting to Search Shipping to find providers...`);
    
    const params = new URLSearchParams({
        'origin_country': '{{ search_params.origin_country }}',
        'origin_city': '{{ search_params.origin_city }}',
        'destination_country': '{{ search_params.destination_country }}',
        'destination_city': '{{ search_params.destination_city }}',
        'transport_type': '{{ search_params.transport_type }}',
        'cargo_weight': '{{ search_params.cargo_weight }}',
        'cargo_type': '{{ search_params.cargo_type }}',
        'urgency': '{{ search_params.urgency }}'
    });
    
    window.location.href = '/customer/search-shipping?' + params.toString();
    {% else %}
    alert('Please calculate pricing first before requesting quotes.');
    {% endif %}
}

function scrollToForm() {
    document.querySelector('form').scrollIntoView({ behavior: 'smooth' });
}

// US States visibility logic
function checkUSSelection() {
    const originCountry = document.getElementById('origin_country');
    const destinationCountry = document.getElementById('destination_country');
    const statesRow = document.getElementById('us_states_row');
    
    if (!originCountry || !destinationCountry || !statesRow) {
        console.log('Missing elements:', {originCountry, destinationCountry, statesRow});
        return;
    }
    
    const originValue = originCountry.value;
    const destinationValue = destinationCountry.value;
    
    // Show states if either origin OR destination is United States
    if (originValue === 'United States' || destinationValue === 'United States') {
        statesRow.style.display = 'block';
        
        // Hide origin state dropdown if origin is not US
        const originStateDiv = document.querySelector('#us_states_row .col-md-3:first-child');
        if (originValue !== 'United States' && originStateDiv) {
            originStateDiv.style.display = 'none';
            const originStateSelect = document.getElementById('origin_state');
            if (originStateSelect) originStateSelect.value = '';
        } else if (originStateDiv) {
            originStateDiv.style.display = 'block';
        }
        
        // Hide destination state dropdown if destination is not US
        const destinationStateDiv = document.querySelector('#us_states_row .col-md-3:last-child');
        if (destinationValue !== 'United States' && destinationStateDiv) {
            destinationStateDiv.style.display = 'none';
            const destinationStateSelect = document.getElementById('destination_state');
            if (destinationStateSelect) destinationStateSelect.value = '';
        } else if (destinationStateDiv) {
            destinationStateDiv.style.display = 'block';
        }
    } else {
        statesRow.style.display = 'none';
        // Clear state selections when hiding
        const originStateSelect = document.getElementById('origin_state');
        const destinationStateSelect = document.getElementById('destination_state');
        if (originStateSelect) originStateSelect.value = '';
        if (destinationStateSelect) destinationStateSelect.value = '';
    }
}

// Add event listeners to country dropdowns
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('origin_country').addEventListener('change', checkUSSelection);
    document.getElementById('destination_country').addEventListener('change', checkUSSelection);
    
    // Check US selection on page load
    checkUSSelection();
});
</script>

{% endblock %}

{% block extra_css %}
<style>
/* Responsive Design */
@media (max-width: 1200px) {
    .container-fluid {
        padding: 1rem;
    }
    
    .card {
        margin-bottom: 1rem;
    }
}

@media (max-width: 992px) {
    .container-fluid {
        padding: 0.75rem;
    }
    
    .page-header h1 {
        font-size: 1.75rem;
    }
    
    .card-body {
        padding: 1.25rem;
    }
}

@media (max-width: 768px) {
    .container-fluid {
        padding: 0.5rem;
    }
    
    .page-header h1 {
        font-size: 1.5rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .form-group {
        margin-bottom: 1rem;
    }
    
    .form-label {
        font-size: 0.9rem;
    }
    
    .form-control,
    .form-select {
        font-size: 0.9rem;
        padding: 0.5rem 0.75rem;
    }
}

@media (max-width: 576px) {
    .container-fluid {
        padding: 0.25rem;
    }
    
    .page-header h1 {
        font-size: 1.25rem;
    }
    
    .card-body {
        padding: 0.75rem;
    }
    
    .form-label {
        font-size: 0.85rem;
    }
    
    .form-control,
    .form-select {
        font-size: 0.85rem;
        padding: 0.5rem;
    }
    
    .btn {
        font-size: 0.9rem;
        padding: 0.75rem 1rem;
    }
}
</style>
{% endblock %}