{% extends "base.html" %}

{% block content %}
<div class="page-container">
    <!-- Success Message -->
    {% if request.query_params.get('success') == 'quote_submitted' %}
    <div class="alert alert-success alert-dismissible fade show mb-4" role="alert">
        <i class="fas fa-check-circle"></i>
        <strong>Quote Request Submitted Successfully!</strong>
        Your quote request has been sent to the logistics provider. They will respond within 24 hours.
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    {% endif %}

    <div class="page-header">
        <h1>Request Quote</h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="/customer/search-shipping">Search Shipping</a></li>
                <li class="breadcrumb-item"><a href="/customer/route-details/{{ route.id }}">Route Details</a></li>
                <li class="breadcrumb-item active">Request Quote</li>
            </ol>
        </nav>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="quote-form-card">
                <div class="provider-summary">
                    <h4>{{ route.provider_name }}</h4>
                    <div class="route-summary">
                        <span class="route-path">
                            <i class="fas fa-map-marker-alt text-success"></i>
                            {{ route.origin_country }}
                            <i class="fas fa-arrow-right mx-2"></i>
                            <i class="fas fa-map-marker-alt text-danger"></i>
                            {{ route.destination_country }}
                        </span>
                        <span class="transport-type">
                            <i class="fas fa-truck"></i>
                            {{ route.transport_type }}
                        </span>
                    </div>
                </div>

                <form method="POST" action="/customer/request-quote/{{ route.id }}" class="quote-form">
                    <div class="form-section">
                        <h5>Shipment Details</h5>
                        <div class="row">
                            <div class="col-lg-6 col-md-6 col-sm-12 mb-3">
                                <div class="form-group">
                                    <label for="cargo_weight">Cargo Weight (kg) *</label>
                                    <input type="number" class="form-control" name="cargo_weight" id="cargo_weight" 
                                           min="0.1" step="0.1" required placeholder="Enter weight in kg">
                                </div>
                            </div>
                            <div class="col-lg-6 col-md-6 col-sm-12 mb-3">
                                <div class="form-group">
                                    <label for="cargo_dimensions">Dimensions (L×W×H cm) *</label>
                                    <input type="text" class="form-control" name="cargo_dimensions" id="cargo_dimensions" 
                                           required placeholder="e.g., 100×50×30">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-section">
                        <h5>Shipping Schedule</h5>
                        <div class="row">
                            <div class="col-lg-6 col-md-6 col-sm-12 mb-3">
                                <div class="form-group">
                                    <label for="pickup_date">Preferred Pickup Date *</label>
                                    <input type="date" class="form-control" name="pickup_date" id="pickup_date" required>
                                </div>
                            </div>
                            <div class="col-lg-6 col-md-6 col-sm-12 mb-3">
                                <div class="form-group">
                                    <label for="delivery_date">Required Delivery Date *</label>
                                    <input type="date" class="form-control" name="delivery_date" id="delivery_date" required>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-section">
                        <h5>Special Requirements</h5>
                        <div class="form-group">
                            <label for="special_requirements">Additional Information</label>
                            <textarea class="form-control" name="special_requirements" id="special_requirements" 
                                      rows="4" placeholder="Enter any special handling requirements, fragile items, customs information, or other notes..."></textarea>
                        </div>
                    </div>

                    <div class="form-section">
                        <h5>Quote Options</h5>
                        <div class="quote-options">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="insurance_option" name="insurance_option">
                                <label class="form-check-label" for="insurance_option">
                                    Include insurance coverage
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="priority_handling" name="priority_handling">
                                <label class="form-check-label" for="priority_handling">
                                    Priority handling
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="door_to_door" name="door_to_door">
                                <label class="form-check-label" for="door_to_door">
                                    Door-to-door delivery
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-paper-plane"></i> Submit Quote Request
                        </button>
                        <a href="/customer/route-details/{{ route.id }}" class="btn btn-outline-secondary btn-lg">
                            <i class="fas fa-arrow-left"></i> Back to Details
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="quote-summary-card">
                <h5>Quote Summary</h5>
                <div class="summary-item">
                    <label>Base Rate:</label>
                    <span class="rate">${{ route.price_per_kg }}/kg</span>
                </div>
                <div class="summary-item">
                    <label>Estimated Transit:</label>
                    <span>{{ route.transit_time }} days</span>
                </div>
                
                <hr>
                
                <div class="estimated-cost">
                    <div class="cost-calculator" id="costCalculator">
                        <div class="calculator-header">
                            <h6><i class="fas fa-calculator"></i> Price Calculator</h6>
                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="calculatePrice()">
                                <i class="fas fa-sync"></i> Calculate
                            </button>
                        </div>
                        
                        <div class="cost-breakdown">
                            <div class="cost-line">
                                <span>Base rate ({{ route.price_per_kg }}/kg):</span>
                                <span id="baseCost">-</span>
                            </div>
                            <div class="cost-line">
                                <span>Distance cost:</span>
                                <span id="distanceCost">-</span>
                            </div>
                            <div class="cost-line">
                                <span>Volume surcharge:</span>
                                <span id="volumeCost">$0.00</span>
                            </div>
                            <div class="cost-line">
                                <span>Fuel surcharge (8%):</span>
                                <span id="fuelSurcharge">$0.00</span>
                            </div>
                            <div class="cost-line">
                                <span>Additional services:</span>
                                <span id="additionalCost">$0.00</span>
                            </div>
                            <hr>
                            <div class="cost-line total">
                                <strong>Estimated Total:</strong>
                                <strong id="totalCost">-</strong>
                            </div>
                        </div>
                        
                        <div class="calculator-info">
                            <small class="text-muted">
                                <i class="fas fa-info-circle"></i>
                                Calculation includes market-average rates for distance, fuel, and volume factors.
                            </small>
                        </div>
                    </div>
                </div>

                <div class="info-note">
                    <i class="fas fa-info-circle"></i>
                    <p>This is an estimated cost. Final pricing will be provided in the official quote response.</p>
                </div>
            </div>

            <div class="provider-contact-card">
                <h6>Provider Contact</h6>
                <p>Need to discuss your requirements?</p>
                <div class="contact-actions">
                    <button class="btn btn-outline-primary btn-sm w-100 mb-2" onclick="callProvider()">
                        <i class="fas fa-phone"></i> Call Provider
                    </button>
                    <button class="btn btn-outline-primary btn-sm w-100" onclick="sendMessage()">
                        <i class="fas fa-envelope"></i> Send Message
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Quote Requests Section -->
    {% if recent_quotes %}
    <div class="row mt-4">
        <div class="col-12">
            <div class="recent-quotes-card">
                <h5><i class="fas fa-history"></i> Recent Quote Requests</h5>
                <div class="quotes-list">
                    {% for quote in recent_quotes %}
                    <div class="quote-item" data-quote-id="{{ quote.id }}">
                        <div class="quote-header">
                            <div class="quote-info">
                                <span class="quote-ref">{{ quote.quote_reference }}</span>
                                <span class="quote-provider">{{ quote.provider_name }}</span>
                                <span class="quote-route">
                                    {{ quote.origin_country }} → {{ quote.destination_country }}
                                </span>
                            </div>
                            <div class="quote-status">
                                <span class="status-badge status-{{ quote.status }}">{{ quote.status|title }}</span>
                                <span class="quote-date">{{ quote.created_at }}</span>
                            </div>
                        </div>
                        
                        <div class="quote-details">
                            <div class="detail-item">
                                <strong>Weight:</strong> {{ quote.cargo_weight }}kg
                            </div>
                            <div class="detail-item">
                                <strong>Dimensions:</strong> {{ quote.cargo_dimensions }}
                            </div>
                            <div class="detail-item">
                                <strong>Pickup:</strong> {{ quote.pickup_date }}
                            </div>
                            <div class="detail-item">
                                <strong>Delivery:</strong> {{ quote.delivery_date }}
                            </div>
                            {% if quote.quoted_price %}
                            <div class="detail-item">
                                <strong>Quoted Price:</strong> ${{ quote.quoted_price }}
                            </div>
                            {% endif %}
                            {% if quote.special_requirements %}
                            <div class="detail-item">
                                <strong>Special Requirements:</strong> {{ quote.special_requirements }}
                            </div>
                            {% endif %}
                        </div>
                        
                        <div class="quote-actions">
                            {% if quote.status == 'pending' %}
                            <button class="btn btn-sm btn-outline-primary" onclick="editQuote({{ quote.id }})">
                                <i class="fas fa-edit"></i> Edit
                            </button>
                            {% endif %}
                            <button class="btn btn-sm btn-outline-danger" onclick="deleteQuote({{ quote.id }})">
                                <i class="fas fa-trash"></i> Delete
                            </button>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<!-- Edit Quote Modal -->
<div class="modal fade" id="editQuoteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Quote Request</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="editQuoteForm">
                <div class="modal-body">
                    <input type="hidden" id="editQuoteId">
                    
                    <div class="mb-3">
                        <label for="editCargoWeight" class="form-label">Cargo Weight (kg)</label>
                        <input type="number" class="form-control" id="editCargoWeight" step="0.1" min="0.1" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="editCargoDimensions" class="form-label">Dimensions (L×W×H cm)</label>
                        <input type="text" class="form-control" id="editCargoDimensions" required>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editPickupDate" class="form-label">Pickup Date</label>
                                <input type="date" class="form-control" id="editPickupDate" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editDeliveryDate" class="form-label">Delivery Date</label>
                                <input type="date" class="form-control" id="editDeliveryDate" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="editSpecialRequirements" class="form-label">Special Requirements</label>
                        <textarea class="form-control" id="editSpecialRequirements" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary" onclick="updateQuoteRequest()">Update Quote Request</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.quote-form-card {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
}

.provider-summary {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
    margin-bottom: 2rem;
}

.provider-summary h4 {
    margin: 0 0 1rem 0;
    color: #2c3e50;
}

.route-summary {
    display: flex;
    align-items: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.route-path {
    display: flex;
    align-items: center;
    font-weight: 500;
}

.transport-type {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #666;
}

.form-section {
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid #e9ecef;
}

.form-section:last-of-type {
    border-bottom: none;
}

.form-section h5 {
    color: #495057;
    margin-bottom: 1rem;
    font-weight: 600;
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: #495057;
}

.quote-options {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.form-check {
    padding: 0.75rem;
    background: #f8f9fa;
    border-radius: 6px;
}

.form-actions {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
    flex-wrap: wrap;
}

.quote-summary-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 1.5rem;
    position: sticky;
    top: 2rem;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
}

.summary-item label {
    font-weight: 500;
    color: #666;
}

.rate {
    font-weight: 600;
    color: #27ae60;
}

.estimated-cost {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 6px;
}

.cost-line {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.cost-line.total {
    border-top: 1px solid #dee2e6;
    padding-top: 0.5rem;
    margin-top: 0.5rem;
    color: #27ae60;
}

.info-note {
    background: #e7f3ff;
    padding: 1rem;
    border-radius: 6px;
    margin-top: 1rem;
    font-size: 0.9rem;
    color: #0066cc;
}

.info-note i {
    margin-right: 0.5rem;
}

.provider-contact-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.contact-actions {
    margin-top: 1rem;
}

@media (max-width: 768px) {
    .route-summary {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .quote-summary-card {
        position: static;
    }
}

/* Recent Quotes Styles */
.recent-quotes-card {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.recent-quotes-card h5 {
    color: #2c3e50;
    margin-bottom: 1.5rem;
    font-weight: 600;
}

.quote-item {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.quote-item:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-color: #dee2e6;
}

.quote-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.quote-info {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.quote-ref {
    font-weight: 600;
    color: #1e88e5;
    font-size: 1.1rem;
}

.quote-provider {
    font-weight: 500;
    color: #2c3e50;
}

.quote-route {
    color: #6c757d;
    font-size: 0.9rem;
}

.quote-status {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 0.5rem;
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
    text-transform: uppercase;
}

.status-pending {
    background-color: #fff3cd;
    color: #856404;
}

.status-quoted {
    background-color: #d1ecf1;
    color: #0c5460;
}

.status-accepted {
    background-color: #d4edda;
    color: #155724;
}

.status-declined {
    background-color: #f8d7da;
    color: #721c24;
}

.status-expired {
    background-color: #f8f9fa;
    color: #6c757d;
}

.quote-date {
    font-size: 0.8rem;
    color: #6c757d;
}

.quote-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
}

.detail-item {
    font-size: 0.9rem;
}

.detail-item strong {
    color: #2c3e50;
}

.quote-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: flex-end;
    border-top: 1px solid #e9ecef;
    padding-top: 1rem;
}

.quote-actions .btn {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
}

/* Enhanced Calculator Styles */
.calculator-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #e9ecef;
}

.calculator-header h6 {
    margin: 0;
    color: #2c3e50;
    font-weight: 600;
}

.cost-breakdown {
    margin-bottom: 1rem;
}

.cost-line {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    font-size: 0.9rem;
}

.cost-line.total {
    border-top: 2px solid #1e88e5;
    padding-top: 1rem;
    margin-top: 0.5rem;
    font-size: 1rem;
}

.calculator-info {
    background: #f8f9fa;
    padding: 0.75rem;
    border-radius: 6px;
    margin-top: 1rem;
}

.calculator-info small {
    line-height: 1.4;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
// Quote management functions
function editQuote(quoteId) {
    const quoteItem = document.querySelector(`[data-quote-id="${quoteId}"]`);
    const quoteData = getQuoteDataFromElement(quoteItem);
    
    // Populate modal with current data
    document.getElementById('editQuoteId').value = quoteId;
    document.getElementById('editCargoWeight').value = quoteData.weight;
    document.getElementById('editCargoDimensions').value = quoteData.dimensions;
    document.getElementById('editPickupDate').value = quoteData.pickupDate;
    document.getElementById('editDeliveryDate').value = quoteData.deliveryDate;
    document.getElementById('editSpecialRequirements').value = quoteData.specialRequirements;
    
    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('editQuoteModal'));
    modal.show();
}

function deleteQuote(quoteId) {
    if (confirm('Are you sure you want to delete this quote request?')) {
        fetch(`/api/quote-request/${quoteId}`, {
            method: 'DELETE',
            credentials: 'include'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Remove quote item from DOM
                const quoteItem = document.querySelector(`[data-quote-id="${quoteId}"]`);
                if (quoteItem) {
                    quoteItem.remove();
                }
                
                // Show success message
                alert('Quote request deleted successfully');
                
                // Reload page to update recent quotes
                window.location.reload();
            } else {
                alert('Error: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Failed to delete quote request');
        });
    }
}

function getQuoteDataFromElement(element) {
    const details = element.querySelectorAll('.detail-item');
    const data = {};
    
    details.forEach(item => {
        const text = item.textContent;
        if (text.includes('Weight:')) {
            data.weight = text.replace('Weight:', '').replace('kg', '').trim();
        } else if (text.includes('Dimensions:')) {
            data.dimensions = text.replace('Dimensions:', '').trim();
        } else if (text.includes('Pickup:')) {
            data.pickupDate = text.replace('Pickup:', '').trim();
        } else if (text.includes('Delivery:')) {
            data.deliveryDate = text.replace('Delivery:', '').trim();
        } else if (text.includes('Special Requirements:')) {
            data.specialRequirements = text.replace('Special Requirements:', '').trim();
        }
    });
    
    return data;
}

// Handle edit form submission
document.addEventListener('DOMContentLoaded', function() {
    // Edit form handler
    const editForm = document.getElementById('editQuoteForm');
    if (editForm) {
        editForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const quoteId = document.getElementById('editQuoteId').value;
            const formData = new FormData();
            
            formData.append('cargo_weight', document.getElementById('editCargoWeight').value);
            formData.append('cargo_dimensions', document.getElementById('editCargoDimensions').value);
            formData.append('pickup_date', document.getElementById('editPickupDate').value);
            formData.append('delivery_date', document.getElementById('editDeliveryDate').value);
            formData.append('special_requirements', document.getElementById('editSpecialRequirements').value);
            
            fetch(`/api/quote-request/${quoteId}`, {
                method: 'PUT',
                body: formData,
                credentials: 'include'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Close modal
                    const modal = bootstrap.Modal.getInstance(document.getElementById('editQuoteModal'));
                    modal.hide();
                    
                    // Show success message
                    alert('Quote request updated successfully');
                    
                    // Reload page to show updated data
                    window.location.reload();
                } else {
                    alert('Error: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Failed to update quote request');
            });
        });
    }
});

// Enhanced pricing calculator with distance, weight, and comprehensive cost factors
function calculatePrice() {
    const weight = parseFloat(document.getElementById('cargo_weight').value) || 0;
    const dimensions = document.getElementById('cargo_dimensions').value || '';
    const baseRate = parseFloat({{ route.price_per_kg }});
    
    if (weight <= 0) {
        alert('Please enter cargo weight to calculate price');
        return;
    }
    
    // Parse dimensions to calculate volume (assume format: LxWxH in cm)
    let volume = 0;
    if (dimensions) {
        const dimensionParts = dimensions.split('x').map(d => parseFloat(d.trim()));
        if (dimensionParts.length === 3 && dimensionParts.every(d => !isNaN(d))) {
            volume = dimensionParts.reduce((a, b) => a * b, 1) / 1000000; // Convert cm³ to m³
        }
    }
    
    // Calculate distance-based cost using route origin/destination
    const originCountry = '{{ route.origin_country }}';
    const destinationCountry = '{{ route.destination_country }}';
    const estimatedDistance = calculateDistance(originCountry, destinationCountry);
    
    // Base calculations
    const baseCost = weight * baseRate;
    const distanceCost = estimatedDistance * 0.15; // $0.15 per km
    const volumeCost = volume > 0 ? Math.max(0, (volume - weight * 0.001) * 200) : 0; // Dimensional weight pricing
    const fuelSurcharge = (baseCost + distanceCost) * 0.08; // 8% fuel surcharge
    const additionalCost = calculateAdditionalServices();
    
    // Calculate total
    const subtotal = baseCost + distanceCost + volumeCost + fuelSurcharge;
    const totalCost = subtotal + additionalCost;
    
    // Update display
    document.getElementById('baseCost').textContent = '$' + baseCost.toFixed(2);
    document.getElementById('distanceCost').textContent = '$' + distanceCost.toFixed(2) + ' (' + estimatedDistance + ' km)';
    document.getElementById('volumeCost').textContent = '$' + volumeCost.toFixed(2);
    document.getElementById('fuelSurcharge').textContent = '$' + fuelSurcharge.toFixed(2);
    document.getElementById('additionalCost').textContent = '$' + additionalCost.toFixed(2);
    document.getElementById('totalCost').textContent = '$' + totalCost.toFixed(2);
    
    // Show success message
    const calculatorInfo = document.querySelector('.calculator-info');
    calculatorInfo.innerHTML = `
        <small class="text-success">
            <i class="fas fa-check-circle"></i>
            Price calculated successfully! Breakdown includes ${weight}kg cargo over ${estimatedDistance}km distance.
        </small>
    `;
}

// Distance calculation function (simplified approximation)
function calculateDistance(origin, destination) {
    // Simplified distance matrix - in real implementation, use geolocation API
    const distances = {
        'United States': {
            'Canada': 500, 'Mexico': 800, 'United Kingdom': 5500, 'Germany': 6200,
            'France': 6000, 'China': 11000, 'Japan': 10000, 'Australia': 15000,
            'Brazil': 6500, 'India': 13000
        },
        'United Kingdom': {
            'Germany': 600, 'France': 400, 'Spain': 1000, 'Italy': 1200,
            'United States': 5500, 'China': 8000, 'Australia': 17000
        },
        'Germany': {
            'France': 500, 'Italy': 800, 'Spain': 1200, 'Poland': 300,
            'United Kingdom': 600, 'China': 7000, 'United States': 6200
        },
        'China': {
            'Japan': 2000, 'India': 3000, 'Australia': 7000, 'Germany': 7000,
            'United States': 11000, 'United Kingdom': 8000
        }
    };
    
    // Check if we have distance data
    if (distances[origin] && distances[origin][destination]) {
        return distances[origin][destination];
    } else if (distances[destination] && distances[destination][origin]) {
        return distances[destination][origin];
    } else if (origin === destination) {
        return 250; // Domestic average
    } else {
        // Default international distance
        return 5000;
    }
}

function calculateAdditionalServices() {
    let additionalCost = 0;
    
    // Check each service and add cost
    if (document.getElementById('insurance_coverage') && document.getElementById('insurance_coverage').checked) {
        additionalCost += 50; // Insurance fee
    }
    if (document.getElementById('priority_handling') && document.getElementById('priority_handling').checked) {
        additionalCost += 100; // Priority handling fee
    }
    if (document.getElementById('door_to_door') && document.getElementById('door_to_door').checked) {
        additionalCost += 75; // Door-to-door fee
    }
    
    return additionalCost;
}

// Contact provider functions
function callProvider() {
    alert('Provider Contact Information:\n\nCompany: {{ route.provider_name }}\nEmail: {{ route.provider_email }}\nPhone: ******-FREIGHT\n\nContact them directly for specific inquiries about this route.');
}

function sendMessage() {
    const message = prompt('Enter your message to {{ route.provider_name }}:');
    if (message && message.trim()) {
        // Send message to database via API
        fetch('/api/send-message-to-provider', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                'provider_id': '{{ route.provider_id }}',
                'message_content': message,
                'route_id': '{{ route.id }}'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Message sent successfully to {{ route.provider_name }}!\n\nYour message: "' + message + '"\n\nThey will respond within 24 hours.');
            } else {
                alert('Failed to send message: ' + (data.error || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Error sending message:', error);
            alert('Failed to send message. Please try again.');
        });
    }
}

// Auto-calculation event listeners for real-time pricing
document.addEventListener('DOMContentLoaded', function() {
    const weightInput = document.getElementById('cargo_weight');
    const dimensionsInput = document.getElementById('cargo_dimensions');
    const checkboxes = document.querySelectorAll('.form-check-input');
    
    // Add event listeners for auto-calculation
    if (weightInput) {
        weightInput.addEventListener('blur', calculatePrice);
        weightInput.addEventListener('input', debounce(calculatePrice, 500));
    }
    if (dimensionsInput) {
        dimensionsInput.addEventListener('blur', calculatePrice);
    }
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', calculatePrice);
    });
    
    // Initial calculation if weight is already filled
    if (weightInput && weightInput.value) {
        calculatePrice();
    }
});

// Debounce function to avoid excessive calculations
function debounce(func, delay) {
    let timeoutId;
    return function (...args) {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => func.apply(this, args), delay);
    };
}

// Set minimum dates for pickup and delivery
document.addEventListener('DOMContentLoaded', function() {
    const today = new Date().toISOString().split('T')[0];
    const pickupDate = document.getElementById('pickup_date');
    const deliveryDate = document.getElementById('delivery_date');
    
    if (pickupDate) {
        pickupDate.min = today;
        pickupDate.addEventListener('change', function() {
            if (deliveryDate) {
                deliveryDate.min = this.value;
            }
        });
    }
    
    if (deliveryDate) {
        deliveryDate.min = today;
    }
});


// Auto-generated button handlers

function updateQuoteRequest() {
    const form = document.querySelector('form');
    if (validateForm(form)) {
        showLoadingSpinner();
        fetch(form.action, {
            method: 'POST',
            body: new FormData(form)
        })
        .then(response => response.json())
        .then(data => {
            hideLoadingSpinner();
            if (data.success) {
                showAlert('Quote request updated successfully', 'success');
                if (data.redirect) window.location.href = data.redirect;
            } else {
                showAlert('Update failed: ' + data.error, 'error');
            }
        })
        .catch(error => {
            hideLoadingSpinner();
            showAlert('Update failed', 'error');
        });
    }
}

// Responsive behavior for button text
document.addEventListener('DOMContentLoaded', function() {
    function handleResize() {
        const isMobile = window.innerWidth <= 576;
        const btnTexts = document.querySelectorAll('.btn-text');
        
        btnTexts.forEach(text => {
            text.style.display = isMobile ? 'none' : 'inline';
        });
    }
    
    // Initial call
    handleResize();
    
    // Listen for window resize
    window.addEventListener('resize', handleResize);
});
</script>

{% block extra_css %}
<style>
/* Responsive Design */
@media (max-width: 1200px) {
    .page-container {
        padding: 1rem;
    }
    
    .quote-form-card {
        margin-bottom: 1rem;
    }
}

@media (max-width: 992px) {
    .page-container {
        padding: 0.75rem;
    }
    
    .page-header h1 {
        font-size: 1.75rem;
    }
    
    .quote-form-card {
        margin-bottom: 1rem;
    }
    
    .provider-summary h4 {
        font-size: 1.25rem;
    }
}

@media (max-width: 768px) {
    .page-container {
        padding: 0.5rem;
    }
    
    .page-header h1 {
        font-size: 1.5rem;
    }
    
    .page-header p {
        font-size: 0.9rem;
    }
    
    .quote-form-card {
        padding: 1rem;
    }
    
    .form-section h5 {
        font-size: 1.1rem;
    }
    
    .form-group {
        margin-bottom: 1rem;
    }
    
    .form-label {
        font-size: 0.9rem;
    }
    
    .form-control {
        font-size: 0.9rem;
        padding: 0.5rem 0.75rem;
    }
    
    .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }
}

@media (max-width: 576px) {
    .page-container {
        padding: 0.25rem;
    }
    
    .page-header h1 {
        font-size: 1.25rem;
    }
    
    .page-header p {
        font-size: 0.8rem;
    }
    
    .quote-form-card {
        padding: 0.75rem;
    }
    
    .form-section h5 {
        font-size: 1rem;
    }
    
    .form-label {
        font-size: 0.85rem;
    }
    
    .form-control {
        font-size: 0.85rem;
        padding: 0.5rem;
    }
    
    .btn {
        font-size: 0.9rem;
        padding: 0.75rem 1rem;
    }
    
    .breadcrumb {
        font-size: 0.8rem;
    }
}
</style>
{% endblock %}