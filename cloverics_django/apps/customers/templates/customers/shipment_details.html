{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2><i class="fas fa-box"></i> Shipment Details</h2>
                        <p class="text-muted">Tracking Number: {{ shipment.tracking_number }}</p>
                    </div>
                    <div>
                        <a href="/customer/manage-shipments" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Shipments
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Shipment Overview -->
        <div class="col-lg-8 col-md-12 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-info-circle"></i> Shipment Overview</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-lg-6 col-md-6 col-sm-12 mb-3">
                            <h6><i class="fas fa-map-marker-alt text-success"></i> Origin</h6>
                            <p><strong>{{ shipment.origin_city }}, {{ shipment.origin_country }}</strong></p>
                            
                            <h6><i class="fas fa-flag-checkered text-danger"></i> Destination</h6>
                            <p><strong>{{ shipment.destination_city }}, {{ shipment.destination_country }}</strong></p>
                            
                            <h6><i class="fas fa-truck text-primary"></i> Provider</h6>
                            <p><strong>{{ shipment.provider_name }}</strong></p>
                        </div>
                        <div class="col-lg-6 col-md-6 col-sm-12 mb-3">
                            <h6><i class="fas fa-weight text-info"></i> Weight</h6>
                            <p><strong>{{ shipment.weight_kg }} kg</strong></p>
                            
                            <h6><i class="fas fa-box text-warning"></i> Cargo Type</h6>
                            <p><strong>{{ shipment.cargo_type }}</strong></p>
                            
                            <h6><i class="fas fa-dollar-sign text-success"></i> Total Cost</h6>
                            <p><strong>${{ shipment.total_cost }}</strong></p>
                        </div>
                    </div>
                    
                    {% if shipment.description %}
                    <hr>
                    <h6><i class="fas fa-file-alt"></i> Description</h6>
                    <p>{{ shipment.description }}</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Status & Timeline -->
        <div class="col-lg-4 col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-timeline"></i> Status & Progress</h5>
                </div>
                <div class="card-body">
                    <div class="status-indicator mb-3">
                        <span class="status-badge status-{{ shipment.status }}">
                            {{ shipment.status_display }}
                        </span>
                    </div>
                    
                    <div class="progress mb-3">
                        <div class="progress-bar bg-primary" role="progressbar" 
                             style="width: {{ shipment.progress_percentage }}%"
                             aria-valuenow="{{ shipment.progress_percentage }}" 
                             aria-valuemin="0" aria-valuemax="100">
                            {{ shipment.progress_percentage }}%
                        </div>
                    </div>
                    
                    <div class="timeline">
                        <div class="timeline-item {% if shipment.progress_percentage >= 10 %}completed{% endif %}">
                            <i class="fas fa-check-circle"></i>
                            <span>Order Placed</span>
                            <small>{{ shipment.created_date }}</small>
                        </div>
                        <div class="timeline-item {% if shipment.progress_percentage >= 20 %}completed{% endif %}">
                            <i class="fas fa-handshake"></i>
                            <span>Confirmed</span>
                        </div>
                        <div class="timeline-item {% if shipment.progress_percentage >= 60 %}completed{% endif %}">
                            <i class="fas fa-truck"></i>
                            <span>In Transit</span>
                        </div>
                        <div class="timeline-item {% if shipment.progress_percentage >= 100 %}completed{% endif %}">
                            <i class="fas fa-flag-checkered"></i>
                            <span>Delivered</span>
                            <small>{{ shipment.estimated_delivery }}</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6><i class="fas fa-tools"></i> Quick Actions</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button class="btn btn-outline-primary" onclick="trackShipment('{{ shipment.tracking_number }}')">
                            <i class="fas fa-route"></i> Track Shipment
                        </button>
                        {% if shipment.status in ['PENDING', 'QUOTE_RECEIVED'] %}
                        <button class="btn btn-outline-warning" onclick="editShipment('{{ shipment.id }}')">
                            <i class="fas fa-edit"></i> Edit Shipment
                        </button>
                        {% endif %}
                        {% if shipment.status in ['PENDING', 'CONFIRMED'] %}
                        <button class="btn btn-outline-danger" onclick="cancelShipment('{{ shipment.id }}')">
                            <i class="fas fa-times"></i> Cancel Shipment
                        </button>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.status-badge {
    padding: 0.5rem 1rem;
    border-radius: 1rem;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.8rem;
}

.status-pending { background-color: #ffeaa7; color: #2d3436; }
.status-confirmed { background-color: #74b9ff; color: white; }
.status-in_transit { background-color: #00b894; color: white; }
.status-customs_clearance { background-color: #fdcb6e; color: #2d3436; }
.status-delivered { background-color: #00b894; color: white; }
.status-cancelled { background-color: #d63031; color: white; }

.timeline {
    position: relative;
}

.timeline-item {
    padding: 0.75rem 0;
    border-left: 3px solid #dee2e6;
    padding-left: 2rem;
    position: relative;
    opacity: 0.5;
}

.timeline-item.completed {
    border-color: #28a745;
    opacity: 1;
}

.timeline-item i {
    position: absolute;
    left: -0.6rem;
    background: white;
    padding: 0.2rem;
    border-radius: 50%;
    color: #dee2e6;
}

.timeline-item.completed i {
    color: #28a745;
}

.timeline-item small {
    display: block;
    color: #6c757d;
    font-size: 0.75rem;
}
</style>

<script>
function trackShipment(trackingNumber) {
    window.location.href = `/customer/track-shipment?tracking=${trackingNumber}`;
}

function editShipment(shipmentId) {
    window.location.href = `/customer/edit-shipment/${shipmentId}`;
}

function cancelShipment(shipmentId) {
    if (confirm('Are you sure you want to cancel this shipment?')) {
        fetch(`/customer/cancel-shipment/${shipmentId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Shipment cancelled successfully');
                location.reload();
            } else {
                alert('Error cancelling shipment: ' + data.message);
            }
        });
    }
}
</script>
{% endblock %}

{% block extra_css %}
<style>
/* Responsive Design */
@media (max-width: 1200px) {
    .container-fluid {
        padding: 1rem;
    }
    
    .card {
        margin-bottom: 1.5rem;
    }
}

@media (max-width: 992px) {
    .container-fluid {
        padding: 0.75rem;
    }
    
    .page-header .d-flex {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .card {
        margin-bottom: 1rem;
    }
}

@media (max-width: 768px) {
    .container-fluid {
        padding: 0.5rem;
    }
    
    h2 {
        font-size: 1.5rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .timeline {
        font-size: 0.9rem;
    }
    
    .timeline-item {
        padding: 0.5rem 0;
    }
    
    .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }
}

@media (max-width: 576px) {
    .container-fluid {
        padding: 0.25rem;
    }
    
    h2 {
        font-size: 1.25rem;
    }
    
    .card-body {
        padding: 0.75rem;
    }
    
    h6 {
        font-size: 0.9rem;
    }
    
    p {
        font-size: 0.85rem;
    }
    
    .timeline {
        font-size: 0.8rem;
    }
    
    .timeline-item {
        padding: 0.25rem 0;
    }
    
    .btn {
        font-size: 0.9rem;
        padding: 0.75rem 1rem;
    }
    
    .status-badge {
        font-size: 0.8rem;
        padding: 0.25rem 0.5rem;
    }
}
</style>
{% endblock %}