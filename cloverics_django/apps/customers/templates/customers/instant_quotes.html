{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-lg-12">
            <div class="d-flex justify-content-between align-items-center mb-4 flex-wrap">
                <div>
                    <h2 class="mb-1">🚀 Instant Multi-Modal Quotes</h2>
                    <p class="text-muted mb-0">Get instant quotes from all providers - Freightos-style instant comparison</p>
                </div>
                <div class="text-end">
                    <span class="badge bg-success">Phase 1 Feature</span>
                    <span class="badge bg-primary">Real-time</span>
                </div>
            </div>

            <!-- Quote Request Form -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-rocket"></i> Instant Quote Request</h5>
                </div>
                <div class="card-body">
                    <form id="instantQuoteForm">
                        <div class="row">
                            <!-- Origin -->
                            <div class="col-lg-6 col-md-6 col-sm-12 mb-3">
                                <h6 class="text-primary mb-3"><i class="fas fa-map-marker-alt"></i> Origin</h6>
                                
                                <div class="mb-3">
                                    <label class="form-label">Origin Country</label>
                                    <select class="form-select" name="origin_country" id="origin_country" required>
                                        <option value="">Select origin country</option>
                                        {% for country in countries %}
                                            <option value="{{ country }}">{{ country }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">Origin City</label>
                                    <input type="text" class="form-control" name="origin_city" id="origin_city" required placeholder="Enter origin city">
                                </div>
                                
                                <div class="mb-3" id="origin_state_div" style="display: none;">
                                    <label class="form-label">Origin State</label>
                                    <select class="form-select" name="origin_state" id="origin_state">
                                        <option value="">Select state</option>
                                        {% for state in us_states %}
                                            <option value="{{ state }}">{{ state }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>

                            <!-- Destination -->
                            <div class="col-lg-6 col-md-6 col-sm-12 mb-3">
                                <h6 class="text-success mb-3"><i class="fas fa-map-marker-alt"></i> Destination</h6>
                                
                                <div class="mb-3">
                                    <label class="form-label">Destination Country</label>
                                    <select class="form-select" name="destination_country" id="destination_country" required>
                                        <option value="">Select destination country</option>
                                        {% for country in countries %}
                                            <option value="{{ country }}">{{ country }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">Destination City</label>
                                    <input type="text" class="form-control" name="destination_city" id="destination_city" required placeholder="Enter destination city">
                                </div>
                                
                                <div class="mb-3" id="destination_state_div" style="display: none;">
                                    <label class="form-label">Destination State</label>
                                    <select class="form-select" name="destination_state" id="destination_state">
                                        <option value="">Select state</option>
                                        {% for state in us_states %}
                                            <option value="{{ state }}">{{ state }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                        </div>

                        <hr class="my-4">

                        <!-- Cargo Details -->
                        <div class="row">
                            <div class="col-lg-3 col-md-6 col-sm-6 mb-3">
                                <div class="mb-3">
                                    <label class="form-label">Weight (kg)</label>
                                    <input type="number" class="form-control" name="weight_kg" id="weight_kg" required min="1" step="0.1" placeholder="100">
                                </div>
                            </div>
                            
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label">Cargo Type</label>
                                    <select class="form-select" name="cargo_type" id="cargo_type" required>
                                        <option value="general">General Cargo</option>
                                        <option value="fragile">Fragile Items</option>
                                        <option value="hazardous">Hazardous Materials</option>
                                        <option value="refrigerated">Refrigerated</option>
                                        <option value="oversized">Oversized</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label">Transport Mode</label>
                                    <select class="form-select" name="transport_type" id="transport_type" required>
                                        <option value="truck">🚛 Truck</option>
                                        <option value="ship">🚢 Ship</option>
                                        <option value="air">✈️ Air</option>
                                        <option value="rail">🚆 Rail</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label">Urgency</label>
                                    <select class="form-select" name="urgency" id="urgency" required>
                                        <option value="standard">Standard</option>
                                        <option value="express">Express (+30%)</option>
                                        <option value="urgent">Urgent (+60%)</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="text-center">
                            <button type="submit" class="btn btn-primary btn-lg px-5" id="getQuotesBtn">
                                <i class="fas fa-rocket"></i> Get Instant Quotes
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Loading State -->
            <div id="loadingState" style="display: none;">
                <div class="card shadow-sm">
                    <div class="card-body text-center py-5">
                        <div class="spinner-border text-primary mb-3" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <h5>Requesting quotes from all providers...</h5>
                        <p class="text-muted">This may take a few seconds while we contact all available logistics providers</p>
                        <div class="progress">
                            <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 100%"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quote Results -->
            <div id="quoteResults" style="display: none;">
                <!-- Market Insights -->
                <div class="card shadow-sm mb-4" id="marketInsights">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0"><i class="fas fa-chart-line"></i> Market Insights</h5>
                    </div>
                    <div class="card-body" id="insightsContent">
                        <!-- Dynamic insights content -->
                    </div>
                </div>

                <!-- Quote Comparison Controls -->
                <div class="card shadow-sm mb-4">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h5 class="mb-1">Quote Comparison</h5>
                                <p class="text-muted mb-0">Sort and compare quotes from multiple providers</p>
                            </div>
                            <div class="d-flex gap-2">
                                <select class="form-select" id="sortBy" style="width: auto;">
                                    <option value="price">Sort by Price</option>
                                    <option value="delivery_time">Sort by Delivery Time</option>
                                    <option value="rating">Sort by Provider Rating</option>
                                    <option value="value">Sort by Best Value</option>
                                </select>
                                <button class="btn btn-outline-primary" id="refreshQuotes">
                                    <i class="fas fa-sync-alt"></i> Refresh
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quote Cards -->
                <div class="row" id="quoteCards">
                    <!-- Dynamic quote cards -->
                </div>
            </div>

            <!-- No Results -->
            <div id="noResults" style="display: none;">
                <div class="card shadow-sm">
                    <div class="card-body text-center py-5">
                        <i class="fas fa-search fa-3x text-muted mb-3"></i>
                        <h5>No quotes available</h5>
                        <p class="text-muted">No active providers found for this route. Try adjusting your search criteria.</p>
                        <button class="btn btn-primary" onclick="location.reload()">Try Again</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// US State visibility logic
document.getElementById('origin_country').addEventListener('change', function() {
    const stateDiv = document.getElementById('origin_state_div');
    if (this.value === 'United States') {
        stateDiv.style.display = 'block';
        document.getElementById('origin_state').required = true;
    } else {
        stateDiv.style.display = 'none';
        document.getElementById('origin_state').required = false;
        document.getElementById('origin_state').value = '';
    }
});

document.getElementById('destination_country').addEventListener('change', function() {
    const stateDiv = document.getElementById('destination_state_div');
    if (this.value === 'United States') {
        stateDiv.style.display = 'block';
        document.getElementById('destination_state').required = true;
    } else {
        stateDiv.style.display = 'none';
        document.getElementById('destination_state').required = false;
        document.getElementById('destination_state').value = '';
    }
});

// Instant Quote Request
document.getElementById('instantQuoteForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const getQuotesBtn = document.getElementById('getQuotesBtn');
    const loadingState = document.getElementById('loadingState');
    const quoteResults = document.getElementById('quoteResults');
    const noResults = document.getElementById('noResults');
    
    // Show loading state
    getQuotesBtn.disabled = true;
    getQuotesBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
    loadingState.style.display = 'block';
    quoteResults.style.display = 'none';
    noResults.style.display = 'none';
    
    try {
        const response = await fetch('/api/quotes/instant-request', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        if (result.success) {
            displayQuoteResults(result.data);
            
            // Start live updates if quotes are still being processed
            if (result.data.status === 'processing') {
                startLiveUpdates(result.data.batch_id);
            }
        } else {
            throw new Error(result.error || 'Failed to get quotes');
        }
        
    } catch (error) {
        console.error('Quote request failed:', error);
        alert('Failed to get quotes: ' + error.message);
        noResults.style.display = 'block';
    } finally {
        loadingState.style.display = 'none';
        getQuotesBtn.disabled = false;
        getQuotesBtn.innerHTML = '<i class="fas fa-rocket"></i> Get Instant Quotes';
    }
});

// Display quote results
function displayQuoteResults(data) {
    const quoteResults = document.getElementById('quoteResults');
    const quoteCards = document.getElementById('quoteCards');
    const insightsContent = document.getElementById('insightsContent');
    
    // Display market insights
    if (data.market_insights) {
        displayMarketInsights(data.market_insights, insightsContent);
    }
    
    // Display quotes
    if (data.quotes && data.quotes.length > 0) {
        displayQuoteCards(data.quotes, quoteCards);
        quoteResults.style.display = 'block';
    } else {
        document.getElementById('noResults').style.display = 'block';
    }
}

// Display market insights
function displayMarketInsights(insights, container) {
    let html = '<div class="row">';
    
    if (insights.average_price) {
        html += `
            <div class="col-md-3">
                <div class="text-center">
                    <h4 class="text-primary mb-1">$${insights.average_price}</h4>
                    <p class="text-muted mb-0">Average Price</p>
                </div>
            </div>
        `;
    }
    
    if (insights.total_options) {
        html += `
            <div class="col-md-3">
                <div class="text-center">
                    <h4 class="text-success mb-1">${insights.total_options}</h4>
                    <p class="text-muted mb-0">Available Options</p>
                </div>
            </div>
        `;
    }
    
    if (insights.price_range) {
        html += `
            <div class="col-md-3">
                <div class="text-center">
                    <h4 class="text-info mb-1">${insights.price_range.spread_percentage}%</h4>
                    <p class="text-muted mb-0">Price Variation</p>
                </div>
            </div>
        `;
    }
    
    if (insights.best_value_provider) {
        html += `
            <div class="col-md-3">
                <div class="text-center">
                    <h6 class="text-warning mb-1">${insights.best_value_provider}</h6>
                    <p class="text-muted mb-0">Best Value</p>
                </div>
            </div>
        `;
    }
    
    html += '</div>';
    
    if (insights.recommendations && insights.recommendations.length > 0) {
        html += '<hr><div class="mt-3"><h6>Recommendations:</h6><ul class="mb-0">';
        insights.recommendations.forEach(rec => {
            html += `<li class="text-muted">${rec}</li>`;
        });
        html += '</ul></div>';
    }
    
    container.innerHTML = html;
}

// Display quote cards
function displayQuoteCards(quotes, container) {
    let html = '';
    
    quotes.forEach((quote, index) => {
        if (quote.status === 'success') {
            const isLowest = index === 0; // Assuming sorted by price
            const badgeClass = isLowest ? 'bg-success' : 'bg-primary';
            const badgeText = isLowest ? 'BEST PRICE' : 'AVAILABLE';
            
            html += `
                <div class="col-lg-4 mb-4">
                    <div class="card h-100 ${isLowest ? 'border-success' : ''}">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-0">${quote.provider_name}</h6>
                                <small class="text-muted">Rating: ${quote.provider_rating || 'N/A'}/5</small>
                            </div>
                            <span class="badge ${badgeClass}">${badgeText}</span>
                        </div>
                        <div class="card-body">
                            <div class="text-center mb-3">
                                <h3 class="text-primary mb-1">$${quote.total_price}</h3>
                                <p class="text-muted mb-0">${quote.estimated_delivery_days} days delivery</p>
                            </div>
                            
                            <div class="mb-3">
                                <h6>Price Breakdown:</h6>
                                <ul class="list-unstyled small">
                                    <li>Base Cost: $${quote.breakdown.base_cost}</li>
                                    <li>Weight Cost: $${quote.breakdown.weight_cost}</li>
                                    <li>Fuel Surcharge: $${quote.breakdown.fuel_surcharge}</li>
                                    <li>Handling Fee: $${quote.breakdown.handling_fee}</li>
                                </ul>
                            </div>
                            
                            <div class="mb-3">
                                <h6>Service Features:</h6>
                                <div class="d-flex flex-wrap gap-1">
                                    ${quote.service_features.map(feature => 
                                        `<span class="badge bg-light text-dark">${feature}</span>`
                                    ).join('')}
                                </div>
                            </div>
                        </div>
                        <div class="card-footer">
                            <div class="d-grid gap-2">
                                <button class="btn btn-primary" onclick="acceptQuote('${quote.batch_id}', ${quote.provider_id}, ${quote.rate_id})">
                                    <i class="fas fa-check"></i> Accept Quote
                                </button>
                                <button class="btn btn-outline-secondary btn-sm" onclick="viewProviderDetails(${quote.provider_id})">
                                    View Provider Details
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }
    });
    
    container.innerHTML = html;
}

// Accept quote
async function acceptQuote(batchId, providerId, rateId) {
    if (!confirm('Are you sure you want to accept this quote? This will create a shipment.')) {
        return;
    }
    
    try {
        const formData = new FormData();
        formData.append('batch_id', batchId);
        formData.append('provider_id', providerId);
        formData.append('rate_id', rateId);
        
        const response = await fetch('/api/quotes/accept', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        if (result.success) {
            alert('Quote accepted successfully!');
            if (result.data.redirect_url) {
                window.location.href = result.data.redirect_url;
            }
        } else {
            throw new Error(result.error || 'Failed to accept quote');
        }
        
    } catch (error) {
        console.error('Quote acceptance failed:', error);
        alert('Failed to accept quote: ' + error.message);
    }
}

// View provider details
function viewProviderDetails(providerId) {
    // Redirect to provider details page
    window.location.href = `/customer/provider-details/${providerId}`;
}

// Sort quotes
document.getElementById('sortBy').addEventListener('change', function() {
    // Re-sort and display quotes based on selected criteria
    // Implementation would call the comparison API
});

// Live updates for quote processing
function startLiveUpdates(batchId) {
    const interval = setInterval(async () => {
        try {
            const response = await fetch(`/api/quotes/live-updates/${batchId}`);
            const result = await response.json();
            
            if (result.success) {
                if (result.data.status === 'completed') {
                    clearInterval(interval);
                    displayQuoteResults(result.data);
                }
            }
        } catch (error) {
            console.error('Live update failed:', error);
            clearInterval(interval);
        }
    }, 2000); // Check every 2 seconds
}


// Auto-generated function implementations

function location.reload() {
    // Refresh functionality
    location.reload();
}
</script>

<style>
.card {
    border: 1px solid #e3e6f0;
    border-radius: 0.375rem;
}

.card-header {
    background-color: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;
}

.border-success {
    border-color: #28a745 !important;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.progress {
    height: 6px;
}

.badge {
    font-size: 0.7em;
}

.spinner-border {
    width: 3rem;
    height: 3rem;
}

.btn-lg {
    padding: 0.75rem 2rem;
    font-size: 1.1rem;
}

.text-primary { color: #007bff !important; }
.text-success { color: #28a745 !important; }
.text-info { color: #17a2b8 !important; }
.text-warning { color: #ffc107 !important; }
</style>
{% endblock %}

{% block extra_css %}
<style>
/* Responsive Design */
@media (max-width: 1200px) {
    .container-fluid {
        padding: 1rem;
    }
    
    .card {
        margin-bottom: 1rem;
    }
}

@media (max-width: 992px) {
    .container-fluid {
        padding: 0.75rem;
    }
    
    .d-flex.justify-content-between {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .text-end {
        text-align: left !important;
    }
    
    .card-body {
        padding: 1.25rem;
    }
}

@media (max-width: 768px) {
    .container-fluid {
        padding: 0.5rem;
    }
    
    h2 {
        font-size: 1.5rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .form-label {
        font-size: 0.9rem;
    }
    
    .form-control,
    .form-select {
        font-size: 0.9rem;
        padding: 0.5rem 0.75rem;
    }
    
    .badge {
        font-size: 0.7rem;
        padding: 0.25rem 0.5rem;
    }
}

@media (max-width: 576px) {
    .container-fluid {
        padding: 0.25rem;
    }
    
    h2 {
        font-size: 1.25rem;
    }
    
    .card-body {
        padding: 0.75rem;
    }
    
    .form-label {
        font-size: 0.85rem;
    }
    
    .form-control,
    .form-select {
        font-size: 0.85rem;
        padding: 0.5rem;
    }
    
    .btn {
        font-size: 0.9rem;
        padding: 0.75rem 1rem;
    }
    
    .badge {
        font-size: 0.65rem;
        padding: 0.2rem 0.4rem;
    }
}
</style>
{% endblock %}