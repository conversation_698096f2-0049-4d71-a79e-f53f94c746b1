{% extends "base.html" %}
{% load translation_tags %}

{% block title %}Messages - Cloverics{% endblock %}

{% block extra_css %}
<style>
/* Responsive Design */
@media (max-width: 1200px) {
    .container-fluid {
        padding: 1rem;
    }
}

@media (max-width: 992px) {
    .container-fluid {
        padding: 0.75rem;
    }
    
    .d-flex.justify-content-between {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .nav-tabs {
        flex-direction: column;
        width: 100%;
    }
    
    .nav-tabs .nav-item {
        width: 100%;
    }
    
    .nav-tabs .nav-link {
        width: 100%;
        text-align: center;
    }
}

@media (max-width: 768px) {
    .container-fluid {
        padding: 0.5rem;
    }
    
    h2 {
        font-size: 1.5rem;
    }
    
    .message-item {
        padding: 0.75rem 0;
    }
    
    .d-flex.justify-content-between.align-items-start {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .d-flex.align-items-center {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .d-flex.gap-2 {
        flex-direction: column;
        width: 100%;
    }
    
    .d-flex.gap-2 .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }
    
    .btn-text {
        display: none;
    }
    
    .badge {
        font-size: 0.7rem;
    }
    
    h6 {
        font-size: 0.9rem;
    }
    
    p {
        font-size: 0.85rem;
    }
    
    small {
        font-size: 0.75rem;
    }
}

@media (max-width: 576px) {
    .container-fluid {
        padding: 0.25rem;
    }
    
    h2 {
        font-size: 1.25rem;
    }
    
    .message-item {
        padding: 0.5rem 0;
    }
    
    .card-body {
        padding: 0.75rem;
    }
    
    .btn-sm {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }
    
    .badge {
        font-size: 0.65rem;
    }
    
    h6 {
        font-size: 0.85rem;
    }
    
    p {
        font-size: 0.8rem;
    }
    
    small {
        font-size: 0.7rem;
    }
}

/* Landscape orientation adjustments for mobile */
@media (max-width: 768px) and (orientation: landscape) {
    .d-flex.gap-2 {
        flex-direction: row;
        flex-wrap: wrap;
    }
    
    .d-flex.gap-2 .btn {
        width: auto;
        margin-bottom: 0;
        flex: 1;
    }
    
    .btn-text {
        display: inline;
    }
}
</style>
{% endblock %}

{% block extra_js %}
<script>
// Responsive behavior for button text
document.addEventListener('DOMContentLoaded', function() {
    function handleResize() {
        const isMobile = window.innerWidth <= 576;
        const btnTexts = document.querySelectorAll('.btn-text');
        
        btnTexts.forEach(text => {
            text.style.display = isMobile ? 'none' : 'inline';
        });
    }
    
    // Initial call
    handleResize();
    
    // Listen for window resize
    window.addEventListener('resize', handleResize);
});
</script>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4 flex-wrap">
                <h2><i class="fas fa-comments text-primary"></i> Messages</h2>
                <button class="btn btn-primary" onclick="composeMessage()">
                    <i class="fas fa-plus"></i> <span class="btn-text">Compose Message</span>
                </button>
            </div>

            <!-- Message Tabs -->
            <ul class="nav nav-tabs mb-4" id="messageTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="inbox-tab" data-bs-toggle="tab" data-bs-target="#inbox" type="button" role="tab">
                        <i class="fas fa-inbox"></i> Inbox ({{ messages.inbox|length }})
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="sent-tab" data-bs-toggle="tab" data-bs-target="#sent" type="button" role="tab">
                        <i class="fas fa-paper-plane"></i> Sent ({{ messages.sent|length }})
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="compose-tab" data-bs-toggle="tab" data-bs-target="#compose" type="button" role="tab">
                        <i class="fas fa-edit"></i> Compose
                    </button>
                </li>
            </ul>

            <!-- Tab Content -->
            <div class="tab-content" id="messageTabContent">
                <!-- Inbox Tab -->
                <div class="tab-pane fade show active" id="inbox" role="tabpanel">
                    <div class="card">
                        <div class="card-body">
                            {% if messages.inbox %}
                                {% for message in messages.inbox %}
                                <div class="message-item border-bottom py-3 {% if not message.is_read %}bg-light{% endif %}">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div class="flex-grow-1">
                                            <div class="d-flex align-items-center mb-2">
                                                <strong class="me-2">{{ message.sender_name }}</strong>
                                                <span class="badge bg-primary me-2">{{ message.sender_type }}</span>
                                                <small class="text-muted">{{ message.created_at|date:"M d, Y H:i" }}</small>
                                            </div>
                                            <h6 class="mb-2">{{ message.subject }}</h6>
                                            <p class="mb-2 text-muted">{{ message.content|truncatewords:20 }}</p>
                                            <div class="d-flex gap-2 flex-wrap">
                                                <button class="btn btn-sm btn-primary" onclick="viewMessage({{ message.id }})">
                                                    <i class="fas fa-eye"></i> <span class="btn-text">View</span>
                                                </button>
                                                <button class="btn btn-sm btn-outline-primary" onclick="replyMessage({{ message.id }})">
                                                    <i class="fas fa-reply"></i> <span class="btn-text">Reply</span>
                                                </button>
                                                <button class="btn btn-sm btn-outline-danger" onclick="deleteMessage({{ message.id }})">
                                                    <i class="fas fa-trash"></i> <span class="btn-text">Delete</span>
                                                </button>
                                            </div>
                                        </div>
                                        {% if not message.is_read %}
                                        <span class="badge bg-success">New</span>
                                        {% endif %}
                                    </div>
                                </div>
                                {% endfor %}
                            {% else %}
                                <div class="text-center py-5">
                                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">No messages in your inbox</h5>
                                    <p class="text-muted">New messages will appear here</p>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Sent Tab -->
                <div class="tab-pane fade" id="sent" role="tabpanel">
                    <div class="card">
                        <div class="card-body">
                            {% if messages.sent %}
                                {% for message in messages.sent %}
                                <div class="message-item border-bottom py-3">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div class="flex-grow-1">
                                            <div class="d-flex align-items-center mb-2">
                                                <strong class="me-2">To: {{ message.receiver_name }}</strong>
                                                <span class="badge bg-secondary me-2">{{ message.receiver_type }}</span>
                                                <small class="text-muted">{{ message.created_at|date:"M d, Y H:i" }}</small>
                                            </div>
                                            <h6 class="mb-2">{{ message.subject }}</h6>
                                            <p class="mb-2 text-muted">{{ message.content|truncatewords:20 }}</p>
                                            <button class="btn btn-sm btn-primary" onclick="viewMessage({{ message.id }})">
                                                <i class="fas fa-eye"></i> View
                                            </button>
                                        </div>
                                        <span class="badge bg-{% if message.is_read %}success{% else %}warning{% endif %}">
                                            {% if message.is_read %}Read{% else %}Unread{% endif %}
                                        </span>
                                    </div>
                                </div>
                                {% endfor %}
                            {% else %}
                                <div class="text-center py-5">
                                    <i class="fas fa-paper-plane fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">{% translate 'no_sent_messages' %}</h5>
                                    <p class="text-muted">{% translate 'messages_you_send' %}</p>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Compose Tab -->
                <div class="tab-pane fade" id="compose" role="tabpanel">
                    <div class="card">
                        <div class="card-body">
                            <form id="composeForm" onsubmit="sendMessage(event)">
                                <div class="mb-3">
                                    <label for="recipient" class="form-label">{% translate 'recipient' %}</label>
                                    <select class="form-select" id="recipient" name="recipient_id" required>
                                        <option value="">{% translate 'select_recipient' %}</option>
                                        {% for provider in logistics_providers %}
                                        <option value="{{ provider.id }}">{{ provider.company_name }} ({% translate 'logistics_provider_option' %})</option>
                                        {% endfor %}
                                        <option value="admin">{% translate 'admin_support' %}</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="subject" class="form-label">{% translate 'subject' %}</label>
                                    <input type="text" class="form-control" id="subject" name="subject" required>
                                </div>
                                <div class="mb-3">
                                    <label for="content" class="form-label">Message</label>
                                    <textarea class="form-control" id="content" name="content" rows="5" required></textarea>
                                </div>
                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-paper-plane"></i> Send Message
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary" onclick="clearForm()">
                                        <i class="fas fa-times"></i> Clear
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function composeMessage() {
    document.getElementById('compose-tab').click();
}

function viewMessage(messageId) {
    // Implement message viewing
    Swal.fire({
        title: 'View Message',
        text: 'Loading message details...',
        icon: 'info'
    });
}

function replyMessage(messageId) {
    // Switch to compose tab and pre-fill recipient
    document.getElementById('compose-tab').click();
    Swal.fire({
        title: 'Reply',
        text: 'Compose tab opened for reply',
        icon: 'success'
    });
}

function deleteMessage(messageId) {
    Swal.fire({
        title: 'Delete Message',
        text: 'Are you sure you want to delete this message?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#dc3545',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'Yes, delete it!'
    }).then((result) => {
        if (result.isConfirmed) {
            // Implement delete functionality
            Swal.fire('Deleted!', 'Message has been deleted.', 'success');
        }
    });
}

function sendMessage(event) {
    event.preventDefault();
    
    const formData = new FormData(event.target);
    
    Swal.fire({
        title: 'Sending Message',
        text: 'Please wait...',
        allowOutsideClick: false,
        didOpen: () => {
            Swal.showLoading();
        }
    });
    
    // Simulate sending
    setTimeout(() => {
        Swal.fire({
            title: 'Message Sent!',
            text: 'Your message has been sent successfully.',
            icon: 'success'
        });
        event.target.reset();
        document.getElementById('inbox-tab').click();
    }, 1500);
}

function clearForm() {
    document.getElementById('composeForm').reset();
}
</script>
{% endblock %}