{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
    <style>
        .export-card {
            border: 1px solid #e3e6f0;
            border-radius: 10px;
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
            transition: all 0.3s;
        }
        .export-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 0.25rem 2rem 0 rgba(58, 59, 69, 0.2);
        }
        .export-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            margin: 0 auto 15px;
        }
        .format-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 15px;
            margin: 2px;
            cursor: pointer;
            transition: all 0.2s;
        }
        .format-badge:hover {
            transform: scale(1.05);
        }
        .format-badge.selected {
            background-color: #4e73df !important;
            color: white !important;
        }
        .export-progress {
            display: none;
        }
        .color-scheme-option {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            cursor: pointer;
            margin: 5px;
            border: 3px solid transparent;
            transition: all 0.2s;
        }
        .color-scheme-option:hover {
            transform: scale(1.1);
        }
        .color-scheme-option.selected {
            border-color: #4e73df;
        }
        .cloverics-scheme { background: linear-gradient(45deg, #2E7D32, #66BB6A); }
        .professional-scheme { background: linear-gradient(45deg, #1976D2, #42A5F5); }
        .executive-scheme { background: linear-gradient(45deg, #424242, #9E9E9E); }

        /* Responsive Design */
        @media (max-width: 1200px) {
            .container-fluid {
                padding: 1rem;
            }
            
            .export-card {
                margin-bottom: 1rem;
            }
        }

        @media (max-width: 992px) {
            .container-fluid {
                padding: 0.75rem;
            }
            
            .d-sm-flex {
                flex-direction: column;
                align-items: flex-start;
                gap: 1rem;
            }
            
            .btn-group {
                justify-content: flex-start;
                width: 100%;
            }
            
            .btn-group .btn {
                flex: 1;
                min-width: 120px;
            }
            
            .export-icon {
                width: 50px;
                height: 50px;
                font-size: 20px;
            }
        }

        @media (max-width: 768px) {
            .container-fluid {
                padding: 0.5rem;
            }
            
            h1.h3 {
                font-size: 1.5rem;
            }
            
            .card-body {
                padding: 1rem;
            }
            
            .btn-group {
                flex-direction: column;
                width: 100%;
            }
            
            .btn-group .btn {
                width: 100%;
                margin-bottom: 0.5rem;
            }
            
            .btn-text {
                display: none;
            }
            
            .export-icon {
                width: 45px;
                height: 45px;
                font-size: 18px;
            }
            
            .format-badge {
                font-size: 0.7rem;
                padding: 3px 6px;
            }
            
            .color-scheme-option {
                width: 25px;
                height: 25px;
            }
        }

        @media (max-width: 576px) {
            .container-fluid {
                padding: 0.25rem;
            }
            
            h1.h3 {
                font-size: 1.25rem;
            }
            
            .card-body {
                padding: 0.75rem;
            }
            
            .export-icon {
                width: 40px;
                height: 40px;
                font-size: 16px;
            }
            
            .format-badge {
                font-size: 0.65rem;
                padding: 2px 4px;
            }
            
            .color-scheme-option {
                width: 20px;
                height: 20px;
            }
            
            .btn-sm {
                font-size: 0.8rem;
                padding: 0.5rem 0.75rem;
            }
        }
    </style>
    <div class="container-fluid">
        <div class="row">
            <!-- Main content -->
            <div class="col-12">
                <div class="container-fluid px-4">
                    <!-- Header -->
                    <div class="d-sm-flex align-items-center justify-content-between mb-4 flex-wrap">
                        <h1 class="h3 mb-0 text-gray-800">
                            <i class="fas fa-file-export text-primary"></i>
                            Advanced Export Center
                        </h1>
                        <div class="btn-group flex-wrap">
                            <button class="btn btn-outline-primary btn-sm" onclick="refreshExportStats()">
                                <i class="fas fa-sync-alt"></i> <span class="btn-text">Refresh</span>
                            </button>
                            <button class="btn btn-outline-info btn-sm" onclick="showExportHistory()">
                                <i class="fas fa-history"></i> <span class="btn-text">History</span>
                            </button>
                        </div>
                    </div>

                    <!-- Export Statistics Row -->
                    <div class="row mb-4">
                        <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12 mb-4">
                            <div class="card border-left-primary shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                                Total Exports</div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="totalExports">0</div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-download fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card border-left-success shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                                Most Popular</div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="popularFormat">PDF</div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-file-pdf fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card border-left-info shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                                Last Export</div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="lastExport">Never</div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card border-left-warning shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                                Available Formats</div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">6</div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-list fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Export Options Row -->
                    <div class="row">
                        <!-- Shipment Reports -->
                        <div class="col-xl-4 col-lg-6 mb-4">
                            <div class="card export-card h-100">
                                <div class="card-body text-center">
                                    <div class="export-icon bg-primary text-white">
                                        <i class="fas fa-shipping-fast"></i>
                                    </div>
                                    <h5 class="card-title">Shipment Reports</h5>
                                    <p class="card-text text-muted">
                                        Export comprehensive shipment data with analytics and charts
                                    </p>
                                    
                                    <!-- Format Selection -->
                                    <div class="mb-3">
                                        <label class="form-label small fw-bold">Export Format:</label>
                                        <div class="format-selection" data-export-type="shipment">
                                            <span class="badge format-badge bg-secondary selected" data-format="pdf">PDF</span>
                                            <span class="badge format-badge bg-secondary" data-format="excel">Excel</span>
                                            <span class="badge format-badge bg-secondary" data-format="csv">CSV</span>
                                            <span class="badge format-badge bg-secondary" data-format="json">JSON</span>
                                            <span class="badge format-badge bg-secondary" data-format="zip">ZIP</span>
                                        </div>
                                    </div>
                                    
                                    <button class="btn btn-primary btn-sm" onclick="exportShipmentReport()">
                                        <i class="fas fa-download"></i> Export Shipments
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Analytics Dashboard -->
                        <div class="col-xl-4 col-lg-6 mb-4">
                            <div class="card export-card h-100">
                                <div class="card-body text-center">
                                    <div class="export-icon bg-success text-white">
                                        <i class="fas fa-chart-line"></i>
                                    </div>
                                    <h5 class="card-title">Analytics Dashboard</h5>
                                    <p class="card-text text-muted">
                                        Export analytics data with interactive charts and insights
                                    </p>
                                    
                                    <!-- Format Selection -->
                                    <div class="mb-3">
                                        <label class="form-label small fw-bold">Export Format:</label>
                                        <div class="format-selection" data-export-type="analytics">
                                            <span class="badge format-badge bg-secondary" data-format="pdf">PDF</span>
                                            <span class="badge format-badge bg-secondary selected" data-format="excel">Excel</span>
                                            <span class="badge format-badge bg-secondary" data-format="csv">CSV</span>
                                            <span class="badge format-badge bg-secondary" data-format="json">JSON</span>
                                            <span class="badge format-badge bg-secondary" data-format="zip">ZIP</span>
                                        </div>
                                    </div>
                                    
                                    <button class="btn btn-success btn-sm" onclick="exportAnalyticsDashboard()">
                                        <i class="fas fa-chart-bar"></i> Export Analytics
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Performance Scorecard -->
                        <div class="col-xl-4 col-lg-6 mb-4">
                            <div class="card export-card h-100">
                                <div class="card-body text-center">
                                    <div class="export-icon bg-info text-white">
                                        <i class="fas fa-trophy"></i>
                                    </div>
                                    <h5 class="card-title">Performance Scorecard</h5>
                                    <p class="card-text text-muted">
                                        Export detailed performance metrics and comparisons
                                    </p>
                                    
                                    <!-- Format Selection -->
                                    <div class="mb-3">
                                        <label class="form-label small fw-bold">Export Format:</label>
                                        <div class="format-selection" data-export-type="performance">
                                            <span class="badge format-badge bg-secondary selected" data-format="pdf">PDF</span>
                                            <span class="badge format-badge bg-secondary" data-format="excel">Excel</span>
                                            <span class="badge format-badge bg-secondary" data-format="csv">CSV</span>
                                            <span class="badge format-badge bg-secondary" data-format="json">JSON</span>
                                            <span class="badge format-badge bg-secondary" data-format="zip">ZIP</span>
                                        </div>
                                    </div>
                                    
                                    <button class="btn btn-info btn-sm" onclick="exportPerformanceScorecard()">
                                        <i class="fas fa-medal"></i> Export Scorecard
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Advanced Configuration Panel -->
                    <div class="row">
                        <div class="col-12">
                            <div class="card shadow mb-4">
                                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                                    <h6 class="m-0 font-weight-bold text-primary">Advanced Export Configuration</h6>
                                    <button class="btn btn-link btn-sm" data-bs-toggle="collapse" data-bs-target="#advancedConfig">
                                        <i class="fas fa-chevron-down"></i>
                                    </button>
                                </div>
                                <div class="collapse" id="advancedConfig">
                                    <div class="card-body">
                                        <div class="row">
                                            <!-- Export Options -->
                                            <div class="col-md-4">
                                                <h6 class="font-weight-bold">Content Options</h6>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="includeCharts" checked>
                                                    <label class="form-check-label" for="includeCharts">
                                                        Include Charts & Visualizations
                                                    </label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="includeSummary" checked>
                                                    <label class="form-check-label" for="includeSummary">
                                                        Include Executive Summary
                                                    </label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="includeDetails" checked>
                                                    <label class="form-check-label" for="includeDetails">
                                                        Include Detailed Data
                                                    </label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="includeBranding" checked>
                                                    <label class="form-check-label" for="includeBranding">
                                                        Include Cloverics Branding
                                                    </label>
                                                </div>
                                            </div>

                                            <!-- Page Layout -->
                                            <div class="col-md-4">
                                                <h6 class="font-weight-bold">Page Layout</h6>
                                                <div class="form-group">
                                                    <label for="pageOrientation" class="form-label">Orientation:</label>
                                                    <select class="form-select form-select-sm" id="pageOrientation">
                                                        <option value="portrait">Portrait</option>
                                                        <option value="landscape">Landscape</option>
                                                    </select>
                                                </div>
                                                <div class="form-group">
                                                    <label for="chartStyle" class="form-label">Chart Style:</label>
                                                    <select class="form-select form-select-sm" id="chartStyle">
                                                        <option value="professional">Professional</option>
                                                        <option value="modern">Modern</option>
                                                        <option value="classic">Classic</option>
                                                    </select>
                                                </div>
                                            </div>

                                            <!-- Color Scheme -->
                                            <div class="col-md-4">
                                                <h6 class="font-weight-bold">Color Scheme</h6>
                                                <div class="d-flex align-items-center mb-2">
                                                    <div class="color-scheme-option cloverics-scheme selected" 
                                                         data-scheme="cloverics" title="Cloverics Theme"></div>
                                                    <span class="ms-2">Cloverics</span>
                                                </div>
                                                <div class="d-flex align-items-center mb-2">
                                                    <div class="color-scheme-option professional-scheme" 
                                                         data-scheme="professional" title="Professional Theme"></div>
                                                    <span class="ms-2">Professional</span>
                                                </div>
                                                <div class="d-flex align-items-center mb-2">
                                                    <div class="color-scheme-option executive-scheme" 
                                                         data-scheme="executive" title="Executive Theme"></div>
                                                    <span class="ms-2">Executive</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Export Progress -->
                    <div class="row export-progress" id="exportProgress">
                        <div class="col-12">
                            <div class="card shadow">
                                <div class="card-body">
                                    <h6 class="card-title">Export in Progress...</h6>
                                    <div class="progress mb-3">
                                        <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                             role="progressbar" style="width: 0%" id="progressBar"></div>
                                    </div>
                                    <div class="text-center">
                                        <small class="text-muted" id="progressText">Preparing export...</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Export History Modal -->
    <div class="modal fade" id="exportHistoryModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Export History</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Type</th>
                                    <th>Format</th>
                                    <th>Size</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="exportHistoryTable">
                                <tr>
                                    <td colspan="6" class="text-center text-muted">No export history available</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        // Global export configuration
        let exportConfig = {
            includeCharts: true,
            includeSummary: true,
            includeDetails: true,
            branding: true,
            pageOrientation: 'portrait',
            chartStyle: 'professional',
            colorScheme: 'cloverics'
        };

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            loadExportStatistics();
            initializeEventListeners();
        });

        function initializeEventListeners() {
            // Format badge selection
            document.querySelectorAll('.format-badge').forEach(badge => {
                badge.addEventListener('click', function() {
                    const parent = this.closest('.format-selection');
                    parent.querySelectorAll('.format-badge').forEach(b => b.classList.remove('selected'));
                    this.classList.add('selected');
                });
            });

            // Color scheme selection
            document.querySelectorAll('.color-scheme-option').forEach(option => {
                option.addEventListener('click', function() {
                    document.querySelectorAll('.color-scheme-option').forEach(o => o.classList.remove('selected'));
                    this.classList.add('selected');
                    exportConfig.colorScheme = this.dataset.scheme;
                });
            });

            // Configuration checkboxes
            document.getElementById('includeCharts').addEventListener('change', function() {
                exportConfig.includeCharts = this.checked;
            });

            document.getElementById('includeSummary').addEventListener('change', function() {
                exportConfig.includeSummary = this.checked;
            });

            document.getElementById('includeDetails').addEventListener('change', function() {
                exportConfig.includeDetails = this.checked;
            });

            document.getElementById('includeBranding').addEventListener('change', function() {
                exportConfig.branding = this.checked;
            });

            // Configuration selects
            document.getElementById('pageOrientation').addEventListener('change', function() {
                exportConfig.pageOrientation = this.value;
            });

            document.getElementById('chartStyle').addEventListener('change', function() {
                exportConfig.chartStyle = this.value;
            });
        }

        async function loadExportStatistics() {
            try {
                const response = await fetch('/api/export/statistics');
                const data = await response.json();
                
                if (data.success) {
                    const stats = data.statistics;
                    document.getElementById('totalExports').textContent = stats.total_exports || 0;
                    
                    // Find most popular format
                    const formats = stats.exports_by_format || {};
                    const mostPopular = Object.keys(formats).reduce((a, b) => 
                        formats[a] > formats[b] ? a : b, 'PDF');
                    document.getElementById('popularFormat').textContent = mostPopular.toUpperCase();
                    
                    // Last export date
                    if (stats.last_export) {
                        const lastDate = new Date(stats.last_export);
                        document.getElementById('lastExport').textContent = lastDate.toLocaleDateString();
                    }
                }
            } catch (error) {
                console.error('Failed to load export statistics:', error);
            }
        }

        async function exportShipmentReport() {
            const format = getSelectedFormat('shipment');
            await performExport('/api/export/shipment-report', format, 'Shipment Report');
        }

        async function exportAnalyticsDashboard() {
            const format = getSelectedFormat('analytics');
            await performExport('/api/export/analytics-dashboard', format, 'Analytics Dashboard');
        }

        async function exportPerformanceScorecard() {
            const format = getSelectedFormat('performance');
            await performExport('/api/export/performance-scorecard', format, 'Performance Scorecard');
        }

        function getSelectedFormat(exportType) {
            const selection = document.querySelector(`[data-export-type="${exportType}"]`);
            const selected = selection.querySelector('.format-badge.selected');
            return selected.dataset.format;
        }

        async function performExport(endpoint, format, exportName) {
            showExportProgress();
            
            try {
                updateProgress(20, `Preparing ${exportName}...`);
                
                const response = await fetch(endpoint, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `format=${format}`
                });

                updateProgress(60, 'Generating export file...');
                
                const result = await response.json();
                
                if (result.success) {
                    updateProgress(100, 'Export completed!');
                    
                    setTimeout(() => {
                        hideExportProgress();
                        Swal.fire({
                            icon: 'success',
                            title: 'Export Successful!',
                            text: `Your ${exportName} has been generated successfully.`,
                            confirmButtonText: 'Download',
                            showCancelButton: true,
                            cancelButtonText: 'Close'
                        }).then((result) => {
                            if (result.isConfirmed) {
                                // In production, this would trigger the actual download
                                window.open(result.download_url, '_blank');
                            }
                        });
                        
                        loadExportStatistics(); // Refresh stats
                    }, 1000);
                } else {
                    hideExportProgress();
                    Swal.fire({
                        icon: 'error',
                        title: 'Export Failed',
                        text: result.error || 'An error occurred during export.'
                    });
                }
            } catch (error) {
                hideExportProgress();
                Swal.fire({
                    icon: 'error',
                    title: 'Export Error',
                    text: 'Failed to generate export. Please try again.'
                });
            }
        }

        function showExportProgress() {
            document.getElementById('exportProgress').style.display = 'block';
            updateProgress(0, 'Initializing export...');
        }

        function hideExportProgress() {
            document.getElementById('exportProgress').style.display = 'none';
        }

        function updateProgress(percentage, text) {
            document.getElementById('progressBar').style.width = percentage + '%';
            document.getElementById('progressText').textContent = text;
        }

        function refreshExportStats() {
            loadExportStatistics();
            Swal.fire({
                icon: 'success',
                title: 'Refreshed',
                text: 'Export statistics have been updated.',
                timer: 1500,
                showConfirmButton: false
            });
        }

        function showExportHistory() {
            // In production, this would load actual export history
            document.getElementById('exportHistoryTable').innerHTML = `
                <tr>
                    <td>2025-07-03</td>
                    <td>Shipment Report</td>
                    <td>PDF</td>
                    <td>2.4 MB</td>
                    <td><span class="badge bg-success">Completed</span></td>
                    <td>
                        <button class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-download"></i>
                        </button>
                    </td>
                </tr>
                <tr>
                    <td>2025-07-03</td>
                    <td>Analytics Dashboard</td>
                    <td>Excel</td>
                    <td>1.8 MB</td>
                    <td><span class="badge bg-success">Completed</span></td>
                    <td>
                        <button class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-download"></i>
                        </button>
                    </td>
                </tr>
            `;
            
            const modal = new bootstrap.Modal(document.getElementById('exportHistoryModal'));
            modal.show();
        }
    </script>
{% endblock %}