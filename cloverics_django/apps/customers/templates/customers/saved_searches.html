{% extends "base.html" %}
{% load translation_tags %}

{% block title %}Saved Searches & Price Alerts - Cloverics{% endblock %}

{% block extra_css %}
    <style>
        .searches-container {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 20px 0;
        }
        
        .search-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            overflow: hidden;
            transition: transform 0.3s ease;
        }
        
        .search-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .search-header {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            padding: 20px;
            display: flex;
            justify-content: between;
            align-items: center;
        }
        
        .search-content {
            padding: 20px;
        }
        
        .search-criteria {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .criteria-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            padding-bottom: 8px;
            border-bottom: 1px solid #e9ecef;
        }
        
        .criteria-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }
        
        .search-actions {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
            margin-top: 15px;
            flex-wrap: wrap;
        }
        
        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 500;
        }
        
        .status-active {
            background: #d4edda;
            color: #155724;
        }
        
        .status-inactive {
            background: #f8d7da;
            color: #721c24;
        }
        
        .matches-indicator {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            display: inline-flex;
            align-items: center;
            gap: 5px;
        }
        
        .create-search-form {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            .searches-container {
                padding: 15px 0;
            }
            
            .search-card {
                margin-bottom: 15px;
            }
            
            .search-header,
            .search-content {
                padding: 15px;
            }
            
            .create-search-form {
                padding: 20px;
            }
        }

        @media (max-width: 992px) {
            .searches-container {
                padding: 10px 0;
            }
            
            .search-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
            
            .search-actions {
                justify-content: flex-start;
                width: 100%;
            }
            
            .search-actions .btn {
                flex: 1;
                min-width: 120px;
            }
            
            .criteria-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 5px;
            }
        }

        @media (max-width: 768px) {
            .searches-container {
                padding: 5px 0;
            }
            
            .search-card {
                margin-bottom: 10px;
            }
            
            .search-header,
            .search-content {
                padding: 15px;
            }
            
            .create-search-form {
                padding: 15px;
            }
            
            .search-actions {
                flex-direction: column;
                width: 100%;
            }
            
            .search-actions .btn {
                width: 100%;
                margin-bottom: 5px;
            }
            
            .btn-text {
                display: none;
            }
            
            .criteria-item {
                font-size: 0.9rem;
            }
            
            .status-badge {
                font-size: 0.8rem;
                padding: 3px 8px;
            }
            
            .matches-indicator {
                font-size: 0.8rem;
                padding: 6px 12px;
            }
        }

        @media (max-width: 576px) {
            .search-header,
            .search-content {
                padding: 10px;
            }
            
            .create-search-form {
                padding: 10px;
            }
            
            .search-criteria {
                padding: 10px;
                margin: 10px 0;
            }
            
            .criteria-item {
                font-size: 0.85rem;
            }
            
            .status-badge {
                font-size: 0.75rem;
                padding: 2px 6px;
            }
            
            .matches-indicator {
                font-size: 0.75rem;
                padding: 5px 10px;
            }
            
            .search-actions .btn {
                font-size: 0.8rem;
                padding: 0.5rem 0.75rem;
            }
        }

        /* Landscape orientation adjustments for mobile */
        @media (max-width: 768px) and (orientation: landscape) {
            .search-actions {
                flex-direction: row;
                flex-wrap: wrap;
            }
            
            .search-actions .btn {
                width: auto;
                margin-bottom: 0;
                flex: 1;
            }
            
            .btn-text {
                display: inline;
            }
        }
            margin-bottom: 30px;
        }
        
        .form-section {
            margin-bottom: 25px;
        }
        
        .form-section h5 {
            color: #495057;
            margin-bottom: 15px;
            font-weight: 600;
        }
        
        .alert-settings {
            background: #e7f3ff;
            border-left: 4px solid #007bff;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
        }
        
        .matches-container {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .match-item {
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            transition: all 0.3s ease;
        }
        
        .match-item:hover {
            border-color: #007bff;
            background: #f8f9fa;
        }
        
        .match-score {
            display: inline-block;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 4px 10px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .analytics-card {
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            color: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
        }
        
        .analytics-stat {
            text-align: center;
            margin-bottom: 15px;
        }
        
        .analytics-number {
            font-size: 2rem;
            font-weight: bold;
            display: block;
        }
        
        .analytics-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
        
        .price-alert-form {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .loading-spinner {
            text-align: center;
            padding: 40px;
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }
        
        .empty-state i {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.5;
        }
    </style>
{% endblock %}

{% block content %}
<div class="page-container">
    <div class="page-header">
        <h1><i class="fas fa-search me-2"></i>Saved Searches & Price Alerts</h1>
        <p class="text-muted">Manage your shipping searches and get notified of price changes</p>
    </div>

    <!-- Analytics Summary -->
    <div class="row mb-4">
        <div class="col-lg-4">
            <div class="analytics-card">
                <div class="analytics-stat">
                    <span class="analytics-number" id="totalSearches">0</span>
                    <span class="analytics-label">Saved Searches</span>
                </div>
            </div>
        </div>
        <div class="col-lg-4">
            <div class="analytics-card">
                <div class="analytics-stat">
                    <span class="analytics-number" id="activeAlerts">0</span>
                    <span class="analytics-label">Active Alerts</span>
                </div>
            </div>
        </div>
        <div class="col-lg-4">
            <div class="analytics-card">
                <div class="analytics-stat">
                    <span class="analytics-number" id="totalMatches">0</span>
                    <span class="analytics-label">Total Matches</span>
                </div>
            </div>
        </div>
    </div>

            <!-- Create New Search Form -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="create-search-form" id="createSearchForm" style="display: none;">
                        <h4><i class="fas fa-plus-circle me-2"></i>Create New Search</h4>
                        <form id="newSearchForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-section">
                                        <h5>Search Details</h5>
                                        <div class="mb-3">
                                            <label class="form-label">Search Name</label>
                                            <input type="text" class="form-control" name="name" required>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-section">
                                        <h5>Alert Settings</h5>
                                        <div class="mb-3">
                                            <label class="form-label">Alert Frequency</label>
                                            <select class="form-select" name="alert_frequency">
                                                <option value="daily">Daily</option>
                                                <option value="weekly">Weekly</option>
                                                <option value="monthly">Monthly</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="form-section">
                                <h5>Route Information</h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Origin Country</label>
                                            <select class="form-select" name="origin_country" required>
                                                <option value="">Select Origin Country</option>
                                            </select>
                                        </div>
                                        <div class="mb-3" id="originStateDiv" style="display: none;">
                                            <label class="form-label">Origin State</label>
                                            <select class="form-select" name="origin_state">
                                                <option value="">Select State</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Destination Country</label>
                                            <select class="form-select" name="destination_country" required>
                                                <option value="">Select Destination Country</option>
                                            </select>
                                        </div>
                                        <div class="mb-3" id="destinationStateDiv" style="display: none;">
                                            <label class="form-label">Destination State</label>
                                            <select class="form-select" name="destination_state">
                                                <option value="">Select State</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="form-section">
                                <h5>Shipping Preferences</h5>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">Transport Type</label>
                                            <select class="form-select" name="transport_type">
                                                <option value="">Any Transport</option>
                                                <option value="truck">Truck</option>
                                                <option value="ship">Ship</option>
                                                <option value="air">Air</option>
                                                <option value="rail">Rail</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">Cargo Type</label>
                                            <select class="form-select" name="cargo_type">
                                                <option value="">Any Cargo Type</option>
                                                <option value="General">General Cargo</option>
                                                <option value="Electronics">Electronics</option>
                                                <option value="Food">Food Products</option>
                                                <option value="Chemicals">Chemicals</option>
                                                <option value="Machinery">Machinery</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">Max Transit Days</label>
                                            <input type="number" class="form-control" name="max_transit_days" min="1" max="90">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="form-section">
                                <h5>Weight & Pricing</h5>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">Min Weight (kg)</label>
                                            <input type="number" class="form-control" name="weight_min" min="0.1" step="0.1">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">Max Weight (kg)</label>
                                            <input type="number" class="form-control" name="weight_max" min="0.1" step="0.1">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">Max Price per kg ($)</label>
                                            <input type="number" class="form-control" name="max_price" min="0.01" step="0.01">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="alert-settings">
                                <h5><i class="fas fa-bell me-2"></i>Price Alert Options</h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="enablePriceAlert">
                                            <label class="form-check-label" for="enablePriceAlert">
                                                Enable price drop alerts
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="enableNewRouteAlert">
                                            <label class="form-check-label" for="enableNewRouteAlert">
                                                Alert for new routes
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="d-flex justify-content-end gap-2 mt-4">
                                <button type="button" class="btn btn-secondary" onclick="cancelCreateSearch()">Cancel</button>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i>Create Search
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="d-flex gap-2 flex-wrap">
                        <button class="btn btn-primary" onclick="showCreateSearchForm()">
                            <i class="fas fa-plus me-1"></i>Create New Search
                        </button>
                        <button class="btn btn-outline-success" onclick="checkPriceAlerts()">
                            <i class="fas fa-bell me-1"></i>Check Price Alerts
                        </button>
                        <button class="btn btn-outline-info" onclick="loadAnalytics()">
                            <i class="fas fa-chart-bar me-1"></i>Refresh Analytics
                        </button>
                    </div>
                </div>
            </div>

            <!-- Saved Searches List -->
            <div class="row">
                <div class="col-12">
                    <div id="savedSearchesList">
                        <div class="loading-spinner">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading saved searches...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Search Matches Modal -->
            <div class="modal fade" id="matchesModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Search Matches</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div id="matchesContent">
                                <div class="loading-spinner">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Finding matches...</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block extra_js %}
    <script>
        let savedSearches = [];
        let currentAnalytics = {};

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            loadCountries();
            loadSavedSearches();
            loadAnalytics();
        });

        // Load countries
        async function loadCountries() {
            try {
                const response = await fetch('/api/countries');
                const countries = await response.json();
                
                const originSelect = document.querySelector('select[name="origin_country"]');
                const destinationSelect = document.querySelector('select[name="destination_country"]');
                
                countries.forEach(country => {
                    const option1 = new Option(country, country);
                    const option2 = new Option(country, country);
                    originSelect.add(option1);
                    destinationSelect.add(option2);
                });
            } catch (error) {
                console.error('Error loading countries:', error);
            }
        }

        // Load saved searches
        async function loadSavedSearches() {
            try {
                const response = await fetch('/api/saved-searches/list');
                const data = await response.json();
                
                if (data.success) {
                    savedSearches = data.searches;
                    displaySavedSearches();
                } else {
                    showEmptyState();
                }
            } catch (error) {
                console.error('Error loading saved searches:', error);
                showEmptyState();
            }
        }

        // Display saved searches
        function displaySavedSearches() {
            const container = document.getElementById('savedSearchesList');
            
            if (savedSearches.length === 0) {
                showEmptyState();
                return;
            }
            
            let html = '';
            savedSearches.forEach(search => {
                html += createSearchCard(search);
            });
            
            container.innerHTML = html;
        }

        // Create search card HTML
        function createSearchCard(search) {
            const isActive = search.is_active;
            const statusClass = isActive ? 'status-active' : 'status-inactive';
            const statusText = isActive ? 'Active' : 'Inactive';
            
            return `
                <div class="search-card">
                    <div class="search-header">
                        <div>
                            <h5 class="mb-1">${search.name}</h5>
                            <span class="status-badge ${statusClass}">${statusText}</span>
                        </div>
                        <div class="matches-indicator">
                            <i class="fas fa-route"></i>
                            ${search.match_count || 0} matches
                        </div>
                    </div>
                    <div class="search-content">
                        <div class="search-criteria">
                            <div class="criteria-item">
                                <span><i class="fas fa-map-marker-alt me-1"></i>Route:</span>
                                <span>${search.criteria.origin_country} → ${search.criteria.destination_country}</span>
                            </div>
                            ${search.criteria.transport_type ? `
                            <div class="criteria-item">
                                <span><i class="fas fa-truck me-1"></i>Transport:</span>
                                <span>${search.criteria.transport_type}</span>
                            </div>` : ''}
                            ${search.criteria.max_price ? `
                            <div class="criteria-item">
                                <span><i class="fas fa-dollar-sign me-1"></i>Max Price:</span>
                                <span>$${search.criteria.max_price}/kg</span>
                            </div>` : ''}
                            <div class="criteria-item">
                                <span><i class="fas fa-bell me-1"></i>Alert Frequency:</span>
                                <span>${search.alert_frequency}</span>
                            </div>
                        </div>
                        <div class="search-actions">
                            <button class="btn btn-outline-primary btn-sm" onclick="findMatches('${search.search_id}')">
                                <i class="fas fa-search me-1"></i>Find Matches
                            </button>
                            <button class="btn btn-outline-warning btn-sm" onclick="toggleSearchStatus('${search.search_id}', ${!isActive})">
                                <i class="fas fa-${isActive ? 'pause' : 'play'} me-1"></i>${isActive ? 'Deactivate' : 'Activate'}
                            </button>
                            <button class="btn btn-outline-danger btn-sm" onclick="deleteSearch('${search.search_id}')">
                                <i class="fas fa-trash me-1"></i>Delete
                            </button>
                        </div>
                    </div>
                </div>
            `;
        }

        // Show empty state
        function showEmptyState() {
            const container = document.getElementById('savedSearchesList');
            container.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-search"></i>
                    <h4>No Saved Searches</h4>
                    <p>Create your first saved search to get started with price monitoring and alerts.</p>
                    <button class="btn btn-primary" onclick="showCreateSearchForm()">
                        <i class="fas fa-plus me-1"></i>Create Your First Search
                    </button>
                </div>
            `;
        }

        // Show create search form
        function showCreateSearchForm() {
            document.getElementById('createSearchForm').style.display = 'block';
            document.querySelector('input[name="name"]').focus();
        }

        // Cancel create search
        function cancelCreateSearch() {
            document.getElementById('createSearchForm').style.display = 'none';
            document.getElementById('newSearchForm').reset();
        }

        // Handle form submission
        document.getElementById('newSearchForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            
            try {
                const response = await fetch('/api/saved-searches/create', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (result.success) {
                    Swal.fire('Success!', 'Saved search created successfully', 'success');
                    cancelCreateSearch();
                    loadSavedSearches();
                    loadAnalytics();
                } else {
                    Swal.fire('Error', result.error, 'error');
                }
            } catch (error) {
                Swal.fire('Error', 'Failed to create saved search', 'error');
            }
        });

        // Find matches for search
        async function findMatches(searchId) {
            const modal = new bootstrap.Modal(document.getElementById('matchesModal'));
            modal.show();
            
            try {
                const response = await fetch(`/api/saved-searches/${searchId}/find-matches`, {
                    method: 'POST'
                });
                const result = await response.json();
                
                if (result.success) {
                    displayMatches(result.matches, result.search_name);
                } else {
                    document.getElementById('matchesContent').innerHTML = `
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            No matches found for this search criteria.
                        </div>
                    `;
                }
            } catch (error) {
                document.getElementById('matchesContent').innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        Error finding matches: ${error.message}
                    </div>
                `;
            }
        }

        // Display matches
        function displayMatches(matches, searchName) {
            const container = document.getElementById('matchesContent');
            
            if (matches.length === 0) {
                container.innerHTML = `
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        No routes currently match your search criteria for "${searchName}".
                    </div>
                `;
                return;
            }
            
            let html = `<h6>Found ${matches.length} matching routes for "${searchName}":</h6>`;
            
            matches.forEach(match => {
                html += `
                    <div class="match-item">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <h6>${match.provider_name}</h6>
                                <p class="mb-1"><strong>Route:</strong> ${match.origin} → ${match.destination}</p>
                                <p class="mb-1"><strong>Transport:</strong> ${match.transport_type}</p>
                                <p class="mb-1"><strong>Price:</strong> $${match.base_price}/kg</p>
                                <p class="mb-0"><strong>Estimated Days:</strong> ${match.estimated_days}</p>
                            </div>
                            <div class="text-end">
                                <div class="match-score">${Math.round(match.match_score * 100)}% match</div>
                                <button class="btn btn-primary btn-sm mt-2" onclick="requestQuote(${match.id})">
                                    Request Quote
                                </button>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            container.innerHTML = html;
        }

        // Toggle search status
        async function toggleSearchStatus(searchId, newStatus) {
            try {
                const formData = new FormData();
                formData.append('is_active', newStatus);
                
                const response = await fetch(`/api/saved-searches/${searchId}`, {
                    method: 'PUT',
                    body: formData
                });
                
                const result = await response.json();
                
                if (result.success) {
                    loadSavedSearches();
                    Swal.fire('Success!', `Search ${newStatus ? 'activated' : 'deactivated'} successfully`, 'success');
                } else {
                    Swal.fire('Error', result.error, 'error');
                }
            } catch (error) {
                Swal.fire('Error', 'Failed to update search status', 'error');
            }
        }

        // Delete search
        async function deleteSearch(searchId) {
            const result = await Swal.fire({
                title: 'Delete Search?',
                text: 'This action cannot be undone.',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#3085d6',
                confirmButtonText: 'Yes, delete it!'
            });
            
            if (result.isConfirmed) {
                try {
                    const response = await fetch(`/api/saved-searches/${searchId}`, {
                        method: 'DELETE'
                    });
                    
                    const data = await response.json();
                    
                    if (data.success) {
                        loadSavedSearches();
                        loadAnalytics();
                        Swal.fire('Deleted!', 'Search has been deleted.', 'success');
                    } else {
                        Swal.fire('Error', data.error, 'error');
                    }
                } catch (error) {
                    Swal.fire('Error', 'Failed to delete search', 'error');
                }
            }
        }

        // Check price alerts
        async function checkPriceAlerts() {
            try {
                const response = await fetch('/api/price-alerts/check');
                const result = await response.json();
                
                if (result.success) {
                    const alertCount = result.total_triggered;
                    Swal.fire(
                        'Price Alerts Checked',
                        `${alertCount} alert(s) triggered. Check your notifications for details.`,
                        alertCount > 0 ? 'info' : 'success'
                    );
                } else {
                    Swal.fire('Error', result.error, 'error');
                }
            } catch (error) {
                Swal.fire('Error', 'Failed to check price alerts', 'error');
            }
        }

        // Load analytics
        async function loadAnalytics() {
            try {
                const response = await fetch('/api/search-analytics');
                const result = await response.json();
                
                if (result.success) {
                    currentAnalytics = result.analytics;
                    updateAnalyticsDisplay();
                }
            } catch (error) {
                console.error('Error loading analytics:', error);
            }
        }

        // Update analytics display
        function updateAnalyticsDisplay() {
            document.getElementById('totalSearches').textContent = currentAnalytics.total_saved_searches || 0;
            document.getElementById('activeAlerts').textContent = currentAnalytics.total_price_alerts || 0;
            document.getElementById('totalMatches').textContent = currentAnalytics.total_matches || 0;
        }

        // Request quote (redirect to quote request page)
        function requestQuote(routeId) {
            window.location.href = `/customer/request-quote?route_id=${routeId}`;
        }

        // Handle country selection for states
        document.addEventListener('change', function(e) {
            if (e.target.name === 'origin_country') {
                toggleStateSelection('origin', e.target.value);
            } else if (e.target.name === 'destination_country') {
                toggleStateSelection('destination', e.target.value);
            }
        });

        // Toggle state selection based on country
        function toggleStateSelection(type, country) {
            const stateDiv = document.getElementById(`${type}StateDiv`);
            const stateSelect = document.querySelector(`select[name="${type}_state"]`);
            
            if (country === 'United States') {
                stateDiv.style.display = 'block';
                loadStates(stateSelect);
            } else {
                stateDiv.style.display = 'none';
                stateSelect.value = '';
            }
        }

        // Load US states
        function loadStates(selectElement) {
            const states = [
                'Alabama', 'Alaska', 'Arizona', 'Arkansas', 'California', 'Colorado', 'Connecticut',
                'Delaware', 'Florida', 'Georgia', 'Hawaii', 'Idaho', 'Illinois', 'Indiana', 'Iowa',
                'Kansas', 'Kentucky', 'Louisiana', 'Maine', 'Maryland', 'Massachusetts', 'Michigan',
                'Minnesota', 'Mississippi', 'Missouri', 'Montana', 'Nebraska', 'Nevada', 'New Hampshire',
                'New Jersey', 'New Mexico', 'New York', 'North Carolina', 'North Dakota', 'Ohio',
                'Oklahoma', 'Oregon', 'Pennsylvania', 'Rhode Island', 'South Carolina', 'South Dakota',
                'Tennessee', 'Texas', 'Utah', 'Vermont', 'Virginia', 'Washington', 'West Virginia',
                'Wisconsin', 'Wyoming'
            ];
            
            selectElement.innerHTML = '<option value="">Select State</option>';
            states.forEach(state => {
                selectElement.add(new Option(state, state));
            });
        }
    </script>
{% endblock %}