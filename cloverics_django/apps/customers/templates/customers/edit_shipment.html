{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2><i class="fas fa-edit"></i> Edit Shipment</h2>
                        <p class="text-muted">Tracking Number: {{ shipment.tracking_number }}</p>
                    </div>
                    <div>
                        <a href="/customer/manage-shipments" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Shipments
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-edit"></i> Edit Shipment Details</h5>
                </div>
                <div class="card-body">
                    <form method="post" action="/customer/update-shipment/{{ shipment.id }}">
                        <div class="row">
                            <div class="col-lg-6 col-md-6 col-sm-12 mb-3">
                                <div class="form-group">
                                    <label for="origin_country">Origin Country</label>
                                    <input type="text" class="form-control" id="origin_country" name="origin_country" 
                                           value="{{ shipment.origin_country }}" readonly>
                                    <small class="text-muted">Cannot be changed after shipment creation</small>
                                </div>
                            </div>
                            <div class="col-lg-6 col-md-6 col-sm-12 mb-3">
                                <div class="form-group">
                                    <label for="origin_city">Origin City</label>
                                    <input type="text" class="form-control" id="origin_city" name="origin_city" 
                                           value="{{ shipment.origin_city }}" readonly>
                                    <small class="text-muted">Cannot be changed after shipment creation</small>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-lg-6 col-md-6 col-sm-12 mb-3">
                                <div class="form-group">
                                    <label for="destination_country">Destination Country</label>
                                    <input type="text" class="form-control" id="destination_country" name="destination_country" 
                                           value="{{ shipment.destination_country }}" readonly>
                                    <small class="text-muted">Cannot be changed after shipment creation</small>
                                </div>
                            </div>
                            <div class="col-lg-6 col-md-6 col-sm-12 mb-3">
                                <div class="form-group">
                                    <label for="destination_city">Destination City</label>
                                    <input type="text" class="form-control" id="destination_city" name="destination_city" 
                                           value="{{ shipment.destination_city }}" readonly>
                                    <small class="text-muted">Cannot be changed after shipment creation</small>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-lg-6 col-md-6 col-sm-12 mb-3">
                                <div class="form-group">
                                    <label for="weight_kg">Weight (kg) <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control" id="weight_kg" name="weight_kg" 
                                           value="{{ shipment.weight_kg }}" min="0.1" step="0.1" required>
                                    <small class="text-muted">You can adjust the weight of your cargo</small>
                                </div>
                            </div>
                            <div class="col-lg-6 col-md-6 col-sm-12 mb-3">
                                <div class="form-group">
                                    <label for="cargo_type">Cargo Type</label>
                                    <select class="form-control" id="cargo_type" name="cargo_type">
                                        <option value="GENERAL" {% if shipment.cargo_type == 'GENERAL' %}selected{% endif %}>General Cargo</option>
                                        <option value="FRAGILE" {% if shipment.cargo_type == 'FRAGILE' %}selected{% endif %}>Fragile</option>
                                        <option value="HAZARDOUS" {% if shipment.cargo_type == 'HAZARDOUS' %}selected{% endif %}>Hazardous</option>
                                        <option value="PERISHABLE" {% if shipment.cargo_type == 'PERISHABLE' %}selected{% endif %}>Perishable</option>
                                        <option value="LIQUID" {% if shipment.cargo_type == 'LIQUID' %}selected{% endif %}>Liquid</option>
                                        <option value="ELECTRONICS" {% if shipment.cargo_type == 'ELECTRONICS' %}selected{% endif %}>Electronics</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="description">Special Instructions / Description</label>
                            <textarea class="form-control" id="description" name="description" rows="4" 
                                      placeholder="Enter any special handling instructions or cargo description...">{{ shipment.description }}</textarea>
                            <small class="text-muted">Provide additional details about your shipment</small>
                        </div>

                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            <strong>Note:</strong> Origin and destination cannot be changed once the shipment is created. 
                            Only weight, cargo type, and description can be modified for {{ shipment.status_display }} shipments.
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Save Changes
                            </button>
                            <a href="/customer/shipment-details/{{ shipment.id }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.form-group {
    margin-bottom: 1.5rem;
}

.form-actions {
    padding-top: 2rem;
    border-top: 1px solid #dee2e6;
    margin-top: 2rem;
}

.form-actions .btn {
    margin-right: 0.5rem;
}

input[readonly] {
    background-color: #f8f9fa;
    cursor: not-allowed;
}

.text-danger {
    color: #dc3545 !important;
}

.alert-info {
    background-color: #e8f4f8;
    border-color: #bee5eb;
    color: #0c5460;
}
</style>

<script>
// Form validation
document.querySelector('form').addEventListener('submit', function(e) {
    const weight = document.getElementById('weight_kg').value;
    
    if (!weight || weight <= 0) {
        e.preventDefault();
        alert('Please enter a valid weight greater than 0');
        return false;
    }
    
    if (weight > 10000) {
        e.preventDefault();
        alert('Weight cannot exceed 10,000 kg. Please contact support for oversized shipments.');
        return false;
    }
    
    return true;
});

// Auto-save draft functionality
let saveTimeout;
function autoSave() {
    clearTimeout(saveTimeout);
    saveTimeout = setTimeout(() => {
        const formData = new FormData(document.querySelector('form'));
        // Could implement auto-save to draft here
        console.log('Auto-saving draft...');
    }, 2000);
}

document.querySelectorAll('input, textarea, select').forEach(element => {
    element.addEventListener('input', autoSave);
});
</script>
{% endblock %}

{% block extra_css %}
<style>
/* Responsive Design */
@media (max-width: 1200px) {
    .container-fluid {
        padding: 1rem;
    }
    
    .card {
        margin-bottom: 1.5rem;
    }
}

@media (max-width: 992px) {
    .container-fluid {
        padding: 0.75rem;
    }
    
    .page-header .d-flex {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .card {
        margin-bottom: 1rem;
    }
}

@media (max-width: 768px) {
    .container-fluid {
        padding: 0.5rem;
    }
    
    h2 {
        font-size: 1.5rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .form-group {
        margin-bottom: 1rem;
    }
    
    .form-label {
        font-size: 0.9rem;
    }
    
    .form-control,
    .form-select {
        font-size: 0.9rem;
        padding: 0.5rem 0.75rem;
    }
    
    .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }
}

@media (max-width: 576px) {
    .container-fluid {
        padding: 0.25rem;
    }
    
    h2 {
        font-size: 1.25rem;
    }
    
    .card-body {
        padding: 0.75rem;
    }
    
    .form-label {
        font-size: 0.85rem;
    }
    
    .form-control,
    .form-select {
        font-size: 0.85rem;
        padding: 0.5rem;
    }
    
    .btn {
        font-size: 0.9rem;
        padding: 0.75rem 1rem;
    }
    
    .alert {
        font-size: 0.85rem;
        padding: 0.75rem;
    }
}
</style>
{% endblock %}