{% extends "base.html" %}
{% load translation_tags %}

{% block title %}Contract Lifecycle Management - Cloverics{% endblock %}

{% block extra_css %}
<style>
    .contract-card {
        border: 1px solid #e5e5e5;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
        background: white;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
    }
    
    .contract-card:hover {
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }
    
    .contract-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 1px solid #e5e5e5;
    }
    
    .contract-status {
        padding: 5px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 600;
        text-transform: uppercase;
    }
    
    .status-draft { background-color: #f8f9fa; color: #6c757d; }
    .status-pending-review { background-color: #fff3cd; color: #856404; }
    .status-under-negotiation { background-color: #d4edda; color: #155724; }
    .status-approved { background-color: #d1ecf1; color: #0c5460; }
    .status-signed { background-color: #d4edda; color: #155724; }
    .status-active { background-color: #d1ecf1; color: #0c5460; }
    .status-completed { background-color: #e2e3e5; color: #383d41; }
    .status-terminated { background-color: #f5c6cb; color: #721c24; }
    
    .compliance-score {
        padding: 4px 8px;
        border-radius: 15px;
        font-size: 11px;
        font-weight: 600;
        color: white;
    }
    
    .compliance-excellent { background-color: #28a745; }
    .compliance-good { background-color: #17a2b8; }
    .compliance-fair { background-color: #ffc107; color: #212529; }
    .compliance-poor { background-color: #dc3545; }
    
    .analytics-overview {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 20px;
    }
    
    .analytics-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 15px;
        margin-top: 15px;
    }

    /* Responsive Design */
    @media (max-width: 1200px) {
        .contract-card {
            padding: 15px;
            margin-bottom: 15px;
        }
        
        .analytics-overview {
            padding: 15px;
        }
        
        .analytics-grid {
            gap: 10px;
        }
        
        .analytics-item {
            padding: 10px;
        }
    }

    @media (max-width: 992px) {
        .contract-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 10px;
        }
        
        .analytics-grid {
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 8px;
        }
        
        .analytics-number {
            font-size: 20px;
        }
        
        .analytics-label {
            font-size: 11px;
        }
    }

    @media (max-width: 768px) {
        .contract-card {
            padding: 10px;
            margin-bottom: 10px;
        }
        
        .analytics-overview {
            padding: 10px;
        }
        
        .analytics-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: 5px;
        }
        
        .analytics-item {
            padding: 8px;
        }
        
        .analytics-number {
            font-size: 18px;
        }
        
        .analytics-label {
            font-size: 10px;
        }
        
        .contract-status {
            font-size: 10px;
            padding: 3px 8px;
        }
        
        .compliance-score {
            font-size: 10px;
            padding: 2px 6px;
        }
    }

    @media (max-width: 576px) {
        .contract-card {
            padding: 8px;
            margin-bottom: 8px;
        }
        
        .analytics-overview {
            padding: 8px;
        }
        
        .analytics-grid {
            grid-template-columns: 1fr;
            gap: 5px;
        }
        
        .analytics-item {
            padding: 6px;
        }
        
        .analytics-number {
            font-size: 16px;
        }
        
        .analytics-label {
            font-size: 9px;
        }
        
        .contract-status {
            font-size: 9px;
            padding: 2px 6px;
        }
        
        .compliance-score {
            font-size: 9px;
            padding: 1px 4px;
        }
    }
    
    .analytics-item {
        text-align: center;
        padding: 15px;
        background: rgba(255,255,255,0.1);
        border-radius: 8px;
    }
    
    .analytics-number {
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 5px;
    }
    
    .analytics-label {
        font-size: 12px;
        opacity: 0.9;
    }
    
    .workflow-progress {
        margin: 10px 0;
    }
    
    .progress-bar {
        width: 100%;
        height: 6px;
        background: #e9ecef;
        border-radius: 3px;
        overflow: hidden;
    }
    
    .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, #28a745, #20c997);
        transition: width 0.3s ease;
    }
    
    .clause-section {
        background: #f8f9fa;
        border-radius: 6px;
        padding: 15px;
        margin: 10px 0;
    }
    
    .clause-title {
        font-weight: 600;
        color: #495057;
        margin-bottom: 8px;
    }
    
    .clause-content {
        font-size: 14px;
        line-height: 1.5;
        color: #6c757d;
    }
    
    .compliance-check {
        background: white;
        border: 1px solid #e5e5e5;
        border-radius: 6px;
        padding: 15px;
        margin: 10px 0;
    }
    
    .compliance-critical { border-left: 4px solid #dc3545; }
    .compliance-high { border-left: 4px solid #fd7e14; }
    .compliance-medium { border-left: 4px solid #ffc107; }
    .compliance-low { border-left: 4px solid #28a745; }
    
    .action-buttons {
        display: flex;
        gap: 8px;
        flex-wrap: wrap;
        margin-top: 15px;
    }
    
    .btn-contract {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        color: white;
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 600;
    }
    
    .btn-contract:hover {
        background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
        color: white;
    }
    
    .filter-section {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        margin-bottom: 20px;
    }
    
    .contract-form {
        background: white;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin-bottom: 20px;
    }
    
    .tab-content {
        padding: 20px 0;
    }
    
    .workflow-step {
        padding: 10px;
        border-radius: 6px;
        margin: 5px 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .step-pending { background-color: #fff3cd; }
    .step-approved { background-color: #d4edda; }
    .step-rejected { background-color: #f8d7da; }
    .step-current { background-color: #d1ecf1; }
    
    .compliance-summary {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin: 15px 0;
    }
    
    .compliance-item {
        background: white;
        border-radius: 6px;
        padding: 15px;
        text-align: center;
        border: 1px solid #e5e5e5;
    }
    
    .modal-body {
        max-height: 500px;
        overflow-y: auto;
    }
    
    .contract-type-card {
        border: 2px solid #e5e5e5;
        border-radius: 8px;
        padding: 15px;
        margin: 10px 0;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .contract-type-card:hover,
    .contract-type-card.selected {
        border-color: #667eea;
        background-color: #f8f9ff;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <h2><i class="fas fa-file-contract"></i> Contract Lifecycle Management</h2>
                <p>Automated contract generation, approval workflows, and compliance tracking</p>
            </div>
        </div>
    </div>

    <!-- Analytics Overview -->
    <div class="row">
        <div class="col-12">
            <div class="analytics-overview">
                <h4><i class="fas fa-chart-line"></i> Contract Analytics Overview</h4>
                <div class="analytics-grid" id="analyticsGrid">
                    <div class="analytics-item">
                        <div class="analytics-number" id="totalContracts">0</div>
                        <div class="analytics-label">Total Contracts</div>
                    </div>
                    <div class="analytics-item">
                        <div class="analytics-number" id="activeContracts">0</div>
                        <div class="analytics-label">Active Contracts</div>
                    </div>
                    <div class="analytics-item">
                        <div class="analytics-number" id="pendingContracts">0</div>
                        <div class="analytics-label">Pending Approval</div>
                    </div>
                    <div class="analytics-item">
                        <div class="analytics-number" id="avgCompliance">0%</div>
                        <div class="analytics-label">Avg Compliance</div>
                    </div>
                    <div class="analytics-item">
                        <div class="analytics-number" id="contractVelocity">0</div>
                        <div class="analytics-label">Contracts/Week</div>
                    </div>
                    <div class="analytics-item">
                        <div class="analytics-number" id="avgApprovalTime">0</div>
                        <div class="analytics-label">Avg Approval Time</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Interface -->
    <div class="row">
        <div class="col-12">
            <div class="contract-card">
                <!-- Tab Navigation -->
                <ul class="nav nav-tabs" id="contractTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="contracts-tab" data-bs-toggle="tab" data-bs-target="#contracts" type="button" role="tab">
                            <i class="fas fa-list"></i> My Contracts
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="create-tab" data-bs-toggle="tab" data-bs-target="#create" type="button" role="tab">
                            <i class="fas fa-plus"></i> Create Contract
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="workflows-tab" data-bs-toggle="tab" data-bs-target="#workflows" type="button" role="tab">
                            <i class="fas fa-tasks"></i> Approval Workflows
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="compliance-tab" data-bs-toggle="tab" data-bs-target="#compliance" type="button" role="tab">
                            <i class="fas fa-shield-alt"></i> Compliance Center
                        </button>
                    </li>
                </ul>

                <div class="tab-content" id="contractTabContent">
                    <!-- Contracts List Tab -->
                    <div class="tab-pane fade show active" id="contracts" role="tabpanel">
                        <!-- Filter Section -->
                        <div class="filter-section">
                            <div class="row">
                                <div class="col-md-3">
                                    <label for="statusFilter" class="form-label">Status Filter</label>
                                    <select class="form-select" id="statusFilter">
                                        <option value="">All Statuses</option>
                                        <option value="draft">Draft</option>
                                        <option value="pending_review">Pending Review</option>
                                        <option value="approved">Approved</option>
                                        <option value="active">Active</option>
                                        <option value="completed">Completed</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label for="typeFilter" class="form-label">Contract Type</label>
                                    <select class="form-select" id="typeFilter">
                                        <option value="">All Types</option>
                                        <option value="logistics_service">Logistics Service</option>
                                        <option value="master_service">Master Service</option>
                                        <option value="freight_forwarding">Freight Forwarding</option>
                                        <option value="warehousing">Warehousing</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">&nbsp;</label>
                                    <button class="btn btn-primary d-block" onclick="applyFilters()">
                                        <i class="fas fa-filter"></i> Apply
                                    </button>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">&nbsp;</label>
                                    <button class="btn btn-outline-secondary d-block" onclick="refreshContracts()">
                                        <i class="fas fa-refresh"></i> Refresh
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Contracts List -->
                        <div id="contractsList">
                            <div class="text-center p-4">
                                <i class="fas fa-spinner fa-spin fa-2x text-muted"></i>
                                <p class="mt-2 text-muted">Loading contracts...</p>
                            </div>
                        </div>
                    </div>

                    <!-- Create Contract Tab -->
                    <div class="tab-pane fade" id="create" role="tabpanel">
                        <div class="contract-form">
                            <h5><i class="fas fa-plus-circle"></i> Create New Contract</h5>
                            <form id="createContractForm">
                                <div class="row">
                                    <div class="col-12">
                                        <h6>Contract Type Selection</h6>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="contract-type-card" data-type="logistics_service">
                                                    <h6><i class="fas fa-truck"></i> Logistics Service Agreement</h6>
                                                    <p class="small text-muted">Standard logistics services including transportation, warehousing, and freight forwarding</p>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="contract-type-card" data-type="master_service">
                                                    <h6><i class="fas fa-handshake"></i> Master Service Agreement</h6>
                                                    <p class="small text-muted">Comprehensive framework agreement for ongoing logistics partnerships</p>
                                                </div>
                                            </div>
                                        </div>
                                        <input type="hidden" id="contractType" name="contract_type" value="">
                                    </div>
                                </div>

                                <div class="row mt-4">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="providerCompany" class="form-label">Provider Company *</label>
                                            <input type="text" class="form-control" id="providerCompany" name="provider_company" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="providerContact" class="form-label">Provider Contact Person</label>
                                            <input type="text" class="form-control" id="providerContact" name="provider_contact">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="serviceType" class="form-label">Service Type *</label>
                                            <select class="form-select" id="serviceType" name="service_type" required>
                                                <option value="">Select service type</option>
                                                <option value="Standard Logistics">Standard Logistics</option>
                                                <option value="Express Delivery">Express Delivery</option>
                                                <option value="Freight Forwarding">Freight Forwarding</option>
                                                <option value="Warehousing">Warehousing</option>
                                                <option value="Last Mile Delivery">Last Mile Delivery</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="contractDuration" class="form-label">Contract Duration</label>
                                            <select class="form-select" id="contractDuration" name="contract_duration">
                                                <option value="6 months">6 months</option>
                                                <option value="12 months" selected>12 months</option>
                                                <option value="24 months">24 months</option>
                                                <option value="36 months">36 months</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="startDate" class="form-label">Contract Start Date</label>
                                            <input type="date" class="form-control" id="startDate" name="start_date">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="liabilityLimit" class="form-label">Liability Limit</label>
                                            <select class="form-select" id="liabilityLimit" name="liability_limit">
                                                <option value="$50,000">$50,000</option>
                                                <option value="$100,000" selected>$100,000</option>
                                                <option value="$250,000">$250,000</option>
                                                <option value="$500,000">$500,000</option>
                                                <option value="$1,000,000">$1,000,000</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="serviceDescription" class="form-label">Service Description</label>
                                    <textarea class="form-control" id="serviceDescription" name="service_description" rows="3" placeholder="Describe the specific services to be provided..."></textarea>
                                </div>

                                <div class="text-end">
                                    <button type="button" class="btn btn-outline-secondary me-2" onclick="resetContractForm()">
                                        <i class="fas fa-undo"></i> Reset
                                    </button>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-magic"></i> Generate Contract
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Approval Workflows Tab -->
                    <div class="tab-pane fade" id="workflows" role="tabpanel">
                        <div id="workflowsList">
                            <div class="text-center p-4">
                                <i class="fas fa-spinner fa-spin fa-2x text-muted"></i>
                                <p class="mt-2 text-muted">Loading approval workflows...</p>
                            </div>
                        </div>
                    </div>

                    <!-- Compliance Center Tab -->
                    <div class="tab-pane fade" id="compliance" role="tabpanel">
                        <div id="complianceCenter">
                            <div class="text-center p-4">
                                <i class="fas fa-spinner fa-spin fa-2x text-muted"></i>
                                <p class="mt-2 text-muted">Loading compliance information...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Contract Details Modal -->
<div class="modal fade" id="contractDetailsModal" tabindex="-1" aria-labelledby="contractDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="contractDetailsModalLabel">Contract Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="contractDetailsContent">
                <!-- Content loaded dynamically -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="contractActionBtn" style="display: none;" onclick="performContractAction(this)">Action</button>
            </div>
        </div>
    </div>
</div>

<!-- Approval Workflow Modal -->
<div class="modal fade" id="approvalModal" tabindex="-1" aria-labelledby="approvalModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="approvalModalLabel">Submit for Approval</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="approvalForm">
                    <input type="hidden" id="approvalContractId" name="contract_id">
                    <div class="mb-3">
                        <label for="workflowType" class="form-label">Approval Workflow Type</label>
                        <select class="form-select" id="workflowType" name="workflow_type">
                            <option value="standard">Standard (3 steps: Legal → Operations → Finance)</option>
                            <option value="expedited">Expedited (2 steps: Operations → Legal)</option>
                            <option value="high_value">High Value (4 steps: Legal → Operations → Finance → Executive)</option>
                            <option value="master_agreement">Master Agreement (5 steps: Legal → Compliance → Operations → Finance → Executive)</option>
                        </select>
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        <strong>Note:</strong> Once submitted, the contract will enter the approval workflow and cannot be edited until the process is complete.
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="submitForApproval()">Submit for Approval</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentContracts = [];
let currentAnalytics = {};

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    loadAnalytics();
    loadContracts();
    
    // Form submission
    document.getElementById('createContractForm').addEventListener('submit', function(e) {
        e.preventDefault();
        createContract();
    });
    
    // Contract type selection
    document.querySelectorAll('.contract-type-card').forEach(card => {
        card.addEventListener('click', function() {
            document.querySelectorAll('.contract-type-card').forEach(c => c.classList.remove('selected'));
            this.classList.add('selected');
            document.getElementById('contractType').value = this.dataset.type;
        });
    });
    
    // Set default start date to today
    document.getElementById('startDate').value = new Date().toISOString().split('T')[0];
});

// Load analytics data
async function loadAnalytics() {
    try {
        const response = await fetch('/api/contract/analytics');
        const data = await response.json();
        
        if (data.success) {
            currentAnalytics = data.analytics;
            updateAnalyticsDisplay(data.analytics);
        }
    } catch (error) {
        console.error('Error loading analytics:', error);
    }
}

// Update analytics display
function updateAnalyticsDisplay(analytics) {
    document.getElementById('totalContracts').textContent = analytics.total_contracts || 0;
    document.getElementById('activeContracts').textContent = analytics.active_contracts || 0;
    document.getElementById('pendingContracts').textContent = analytics.pending_contracts || 0;
    document.getElementById('avgCompliance').textContent = (analytics.average_compliance_score || 0) + '%';
    document.getElementById('contractVelocity').textContent = analytics.contract_velocity?.contracts_per_week || 0;
    document.getElementById('avgApprovalTime').textContent = analytics.approval_time_avg || '0 days';
}

// Load contracts
async function loadContracts(statusFilter = null) {
    try {
        const url = statusFilter ? `/api/contract/list?status_filter=${statusFilter}` : '/api/contract/list';
        const response = await fetch(url);
        const data = await response.json();
        
        if (data.success) {
            currentContracts = data.contracts;
            displayContracts(data.contracts);
        } else {
            document.getElementById('contractsList').innerHTML = `
                <div class="text-center p-4">
                    <i class="fas fa-exclamation-triangle fa-2x text-warning"></i>
                    <p class="mt-2 text-muted">Error loading contracts: ${data.error}</p>
                </div>
            `;
        }
    } catch (error) {
        console.error('Error loading contracts:', error);
        document.getElementById('contractsList').innerHTML = `
            <div class="text-center p-4">
                <i class="fas fa-exclamation-triangle fa-2x text-danger"></i>
                <p class="mt-2 text-muted">Error loading contracts</p>
            </div>
        `;
    }
}

// Display contracts
function displayContracts(contracts) {
    const container = document.getElementById('contractsList');
    
    if (!contracts || contracts.length === 0) {
        container.innerHTML = `
            <div class="text-center p-4">
                <i class="fas fa-file-contract fa-2x text-muted"></i>
                <p class="mt-2 text-muted">No contracts found</p>
                <button class="btn btn-primary" onclick="document.getElementById('create-tab').click()">
                    <i class="fas fa-plus"></i> Create First Contract
                </button>
            </div>
        `;
        return;
    }
    
    let html = '';
    
    contracts.forEach(contract => {
        const statusClass = `status-${contract.status.replace('_', '-')}`;
        const complianceClass = getComplianceClass(contract.compliance_score);
        
        html += `
            <div class="contract-card">
                <div class="contract-header">
                    <div>
                        <h6 class="mb-1">${contract.contract_id}</h6>
                        <small class="text-muted">${formatContractType(contract.contract_type)} • Created ${formatDate(contract.created_date)}</small>
                    </div>
                    <div class="text-end">
                        <span class="contract-status ${statusClass}">${formatStatus(contract.status)}</span>
                        <div class="mt-1">
                            <span class="compliance-score ${complianceClass}">${contract.compliance_score}% Compliant</span>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-8">
                        <div class="row">
                            <div class="col-sm-6">
                                <strong>Customer:</strong> ${contract.parties?.customer?.company_name || 'N/A'}
                            </div>
                            <div class="col-sm-6">
                                <strong>Provider:</strong> ${contract.parties?.provider?.company_name || 'N/A'}
                            </div>
                        </div>
                        ${contract.workflow_status ? `
                            <div class="workflow-progress mt-2">
                                <small class="text-muted">Approval Progress: Step ${contract.workflow_status.current_step} of ${contract.workflow_status.total_steps}</small>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: ${(contract.workflow_status.current_step / contract.workflow_status.total_steps) * 100}%"></div>
                                </div>
                            </div>
                        ` : ''}
                    </div>
                    <div class="col-md-4">
                        <div class="action-buttons">
                            <button class="btn btn-sm btn-outline-primary" onclick="viewContractDetails('${contract.contract_id}')">
                                <i class="fas fa-eye"></i> View
                            </button>
                            ${getActionButtons(contract)}
                        </div>
                    </div>
                </div>
            </div>
        `;
    });
    
    container.innerHTML = html;
}

// Create new contract
async function createContract() {
    try {
        if (!document.getElementById('contractType').value) {
            Swal.fire({
                icon: 'warning',
                title: 'Contract Type Required',
                text: 'Please select a contract type before proceeding'
            });
            return;
        }
        
        const formData = new FormData(document.getElementById('createContractForm'));
        
        Swal.fire({
            title: 'Generating Contract...',
            text: 'Creating contract with automated compliance validation',
            allowOutsideClick: false,
            showConfirmButton: false,
            willOpen: () => {
                Swal.showLoading();
            }
        });
        
        const response = await fetch('/api/contract/create', {
            method: 'POST',
            body: formData
        });
        
        const data = await response.json();
        Swal.close();
        
        if (data.success) {
            Swal.fire({
                icon: 'success',
                title: 'Contract Generated Successfully',
                html: `
                    <p><strong>Contract ID:</strong> ${data.contract_id}</p>
                    <p><strong>Compliance Score:</strong> ${data.compliance_summary.score}%</p>
                    <p><strong>Status:</strong> ${data.compliance_summary.overall_status}</p>
                    ${data.compliance_summary.critical_issues > 0 ? 
                        `<p class="text-warning"><i class="fas fa-exclamation-triangle"></i> ${data.compliance_summary.critical_issues} critical compliance issues detected</p>` : 
                        '<p class="text-success"><i class="fas fa-check-circle"></i> No critical compliance issues</p>'
                    }
                `,
                showConfirmButton: true
            });
            
            // Reset form and refresh data
            document.getElementById('createContractForm').reset();
            document.querySelectorAll('.contract-type-card').forEach(c => c.classList.remove('selected'));
            document.getElementById('contractType').value = '';
            document.getElementById('contracts-tab').click();
            loadContracts();
            loadAnalytics();
        } else {
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: data.error || 'Failed to create contract'
            });
        }
    } catch (error) {
        Swal.close();
        console.error('Error creating contract:', error);
        Swal.fire({
            icon: 'error',
            title: 'Error',
            text: 'Failed to create contract'
        });
    }
}

// View contract details
async function viewContractDetails(contractId) {
    try {
        const response = await fetch(`/api/contract/${contractId}`);
        const data = await response.json();
        
        if (!data.success) {
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: data.error || 'Failed to load contract details'
            });
            return;
        }
        
        const contract = data.contract;
        const workflowDetails = data.workflow_details;
        const complianceDetails = data.compliance_details;
        
        let html = `
            <div class="contract-details">
                <h6>Contract Information</h6>
                <div class="row mb-3">
                    <div class="col-sm-6"><strong>Contract ID:</strong> ${contract.contract_id}</div>
                    <div class="col-sm-6"><strong>Type:</strong> ${formatContractType(contract.contract_type)}</div>
                    <div class="col-sm-6"><strong>Status:</strong> ${formatStatus(contract.status)}</div>
                    <div class="col-sm-6"><strong>Version:</strong> ${contract.version}</div>
                    <div class="col-sm-6"><strong>Created:</strong> ${formatDate(contract.created_date)}</div>
                    <div class="col-sm-6"><strong>Template:</strong> ${contract.template_name}</div>
                </div>
                
                <h6>Parties</h6>
                <div class="row mb-3">
                    <div class="col-sm-6">
                        <strong>Customer:</strong><br>
                        ${contract.parties?.customer?.company_name || 'N/A'}<br>
                        <small class="text-muted">${contract.parties?.customer?.email || ''}</small>
                    </div>
                    <div class="col-sm-6">
                        <strong>Provider:</strong><br>
                        ${contract.parties?.provider?.company_name || 'N/A'}<br>
                        <small class="text-muted">${contract.parties?.provider?.contact_person || ''}</small>
                    </div>
                </div>
                
                <h6>Service Details</h6>
                <div class="mb-3">
                    <p><strong>Service Type:</strong> ${contract.service_details?.service_type || 'N/A'}</p>
                    <p><strong>Duration:</strong> ${contract.service_details?.duration || 'N/A'}</p>
                    <p><strong>Start Date:</strong> ${contract.service_details?.start_date || 'N/A'}</p>
                    ${contract.service_details?.description ? `<p><strong>Description:</strong> ${contract.service_details.description}</p>` : ''}
                </div>
                
                <h6>Contract Clauses</h6>
                <div class="mb-3">
                    ${contract.clauses?.map(clause => `
                        <div class="clause-section">
                            <div class="clause-title">${clause.title}</div>
                            <div class="clause-content">${clause.content}</div>
                            <small class="text-muted">
                                Section: ${clause.section} | 
                                ${clause.is_mandatory ? 'Mandatory' : 'Optional'} | 
                                ${clause.is_negotiable ? 'Negotiable' : 'Non-negotiable'}
                            </small>
                        </div>
                    `).join('') || '<p class="text-muted">No clauses available</p>'}
                </div>
                
                ${complianceDetails?.compliance_results ? `
                    <h6>Compliance Status</h6>
                    <div class="compliance-summary">
                        <div class="compliance-item">
                            <h5>${complianceDetails.compliance_score}%</h5>
                            <small>Overall Score</small>
                        </div>
                        <div class="compliance-item">
                            <h5>${complianceDetails.critical_issues}</h5>
                            <small>Critical Issues</small>
                        </div>
                        <div class="compliance-item">
                            <h5>${complianceDetails.total_checks}</h5>
                            <small>Total Checks</small>
                        </div>
                    </div>
                    <div class="mt-3">
                        ${complianceDetails.compliance_results.map(check => `
                            <div class="compliance-check compliance-${check.severity}">
                                <strong>${check.regulation}</strong>
                                <span class="badge bg-${check.status === 'compliant' ? 'success' : 'danger'} float-end">${check.status}</span>
                                <p class="mb-1">${check.description}</p>
                                <small class="text-muted">Severity: ${check.severity} | Last checked: ${formatDate(check.last_checked)}</small>
                            </div>
                        `).join('')}
                    </div>
                ` : ''}
                
                ${workflowDetails ? `
                    <h6>Approval Workflow</h6>
                    <div class="mb-3">
                        <p><strong>Status:</strong> ${workflowDetails.status}</p>
                        <p><strong>Progress:</strong> Step ${workflowDetails.current_step} of ${workflowDetails.total_steps}</p>
                        <div class="mt-2">
                            ${workflowDetails.approvers?.map((approver, index) => `
                                <div class="workflow-step step-${approver.status}">
                                    <span><strong>${approver.role}:</strong> ${approver.status}</span>
                                    <small>${approver.decision_date ? formatDate(approver.decision_date) : 'Pending'}</small>
                                </div>
                            `).join('') || ''}
                        </div>
                    </div>
                ` : ''}
            </div>
        `;
        
        document.getElementById('contractDetailsContent').innerHTML = html;
        
        // Show action button if applicable
        const actionBtn = document.getElementById('contractActionBtn');
        const availableActions = data.available_actions || [];
        
        if (availableActions.includes('submit_for_approval')) {
            actionBtn.style.display = 'block';
            actionBtn.textContent = 'Submit for Approval';
            actionBtn.onclick = () => showApprovalModal(contractId);
        } else {
            actionBtn.style.display = 'none';
        }
        
        new bootstrap.Modal(document.getElementById('contractDetailsModal')).show();
        
    } catch (error) {
        console.error('Error loading contract details:', error);
        Swal.fire({
            icon: 'error',
            title: 'Error',
            text: 'Failed to load contract details'
        });
    }
}

// Show approval modal
function showApprovalModal(contractId) {
    document.getElementById('approvalContractId').value = contractId;
    bootstrap.Modal.getInstance(document.getElementById('contractDetailsModal')).hide();
    new bootstrap.Modal(document.getElementById('approvalModal')).show();
}

// Submit contract for approval
async function submitForApproval() {
    try {
        const contractId = document.getElementById('approvalContractId').value;
        const workflowType = document.getElementById('workflowType').value;
        
        const formData = new FormData();
        formData.append('workflow_type', workflowType);
        
        const response = await fetch(`/api/contract/${contractId}/submit-approval`, {
            method: 'POST',
            body: formData
        });
        
        const data = await response.json();
        
        if (data.success) {
            bootstrap.Modal.getInstance(document.getElementById('approvalModal')).hide();
            
            Swal.fire({
                icon: 'success',
                title: 'Submitted for Approval',
                html: `
                    <p>Contract submitted successfully</p>
                    <p><strong>Workflow ID:</strong> ${data.workflow_id}</p>
                    <p><strong>Current Step:</strong> ${data.current_step} of ${data.total_steps}</p>
                    <p><strong>Estimated Completion:</strong> ${formatDate(data.estimated_completion)}</p>
                `,
                timer: 3000,
                showConfirmButton: true
            });
            
            loadContracts();
            loadAnalytics();
        } else {
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: data.error || 'Failed to submit contract for approval'
            });
        }
    } catch (error) {
        console.error('Error submitting for approval:', error);
        Swal.fire({
            icon: 'error',
            title: 'Error',
            text: 'Failed to submit contract for approval'
        });
    }
}

// Helper functions
function getComplianceClass(score) {
    if (score >= 90) return 'compliance-excellent';
    if (score >= 75) return 'compliance-good';
    if (score >= 60) return 'compliance-fair';
    return 'compliance-poor';
}

function formatContractType(type) {
    return type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
}

function formatStatus(status) {
    return status.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
}

function formatDate(dateStr) {
    try {
        return new Date(dateStr).toLocaleDateString();
    } catch {
        return dateStr;
    }
}

function getActionButtons(contract) {
    let buttons = '';
    
    if (contract.status === 'draft') {
        buttons += `
            <button class="btn btn-sm btn-contract" onclick="showApprovalModal('${contract.contract_id}')">
                <i class="fas fa-paper-plane"></i> Submit
            </button>
        `;
    }
    
    if (contract.status === 'approved') {
        buttons += `
            <button class="btn btn-sm btn-success" onclick="signContract('${contract.contract_id}')">
                <i class="fas fa-signature"></i> Sign
            </button>
        `;
    }
    
    return buttons;
}

// Apply filters
function applyFilters() {
    const statusFilter = document.getElementById('statusFilter').value;
    loadContracts(statusFilter);
}

// Refresh contracts
function refreshContracts() {
    document.getElementById('statusFilter').value = '';
    document.getElementById('typeFilter').value = '';
    loadContracts();
    loadAnalytics();
}

// Reset contract form
function resetContractForm() {
    document.getElementById('createContractForm').reset();
    document.querySelectorAll('.contract-type-card').forEach(c => c.classList.remove('selected'));
    document.getElementById('contractType').value = '';
    document.getElementById('startDate').value = new Date().toISOString().split('T')[0];
}


// Auto-generated function implementations

function document.getElementById() {
    // Generic function implementation
    console.log('document.getElementById called');
    showAlert('Function document.getElementById executed', 'info');
}

function signContract() {
    // Generic function implementation
    console.log('signContract called');
    showAlert('Function signContract executed', 'info');
}


// Auto-generated button handlers

function performContractAction(button) {
    const action = button.textContent.trim();
    const contractId = button.dataset.contractId || button.closest('[data-contract-id]')?.dataset.contractId;
    
    if (!contractId) {
        showAlert('Contract ID not found', 'error');
        return;
    }
    
    if (confirm(`Are you sure you want to ${action.toLowerCase()} this contract?`)) {
        fetch(`/contract/${contractId}/action`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCSRFToken()
            },
            body: JSON.stringify({action: action.toLowerCase()})
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert(`Contract ${action.toLowerCase()}ed successfully`, 'success');
                location.reload();
            } else {
                showAlert(`Action failed: ${data.error}`, 'error');
            }
        })
        .catch(error => showAlert('Action failed', 'error'));
    }
}
</script>
{% endblock %}