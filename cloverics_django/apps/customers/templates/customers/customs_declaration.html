{% extends "base.html" %}
{% load translation_tags %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <h1><i class="fas fa-file-signature"></i> {% translate 'customs_declaration' %} <span class="badge bg-info ms-2">{% translate 'optional' %}</span></h1>
<p class="text-muted">{% translate 'submit_declaration' %}</p>
                <div class="alert alert-info mb-3" role="alert">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>{% translate 'optional_feature' %}:</strong> {% translate 'optional_feature_description' %}
                </div>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <div id="alertContainer"></div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title"><i class="fas fa-plus-circle"></i> {% translate 'new_message' %}</h5>
                    <p class="card-text">{% translate 'submit_declaration' %}</p>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#newDeclarationModal">
                        <i class="fas fa-plus"></i> {% translate 'create_new_customs_declaration' %}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Existing Declarations -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-list"></i> Your Customs Declarations</h5>
                </div>
                <div class="card-body">
                    {% if declarations %}
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Declaration #</th>
                                        <th>Shipment</th>
                                        <th>Status</th>
                                        <th>Submitted Date</th>
                                        <th>Customs Value</th>
                                        <th>Estimated Duties</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for declaration in declarations %}
                                    <tr>
                                        <td><strong>{{ declaration.declaration_number }}</strong></td>
                                        <td>{{ declaration.tracking_number }}</td>
                                        <td>
                                            {% if declaration.status == 'PENDING' %}
                                                <span class="badge bg-warning">Pending</span>
                                            {% elif declaration.status == 'SUBMITTED' %}
                                                <span class="badge bg-info">Submitted</span>
                                            {% elif declaration.status == 'APPROVED' %}
                                                <span class="badge bg-success">Approved</span>
                                            {% elif declaration.status == 'REJECTED' %}
                                                <span class="badge bg-danger">Rejected</span>
                                            {% elif declaration.status == 'RELEASED' %}
                                                <span class="badge bg-success">Released</span>
                                            {% else %}
                                                <span class="badge bg-secondary">{{ declaration.status }}</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ declaration.submitted_date }}</td>
                                        <td>{{ declaration.customs_value }}</td>
                                        <td>{{ declaration.duties_estimated }}</td>
                                        <td>
                                            <div class="btn-group btn-group-sm flex-wrap">
                                                <button class="btn btn-outline-info" title="View Details" onclick="viewDeclarationDetails({{ declaration.id }})">
                                                    <i class="fas fa-eye"></i> <span class="btn-text">View</span>
                                                </button>
                                                <button class="btn btn-outline-primary" title="Download PDF" onclick="downloadDeclaration({{ declaration.id }})">
                                                    <i class="fas fa-download"></i> <span class="btn-text">Download</span>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="empty-state text-center py-5">
                            <i class="fas fa-file-signature fa-3x text-muted mb-3"></i>
                            <h5>No Customs Declarations Yet</h5>
                            <p class="text-muted">Submit your first customs declaration for international shipments</p>
                            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#newDeclarationModal">
                                <i class="fas fa-plus"></i> Create First Declaration
                            </button>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- International Shipments Requiring Declaration -->
    {% if shipments %}
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-globe"></i> International Shipments</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Tracking #</th>
                                    <th>Route</th>
                                    <th>Cargo</th>
                                    <th>Value</th>
                                    <th>Status</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for shipment in shipments %}
                                {% if shipment.is_international %}
                                <tr>
                                    <td><strong>{{ shipment.tracking_number }}</strong></td>
                                    <td>{{ shipment.origin }} → {{ shipment.destination }}</td>
                                    <td>{{ shipment.cargo }}</td>
                                    <td>{{ shipment.value }}</td>
                                    <td>
                                        <span class="badge bg-info">{{ shipment.status }}</span>
                                    </td>
                                    <td>
                                        {% if shipment.needs_declaration %}
                                            <button class="btn btn-sm btn-outline-primary" onclick="openDeclarationModal({{ shipment.id }}, '{{ shipment.tracking_number }}')">
                                                <i class="fas fa-file-plus"></i> Declare
                                            </button>
                                        {% else %}
                                            <span class="text-muted">No action needed</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endif %}
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<!-- New Declaration Modal -->
<div class="modal fade" id="newDeclarationModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-file-signature"></i> New Customs Declaration</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="/customer/customs-declaration/submit" onsubmit="return submitDeclarationForm(this)">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Select Shipment *</label>
                                <select class="form-control" name="shipment_id" required>
                                    <option value="">Choose shipment...</option>
                                    {% for shipment in shipments %}
                                    {% if shipment.is_international and shipment.needs_declaration %}
                                    <option value="{{ shipment.id }}">{{ shipment.tracking_number }} - {{ shipment.origin }} → {{ shipment.destination }}</option>
                                    {% endif %}
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Country of Origin *</label>
                                <input type="text" class="form-control" name="country_of_origin" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">{% translate 'goods_description' %} *</label>
                        <textarea class="form-control" name="goods_description" rows="3" required placeholder="{% translate 'detailed_description_placeholder' %}"></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">{% translate 'total_value_usd' %} *</label>
                                <input type="number" class="form-control" name="goods_value" step="0.01" min="0" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                                        <label class="form-label">{% translate 'hs_tariff_code' %}</label>
                        <input type="text" class="form-control" name="tariff_code" placeholder="{% translate 'tariff_code_placeholder' %}">
                        <small class="form-text text-muted">{% translate 'harmonized_system_code' %}</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        <strong>{% translate 'important' %}:</strong> {% translate 'accurate_information_warning' %}
                    </div>
                </div>
                <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% translate 'cancel' %}</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-paper-plane"></i> {% translate 'submit_declaration' %}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function openDeclarationModal(shipmentId, trackingNumber) {
    const modal = new bootstrap.Modal(document.getElementById('newDeclarationModal'));
    const selectElement = document.querySelector('select[name="shipment_id"]');
    selectElement.value = shipmentId;
    modal.show();
}

function viewDeclarationDetails(declarationId) {
    fetch(`/api/customs-declaration/${declarationId}/details`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const details = data.details;
                const modalContent = `
                    <div class="modal fade" id="detailsModal" tabindex="-1">
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title">Declaration Details: ${details.declaration_number}</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                </div>
                                <div class="modal-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h6>Basic Information</h6>
                                            <p><strong>Declaration Number:</strong> ${details.declaration_number}</p>
                                            <p><strong>Status:</strong> <span class="badge bg-primary">${details.status}</span></p>
                                            <p><strong>Shipment:</strong> ${details.shipment_tracking}</p>
                                            <p><strong>Country of Origin:</strong> ${details.country_of_origin}</p>
                                        </div>
                                        <div class="col-md-6">
                                            <h6>Financial Information</h6>
                                            <p><strong>Goods Value:</strong> $${details.goods_value}</p>
                                            <p><strong>Duties Amount:</strong> $${details.duties_amount}</p>
                                            <p><strong>Taxes Amount:</strong> $${details.taxes_amount}</p>
                                            <p><strong>Submitted:</strong> ${details.submitted_date ? new Date(details.submitted_date).toLocaleDateString() : 'Not submitted'}</p>
                                        </div>
                                    </div>
                                    <div class="row mt-3">
                                        <div class="col-12">
                                            <h6>Goods Description</h6>
                                            <p>${details.goods_description}</p>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-12">
                                            <h6>Tariff Codes</h6>
                                            <p>${details.tariff_codes.join(', ')}</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                    <button type="button" class="btn btn-primary" onclick="downloadDeclaration(${declarationId})">
                                        <i class="fas fa-download"></i> Download PDF
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                
                // Remove existing modal if any
                const existingModal = document.getElementById('detailsModal');
                if (existingModal) {
                    existingModal.remove();
                }
                
                // Add new modal to body
                document.body.insertAdjacentHTML('beforeend', modalContent);
                
                // Show modal
                const modal = new bootstrap.Modal(document.getElementById('detailsModal'));
                modal.show();
            } else {
                alert('Error loading declaration details: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Failed to load declaration details. Please try again.');
        });
}

function downloadDeclaration(declarationId) {
    // Create a temporary link to trigger download
    const downloadUrl = `/api/customs-declaration/${declarationId}/download`;
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = `declaration_${declarationId}.pdf`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

function quickDeclareShipment(shipmentId, trackingNumber) {
    if (confirm(`Create a quick declaration for shipment ${trackingNumber}?\n\nThis will create a basic declaration with generic information that you can update later.`)) {
        const formData = new FormData();
        formData.append('shipment_id', shipmentId);
        
        fetch('/api/customs-declaration/quick-declare', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', data.message);
                setTimeout(() => location.reload(), 2000); // Reload after 2 seconds
            } else {
                showAlert('error', 'Error: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('error', 'Failed to create declaration. Please try again.');
        });
    }
}

// Function to show alerts
function showAlert(type, message) {
    const alertContainer = document.getElementById('alertContainer');
    const alertClass = type === 'success' ? 'alert-success' : type === 'error' ? 'alert-danger' : 'alert-info';
    const iconClass = type === 'success' ? 'fas fa-check-circle' : type === 'error' ? 'fas fa-exclamation-triangle' : 'fas fa-info-circle';
    
    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            <i class="${iconClass}"></i> ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    alertContainer.innerHTML = alertHtml;
    
    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        const alert = alertContainer.querySelector('.alert');
        if (alert) {
            alert.remove();
        }
    }, 5000);
}

function submitDeclarationForm(form) {
    // Show loading feedback
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Submitting...';
    submitBtn.disabled = true;
    
    // Show alert that submission is in progress
    showAlert('info', 'Submitting customs declaration... Please wait.');
    
    return true; // Allow form submission to continue
}

// Check for flash messages on page load
document.addEventListener('DOMContentLoaded', function() {
    // Check cookies for flash messages
    const cookies = document.cookie.split(';');
    
    cookies.forEach(cookie => {
        const [name, value] = cookie.trim().split('=');
        if (name === 'flash_message') {
            showAlert('success', decodeURIComponent(value));
            // Clear the cookie
            document.cookie = 'flash_message=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
        } else if (name === 'flash_error') {
            showAlert('error', decodeURIComponent(value));
            // Clear the cookie
            document.cookie = 'flash_error=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
        }
    });
});
</script>

<style>
.empty-state {
    padding: 3rem 1rem;
}

.stat-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1.5rem;
    border-radius: 10px;
    text-align: center;
    margin-bottom: 1rem;
}

.stat-card h3 {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.page-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e9ecef;
}

.page-header h1 {
    color: #2c3e50;
    font-weight: 600;
}

.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}
</style>
{% endblock %}

{% block extra_css %}
<style>
/* Responsive Design */
@media (max-width: 1200px) {
    .container-fluid {
        padding: 1rem;
    }
    
    .card {
        margin-bottom: 1.5rem;
    }
}

@media (max-width: 992px) {
    .container-fluid {
        padding: 0.75rem;
    }
    
    .card {
        margin-bottom: 1rem;
    }
    
    .table-responsive {
        font-size: 0.9rem;
    }
    
    .btn-group {
        flex-direction: column;
        width: 100%;
    }
    
    .btn-group .btn {
        width: 100%;
        margin-bottom: 0.25rem;
    }
}

@media (max-width: 768px) {
    .container-fluid {
        padding: 0.5rem;
    }
    
    h1 {
        font-size: 1.75rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .table-responsive {
        font-size: 0.8rem;
    }
    
    .table th,
    .table td {
        padding: 0.5rem 0.25rem;
    }
    
    .btn-text {
        display: none;
    }
    
    .badge {
        font-size: 0.7rem;
        padding: 0.25rem 0.5rem;
    }
}

@media (max-width: 576px) {
    .container-fluid {
        padding: 0.25rem;
    }
    
    h1 {
        font-size: 1.5rem;
    }
    
    .card-body {
        padding: 0.75rem;
    }
    
    .table-responsive {
        font-size: 0.75rem;
    }
    
    .table th,
    .table td {
        padding: 0.25rem 0.125rem;
    }
    
    .btn-group-sm .btn {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }
    
    .badge {
        font-size: 0.65rem;
        padding: 0.2rem 0.4rem;
    }
    
    .empty-state {
        padding: 2rem 0.5rem;
    }
    
    .empty-state i {
        font-size: 2.5rem;
    }
}

/* Landscape orientation adjustments for mobile */
@media (max-width: 768px) and (orientation: landscape) {
    .btn-group {
        flex-direction: row;
        flex-wrap: wrap;
    }
    
    .btn-group .btn {
        width: auto;
        margin-bottom: 0;
        flex: 1;
    }
    
    .btn-text {
        display: inline;
    }
}
</style>
{% endblock %}

{% block extra_js %}
<script>
// Responsive behavior for button text
document.addEventListener('DOMContentLoaded', function() {
    function handleResize() {
        const isMobile = window.innerWidth <= 576;
        const btnTexts = document.querySelectorAll('.btn-text');
        
        btnTexts.forEach(text => {
            text.style.display = isMobile ? 'none' : 'inline';
        });
    }
    
    // Initial call
    handleResize();
    
    // Listen for window resize
    window.addEventListener('resize', handleResize);
});
</script>
{% endblock %}