{% extends "base.html" %}

{% block title %}AI Analytics & Intelligence - Cloverics{% endblock %}

{% block extra_css %}
<style>
/* Responsive Design */
@media (max-width: 1200px) {
    .container-fluid {
        padding: 1rem;
    }
    
    .card {
        margin-bottom: 1rem;
    }
}

@media (max-width: 992px) {
    .container-fluid {
        padding: 0.75rem;
    }
    
    .d-flex.justify-content-between {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .d-flex.gap-2 {
        justify-content: flex-start;
        width: 100%;
    }
    
    .d-flex.gap-2 .btn {
        flex: 1;
        min-width: 120px;
    }
    
    .nav-tabs {
        flex-direction: column;
        width: 100%;
    }
    
    .nav-tabs .nav-item {
        width: 100%;
    }
    
    .nav-tabs .nav-link {
        width: 100%;
        text-align: center;
    }
    
    .display-6 {
        font-size: 2rem;
    }
    
    .card-title {
        font-size: 0.9rem;
    }
    
    .card h2 {
        font-size: 1.5rem;
    }
}

@media (max-width: 768px) {
    .container-fluid {
        padding: 0.5rem;
    }
    
    h1.h3 {
        font-size: 1.5rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .d-flex.gap-2 {
        flex-direction: column;
        width: 100%;
    }
    
    .d-flex.gap-2 .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }
    
    .btn-text {
        display: none;
    }
    
    .display-6 {
        font-size: 1.75rem;
    }
    
    .card-title {
        font-size: 0.85rem;
    }
    
    .card h2 {
        font-size: 1.25rem;
    }
    
    .nav-tabs .nav-link {
        font-size: 0.9rem;
        padding: 0.5rem;
    }
}

@media (max-width: 576px) {
    .container-fluid {
        padding: 0.25rem;
    }
    
    h1.h3 {
        font-size: 1.25rem;
    }
    
    .card-body {
        padding: 0.75rem;
    }
    
    .display-6 {
        font-size: 1.5rem;
    }
    
    .card-title {
        font-size: 0.8rem;
    }
    
    .card h2 {
        font-size: 1.1rem;
    }
    
    .nav-tabs .nav-link {
        font-size: 0.8rem;
        padding: 0.5rem;
    }
    
    .btn {
        font-size: 0.9rem;
        padding: 0.75rem 1rem;
    }
}

/* Landscape orientation adjustments for mobile */
@media (max-width: 768px) and (orientation: landscape) {
    .d-flex.gap-2 {
        flex-direction: row;
        flex-wrap: wrap;
    }
    
    .d-flex.gap-2 .btn {
        width: auto;
        margin-bottom: 0;
        flex: 1;
    }
    
    .btn-text {
        display: inline;
    }
}
</style>
{% endblock %}

{% block extra_js %}
<script>
// Responsive behavior for button text
document.addEventListener('DOMContentLoaded', function() {
    function handleResize() {
        const isMobile = window.innerWidth <= 576;
        const btnTexts = document.querySelectorAll('.btn-text');
        
        btnTexts.forEach(text => {
            text.style.display = isMobile ? 'none' : 'inline';
        });
    }
    
    // Initial call
    handleResize();
    
    // Listen for window resize
    window.addEventListener('resize', handleResize);
});
</script>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4 flex-wrap">
                <div>
                    <h1 class="h3 mb-1">
                        <i class="fas fa-robot text-primary me-2"></i>
                        AI Analytics & Intelligence
                    </h1>
                    <p class="text-muted mb-0">Advanced AI-powered logistics insights and predictive analytics</p>
                </div>
                <div class="d-flex gap-2 flex-wrap">
                    <button class="btn btn-outline-primary" onclick="refreshAnalytics()">
                        <i class="fas fa-sync-alt me-1"></i> <span class="btn-text">Refresh</span>
                    </button>
                    <button class="btn btn-primary" onclick="generateComprehensiveAnalytics()">
                        <i class="fas fa-brain me-1"></i> <span class="btn-text">Generate AI Insights</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- AI Analytics Tabs -->
    <ul class="nav nav-tabs" id="analyticsTab" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link active" id="overview-tab" data-bs-toggle="tab" data-bs-target="#overview" type="button" role="tab">
                <i class="fas fa-chart-line me-1"></i> Analytics Overview
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="demand-forecast-tab" data-bs-toggle="tab" data-bs-target="#demand-forecast" type="button" role="tab">
                <i class="fas fa-chart-area me-1"></i> Demand Forecasting
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="price-prediction-tab" data-bs-toggle="tab" data-bs-target="#price-prediction" type="button" role="tab">
                <i class="fas fa-dollar-sign me-1"></i> Price Prediction
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="route-optimization-tab" data-bs-toggle="tab" data-bs-target="#route-optimization" type="button" role="tab">
                <i class="fas fa-route me-1"></i> Route Optimization
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="ai-insights-tab" data-bs-toggle="tab" data-bs-target="#ai-insights" type="button" role="tab">
                <i class="fas fa-lightbulb me-1"></i> AI Insights
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="market-intelligence-tab" data-bs-toggle="tab" data-bs-target="#market-intelligence" type="button" role="tab">
                <i class="fas fa-globe me-1"></i> Market Intelligence
            </button>
        </li>
    </ul>

    <div class="tab-content" id="analyticsTabContent">
        <!-- Analytics Overview Tab -->
        <div class="tab-pane fade show active" id="overview" role="tabpanel">
            <div class="py-4">
                <!-- AI Analytics Summary Cards -->
                <div class="row mb-4">
                    <div class="col-lg-3 col-md-6 col-sm-6 mb-3">
                        <div class="card border-0 shadow-sm h-100">
                            <div class="card-body text-center">
                                <div class="display-6 text-primary mb-2">
                                    <i class="fas fa-brain"></i>
                                </div>
                                <h5 class="card-title mb-1">AI Models Active</h5>
                                <h2 class="text-primary mb-1" id="active-models-count">3</h2>
                                <small class="text-success">
                                    <i class="fas fa-arrow-up"></i> 87% Avg Accuracy
                                </small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card border-0 shadow-sm h-100">
                            <div class="card-body text-center">
                                <div class="display-6 text-info mb-2">
                                    <i class="fas fa-chart-line"></i>
                                </div>
                                <h5 class="card-title mb-1">Predictions Generated</h5>
                                <h2 class="text-info mb-1" id="predictions-count">1,247</h2>
                                <small class="text-success">
                                    <i class="fas fa-arrow-up"></i> +23% This Month
                                </small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card border-0 shadow-sm h-100">
                            <div class="card-body text-center">
                                <div class="display-6 text-success mb-2">
                                    <i class="fas fa-lightbulb"></i>
                                </div>
                                <h5 class="card-title mb-1">AI Insights</h5>
                                <h2 class="text-success mb-1" id="insights-count">42</h2>
                                <small class="text-warning">
                                    <i class="fas fa-exclamation-triangle"></i> 8 High Priority
                                </small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card border-0 shadow-sm h-100">
                            <div class="card-body text-center">
                                <div class="display-6 text-warning mb-2">
                                    <i class="fas fa-dollar-sign"></i>
                                </div>
                                <h5 class="card-title mb-1">Cost Savings</h5>
                                <h2 class="text-warning mb-1" id="cost-savings">$12.5K</h2>
                                <small class="text-success">
                                    <i class="fas fa-arrow-up"></i> From AI Optimization
                                </small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Analytics Form -->
                <div class="row">
                    <div class="col-lg-8">
                        <div class="card border-0 shadow-sm">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0">
                                    <i class="fas fa-robot me-2"></i>
                                    Generate Comprehensive AI Analytics
                                </h5>
                            </div>
                            <div class="card-body">
                                <form id="comprehensive-analytics-form">
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">Origin</label>
                                            <input type="text" class="form-control" name="origin" value="Istanbul" placeholder="Origin city">
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">Destination</label>
                                            <input type="text" class="form-control" name="destination" value="Berlin" placeholder="Destination city">
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-4 mb-3">
                                            <label class="form-label">Cargo Type</label>
                                            <select class="form-select" name="cargo_type">
                                                <option value="general">General Cargo</option>
                                                <option value="electronics">Electronics</option>
                                                <option value="automotive">Automotive</option>
                                                <option value="food">Food & Beverages</option>
                                                <option value="chemicals">Chemicals</option>
                                                <option value="textiles">Textiles</option>
                                            </select>
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <label class="form-label">Urgency</label>
                                            <select class="form-select" name="urgency">
                                                <option value="standard">Standard</option>
                                                <option value="urgent">Urgent</option>
                                                <option value="express">Express</option>
                                            </select>
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <label class="form-label">Environmental Priority</label>
                                            <select class="form-select" name="environmental_priority">
                                                <option value="low">Low</option>
                                                <option value="medium" selected>Medium</option>
                                                <option value="high">High</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">Forecast Horizon (Days)</label>
                                            <input type="number" class="form-control" name="forecast_days" value="30" min="7" max="90">
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">Price Prediction (Days)</label>
                                            <input type="number" class="form-control" name="price_forecast_days" value="14" min="3" max="30">
                                        </div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-12">
                                            <label class="form-label">Analytics Components</label>
                                            <div class="d-flex gap-3">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" name="include_demand_forecast" checked>
                                                    <label class="form-check-label">Demand Forecasting</label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" name="include_price_prediction" checked>
                                                    <label class="form-check-label">Price Prediction</label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" name="include_route_optimization" checked>
                                                    <label class="form-check-label">Route Optimization</label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-brain me-1"></i> Generate AI Analytics
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4">
                        <div class="card border-0 shadow-sm">
                            <div class="card-header bg-info text-white">
                                <h5 class="mb-0">
                                    <i class="fas fa-chart-pie me-2"></i>
                                    AI Model Performance
                                </h5>
                            </div>
                            <div class="card-body">
                                <div id="model-performance-chart" style="height: 250px;"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent AI Insights -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card border-0 shadow-sm">
                            <div class="card-header bg-light">
                                <h5 class="mb-0">
                                    <i class="fas fa-lightbulb me-2"></i>
                                    Recent AI Insights
                                </h5>
                            </div>
                            <div class="card-body">
                                <div id="recent-insights-container">
                                    <div class="text-center text-muted py-4">
                                        <i class="fas fa-brain fa-2x mb-2"></i>
                                        <p>Click "Generate AI Analytics" to get personalized insights</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Demand Forecasting Tab -->
        <div class="tab-pane fade" id="demand-forecast" role="tabpanel">
            <div class="py-4">
                <div class="row">
                    <div class="col-lg-8">
                        <div class="card border-0 shadow-sm">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0">
                                    <i class="fas fa-chart-area me-2"></i>
                                    AI Demand Forecasting
                                </h5>
                            </div>
                            <div class="card-body">
                                <div id="demand-forecast-chart" style="height: 400px;"></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4">
                        <div class="card border-0 shadow-sm">
                            <div class="card-header bg-light">
                                <h5 class="mb-0">Forecast Parameters</h5>
                            </div>
                            <div class="card-body">
                                <form id="demand-forecast-form">
                                    <div class="mb-3">
                                        <label class="form-label">Route</label>
                                        <input type="text" class="form-control" name="route" value="Istanbul → Berlin">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Time Horizon (Days)</label>
                                        <input type="number" class="form-control" name="time_horizon" value="30" min="7" max="90">
                                    </div>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-chart-area me-1"></i> Generate Forecast
                                    </button>
                                </form>
                                <div id="demand-forecast-summary" class="mt-4"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Price Prediction Tab -->
        <div class="tab-pane fade" id="price-prediction" role="tabpanel">
            <div class="py-4">
                <div class="row">
                    <div class="col-lg-8">
                        <div class="card border-0 shadow-sm">
                            <div class="card-header bg-success text-white">
                                <h5 class="mb-0">
                                    <i class="fas fa-dollar-sign me-2"></i>
                                    AI Price Prediction
                                </h5>
                            </div>
                            <div class="card-body">
                                <div id="price-prediction-chart" style="height: 400px;"></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4">
                        <div class="card border-0 shadow-sm">
                            <div class="card-header bg-light">
                                <h5 class="mb-0">Prediction Parameters</h5>
                            </div>
                            <div class="card-body">
                                <form id="price-prediction-form">
                                    <div class="mb-3">
                                        <label class="form-label">Route</label>
                                        <input type="text" class="form-control" name="route" value="Istanbul → Berlin">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Cargo Type</label>
                                        <select class="form-select" name="cargo_type">
                                            <option value="general">General Cargo</option>
                                            <option value="electronics">Electronics</option>
                                            <option value="automotive">Automotive</option>
                                            <option value="chemicals">Chemicals</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Time Horizon (Days)</label>
                                        <input type="number" class="form-control" name="time_horizon" value="14" min="3" max="30">
                                    </div>
                                    <button type="submit" class="btn btn-success">
                                        <i class="fas fa-dollar-sign me-1"></i> Predict Prices
                                    </button>
                                </form>
                                <div id="price-prediction-summary" class="mt-4"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Route Optimization Tab -->
        <div class="tab-pane fade" id="route-optimization" role="tabpanel">
            <div class="py-4">
                <div class="row">
                    <div class="col-lg-8">
                        <div class="card border-0 shadow-sm">
                            <div class="card-header bg-warning text-dark">
                                <h5 class="mb-0">
                                    <i class="fas fa-route me-2"></i>
                                    AI Route Optimization
                                </h5>
                            </div>
                            <div class="card-body">
                                <div id="route-optimization-results">
                                    <div class="text-center text-muted py-5">
                                        <i class="fas fa-route fa-3x mb-3"></i>
                                        <p>Submit route optimization parameters to get AI recommendations</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4">
                        <div class="card border-0 shadow-sm">
                            <div class="card-header bg-light">
                                <h5 class="mb-0">Optimization Parameters</h5>
                            </div>
                            <div class="card-body">
                                <form id="route-optimization-form">
                                    <div class="mb-3">
                                        <label class="form-label">Origin</label>
                                        <input type="text" class="form-control" name="origin" value="Istanbul">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Destination</label>
                                        <input type="text" class="form-control" name="destination" value="Berlin">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Urgency</label>
                                        <select class="form-select" name="urgency">
                                            <option value="standard">Standard</option>
                                            <option value="urgent">Urgent</option>
                                            <option value="express">Express</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Budget Constraint</label>
                                        <input type="number" class="form-control" name="budget_constraint" placeholder="Max budget (optional)">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Environmental Priority</label>
                                        <select class="form-select" name="environmental_priority">
                                            <option value="low">Low</option>
                                            <option value="medium">Medium</option>
                                            <option value="high">High</option>
                                        </select>
                                    </div>
                                    <button type="submit" class="btn btn-warning">
                                        <i class="fas fa-route me-1"></i> Optimize Route
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- AI Insights Tab -->
        <div class="tab-pane fade" id="ai-insights" role="tabpanel">
            <div class="py-4">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h4>
                        <i class="fas fa-lightbulb me-2"></i>
                        Personalized AI Insights
                    </h4>
                    <button class="btn btn-primary" onclick="loadAIInsights()">
                        <i class="fas fa-sync-alt me-1"></i> Refresh Insights
                    </button>
                </div>
                
                <div id="ai-insights-container">
                    <div class="text-center text-muted py-5">
                        <i class="fas fa-lightbulb fa-3x mb-3"></i>
                        <p>Loading AI insights...</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Market Intelligence Tab -->
        <div class="tab-pane fade" id="market-intelligence" role="tabpanel">
            <div class="py-4">
                <div class="row">
                    <div class="col-12">
                        <div class="card border-0 shadow-sm">
                            <div class="card-header bg-info text-white">
                                <h5 class="mb-0">
                                    <i class="fas fa-globe me-2"></i>
                                    Global Market Intelligence
                                </h5>
                            </div>
                            <div class="card-body">
                                <div id="market-intelligence-content">
                                    <div class="text-center text-muted py-5">
                                        <i class="fas fa-globe fa-3x mb-3"></i>
                                        <p>Loading market intelligence data...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Chart.js for visualizations -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    loadModelPerformanceChart();
    loadAIInsights();
    loadMarketIntelligence();
});

// Model Performance Chart
function loadModelPerformanceChart() {
    const ctx = document.getElementById('model-performance-chart');
    if (ctx) {
        try {
            const chart = ctx.getContext('2d');
            if (chart) {
                new Chart(chart, {
                type: 'doughnut',
                data: {
                    labels: ['Demand Forecasting', 'Price Prediction', 'Route Optimization'],
                    datasets: [{
                        data: [89, 84, 92],
                        backgroundColor: ['#198754', '#0dcaf0', '#ffc107'],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return context.label + ': ' + context.parsed + '% Accuracy';
                                }
                            }
                        }
                    }
                }
            });
        } catch (error) {
            console.error('Chart creation error:', error);
            // Fallback: Show simple text instead of chart
            ctx.innerHTML = '<div class="text-center p-4"><div class="alert alert-info">Model Performance: Demand Forecasting 89%, Price Prediction 84%, Route Optimization 92%</div></div>';
        }
    }
}

// Comprehensive Analytics Form
document.getElementById('comprehensive-analytics-form').addEventListener('submit', function(e) {
    e.preventDefault();
    generateComprehensiveAnalytics();
});

async function generateComprehensiveAnalytics() {
    const form = document.getElementById('comprehensive-analytics-form');
    const formData = new FormData(form);
    
    // Show loading state
    showLoadingState('recent-insights-container');
    
    try {
        const response = await fetch('/api/ai/comprehensive-analytics', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        if (result.success) {
            displayComprehensiveResults(result);
            updateAnalyticsCards(result);
        } else {
            showErrorMessage('recent-insights-container', result.error);
        }
    } catch (error) {
        showErrorMessage('recent-insights-container', 'Failed to generate analytics');
    }
}

function displayComprehensiveResults(result) {
    const container = document.getElementById('recent-insights-container');
    
    let html = '<div class="row">';
    
    // AI Insights
    if (result.ai_insights && result.ai_insights.length > 0) {
        html += '<div class="col-12 mb-4">';
        html += '<h5><i class="fas fa-lightbulb me-2"></i>AI Insights</h5>';
        
        result.ai_insights.forEach(insight => {
            const priorityClass = insight.priority === 'high' ? 'border-danger' : 
                                insight.priority === 'medium' ? 'border-warning' : 'border-info';
            
            html += `
                <div class="card ${priorityClass} mb-2">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <h6 class="card-title">${insight.title}</h6>
                                <p class="card-text">${insight.description}</p>
                                <small class="text-muted">Confidence: ${(insight.confidence_score * 100).toFixed(1)}%</small>
                            </div>
                            <span class="badge bg-${insight.priority === 'high' ? 'danger' : insight.priority === 'medium' ? 'warning' : 'info'}">${insight.priority}</span>
                        </div>
                    </div>
                </div>
            `;
        });
        html += '</div>';
    }
    
    // Recommendations
    if (result.personalized_recommendations && result.personalized_recommendations.length > 0) {
        html += '<div class="col-12">';
        html += '<h5><i class="fas fa-tasks me-2"></i>Personalized Recommendations</h5>';
        
        result.personalized_recommendations.forEach(rec => {
            html += `
                <div class="card border-success mb-2">
                    <div class="card-body">
                        <h6 class="card-title">${rec.title}</h6>
                        <p class="card-text">${rec.description}</p>
                        <small class="text-success">Priority: ${rec.priority} | Success Rate: ${(rec.success_probability * 100).toFixed(0)}%</small>
                    </div>
                </div>
            `;
        });
        html += '</div>';
    }
    
    html += '</div>';
    container.innerHTML = html;
}

// Demand Forecasting
document.getElementById('demand-forecast-form').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const route = formData.get('route');
    const timeHorizon = formData.get('time_horizon');
    
    try {
        const response = await fetch(`/api/ai/demand-forecast?route=${encodeURIComponent(route)}&time_horizon=${timeHorizon}`);
        const result = await response.json();
        
        if (result.success) {
            displayDemandForecastChart(result);
            displayDemandForecastSummary(result);
        }
    } catch (error) {
        console.error('Error loading demand forecast:', error);
    }
});

function displayDemandForecastChart(result) {
    const ctx = document.getElementById('demand-forecast-chart');
    if (ctx && result.forecast_data) {
        try {
            const chart = ctx.getContext('2d');
        
        // Clear existing chart
        if (window.demandChart) {
            window.demandChart.destroy();
        }
        
        const labels = result.forecast_data.map(d => new Date(d.date).toLocaleDateString());
        const data = result.forecast_data.map(d => d.predicted_demand);
        const upperBound = result.forecast_data.map(d => d.confidence_interval.upper);
        const lowerBound = result.forecast_data.map(d => d.confidence_interval.lower);
        
            window.demandChart = new Chart(chart, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [
                        {
                            label: 'Predicted Demand',
                            data: data,
                            borderColor: '#0d6efd',
                            backgroundColor: 'rgba(13, 110, 253, 0.1)',
                            fill: false,
                            tension: 0.4
                        },
                        {
                            label: 'Upper Confidence',
                            data: upperBound,
                            borderColor: 'rgba(13, 110, 253, 0.3)',
                            borderDash: [5, 5],
                            fill: false
                        },
                        {
                            label: 'Lower Confidence',
                            data: lowerBound,
                            borderColor: 'rgba(13, 110, 253, 0.3)',
                            borderDash: [5, 5],
                            fill: false
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: `Demand Forecast: ${result.route}`
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Demand Units'
                            }
                        }
                    }
                }
            });
        } catch (error) {
            console.error('Failed to create demand forecast chart:', error);
        }
    }
}

// Price Prediction
document.getElementById('price-prediction-form').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const route = formData.get('route');
    const cargoType = formData.get('cargo_type');
    const timeHorizon = formData.get('time_horizon');
    
    try {
        const response = await fetch(`/api/ai/price-prediction?route=${encodeURIComponent(route)}&cargo_type=${cargoType}&time_horizon=${timeHorizon}`);
        const result = await response.json();
        
        if (result.success) {
            displayPricePredictionChart(result);
            displayPricePredictionSummary(result);
        }
    } catch (error) {
        console.error('Error loading price prediction:', error);
    }
});

function displayPricePredictionChart(result) {
    const ctx = document.getElementById('price-prediction-chart');
    if (ctx && result.price_predictions) {
        try {
            const chart = ctx.getContext('2d');
        
        // Clear existing chart
        if (window.priceChart) {
            window.priceChart.destroy();
        }
        
        const labels = result.price_predictions.map(d => new Date(d.date).toLocaleDateString());
        const data = result.price_predictions.map(d => d.predicted_price);
        
        window.priceChart = new Chart(chart, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Predicted Price ($)',
                    data: data,
                    borderColor: '#198754',
                    backgroundColor: 'rgba(25, 135, 84, 0.1)',
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: `Price Prediction: ${result.route}`
                    }
                },
                scales: {
                    y: {
                        beginAtZero: false,
                        title: {
                            display: true,
                            text: 'Price ($)'
                        }
                    }
                }
            }
        });
        } catch (error) {
            console.error('Failed to create price prediction chart:', error);
        }
    }
}

// Route Optimization
document.getElementById('route-optimization-form').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    
    try {
        const response = await fetch('/api/ai/route-optimization', {
            method: 'POST',
            body: formData
        });
        const result = await response.json();
        
        if (result.success) {
            displayRouteOptimizationResults(result);
        }
    } catch (error) {
        console.error('Error loading route optimization:', error);
    }
});

function displayRouteOptimizationResults(result) {
    const container = document.getElementById('route-optimization-results');
    
    let html = `
        <h5>Optimized Routes for ${result.optimization_request.origin} → ${result.optimization_request.destination}</h5>
        <div class="row">
    `;
    
    result.optimized_routes.forEach((route, index) => {
        const rankClass = index === 0 ? 'border-success' : index === 1 ? 'border-warning' : 'border-info';
        const rankIcon = index === 0 ? '🥇' : index === 1 ? '🥈' : '🥉';
        
        html += `
            <div class="col-12 mb-3">
                <div class="card ${rankClass}">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <h6>${rankIcon} ${route.transport_modes.join(' → ').toUpperCase()}</h6>
                                <p class="mb-1">Distance: ${route.total_distance_km} km | Time: ${route.estimated_time_hours}h</p>
                                <p class="mb-1">Cost: $${route.estimated_cost.toLocaleString()} | Carbon: ${route.carbon_footprint_kg}kg CO₂</p>
                                <small class="text-muted">Reliability: ${(route.reliability_score * 100).toFixed(0)}%</small>
                            </div>
                            <div class="text-end">
                                <div class="badge bg-primary mb-1">Score: ${route.optimization_score}</div>
                                <br>
                                ${index === 0 ? '<span class="badge bg-success">Recommended</span>' : ''}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });
    
    html += '</div>';
    
    if (result.ai_insights && result.ai_insights.length > 0) {
        html += '<div class="mt-3"><h6>AI Insights:</h6><ul>';
        result.ai_insights.forEach(insight => {
            html += `<li>${insight}</li>`;
        });
        html += '</ul></div>';
    }
    
    container.innerHTML = html;
}

// Load AI Insights
async function loadAIInsights() {
    try {
        const response = await fetch('/api/ai/insights');
        const result = await response.json();
        
        if (result.success) {
            displayAIInsights(result);
        }
    } catch (error) {
        console.error('Error loading AI insights:', error);
    }
}

function displayAIInsights(result) {
    const container = document.getElementById('ai-insights-container');
    
    let html = '';
    
    if (result.ai_insights && result.ai_insights.length > 0) {
        html += '<div class="row mb-4">';
        
        // Summary cards
        html += `
            <div class="col-md-4">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <h3>${result.insights_summary.total_insights}</h3>
                        <p class="mb-0">Total Insights</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card bg-warning text-dark">
                    <div class="card-body text-center">
                        <h3>${result.insights_summary.high_priority_count}</h3>
                        <p class="mb-0">High Priority</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <h3>${(result.insights_summary.avg_confidence * 100).toFixed(0)}%</h3>
                        <p class="mb-0">Avg Confidence</p>
                    </div>
                </div>
            </div>
        `;
        
        html += '</div>';
        
        // Insights list
        result.ai_insights.forEach(insight => {
            const priorityClass = insight.priority === 'high' ? 'border-danger' : 
                                insight.priority === 'medium' ? 'border-warning' : 'border-info';
            
            html += `
                <div class="card ${priorityClass} mb-3">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start">
                            <div class="flex-grow-1">
                                <h5 class="card-title">${insight.title}</h5>
                                <p class="card-text">${insight.description}</p>
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6>Impact Assessment:</h6>
                                        <ul class="list-unstyled">
                                            ${Object.entries(insight.impact_assessment).map(([key, value]) => 
                                                `<li><strong>${key.replace('_', ' ')}:</strong> ${value}</li>`
                                            ).join('')}
                                        </ul>
                                    </div>
                                    <div class="col-md-6">
                                        <h6>Recommendations:</h6>
                                        <ul>
                                            ${insight.recommendations.map(rec => `<li>${rec}</li>`).join('')}
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="text-end">
                                <span class="badge bg-${insight.priority === 'high' ? 'danger' : insight.priority === 'medium' ? 'warning' : 'info'} mb-2">${insight.priority}</span>
                                <br>
                                <small class="text-muted">Confidence: ${(insight.confidence_score * 100).toFixed(1)}%</small>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });
        
        // Recommendations
        if (result.personalized_recommendations && result.personalized_recommendations.length > 0) {
            html += '<h5 class="mt-4"><i class="fas fa-tasks me-2"></i>Personalized Recommendations</h5>';
            
            result.personalized_recommendations.forEach(rec => {
                html += `
                    <div class="card border-success mb-2">
                        <div class="card-body">
                            <h6 class="card-title">${rec.title}</h6>
                            <p class="card-text">${rec.description}</p>
                            <small class="text-success">Priority: ${rec.priority} | Success Rate: ${(rec.success_probability * 100).toFixed(0)}%</small>
                        </div>
                    </div>
                `;
            });
        }
    } else {
        html = `
            <div class="text-center text-muted py-5">
                <i class="fas fa-lightbulb fa-3x mb-3"></i>
                <p>No AI insights available. Generate comprehensive analytics to get personalized insights.</p>
            </div>
        `;
    }
    
    container.innerHTML = html;
}

// Load Market Intelligence
async function loadMarketIntelligence() {
    try {
        const response = await fetch('/api/ai/market-intelligence');
        const result = await response.json();
        
        if (result.success) {
            displayMarketIntelligence(result);
        }
    } catch (error) {
        console.error('Error loading market intelligence:', error);
    }
}

function displayMarketIntelligence(result) {
    const container = document.getElementById('market-intelligence-content');
    
    let html = `
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body text-center">
                        <h4>${result.global_trends.avg_demand_index.toFixed(2)}</h4>
                        <p class="mb-0">Global Demand Index</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <h4>${(result.global_trends.avg_capacity_utilization * 100).toFixed(0)}%</h4>
                        <p class="mb-0">Avg Capacity Utilization</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-dark">
                    <div class="card-body text-center">
                        <h4>${result.global_trends.highest_growth_mode}</h4>
                        <p class="mb-0">Highest Growth Mode</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <h4>${result.global_trends.market_outlook}</h4>
                        <p class="mb-0">Market Outlook</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
    `;
    
    result.market_intelligence.forEach(market => {
        html += `
            <div class="col-lg-4 mb-4">
                <div class="card h-100">
                    <div class="card-header">
                        <h6 class="mb-0">${market.region} - ${market.transport_mode.toUpperCase()}</h6>
                    </div>
                    <div class="card-body">
                        <div class="row text-center mb-3">
                            <div class="col-6">
                                <div class="metric">
                                    <h5 class="text-primary">${market.demand_index.toFixed(2)}</h5>
                                    <small>Demand Index</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="metric">
                                    <h5 class="text-success">${(market.capacity_utilization * 100).toFixed(0)}%</h5>
                                    <small>Capacity Utilization</small>
                                </div>
                            </div>
                        </div>
                        <div class="row text-center mb-3">
                            <div class="col-6">
                                <div class="metric">
                                    <h5 class="text-warning">${(market.price_volatility * 100).toFixed(0)}%</h5>
                                    <small>Price Volatility</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="metric">
                                    <h5 class="text-info">${market.competitive_density}</h5>
                                    <small>Competitors</small>
                                </div>
                            </div>
                        </div>
                        <h6>Market Insights:</h6>
                        <ul class="list-unstyled">
                            ${market.insights.map(insight => `<li><i class="fas fa-check-circle text-success me-1"></i>${insight}</li>`).join('')}
                        </ul>
                    </div>
                </div>
            </div>
        `;
    });
    
    html += '</div>';
    container.innerHTML = html;
}

// Utility functions
function showLoadingState(containerId) {
    const container = document.getElementById(containerId);
    container.innerHTML = `
        <div class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2">Generating AI analytics...</p>
        </div>
    `;
}

function showErrorMessage(containerId, message) {
    const container = document.getElementById(containerId);
    container.innerHTML = `
        <div class="alert alert-danger" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            ${message}
        </div>
    `;
}

function updateAnalyticsCards(result) {
    // Update summary cards with real data
    if (result.analytics_summary) {
        document.getElementById('insights-count').textContent = result.analytics_summary.total_insights_generated || 42;
        document.getElementById('predictions-count').textContent = '1,247'; // Keep static for demo
    }
}

function refreshAnalytics() {
    loadAIInsights();
    loadMarketIntelligence();
    loadModelPerformanceChart();
}

function displayDemandForecastSummary(result) {
    const container = document.getElementById('demand-forecast-summary');
    if (result.summary) {
        container.innerHTML = `
            <div class="card bg-light">
                <div class="card-body">
                    <h6>Forecast Summary</h6>
                    <p><strong>Avg Daily Demand:</strong> ${result.summary.avg_daily_demand}</p>
                    <p><strong>Peak Demand:</strong> ${result.summary.peak_demand}</p>
                    <p><strong>Trend:</strong> <span class="badge bg-${result.summary.trend === 'increasing' ? 'success' : 'warning'}">${result.summary.trend}</span></p>
                    <p><strong>Volatility:</strong> ${(result.summary.volatility_index * 100).toFixed(1)}%</p>
                </div>
            </div>
        `;
    }
}

function displayPricePredictionSummary(result) {
    const container = document.getElementById('price-prediction-summary');
    if (result.analysis) {
        container.innerHTML = `
            <div class="card bg-light">
                <div class="card-body">
                    <h6>Price Analysis</h6>
                    <p><strong>Avg Price:</strong> $${result.analysis.avg_predicted_price.toLocaleString()}</p>
                    <p><strong>Trend:</strong> <span class="badge bg-${result.analysis.price_trend === 'increasing' ? 'danger' : 'success'}">${result.analysis.price_trend}</span></p>
                    <p><strong>Market:</strong> ${result.analysis.market_conditions}</p>
                    <p><strong>Optimal Booking:</strong> ${result.analysis.optimal_booking_window} days</p>
                    <div class="mt-2">
                        <h6>Recommendations:</h6>
                        <ul class="list-unstyled">
                            ${result.recommendations.slice(0,2).map(rec => `<li><i class="fas fa-check text-success me-1"></i>${rec}</li>`).join('')}
                        </ul>
                    </div>
                </div>
            </div>
        `;
    }
}
</script>

<style>
.metric h5 {
    margin-bottom: 2px;
}

.metric small {
    color: #6c757d;
    font-size: 0.8rem;
}

.card {
    transition: transform 0.2s;
}

.card:hover {
    transform: translateY(-2px);
}

.nav-tabs .nav-link {
    border: none;
    color: #6c757d;
}

.nav-tabs .nav-link.active {
    background-color: #0d6efd;
    color: white;
    border: none;
}

.badge {
    font-size: 0.75rem;
}

#demand-forecast-chart, #price-prediction-chart {
    min-height: 400px;
}
</style>
{% endblock %}