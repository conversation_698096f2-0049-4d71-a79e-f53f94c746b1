{% extends "base.html" %}

{% block content %}
<div class="page-container">
    <div class="page-header">
        <h1>Manage Shipments</h1>
        <p>View and manage all your shipments in one place</p>
    </div>

    <!-- Shipment Type Tabs -->
    <div class="row mb-4">
        <div class="col-12">
            <ul class="nav nav-tabs" id="shipmentTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="regular-tab" data-bs-toggle="tab" data-bs-target="#regular-shipments" type="button" role="tab">
                        <i class="fas fa-shipping-fast me-2"></i>Regular Shipments ({{ shipments|length }})
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="private-tab" data-bs-toggle="tab" data-bs-target="#private-shipments" type="button" role="tab">
                        <i class="fas fa-lock me-2"></i>Private Bookings ({{ private_shipments|length|default:"0" }})
                    </button>
                </li>
            </ul>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="action-bar">
                <div class="action-buttons">
                    <a href="/customer/search-shipping" class="btn btn-primary">
                        <i class="fas fa-plus"></i> <span class="btn-text">Create New Shipment</span>
                    </a>
                    <button class="btn btn-outline-secondary" onclick="exportShipments()">
                        <i class="fas fa-download"></i> <span class="btn-text">Export Data</span>
                    </button>
                    <button class="btn btn-outline-secondary" onclick="bulkActions()">
                        <i class="fas fa-tasks"></i> <span class="btn-text">Bulk Actions</span>
                    </button>
                </div>
                <div class="filter-controls">
                    <select class="form-select form-select-sm" onchange="filterShipments(this.value)">
                        <option value="all" {% if current_status == 'all' %}selected{% endif %}>All Shipments</option>
                        <option value="pending" {% if current_status == 'pending' %}selected{% endif %}>Pending</option>
                        <option value="in_transit" {% if current_status == 'in_transit' %}selected{% endif %}>In Transit</option>
                        <option value="delivered" {% if current_status == 'delivered' %}selected{% endif %}>Delivered</option>
                        <option value="cancelled" {% if current_status == 'cancelled' %}selected{% endif %}>Cancelled</option>
                    </select>
                </div>
            </div>
        </div>
    </div>

    <!-- Tab Content -->
    <div class="tab-content" id="shipmentTabsContent">
        <!-- Regular Shipments Tab -->
        <div class="tab-pane fade show active" id="regular-shipments" role="tabpanel">
            <div class="row">
                <div class="col-12">
                    <div class="shipments-table-card">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>
                                            <input type="checkbox" class="form-check-input" onclick="selectAll(this)">
                                        </th>
                                        <th>Tracking Number</th>
                                        <th>Route</th>
                                <th>Logistics Provider</th>
                                <th>Status</th>
                                <th>Created Date</th>
                                <th>Est. Delivery</th>
                                <th>Total Cost</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for shipment in shipments %}
                            <tr>
                                <td>
                                    <input type="checkbox" class="form-check-input shipment-checkbox" value="{{ shipment.id }}">
                                </td>
                                <td>
                                    <div class="tracking-cell">
                                        <strong>#{{ shipment.tracking_number }}</strong>
                                        <div class="shipment-meta">{{ shipment.weight }}kg • {{ shipment.cargo_type }}</div>
                                    </div>
                                </td>
                                <td>
                                    <div class="route-cell">
                                        <div class="route-path">
                                            <i class="fas fa-map-marker-alt text-success"></i>
                                            {{ shipment.origin }}
                                            <i class="fas fa-arrow-right mx-1"></i>
                                            <i class="fas fa-map-marker-alt text-danger"></i>
                                            {{ shipment.destination }}
                                        </div>
                                        <div class="transport-type">
                                            <span class="badge bg-secondary">{{ shipment.transport_type }}</span>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="provider-cell">
                                        <strong>{{ shipment.provider_name }}</strong>
                                        <div class="provider-rating">
                                            {% if shipment.provider_rating >= 1 %}<i class="fas fa-star text-warning"></i>{% endif %}
                                            {% if shipment.provider_rating >= 2 %}<i class="fas fa-star text-warning"></i>{% endif %}
                                            {% if shipment.provider_rating >= 3 %}<i class="fas fa-star text-warning"></i>{% endif %}
                                            {% if shipment.provider_rating >= 4 %}<i class="fas fa-star text-warning"></i>{% endif %}
                                            {% if shipment.provider_rating >= 5 %}<i class="fas fa-star text-warning"></i>{% endif %}
                                            <span class="rating-text">({{ shipment.provider_rating }}/5)</span>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="status-badge status-{{ shipment.status }}">
                                        {{ shipment.status_display }}
                                    </span>
                                    {% if shipment.status == 'IN_TRANSIT' %}
                                    <div class="progress-indicator">
                                        <small>{{ shipment.progress_percentage }}% complete</small>
                                    </div>
                                    {% endif %}
                                </td>
                                <td>{{ shipment.created_date }}</td>
                                <td>{{ shipment.estimated_delivery }}</td>
                                <td>
                                    <div class="cost-cell">
                                        <strong>${{ shipment.total_cost }}</strong>
                                        <div class="payment-status">
                                            <span class="badge payment-{{ shipment.payment_status }}">
                                                {{ shipment.payment_status }}
                                            </span>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <a href="/shipment/{{ shipment.id }}" class="btn btn-sm btn-outline-primary" title="View Process Flow">
                                            <i class="fas fa-route"></i>
                                        </a>
                                        <button class="btn btn-sm btn-outline-success" onclick="trackShipment('{{ shipment.tracking_number }}')" title="Track">
                                            <i class="fas fa-route"></i>
                                        </button>
                                        {% if shipment.status == 'pending' or shipment.status == 'quote_received' %}
                                        <button class="btn btn-sm btn-outline-warning" onclick="editShipment('{{ shipment.id }}')" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-danger" onclick="cancelShipment('{{ shipment.id }}')" title="Cancel">
                                            <i class="fas fa-times"></i>
                                        </button>
                                        {% endif %}
                                        {% if shipment.payment_status == 'pending' %}
                                        <button class="btn btn-sm btn-success" onclick="makePayment('{{ shipment.id }}')" title="Pay Now">
                                            <i class="fas fa-credit-card"></i>
                                        </button>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="table-pagination">
                    <div class="pagination-info">
                        Showing {{ start_index }}-{{ end_index }} of {{ total_shipments }} shipments
                    </div>
                    <nav aria-label="Shipments pagination">
                        <ul class="pagination pagination-sm">
                            <li class="page-item {% if current_page <= 1 %}disabled{% endif %}">
                                <a class="page-link" href="?page={{ prev_page }}">Previous</a>
                            </li>
                            {% for page_num in page_range %}
                            <li class="page-item {% if page_num == current_page %}active{% endif %}">
                                <a class="page-link" href="?page={{ page_num }}">{{ page_num }}</a>
                            </li>
                            {% endfor %}
                            <li class="page-item {% if current_page >= total_pages %}disabled{% endif %}">
                                <a class="page-link" href="?page={{ next_page }}">Next</a>
                            </li>
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Private Shipments Tab -->
    <div class="tab-pane fade" id="private-shipments" role="tabpanel">
        <!-- Customs Declaration Advisory for International Shipments -->
        {% for invitation in international_private_invitations %}
        <div class="alert alert-warning customs-advisory" role="alert">
            <div class="d-flex align-items-center">
                <i class="fas fa-exclamation-triangle text-warning me-3"></i>
                <div class="flex-grow-1">
                    <h6 class="alert-heading mb-1">Customs Declaration Required</h6>
                    <p class="mb-2">Your private shipment <strong>{{ invitation.private_shipment.container_id }}</strong> ({{ invitation.private_shipment.origin_country }} → {{ invitation.private_shipment.destination_country }}) requires customs declaration.</p>
                    <div class="d-flex gap-2">
                        <a href="/customer/customs-declaration" class="btn btn-warning btn-sm">
                            <i class="fas fa-file-alt me-1"></i>Submit Declaration
                        </a>
                        <button class="btn btn-outline-warning btn-sm" onclick="dismissAdvisory(this)">
                            <i class="fas fa-times me-1"></i>Dismiss
                        </button>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
        
        <div class="row">
            <div class="col-12">
                <div class="shipments-table-card">
                    {% if private_invitations %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Container ID</th>
                                    <th>Route</th>
                                    <th>Logistics Provider</th>
                                    <th>Your Cargo</th>
                                    <th>Cost</th>
                                    <th>Status</th>
                                    <th>Customs</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for invitation in private_invitations %}
                                <tr class="private-shipment-row">
                                    <td>
                                        <div class="container-id">
                                            <strong>{{ invitation.private_shipment.container_id }}</strong>
                                            <div class="container-type">
                                                <small class="text-muted">{{ invitation.private_shipment.container_type }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="route-cell">
                                            <div class="route-main">{{ invitation.private_shipment.origin_city }}, {{ invitation.private_shipment.origin_country }}</div>
                                            <div class="route-arrow">
                                                <i class="fas fa-arrow-right text-primary"></i>
                                            </div>
                                            <div class="route-main">{{ invitation.private_shipment.destination_city }}, {{ invitation.private_shipment.destination_country }}</div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="provider-info">
                                            <strong>{{ invitation.provider_name }}</strong>
                                            <div class="provider-rating">
                                                {% if invitation.provider_rating >= 1 %}<i class="fas fa-star text-warning"></i>{% endif %}
                                                {% if invitation.provider_rating >= 2 %}<i class="fas fa-star text-warning"></i>{% endif %}
                                                {% if invitation.provider_rating >= 3 %}<i class="fas fa-star text-warning"></i>{% endif %}
                                                {% if invitation.provider_rating >= 4 %}<i class="fas fa-star text-warning"></i>{% endif %}
                                                {% if invitation.provider_rating >= 5 %}<i class="fas fa-star text-warning"></i>{% endif %}
                                                <span class="rating-text">({{ invitation.provider_rating }}/5)</span>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="cargo-details">
                                            <div><strong>{{ invitation.weight_kg }}kg</strong></div>
                                            <div><strong>{{ invitation.volume_m3 }}m³</strong></div>
                                            <small class="text-muted">{{ invitation.cargo_type }}</small>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="cost-cell">
                                            <strong>${{ invitation.cost_formatted }}</strong>
                                            {% if invitation.savings %}
                                            <div class="savings-badge">
                                                <span class="badge bg-success">Save ${{ invitation.savings_formatted }}</span>
                                            </div>
                                            {% endif %}
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge status-{{ invitation.status }}">
                                            {{ invitation.status_display }}
                                        </span>
                                    </td>
                                    <td>
                                        {% if invitation.requires_customs %}
                                        <span class="badge bg-warning">
                                            <i class="fas fa-exclamation-triangle me-1"></i>Required
                                        </span>
                                        {% else %}
                                        <span class="badge bg-success">
                                            <i class="fas fa-check me-1"></i>Not Required
                                        </span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="action-buttons">
                                            {% if invitation.status == 'pending' %}
                                            <button class="btn btn-sm btn-success" onclick="acceptInvitation('{{ invitation.id }}')" title="Accept & Pay">
                                                <i class="fas fa-check"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger" onclick="declineInvitation('{{ invitation.id }}')" title="Decline">
                                                <i class="fas fa-times"></i>
                                            </button>
                                            {% endif %}
                                            {% if invitation.status == 'accepted' %}
                                            <button class="btn btn-sm btn-outline-primary" onclick="viewContract('{{ invitation.id }}')" title="View Contract">
                                                <i class="fas fa-file-contract"></i>
                                            </button>
                                            {% endif %}
                                            <button class="btn btn-sm btn-outline-info" onclick="viewPrivateShipmentDetails('{{ invitation.id }}')" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="empty-state">
                        <div class="empty-state-icon">
                            <i class="fas fa-lock fa-3x text-muted"></i>
                        </div>
                        <h4>No Private Shipment Invitations</h4>
                        <p>You haven't received any private container sharing invitations yet.</p>
                        <a href="/customer/search-shipping" class="btn btn-primary">
                            <i class="fas fa-search me-2"></i>Search for Shipping Options
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

    <!-- Quick Stats -->
    <div class="row mt-4">
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-value">{{ stats.total_shipments }}</div>
                <div class="stat-label">Total Shipments</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-value">{{ stats.in_transit }}</div>
                <div class="stat-label">In Transit</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-value">${{ stats.total_spent }}</div>
                <div class="stat-label">Total Spent</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-value">{{ stats.delivered_rate }}%</div>
                <div class="stat-label">Delivery Rate</div>
            </div>
        </div>
    </div>
</div>

<!-- Payment Modal -->
<div class="modal fade" id="paymentModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Complete Payment</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="payment-content">
                    <!-- Payment form will be loaded here -->
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.action-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: white;
    padding: 1rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.action-buttons {
    display: flex;
    gap: 0.5rem;
}

.filter-controls {
    min-width: 200px;
}

.shipments-table-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    overflow: hidden;
}

.table th {
    background: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: #495057;
}

.tracking-cell strong {
    color: #2c3e50;
}

.shipment-meta {
    font-size: 0.8rem;
    color: #666;
}

.route-cell {
    min-width: 200px;
}

.route-path {
    margin-bottom: 0.25rem;
    font-size: 0.9rem;
}

.provider-cell strong {
    color: #2c3e50;
}

.provider-rating {
    font-size: 0.8rem;
}

.rating-text {
    margin-left: 0.25rem;
    color: #666;
}

.status-pending { background: #ffc107; color: #212529; }
.status-in_transit { background: #17a2b8; color: white; }
.status-delivered { background: #28a745; color: white; }
.status-cancelled { background: #dc3545; color: white; }

.payment-pending { background: #ffc107; color: #212529; }
.payment-paid { background: #28a745; color: white; }
.payment-failed { background: #dc3545; color: white; }

.progress-indicator {
    font-size: 0.75rem;
    color: #666;
    margin-top: 0.25rem;
}

.cost-cell strong {
    color: #27ae60;
    font-size: 1.1rem;
}

.payment-status {
    margin-top: 0.25rem;
}

.action-buttons {
    display: flex;
    gap: 0.25rem;
    flex-wrap: wrap;
}

.table-pagination {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 1rem;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .action-bar {
        padding: 0.75rem;
    }
    
    .shipments-table-card {
        font-size: 0.9rem;
    }
}

@media (max-width: 992px) {
    .action-bar {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .action-buttons {
        justify-content: center;
    }
    
    .filter-controls {
        min-width: auto;
    }
    
    .route-cell {
        min-width: 150px;
    }
    
    .table th,
    .table td {
        padding: 0.5rem 0.25rem;
    }
}

@media (max-width: 768px) {
    .action-bar {
        padding: 0.5rem;
    }
    
    .action-buttons {
        flex-direction: column;
        width: 100%;
    }
    
    .action-buttons .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }
    
    .btn-text {
        display: none;
    }
    
    .shipments-table-card {
        font-size: 0.8rem;
    }
    
    .table-responsive {
        font-size: 0.75rem;
    }
    
    .table th,
    .table td {
        padding: 0.25rem 0.125rem;
    }
    
    .route-path {
        font-size: 0.8rem;
    }
    
    .shipment-meta {
        font-size: 0.7rem;
    }
    
    .provider-rating {
        font-size: 0.7rem;
    }
    
    .progress-indicator {
        font-size: 0.65rem;
    }
    
    .cost-cell strong {
        font-size: 1rem;
    }
    
    .table-pagination {
        flex-direction: column;
        align-items: center;
        text-align: center;
    }
}

@media (max-width: 576px) {
    .action-bar {
        padding: 0.25rem;
    }
    
    .shipments-table-card {
        font-size: 0.75rem;
    }
    
    .table-responsive {
        font-size: 0.7rem;
    }
    
    .route-cell {
        min-width: 120px;
    }
    
    .route-path {
        font-size: 0.75rem;
    }
    
    .shipment-meta {
        font-size: 0.65rem;
    }
    
    .provider-rating {
        font-size: 0.65rem;
    }
    
    .progress-indicator {
        font-size: 0.6rem;
    }
    
    .cost-cell strong {
        font-size: 0.9rem;
    }
    
    .action-buttons .btn {
        font-size: 0.8rem;
        padding: 0.5rem 0.75rem;
    }
}

/* Landscape orientation adjustments for mobile */
@media (max-width: 768px) and (orientation: landscape) {
    .action-buttons {
        flex-direction: row;
        flex-wrap: wrap;
    }
    
    .action-buttons .btn {
        width: auto;
        margin-bottom: 0;
        flex: 1;
    }
    
    .table-pagination {
        flex-direction: row;
    }
}
    align-items: center;
    padding: 1rem;
    background: #f8f9fa;
    border-top: 1px solid #dee2e6;
}

.pagination-info {
    color: #666;
    font-size: 0.9rem;
}

.stat-card {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    text-align: center;
}

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: #666;
    font-size: 0.9rem;
}

@media (max-width: 768px) {
    .action-bar {
        flex-direction: column;
        gap: 1rem;
    }
    
    .action-buttons {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .table-responsive {
        font-size: 0.9rem;
    }
    
    .action-buttons .btn {
        padding: 0.25rem 0.5rem;
    }
}
</style>
{% endblock %}

{% block extra_js %}
<script>
function filterShipments(status) {
    const params = new URLSearchParams(window.location.search);
    if (status === 'all') {
        params.delete('status');
    } else {
        params.set('status', status);
    }
    window.location.search = params.toString();
}

function selectAll(checkbox) {
    const checkboxes = document.querySelectorAll('.shipment-checkbox');
    checkboxes.forEach(cb => cb.checked = checkbox.checked);
}

function viewShipment(shipmentId) {
    // Fetch shipment details and show in modal
    fetch(`/api/shipment-details/${shipmentId}`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showShipmentDetailsModal(data.shipment);
        } else {
            alert('Error loading shipment details: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Failed to load shipment details. Please try again.');
    });
}

function trackShipment(trackingNumber) {
    window.location.href = `/customer/track-shipment?tracking=${trackingNumber}`;
}

function editShipment(shipmentId) {
    window.location.href = `/customer/edit-shipment/${shipmentId}`;
}

function cancelShipment(shipmentId) {
    if (confirm('Are you sure you want to cancel this shipment?')) {
        fetch(`/customer/cancel-shipment/${shipmentId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error cancelling shipment: ' + data.message);
            }
        });
    }
}

function makePayment(shipmentId) {
    // Redirect to payment checkout with shipment ID
    window.location.href = `/customer/payment-checkout?shipment_id=${shipmentId}`;
}

function showShipmentDetailsModal(shipment) {
    const modalHtml = `
        <div class="modal fade" id="shipmentDetailsModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-box"></i> Shipment Details - ${shipment.tracking_number}
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6><i class="fas fa-route"></i> Route Information</h6>
                                <p><strong>Origin:</strong> ${shipment.origin}</p>
                                <p><strong>Destination:</strong> ${shipment.destination}</p>
                                <p><strong>Transport Type:</strong> <span class="badge bg-secondary">${shipment.transport_type}</span></p>
                                <p><strong>Distance:</strong> ${shipment.distance || 'Calculating...'}</p>
                            </div>
                            <div class="col-md-6">
                                <h6><i class="fas fa-box-open"></i> Cargo Details</h6>
                                <p><strong>Weight:</strong> ${shipment.weight}kg</p>
                                <p><strong>Cargo Type:</strong> ${shipment.cargo_type}</p>
                                <p><strong>Dimensions:</strong> ${shipment.dimensions || 'Not specified'}</p>
                                <p><strong>Description:</strong> ${shipment.description || 'Standard shipment'}</p>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <h6><i class="fas fa-truck"></i> Logistics Provider</h6>
                                <p><strong>Company:</strong> ${shipment.provider_name}</p>
                                <p><strong>Rating:</strong> 
                                    ${'★'.repeat(shipment.provider_rating)}${'☆'.repeat(5-shipment.provider_rating)} 
                                    (${shipment.provider_rating}/5)
                                </p>
                                <p><strong>Contact:</strong> ${shipment.provider_contact || 'Available after booking'}</p>
                            </div>
                            <div class="col-md-6">
                                <h6><i class="fas fa-calendar-alt"></i> Timeline</h6>
                                <p><strong>Created:</strong> ${shipment.created_date}</p>
                                <p><strong>Estimated Delivery:</strong> ${shipment.estimated_delivery}</p>
                                <p><strong>Status:</strong> <span class="status-badge status-${shipment.status.toLowerCase()}">${shipment.status_display}</span></p>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <h6><i class="fas fa-dollar-sign"></i> Pricing</h6>
                                <p><strong>Total Cost:</strong> <span class="text-success fs-5">$${shipment.total_cost}</span></p>
                                <p><strong>Payment Status:</strong> 
                                    <span class="badge payment-${shipment.payment_status.toLowerCase()}">${shipment.payment_status}</span>
                                </p>
                            </div>
                            <div class="col-md-6">
                                <h6><i class="fas fa-info-circle"></i> Additional Info</h6>
                                <p><strong>Special Requirements:</strong> ${shipment.special_requirements || 'None'}</p>
                                <p><strong>Insurance:</strong> ${shipment.insurance_coverage || 'Basic coverage'}</p>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        <button type="button" class="btn btn-primary" onclick="trackShipment('${shipment.tracking_number}')">
                            <i class="fas fa-route"></i> Track Shipment
                        </button>
                        ${shipment.payment_status === 'PENDING' ? 
                            `<button type="button" class="btn btn-success" onclick="makePayment('${shipment.id}')">
                                <i class="fas fa-credit-card"></i> Pay Now
                            </button>` : ''
                        }
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // Remove existing modal if any
    const existingModal = document.getElementById('shipmentDetailsModal');
    if (existingModal) {
        existingModal.remove();
    }
    
    // Add new modal to body
    document.body.insertAdjacentHTML('beforeend', modalHtml);
    
    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('shipmentDetailsModal'));
    modal.show();
}

function exportShipments() {
    window.location.href = '/customer/export-shipments';
}

function bulkActions() {
    const selected = document.querySelectorAll('.shipment-checkbox:checked');
    if (selected.length === 0) {
        alert('Please select at least one shipment');
        return;
    }
    
    const shipmentIds = Array.from(selected).map(cb => cb.value);
    
    // Show action menu
    const action = prompt(`Selected ${shipmentIds.length} shipments.\nChoose action:\n- cancel: Cancel selected shipments\n- export: Export selected shipments\n- mark-delivered: Mark as delivered\n\nEnter action:`);
    
    if (action && ['cancel', 'export', 'mark-delivered'].includes(action)) {
        // Send bulk action request
        fetch('/api/bulk-shipment-action', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: action,
                shipment_ids: shipmentIds
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(`${action} completed successfully for ${data.processed} shipments`);
                location.reload();
            } else {
                alert('Error processing bulk action: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error processing bulk action');
        });
    } else if (action) {
        alert('Invalid action. Please use: cancel, export, or mark-delivered');
    }
}

// Private Shipment Functions
function acceptInvitation(invitationId) {
    Swal.fire({
        title: 'Accept Private Shipment?',
        text: 'By accepting, you agree to the contract terms and will proceed to payment.',
        icon: 'question',
        showCancelButton: true,
        confirmButtonText: 'Accept & Pay',
        cancelButtonText: 'Cancel'
    }).then((result) => {
        if (result.isConfirmed) {
            // Redirect to payment form with private shipment data
            window.location.href = `/customer/payment-form?invitation_id=${invitationId}&type=private_shipment`;
        }
    });
}

function declineInvitation(invitationId) {
    Swal.fire({
        title: 'Decline Invitation?',
        text: 'This action cannot be undone.',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'Decline',
        cancelButtonText: 'Cancel'
    }).then((result) => {
        if (result.isConfirmed) {
            fetch(`/api/private-shipment/invitation/${invitationId}/decline`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire('Declined!', 'The invitation has been declined.', 'success')
                        .then(() => location.reload());
                } else {
                    Swal.fire('Error', data.error || 'Failed to decline invitation', 'error');
                }
            });
        }
    });
}

function viewContract(invitationId) {
    window.open(`/api/private-shipment/invitation/${invitationId}/contract`, '_blank');
}

function viewPrivateShipmentDetails(invitationId) {
    window.location.href = `/customer/private-shipment-details/${invitationId}`;
}

function dismissAdvisory(element) {
    element.closest('.customs-advisory').style.display = 'none';
}
</script>
{% endblock %}