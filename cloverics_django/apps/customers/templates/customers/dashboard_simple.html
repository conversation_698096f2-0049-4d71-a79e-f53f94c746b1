{% extends 'base.html' %}
{% load translation_tags %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
/* Responsive Design */
@media (max-width: 1200px) {
    .container-fluid {
        padding: 1rem;
    }
    
    .card {
        margin-bottom: 1rem;
    }
}

@media (max-width: 992px) {
    .container-fluid {
        padding: 0.75rem;
    }
    
    .dashboard-header h1 {
        font-size: 1.75rem;
    }
    
    .card-title {
        font-size: 0.9rem;
    }
    
    .card h3 {
        font-size: 1.5rem;
    }
}

@media (max-width: 768px) {
    .container-fluid {
        padding: 0.5rem;
    }
    
    .dashboard-header h1 {
        font-size: 1.5rem;
    }
    
    .dashboard-header p {
        font-size: 0.9rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .card-title {
        font-size: 0.85rem;
    }
    
    .card h3 {
        font-size: 1.25rem;
    }
    
    .table-responsive {
        font-size: 0.85rem;
    }
    
    .table th,
    .table td {
        padding: 0.5rem 0.25rem;
    }
    
    .badge {
        font-size: 0.7rem;
        padding: 0.25rem 0.5rem;
    }
}

@media (max-width: 576px) {
    .container-fluid {
        padding: 0.25rem;
    }
    
    .dashboard-header h1 {
        font-size: 1.25rem;
    }
    
    .dashboard-header p {
        font-size: 0.8rem;
    }
    
    .card-body {
        padding: 0.75rem;
    }
    
    .card-title {
        font-size: 0.8rem;
    }
    
    .card h3 {
        font-size: 1.1rem;
    }
    
    .table-responsive {
        font-size: 0.75rem;
    }
    
    .table th,
    .table td {
        padding: 0.25rem 0.125rem;
    }
    
    .badge {
        font-size: 0.65rem;
        padding: 0.2rem 0.4rem;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="dashboard-header mb-4">
                <h1 class="text-primary">🍀 {% translate 'customer_dashboard' %}</h1>
                <p class="text-muted">{% translate 'welcome_back' %}, {{ user.company_name|default:user.email }}!</p>
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-lg-3 col-md-6 col-sm-6 mb-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <h5 class="card-title">{% translate 'active_shipments' %}</h5>
                            <h3>{{ active_shipments }}</h3>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 col-sm-6 mb-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <h5 class="card-title">{% translate 'total_shipments' %}</h5>
                            <h3>{{ total_shipments }}</h3>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 col-sm-6 mb-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <h5 class="card-title">{% translate 'pending_quotes' %}</h5>
                            <h3>{{ pending_quotes }}</h3>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 col-sm-6 mb-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <h5 class="card-title">{% translate 'recent_activity' %}</h5>
                            <h3>{{ notifications|length }}</h3>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Shipments -->
            <div class="row mb-4">
                <div class="col-lg-8 col-md-12 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">{% translate 'recent_shipments' %}</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>{% translate 'shipment_id' %}</th>
                                            <th>{% translate 'route' %}</th>
                                            <th>{% translate 'status' %}</th>
                                            <th>{% translate 'date' %}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for shipment in shipments %}
                                        <tr>
                                            <td>{{ shipment.id }}</td>
                                            <td>{{ shipment.origin }} → {{ shipment.destination }}</td>
                                            <td>
                                                <span class="badge bg-{% if shipment.status == 'in_transit' %}info{% elif shipment.status == 'pending' %}warning{% else %}success{% endif %}">
                                                    {{ shipment.status|title }}
                                                </span>
                                            </td>
                                            <td>{{ shipment.created_at }}</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Notifications -->
                <div class="col-lg-4 col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">{% translate 'recent_activity' %}</h5>
                        </div>
                        <div class="card-body">
                            {% for notification in notifications %}
                            <div class="d-flex align-items-center mb-3">
                                <div class="flex-shrink-0">
                                    <i class="{{ notification.icon }} {{ notification.color }} p-2 rounded-circle text-white"></i>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <div class="fw-bold">{{ notification.message }}</div>
                                    <small class="text-muted">{{ notification.time_ago }}</small>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">{% translate 'quick_actions' %}</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <a href="/customer/search-shipping" class="btn btn-outline-primary w-100 mb-2">
                                        <i class="fas fa-search me-2"></i>{% translate 'search_shipping' %}
                                    </a>
                                </div>
                                <div class="col-md-3">
                                    <a href="/customer/track-shipment" class="btn btn-outline-success w-100 mb-2">
                                        <i class="fas fa-map-marked-alt me-2"></i>{% translate 'track_shipment' %}
                                    </a>
                                </div>
                                <div class="col-md-3">
                                    <a href="/customer/manage-shipments" class="btn btn-outline-info w-100 mb-2">
                                        <i class="fas fa-boxes me-2"></i>{% translate 'manage_shipments' %}
                                    </a>
                                </div>
                                <div class="col-md-3">
                                    <a href="/customer/pricing-calculator" class="btn btn-outline-warning w-100 mb-2">
                                        <i class="fas fa-calculator me-2"></i>{% translate 'get_quote' %}
                                    </a>   
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}