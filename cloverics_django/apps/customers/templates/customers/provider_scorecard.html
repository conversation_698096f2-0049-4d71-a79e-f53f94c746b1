<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="/static/base.css" rel="stylesheet">
    <style>
        .provider-scorecard {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            padding: 20px 0;
        }
        
        .scorecard-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            overflow: hidden;
        }
        
        .provider-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .overall-score {
            font-size: 3.5rem;
            font-weight: bold;
            margin: 15px 0;
        }
        
        .performance-tier {
            font-size: 1.3rem;
            margin-bottom: 15px;
        }
        
        .badges-display {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            justify-content: center;
            margin-top: 15px;
        }
        
        .score-badge {
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .metric-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            padding: 30px;
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            .provider-scorecard {
                padding: 15px 0;
            }
            
            .provider-header {
                padding: 25px;
            }
            
            .metric-grid {
                gap: 15px;
                padding: 25px;
            }
            
            .metric-display {
                padding: 15px;
            }
        }

        @media (max-width: 992px) {
            .provider-scorecard {
                padding: 10px 0;
            }
            
            .provider-header {
                padding: 20px;
            }
            
            .overall-score {
                font-size: 3rem;
            }
            
            .performance-tier {
                font-size: 1.1rem;
            }
            
            .metric-grid {
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 15px;
                padding: 20px;
            }
            
            .metric-display {
                padding: 15px;
            }
            
            .metric-score {
                font-size: 1.5rem;
            }
        }

        @media (max-width: 768px) {
            .provider-scorecard {
                padding: 5px 0;
            }
            
            .provider-header {
                padding: 15px;
            }
            
            .overall-score {
                font-size: 2.5rem;
            }
            
            .performance-tier {
                font-size: 1rem;
            }
            
            .metric-grid {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 10px;
                padding: 15px;
            }
            
            .metric-display {
                padding: 10px;
            }
            
            .metric-icon {
                font-size: 1.5rem;
            }
            
            .metric-score {
                font-size: 1.25rem;
            }
            
            .badges-display {
                gap: 5px;
            }
            
            .score-badge {
                font-size: 0.7rem;
                padding: 4px 8px;
            }
        }

        @media (max-width: 576px) {
            .provider-header {
                padding: 10px;
            }
            
            .overall-score {
                font-size: 2rem;
            }
            
            .performance-tier {
                font-size: 0.9rem;
            }
            
            .metric-grid {
                grid-template-columns: 1fr;
                gap: 8px;
                padding: 10px;
            }
            
            .metric-display {
                padding: 8px;
            }
            
            .metric-icon {
                font-size: 1.25rem;
            }
            
            .metric-score {
                font-size: 1.1rem;
            }
            
            .score-badge {
                font-size: 0.65rem;
                padding: 3px 6px;
            }
        }
        
        .metric-display {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            border: 1px solid #e9ecef;
        }
        
        .metric-icon {
            font-size: 2rem;
            margin-bottom: 10px;
        }
        
        .metric-score {
            font-size: 1.8rem;
            font-weight: bold;
            margin: 8px 0;
        }
        
        .metric-bar {
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .metric-fill {
            height: 100%;
            border-radius: 4px;
            transition: width 0.5s ease;
        }
        
        .provider-stats {
            background: #fff;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .stat-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .stat-row:last-child {
            border-bottom: none;
        }
        
        .contact-provider {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: 500;
            transition: transform 0.3s ease;
        }
        
        .contact-provider:hover {
            transform: translateY(-2px);
            color: white;
        }
        
        .back-button {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(255,255,255,0.2);
            border: 1px solid rgba(255,255,255,0.3);
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            text-decoration: none;
            transition: background 0.3s ease;
        }
        
        .back-button:hover {
            background: rgba(255,255,255,0.3);
            color: white;
        }
    </style>
</head>
<body>
    <div class="provider-scorecard">
        <div class="container">
            <!-- Provider Header -->
            <div class="row">
                <div class="col-12">
                    <div class="scorecard-card">
                        <div class="provider-header position-relative">
                            <a href="/dashboard" class="back-button">
                                <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
                            </a>
                            
                            <h1><i class="fas fa-building me-2"></i>{{ scorecard.provider_name }}</h1>
                            <p class="mb-3">Performance Analysis Report</p>
                            
                            <div class="overall-score">{{ scorecard.overall_score|floatformat:1 }}/5.0</div>
                            <div class="performance-tier">
                                <span class="badge bg-{% if scorecard.performance_tier == 'Excellent' %}success{% elif scorecard.performance_tier == 'Good' %}primary{% elif scorecard.performance_tier == 'Average' %}warning{% elif scorecard.performance_tier == 'Needs Improvement' %}danger{% else %}secondary{% endif %} fs-6">
                                    {{ scorecard.performance_tier }}
                                </span>
                            </div>
                            
                            <!-- Performance Badges -->
                            {% if scorecard.badges %}
                            <div class="badges-display">
                                {% for badge in scorecard.badges %}
                                <span class="score-badge bg-{{ badge.color }}">
                                    <i class="fas fa-{{ badge.icon }}"></i>
                                    {{ badge.name }}
                                </span>
                                {% endfor %}
                            </div>
                            {% endif %}
                            
                            <div class="mt-4">
                                <button class="btn contact-provider" onclick="contactProvider()">
                                    <i class="fas fa-envelope me-2"></i>Contact Provider
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Performance Metrics -->
            <div class="row">
                <div class="col-12">
                    <div class="scorecard-card">
                        <div class="card-body">
                            <h3 class="mb-4"><i class="fas fa-chart-bar me-2"></i>Performance Metrics</h3>
                            <div class="metric-grid">
                                {% for metric_key, metric_data in scorecard.metrics.items %}
                                {% if metric_key == 'on_time_delivery' %}
                                <div class="metric-display">
                                    <div class="metric-icon" style="color: #28a745">
                                        <i class="fas fa-clock"></i>
                                    </div>
                                    <h5>On-Time Delivery</h5>
                                    <div class="metric-score" style="color: #28a745">
                                        {{ metric_data.score|floatformat:1 }}/5.0
                                    </div>
                                    <div class="metric-bar">
                                        <div class="metric-fill" style="width: {{ metric_data.score|floatformat:0 }}%; background-color: #28a745;"></div>
                                    </div>
                                    <small class="text-muted">
                                        {{ metric_data.percentage }}% on-time delivery
                                    </small>
                                </div>
                                {% elif metric_key == 'customer_rating' %}
                                <div class="metric-display">
                                    <div class="metric-icon" style="color: #ffc107">
                                        <i class="fas fa-star"></i>
                                    </div>
                                    <h5>Customer Rating</h5>
                                    <div class="metric-score" style="color: #ffc107">
                                        {{ metric_data.score|floatformat:1 }}/5.0
                                    </div>
                                    <div class="metric-bar">
                                        <div class="metric-fill" style="width: {{ metric_data.score|floatformat:0 }}%; background-color: #ffc107;"></div>
                                    </div>
                                    <small class="text-muted">
                                        Average: {{ metric_data.rating|floatformat:1 }}/5.0
                                    </small>
                                </div>
                                {% elif metric_key == 'response_time' %}
                                <div class="metric-display">
                                    <div class="metric-icon" style="color: #17a2b8">
                                        <i class="fas fa-bolt"></i>
                                    </div>
                                    <h5>Response Time</h5>
                                    <div class="metric-score" style="color: #17a2b8">
                                        {{ metric_data.score|floatformat:1 }}/5.0
                                    </div>
                                    <div class="metric-bar">
                                        <div class="metric-fill" style="width: {{ metric_data.score|floatformat:0 }}%; background-color: #17a2b8;"></div>
                                    </div>
                                    <small class="text-muted">
                                        Avg: {{ metric_data.average_hours }} hours
                                    </small>
                                </div>
                                {% elif metric_key == 'completion_rate' %}
                                <div class="metric-display">
                                    <div class="metric-icon" style="color: #007bff">
                                        <i class="fas fa-check-circle"></i>
                                    </div>
                                    <h5>Completion Rate</h5>
                                    <div class="metric-score" style="color: #007bff">
                                        {{ metric_data.score|floatformat:1 }}/5.0
                                    </div>
                                    <div class="metric-bar">
                                        <div class="metric-fill" style="width: {{ metric_data.score|floatformat:0 }}%; background-color: #007bff;"></div>
                                    </div>
                                    <small class="text-muted">
                                        {{ metric_data.percentage }}% completion
                                    </small>
                                </div>
                                {% elif metric_key == 'pricing_competitiveness' %}
                                <div class="metric-display">
                                    <div class="metric-icon" style="color: #28a745">
                                        <i class="fas fa-dollar-sign"></i>
                                    </div>
                                    <h5>Pricing</h5>
                                    <div class="metric-score" style="color: #28a745">
                                        {{ metric_data.score|floatformat:1 }}/5.0
                                    </div>
                                    <div class="metric-bar">
                                        <div class="metric-fill" style="width: {{ metric_data.score|floatformat:0 }}%; background-color: #28a745;"></div>
                                    </div>
                                    <small class="text-muted">
                                        {{ metric_data.market_position }}
                                    </small>
                                </div>
                                {% elif metric_key == 'communication_quality' %}
                                <div class="metric-display">
                                    <div class="metric-icon" style="color: #6f42c1">
                                        <i class="fas fa-comments"></i>
                                    </div>
                                    <h5>Communication</h5>
                                    <div class="metric-score" style="color: #6f42c1">
                                        {{ metric_data.score|floatformat:1 }}/5.0
                                    </div>
                                    <div class="metric-bar">
                                        <div class="metric-fill" style="width: {{ metric_data.score|floatformat:0 }}%; background-color: #6f42c1;"></div>
                                    </div>
                                    <small class="text-muted">
                                        Rating: {{ metric_data.rating|floatformat:1 }}/5.0
                                    </small>
                                </div>
                                {% elif metric_key == 'reliability_score' %}
                                <div class="metric-display">
                                    <div class="metric-icon" style="color: #fd7e14">
                                        <i class="fas fa-shield-alt"></i>
                                    </div>
                                    <h5>Reliability</h5>
                                    <div class="metric-score" style="color: #fd7e14">
                                        {{ metric_data.score|floatformat:1 }}/5.0
                                    </div>
                                    <div class="metric-bar">
                                        <div class="metric-fill" style="width: {{ metric_data.score|floatformat:0 }}%; background-color: #fd7e14;"></div>
                                    </div>
                                    <small class="text-muted">
                                        {{ metric_data.consistency_level }}
                                    </small>
                                </div>
                                {% endif %}
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Provider Statistics -->
            <div class="row">
                <div class="col-lg-6">
                    <div class="scorecard-card">
                        <div class="card-body">
                            <h4><i class="fas fa-chart-pie me-2"></i>Provider Statistics</h4>
                            <div class="provider-stats">
                                <div class="stat-row">
                                    <span><i class="fas fa-shipping-fast text-primary me-2"></i>Total Shipments</span>
                                    <strong>{{ scorecard.statistics.total_shipments }}</strong>
                                </div>
                                <div class="stat-row">
                                    <span><i class="fas fa-quote-left text-success me-2"></i>Quote Requests</span>
                                    <strong>{{ scorecard.statistics.total_quotes }}</strong>
                                </div>
                                <div class="stat-row">
                                    <span><i class="fas fa-route text-warning me-2"></i>Active Routes</span>
                                    <strong>{{ scorecard.statistics.active_routes }}</strong>
                                </div>
                                <div class="stat-row">
                                    <span><i class="fas fa-users text-info me-2"></i>Customers Served</span>
                                    <strong>{{ scorecard.statistics.customer_count }}</strong>
                                </div>
                                <div class="stat-row">
                                    <span><i class="fas fa-dollar-sign text-success me-2"></i>Revenue Generated</span>
                                    <strong>${{ scorecard.statistics.revenue_generated|floatformat:2 }}</strong>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-6">
                    <div class="scorecard-card">
                        <div class="card-body">
                            <h4><i class="fas fa-trend-up me-2"></i>Performance Trends</h4>
                            {% if scorecard.trends %}
                            <div class="provider-stats">
                                <div class="stat-row">
                                    <span>Last 30 Days Rating</span>
                                    <strong>{{ scorecard.trends.last_30_days.average_rating|floatformat:1 }}/5.0</strong>
                                </div>
                                <div class="stat-row">
                                    <span>Last 60 Days Rating</span>
                                    <strong>{{ scorecard.trends.last_60_days.average_rating|floatformat:1 }}/5.0</strong>
                                </div>
                                <div class="stat-row">
                                    <span>Last 90 Days Rating</span>
                                    <strong>{{ scorecard.trends.last_90_days.average_rating|floatformat:1 }}/5.0</strong>
                                </div>
                                <div class="stat-row">
                                    <span>Performance Trend</span>
                                    <strong>
                                        {% if scorecard.trends.trend_analysis.rating_trend == 'improving' %}
                                            <span class="text-success"><i class="fas fa-arrow-up"></i> Improving</span>
                                        {% elif scorecard.trends.trend_analysis.rating_trend == 'declining' %}
                                            <span class="text-danger"><i class="fas fa-arrow-down"></i> Declining</span>
                                        {% else %}
                                            <span class="text-primary"><i class="fas fa-minus"></i> Stable</span>
                                        {% endif %}
                                    </strong>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Analysis Period Info -->
            <div class="row">
                <div class="col-12">
                    <div class="scorecard-card">
                        <div class="card-body text-center">
                            <p class="text-muted mb-0">
                                <i class="fas fa-calendar-alt me-2"></i>
                                Analysis Period: {{ scorecard.period_days }} days 
                                | Generated: {{ scorecard.calculation_date|date:"Y-m-d" }}
                                | Last Updated: {{ scorecard.calculation_date|time:"H:i" }}
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        function contactProvider() {
            Swal.fire({
                title: 'Contact {{ scorecard.provider_name }}',
                html: `
                    <div class="text-start">
                        <p><strong>Request a Quote:</strong></p>
                        <p>Click below to request a personalized quote from this provider:</p>
                        <a href="/search-shipping" class="btn btn-primary w-100 mb-3">
                            <i class="fas fa-search me-2"></i>Search & Request Quote
                        </a>
                        
                        <p><strong>View Routes:</strong></p>
                        <p>See all available routes from this provider:</p>
                        <a href="/search-shipping" class="btn btn-outline-primary w-100 mb-3">
                            <i class="fas fa-route me-2"></i>View Available Routes
                        </a>
                        
                        <p><strong>Direct Contact:</strong></p>
                        <p class="text-muted small">For direct communication, use the messaging system after requesting a quote.</p>
                    </div>
                `,
                showConfirmButton: false,
                showCloseButton: true,
                width: '500px'
            });
        }

        // Animate progress bars on load
        document.addEventListener('DOMContentLoaded', function() {
            const progressBars = document.querySelectorAll('.metric-fill');
            
            setTimeout(() => {
                progressBars.forEach(bar => {
                    const width = bar.style.width;
                    bar.style.width = '0%';
                    setTimeout(() => {
                        bar.style.width = width;
                    }, 100);
                });
            }, 500);
        });
    </script>
</body>
</html>