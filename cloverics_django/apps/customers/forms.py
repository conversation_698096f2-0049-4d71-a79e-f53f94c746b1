from django import forms
from django.core.exceptions import ValidationError
from datetime import date, timedelta
from .models import QuoteRequest, CustomerProfile
from apps.saved_searches.models import SavedSearch


class QuoteRequestForm(forms.ModelForm):
    """
    Form for requesting shipping quotes
    """
    class Meta:
        model = QuoteRequest
        fields = [
            'cargo_weight', 'cargo_dimensions', 'pickup_date', 'delivery_date', 'special_requirements'
        ]
        widgets = {
            'cargo_weight': forms.NumberInput(attrs={'class': 'form-control', 'placeholder': 'Weight in KG', 'step': '0.01'}),
            'cargo_dimensions': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Dimensions (L x W x H)'}),
            'pickup_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'delivery_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'special_requirements': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'Any special requirements or instructions'}),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Set minimum pickup date to tomorrow
        tomorrow = date.today() + timedelta(days=1)
        self.fields['pickup_date'].widget.attrs['min'] = tomorrow.strftime('%Y-%m-%d')
        
        # Set field labels and help texts
        self.fields['weight_kg'].help_text = 'Total weight of the shipment in kilograms'
        self.fields['volume_cbm'].help_text = 'Total volume in cubic meters (optional)'
        self.fields['pickup_date'].help_text = 'Earliest pickup date'
        self.fields['delivery_date'].help_text = 'Required delivery date (optional)'
    
    def clean_pickup_date(self):
        pickup_date = self.cleaned_data.get('pickup_date')
        if pickup_date and pickup_date <= date.today():
            raise ValidationError('Pickup date must be in the future.')
        return pickup_date
    
    def clean(self):
        cleaned_data = super().clean()
        pickup_date = cleaned_data.get('pickup_date')
        delivery_date = cleaned_data.get('delivery_date')
        
        if pickup_date and delivery_date:
            if delivery_date <= pickup_date:
                raise ValidationError('Delivery date must be after pickup date.')
        
        return cleaned_data


# class ShippingAddressForm(forms.ModelForm):
#     """
#     Form for managing shipping addresses
#     """
#     class Meta:
#         model = ShippingAddress
#         fields = [
#             'label', 'company_name', 'contact_person', 'phone_number', 'email',
#             'address_line_1', 'address_line_2', 'city', 'state', 'postal_code', 'country',
#             'is_pickup_address', 'is_delivery_address', 'is_default'
#         ]
#         widgets = {
#             'label': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Address Label (e.g., Home, Office)'}),
#             'company_name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Company Name (optional)'}),
#             'contact_person': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Contact Person Name'}),
#             'phone_number': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Phone Number'}),
#             'email': forms.EmailInput(attrs={'class': 'form-control', 'placeholder': 'Email Address (optional)'}),
#             'address_line_1': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Street Address'}),
#             'address_line_2': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Apartment, Suite, etc. (optional)'}),
#             'city': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'City'}),
#             'state': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'State/Province (optional)'}),
#             'postal_code': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Postal Code'}),
#             'country': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Country'}),
#             'is_pickup_address': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
#             'is_delivery_address': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
#             'is_default': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
#         }


class CustomerProfileForm(forms.ModelForm):
    """
    Form for customer profile settings
    """
    first_name = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'First Name'})
    )
    last_name = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Last Name'})
    )
    email = forms.EmailField(
        widget=forms.EmailInput(attrs={'class': 'form-control', 'placeholder': 'Email Address'})
    )
    company_name = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Company Name'})
    )
    phone_number = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Phone Number'})
    )
    
    class Meta:
        model = CustomerProfile
        fields = [
            'company_name', 'company_registration', 'address', 'phone_number', 'preferred_currency', 'credit_limit'
        ]
        widgets = {
            'company_name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Company Name'}),
            'company_registration': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Company Registration Number'}),
            'address': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'Company Address'}),
            'phone_number': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Phone Number'}),
            'preferred_currency': forms.Select(attrs={'class': 'form-select'}),
            'credit_limit': forms.NumberInput(attrs={'class': 'form-control', 'placeholder': 'Credit Limit', 'step': '0.01'}),
        }
    
    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        if user:
            self.fields['first_name'].initial = user.first_name
            self.fields['last_name'].initial = user.last_name
            self.fields['email'].initial = user.email
            self.fields['company_name'].initial = user.company_name
            self.fields['phone_number'].initial = user.phone_number
    
    def save(self, commit=True):
        instance = super().save(commit=False)
        
        # Also update the User model fields
        user = instance.user
        user.first_name = self.cleaned_data.get('first_name', '')
        user.last_name = self.cleaned_data.get('last_name', '')
        user.email = self.cleaned_data.get('email', user.email)
        user.company_name = self.cleaned_data.get('company_name', '')
        user.phone_number = self.cleaned_data.get('phone_number', '')
        
        if commit:
            user.save()
            instance.save()
        
        return instance


class SavedSearchForm(forms.ModelForm):
    """
    Form for saving search criteria with alerts
    """
    class Meta:
        model = SavedSearch
        fields = [
            'name', 'origin_country', 'destination_country', 'origin_state', 'destination_state',
            'transport_type', 'cargo_type', 'weight_min', 'weight_max', 'max_price', 'max_transit_days'
        ]
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Search Name'}),
            'origin_country': forms.Select(attrs={'class': 'form-select'}),
            'destination_country': forms.Select(attrs={'class': 'form-select'}),
            'origin_state': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Origin State (optional)'}),
            'destination_state': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Destination State (optional)'}),
            'transport_type': forms.Select(
                choices=[
                    ('', 'Any Transport Type'),
                    ('truck', 'Truck'),
                    ('ship', 'Ship'),
                    ('air', 'Air'),
                    ('rail', 'Rail'),
                ],
                attrs={'class': 'form-select'}
            ),
            'cargo_type': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Cargo Type (optional)'}),
            'weight_min': forms.NumberInput(attrs={'class': 'form-control', 'placeholder': 'Min Weight (KG)', 'step': '0.01'}),
            'weight_max': forms.NumberInput(attrs={'class': 'form-control', 'placeholder': 'Max Weight (KG)', 'step': '0.01'}),
            'max_price': forms.NumberInput(attrs={'class': 'form-control', 'placeholder': 'Maximum Price (optional)', 'step': '0.01'}),
            'max_transit_days': forms.NumberInput(attrs={'class': 'form-control', 'placeholder': 'Max Transit Days (optional)'}),
        }


class SearchForm(forms.Form):
    """
    Form for searching shipping routes
    """
    origin_country = forms.CharField(
        max_length=100,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    origin_city = forms.CharField(
        max_length=100,
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Origin City'})
    )
    origin_state = forms.CharField(
        max_length=100,
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'State (optional)'})
    )
    destination_country = forms.CharField(
        max_length=100,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    destination_city = forms.CharField(
        max_length=100,
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Destination City'})
    )
    destination_state = forms.CharField(
        max_length=100,
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'State (optional)'})
    )
    transport_mode = forms.ChoiceField(
        choices=[
            ('', 'Any Transport Mode'),
            ('truck', 'Truck'),
            ('ship', 'Ship'),
            ('air', 'Air'),
            ('rail', 'Rail'),
        ],
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    weight_kg = forms.DecimalField(
        max_digits=10,
        decimal_places=2,
        widget=forms.NumberInput(attrs={'class': 'form-control', 'placeholder': 'Weight (KG)', 'step': '0.01'})
    )
    cargo_type = forms.CharField(
        max_length=100,
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Cargo Type (optional)'})
    )
    urgency = forms.ChoiceField(
        choices=[
            ('standard', 'Standard'),
            ('express', 'Express'),
            ('urgent', 'Urgent'),
        ],
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    save_search = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input'})
    )
    search_name = forms.CharField(
        max_length=100,
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Save this search as...'})
    )


class PersonalInformationForm(forms.Form):
    """
    Form for updating personal information
    """
    first_name = forms.CharField(
        max_length=30,
        required=True,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'First Name'
        })
    )
    last_name = forms.CharField(
        max_length=30,
        required=True,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Last Name'
        })
    )
    phone = forms.CharField(
        max_length=17,
        required=True,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Phone Number'
        })
    )
    country = forms.ChoiceField(
        choices=[
            ('United States', 'United States'),
            ('Canada', 'Canada'),
            ('United Kingdom', 'United Kingdom'),
            ('Germany', 'Germany'),
            ('France', 'France'),
            ('Other', 'Other'),
        ],
        required=True,
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    timezone = forms.ChoiceField(
        choices=[
            ('UTC-5 (Eastern Time)', 'UTC-5 (Eastern Time)'),
            ('UTC-6 (Central Time)', 'UTC-6 (Central Time)'),
            ('UTC-7 (Mountain Time)', 'UTC-7 (Mountain Time)'),
            ('UTC-8 (Pacific Time)', 'UTC-8 (Pacific Time)'),
            ('UTC+0 (GMT)', 'UTC+0 (GMT)'),
            ('UTC+1 (CET)', 'UTC+1 (CET)'),
        ],
        required=True,
        widget=forms.Select(attrs={'class': 'form-control'})
    )

    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        if user:
            self.fields['first_name'].initial = user.first_name
            self.fields['last_name'].initial = user.last_name
            self.fields['phone'].initial = user.phone_number
            self.fields['country'].initial = user.country


class CompanyInformationForm(forms.Form):
    """
    Form for updating company information
    """
    company_name = forms.CharField(
        max_length=255,
        required=True,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Company Name'
        })
    )
    industry = forms.ChoiceField(
        choices=[
            ('Manufacturing', 'Manufacturing'),
            ('Retail', 'Retail'),
            ('Technology', 'Technology'),
            ('Healthcare', 'Healthcare'),
            ('Other', 'Other'),
        ],
        required=True,
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    tax_id = forms.CharField(
        max_length=50,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Tax ID (optional)'
        })
    )
    website = forms.URLField(
        required=False,
        widget=forms.URLInput(attrs={
            'class': 'form-control',
            'placeholder': 'Website (optional)'
        })
    )

    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        if user:
            self.fields['company_name'].initial = user.company_name
            self.fields['industry'].initial = user.user_type
            self.fields['tax_id'].initial = user.tax_id