"""
Customer Management Models
Extracted from FastAPI Phase 8 - Lines 1593-2194
"""

from django.db import models
from django.contrib.auth import get_user_model
import uuid

User = get_user_model()

class CustomerProfile(models.Model):
    """
    Extended customer profile information
    Extracted from FastAPI customer dashboard functionality
    """
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='customer_profile')
    company_name = models.CharField(max_length=200, blank=True, null=True)
    company_registration = models.CharField(max_length=100, blank=True, null=True)
    address = models.TextField(blank=True, null=True)
    phone_number = models.CharField(max_length=20, blank=True, null=True)
    preferred_currency = models.CharField(max_length=3, default='USD')
    credit_limit = models.DecimalField(max_digits=12, decimal_places=2, default=0.00)
    account_status = models.CharField(
        max_length=20,
        choices=[
            ('ACTIVE', 'Active'),
            ('SUSPENDED', 'Suspended'), 
            ('PENDING', 'Pending Verification'),
            ('INACTIVE', 'Inactive')
        ],
        default='ACTIVE'
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'customers_profile'
        indexes = [
            models.Index(fields=['user']),
            models.Index(fields=['account_status']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f"{self.company_name or self.user.email} - {self.account_status}"


class QuoteRequest(models.Model):
    """
    Individual quote requests from customers
    Extracted from FastAPI quote management functionality
    """
    customer = models.ForeignKey(User, on_delete=models.CASCADE, related_name='customer_quote_requests')
    quote_reference = models.CharField(max_length=50, unique=True, default=uuid.uuid4)
    route_id = models.IntegerField()  # Reference to shipping route
    
    # Cargo details
    cargo_weight = models.DecimalField(max_digits=10, decimal_places=2)
    cargo_dimensions = models.CharField(max_length=200)
    pickup_date = models.DateField()
    delivery_date = models.DateField()
    special_requirements = models.TextField(blank=True, null=True)
    
    # Status tracking
    status = models.CharField(
        max_length=20,
        choices=[
            ('PENDING', 'Pending'),
            ('QUOTED', 'Quoted'),
            ('ACCEPTED', 'Accepted'),
            ('REJECTED', 'Rejected'),
            ('EXPIRED', 'Expired')
        ],
        default='PENDING'
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'customers_quote_requests'
        indexes = [
            models.Index(fields=['customer']),
            models.Index(fields=['status']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f"Quote {self.quote_reference} - {self.customer.email}"


class PricingRequest(models.Model):
    """
    Customer pricing requests
    Extracted from FastAPI pricing calculator functionality
    """
    customer = models.ForeignKey(User, on_delete=models.CASCADE, related_name='pricing_requests')
    reference_id = models.CharField(max_length=50, unique=True, default=uuid.uuid4)
    
    # Route information
    origin_country = models.CharField(max_length=100)
    origin_city = models.CharField(max_length=100)
    origin_state = models.CharField(max_length=100, blank=True, null=True)
    destination_country = models.CharField(max_length=100)
    destination_city = models.CharField(max_length=100)
    destination_state = models.CharField(max_length=100, blank=True, null=True)
    
    # Cargo details
    weight_kg = models.DecimalField(max_digits=10, decimal_places=2)
    cargo_dimensions = models.CharField(max_length=200, blank=True, null=True)
    cargo_type = models.CharField(max_length=100, default='general')
    cargo_value_usd = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True)
    
    # Service preferences  
    transport_mode = models.CharField(
        max_length=20,
        choices=[
            ('truck', 'Truck'),
            ('train', 'Train'),
            ('ship', 'Ship'),
            ('aircraft', 'Aircraft')
        ],
        default='truck'
    )
    urgency_level = models.CharField(
        max_length=20,
        choices=[
            ('standard', 'Standard'),
            ('express', 'Express'),
            ('urgent', 'Urgent')
        ],
        default='standard'
    )
    pickup_date = models.DateField(null=True, blank=True)
    delivery_date = models.DateField(null=True, blank=True)
    
    # Requirements
    customs_required = models.BooleanField(default=True)
    insurance_required = models.BooleanField(default=True)
    special_requirements = models.TextField(blank=True, null=True)
    
    # Calculated pricing
    calculated_price = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True)
    price_breakdown = models.JSONField(default=dict, blank=True)
    
    # Status tracking
    status = models.CharField(
        max_length=20,
        choices=[
            ('PENDING', 'Pending'),
            ('CALCULATED', 'Calculated'),
            ('QUOTED', 'Quoted'),
            ('EXPIRED', 'Expired')
        ],
        default='PENDING'
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    expires_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        db_table = 'customers_pricing_request'
        indexes = [
            models.Index(fields=['customer', 'status']),
            models.Index(fields=['reference_id']),
            models.Index(fields=['created_at']),
            models.Index(fields=['expires_at']),
        ]

    def __str__(self):
        return f"Pricing Request {self.reference_id} - {self.status}"


class QuoteBatch(models.Model):
    """
    Batch quote requests for instant multi-modal quotes
    Extracted from FastAPI instant quotes functionality
    """
    customer = models.ForeignKey(User, on_delete=models.CASCADE, related_name='customer_quote_batches')
    batch_id = models.CharField(max_length=50, unique=True, default=uuid.uuid4)
    
    # Route and cargo same as PricingRequest
    origin_country = models.CharField(max_length=100)
    origin_city = models.CharField(max_length=100)
    destination_country = models.CharField(max_length=100)
    destination_city = models.CharField(max_length=100)
    weight_kg = models.DecimalField(max_digits=10, decimal_places=2)
    cargo_type = models.CharField(max_length=100, default='general')
    
    # Multi-modal options
    requested_modes = models.JSONField(default=list)  # ['truck', 'train', 'ship', 'aircraft']
    
    # Batch processing
    total_quotes_requested = models.IntegerField(default=0)
    quotes_received = models.IntegerField(default=0)
    processing_status = models.CharField(
        max_length=20,
        choices=[
            ('PROCESSING', 'Processing'),
            ('COMPLETED', 'Completed'),
            ('FAILED', 'Failed'),
            ('TIMEOUT', 'Timeout')
        ],
        default='PROCESSING'
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    completed_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        db_table = 'customers_quote_batch'
        indexes = [
            models.Index(fields=['customer', 'processing_status']),
            models.Index(fields=['batch_id']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f"Quote Batch {self.batch_id} - {self.processing_status}"


class PricingHistory(models.Model):
    """
    Historical pricing data for analytics
    Tracks all pricing calculations for customers
    """
    customer = models.ForeignKey(User, on_delete=models.CASCADE, related_name='pricing_history')
    pricing_request = models.ForeignKey(PricingRequest, on_delete=models.CASCADE, null=True, blank=True)
    
    # Route hash for duplicate detection
    route_hash = models.CharField(max_length=64, db_index=True)
    
    # Pricing data
    calculated_price = models.DecimalField(max_digits=12, decimal_places=2)
    transport_mode = models.CharField(max_length=20)
    calculation_method = models.CharField(max_length=50, default='standard')
    
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'customers_pricing_history'
        indexes = [
            models.Index(fields=['customer', 'created_at']),
            models.Index(fields=['route_hash']),
            models.Index(fields=['transport_mode']),
        ]

    def __str__(self):
        return f"Pricing History {self.customer.email} - ${self.calculated_price}"