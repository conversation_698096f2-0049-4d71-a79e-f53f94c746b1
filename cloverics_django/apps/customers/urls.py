"""
Customer URLs - Django Implementation
Phase 8: Customer Management System
URL patterns for customer functionality
"""

from django.urls import path
from . import views
from . import simple_views

app_name = 'customers'

urlpatterns = [
    # Customer dashboard - Working configuration
    path('dashboard/', simple_views.dashboard, name='dashboard'),
    
    # Search shipping routes - NEW
    path('search-shipping/', views.search_shipping, name='search_shipping'),
    
    # Shipping & Logistics Group
    path('track-shipment/', simple_views.track_shipment, name='track_shipment'),
    path('manage-shipments/', simple_views.manage_shipments, name='manage_shipments'),
    path('instant-multi-modal-quotes/', simple_views.instant_multi_modal_quotes, name='instant_multi_modal_quotes'),
    path('multi-modal-optimizer/', simple_views.multi_modal_optimizer, name='multi_modal_optimizer'),
    path('pricing-calculator/', simple_views.pricing_calculator, name='pricing_calculator'),
    
    # Intelligence & Analytics Group
    path('market-intelligence/', simple_views.market_intelligence, name='market_intelligence'),
    path('ai-analytics/', simple_views.ai_analytics, name='ai_analytics'),
    path('logistics-index/', simple_views.logistics_index, name='logistics_index'),
    
    # Business Operations Group
    path('payment-checkout/', simple_views.payment_checkout, name='payment_checkout'),
    path('invoice-reconciliation/', simple_views.invoice_reconciliation, name='invoice_reconciliation'),
    path('rfq-automation/', simple_views.rfq_automation, name='rfq_automation'),
    path('saved-searches/', views.customer_saved_searches, name='saved_searches'),
    
    # Documents & Compliance Group
    path('customs-declaration/', simple_views.customs_declaration, name='customs_declaration'),
    path('document-automation/', simple_views.document_automation, name='document_automation'),
    path('contract-lifecycle/', simple_views.contract_lifecycle, name='contract_lifecycle'),
    path('compliance-automation/', simple_views.compliance_automation, name='compliance_automation'),
    path('file-export-center/', simple_views.file_export_center, name='file_export_center'),
    
    # Account Group
    path('profile/', views.profile, name='profile'),
    path('profile/update-personal/', views.update_personal_information, name='update_personal_information'),
    path('profile/update-company/', views.update_company_information, name='update_company_information'),
    path('profile/change-password/', views.change_password, name='change_password'),
    path('profile/update-preferences/', views.update_preferences, name='update_preferences'),
    path('messages/', simple_views.messages, name='messages'),
    path('contact-support/', simple_views.contact_support, name='contact_support'),
    
    # Quote management - Working views
    path('quotes/<int:quote_id>/', views.get_quote_details, name='quote_details'),
    path('quotes/<int:quote_id>/accept/', views.accept_quote, name='accept_quote'),
    path('quotes/<int:quote_id>/reject/', views.reject_quote, name='reject_quote'),
    
    # Provider scorecard - Working view
    path('provider-scorecard/<int:provider_id>/', views.customer_view_provider_scorecard, name='provider_scorecard'),
    
    # Route and quote management
    path('route-details/<int:route_id>/', simple_views.route_details, name='route_details'),
    path('request-quote/<int:route_id>/', simple_views.request_quote, name='request_quote'),
    
    # Shipment management
    path('shipment-details/<int:shipment_id>/', simple_views.shipment_details, name='shipment_details'),
    path('edit-shipment/<int:shipment_id>/', simple_views.edit_shipment, name='edit_shipment'),
    
    # Real-time notifications - Working view
    path('api/notifications/realtime/', views.get_realtime_notifications_api, name='realtime_notifications'),
]