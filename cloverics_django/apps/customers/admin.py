"""
Customer Admin - Django Implementation
Phase 8: Customer Management System
"""

from django.contrib import admin
from .models import CustomerProfile, PricingRequest, QuoteBatch, PricingHistory

@admin.register(CustomerProfile)
class CustomerProfileAdmin(admin.ModelAdmin):
    list_display = ['user', 'company_name', 'account_status', 'credit_limit', 'created_at']
    list_filter = ['account_status', 'preferred_currency', 'created_at']
    search_fields = ['user__email', 'company_name', 'company_registration']
    readonly_fields = ['created_at', 'updated_at']
    ordering = ['-created_at']

@admin.register(PricingRequest)
class PricingRequestAdmin(admin.ModelAdmin):
    list_display = ['reference_id', 'customer', 'origin_country', 'destination_country', 'status', 'calculated_price', 'created_at']
    list_filter = ['status', 'transport_mode', 'urgency_level', 'created_at']
    search_fields = ['reference_id', 'customer__email', 'origin_country', 'destination_country']
    readonly_fields = ['reference_id', 'calculated_price', 'price_breakdown', 'created_at', 'updated_at']
    ordering = ['-created_at']

@admin.register(QuoteBatch)
class QuoteBatchAdmin(admin.ModelAdmin):
    list_display = ['batch_id', 'customer', 'origin_country', 'destination_country', 'processing_status', 'quotes_received', 'created_at']
    list_filter = ['processing_status', 'created_at']
    search_fields = ['batch_id', 'customer__email', 'origin_country', 'destination_country']
    readonly_fields = ['batch_id', 'requested_modes', 'created_at', 'completed_at']
    ordering = ['-created_at']

@admin.register(PricingHistory)
class PricingHistoryAdmin(admin.ModelAdmin):
    list_display = ['customer', 'transport_mode', 'calculated_price', 'calculation_method', 'created_at']
    list_filter = ['transport_mode', 'calculation_method', 'created_at']
    search_fields = ['customer__email', 'route_hash']
    readonly_fields = ['route_hash', 'created_at']
    ordering = ['-created_at']