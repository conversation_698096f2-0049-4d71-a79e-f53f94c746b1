# Generated by Django 5.2.1 on 2025-08-07 12:45

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='PricingRequest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('reference_id', models.Char<PERSON>ield(default=uuid.uuid4, max_length=50, unique=True)),
                ('origin_country', models.CharField(max_length=100)),
                ('origin_city', models.CharField(max_length=100)),
                ('origin_state', models.CharField(blank=True, max_length=100, null=True)),
                ('destination_country', models.CharField(max_length=100)),
                ('destination_city', models.Char<PERSON>ield(max_length=100)),
                ('destination_state', models.CharField(blank=True, max_length=100, null=True)),
                ('weight_kg', models.DecimalField(decimal_places=2, max_digits=10)),
                ('cargo_dimensions', models.CharField(blank=True, max_length=200, null=True)),
                ('cargo_type', models.CharField(default='general', max_length=100)),
                ('cargo_value_usd', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True)),
                ('transport_mode', models.CharField(choices=[('truck', 'Truck'), ('train', 'Train'), ('ship', 'Ship'), ('aircraft', 'Aircraft')], default='truck', max_length=20)),
                ('urgency_level', models.CharField(choices=[('standard', 'Standard'), ('express', 'Express'), ('urgent', 'Urgent')], default='standard', max_length=20)),
                ('pickup_date', models.DateField(blank=True, null=True)),
                ('delivery_date', models.DateField(blank=True, null=True)),
                ('customs_required', models.BooleanField(default=True)),
                ('insurance_required', models.BooleanField(default=True)),
                ('special_requirements', models.TextField(blank=True, null=True)),
                ('calculated_price', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True)),
                ('price_breakdown', models.JSONField(blank=True, default=dict)),
                ('status', models.CharField(choices=[('PENDING', 'Pending'), ('CALCULATED', 'Calculated'), ('QUOTED', 'Quoted'), ('EXPIRED', 'Expired')], default='PENDING', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('expires_at', models.DateTimeField(blank=True, null=True)),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='pricing_requests', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'customers_pricing_request',
            },
        ),
        migrations.CreateModel(
            name='PricingHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('route_hash', models.CharField(db_index=True, max_length=64)),
                ('calculated_price', models.DecimalField(decimal_places=2, max_digits=12)),
                ('transport_mode', models.CharField(max_length=20)),
                ('calculation_method', models.CharField(default='standard', max_length=50)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='pricing_history', to=settings.AUTH_USER_MODEL)),
                ('pricing_request', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='customers.pricingrequest')),
            ],
            options={
                'db_table': 'customers_pricing_history',
            },
        ),
        migrations.CreateModel(
            name='QuoteBatch',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('batch_id', models.CharField(default=uuid.uuid4, max_length=50, unique=True)),
                ('origin_country', models.CharField(max_length=100)),
                ('origin_city', models.CharField(max_length=100)),
                ('destination_country', models.CharField(max_length=100)),
                ('destination_city', models.CharField(max_length=100)),
                ('weight_kg', models.DecimalField(decimal_places=2, max_digits=10)),
                ('cargo_type', models.CharField(default='general', max_length=100)),
                ('requested_modes', models.JSONField(default=list)),
                ('total_quotes_requested', models.IntegerField(default=0)),
                ('quotes_received', models.IntegerField(default=0)),
                ('processing_status', models.CharField(choices=[('PROCESSING', 'Processing'), ('COMPLETED', 'Completed'), ('FAILED', 'Failed'), ('TIMEOUT', 'Timeout')], default='PROCESSING', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='customer_quote_batches', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'customers_quote_batch',
            },
        ),
        migrations.CreateModel(
            name='QuoteRequest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quote_reference', models.CharField(default=uuid.uuid4, max_length=50, unique=True)),
                ('route_id', models.IntegerField()),
                ('cargo_weight', models.DecimalField(decimal_places=2, max_digits=10)),
                ('cargo_dimensions', models.CharField(max_length=200)),
                ('pickup_date', models.DateField()),
                ('delivery_date', models.DateField()),
                ('special_requirements', models.TextField(blank=True, null=True)),
                ('status', models.CharField(choices=[('PENDING', 'Pending'), ('QUOTED', 'Quoted'), ('ACCEPTED', 'Accepted'), ('REJECTED', 'Rejected'), ('EXPIRED', 'Expired')], default='PENDING', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='customer_quote_requests', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'customers_quote_requests',
            },
        ),
        migrations.CreateModel(
            name='CustomerProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('company_name', models.CharField(blank=True, max_length=200, null=True)),
                ('company_registration', models.CharField(blank=True, max_length=100, null=True)),
                ('address', models.TextField(blank=True, null=True)),
                ('phone_number', models.CharField(blank=True, max_length=20, null=True)),
                ('preferred_currency', models.CharField(default='USD', max_length=3)),
                ('credit_limit', models.DecimalField(decimal_places=2, default=0.0, max_digits=12)),
                ('account_status', models.CharField(choices=[('ACTIVE', 'Active'), ('SUSPENDED', 'Suspended'), ('PENDING', 'Pending Verification'), ('INACTIVE', 'Inactive')], default='ACTIVE', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='customer_profile', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'customers_profile',
                'indexes': [models.Index(fields=['user'], name='customers_p_user_id_b54751_idx'), models.Index(fields=['account_status'], name='customers_p_account_9f64c0_idx'), models.Index(fields=['created_at'], name='customers_p_created_83ca22_idx')],
            },
        ),
        migrations.AddIndex(
            model_name='pricingrequest',
            index=models.Index(fields=['customer', 'status'], name='customers_p_custome_da45aa_idx'),
        ),
        migrations.AddIndex(
            model_name='pricingrequest',
            index=models.Index(fields=['reference_id'], name='customers_p_referen_5baef7_idx'),
        ),
        migrations.AddIndex(
            model_name='pricingrequest',
            index=models.Index(fields=['created_at'], name='customers_p_created_5101ea_idx'),
        ),
        migrations.AddIndex(
            model_name='pricingrequest',
            index=models.Index(fields=['expires_at'], name='customers_p_expires_d86de8_idx'),
        ),
        migrations.AddIndex(
            model_name='pricinghistory',
            index=models.Index(fields=['customer', 'created_at'], name='customers_p_custome_9856e8_idx'),
        ),
        migrations.AddIndex(
            model_name='pricinghistory',
            index=models.Index(fields=['route_hash'], name='customers_p_route_h_754f8c_idx'),
        ),
        migrations.AddIndex(
            model_name='pricinghistory',
            index=models.Index(fields=['transport_mode'], name='customers_p_transpo_d6527c_idx'),
        ),
        migrations.AddIndex(
            model_name='quotebatch',
            index=models.Index(fields=['customer', 'processing_status'], name='customers_q_custome_19892c_idx'),
        ),
        migrations.AddIndex(
            model_name='quotebatch',
            index=models.Index(fields=['batch_id'], name='customers_q_batch_i_377469_idx'),
        ),
        migrations.AddIndex(
            model_name='quotebatch',
            index=models.Index(fields=['created_at'], name='customers_q_created_5e8082_idx'),
        ),
        migrations.AddIndex(
            model_name='quoterequest',
            index=models.Index(fields=['customer'], name='customers_q_custome_8ccd44_idx'),
        ),
        migrations.AddIndex(
            model_name='quoterequest',
            index=models.Index(fields=['status'], name='customers_q_status_4b5c8d_idx'),
        ),
        migrations.AddIndex(
            model_name='quoterequest',
            index=models.Index(fields=['created_at'], name='customers_q_created_88bc50_idx'),
        ),
    ]
