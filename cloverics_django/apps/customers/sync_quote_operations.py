"""
Sync quote operations for customer quote management
Handles quote acceptance and rejection in sync context
"""

import logging
from django.utils import timezone
from django.db import transaction
from datetime import timedelta
import uuid

logger = logging.getLogger(__name__)


def accept_quote_sync(quote_id, user):
    """
    Accept a quote and create a shipment - Sync operation
    """
    try:
        # Import models with error handling
        try:
            from shipments.models import QuoteRequest, Shipment, Route
        except ImportError:
            # Fallback to local models if shipments app not available
            from .models import QuoteRequest
            from django.contrib.auth import get_user_model
            User = get_user_model()
            
            # Create a simple shipment record in customer models
            with transaction.atomic():
                quote = QuoteRequest.objects.get(id=quote_id, customer=user)
                if quote.status != 'quoted':
                    raise ValueError("Quote is not in quoted status")
                
                quote.status = 'accepted'
                quote.save()
                
                return {
                    "shipment_id": quote.id,
                    "tracking_number": f"CL{timezone.now().strftime('%Y%m%d')}{uuid.uuid4().hex[:8].upper()}",
                    "quote_reference": quote.quote_reference
                }
        
        from django.contrib.auth import get_user_model
        
        User = get_user_model()
        
        with transaction.atomic():
            # Get the quote and verify it belongs to this customer
            quote = QuoteRequest.objects.get(id=quote_id, customer=user)
            
            if quote.status != 'quoted':
                raise ValueError("Quote is not in quoted status")
            
            # Update quote status
            quote.status = 'accepted'
            quote.save()
            
            # Create shipment - use fallback provider and route
            logistics_provider = User.objects.filter(user_type='LOGISTICS_PROVIDER').first()
            if not logistics_provider:
                raise ValueError("No logistics provider available")
            
            route = Route.objects.first()
            if not route:
                raise ValueError("No route available")
            
            # Generate tracking number
            tracking_number = f"CL{timezone.now().strftime('%Y%m%d')}{uuid.uuid4().hex[:8].upper()}"
            
            # Create shipment
            shipment = Shipment.objects.create(
                customer=user,
                logistics_provider=logistics_provider,
                route=route,
                tracking_number=tracking_number,
                status='created',
                cargo_weight=quote.cargo_weight,
                cargo_type=quote.cargo_type,
                total_price=quote.quoted_price or 0.00,
                pickup_date=timezone.now() + timedelta(days=1),
                delivery_date=timezone.now() + timedelta(days=7)
            )
            
            logger.info(f"Created shipment {shipment.id} with tracking {tracking_number}")
            
            return {
                "shipment_id": shipment.id,
                "tracking_number": tracking_number,
                "quote_reference": quote.quote_reference
            }
            
    except QuoteRequest.DoesNotExist:
        raise ValueError("Quote not found")
    except Exception as e:
        logger.error(f"Error in accept_quote_sync: {e}")
        raise ValueError(f"Failed to accept quote: {str(e)}")


def reject_quote_sync(quote_id, user):
    """
    Reject a quote request - Sync operation
    """
    try:
        # Import models with error handling
        try:
            from shipments.models import QuoteRequest
        except ImportError:
            # Fallback to local models if shipments app not available
            from .models import QuoteRequest
        
        with transaction.atomic():
            # Get the quote and verify it belongs to this customer
            quote = QuoteRequest.objects.get(id=quote_id, customer=user)
            
            if quote.status not in ['pending', 'quoted']:
                raise ValueError("Quote cannot be rejected in current status")
            
            # Update quote status
            quote.status = 'rejected'
            quote.save()
            
            logger.info(f"Rejected quote {quote_id} by user {user.id}")
            
            return {
                "quote_id": quote.id,
                "quote_reference": quote.quote_reference
            }
            
    except QuoteRequest.DoesNotExist:
        raise ValueError("Quote not found")
    except Exception as e:
        logger.error(f"Error in reject_quote_sync: {e}")
        raise ValueError(f"Failed to reject quote: {str(e)}") 