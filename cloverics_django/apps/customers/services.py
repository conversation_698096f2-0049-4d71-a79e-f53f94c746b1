"""
Customer Services
Extracted from FastAPI Phase 8 - Business Logic Layer
"""

import hashlib
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Dict, List, Optional, Tuple
from django.db import transaction
from django.db.models import Sum
from django.contrib.auth import get_user_model
from .models import CustomerProfile, PricingRequest, QuoteBatch, PricingHistory

User = get_user_model()


class CustomerPricingService:
    """
    Pricing calculation service extracted from FastAPI
    Original lines 1558-1628
    """
    
    @staticmethod
    def calculate_shipment_price(
        weight_kg: float,
        distance_km: float,
        transport_mode: str = "truck",
        customs_required: bool = True,
        urgency_level: str = "standard",
        insurance_required: bool = True
    ) -> Dict:
        """
        Smart price calculator - extracted from FastAPI lines 1558-1628
        """
        # Base rate settings
        base_per_km_rate = {
            "truck": 0.50,
            "train": 0.30,
            "ship": 0.15,
            "aircraft": 1.80
        }

        base_per_kg_rate = {
            "truck": 0.25,
            "train": 0.15,
            "ship": 0.10,
            "aircraft": 1.50
        }

        fixed_base_fee = {
            "truck": 120,
            "train": 100,
            "ship": 80,
            "aircraft": 300
        }

        # Adjustments
        customs_fee = 120 if customs_required else 0
        insurance_fee = 0.01 * (weight_kg * 2.5) if insurance_required else 0
        urgency_multiplier = {
            "standard": 1.0,
            "express": 1.2,
            "urgent": 1.5
        }

        # Safety checks
        if transport_mode not in base_per_km_rate:
            transport_mode = "truck"

        # Price calculation
        distance_cost = distance_km * base_per_km_rate[transport_mode]
        weight_cost = weight_kg * base_per_kg_rate[transport_mode]
        base_fee = fixed_base_fee[transport_mode]

        subtotal = base_fee + distance_cost + weight_cost + customs_fee + insurance_fee
        total_price = subtotal * urgency_multiplier[urgency_level]
        
        # Platform fee (0.1% markup)
        platform_fee = total_price * 0.001
        final_price = round(total_price + platform_fee, 2)

        return {
            "base_fee": round(base_fee, 2),
            "distance_cost": round(distance_cost, 2),
            "weight_cost": round(weight_cost, 2),
            "customs_fee": round(customs_fee, 2),
            "insurance_fee": round(insurance_fee, 2),
            "platform_fee": round(platform_fee, 2),
            "subtotal": round(subtotal, 2),
            "urgency_multiplier": urgency_multiplier[urgency_level],
            "total_price": round(total_price, 2),
            "final_price": final_price
        }

    @staticmethod
    def estimate_distance(origin_country: str, origin_city: str, 
                         destination_country: str, destination_city: str) -> float:
        """
        Estimate distance between locations - extracted from FastAPI lines 1630-1700
        """
        # Major city distances database (simplified)
        distance_matrix = {
            # Europe to Europe
            ("Germany", "Berlin", "France", "Paris"): 1050,
            ("Turkey", "Istanbul", "Germany", "Berlin"): 2200,
            ("United Kingdom", "London", "France", "Paris"): 460,
            ("Spain", "Madrid", "Italy", "Rome"): 1365,
            ("Poland", "Warsaw", "Czech Republic", "Prague"): 515,
            
            # Europe to Asia
            ("Turkey", "Istanbul", "Azerbaijan", "Baku"): 1800,
            ("Germany", "Berlin", "China", "Beijing"): 7500,
            ("France", "Paris", "India", "Mumbai"): 6800,
            
            # Cross-continental
            ("United States", "New York", "United Kingdom", "London"): 5550,
            ("United States", "Los Angeles", "Japan", "Tokyo"): 9600,
            ("Australia", "Sydney", "Singapore", "Singapore"): 6300,
            
            # Regional defaults
            ("Turkey", "Ankara", "Germany", "Munich"): 2100,
            ("United States", "Chicago", "Canada", "Toronto"): 520,
        }
        
        # Try exact match
        key = (origin_country, origin_city, destination_country, destination_city)
        if key in distance_matrix:
            return distance_matrix[key]
        
        # Try reverse direction
        reverse_key = (destination_country, destination_city, origin_country, origin_city)
        if reverse_key in distance_matrix:
            return distance_matrix[reverse_key]
        
        # Regional estimates
        if origin_country == destination_country:
            return 500  # Domestic shipment
        elif any(country in ["Germany", "France", "Italy", "Spain", "Poland"] 
                for country in [origin_country, destination_country]):
            return 1500  # European route
        else:
            return 3000  # International default

    @classmethod
    def create_pricing_request(cls, customer: User, request_data: Dict) -> PricingRequest:
        """
        Create a new pricing request with calculation
        """
        with transaction.atomic():
            # Calculate distance
            distance = cls.estimate_distance(
                request_data['origin_country'],
                request_data['origin_city'],
                request_data['destination_country'],
                request_data['destination_city']
            )
            
            # Calculate pricing
            pricing = cls.calculate_shipment_price(
                weight_kg=float(request_data['weight_kg']),
                distance_km=distance,
                transport_mode=request_data.get('transport_mode', 'truck'),
                customs_required=request_data.get('customs_required', True),
                urgency_level=request_data.get('urgency_level', 'standard'),
                insurance_required=request_data.get('insurance_required', True)
            )
            
            # Create pricing request
            pricing_request = PricingRequest.objects.create(
                customer=customer,
                origin_country=request_data['origin_country'],
                origin_city=request_data['origin_city'],
                origin_state=request_data.get('origin_state'),
                destination_country=request_data['destination_country'],
                destination_city=request_data['destination_city'],
                destination_state=request_data.get('destination_state'),
                weight_kg=request_data['weight_kg'],
                cargo_dimensions=request_data.get('cargo_dimensions'),
                cargo_type=request_data.get('cargo_type', 'general'),
                cargo_value_usd=request_data.get('cargo_value_usd'),
                transport_mode=request_data.get('transport_mode', 'truck'),
                urgency_level=request_data.get('urgency_level', 'standard'),
                pickup_date=request_data.get('pickup_date'),
                delivery_date=request_data.get('delivery_date'),
                customs_required=request_data.get('customs_required', True),
                insurance_required=request_data.get('insurance_required', True),
                special_requirements=request_data.get('special_requirements'),
                calculated_price=pricing['final_price'],
                price_breakdown=pricing,
                status='CALCULATED',
                expires_at=datetime.now() + timedelta(days=7)
            )
            
            # Record in pricing history
            route_hash = hashlib.md5(
                f"{request_data['origin_country']}-{request_data['origin_city']}-"
                f"{request_data['destination_country']}-{request_data['destination_city']}-"
                f"{request_data.get('transport_mode', 'truck')}".encode()
            ).hexdigest()
            
            PricingHistory.objects.create(
                customer=customer,
                pricing_request=pricing_request,
                route_hash=route_hash,
                calculated_price=pricing['final_price'],
                transport_mode=request_data.get('transport_mode', 'truck'),
                calculation_method='standard'
            )
            
            return pricing_request


class CustomerQuoteService:
    """
    Quote batch processing service
    Extracted from FastAPI instant quotes functionality
    """
    
    @classmethod
    def create_quote_batch(cls, customer: User, request_data: Dict) -> QuoteBatch:
        """
        Create a new quote batch for multi-modal quotes
        """
        quote_batch = QuoteBatch.objects.create(
            customer=customer,
            origin_country=request_data['origin_country'],
            origin_city=request_data['origin_city'],
            destination_country=request_data['destination_country'],
            destination_city=request_data['destination_city'],
            weight_kg=request_data['weight_kg'],
            cargo_type=request_data.get('cargo_type', 'general'),
            requested_modes=request_data.get('transport_modes', ['truck']),
            total_quotes_requested=len(request_data.get('transport_modes', ['truck']))
        )
        
        return quote_batch

    @classmethod
    def process_quote_batch(cls, batch_id: str) -> Dict:
        """
        Process quote batch and return results
        """
        try:
            batch = QuoteBatch.objects.get(batch_id=batch_id)
            results = []
            
            for mode in batch.requested_modes:
                # Calculate pricing for each transport mode
                distance = CustomerPricingService.estimate_distance(
                    batch.origin_country, batch.origin_city,
                    batch.destination_country, batch.destination_city
                )
                
                pricing = CustomerPricingService.calculate_shipment_price(
                    weight_kg=float(batch.weight_kg),
                    distance_km=distance,
                    transport_mode=mode,
                    customs_required=True,
                    urgency_level='standard',
                    insurance_required=True
                )
                
                results.append({
                    'transport_mode': mode,
                    'price': pricing['final_price'],
                    'estimated_days': cls._get_estimated_days(mode, distance),
                    'pricing_breakdown': pricing
                })
            
            # Update batch status
            batch.quotes_received = len(results)
            batch.processing_status = 'COMPLETED'
            batch.completed_at = datetime.now()
            batch.save()
            
            return {
                'success': True,
                'batch_id': batch_id,
                'quotes': results
            }
            
        except QuoteBatch.DoesNotExist:
            return {'success': False, 'error': 'Batch not found'}

    @staticmethod
    def _get_estimated_days(transport_mode: str, distance_km: float) -> int:
        """
        Estimate delivery days based on transport mode and distance
        """
        days_per_km = {
            'truck': 0.002,  # ~500km per day
            'train': 0.0015, # ~650km per day
            'ship': 0.01,    # ~100km per day
            'aircraft': 0.0005  # ~2000km per day
        }
        
        base_days = max(1, int(distance_km * days_per_km.get(transport_mode, 0.002)))
        
        # Add processing time
        processing_days = {
            'truck': 1,
            'train': 2,
            'ship': 3,
            'aircraft': 1
        }
        
        return base_days + processing_days.get(transport_mode, 1)


class CustomerProfileService:
    """
    Customer profile management service
    """
    
    @classmethod
    def get_or_create_profile(cls, user: User) -> CustomerProfile:
        """
        Get or create customer profile
        """
        profile, created = CustomerProfile.objects.get_or_create(
            user=user,
            defaults={
                'account_status': 'ACTIVE',
                'preferred_currency': 'USD',
                'credit_limit': Decimal('0.00')
            }
        )
        return profile

    @classmethod
    def get_customer_dashboard_data(cls, user: User) -> Dict:
        """
        Get all data needed for customer dashboard
        """
        profile = cls.get_or_create_profile(user)
        
        # Get recent pricing requests
        recent_requests = PricingRequest.objects.filter(
            customer=user
        ).order_by('-created_at')[:10]
        
        # Get quote batches
        recent_batches = QuoteBatch.objects.filter(
            customer=user
        ).order_by('-created_at')[:5]
        
        # Calculate statistics
        total_requests = PricingRequest.objects.filter(customer=user).count()
        total_spent = PricingHistory.objects.filter(customer=user).aggregate(
            total=Sum('calculated_price')
        )['total'] or Decimal('0.00')
        
        return {
            'profile': profile,
            'recent_requests': recent_requests,
            'recent_batches': recent_batches,
            'stats': {
                'total_requests': total_requests,
                'total_spent': total_spent,
                'active_quotes': recent_batches.filter(processing_status='PROCESSING').count()
            }
        }