from django.shortcuts import render

def dashboard(request):
    """
    Customer Dashboard - Simple view implementation
    Displays customer statistics and quick actions
    """
    # Create mock user object with proper user_type for sidebar navigation
    class MockUser:
        def __init__(self):
            self.user_type = 'CUSTOMER'
            self.id = 90
            self.email = '<EMAIL>'
            self.company_name = 'Demo Customer Company'
    
    context = {
        'title': 'Customer Dashboard - Cloverics',
        'user_type': 'CUSTOMER',
        'user': MockUser(),
        'active_shipments': 3,
        'total_shipments': 12,
        'pending_quotes': 2,
        'shipments': [
            {
                'id': 'CL001',
                'origin': 'Istanbul, Turkey',
                'destination': 'Berlin, Germany',
                'status': 'in_transit',
                'created_at': '2025-07-20'
            },
            {
                'id': 'CL002',
                'origin': 'Ankara, Turkey',
                'destination': 'Paris, France',
                'status': 'pending',
                'created_at': '2025-07-22'
            }
        ],
        'quotes': [
            {
                'id': 'QT001',
                'quote_reference': 'QT-2025-001',
                'provider_name': 'Express Logistics',
                'route': 'Istanbul → Berlin',
                'cargo_weight': 1250,
                'status': 'quoted',
                'created_at': '2025-07-21'
            }
        ],
        'notifications': [
            {
                'message': 'Your shipment CL001 is in transit',
                'time_ago': '2 hours ago',
                'icon': 'fas fa-truck',
                'color': 'bg-info'
            },
            {
                'message': 'Quote received from Express Logistics',
                'time_ago': '1 day ago',
                'icon': 'fas fa-quote-left',
                'color': 'bg-success'
            }
        ]
    }
    
    return render(request, 'customers/dashboard.html', context)

def track_shipment(request):
    """Track shipment view"""
    class MockUser:
        def __init__(self):
            self.user_type = 'CUSTOMER'
            self.id = 90
            self.email = '<EMAIL>'
            self.company_name = 'Demo Customer Company'
    
    # Mock tracking data
    tracking_data = {
        'tracking_number': 'CL001',
        'origin': 'Istanbul, Turkey',
        'destination': 'Berlin, Germany',
        'provider_name': 'Express Logistics',
        'status': 'in_transit',  # This will be used for CSS classes
        'status_display': 'In Transit',
        'progress': 65,
        'weight': '1,250',
        'cargo_description': 'Electronics and Machinery',
        'transport_type': 'Truck',
        'estimated_delivery': 'July 25, 2025',
        'current_location': 'Munich, Germany',
        'customs_cleared': True,
        'insurance_included': True,
        'events': [
            {
                'event': 'Shipment Picked Up',
                'date': 'July 20, 2025 - 09:30',
                'location': 'Istanbul, Turkey'
            },
            {
                'event': 'In Transit',
                'date': 'July 21, 2025 - 14:15',
                'location': 'Bucharest, Romania'
            },
            {
                'event': 'Customs Cleared',
                'date': 'July 22, 2025 - 11:45',
                'location': 'Budapest, Hungary'
            },
            {
                'event': 'In Transit',
                'date': 'July 23, 2025 - 16:20',
                'location': 'Munich, Germany'
            }
        ]
    }
    
    context = {
        'title': 'Track Shipment - Cloverics',
        'user': MockUser(),
        'tracking_data': tracking_data,
    }
    return render(request, 'customers/track_shipment.html', context)

def manage_shipments(request):
    """Manage shipments view"""
    class MockUser:
        def __init__(self):
            self.user_type = 'CUSTOMER'
            self.id = 90
            self.email = '<EMAIL>'
            self.company_name = 'Demo Customer Company'
    
    # Mock shipments data
    shipments = [
        {
            'id': 1,
            'tracking_number': 'CL001',
            'origin': 'Istanbul, Turkey',
            'destination': 'Berlin, Germany',
            'status': 'in_transit',
            'status_display': 'In Transit',
            'payment_status': 'paid',
            'total_cost': '1,250.00',
            'created_date': '2025-07-20',
            'estimated_delivery': '2025-07-25',
            'weight': '1,250',
            'cargo_type': 'Electronics',
            'transport_type': 'Truck',
            'provider_name': 'Express Logistics',
            'provider_rating': 4
        },
        {
            'id': 2,
            'tracking_number': 'CL002',
            'origin': 'Ankara, Turkey',
            'destination': 'Paris, France',
            'status': 'pending',
            'status_display': 'Pending',
            'payment_status': 'pending',
            'total_cost': '890.00',
            'created_date': '2025-07-22',
            'estimated_delivery': '2025-07-28',
            'weight': '850',
            'cargo_type': 'Machinery',
            'transport_type': 'Air',
            'provider_name': 'FastTrack Shipping',
            'provider_rating': 5
        }
    ]
    
    # Mock private shipments data
    private_shipments = [
        {
            'id': 3,
            'tracking_number': 'CL003',
            'origin': 'Istanbul, Turkey',
            'destination': 'London, UK',
            'status': 'delivered',
            'status_display': 'Delivered',
            'payment_status': 'paid',
            'total_cost': '2,100.00',
            'created_date': '2025-07-15',
            'estimated_delivery': '2025-07-20',
            'weight': '2,000',
            'cargo_type': 'Confidential Documents',
            'transport_type': 'Express',
            'provider_name': 'Secure Logistics',
            'provider_rating': 5
        }
    ]
    
    # Mock international private invitations
    international_private_invitations = [
        {
            'id': 1,
            'provider_name': 'Global Container Solutions',
            'provider_rating': 4,
            'weight_kg': '5,000',
            'volume_m3': '15.5',
            'cargo_type': 'Electronics',
            'cost': 3200.00,
            'cost_formatted': '3,200.00',
            'savings': 800.00,
            'savings_formatted': '800.00',
            'status': 'pending',
            'status_display': 'Pending',
            'requires_customs': True,
            'private_shipment': {
                'container_id': 'CONT-2025-001',
                'origin_country': 'Turkey',
                'destination_country': 'Germany',
                'origin_city': 'Istanbul',
                'destination_city': 'Berlin'
            }
        }
    ]
    
    # Mock private invitations (same as international_private_invitations for now)
    private_invitations = international_private_invitations
    
    # Mock statistics
    stats = {
        'total_shipments': 12,
        'in_transit': 3,
        'total_spent': '12,847.00',
        'delivered_rate': 87
    }
    
    # Mock pagination data
    current_status = 'all'
    current_page = 1
    total_shipments = len(shipments)
    start_index = 1
    end_index = len(shipments)
    total_pages = 1
    page_range = [1]
    prev_page = current_page - 1
    next_page = current_page + 1
    
    context = {
        'title': 'Manage Shipments - Cloverics',
        'user': MockUser(),
        'shipments': shipments,
        'private_shipments': private_shipments,
        'international_private_invitations': international_private_invitations,
        'private_invitations': private_invitations,
        'stats': stats,
        'current_status': current_status,
        'current_page': current_page,
        'total_shipments': total_shipments,
        'start_index': start_index,
        'end_index': end_index,
        'total_pages': total_pages,
        'page_range': page_range,
        'prev_page': prev_page,
        'next_page': next_page,
    }
    return render(request, 'customers/manage_shipments.html', context)

def instant_multi_modal_quotes(request):
    """Instant multi-modal quotes view"""
    class MockUser:
        def __init__(self):
            self.user_type = 'CUSTOMER'
            self.id = 90
            self.email = '<EMAIL>'
            self.company_name = 'Demo Customer Company'
    
    context = {
        'title': 'Instant Multi-Modal Quotes - Cloverics',
        'user': MockUser(),
    }
    return render(request, 'customers/instant_quotes.html', context)

def multi_modal_optimizer(request):
    """Multi-modal optimizer view"""
    class MockUser:
        def __init__(self):
            self.user_type = 'CUSTOMER'
            self.id = 90
            self.email = '<EMAIL>'
            self.company_name = 'Demo Customer Company'
    
    # Mock countries data
    countries = [
        "Afghanistan", "Albania", "Algeria", "Andorra", "Angola", "Antigua and Barbuda", "Argentina", "Armenia", "Australia", "Austria",
        "Azerbaijan", "Bahamas", "Bahrain", "Bangladesh", "Barbados", "Belarus", "Belgium", "Belize", "Benin", "Bhutan",
        "Bolivia", "Bosnia and Herzegovina", "Botswana", "Brazil", "Brunei", "Bulgaria", "Burkina Faso", "Burundi", "Cabo Verde", "Cambodia",
        "Cameroon", "Canada", "Central African Republic", "Chad", "Chile", "China", "Colombia", "Comoros", "Congo", "Costa Rica",
        "Croatia", "Cuba", "Cyprus", "Czech Republic", "Democratic Republic of the Congo", "Denmark", "Djibouti", "Dominica", "Dominican Republic", "Ecuador",
        "Egypt", "El Salvador", "Equatorial Guinea", "Eritrea", "Estonia", "Eswatini", "Ethiopia", "Fiji", "Finland", "France",
        "Gabon", "Gambia", "Georgia", "Germany", "Ghana", "Greece", "Grenada", "Guatemala", "Guinea", "Guinea-Bissau",
        "Guyana", "Haiti", "Honduras", "Hungary", "Iceland", "India", "Indonesia", "Iran", "Iraq", "Ireland",
        "Israel", "Italy", "Ivory Coast", "Jamaica", "Japan", "Jordan", "Kazakhstan", "Kenya", "Kiribati", "Kuwait",
        "Kyrgyzstan", "Laos", "Latvia", "Lebanon", "Lesotho", "Liberia", "Libya", "Liechtenstein", "Lithuania", "Luxembourg",
        "Madagascar", "Malawi", "Malaysia", "Maldives", "Mali", "Malta", "Marshall Islands", "Mauritania", "Mauritius", "Mexico",
        "Micronesia", "Moldova", "Monaco", "Mongolia", "Montenegro", "Morocco", "Mozambique", "Myanmar", "Namibia", "Nauru",
        "Nepal", "Netherlands", "New Zealand", "Nicaragua", "Niger", "Nigeria", "North Korea", "North Macedonia", "Norway", "Oman",
        "Pakistan", "Palau", "Panama", "Papua New Guinea", "Paraguay", "Peru", "Philippines", "Poland", "Portugal", "Qatar",
        "Romania", "Russia", "Rwanda", "Saint Kitts and Nevis", "Saint Lucia", "Saint Vincent and the Grenadines", "Samoa", "San Marino", "Sao Tome and Principe", "Saudi Arabia",
        "Senegal", "Serbia", "Seychelles", "Sierra Leone", "Singapore", "Slovakia", "Slovenia", "Solomon Islands", "Somalia", "South Africa",
        "South Korea", "South Sudan", "Spain", "Sri Lanka", "Sudan", "Suriname", "Sweden", "Switzerland", "Syria", "Taiwan",
        "Tajikistan", "Tanzania", "Thailand", "Timor-Leste", "Togo", "Tonga", "Trinidad and Tobago", "Tunisia", "Turkey", "Turkmenistan",
        "Tuvalu", "Uganda", "Ukraine", "United Arab Emirates", "United Kingdom", "United States", "Uruguay", "Uzbekistan", "Vanuatu", "Vatican City",
        "Venezuela", "Vietnam", "Yemen", "Zambia", "Zimbabwe"
    ]
    
    context = {
        'title': 'Multi-Modal Optimizer - Cloverics',
        'user': MockUser(),
        'countries': countries,
    }
    return render(request, 'customers/multi_modal_optimizer.html', context)

def pricing_calculator(request):
    """Pricing calculator view"""
    class MockUser:
        def __init__(self):
            self.user_type = 'CUSTOMER'
            self.id = 90
            self.email = '<EMAIL>'
            self.company_name = 'Demo Customer Company'
    
    context = {
        'title': 'Pricing Calculator - Cloverics',
        'user': MockUser(),
    }
    return render(request, 'customers/pricing_calculator.html', context)

def market_intelligence(request):
    """Market intelligence view"""
    class MockUser:
        def __init__(self):
            self.user_type = 'CUSTOMER'
            self.id = 90
            self.email = '<EMAIL>'
            self.company_name = 'Demo Customer Company'
    
    context = {
        'title': 'Market Intelligence - Cloverics',
        'user': MockUser(),
    }
    return render(request, 'customers/market_intelligence.html', context)

def ai_analytics(request):
    """AI analytics view"""
    class MockUser:
        def __init__(self):
            self.user_type = 'CUSTOMER'
            self.id = 90
            self.email = '<EMAIL>'
            self.company_name = 'Demo Customer Company'
    
    context = {
        'title': 'AI Analytics - Cloverics',
        'user': MockUser(),
    }
    return render(request, 'customers/ai_analytics.html', context)

def logistics_index(request):
    """Logistics index view"""
    class MockUser:
        def __init__(self):
            self.user_type = 'CUSTOMER'
            self.id = 90
            self.email = '<EMAIL>'
            self.company_name = 'Demo Customer Company'
    
    context = {
        'title': 'Logistics Index - Cloverics',
        'user': MockUser(),
    }
    return render(request, 'customers/logistics_index.html', context)

def payment_checkout(request):
    """Payment checkout view"""
    class MockUser:
        def __init__(self):
            self.user_type = 'customer'
            self.id = 90
            self.email = '<EMAIL>'
            self.company_name = 'Demo Customer Company'
    
    # Mock shipment data
    shipment = {
        'id': 1,
        'tracking_number': 'TRK-2025-001',
        'status': 'pending_payment',
        'status_display': 'Pending Payment',
        'origin_city': 'Istanbul',
        'origin_country': 'Turkey',
        'destination_city': 'Berlin',
        'destination_country': 'Germany',
        'cargo_type': 'Electronics',
        'weight_kg': '2,500',
        'volume_m3': '8.5',
        'total_cost': 3200.00,
        'insurance_cost': 160.00,
        'logistics_provider__company_name': 'Global Logistics Solutions',
        'logistics_provider__first_name': 'John',
        'logistics_provider__last_name': 'Smith',
        'provider_display_name': 'Global Logistics Solutions',  # Pre-calculated display name
        'estimated_delivery': '2025-02-15',
        'created_at': '2025-01-15'
    }
    
    # Mock payment data
    payment = {
        'amount': 3200.00,
        'currency': 'USD',
        'payment_method': 'credit_card',
        'base_amount': 2800.00,
        'insurance_amount': 160.00,
        'processing_fee': 120.00,
        'tax_amount': 120.00
    }
    
    # Mock amount and savings for private shipments
    amount = 3200.00
    savings = 800.00
    platform_fee = 3.20  # amount * 0.001
    total_with_fee = 3203.20  # amount + platform_fee
    
    # Mock private shipment data
    private_shipment = {
        'container_id': 'CONT-2025-001',
        'origin_city': 'Istanbul',
        'origin_country': 'Turkey',
        'destination_city': 'Berlin',
        'destination_country': 'Germany'
    }
    
    # Mock invitation data
    invitation = {
        'weight_kg': '5,000',
        'volume_m3': '15.5'
    }
    
    # Mock provider name
    provider_name = 'Global Container Solutions'
    
    # Mock payment type
    payment_type = 'shipment'  # or 'private_shipment'
    
    context = {
        'title': 'Payment Checkout - Cloverics',
        'user': MockUser(),
        'shipment': shipment,
        'payment': payment,
        'private_shipment': private_shipment,
        'invitation': invitation,
        'provider_name': provider_name,
        'payment_type': payment_type,
        'amount': amount,
        'savings': savings,
        'platform_fee': platform_fee,
        'total_with_fee': total_with_fee,
    }
    return render(request, 'customers/payment_form.html', context)

def invoice_reconciliation(request):
    """Invoice reconciliation view"""
    class MockUser:
        def __init__(self):
            self.user_type = 'customer'
            self.id = 90
            self.email = '<EMAIL>'
            self.company_name = 'Demo Customer Company'
    
    context = {
        'title': 'Invoice Reconciliation - Cloverics',
        'user': MockUser(),
    }
    return render(request, 'customers/invoice_reconciliation.html', context)

def rfq_automation(request):
    """RFQ automation view"""
    class MockUser:
        def __init__(self):
            self.user_type = 'customer'
            self.id = 90
            self.email = '<EMAIL>'
            self.company_name = 'Demo Customer Company'
    
    context = {
        'title': 'RFQ Automation - Cloverics',
        'user': MockUser(),
    }
    return render(request, 'customers/rfq_automation.html', context)

def customs_declaration(request):
    """Customs declaration view"""
    class MockUser:
        def __init__(self):
            self.user_type = 'customer'
            self.id = 90
            self.email = '<EMAIL>'
            self.company_name = 'Demo Customer Company'
    
    context = {
        'title': 'Customs Declaration - Cloverics',
        'user': MockUser(),
    }
    return render(request, 'customers/customs_declaration.html', context)

def document_automation(request):
    """Document automation view"""
    class MockUser:
        def __init__(self):
            self.user_type = 'customer'
            self.id = 90
            self.email = '<EMAIL>'
            self.company_name = 'Demo Customer Company'
    
    context = {
        'title': 'Document Automation - Cloverics',
        'user': MockUser(),
    }
    return render(request, 'customers/document_automation.html', context)

def contract_lifecycle(request):
    """Contract lifecycle view"""
    class MockUser:
        def __init__(self):
            self.user_type = 'customer'
            self.id = 90
            self.email = '<EMAIL>'
            self.company_name = 'Demo Customer Company'
    
    context = {
        'title': 'Contract Lifecycle - Cloverics',
        'user': MockUser(),
    }
    return render(request, 'customers/contract_lifecycle.html', context)

def compliance_automation(request):
    """Compliance automation view"""
    class MockUser:
        def __init__(self):
            self.user_type = 'customer'
            self.id = 90
            self.email = '<EMAIL>'
            self.company_name = 'Demo Customer Company'
    
    context = {
        'title': 'Compliance Automation - Cloverics',
        'user': MockUser(),
    }
    return render(request, 'customers/compliance_automation.html', context)

def file_export_center(request):
    """File export center view"""
    class MockUser:
        def __init__(self):
            self.user_type = 'customer'
            self.id = 90
            self.email = '<EMAIL>'
            self.company_name = 'Demo Customer Company'
    
    context = {
        'title': 'File Export Center - Cloverics',
        'user': MockUser(),
    }
    return render(request, 'customers/advanced_export_center.html', context)

def profile(request):
    """Customer profile view"""
    class MockUser:
        def __init__(self):
            self.user_type = 'customer'
            self.id = 90
            self.email = '<EMAIL>'
            self.company_name = 'Demo Customer Company'
    
    context = {
        'title': 'Profile - Cloverics',
        'user': MockUser(),
    }
    return render(request, 'customers/profile.html', context)



def messages(request):
    """Customer messages view"""
    class MockUser:
        def __init__(self):
            self.user_type = 'customer'
            self.id = 90
            self.email = '<EMAIL>'
            self.company_name = 'Demo Customer Company'
    
    context = {
        'title': 'Messages - Cloverics',
        'user': MockUser(),
    }
    return render(request, 'customers/messages.html', context)

def contact_support(request):
    """Contact support view"""
    class MockUser:
        def __init__(self):
            self.user_type = 'customer'
            self.id = 90
            self.email = '<EMAIL>'
            self.company_name = 'Demo Customer Company'
    
    context = {
        'title': 'Contact Support - Cloverics',
        'user': MockUser(),
    }
    return render(request, 'customers/support.html', context)

def route_details(request, route_id):
    """Route details view"""
    class MockUser:
        def __init__(self):
            self.user_type = 'customer'
            self.id = 90
            self.email = '<EMAIL>'
            self.company_name = 'Demo Customer Company'
    
    context = {
        'title': f'Route Details - Cloverics',
        'user': MockUser(),
        'route': {
            'id': route_id,
            'provider_name': 'Demo Provider',
            'origin': 'Istanbul, Turkey',
            'destination': 'Berlin, Germany',
            'price': 1250.00,
            'transit_time': 3,
            'transport_type': 'Truck',
            'rating': 4.5
        }
    }
    return render(request, 'customers/route_details.html', context)

def request_quote(request, route_id):
    """Request quote view"""
    class MockUser:
        def __init__(self):
            self.user_type = 'customer'
            self.id = 90
            self.email = '<EMAIL>'
            self.company_name = 'Demo Customer Company'
    
    context = {
        'title': f'Request Quote - Cloverics',
        'user': MockUser(),
        'route': {
            'id': route_id,
            'provider_name': 'Demo Provider',
            'origin': 'Istanbul, Turkey',
            'destination': 'Berlin, Germany',
            'price': 1250.00,
            'transit_time': 3,
            'transport_type': 'Truck',
            'rating': 4.5
        }
    }
    return render(request, 'customers/request_quote.html', context)

def shipment_details(request, shipment_id):
    """Shipment details view"""
    class MockUser:
        def __init__(self):
            self.user_type = 'customer'
            self.id = 90
            self.email = '<EMAIL>'
            self.company_name = 'Demo Customer Company'
    
    context = {
        'title': f'Shipment Details - Cloverics',
        'user': MockUser(),
        'shipment': {
            'id': shipment_id,
            'tracking_number': f'CL{shipment_id:03d}',
            'origin': 'Istanbul, Turkey',
            'destination': 'Berlin, Germany',
            'status': 'in_transit',
            'status_display': 'In Transit',
            'created_at': '2025-07-20'
        }
    }
    return render(request, 'customers/shipment_details.html', context)

def edit_shipment(request, shipment_id):
    """Edit shipment view"""
    class MockUser:
        def __init__(self):
            self.user_type = 'customer'
            self.id = 90
            self.email = '<EMAIL>'
            self.company_name = 'Demo Customer Company'
    
    context = {
        'title': f'Edit Shipment - Cloverics',
        'user': MockUser(),
        'shipment': {
            'id': shipment_id,
            'tracking_number': f'CL{shipment_id:03d}',
            'origin': 'Istanbul, Turkey',
            'destination': 'Berlin, Germany',
            'status': 'pending',
            'status_display': 'Pending',
            'created_at': '2025-07-20'
        }
    }
    return render(request, 'customers/edit_shipment.html', context)