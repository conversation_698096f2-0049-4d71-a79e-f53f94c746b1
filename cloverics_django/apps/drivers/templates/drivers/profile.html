{% extends "base.html" %}

{% block title %}Driver Profile - Cloverics{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Driver Profile</h1>
            <p class="text-muted">Manage your driver profile and credentials</p>
        </div>
        <div>
            <button class="btn btn-primary" onclick="editProfile()">
                <i class="fas fa-edit"></i> Edit Profile
            </button>
        </div>
    </div>

    <div class="row">
        <!-- Profile Information -->
        <div class="col-md-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Personal Information</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label><strong>Full Name:</strong></label>
                                <p>{{ profile.full_name }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label><strong>Email:</strong></label>
                                <p>{{ profile.email }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label><strong>Phone Number:</strong></label>
                                <p>{{ profile.phone_number }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label><strong>Date Joined:</strong></label>
                                <p>{{ profile.date_joined }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Vehicle Information -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Vehicle Information</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label><strong>Truck Type:</strong></label>
                                <p>{{ profile.truck_type }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label><strong>Capacity:</strong></label>
                                <p>{{ profile.capacity_kg }} kg</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Credentials -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Credentials & Licenses</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label><strong>License Number:</strong></label>
                                <p>{{ profile.license_number }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label><strong>License Expiry:</strong></label>
                                <p>{{ profile.license_expiry }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label><strong>Insurance Policy:</strong></label>
                                <p>{{ profile.insurance_policy_number }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label><strong>Insurance Expiry:</strong></label>
                                <p>{{ profile.insurance_expiry }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Profile Status -->
        <div class="col-md-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Profile Status</h6>
                </div>
                <div class="card-body text-center">
                    <div class="mb-3">
                        <h3>{{ "%.1f"|format(profile.driver_rating) }}</h3>
                        <div class="star-rating">
                            {% for i in range(5) %}
                                {% if i < profile.driver_rating %}
                                    <i class="fas fa-star text-warning"></i>
                                {% else %}
                                    <i class="far fa-star text-muted"></i>
                                {% endif %}
                            {% endfor %}
                        </div>
                        <p class="text-muted">Driver Rating</p>
                    </div>
                    
                    <div class="mb-3">
                        {% if profile.verification_status == 'approved' %}
                            <span class="badge badge-success badge-lg">
                                <i class="fas fa-check-circle"></i> Verified
                            </span>
                        {% elif profile.verification_status == 'pending' %}
                            <span class="badge badge-warning badge-lg">
                                <i class="fas fa-clock"></i> Pending
                            </span>
                        {% else %}
                            <span class="badge badge-danger badge-lg">
                                <i class="fas fa-times-circle"></i> Rejected
                            </span>
                        {% endif %}
                        <p class="text-muted mt-2">Verification Status</p>
                    </div>

                    <div class="mb-3">
                        {% if profile.can_operate_independently %}
                            <span class="badge badge-success badge-lg">
                                <i class="fas fa-truck"></i> Independent
                            </span>
                            <p class="text-muted mt-2">Can Create Routes</p>
                        {% else %}
                            <span class="badge badge-secondary badge-lg">
                                <i class="fas fa-user-friends"></i> Employed
                            </span>
                            <p class="text-muted mt-2">Under Logistics Provider</p>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                </div>
                <div class="card-body">
                    <button class="btn btn-outline-primary btn-block mb-2" onclick="updatePhoto()">
                        <i class="fas fa-camera"></i> Update Photo
                    </button>
                    <button class="btn btn-outline-info btn-block mb-2" onclick="viewDocuments()">
                        <i class="fas fa-file-alt"></i> View Documents
                    </button>
                    <button class="btn btn-outline-success btn-block mb-2" onclick="contactSupport()">
                        <i class="fas fa-headset"></i> Contact Support
                    </button>
                    {% if not profile.can_operate_independently %}
                    <button class="btn btn-outline-warning btn-block" onclick="requestIndependence()">
                        <i class="fas fa-paper-plane"></i> Request Independence
                    </button>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Edit Profile Modal -->
<div class="modal fade" id="editProfileModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Profile</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="profileForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Full Name</label>
                                <input type="text" class="form-control" id="fullName" value="{{ profile.full_name }}">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Phone Number</label>
                                <input type="text" class="form-control" id="phoneNumber" value="{{ profile.phone_number }}">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>License Number</label>
                                <input type="text" class="form-control" id="licenseNumber" value="{{ profile.license_number }}">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>License Expiry</label>
                                <input type="date" class="form-control" id="licenseExpiry" value="{{ profile.license_expiry }}">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Insurance Policy Number</label>
                                <input type="text" class="form-control" id="insurancePolicy" value="{{ profile.insurance_policy_number }}">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Insurance Expiry</label>
                                <input type="date" class="form-control" id="insuranceExpiry" value="{{ profile.insurance_expiry }}">
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="saveProfile()">Save Changes</button>
            </div>
        </div>
    </div>
</div>

<script>
function editProfile() {
    $('#editProfileModal').modal('show');
}

function saveProfile() {
    // Implement profile save functionality
    alert('Profile updated successfully!');
    $('#editProfileModal').modal('hide');
    location.reload();
}

function updatePhoto() {
    alert('Photo update functionality will be implemented');
}

function viewDocuments() {
    alert('Document viewing functionality will be implemented');
}

function contactSupport() {
    window.location.href = '/contact-support';
}

function requestIndependence() {
    if(confirm('Request independent driver status? This will be reviewed by administrators.')) {
        alert('Independence request submitted for review!');
    }
}
</script>

<style>
.badge-lg {
    font-size: 14px;
    padding: 8px 12px;
}

.star-rating {
    font-size: 18px;
    margin: 5px 0;
}

.card {
    border: none;
    border-radius: 10px;
}
</style>
{% endblock %}