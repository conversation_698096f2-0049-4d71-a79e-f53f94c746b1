{% extends "base.html" %}

{% block content %}
<div class="dashboard-container">
    <div class="dashboard-header">
        <h1>Available Jobs</h1>
        <p>Browse and accept available shipping jobs in your area</p>
    </div>

    <!-- Filter Section -->
    <div class="card mb-4">
        <div class="card-header">
            <h5>Filter Jobs</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <select class="form-select">
                        <option>All Cargo Types</option>
                        <option>Electronics</option>
                        <option>General Cargo</option>
                        <option>Refrigerated</option>
                        <option>Hazardous</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <select class="form-select">
                        <option>All Distances</option>
                        <option>Under 200 km</option>
                        <option>200-500 km</option>
                        <option>500+ km</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <select class="form-select">
                        <option>All Priorities</option>
                        <option>High Priority</option>
                        <option>Standard</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <button class="btn btn-primary" onclick="applyJobFilters()">Apply Filters</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Available Jobs List -->
    <div class="row">
        {% for job in available_jobs %}
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6>{{ job.id }}</h6>
                    {% if job.priority == "High" %}
                        <span class="badge bg-danger">High Priority</span>
                    {% else %}
                        <span class="badge bg-secondary">{{ job.priority }}</span>
                    {% endif %}
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-6">
                            <strong>From:</strong><br>
                            <small>{{ job.origin }}</small>
                        </div>
                        <div class="col-6">
                            <strong>To:</strong><br>
                            <small>{{ job.destination }}</small>
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-6">
                            <strong>Cargo:</strong><br>
                            <small>{{ job.cargo_type }} - {{ job.weight }}</small>
                        </div>
                        <div class="col-6">
                            <strong>Distance:</strong><br>
                            <small>{{ job.distance }}</small>
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-6">
                            <strong>Pickup:</strong><br>
                            <small>{{ job.pickup_date }}</small>
                        </div>
                        <div class="col-6">
                            <strong>Delivery:</strong><br>
                            <small>{{ job.delivery_date }}</small>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="payment-amount">
                            <h4 class="text-success">${{ job.payment }}</h4>
                        </div>
                        <button class="btn btn-success" onclick="acceptJob(this)">Accept Job</button>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
</div>
{% endblock %}