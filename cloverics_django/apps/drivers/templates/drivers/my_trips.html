{% extends "base.html" %}

{% block content %}
<div class="dashboard-container">
    <div class="dashboard-header">
        <h1>My Trips</h1>
        <p>Track your current and completed trips</p>
    </div>

    <!-- Current Trips -->
    <div class="card mb-4">
        <div class="card-header">
            <h5>Current Trips</h5>
        </div>
        <div class="card-body">
            {% if current_trips %}
                {% for trip in current_trips %}
                <div class="trip-card mb-3 p-3 border rounded">
                    <div class="row">
                        <div class="col-md-8">
                            <h6>{{ trip.id }} - {{ trip.route }}</h6>
                            <p class="mb-1"><strong>Cargo:</strong> {{ trip.cargo_type }}</p>
                            <p class="mb-1"><strong>Pickup:</strong> {{ trip.pickup_time }}</p>
                            <p class="mb-1"><strong>Delivery:</strong> {{ trip.delivery_time }}</p>
                        </div>
                        <div class="col-md-4 text-end">
                            <span class="badge bg-info mb-2">{{ trip.status }}</span>
                            <h5 class="text-success">${{ trip.payment }}</h5>
                            <div class="progress mt-2">
                                <div class="progress-bar" style="width: {{ trip.progress }}%"></div>
                            </div>
                            <small>{{ trip.progress }}% Complete</small>
                        </div>
                    </div>
                </div>
                {% endfor %}
            {% else %}
                <p class="text-muted">No current trips</p>
            {% endif %}
        </div>
    </div>

    <!-- Completed Trips -->
    <div class="card">
        <div class="card-header">
            <h5>Completed Trips</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Trip ID</th>
                            <th>Route</th>
                            <th>Cargo Type</th>
                            <th>Completed</th>
                            <th>Distance</th>
                            <th>Payment</th>
                            <th>Rating</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for trip in completed_trips %}
                        <tr>
                            <td>{{ trip.id }}</td>
                            <td>{{ trip.route }}</td>
                            <td>{{ trip.cargo_type }}</td>
                            <td>{{ trip.completed_date }}</td>
                            <td>{{ trip.distance }}</td>
                            <td>${{ trip.payment }}</td>
                            <td>
                                {% for i in range(trip.rating) %}
                                    <i class="fas fa-star text-warning"></i>
                                {% endfor %}
                                {% for i in range(5 - trip.rating) %}
                                    <i class="far fa-star text-muted"></i>
                                {% endfor %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}