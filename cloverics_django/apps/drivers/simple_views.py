from django.shortcuts import render

def dashboard(request):
    """
    Independent Driver Dashboard
    Displays driver statistics, jobs, and earnings
    """
    # Create mock user object with proper user_type for sidebar navigation
    class MockUser:
        def __init__(self):
            self.user_type = 'INDEPENDENT_DRIVER'
            self.id = 83
            self.email = '<EMAIL>'
            self.company_name = 'Independent Driver'
    
    context = {
        'title': 'Driver Dashboard - Cloverics',
        'user_type': 'INDEPENDENT_DRIVER',
        'user': MockUser(),
        'driver_stats': {
            'available_jobs': 8,
            'completed_deliveries': 156,
            'monthly_earnings': 4200.00,
            'driver_rating': 4.8,
            'total_distance': '12,450 km',
            'fuel_efficiency': '8.5 L/100km'
        },
        'available_jobs': [
            {
                'id': 'JB445K789L',
                'route': 'Istanbul → Ankara',
                'cargo': 'Electronics',
                'payment': 850.00,
                'distance': '450 km'
            },
            {
                'id': 'JB556M890N',
                'route': 'Izmir → Antalya',
                'cargo': 'Textiles',
                'payment': 720.00,
                'distance': '380 km'
            }
        ]
    }
    
    return render(request, 'drivers/dashboard.html', context)