"""
Django implementation of extracted driver functionality
Converting FastAPI async patterns to Django views
"""

from django.shortcuts import render, get_object_or_404, redirect
from django.http import JsonResponse, HttpResponseForbidden
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.utils import timezone
from django.core.paginator import Paginator
from django.db.models import Q, Count, Sum, Avg
from rest_framework import viewsets, status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from .models import DriverProfile, DriverJob, DriverTrip, DriverEarnings, DriverLocation
from django.contrib.auth import get_user_model

User = get_user_model(), Notification
from django.contrib.auth.decorators import user_passes_test

import logging
logger = logging.getLogger(__name__)


def driver_role_required(user):
    """Check if user is an independent driver"""
    return user.is_authenticated and user.user_type == 'INDEPENDENT_DRIVER'


# ==================== DRIVER DASHBOARD ====================
@login_required
@user_passes_test(driver_role_required)
def driver_dashboard(request):
    """Main dashboard for independent drivers"""
    try:
        driver_profile, created = DriverProfile.objects.get_or_create(
            user=request.user,
            defaults={
                'license_number': f'DL-{request.user.id:06d}',
                'license_expiry': timezone.now().date() + timezone.timedelta(days=365),
                'vehicle_type': 'box_truck',
                'max_capacity_kg': 3000
            }
        )
        
        # Get driver statistics
        total_trips = DriverTrip.objects.filter(driver=driver_profile).count()
        completed_trips = DriverTrip.objects.filter(driver=driver_profile, status='delivered').count()
        total_earnings = DriverEarnings.objects.filter(driver=driver_profile).aggregate(
            total=Sum('amount')
        )['total'] or 0
        
        # Get available jobs count
        available_jobs = DriverJob.objects.filter(status='available').count()
        
        # Get recent trips
        recent_trips = DriverTrip.objects.filter(driver=driver_profile).order_by('-start_date')[:5]
        
        context = {
            'user': request.user,
            'driver_profile': driver_profile,
            'total_trips': total_trips,
            'completed_trips': completed_trips,
            'total_earnings': total_earnings,
            'available_jobs': available_jobs,
            'recent_trips': recent_trips,
            'completion_rate': round((completed_trips / max(total_trips, 1)) * 100, 1)
        }
        
        return render(request, 'drivers/dashboard.html', context)
        
    except Exception as e:
        logger.error(f"Error in driver dashboard: {e}")
        messages.error(request, 'Error loading dashboard')
        return render(request, 'drivers/dashboard.html', {'user': request.user})


# ==================== AVAILABLE JOBS VIEW ====================
@login_required  
@user_passes_test(driver_role_required)
def driver_available_jobs(request):
    """
    Available jobs for independent drivers
    Converted from FastAPI lines 8399-8449
    """
    try:
        # Get driver profile
        driver_profile = DriverProfile.objects.get(user=request.user)
        
        # Filter jobs based on driver capacity and availability
        available_jobs = DriverJob.objects.filter(
            status='available',
            weight_kg__lte=driver_profile.max_capacity_kg
        ).order_by('-priority', '-payment_amount')
        
        # Pagination
        paginator = Paginator(available_jobs, 10)
        page_number = request.GET.get('page')
        page_obj = paginator.get_page(page_number)
        
        context = {
            'user': request.user,
            'driver_profile': driver_profile,
            'page_obj': page_obj,
            'title': 'Available Jobs - Cloverics'
        }
        
        return render(request, 'drivers/available_jobs.html', context)
        
    except DriverProfile.DoesNotExist:
        messages.error(request, 'Driver profile not found. Please complete your profile setup.')
        return redirect('drivers:dashboard')
    except Exception as e:
        logger.error(f"Error loading available jobs: {e}")
        messages.error(request, 'Error loading available jobs')
        return render(request, 'drivers/available_jobs.html', {'user': request.user})


# ==================== DRIVER TRIPS VIEW ====================
@login_required
@user_passes_test(driver_role_required)
def driver_my_trips(request):
    """
    Driver's trip history and current trips
    Converted from FastAPI lines 8451-8500+
    """
    try:
        driver_profile = DriverProfile.objects.get(user=request.user)
        
        # Get current trips (in progress)
        current_trips = DriverTrip.objects.filter(
            driver=driver_profile,
            status__in=['scheduled', 'in_transit']
        ).order_by('-start_date')
        
        # Get completed trips
        completed_trips = DriverTrip.objects.filter(
            driver=driver_profile,
            status='delivered'
        ).order_by('-end_date')
        
        # Trip statistics
        total_distance = DriverTrip.objects.filter(driver=driver_profile).aggregate(
            total=Sum('job__distance_km')
        )['total'] or 0
        
        context = {
            'user': request.user,
            'driver_profile': driver_profile,
            'current_trips': current_trips,
            'completed_trips': completed_trips,
            'total_distance': total_distance,
            'title': 'My Trips - Cloverics'
        }
        
        return render(request, 'drivers/my_trips.html', context)
        
    except DriverProfile.DoesNotExist:
        messages.error(request, 'Driver profile not found')
        return redirect('drivers:dashboard')
    except Exception as e:
        logger.error(f"Error loading trips: {e}")
        messages.error(request, 'Error loading trip history')
        return render(request, 'drivers/my_trips.html', {'user': request.user})


# ==================== DRIVER EARNINGS VIEW ====================
@login_required
@user_passes_test(driver_role_required)
def driver_earnings(request):
    """Driver earnings and payment history"""
    try:
        driver_profile = DriverProfile.objects.get(user=request.user)
        
        # Get earnings data
        earnings = DriverEarnings.objects.filter(driver=driver_profile).order_by('-payment_date')
        
        # Calculate totals
        total_earned = earnings.aggregate(total=Sum('amount'))['total'] or 0
        pending_payments = earnings.filter(payment_status='pending').aggregate(
            total=Sum('amount')
        )['total'] or 0
        
        # Monthly earnings (last 12 months)
        from django.db.models import Extract
        monthly_earnings = earnings.values(
            month=Extract('payment_date', 'month'),
            year=Extract('payment_date', 'year')
        ).annotate(total=Sum('amount')).order_by('-year', '-month')[:12]
        
        context = {
            'user': request.user,
            'driver_profile': driver_profile,
            'earnings': earnings,
            'total_earned': total_earned,
            'pending_payments': pending_payments,
            'monthly_earnings': monthly_earnings,
            'title': 'My Earnings - Cloverics'
        }
        
        return render(request, 'drivers/earnings.html', context)
        
    except DriverProfile.DoesNotExist:
        messages.error(request, 'Driver profile not found')
        return redirect('drivers:dashboard')
    except Exception as e:
        logger.error(f"Error loading earnings: {e}")
        messages.error(request, 'Error loading earnings data')
        return render(request, 'drivers/earnings.html', {'user': request.user})


# ==================== JOB ACCEPTANCE API ====================
@api_view(['POST'])
@permission_classes([IsAuthenticated])
def accept_job(request, job_id):
    """Accept an available job"""
    if request.user.user_type != 'INDEPENDENT_DRIVER':
        return Response({'error': 'Access denied'}, status=status.HTTP_403_FORBIDDEN)
    
    try:
        driver_profile = DriverProfile.objects.get(user=request.user)
        job = DriverJob.objects.get(id=job_id, status='available')
        
        # Check driver availability and capacity
        if not driver_profile.is_available:
            return Response({
                'error': 'Driver not available'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        if job.weight_kg > driver_profile.max_capacity_kg:
            return Response({
                'error': 'Job exceeds vehicle capacity'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Assign job to driver
        job.assigned_driver = driver_profile
        job.status = 'assigned'
        job.save()
        
        # Create trip record
        trip = DriverTrip.objects.create(
            trip_number=f'TRP-{timezone.now().strftime("%Y%m%d")}-{job.id:04d}',
            driver=driver_profile,
            job=job,
            origin_location=job.origin_address,
            destination_location=job.destination_address,
            cargo_type=job.cargo_type,
            earnings=job.payment_amount,
            start_date=job.pickup_date,
            status='scheduled'
        )
        
        return Response({
            'success': True,
            'message': 'Job accepted successfully',
            'trip_id': trip.id
        })
        
    except DriverProfile.DoesNotExist:
        return Response({
            'error': 'Driver profile not found'
        }, status=status.HTTP_404_NOT_FOUND)
    except DriverJob.DoesNotExist:
        return Response({
            'error': 'Job not found or no longer available'
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        logger.error(f"Error accepting job: {e}")
        return Response({
            'error': 'Failed to accept job'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# ==================== LOCATION UPDATE API ====================
@api_view(['POST'])
@permission_classes([IsAuthenticated])
def update_location(request):
    """Update driver's current location"""
    if request.user.user_type != 'INDEPENDENT_DRIVER':
        return Response({'error': 'Access denied'}, status=status.HTTP_403_FORBIDDEN)
    
    try:
        driver_profile = DriverProfile.objects.get(user=request.user)
        latitude = request.data.get('latitude')
        longitude = request.data.get('longitude')
        address = request.data.get('address', '')
        
        if not latitude or not longitude:
            return Response({
                'error': 'Latitude and longitude required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Update driver location
        DriverLocation.objects.create(
            driver=driver_profile,
            latitude=latitude,
            longitude=longitude,
            address=address
        )
        
        return Response({
            'success': True,
            'message': 'Location updated successfully'
        })
        
    except DriverProfile.DoesNotExist:
        return Response({
            'error': 'Driver profile not found'
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        logger.error(f"Error updating location: {e}")
        return Response({
            'error': 'Failed to update location'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)