"""
Driver models for the logistics platform
"""

from django.db import models
from django.conf import settings
from django.utils import timezone

class DriverProfile(models.Model):
    """Driver profile model for managing driver information"""
    
    # Verification Status Choices
    VERIFICATION_STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
    ]
    
    VEHICLE_TYPES = [
        ('BOX_TRUCK', 'Box Truck'),
        ('FLATBED', 'Flatbed'),
        ('REFRIGERATED', 'Refrigerated'),
        ('VAN', 'Van'),
        ('TANKER', 'Tanker'),
        ('CARGO_TRUCK', 'Cargo Truck')
    ]
    
    STATUS_CHOICES = [
        ('AVAILABLE', 'Available'),
        ('BUSY', 'Busy'),
        ('OFFLINE', 'Offline'),
        ('MAINTENANCE', 'Maintenance')
    ]
    
    user = models.OneToOneField(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='driver_profile')
    vehicle_type = models.CharField(max_length=20, choices=VEHICLE_TYPES)
    max_capacity_kg = models.DecimalField(max_digits=10, decimal_places=2)
    license_plate = models.CharField(max_length=20)
    region = models.CharField(max_length=100, blank=True)
    emergency_contact = models.CharField(max_length=100, blank=True)
    
    # Additional fields from TruckDriverProfile
    phone = models.CharField(max_length=20, blank=True, null=True)
    license_doc = models.TextField(blank=True, null=True)
    verification_status = models.CharField(
        max_length=20, 
        choices=VERIFICATION_STATUS_CHOICES, 
        default='pending'
    )
    
    # Logistics provider relationship
    created_by_logistics = models.ForeignKey(
        settings.AUTH_USER_MODEL, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True,
        related_name='created_drivers'
    )
    logistics_owner_id = models.IntegerField(null=True, blank=True)
    is_verified = models.BooleanField(default=False)
    
    # Additional logistics provider supervision fields
    created_by_driver = models.BooleanField(null=True, blank=True)
    can_operate_independently = models.BooleanField(null=True, blank=True)
    
    # Performance metrics
    rating = models.DecimalField(max_digits=3, decimal_places=2, default=5.00)
    completed_deliveries = models.IntegerField(default=0)
    on_time_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=100.00)
    total_earnings = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    last_delivery_date = models.DateTimeField(null=True, blank=True)
    
    # Status and availability
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='AVAILABLE')
    current_latitude = models.DecimalField(max_digits=10, decimal_places=8, null=True, blank=True)
    current_longitude = models.DecimalField(max_digits=11, decimal_places=8, null=True, blank=True)
    last_location_update = models.DateTimeField(null=True, blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'truck_driver_profiles'
        indexes = [
            models.Index(fields=['logistics_owner_id']),
            models.Index(fields=['vehicle_type']),
            models.Index(fields=['status']),
            models.Index(fields=['verification_status']),
        ]
    
    def __str__(self):
        return f"{self.user.first_name} {self.user.last_name} - {self.get_vehicle_type_display()}"
    
    @property
    def is_active(self):
        """Check if driver is active (assigned to logistics provider and verified)"""
        return (
            self.logistics_owner_id is not None and 
            self.verification_status == 'approved' and 
            self.is_verified
        )
    
    @property
    def status_display(self):
        """Human readable status"""
        if not self.logistics_owner_id:
            return "Unassigned"
        elif self.verification_status == 'pending':
            return "Pending Verification"
        elif self.verification_status == 'rejected':
            return "Rejected"
        elif self.is_verified:
            return "Active"
        else:
            return "Inactive"
    
    @property
    def logistics_provider_name(self):
        """Get logistics provider company name"""
        if self.logistics_owner_id:
            try:
                provider = User.objects.get(id=self.logistics_owner_id)
                return provider.company_name or f"{provider.first_name} {provider.last_name}".strip()
            except User.DoesNotExist:
                return "Unknown Provider"
        return "Unassigned"

class DriverJob(models.Model):
    """Driver job assignments"""
    
    driver = models.ForeignKey(DriverProfile, on_delete=models.CASCADE, related_name='jobs')
    shipment = models.ForeignKey('shipments.Shipment', on_delete=models.CASCADE, related_name='driver_jobs')
    assigned_at = models.DateTimeField(auto_now_add=True)
    started_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    
    STATUS_CHOICES = [
        ('ASSIGNED', 'Assigned'),
        ('IN_PROGRESS', 'In Progress'),
        ('COMPLETED', 'Completed'),
        ('CANCELLED', 'Cancelled')
    ]
    
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='ASSIGNED')
    payment_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    
    class Meta:
        db_table = 'driver_jobs'
    
    def __str__(self):
        return f"{self.driver.user.first_name} - {self.shipment.tracking_number}"

class DriverTrip(models.Model):
    """Driver trip tracking"""
    
    driver = models.ForeignKey(DriverProfile, on_delete=models.CASCADE, related_name='trips')
    job = models.ForeignKey(DriverJob, on_delete=models.CASCADE, related_name='trips')
    
    start_time = models.DateTimeField(null=True, blank=True)
    end_time = models.DateTimeField(null=True, blank=True)
    total_distance_km = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    fuel_consumed_liters = models.DecimalField(max_digits=8, decimal_places=2, default=0.00)
    
    pickup_latitude = models.DecimalField(max_digits=10, decimal_places=8, null=True, blank=True)
    pickup_longitude = models.DecimalField(max_digits=11, decimal_places=8, null=True, blank=True)
    delivery_latitude = models.DecimalField(max_digits=10, decimal_places=8, null=True, blank=True)
    delivery_longitude = models.DecimalField(max_digits=11, decimal_places=8, null=True, blank=True)
    
    notes = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'driver_trips'
    
    def __str__(self):
        return f"{self.driver.user.first_name} - Trip {self.id}"

class DriverEarnings(models.Model):
    """Driver earnings tracking"""
    
    driver = models.ForeignKey(DriverProfile, on_delete=models.CASCADE, related_name='earnings')
    job = models.ForeignKey(DriverJob, on_delete=models.CASCADE, related_name='earnings')
    
    base_payment = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    bonus_payment = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    fuel_allowance = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    total_earning = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    
    payment_date = models.DateTimeField(null=True, blank=True)
    payment_status = models.CharField(max_length=20, default='PENDING')
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'driver_earnings'
    
    def __str__(self):
        return f"{self.driver.user.first_name} - ${self.total_earning}"

class DriverLocation(models.Model):
    """Driver real-time location tracking"""
    
    driver = models.ForeignKey(DriverProfile, on_delete=models.CASCADE, related_name='locations')
    latitude = models.DecimalField(max_digits=10, decimal_places=8)
    longitude = models.DecimalField(max_digits=11, decimal_places=8)
    accuracy = models.DecimalField(max_digits=6, decimal_places=2, null=True, blank=True)
    speed_kmh = models.DecimalField(max_digits=6, decimal_places=2, null=True, blank=True)
    
    timestamp = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'driver_locations'
        indexes = [
            models.Index(fields=['driver', '-timestamp']),
        ]
    
    def __str__(self):
        return f"{self.driver.user.first_name} - {self.timestamp}"