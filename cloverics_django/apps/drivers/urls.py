"""
URL patterns for drivers app
"""

from django.urls import path
from . import views

app_name = 'drivers'

urlpatterns = [
    # Unified driver management APIs - extracted from FastAPI
    path('api/create-new/', views.create_new_driver_unified, name='create_new_driver'),
    path('api/unassigned/', views.get_unassigned_drivers_unified, name='get_unassigned_drivers'),
    path('api/assign-existing/', views.assign_existing_driver_unified, name='assign_existing_driver'),
    path('api/assigned/', views.get_assigned_drivers_unified, name='get_assigned_drivers'),
    path('api/<int:driver_id>/update-unified/', views.update_driver_profile_unified, name='update_driver_profile'),
    path('api/<int:driver_id>/performance-unified/', views.get_driver_performance_unified, name='get_driver_performance'),
    
    # Phase 3: Additional driver endpoints for 100% coverage
    path('dashboard/', views.driver_dashboard, name='driver_dashboard'),
    path('jobs/available/', views.available_jobs, name='available_jobs'),
    path('jobs/<int:job_id>/accept/', views.accept_job, name='accept_job'),
    path('jobs/<int:job_id>/decline/', views.decline_job, name='decline_job'),
    path('trips/<int:trip_id>/start/', views.start_trip, name='start_trip'),
    path('trips/<int:trip_id>/complete/', views.complete_trip, name='complete_trip'),
    path('earnings/', views.driver_earnings, name='driver_earnings'),
    path('earnings/summary/', views.earnings_summary, name='earnings_summary'),
    path('vehicle/inspection/', views.vehicle_inspection, name='vehicle_inspection'),
    path('documents/', views.driver_documents, name='driver_documents'),
    path('api/location/update/', views.api_update_location, name='api_update_location'),
    path('api/jobs/nearby/', views.api_nearby_jobs, name='api_nearby_jobs'),
    path('api/earnings/stats/', views.api_earnings_stats, name='api_earnings_stats'),
]