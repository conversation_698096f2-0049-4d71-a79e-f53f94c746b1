from django.contrib.auth.models import AbstractUser, BaseUserManager
from django.db import models
from django.utils import timezone
from django.core.validators import RegexValidator
from django.utils.translation import gettext_lazy as _


def default_list():
    return []


def default_dict():
    return {}


class UserManager(BaseUserManager):
    """Custom User Manager for email-based authentication"""
    
    def create_user(self, email, password=None, **extra_fields):
        """Create and save a regular user"""
        if not email:
            raise ValueError('The Email field must be set')
        email = self.normalize_email(email)
        user = self.model(email=email, **extra_fields)
        user.set_password(password)
        user.save(using=self._db)
        return user
    
    def create_superuser(self, email, password=None, **extra_fields):
        """Create and save a superuser"""
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)
        extra_fields.setdefault('user_type', 'ADMIN')
        extra_fields.setdefault('company_name', 'System Administrator')
        
        if extra_fields.get('is_staff') is not True:
            raise ValueError('Superuser must have is_staff=True.')
        if extra_fields.get('is_superuser') is not True:
            raise ValueError('Superuser must have is_superuser=True.')
        
        return self.create_user(email, password, **extra_fields)


class User(AbstractUser):
    """Custom User model for the LogistiLink platform."""
    
    USER_TYPE_CHOICES = [
        ('CUSTOMER', 'Customer'),
        ('LOGISTICS', 'Logistics Company'),
        ('CUSTOMS', 'Customs Authority'),
        ('INSURANCE', 'Insurance Company'),
        ('ADMIN', 'Administrator'),
    ]
    
    VERIFICATION_STATUS_CHOICES = [
        ('PENDING', 'Pending Verification'),
        ('IN_REVIEW', 'In Review'),
        ('VERIFIED', 'Verified'),
        ('REJECTED', 'Rejected'),
    ]
    
    # username = None  # Remove username field
    email = models.EmailField(_('email address'), unique=True)
    user_type = models.CharField(max_length=20, choices=USER_TYPE_CHOICES, default='CUSTOMER', db_index=True)
    company_name = models.CharField(max_length=255, db_index=True)
    tax_id = models.CharField(max_length=50, unique=True, null=True, blank=True)
    phone_regex = RegexValidator(
        regex=r'^\+?1?\d{9,15}$',
        message="Phone number must be entered in the format: '+*********'. Up to 15 digits allowed."
    )
    phone_number = models.CharField(validators=[phone_regex], max_length=17, blank=True)
    address = models.TextField(blank=True)
    country = models.CharField(max_length=100, blank=True, db_index=True)
    city = models.CharField(max_length=100, blank=True, db_index=True)
    
    # Verification fields
    is_verified = models.BooleanField(default=False, db_index=True)
    verification_status = models.CharField(
        max_length=20, 
        choices=VERIFICATION_STATUS_CHOICES, 
        default='PENDING',
        db_index=True
    )
    verification_notes = models.TextField(blank=True)
    verification_date = models.DateTimeField(null=True, blank=True)
    
    # Document storage fields
    business_license = models.CharField(max_length=255, blank=True, null=True)
    registration_document = models.CharField(max_length=255, blank=True, null=True)
    additional_documents = models.JSONField(default=default_list, blank=True, null=True)
    
    # Referral system fields
    referral_code = models.CharField(max_length=50, blank=True, null=True, unique=True)
    referred_by_code = models.CharField(max_length=50, blank=True, null=True)
    referred_by_user = models.ForeignKey('self', on_delete=models.SET_NULL, null=True, blank=True, related_name='referrals')
    premium_bonus_months = models.IntegerField(default=0)
    referral_bonus_eligible = models.BooleanField(default=False, help_text="Whether this user is eligible for referral bonus")
    first_payment_done = models.BooleanField(default=False, help_text="Whether user has completed their first payment")
    referral_code_locked = models.BooleanField(default=False, help_text="Whether referral code can still be changed")
    referral_bonus_granted = models.BooleanField(default=False, help_text="Whether any referral bonus has been granted for this customer")
    connection_bonus_granted = models.BooleanField(default=False, help_text="Whether customer has received 3-month connection bonus")
    
    # Payment card information for hybrid payment flow
    has_registered_card = models.BooleanField(default=False, help_text="Whether customer has a registered payment card on file for platform fees")
    stripe_customer_id = models.CharField(max_length=100, blank=True, null=True, help_text="Stripe customer ID for payment processing")
    
    created_at = models.DateTimeField(auto_now_add=True, db_index=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    # For Logistics Providers only
    transport_types = models.JSONField(default=default_list, blank=True, null=True)  # List of transport types
    serviceable_routes = models.JSONField(default=default_list, blank=True, null=True)  # List of serviced routes
    container_details = models.JSONField(default=default_dict, blank=True, null=True)  # Container and vehicle details
    cargo_types = models.JSONField(default=default_list, blank=True, null=True)  # Types of cargo accepted
    
    # For Insurance Providers only
    insurance_types = models.JSONField(default=default_list, blank=True, null=True)  # Types of insurance offered
    coverage_countries = models.JSONField(default=default_list, blank=True, null=True)  # Countries where coverage is offered
    policy_details = models.JSONField(default=default_dict, blank=True, null=True)  # Base policy details
    
    # JWT related fields (keeping from original)
    jwt_secret = models.CharField(max_length=255, blank=True, null=True)
    last_login_ip = models.GenericIPAddressField(blank=True, null=True)
    
    # Profile fields (moved from UserProfile)
    avatar = models.ImageField(upload_to='avatars/', blank=True, null=True)
    bio = models.TextField(blank=True, null=True)
    website = models.URLField(blank=True, null=True)
    linkedin = models.URLField(blank=True, null=True)
    language = models.CharField(max_length=10, default='en')
    user_timezone = models.CharField(max_length=50, default='UTC')
    
    objects = UserManager()
    
    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['company_name', 'tax_id', 'user_type']
    
    class Meta(AbstractUser.Meta):
        indexes = [
            models.Index(fields=['email']),
            models.Index(fields=['user_type']),
            models.Index(fields=['company_name']),
            models.Index(fields=['country']),
            models.Index(fields=['is_verified']),
            models.Index(fields=['verification_status']),
            models.Index(fields=['created_at']),
        ]
    
    def save(self, *args, **kwargs):
        """Override save to generate referral code for logistics providers"""
        if self.user_type == 'LOGISTICS' and not self.referral_code:
            # Use email (without domain) as referral code for logistics providers
            email_str = str(self.email)
            self.referral_code = email_str.split('@')[0].lower()
        super().save(*args, **kwargs)
    
    def get_referral_count(self):
        """Get total number of successful referrals"""
        return self.referrals_made.filter(bonus_awarded=True).count() if hasattr(self, 'referrals_made') else 0
    
    def get_client_count(self):
        """Get total number of clients for logistics providers"""
        if self.user_type == 'LOGISTICS':
            return self.client_relationships.count() if hasattr(self, 'client_relationships') else 0
        return 0
    
    def get_unread_message_count(self):
        """Get count of unread messages"""
        return self.messages_received.filter(is_read=False).count() if hasattr(self, 'messages_received') else 0

    def __str__(self):
        return f"{self.company_name} ({self.get_user_type_display()})"
    
    @property
    def display_name(self):
        """Return the display name for the user"""
        if self.first_name and self.last_name:
            return f"{self.first_name} {self.last_name}"
        elif self.company_name:
            return self.company_name
        return self.email
    
    def get_dashboard_url(self):
        """Return the appropriate dashboard URL based on user type"""
        dashboard_urls = {
            'CUSTOMER': '/customer/dashboard/',
            'LOGISTICS': '/logistics/dashboard/',
            'ADMIN': '/admin-panel/dashboard/',
            'CUSTOMS': '/customs/dashboard/',
            'INSURANCE': '/insurance/dashboard/',
        }
        return dashboard_urls.get(self.user_type, '/customer/dashboard/')





class JWTToken(models.Model):
    """
    JWT Token management for mobile/API authentication
    """
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='jwt_tokens')
    token = models.TextField()
    device_info = models.CharField(max_length=255, blank=True, null=True)
    ip_address = models.GenericIPAddressField()
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(default=timezone.now)
    expires_at = models.DateTimeField()
    last_used = models.DateTimeField(blank=True, null=True)
    
    class Meta:
        ordering = ['-created_at']
    
    def __str__(self):
        return f"JWT Token for {self.user.email}"
    
    @property
    def is_expired(self):
        return timezone.now() > self.expires_at
    
    def mark_used(self):
        """Mark token as recently used"""
        self.last_used = timezone.now()
        self.save(update_fields=['last_used'])


class UserSession(models.Model):
    """
    Enhanced session tracking for security
    """
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='user_sessions')
    session_key = models.CharField(max_length=40, unique=True)
    ip_address = models.GenericIPAddressField()
    user_agent = models.TextField()
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(default=timezone.now)
    last_activity = models.DateTimeField(default=timezone.now)
    
    class Meta:
        ordering = ['-last_activity']
    
    def __str__(self):
        return f"Session for {self.user.email} from {self.ip_address}"