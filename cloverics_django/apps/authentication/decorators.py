from django.shortcuts import redirect
from django.contrib.auth import get_user_model
from functools import wraps

User = get_user_model()


def customer_required(view_func):
    """
    Decorator to ensure user is a customer
    """
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        if not request.user.is_authenticated:
            return redirect('authentication:login')
        
        if request.user.user_type != 'CUSTOMER':
            return redirect('authentication:dashboard')
        
        return view_func(request, *args, **kwargs)
    
    return wrapper


def logistics_required(view_func):
    """
    Decorator to ensure user is a logistics provider
    """
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        if not request.user.is_authenticated:
            return redirect('authentication:login')
        
        if request.user.user_type != 'LOGISTICS':
            return redirect('authentication:dashboard')
        
        return view_func(request, *args, **kwargs)
    
    return wrapper


def admin_required(view_func):
    """
    Decorator to ensure user is an admin
    """
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        if not request.user.is_authenticated:
            return redirect('authentication:login')
        
        if request.user.user_type != 'ADMIN' and not request.user.is_staff:
            return redirect('authentication:dashboard')
        
        return view_func(request, *args, **kwargs)
    
    return wrapper


def user_type_required(*allowed_types):
    """
    Decorator to ensure user has one of the allowed user types
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            if not request.user.is_authenticated:
                return redirect('authentication:login')
            
            if request.user.user_type not in allowed_types:
                return redirect('authentication:dashboard')
            
            return view_func(request, *args, **kwargs)
        
        return wrapper
    return decorator


def require_user_type(required_type):
    """
    Decorator to require a specific user type for access
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            if not request.user.is_authenticated:
                return redirect('authentication:login')
            
            if request.user.user_type != required_type:
                return redirect('authentication:dashboard')
            
            return view_func(request, *args, **kwargs)
        
        return wrapper
    return decorator