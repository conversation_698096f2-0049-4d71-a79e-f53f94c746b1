from django import template
import sys
import os
import json

register = template.Library()

# Cache for translations
_translations_cache = None

def load_translations():
    """Load translations from JSON files with caching"""
    global _translations_cache
    
    if _translations_cache is not None:
        return _translations_cache
    
    translations = {}
    try:
        # Calculate the correct path to the utils directory
        current_dir = os.path.dirname(__file__)
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(current_dir))))
        utils_path = os.path.join(project_root, 'utils')
        translation_file = os.path.join(utils_path, 'translations', 'en.json')
        
        if os.path.exists(translation_file):
            with open(translation_file, 'r', encoding='utf-8') as f:
                translations = json.load(f)
                print(f"✅ Loaded {len(translations)} translations from {translation_file}")
        else:
            print(f"❌ Translation file not found: {translation_file}")
    except Exception as e:
        print(f"❌ Error loading translations: {e}")
    
    _translations_cache = translations
    return translations

@register.simple_tag
def translate(key, **kwargs):
    """Custom template tag for translation"""
    translations = load_translations()
    result = translations.get(key, key)
    return result

@register.filter
def get_range(value):
    """Template filter to get a range of numbers for pagination"""
    try:
        return range(1, int(value) + 1)
    except (ValueError, TypeError):
        return range(1) 