from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from .models import User, J<PERSON>TToken, UserSession


@admin.register(User)
class UserAdmin(BaseUserAdmin):
    """
    Custom User admin with additional fields
    """
    list_display = ('username', 'email', 'user_type', 'company_name', 'is_verified', 'is_active', 'created_at')
    list_filter = ('user_type', 'is_verified', 'is_active', 'is_staff', 'created_at')
    search_fields = ('username', 'email', 'company_name', 'first_name', 'last_name')
    ordering = ('-created_at',)
    
    fieldsets = BaseUserAdmin.fieldsets + (
        ('Cloverics Information', {
            'fields': ('user_type', 'company_name', 'phone_number', 'country', 'city', 'is_verified')
        }),
        ('Profile Information', {
            'fields': ('avatar', 'bio', 'website', 'linkedin', 'language', 'user_timezone')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at')
        }),
    )
    
    readonly_fields = ('created_at', 'updated_at')
    
    add_fieldsets = BaseUserAdmin.add_fieldsets + (
        ('Cloverics Information', {
            'fields': ('email', 'user_type', 'company_name', 'phone_number', 'country', 'city')
        }),
    )





@admin.register(JWTToken)
class JWTTokenAdmin(admin.ModelAdmin):
    """
    JWT Token admin for monitoring mobile/API authentication
    """
    list_display = ('user', 'device_info', 'ip_address', 'is_active', 'created_at', 'expires_at')
    list_filter = ('is_active', 'created_at', 'expires_at')
    search_fields = ('user__username', 'user__email', 'ip_address', 'device_info')
    readonly_fields = ('token', 'created_at', 'last_used')
    ordering = ('-created_at',)
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user')


@admin.register(UserSession)
class UserSessionAdmin(admin.ModelAdmin):
    """
    User Session admin for security monitoring
    """
    list_display = ('user', 'ip_address', 'is_active', 'created_at', 'last_activity')
    list_filter = ('is_active', 'created_at', 'last_activity')
    search_fields = ('user__username', 'user__email', 'ip_address', 'session_key')
    readonly_fields = ('session_key', 'user_agent', 'created_at')
    ordering = ('-last_activity',)
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user')