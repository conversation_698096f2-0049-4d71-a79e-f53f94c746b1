import jwt
from django.conf import settings
from django.contrib.auth import get_user_model
from django.utils.deprecation import MiddlewareMixin
from django.http import JsonResponse
from .models import JWTToken

User = get_user_model()


class JWTAuthenticationMiddleware(MiddlewareMixin):
    """
    Middleware to handle JWT authentication alongside Django sessions
    This enables hybrid authentication for both web and mobile/API users
    """
    
    def process_request(self, request):
        """
        Process incoming request to authenticate via JWT if session auth fails
        """
        # Skip if already authenticated via session
        if request.user.is_authenticated:
            return None
        
        # Check for JWT token in cookies or Authorization header
        jwt_token = self.get_jwt_token(request)
        
        if jwt_token:
            user = self.authenticate_jwt(jwt_token, request)
            if user:
                request.user = user
                # Mark this as JWT-authenticated for later use
                request.jwt_authenticated = True
        
        return None
    
    def get_jwt_token(self, request):
        """
        Extract JWT token from cookies or Authorization header
        """
        # Try cookie first (for web app)
        jwt_token = request.COOKIES.get('access_token')
        
        if not jwt_token:
            # Try Authorization header (for API/mobile)
            auth_header = request.META.get('HTTP_AUTHORIZATION', '')
            if auth_header.startswith('Bearer '):
                jwt_token = auth_header[7:]  # Remove 'Bearer ' prefix
        
        return jwt_token
    
    def authenticate_jwt(self, token, request):
        """
        Authenticate user using JWT token
        """
        try:
            # Decode JWT token
            payload = jwt.decode(token, settings.SECRET_KEY, algorithms=['HS256'])
            user_id = payload.get('user_id')
            
            if not user_id:
                return None
            
            # Get user from database
            user = User.objects.get(id=user_id, is_active=True)
            
            # Verify token exists in database and is active
            jwt_token_obj = JWTToken.objects.filter(
                user=user,
                token=token,
                is_active=True
            ).first()
            
            if jwt_token_obj and not jwt_token_obj.is_expired:
                # Update last used timestamp
                jwt_token_obj.mark_used()
                return user
            
            return None
            
        except (jwt.ExpiredSignatureError, jwt.InvalidTokenError, User.DoesNotExist):
            return None
        except Exception:
            # Log error in production
            return None


class SecurityHeadersMiddleware(MiddlewareMixin):
    """
    Add security headers to all responses
    """
    
    def process_response(self, request, response):
        """
        Add security headers to response
        """
        # Content Security Policy
        response['Content-Security-Policy'] = (
            "default-src 'self'; "
            "script-src 'self' 'unsafe-inline' 'unsafe-eval' cdn.jsdelivr.net; "
            "style-src 'self' 'unsafe-inline' cdn.jsdelivr.net; "
            "img-src 'self' data: https:; "
            "font-src 'self' cdn.jsdelivr.net; "
            "connect-src 'self' ws: wss:; "
        )
        
        # Other security headers
        response['X-Content-Type-Options'] = 'nosniff'
        response['X-Frame-Options'] = 'DENY'
        response['X-XSS-Protection'] = '1; mode=block'
        response['Referrer-Policy'] = 'strict-origin-when-cross-origin'
        
        # HSTS header for HTTPS
        if request.is_secure():
            response['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'
        
        return response


class SessionTrackingMiddleware(MiddlewareMixin):
    """
    Track user sessions for security monitoring
    """
    
    def process_request(self, request):
        """
        Track session information
        """
        if request.user.is_authenticated and not getattr(request, 'jwt_authenticated', False):
            # This is a session-authenticated user
            from .models import UserSession
            
            session_key = request.session.session_key
            if session_key:
                # Update or create session tracking
                UserSession.objects.update_or_create(
                    session_key=session_key,
                    defaults={
                        'user': request.user,
                        'ip_address': self.get_client_ip(request),
                        'user_agent': request.META.get('HTTP_USER_AGENT', ''),
                        'is_active': True,
                    }
                )
        
        return None
    
    def get_client_ip(self, request):
        """Get client IP address"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR', '127.0.0.1')
        return ip