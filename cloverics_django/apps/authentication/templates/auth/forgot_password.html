{% extends "base.html" %}
{% load translation_tags %}

{% block title %}Forgot Password - Cloverics{% endblock %}

{% block extra_css %}
<style>
.auth-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, #1E88E5 0%, #0D47A1 100%);
}

.auth-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.auth-header {
    text-align: center;
    margin-bottom: 2rem;
}

.auth-header h2 {
    color: #1E88E5;
    margin-bottom: 0.5rem;
}

.auth-header p {
    color: #666;
    margin: 0;
}

.form-label {
    font-weight: 500;
    color: #333;
}

.form-control {
    border-radius: 8px;
    border: 1px solid #ddd;
    padding: 12px;
}

.form-control:focus {
    border-color: #1E88E5;
    box-shadow: 0 0 0 0.2rem rgba(30, 136, 229, 0.25);
}

.btn-primary {
    background: #1E88E5;
    border: none;
    border-radius: 8px;
    padding: 12px;
    font-weight: 500;
}

.btn-primary:hover {
    background: #1976D2;
}

.auth-footer {
    text-align: center;
    margin-top: 1rem;
}

.auth-footer p {
    margin: 0.5rem 0;
    color: #666;
}

.auth-footer a {
    color: #1E88E5;
    text-decoration: none;
}

.auth-footer a:hover {
    text-decoration: underline;
}
</style>
{% endblock %}

{% block content %}
<div class="auth-container">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-5">
            <div class="auth-card">
                <div class="auth-header">
                    <h2>🍀 {% translate 'reset_password' %}</h2>
                    <p>{% translate 'enter_email_reset_instructions' %}</p>
                </div>

                {% if success %}
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    {{ success }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                {% endif %}

                {% if error %}
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    {{ error }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                {% endif %}

                <form method="POST" action="/forgot-password">
                    <div class="mb-3">
                        <label for="email" class="form-label">{% translate 'email_address' %}</label>
                        <input type="email" class="form-control" id="email" name="email" required>
                        <div class="form-text">{% translate 'password_reset_instructions_note' %}</div>
                    </div>
                    
                    <button type="submit" class="btn btn-primary w-100 mb-3" onclick="submitPasswordReset()">{% translate 'send_reset_instructions' %}</button>
                </form>

                <div class="auth-footer">
                    <p>{% translate 'remember_password' %} <a href="/login">{% translate 'back_to_sign_in' %}</a></p>
                    <p>{% translate 'dont_have_account' %} <a href="/register">{% translate 'register_here' %}</a></p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}