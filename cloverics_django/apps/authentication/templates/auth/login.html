{% extends "base_public.html" %}

{% block content %}
<div class="auth-container">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-5">
            <div class="auth-card">
                <div class="auth-header">
                    <h2>🍀 Sign In</h2>
                    <p>Access your Cloverics account</p>
                </div>

                {% if error %}
                <div class="alert alert-danger" role="alert">
                    {{ error }}
                </div>
                {% endif %}

                <form method="POST" action="/auth/login-submit/">
                    <div class="mb-3">
                        <label for="email" class="form-label">Email Address</label>
                        <input type="email" class="form-control" id="email" name="email" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="password" class="form-label">Password</label>
                        <input type="password" class="form-control" id="password" name="password" required>
                    </div>
                    
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="remember">
                        <label class="form-check-label" for="remember">
                            Remember me
                        </label>
                    </div>
                    
                    <button type="submit" class="btn btn-primary w-100 mb-3">Sign In</button>
                </form>

                <div class="auth-footer">
                    <p>Don't have an account? <a href="/register">Register here</a></p>
                    <p><a href="/forgot-password">Forgot your password?</a></p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.auth-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, #1E88E5 0%, #0D47A1 100%);
}

.auth-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.auth-header {
    text-align: center;
    margin-bottom: 2rem;
}

.auth-header h2 {
    color: #1E88E5;
    margin-bottom: 0.5rem;
}

.auth-header p {
    color: #666;
    margin: 0;
}

.form-label {
    font-weight: 500;
    color: #333;
}

.form-control {
    border-radius: 8px;
    border: 1px solid #ddd;
    padding: 12px;
}

.form-control:focus {
    border-color: #1E88E5;
    box-shadow: 0 0 0 0.2rem rgba(30, 136, 229, 0.25);
}

.btn-primary {
    background: #1E88E5;
    border: none;
    border-radius: 8px;
    padding: 12px;
    font-weight: 500;
}

.btn-primary:hover {
    background: #1976D2;
}

.auth-footer {
    text-align: center;
    margin-top: 1rem;
}

.auth-footer p {
    margin: 0.5rem 0;
    color: #666;
}

.auth-footer a {
    color: #1E88E5;
    text-decoration: none;
}

.auth-footer a:hover {
    text-decoration: underline;
}
</style>
{% endblock %}