import jwt
from django.conf import settings
from django.contrib.auth import get_user_model
from rest_framework.authentication import BaseAuthentication
from rest_framework.exceptions import AuthenticationFailed
from .models import JWTToken

User = get_user_model()


class JWTAuthentication(BaseAuthentication):
    """
    Custom JWT authentication for Django REST Framework
    """
    
    def authenticate(self, request):
        """
        Authenticate user using JWT token
        """
        # Get JWT token from Authorization header or cookies
        jwt_token = self.get_jwt_token(request)
        
        if not jwt_token:
            return None
        
        try:
            # Decode JWT token
            payload = jwt.decode(jwt_token, settings.SECRET_KEY, algorithms=['HS256'])
            user_id = payload.get('user_id')
            
            if not user_id:
                raise AuthenticationFailed('Invalid token payload')
            
            # Get user from database
            user = User.objects.get(id=user_id, is_active=True)
            
            # Verify token exists in database and is active
            jwt_token_obj = JWTToken.objects.filter(
                user=user,
                token=jwt_token,
                is_active=True
            ).first()
            
            if not jwt_token_obj:
                raise AuthenticationFailed('Token not found or inactive')
            
            if jwt_token_obj.is_expired:
                # Mark token as inactive
                jwt_token_obj.is_active = False
                jwt_token_obj.save()
                raise AuthenticationFailed('Token has expired')
            
            # Update last used timestamp
            jwt_token_obj.mark_used()
            
            return (user, jwt_token_obj)
            
        except jwt.ExpiredSignatureError:
            raise AuthenticationFailed('Token has expired')
        except jwt.InvalidTokenError:
            raise AuthenticationFailed('Invalid token')
        except User.DoesNotExist:
            raise AuthenticationFailed('User not found')
        except Exception as e:
            raise AuthenticationFailed(f'Authentication failed: {str(e)}')
    
    def get_jwt_token(self, request):
        """
        Extract JWT token from Authorization header or cookies
        """
        # Try Authorization header first (standard for APIs)
        auth_header = request.META.get('HTTP_AUTHORIZATION', '')
        if auth_header.startswith('Bearer '):
            return auth_header[7:]  # Remove 'Bearer ' prefix
        
        # Try cookie (for web app hybrid authentication)
        return request.COOKIES.get('access_token')
    
    def authenticate_header(self, request):
        """
        Return the authentication header for 401 responses
        """
        return 'Bearer'


class HybridAuthentication(BaseAuthentication):
    """
    Hybrid authentication that supports both JWT and Session authentication
    Useful for endpoints that need to work with both web and mobile clients
    """
    
    def authenticate(self, request):
        """
        Try JWT authentication first, then fall back to session authentication
        """
        # Try JWT authentication first
        jwt_auth = JWTAuthentication()
        result = jwt_auth.authenticate(request)
        
        if result:
            return result
        
        # Fall back to session authentication
        if hasattr(request, 'user') and request.user.is_authenticated:
            return (request.user, None)
        
        return None