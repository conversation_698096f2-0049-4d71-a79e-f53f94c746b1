from django.urls import path
from . import views

app_name = 'auth'

urlpatterns = [
    # Authentication views
    path('login/', views.LoginView.as_view(), name='login'),
    path('register/', views.RegisterView.as_view(), name='register'),
    path('logout/', views.logout_view, name='logout'),
    
    # Dashboard redirect
    path('dashboard/', views.dashboard_redirect, name='dashboard'),
    
    # Password reset
    path('password-reset/', views.PasswordResetView.as_view(), name='password_reset'),
    path('password-reset-confirm/<uidb64>/<token>/', 
         views.PasswordResetConfirmView.as_view(), name='password_reset_confirm'),
    
    # API endpoints
    path('api/user/', views.api_user_info, name='api_user_info'),
    path('api/notifications/count/', views.api_notifications_count, name='api_notifications_count'),
]