from django.conf import settings
import sys
import os
import json

# Add the utils directory to the Python path so we can import the i18n module
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
utils_path = os.path.join(project_root, 'utils')
sys.path.insert(0, utils_path)

# Simple translation function that loads translations directly
def load_translations():
    """Load translations from JSON files"""
    translations = {}
    try:
        translation_file = os.path.join(utils_path, 'translations', 'en.json')
        if os.path.exists(translation_file):
            with open(translation_file, 'r', encoding='utf-8') as f:
                translations = json.load(f)
    except Exception as e:
        print(f"Error loading translations: {e}")
    return translations

# Load translations once
_translations = load_translations()

def translate(key, **kwargs):
    """Simple translation function"""
    return _translations.get(key, key)

def get_language_selector_data():
    """Get language selector data"""
    return {
        'current_language': 'en',
        'current_language_name': 'English',
        'supported_languages': {'en': 'English', 'ru': 'Русский', 'fr': 'Français', 'tr': 'Türkçe', 'ar': 'العربية', 'zh': '中文', 'es': 'Español'},
        'direction': 'ltr'
    }


def user_context(request):
    """
    Add user-related context variables to all templates
    """
    context = {
        'current_user': request.user if request.user.is_authenticated else None,
        'is_jwt_authenticated': getattr(request, 'jwt_authenticated', False),
    }
    
    if request.user.is_authenticated:
        context.update({
            'user_type': request.user.user_type,
            'user_display_name': request.user.display_name,
            'user_dashboard_url': request.user.get_dashboard_url(),
            'is_customer': request.user.user_type == 'CUSTOMER',
            'is_logistics': request.user.user_type == 'LOGISTICS',
            'is_admin': request.user.user_type == 'ADMIN',
            'is_customs': request.user.user_type == 'CUSTOMS',
            'is_insurance': request.user.user_type == 'INSURANCE',
            'is_driver': request.user.user_type == 'DRIVER',
        })
    
    return context


def site_context(request):
    """
    Add site-wide context variables
    """
    return {
        'site_name': 'Cloverics',
        'site_description': 'Global Logistics Platform',
        'debug': settings.DEBUG,
        'current_path': request.path,
        'current_path_name': request.resolver_match.url_name if request.resolver_match else None,
    }


def translation_context(request):
    """
    Add translation function and language data to all templates
    """
    print("🔧 Translation context processor called")
    result = {
        'translate': translate,  # Use 'translate' instead of '_' to avoid conflicts
        'language_data': get_language_selector_data(),
        'current_language': get_language_selector_data()['current_language'],
        'direction': get_language_selector_data()['direction'],
    }
    print(f"🔧 Translation context result: {result}")
    return result