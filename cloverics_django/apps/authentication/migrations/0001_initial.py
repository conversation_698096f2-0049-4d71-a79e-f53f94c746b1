# Generated by Django 5.2.1 on 2025-08-07 12:45

import apps.authentication.models
import django.contrib.auth.validators
import django.core.validators
import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='User',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('username', models.CharField(error_messages={'unique': 'A user with that username already exists.'}, help_text='Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.', max_length=150, unique=True, validators=[django.contrib.auth.validators.UnicodeUsernameValidator()], verbose_name='username')),
                ('first_name', models.CharField(blank=True, max_length=150, verbose_name='first name')),
                ('last_name', models.CharField(blank=True, max_length=150, verbose_name='last name')),
                ('is_staff', models.BooleanField(default=False, help_text='Designates whether the user can log into this admin site.', verbose_name='staff status')),
                ('is_active', models.BooleanField(default=True, help_text='Designates whether this user should be treated as active. Unselect this instead of deleting accounts.', verbose_name='active')),
                ('date_joined', models.DateTimeField(default=django.utils.timezone.now, verbose_name='date joined')),
                ('email', models.EmailField(max_length=254, unique=True, verbose_name='email address')),
                ('user_type', models.CharField(choices=[('CUSTOMER', 'Customer'), ('LOGISTICS', 'Logistics Company'), ('CUSTOMS', 'Customs Authority'), ('INSURANCE', 'Insurance Company'), ('ADMIN', 'Administrator')], db_index=True, default='CUSTOMER', max_length=20)),
                ('company_name', models.CharField(db_index=True, max_length=255)),
                ('tax_id', models.CharField(blank=True, max_length=50, null=True, unique=True)),
                ('phone_number', models.CharField(blank=True, max_length=17, validators=[django.core.validators.RegexValidator(message="Phone number must be entered in the format: '+*********'. Up to 15 digits allowed.", regex='^\\+?1?\\d{9,15}$')])),
                ('address', models.TextField(blank=True)),
                ('country', models.CharField(blank=True, db_index=True, max_length=100)),
                ('city', models.CharField(blank=True, db_index=True, max_length=100)),
                ('is_verified', models.BooleanField(db_index=True, default=False)),
                ('verification_status', models.CharField(choices=[('PENDING', 'Pending Verification'), ('IN_REVIEW', 'In Review'), ('VERIFIED', 'Verified'), ('REJECTED', 'Rejected')], db_index=True, default='PENDING', max_length=20)),
                ('verification_notes', models.TextField(blank=True)),
                ('verification_date', models.DateTimeField(blank=True, null=True)),
                ('business_license', models.CharField(blank=True, max_length=255, null=True)),
                ('registration_document', models.CharField(blank=True, max_length=255, null=True)),
                ('additional_documents', models.JSONField(blank=True, default=apps.authentication.models.default_list, null=True)),
                ('referral_code', models.CharField(blank=True, max_length=50, null=True, unique=True)),
                ('referred_by_code', models.CharField(blank=True, max_length=50, null=True)),
                ('premium_bonus_months', models.IntegerField(default=0)),
                ('referral_bonus_eligible', models.BooleanField(default=False, help_text='Whether this user is eligible for referral bonus')),
                ('first_payment_done', models.BooleanField(default=False, help_text='Whether user has completed their first payment')),
                ('referral_code_locked', models.BooleanField(default=False, help_text='Whether referral code can still be changed')),
                ('referral_bonus_granted', models.BooleanField(default=False, help_text='Whether any referral bonus has been granted for this customer')),
                ('connection_bonus_granted', models.BooleanField(default=False, help_text='Whether customer has received 3-month connection bonus')),
                ('has_registered_card', models.BooleanField(default=False, help_text='Whether customer has a registered payment card on file for platform fees')),
                ('stripe_customer_id', models.CharField(blank=True, help_text='Stripe customer ID for payment processing', max_length=100, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True, db_index=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('transport_types', models.JSONField(blank=True, default=apps.authentication.models.default_list, null=True)),
                ('serviceable_routes', models.JSONField(blank=True, default=apps.authentication.models.default_list, null=True)),
                ('container_details', models.JSONField(blank=True, default=apps.authentication.models.default_dict, null=True)),
                ('cargo_types', models.JSONField(blank=True, default=apps.authentication.models.default_list, null=True)),
                ('insurance_types', models.JSONField(blank=True, default=apps.authentication.models.default_list, null=True)),
                ('coverage_countries', models.JSONField(blank=True, default=apps.authentication.models.default_list, null=True)),
                ('policy_details', models.JSONField(blank=True, default=apps.authentication.models.default_dict, null=True)),
                ('jwt_secret', models.CharField(blank=True, max_length=255, null=True)),
                ('last_login_ip', models.GenericIPAddressField(blank=True, null=True)),
                ('avatar', models.ImageField(blank=True, null=True, upload_to='avatars/')),
                ('bio', models.TextField(blank=True, null=True)),
                ('website', models.URLField(blank=True, null=True)),
                ('linkedin', models.URLField(blank=True, null=True)),
                ('language', models.CharField(default='en', max_length=10)),
                ('user_timezone', models.CharField(default='UTC', max_length=50)),
                ('groups', models.ManyToManyField(blank=True, help_text='The groups this user belongs to. A user will get all permissions granted to each of their groups.', related_name='user_set', related_query_name='user', to='auth.group', verbose_name='groups')),
                ('referred_by_user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='referrals', to=settings.AUTH_USER_MODEL)),
                ('user_permissions', models.ManyToManyField(blank=True, help_text='Specific permissions for this user.', related_name='user_set', related_query_name='user', to='auth.permission', verbose_name='user permissions')),
            ],
            options={
                'verbose_name': 'user',
                'verbose_name_plural': 'users',
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='JWTToken',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('token', models.TextField()),
                ('device_info', models.CharField(blank=True, max_length=255, null=True)),
                ('ip_address', models.GenericIPAddressField()),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('expires_at', models.DateTimeField()),
                ('last_used', models.DateTimeField(blank=True, null=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='jwt_tokens', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='UserSession',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('session_key', models.CharField(max_length=40, unique=True)),
                ('ip_address', models.GenericIPAddressField()),
                ('user_agent', models.TextField()),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('last_activity', models.DateTimeField(default=django.utils.timezone.now)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='user_sessions', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-last_activity'],
            },
        ),
        migrations.AddIndex(
            model_name='user',
            index=models.Index(fields=['email'], name='authenticat_email_d74434_idx'),
        ),
        migrations.AddIndex(
            model_name='user',
            index=models.Index(fields=['user_type'], name='authenticat_user_ty_277406_idx'),
        ),
        migrations.AddIndex(
            model_name='user',
            index=models.Index(fields=['company_name'], name='authenticat_company_0ff168_idx'),
        ),
        migrations.AddIndex(
            model_name='user',
            index=models.Index(fields=['country'], name='authenticat_country_cd20bf_idx'),
        ),
        migrations.AddIndex(
            model_name='user',
            index=models.Index(fields=['is_verified'], name='authenticat_is_veri_984052_idx'),
        ),
        migrations.AddIndex(
            model_name='user',
            index=models.Index(fields=['verification_status'], name='authenticat_verific_44afad_idx'),
        ),
        migrations.AddIndex(
            model_name='user',
            index=models.Index(fields=['created_at'], name='authenticat_created_b28532_idx'),
        ),
    ]
