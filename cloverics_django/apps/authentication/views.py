from django.shortcuts import render, redirect
from django.contrib.auth import authenticate, login, logout
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.views.generic import CreateView, FormView
from django.urls import reverse_lazy
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.views.decorators.http import require_http_methods
import json
import jwt
from datetime import datetime, timedelta
from django.conf import settings

from .models import User, JWTToken
from .forms import CustomUserCreationForm, LoginForm


class RegisterView(CreateView):
    """
    User registration view with support for different user types
    """
    model = User
    form_class = CustomUserCreationForm
    template_name = 'auth/register.html'
    success_url = reverse_lazy('auth:login')
    
    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Account created successfully! Please log in.')
        return response
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['user_types'] = User.USER_TYPE_CHOICES
        return context


class LoginView(FormView):
    """
    Login view with JWT and session support
    """
    template_name = 'auth/login.html'
    form_class = LoginForm
    
    def form_valid(self, form):
        email = form.cleaned_data['email']
        password = form.cleaned_data['password']
        user = authenticate(self.request, username=email, password=password)
        
        if user is not None:
            if user.is_active:
                login(self.request, user)
                
                # Generate JWT token for API/mobile compatibility
                jwt_token = self.generate_jwt_token(user)
                
                # Store JWT in database
                JWTToken.objects.create(
                    user=user,
                    token=jwt_token,
                    ip_address=self.get_client_ip(),
                    expires_at=datetime.now() + timedelta(days=7),
                    device_info=self.request.META.get('HTTP_USER_AGENT', '')
                )
                
                # Set JWT as cookie for hybrid authentication
                response = redirect(user.get_dashboard_url())
                response.set_cookie(
                    'access_token',
                    jwt_token,
                    max_age=7*24*60*60,  # 7 days
                    httponly=True,
                    secure=not settings.DEBUG
                )
                
                messages.success(self.request, f'Welcome back, {user.display_name}!')
                return response
            else:
                messages.error(self.request, 'Your account is inactive. Please contact support.')
        else:
            messages.error(self.request, 'Invalid email or password.')
        
        return self.form_invalid(form)
    
    def get_client_ip(self):
        """Get client IP address"""
        x_forwarded_for = self.request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = self.request.META.get('REMOTE_ADDR')
        return ip
    
    def generate_jwt_token(self, user):
        """Generate JWT token for user"""
        payload = {
            'user_id': user.id,
            'email': user.email,
            'user_type': user.user_type,
            'exp': datetime.utcnow() + timedelta(days=7),
            'iat': datetime.utcnow(),
        }
        return jwt.encode(payload, settings.SECRET_KEY, algorithm='HS256')


@login_required
def logout_view(request):
    """
    Logout view that clears both session and JWT tokens
    """
    # Deactivate JWT tokens
    JWTToken.objects.filter(user=request.user, is_active=True).update(is_active=False)
    
    logout(request)
    messages.success(request, 'You have been successfully logged out.')
    
    response = redirect('home')
    response.delete_cookie('access_token')
    return response


@csrf_exempt
@require_http_methods(["GET"])
def api_user_info(request):
    """
    API endpoint to get current user information
    """
    if request.user.is_authenticated:
        return JsonResponse({
            'success': True,
            'user': {
                'id': request.user.id,
                'email': request.user.email,
                'user_type': request.user.user_type,
                'company_name': request.user.company_name,
                'display_name': request.user.display_name,
                'is_verified': request.user.is_verified,
            }
        })
    else:
        return JsonResponse({'success': False, 'error': 'Not authenticated'}, status=401)


@csrf_exempt
@require_http_methods(["GET"])
def api_notifications_count(request):
    """
    API endpoint to get notification count for user
    """
    if request.user.is_authenticated:
        # This will be implemented when notifications app is created
        unread_count = 0  # Placeholder
        return JsonResponse({
            'success': True,
            'unread_count': unread_count,
            'total_count': unread_count
        })
    else:
        return JsonResponse({'success': False, 'error': 'Not authenticated'}, status=401)


def dashboard_redirect(request):
    """
    Redirect to appropriate dashboard based on user type
    """
    if request.user.is_authenticated:
        return redirect(request.user.get_dashboard_url())
    else:
        return redirect('auth:login')


# Error handlers
def handler404(request, exception):
    """Custom 404 error handler"""
    return render(request, 'error/404.html', status=404)


def handler500(request):
    """Custom 500 error handler"""
    return render(request, 'error/500.html', status=500)


# Password reset views (to be implemented)
class PasswordResetView(FormView):
    """Password reset request view"""
    template_name = 'auth/password_reset.html'
    
    def form_valid(self, form):
        # Password reset logic will be implemented
        messages.success(self.request, 'Password reset email sent.')
        return redirect('auth:login')


class PasswordResetConfirmView(FormView):
    """Password reset confirmation view"""
    template_name = 'auth/password_reset_confirm.html'
    
    def form_valid(self, form):
        # Password reset confirmation logic will be implemented
        messages.success(self.request, 'Password reset successful.')
        return redirect('auth:login')