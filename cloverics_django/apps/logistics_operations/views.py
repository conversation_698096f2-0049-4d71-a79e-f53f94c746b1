"""
Logistics operations views extracted from FastAPI
Extracted functionality maintaining all original business logic
"""

from django.shortcuts import render, redirect
from django.http import JsonResponse
from django.contrib.auth.decorators import login_required
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.utils import timezone
from django.db import connection, transaction
import json
import logging

logger = logging.getLogger(__name__)

# ============================================================================
# UNIFIED ROUTE CREATION SYSTEM - EXTRACTED FROM FASTAPI
# ============================================================================

@csrf_exempt
@require_http_methods(["POST"])
@login_required
def create_unified_route(request):
    """Create new route with unified capacity management - EXTRACTED FROM FASTAPI"""
    if request.user.user_type != 'LOGISTICS_PROVIDER':
        return JsonResponse({'success': False, 'error': 'Access denied'}, status=403)
    
    try:
        # Handle both JSON and form data
        content_type = request.META.get('CONTENT_TYPE', '')
        if 'application/json' in content_type:
            data = json.loads(request.body)
        else:
            data = dict(request.POST)
            # Convert lists to single values for form data
            for key, value in data.items():
                if isinstance(value, list) and len(value) == 1:
                    data[key] = value[0]
        
        logger.info(f"Route creation request - Content-Type: {content_type}")
        logger.info(f"Route creation data received: {data}")
        
        # Calculate initial available capacity (start at 100% available)
        total_capacity = int(data.get('total_capacity_kg', data.get('totalCapacity', data.get('available_space', 1000))))
        available_capacity = total_capacity
        is_private = data.get('is_private') == 'true' or data.get('service_type') == 'private'
        
        # Extract and validate all required fields with proper defaults
        try:
            base_price = float(data.get('base_price', 0)) if data.get('base_price') else 0.0
            price_per_kg = float(data.get('price_per_kg', 0)) if data.get('price_per_kg') else 0.0
            price_per_km = float(data.get('price_per_km', 0)) if data.get('price_per_km') else 0.0
            estimated_days = int(data.get('estimated_days', 7)) if data.get('estimated_days') else 7
            available_space = int(data.get('available_space', 1000)) if data.get('available_space') else 1000
            distance_km = float(data.get('distance_km', 100)) if data.get('distance_km') else 100.0
            typical_duration_days = int(data.get('typical_duration_days', 7)) if data.get('typical_duration_days') else 7
            volume_factor = float(data.get('volume_factor', 10.0)) if data.get('volume_factor') else 10.0
            weight_factor = float(data.get('weight_factor', 1.5)) if data.get('weight_factor') else 1.5
            fuel_surcharge = float(data.get('fuel_surcharge', 8.0)) if data.get('fuel_surcharge') else 8.0
            handling_fee = float(data.get('handling_fee', 25.0)) if data.get('handling_fee') else 25.0
            min_fill_threshold = int(data.get('min_fill_threshold', 70)) if data.get('min_fill_threshold') else 70
        except (ValueError, TypeError) as e:
            return JsonResponse({
                "success": False,
                "error": f"Invalid numeric values in form fields: {str(e)}"
            }, status=400)
            
        # Validate required fields
        if not all([
            data.get('origin_country'),
            data.get('destination_country'),
            data.get('origin_city'),
            data.get('destination_city'),
            data.get('transport_type'),
            data.get('container_type'),
            base_price > 0,
            price_per_kg > 0,
            price_per_km > 0
        ]):
            return JsonResponse({
                "success": False,
                "error": "Missing required fields. Please fill all fields including valid pricing."
            }, status=400)
        
        # Build location string with state support
        origin_loc = data.get('origin_city', '')
        if data.get('origin_state'):
            origin_loc += f", {data.get('origin_state')}"
        origin_loc += f", {data.get('origin_country', '')}"
        
        dest_loc = data.get('destination_city', '')
        if data.get('destination_state'):
            dest_loc += f", {data.get('destination_state')}"
        dest_loc += f", {data.get('destination_country', '')}"
        
        # Create route in database with transaction
        with transaction.atomic():
            with connection.cursor() as cursor:
                # First create the route
                cursor.execute("""
                    INSERT INTO shipments_route (
                        origin_country, origin_city, destination_country, destination_city,
                        origin_state, destination_state, distance_km, typical_duration_days, shipping_type,
                        is_private, total_capacity_kg, available_capacity_kg,
                        container_status, min_fill_threshold, route_summary,
                        created_at, updated_at
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW(), NOW())
                    RETURNING id
                """, [
                    data.get('origin_country'), data.get('origin_city'),
                    data.get('destination_country'), data.get('destination_city'),
                    data.get('origin_state', ''), data.get('destination_state', ''),
                    distance_km, typical_duration_days, data.get('transport_type'),
                    is_private, total_capacity, available_capacity,
                    'available', min_fill_threshold, f"{origin_loc} → {dest_loc}"
                ])
                
                route_result = cursor.fetchone()
                if not route_result:
                    raise Exception("Failed to create route")
                
                route_id = route_result[0]
                
                # Get transport type ID
                cursor.execute("SELECT id FROM shipments_transporttype WHERE type = %s", [data.get('transport_type')])
                transport_result = cursor.fetchone()
                if not transport_result:
                    # Create transport type if it doesn't exist
                    cursor.execute("""
                        INSERT INTO shipments_transporttype (type, description, created_at, updated_at)
                        VALUES (%s, %s, NOW(), NOW()) RETURNING id
                    """, [data.get('transport_type'), f"{data.get('transport_type').title()} Transport"])
                    transport_result = cursor.fetchone()
                
                transport_type_id = transport_result[0]
                
                # Create shipping rate
                cursor.execute("""
                    INSERT INTO shipments_shippingrate (
                        logistics_provider_id, route_id, transport_type_id, container_type,
                        base_price, price_per_kg, price_per_km, estimated_days, available_space,
                        volume_factor, weight_factor, fuel_surcharge, handling_fee,
                        is_active, created_at, updated_at
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW(), NOW())
                    RETURNING id
                """, [
                    request.user.id, route_id, transport_type_id, data.get('container_type'),
                    base_price, price_per_kg, price_per_km, estimated_days, available_space,
                    volume_factor, weight_factor, fuel_surcharge, handling_fee, True
                ])
                
                rate_result = cursor.fetchone()
                if not rate_result:
                    raise Exception("Failed to create shipping rate")
                
                rate_id = rate_result[0]
        
        return JsonResponse({
            "success": True,
            "message": f"Route created successfully: {origin_loc} → {dest_loc}",
            "route_id": route_id,
            "rate_id": rate_id,
            "service_type": "Private Route" if is_private else "Public Route"
        })
        
    except Exception as e:
        logger.error(f"Error creating unified route: {e}")
        return JsonResponse({
            "success": False,
            "error": f"Failed to create route: {str(e)}"
        }, status=500)

@login_required
def unified_manage_shipments(request):
    """Unified shipments management page - EXTRACTED FROM FASTAPI"""
    if request.user.user_type != 'LOGISTICS_PROVIDER':
        return redirect('/login')
    
    try:
        # Get route statistics
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT 
                    COUNT(*) as total_routes,
                    COUNT(CASE WHEN is_private = false THEN 1 END) as public_routes,
                    COUNT(CASE WHEN is_private = true THEN 1 END) as private_routes,
                    COUNT(CASE WHEN container_status = 'available' THEN 1 END) as available_routes,
                    AVG(CASE WHEN total_capacity_kg > 0 THEN 
                        ((total_capacity_kg - available_capacity_kg) * 100.0 / total_capacity_kg) 
                        ELSE 0 END) as avg_utilization
                FROM shipments_route sr
                JOIN shipments_shippingrate ssr ON sr.id = ssr.route_id
                WHERE ssr.logistics_provider_id = %s AND ssr.is_active = true
            """, [request.user.id])
            
            stats = cursor.fetchone()
            route_stats = {
                'total_routes': stats[0] if stats else 0,
                'public_routes': stats[1] if stats else 0,
                'private_routes': stats[2] if stats else 0,
                'available_routes': stats[3] if stats else 0,
                'avg_utilization': round(stats[4], 1) if stats and stats[4] else 0
            }
        
        context = {
            'user': request.user,
            'title': 'Unified Manage Shipments - Cloverics',
            'route_stats': route_stats
        }
        
        return render(request, 'logistics/unified_manage_shipments.html', context)
        
    except Exception as e:
        logger.error(f"Error loading unified manage shipments: {e}")
        return render(request, 'error.html', {
            'error_message': 'Unable to load shipments management page'
        })

@login_required
def get_unified_routes(request):
    """Get unified routes for logistics provider - EXTRACTED FROM FASTAPI"""
    if request.user.user_type != 'LOGISTICS_PROVIDER':
        return JsonResponse({'success': False, 'error': 'Access denied'}, status=403)
    
    try:
        route_type = request.GET.get('type', 'all')  # all, public, private, container
        
        # Build query based on route type
        base_query = """
            SELECT sr.id, sr.origin_city, sr.origin_country, sr.destination_city, 
                   sr.destination_country, sr.route_summary, sr.is_private,
                   sr.total_capacity_kg, sr.available_capacity_kg, sr.container_status,
                   ssr.base_price, ssr.price_per_kg, ssr.transport_type_id,
                   st.type as transport_type, ssr.container_type,
                   sr.created_at, sr.updated_at
            FROM shipments_route sr
            JOIN shipments_shippingrate ssr ON sr.id = ssr.route_id
            LEFT JOIN shipments_transporttype st ON ssr.transport_type_id = st.id
            WHERE ssr.logistics_provider_id = %s AND ssr.is_active = true
        """
        
        params = [request.user.id]
        
        if route_type == 'public':
            base_query += " AND sr.is_private = false"
        elif route_type == 'private':
            base_query += " AND sr.is_private = true"
        elif route_type == 'container':
            base_query += " AND sr.total_capacity_kg > 0"
        
        base_query += " ORDER BY sr.created_at DESC LIMIT 50"
        
        with connection.cursor() as cursor:
            cursor.execute(base_query, params)
            routes = []
            
            for row in cursor.fetchall():
                utilization = 0
                if row[7] and row[7] > 0:  # total_capacity_kg
                    utilization = ((row[7] - row[8]) / row[7]) * 100  # (total - available) / total * 100
                
                routes.append({
                    'id': row[0],
                    'origin': f"{row[1]}, {row[2]}",
                    'destination': f"{row[3]}, {row[4]}",
                    'route_summary': row[5],
                    'is_private': row[6],
                    'total_capacity_kg': row[7],
                    'available_capacity_kg': row[8],
                    'container_status': row[9],
                    'base_price': float(row[10]) if row[10] else 0,
                    'price_per_kg': float(row[11]) if row[11] else 0,
                    'transport_type': row[13] or 'truck',
                    'container_type': row[14] or 'standard',
                    'utilization_percentage': round(utilization, 1),
                    'status_class': 'success' if row[9] == 'available' else 'warning',
                    'created_at': row[15].strftime('%Y-%m-%d') if row[15] else 'Unknown'
                })
        
        return JsonResponse({
            'success': True,
            'routes': routes,
            'total_routes': len(routes),
            'route_type': route_type
        })
        
    except Exception as e:
        logger.error(f"Error getting unified routes: {e}")
        return JsonResponse({
            'success': False,
            'error': str(e),
            'routes': []
        })