from django.contrib import admin
from .models import RouteOptimization, CarrierIntegration, FleetVehicle, TrackingEvent, ShipmentMonitoring

@admin.register(RouteOptimization)
class RouteOptimizationAdmin(admin.ModelAdmin):
    list_display = ('route_id', 'origin_country', 'origin_city', 'destination_country', 'destination_city', 'distance_km', 'fuel_cost_estimate', 'is_active', 'created_at')
    list_filter = ('is_active', 'optimization_algorithm', 'created_at')
    search_fields = ('route_id', 'origin_country', 'origin_city', 'destination_country', 'destination_city')
    readonly_fields = ('created_at', 'updated_at')

@admin.register(CarrierIntegration)
class CarrierIntegrationAdmin(admin.ModelAdmin):
    list_display = ('carrier_name', 'sync_status', 'success_rate', 'last_sync', 'is_active')
    list_filter = ('carrier_name', 'sync_status', 'is_active')
    search_fields = ('carrier_name', 'api_endpoint')
    readonly_fields = ('created_at', 'updated_at', 'last_sync')

@admin.register(FleetVehicle)
class FleetVehicleAdmin(admin.ModelAdmin):
    list_display = ('vehicle_id', 'vehicle_type', 'license_plate', 'status', 'capacity_kg', 'owner', 'is_active')
    list_filter = ('vehicle_type', 'status', 'is_active')
    search_fields = ('vehicle_id', 'license_plate', 'owner__email')
    readonly_fields = ('created_at', 'updated_at')

@admin.register(TrackingEvent)
class TrackingEventAdmin(admin.ModelAdmin):
    list_display = ('tracking_number', 'event_type', 'event_location', 'event_timestamp', 'carrier_name')
    list_filter = ('event_type', 'carrier_name', 'event_timestamp')
    search_fields = ('tracking_number', 'event_location', 'event_description')
    readonly_fields = ('created_at', 'updated_at')
    date_hierarchy = 'event_timestamp'

@admin.register(ShipmentMonitoring)
class ShipmentMonitoringAdmin(admin.ModelAdmin):
    list_display = ('tracking_number', 'alert_type', 'priority', 'is_resolved', 'created_at')
    list_filter = ('alert_type', 'priority', 'is_resolved')
    search_fields = ('tracking_number', 'alert_message')
    readonly_fields = ('created_at', 'updated_at', 'resolved_at')
    actions = ['mark_resolved']
    
    def mark_resolved(self, request, queryset):
        from django.utils import timezone
        updated = queryset.update(is_resolved=True, resolved_at=timezone.now(), resolved_by=request.user)
        self.message_user(request, f'{updated} alerts marked as resolved.')
    mark_resolved.short_description = 'Mark selected alerts as resolved'