from django.urls import path
from . import views

app_name = 'logistics_operations'

urlpatterns = [
    # Route Optimization
    path('route-optimization/', views.route_optimization_dashboard, name='route_optimization'),
    path('api/route/calculate/', views.calculate_route_optimization, name='calculate_route'),
    
    # Carrier Integration
    path('carrier-integrations/', views.carrier_integration_dashboard, name='carrier_integrations'),
    path('api/carrier/sync/', views.sync_carrier_data, name='sync_carrier'),
    
    # Fleet Management
    path('fleet-management/', views.fleet_management_dashboard, name='fleet_management'),
    path('api/vehicle/location/', views.update_vehicle_location, name='update_vehicle_location'),
    
    # Shipment Tracking
    path('shipment-tracking/', views.shipment_tracking_dashboard, name='shipment_tracking'),
    path('api/tracking/event/', views.create_tracking_event, name='create_tracking_event'),
    path('api/monitoring/resolve/', views.resolve_monitoring_alert, name='resolve_alert'),
]