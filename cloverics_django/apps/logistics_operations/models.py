from django.db import models
from django.conf import settings
from django.utils import timezone
from decimal import Decimal

class RouteOptimization(models.Model):
    """Route optimization and management system"""
    route_id = models.CharField(max_length=50, unique=True)
    origin_country = models.CharField(max_length=100)
    origin_city = models.CharField(max_length=100)
    destination_country = models.CharField(max_length=100)
    destination_city = models.CharField(max_length=100)
    distance_km = models.FloatField(default=0)
    estimated_duration_hours = models.FloatField(default=0)
    fuel_cost_estimate = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    optimization_algorithm = models.CharField(max_length=50, default='dijkstra')
    waypoints = models.JSONField(default=list, blank=True)
    traffic_data = models.JSONField(default=dict, blank=True)
    weather_factors = models.JSONField(default=dict, blank=True)
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='logistics_route_optimizations')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True)

    class Meta:
        db_table = 'logistics_route_optimization'
        indexes = [
            models.Index(fields=['origin_country', 'destination_country']),
            models.Index(fields=['created_by', 'is_active']),
            models.Index(fields=['distance_km']),
        ]

class CarrierIntegration(models.Model):
    """Carrier API integrations and tracking"""
    CARRIER_CHOICES = [
        ('DHL', 'DHL Express'),
        ('FEDEX', 'FedEx'),
        ('UPS', 'UPS'),
        ('MAERSK', 'Maersk'),
        ('LUFTHANSA', 'Lufthansa Cargo'),
        ('TNT', 'TNT Express'),
        ('CUSTOM', 'Custom Carrier'),
    ]
    
    carrier_name = models.CharField(max_length=50, choices=CARRIER_CHOICES)
    api_endpoint = models.URLField(max_length=500)
    api_key_hash = models.CharField(max_length=255)  # Store hashed API key
    tracking_number_prefix = models.CharField(max_length=10, default='')
    supported_services = models.JSONField(default=list)
    rate_api_available = models.BooleanField(default=False)
    tracking_api_available = models.BooleanField(default=True)
    pickup_api_available = models.BooleanField(default=False)
    last_sync = models.DateTimeField(null=True, blank=True)
    sync_status = models.CharField(max_length=20, default='active')
    error_count = models.IntegerField(default=0)
    success_rate = models.FloatField(default=100.0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True)

    class Meta:
        db_table = 'logistics_carrier_integration'
        unique_together = ['carrier_name', 'api_endpoint']

class FleetVehicle(models.Model):
    """Fleet management and vehicle tracking"""
    VEHICLE_TYPES = [
        ('truck', 'Truck'),
        ('van', 'Van'),
        ('trailer', 'Trailer'),
        ('container', 'Container'),
        ('aircraft', 'Aircraft'),
        ('vessel', 'Vessel'),
    ]
    
    STATUS_CHOICES = [
        ('available', 'Available'),
        ('in_transit', 'In Transit'),
        ('maintenance', 'Under Maintenance'),
        ('offline', 'Offline'),
    ]
    
    vehicle_id = models.CharField(max_length=50, unique=True)
    vehicle_type = models.CharField(max_length=20, choices=VEHICLE_TYPES)
    license_plate = models.CharField(max_length=20, unique=True)
    capacity_kg = models.IntegerField()
    capacity_m3 = models.FloatField()
    current_location_lat = models.FloatField(null=True, blank=True)
    current_location_lng = models.FloatField(null=True, blank=True)
    current_location_address = models.TextField(blank=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='available')
    driver_assigned = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True)
    owner = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='owned_vehicles')
    fuel_efficiency_kmpl = models.FloatField(default=8.0)
    maintenance_due_date = models.DateField(null=True, blank=True)
    insurance_expiry = models.DateField(null=True, blank=True)
    last_service_date = models.DateField(null=True, blank=True)
    total_distance_km = models.FloatField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True)

    class Meta:
        db_table = 'logistics_fleet_vehicle'
        indexes = [
            models.Index(fields=['owner', 'status']),
            models.Index(fields=['vehicle_type', 'status']),
            models.Index(fields=['current_location_lat', 'current_location_lng']),
        ]

class TrackingEvent(models.Model):
    """Shipment tracking events and monitoring"""
    EVENT_TYPES = [
        ('created', 'Shipment Created'),
        ('picked_up', 'Picked Up'),
        ('in_transit', 'In Transit'),
        ('customs_clearance', 'Customs Clearance'),
        ('out_for_delivery', 'Out for Delivery'),
        ('delivered', 'Delivered'),
        ('exception', 'Exception'),
        ('delayed', 'Delayed'),
    ]
    
    tracking_number = models.CharField(max_length=100, db_index=True)
    event_type = models.CharField(max_length=30, choices=EVENT_TYPES)
    event_description = models.TextField()
    event_location = models.CharField(max_length=200)
    event_timestamp = models.DateTimeField()
    carrier_event_id = models.CharField(max_length=100, blank=True)
    carrier_name = models.CharField(max_length=50, blank=True)
    vehicle = models.ForeignKey(FleetVehicle, on_delete=models.SET_NULL, null=True, blank=True)
    location_lat = models.FloatField(null=True, blank=True)
    location_lng = models.FloatField(null=True, blank=True)
    additional_data = models.JSONField(default=dict, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'logistics_tracking_event'
        indexes = [
            models.Index(fields=['tracking_number', 'event_timestamp']),
            models.Index(fields=['event_type', 'event_timestamp']),
            models.Index(fields=['carrier_name', 'event_timestamp']),
        ]
        ordering = ['-event_timestamp']

class ShipmentMonitoring(models.Model):
    """Real-time shipment monitoring and alerts"""
    ALERT_TYPES = [
        ('delay', 'Delivery Delay'),
        ('route_deviation', 'Route Deviation'),
        ('temperature', 'Temperature Alert'),
        ('security', 'Security Breach'),
        ('customs_hold', 'Customs Hold'),
        ('delivery_attempt', 'Delivery Attempt Failed'),
    ]
    
    PRIORITY_LEVELS = [
        ('low', 'Low Priority'),
        ('medium', 'Medium Priority'),
        ('high', 'High Priority'),
        ('critical', 'Critical'),
    ]
    
    tracking_number = models.CharField(max_length=100, db_index=True)
    alert_type = models.CharField(max_length=30, choices=ALERT_TYPES)
    priority = models.CharField(max_length=10, choices=PRIORITY_LEVELS, default='medium')
    alert_message = models.TextField()
    location = models.CharField(max_length=200, blank=True)
    expected_delivery_time = models.DateTimeField(null=True, blank=True)
    actual_delivery_time = models.DateTimeField(null=True, blank=True)
    delay_duration_hours = models.FloatField(default=0)
    temperature_celsius = models.FloatField(null=True, blank=True)
    humidity_percentage = models.FloatField(null=True, blank=True)
    is_resolved = models.BooleanField(default=False)
    resolved_at = models.DateTimeField(null=True, blank=True)
    resolved_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'logistics_shipment_monitoring'
        indexes = [
            models.Index(fields=['tracking_number', 'is_resolved']),
            models.Index(fields=['priority', 'is_resolved']),
            models.Index(fields=['alert_type', 'created_at']),
        ]
        ordering = ['-created_at']