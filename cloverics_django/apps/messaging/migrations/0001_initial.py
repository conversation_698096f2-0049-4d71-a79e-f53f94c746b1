# Generated by Django 5.2.1 on 2025-08-07 12:45

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Conversation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(blank=True, max_length=255, null=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('avatar', models.ImageField(blank=True, null=True, upload_to='conversation_avatars/')),
                ('chat_type', models.CharField(choices=[('support', 'Support Team'), ('logistics', 'Logistics Partner'), ('driver', 'Driver'), ('customs', 'Customs Officer'), ('general', 'General User'), ('group', 'Group Chat'), ('broadcast', 'Broadcast')], db_index=True, default='general', max_length=20)),
                ('related_shipment_id', models.Char<PERSON>ield(blank=True, db_index=True, max_length=50, null=True)),
                ('related_declaration_id', models.CharField(blank=True, db_index=True, max_length=50, null=True)),
                ('related_policy_id', models.CharField(blank=True, db_index=True, max_length=50, null=True)),
                ('related_quote_id', models.CharField(blank=True, db_index=True, max_length=50, null=True)),
                ('status', models.CharField(choices=[('active', 'Active'), ('archived', 'Archived'), ('muted', 'Muted'), ('blocked', 'Blocked'), ('deleted', 'Deleted')], db_index=True, default='active', max_length=20)),
                ('priority', models.CharField(choices=[('low', 'Low'), ('normal', 'Normal'), ('high', 'High'), ('urgent', 'Urgent'), ('critical', 'Critical')], db_index=True, default='normal', max_length=20)),
                ('is_encrypted', models.BooleanField(default=True, help_text='Conversations are encrypted by default for security')),
                ('encryption_key_id', models.CharField(blank=True, help_text='ID of the encryption key used for this conversation', max_length=100)),
                ('allow_attachments', models.BooleanField(default=True)),
                ('allow_reactions', models.BooleanField(default=True)),
                ('allow_editing', models.BooleanField(default=True)),
                ('auto_archive_days', models.IntegerField(default=30)),
                ('message_count', models.IntegerField(default=0)),
                ('last_message_at', models.DateTimeField(blank=True, null=True)),
                ('last_activity_at', models.DateTimeField(auto_now=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('archived_at', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'ordering': ['-last_activity_at'],
            },
        ),
        migrations.CreateModel(
            name='ConversationParticipant',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('role', models.CharField(choices=[('owner', 'Owner'), ('admin', 'Admin'), ('moderator', 'Moderator'), ('member', 'Member'), ('readonly', 'Read Only')], default='member', max_length=20)),
                ('is_muted', models.BooleanField(default=False)),
                ('is_blocked', models.BooleanField(default=False)),
                ('notification_preferences', models.JSONField(default=dict)),
                ('joined_at', models.DateTimeField(auto_now_add=True)),
                ('last_read_at', models.DateTimeField(blank=True, null=True)),
                ('last_activity_at', models.DateTimeField(auto_now=True)),
                ('conversation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='messaging.conversation')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.AddField(
            model_name='conversation',
            name='participants',
            field=models.ManyToManyField(related_name='conversations', through='messaging.ConversationParticipant', to=settings.AUTH_USER_MODEL),
        ),
        migrations.CreateModel(
            name='EncryptionKey',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('key_id', models.CharField(help_text='Unique identifier for the encryption key', max_length=100, unique=True)),
                ('key_type', models.CharField(choices=[('conversation', 'Conversation Key'), ('message', 'Message Key'), ('system', 'System Key')], default='conversation', max_length=20)),
                ('status', models.CharField(choices=[('active', 'Active'), ('rotated', 'Rotated'), ('compromised', 'Compromised'), ('expired', 'Expired')], default='active', max_length=20)),
                ('key_version', models.IntegerField(default=1)),
                ('key_size', models.IntegerField(default=256)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('expires_at', models.DateTimeField(blank=True, null=True)),
                ('rotated_at', models.DateTimeField(blank=True, null=True)),
                ('authorized_users', models.ManyToManyField(blank=True, related_name='authorized_encryption_keys', to=settings.AUTH_USER_MODEL)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_encryption_keys', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='EncryptionPolicy',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('policy_type', models.CharField(choices=[('conversation', 'Conversation Policy'), ('message', 'Message Policy'), ('attachment', 'Attachment Policy'), ('search', 'Search Policy')], max_length=20)),
                ('encryption_level', models.CharField(choices=[('none', 'No Encryption'), ('basic', 'Basic Encryption'), ('standard', 'Standard Encryption'), ('high', 'High Security'), ('military', 'Military Grade')], default='standard', max_length=20)),
                ('chat_types', models.JSONField(default=list, help_text='Chat types this policy applies to')),
                ('user_roles', models.JSONField(default=list, help_text='User roles this policy applies to')),
                ('content_keywords', models.JSONField(default=list, help_text='Keywords that trigger this policy')),
                ('algorithm', models.CharField(default='AES-256-GCM', max_length=50)),
                ('key_rotation_days', models.IntegerField(default=90)),
                ('require_key_verification', models.BooleanField(default=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_encryption_policies', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-is_active', 'name'],
            },
        ),
        migrations.CreateModel(
            name='MessageAttachment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('file', models.FileField(upload_to='chat_attachments/%Y/%m/%d/')),
                ('original_filename', models.CharField(max_length=255)),
                ('file_type', models.CharField(choices=[('document', 'Document'), ('image', 'Image'), ('video', 'Video'), ('audio', 'Audio'), ('archive', 'Archive'), ('other', 'Other')], max_length=20)),
                ('file_size', models.BigIntegerField()),
                ('mime_type', models.CharField(max_length=100)),
                ('width', models.IntegerField(blank=True, null=True)),
                ('height', models.IntegerField(blank=True, null=True)),
                ('duration', models.FloatField(blank=True, null=True)),
                ('is_public', models.BooleanField(default=False)),
                ('access_token', models.CharField(blank=True, max_length=100)),
                ('uploaded_at', models.DateTimeField(auto_now_add=True)),
                ('uploaded_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='uploaded_attachments', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='Message',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('content_type', models.CharField(choices=[('text', 'Text Message'), ('file', 'File Attachment'), ('system', 'System Message'), ('quote', 'Quote Request'), ('status_update', 'Status Update'), ('payment', 'Payment Notification'), ('customs', 'Customs Update'), ('driver', 'Driver Update')], db_index=True, default='text', max_length=20)),
                ('rich_content', models.JSONField(blank=True, default=dict)),
                ('is_read', models.BooleanField(db_index=True, default=False)),
                ('is_edited', models.BooleanField(default=False)),
                ('is_deleted', models.BooleanField(default=False)),
                ('is_pinned', models.BooleanField(default=False)),
                ('reactions', models.JSONField(default=dict)),
                ('delivered_at', models.DateTimeField(blank=True, null=True)),
                ('read_at', models.DateTimeField(blank=True, null=True)),
                ('is_encrypted', models.BooleanField(default=True, help_text='Messages are encrypted by default for security')),
                ('encryption_key_id', models.CharField(blank=True, help_text='ID of the encryption key used for this message', max_length=100)),
                ('encrypted_content', models.TextField(blank=True, help_text='Encrypted message content')),
                ('content_hash', models.CharField(blank=True, help_text='SHA-256 hash of original content for integrity verification', max_length=64)),
                ('created_at', models.DateTimeField(auto_now_add=True, db_index=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('edited_at', models.DateTimeField(blank=True, null=True)),
                ('conversation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='messages', to='messaging.conversation')),
                ('reply_to', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='replies', to='messaging.message')),
                ('sender', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='messages_sent', to=settings.AUTH_USER_MODEL)),
                ('attachments', models.ManyToManyField(blank=True, to='messaging.messageattachment')),
            ],
            options={
                'ordering': ['created_at'],
            },
        ),
        migrations.CreateModel(
            name='MessageSearch',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('encrypted_search_vector', models.TextField(help_text='Encrypted full-text search vector')),
                ('encrypted_keywords', models.TextField(help_text='Encrypted extracted keywords')),
                ('message_type', models.CharField(blank=True, max_length=20)),
                ('sender_id', models.IntegerField(blank=True, null=True)),
                ('conversation_id', models.IntegerField(blank=True, null=True)),
                ('created_date', models.DateField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('message', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='messaging.message')),
            ],
        ),
        migrations.CreateModel(
            name='SecurityAuditLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('event_type', models.CharField(choices=[('key_created', 'Encryption Key Created'), ('key_rotated', 'Encryption Key Rotated'), ('key_compromised', 'Encryption Key Compromised'), ('message_encrypted', 'Message Encrypted'), ('message_decrypted', 'Message Decrypted'), ('access_denied', 'Access Denied'), ('conversation_created', 'Encrypted Conversation Created'), ('participant_added', 'Participant Added to Encrypted Conversation'), ('participant_removed', 'Participant Removed from Encrypted Conversation')], max_length=50)),
                ('details', models.JSONField(default=dict)),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('user_agent', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('conversation', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='security_audit_events', to='messaging.conversation')),
                ('encryption_key', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='security_audit_events', to='messaging.encryptionkey')),
                ('message', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='security_audit_events', to='messaging.message')),
                ('user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='security_audit_events', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ConversationInvitation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('message', models.TextField(blank=True)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('accepted', 'Accepted'), ('declined', 'Declined'), ('expired', 'Expired')], default='pending', max_length=20)),
                ('expires_at', models.DateTimeField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('responded_at', models.DateTimeField(blank=True, null=True)),
                ('conversation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='invitations', to='messaging.conversation')),
                ('invited_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sent_invitations', to=settings.AUTH_USER_MODEL)),
                ('invited_user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='received_invitations', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'indexes': [models.Index(fields=['invited_user', 'status'], name='messaging_c_invited_979cb6_idx'), models.Index(fields=['expires_at'], name='messaging_c_expires_124514_idx')],
                'unique_together': {('conversation', 'invited_user')},
            },
        ),
        migrations.AddIndex(
            model_name='conversationparticipant',
            index=models.Index(fields=['conversation', 'role'], name='messaging_c_convers_f27317_idx'),
        ),
        migrations.AddIndex(
            model_name='conversationparticipant',
            index=models.Index(fields=['user', 'last_activity_at'], name='messaging_c_user_id_f2dd88_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='conversationparticipant',
            unique_together={('conversation', 'user')},
        ),
        migrations.AddIndex(
            model_name='conversation',
            index=models.Index(fields=['chat_type', 'status'], name='messaging_c_chat_ty_02f687_idx'),
        ),
        migrations.AddIndex(
            model_name='conversation',
            index=models.Index(fields=['priority', 'last_activity_at'], name='messaging_c_priorit_1ec388_idx'),
        ),
        migrations.AddIndex(
            model_name='conversation',
            index=models.Index(fields=['related_shipment_id', 'status'], name='messaging_c_related_ed0edb_idx'),
        ),
        migrations.AddIndex(
            model_name='encryptionkey',
            index=models.Index(fields=['key_type', 'status'], name='messaging_e_key_typ_dee517_idx'),
        ),
        migrations.AddIndex(
            model_name='encryptionkey',
            index=models.Index(fields=['expires_at'], name='messaging_e_expires_3cd83d_idx'),
        ),
        migrations.AddIndex(
            model_name='encryptionkey',
            index=models.Index(fields=['created_at'], name='messaging_e_created_6a18ba_idx'),
        ),
        migrations.AddIndex(
            model_name='encryptionpolicy',
            index=models.Index(fields=['policy_type', 'is_active'], name='messaging_e_policy__2c5812_idx'),
        ),
        migrations.AddIndex(
            model_name='encryptionpolicy',
            index=models.Index(fields=['encryption_level', 'is_active'], name='messaging_e_encrypt_c2b795_idx'),
        ),
        migrations.AddIndex(
            model_name='messageattachment',
            index=models.Index(fields=['file_type', 'uploaded_at'], name='messaging_m_file_ty_609a81_idx'),
        ),
        migrations.AddIndex(
            model_name='messageattachment',
            index=models.Index(fields=['uploaded_by', 'uploaded_at'], name='messaging_m_uploade_9db7e4_idx'),
        ),
        migrations.AddIndex(
            model_name='message',
            index=models.Index(fields=['conversation', 'created_at'], name='messaging_m_convers_7bc91b_idx'),
        ),
        migrations.AddIndex(
            model_name='message',
            index=models.Index(fields=['sender', 'created_at'], name='messaging_m_sender__277197_idx'),
        ),
        migrations.AddIndex(
            model_name='message',
            index=models.Index(fields=['is_read', 'created_at'], name='messaging_m_is_read_0c5cc5_idx'),
        ),
        migrations.AddIndex(
            model_name='message',
            index=models.Index(fields=['content_type', 'created_at'], name='messaging_m_content_557104_idx'),
        ),
        migrations.AddIndex(
            model_name='message',
            index=models.Index(fields=['reply_to'], name='messaging_m_reply_t_db4fa3_idx'),
        ),
        migrations.AddIndex(
            model_name='messagesearch',
            index=models.Index(fields=['message_type', 'created_date'], name='messaging_m_message_474b1d_idx'),
        ),
        migrations.AddIndex(
            model_name='messagesearch',
            index=models.Index(fields=['sender_id', 'created_date'], name='messaging_m_sender__2aa298_idx'),
        ),
        migrations.AddIndex(
            model_name='messagesearch',
            index=models.Index(fields=['conversation_id', 'created_date'], name='messaging_m_convers_6ae7d6_idx'),
        ),
        migrations.AddIndex(
            model_name='securityauditlog',
            index=models.Index(fields=['event_type', 'created_at'], name='messaging_s_event_t_b930a0_idx'),
        ),
        migrations.AddIndex(
            model_name='securityauditlog',
            index=models.Index(fields=['user', 'created_at'], name='messaging_s_user_id_221baf_idx'),
        ),
        migrations.AddIndex(
            model_name='securityauditlog',
            index=models.Index(fields=['conversation', 'created_at'], name='messaging_s_convers_84d07b_idx'),
        ),
    ]
