"""
Chat System URL Patterns
Complete messaging system routing
"""

from django.urls import path
from . import views

app_name = 'messaging'

urlpatterns = [
    # ============================================================================
    # MAIN CHAT INTERFACE
    # ============================================================================
    
    # Main chat dashboard
    path('', views.chat_dashboard, name='chat_dashboard'),
    
    # Conversation management
    path('conversation/<int:conversation_id>/', views.conversation_detail, name='conversation_detail'),
    path('conversation/create/', views.create_conversation, name='create_conversation'),
    path('conversation/<int:conversation_id>/add-participant/', views.add_participant, name='add_participant'),
    path('conversation/<int:conversation_id>/remove-participant/', views.remove_participant, name='remove_participant'),
    
    # ============================================================================
    # MESSAGE OPERATIONS
    # ============================================================================
    
    # Message CRUD operations
    path('message/send/', views.send_message, name='send_message'),
    path('message/<int:message_id>/edit/', views.edit_message, name='edit_message'),
    path('message/<int:message_id>/delete/', views.delete_message, name='delete_message'),
    
    # ============================================================================
    # SEARCH AND FILTERING
    # ============================================================================
    
    # Message search
    path('search/', views.search_messages, name='search_messages'),
    
    # User search for participants
    path('search/users/', views.search_users, name='search_users'),
    
    # ============================================================================
    # SECURITY AND ENCRYPTION
    # ============================================================================
    
    # Security features
    path('security/audit/', views.security_audit_log, name='security_audit'),
    path('conversation/<int:conversation_id>/encryption-status/', views.encryption_status, name='encryption_status'),
    
    # ============================================================================
    # LEGACY COMPATIBILITY ENDPOINTS
    # ============================================================================
    
    # Legacy message pages (redirect to new chat system)
    path('customer/', views.customer_messages_page, name='customer_messages'),
    path('logistics/', views.logistics_messages_page, name='logistics_messages'),
    path('logistics/fastapi/', views.logistics_messages_fastapi_extracted, name='logistics_messages_fastapi'),
    path('forward/<int:message_id>/', views.forward_message_page, name='forward_message'),
    
    # Support and help pages
    path('help/', views.help_center_page, name='help_center'),
    path('contact/', views.contact_page, name='contact'),
    path('support/', views.contact_support_page, name='contact_support'),
    path('contact/submit/', views.submit_contact_form, name='submit_contact_form'),
]