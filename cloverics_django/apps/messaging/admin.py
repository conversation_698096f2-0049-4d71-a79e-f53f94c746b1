"""
Admin configuration for messaging models
"""

from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe

from .models import (
    Conversation, Message, MessageAttachment, ConversationParticipant,
    EncryptionKey, SecurityAuditLog, EncryptionPolicy, MessageSearch,
    ConversationInvitation
)


@admin.register(Conversation)
class ConversationAdmin(admin.ModelAdmin):
    list_display = [
        'id', 'title', 'chat_type', 'status', 'priority', 
        'is_encrypted', 'participant_count', 'message_count', 
        'last_activity_at', 'created_at'
    ]
    list_filter = [
        'chat_type', 'status', 'priority', 'is_encrypted', 
        'created_at', 'last_activity_at'
    ]
    search_fields = ['title', 'description', 'participants__email', 'participants__first_name']
    readonly_fields = ['message_count', 'last_activity_at', 'created_at', 'updated_at']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('title', 'description', 'chat_type', 'avatar')
        }),
        ('Participants', {
            'fields': ()
        }),
        ('Security', {
            'fields': ('is_encrypted', 'encryption_key_id')
        }),
        ('Settings', {
            'fields': ('allow_attachments', 'allow_reactions', 'allow_editing', 'auto_archive_days')
        }),
        ('Status', {
            'fields': ('status', 'priority')
        }),
        ('Related Objects', {
            'fields': ('related_shipment_id', 'related_declaration_id', 'related_policy_id', 'related_quote_id'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at', 'archived_at'),
            'classes': ('collapse',)
        })
    )
    
    def participant_count(self, obj):
        return obj.participants.count()
    participant_count.short_description = 'Participants'
    
    def get_queryset(self, request):
        return super().get_queryset(request).prefetch_related('participants')


@admin.register(Message)
class MessageAdmin(admin.ModelAdmin):
    list_display = [
        'id', 'conversation_link', 'sender_link', 'content_type', 
        'is_read', 'is_encrypted', 'is_edited', 'is_deleted', 
        'created_at'
    ]
    list_filter = [
        'content_type', 'is_read', 'is_encrypted', 'is_edited', 
        'is_deleted', 'created_at', 'conversation__chat_type'
    ]
    search_fields = [
        'sender__email', 'sender__first_name', 
        'conversation__title'
    ]
    readonly_fields = [
        'created_at', 'updated_at', 'edited_at', 'delivered_at', 'read_at', 'get_display_content'
    ]
    filter_horizontal = ['attachments']
    
    fieldsets = (
        ('Message Content', {
            'fields': ('conversation', 'sender', 'get_display_content', 'content_type', 'rich_content')
        }),
        ('Attachments', {
            'fields': ('attachments',)
        }),
        ('Message Status', {
            'fields': ('is_read', 'is_edited', 'is_deleted', 'is_pinned')
        }),
        ('Encryption', {
            'fields': ('is_encrypted', 'encryption_key_id', 'encrypted_content', 'content_hash')
        }),
        ('Replies & Reactions', {
            'fields': ('reply_to', 'reactions'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at', 'edited_at', 'delivered_at', 'read_at'),
            'classes': ('collapse',)
        })
    )
    
    def get_display_content(self, obj):
        """Get decrypted content for display"""
        return obj.get_content()
    get_display_content.short_description = 'Content'
    
    def conversation_link(self, obj):
        if obj.conversation:
            url = reverse('admin:messaging_conversation_change', args=[obj.conversation.id])
            return format_html('<a href="{}">{}</a>', url, obj.conversation.title or f"Conversation {obj.conversation.id}")
        return '-'
    conversation_link.short_description = 'Conversation'
    
    def sender_link(self, obj):
        if obj.sender:
            url = reverse('admin:auth_user_change', args=[obj.sender.id])
            return format_html('<a href="{}">{}</a>', url, obj.sender.get_full_name() or obj.sender.email)
        return '-'
    sender_link.short_description = 'Sender'


@admin.register(ConversationParticipant)
class ConversationParticipantAdmin(admin.ModelAdmin):
    list_display = [
        'conversation', 'user', 'role', 'is_muted', 'is_blocked', 
        'joined_at', 'last_activity_at'
    ]
    list_filter = ['role', 'is_muted', 'is_blocked', 'joined_at']
    search_fields = ['conversation__title', 'user__email', 'user__first_name']
    readonly_fields = ['joined_at', 'last_activity_at']


@admin.register(EncryptionKey)
class EncryptionKeyAdmin(admin.ModelAdmin):
    list_display = [
        'key_id', 'key_type', 'status', 'get_algorithm', 'key_size', 
        'created_by', 'created_at', 'expires_at'
    ]
    list_filter = ['key_type', 'status', 'created_at']
    search_fields = ['key_id', 'created_by__email']
    readonly_fields = ['created_at', 'rotated_at', 'get_algorithm']
    filter_horizontal = ['authorized_users']
    
    fieldsets = (
        ('Key Information', {
            'fields': ('key_id', 'key_type', 'status', 'get_algorithm', 'key_size')
        }),
        ('Access Control', {
            'fields': ('created_by', 'authorized_users')
        }),
        ('Lifecycle', {
            'fields': ('created_at', 'expires_at', 'rotated_at')
        })
    )
    
    def get_algorithm(self, obj):
        """Get algorithm from Django settings"""
        return obj.algorithm
    get_algorithm.short_description = 'Algorithm'
    get_algorithm.admin_order_field = 'key_id'  # Use key_id for sorting since algorithm is a property


@admin.register(SecurityAuditLog)
class SecurityAuditLogAdmin(admin.ModelAdmin):
    list_display = [
        'event_type', 'user', 'conversation_link', 'ip_address', 
        'created_at'
    ]
    list_filter = ['event_type', 'created_at']
    search_fields = [
        'user__email', 'conversation__title', 'ip_address', 
        'user_agent'
    ]
    readonly_fields = ['created_at']
    
    fieldsets = (
        ('Event Information', {
            'fields': ('event_type', 'user', 'details')
        }),
        ('Related Objects', {
            'fields': ('conversation', 'message', 'encryption_key'),
            'classes': ('collapse',)
        }),
        ('Request Information', {
            'fields': ('ip_address', 'user_agent'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at',),
            'classes': ('collapse',)
        })
    )
    
    def conversation_link(self, obj):
        if obj.conversation:
            url = reverse('admin:messaging_conversation_change', args=[obj.conversation.id])
            return format_html('<a href="{}">{}</a>', url, obj.conversation.title or f"Conversation {obj.conversation.id}")
        return '-'
    conversation_link.short_description = 'Conversation'


@admin.register(EncryptionPolicy)
class EncryptionPolicyAdmin(admin.ModelAdmin):
    list_display = [
        'name', 'policy_type', 'encryption_level', 'is_active', 
        'created_by', 'created_at'
    ]
    list_filter = ['policy_type', 'encryption_level', 'is_active', 'created_at']
    search_fields = ['name', 'created_by__email']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('Policy Information', {
            'fields': ('name', 'policy_type', 'encryption_level', 'is_active')
        }),
        ('Policy Rules', {
            'fields': ('chat_types', 'user_roles', 'content_keywords')
        }),
        ('Encryption Settings', {
            'fields': ('algorithm', 'key_rotation_days', 'require_key_verification')
        }),
        ('Access Control', {
            'fields': ('created_by',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )


@admin.register(MessageSearch)
class MessageSearchAdmin(admin.ModelAdmin):
    list_display = [
        'message', 'message_type', 'sender_id', 'conversation_id', 
        'created_date', 'created_at'
    ]
    list_filter = ['message_type', 'created_date', 'created_at']
    search_fields = ['message__sender__email', 'message__conversation__title']
    readonly_fields = ['created_at', 'updated_at']


@admin.register(ConversationInvitation)
class ConversationInvitationAdmin(admin.ModelAdmin):
    list_display = [
        'conversation', 'invited_by', 'invited_user', 'status', 
        'expires_at', 'created_at'
    ]
    list_filter = ['status', 'expires_at', 'created_at']
    search_fields = [
        'conversation__title', 'invited_by__email', 'invited_user__email'
    ]
    readonly_fields = ['created_at', 'responded_at']


@admin.register(MessageAttachment)
class MessageAttachmentAdmin(admin.ModelAdmin):
    list_display = [
        'original_filename', 'file_type', 'file_size', 'mime_type', 
        'uploaded_by', 'uploaded_at'
    ]
    list_filter = ['file_type', 'is_public', 'uploaded_at']
    search_fields = ['original_filename', 'uploaded_by__email']
    readonly_fields = ['uploaded_at']
    
    fieldsets = (
        ('File Information', {
            'fields': ('file', 'original_filename', 'file_type', 'file_size', 'mime_type')
        }),
        ('Media Properties', {
            'fields': ('width', 'height', 'duration'),
            'classes': ('collapse',)
        }),
        ('Access Control', {
            'fields': ('is_public', 'access_token', 'uploaded_by')
        }),
        ('Timestamps', {
            'fields': ('uploaded_at',),
            'classes': ('collapse',)
        })
    ) 