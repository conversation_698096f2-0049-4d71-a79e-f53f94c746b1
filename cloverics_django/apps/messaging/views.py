"""
Enhanced Chat System Views
Complete messaging functionality with encryption and real-time features
"""

from django.shortcuts import render, redirect, get_object_or_404
from django.http import JsonResponse, HttpResponse
from django.contrib.auth.decorators import login_required
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.utils import timezone
from django.db.models import Q, Count, Max
from django.core.paginator import Paginator
from django.contrib import messages as django_messages
from django.db import transaction
from datetime import datetime, timedelta
import json
import logging
import hashlib
import uuid

from .models import (
    Conversation, Message, MessageAttachment, ConversationParticipant,
    EncryptionKey, SecurityAuditLog, EncryptionPolicy, MessageSearch,
    ConversationInvitation
)
from apps.authentication.models import User

logger = logging.getLogger(__name__)


# ============================================================================
# MAIN CHAT INTERFACE
# ============================================================================

@login_required
def chat_dashboard(request):
    """Main chat dashboard showing conversations and messages"""
    user = request.user
    
    # Get user's conversations
    conversations = Conversation.objects.filter(
        participants=user
    ).prefetch_related(
        'participants', 'messages'
    ).annotate(
        unread_count=Count('messages', filter=Q(
            messages__is_read=False,
            messages__sender__in=Conversation.objects.filter(
                participants=user
            ).values('participants')
        ))
    ).order_by('-last_activity_at')
    
    # Get recent messages for preview
    recent_messages = Message.objects.filter(
        conversation__participants=user
    ).select_related('sender', 'conversation').order_by('-created_at')[:10]
    
    context = {
        'user': user,
        'conversations': conversations,
        'recent_messages': recent_messages,
        'unread_total': sum(conv.unread_count for conv in conversations),
    }
    
    return render(request, 'messaging/chat_dashboard.html', context)


@login_required
def conversation_detail(request, conversation_id):
    """Detailed conversation view with messages"""
    user = request.user
    conversation = get_object_or_404(Conversation, id=conversation_id)
    
    # Check access permissions
    if not conversation.can_user_access(user):
        django_messages.error(request, "You don't have access to this conversation.")
        return redirect('chat_dashboard')
    
    # Get messages with pagination
    message_list = conversation.messages.select_related('sender').order_by('created_at')
    paginator = Paginator(message_list, 50)  # 50 messages per page
    page_number = request.GET.get('page')
    messages = paginator.get_page(page_number)
    
    # Decrypt messages for display
    for message in messages:
        message.decrypted_content = message.get_content(conversation.encryption_key_id)
    
    # Mark messages as read
    unread_messages = conversation.messages.filter(
        is_read=False,
        sender__in=conversation.participants.exclude(id=user.id)
    )
    unread_messages.update(is_read=True, read_at=timezone.now())
    
    # Get conversation participants
    participants = conversation.participants.all()
    
    # Check if user can send messages
    participant = ConversationParticipant.objects.filter(
        conversation=conversation,
        user=user
    ).first()
    
    can_send = participant and not participant.is_blocked if participant else True
    
    context = {
        'user': user,
        'conversation': conversation,
        'messages': messages,
        'participants': participants,
        'can_send': can_send,
        'is_encrypted': conversation.is_encrypted,
    }
    
    return render(request, 'messaging/conversation_detail.html', context)


# ============================================================================
# MESSAGE OPERATIONS
# ============================================================================

@csrf_exempt
@require_http_methods(["POST"])
@login_required
def send_message(request):
    """Send a new message to a conversation"""
    try:
        data = json.loads(request.body)
        conversation_id = data.get('conversation_id')
        content = data.get('content', '').strip()
        content_type = data.get('content_type', 'text')
        reply_to_id = data.get('reply_to_id')
        
        if not content:
            return JsonResponse({
                'status': 'error',
                'message': 'Message content cannot be empty'
            }, status=400)
        
        conversation = get_object_or_404(Conversation, id=conversation_id)
        
        # Check access permissions
        if not conversation.can_user_access(request.user):
            return JsonResponse({
                'status': 'error',
                'message': 'You don\'t have access to this conversation'
            }, status=403)
        
        # Check if user is blocked
        participant = ConversationParticipant.objects.filter(
            conversation=conversation,
            user=request.user
        ).first()
        
        if participant and participant.is_blocked:
            return JsonResponse({
                'status': 'error',
                'message': 'You are blocked from sending messages in this conversation'
            }, status=403)
        
        with transaction.atomic():
            # Create message without content first
            message = Message.objects.create(
                conversation=conversation,
                sender=request.user,
                content_type=content_type,
                reply_to_id=reply_to_id,
                is_encrypted=conversation.is_encrypted
            )
            
            # Set content with automatic encryption
            message.set_content(content, conversation.encryption_key_id)
            
            # Log security event if encrypted
            if message.is_encrypted:
                from django.conf import settings
                algorithm = getattr(settings, 'ENCRYPTION_CONFIG', {}).get('ALGORITHM', 'AES-256-GCM')
                SecurityAuditLog.objects.create(
                    event_type='message_encrypted',
                    user=request.user,
                    conversation=conversation,
                    message=message,
                    details={'key_id': message.encryption_key_id, 'algorithm': algorithm}
                )
            
            # Update conversation
            conversation.message_count += 1
            conversation.last_message_at = timezone.now()
            conversation.save(update_fields=['message_count', 'last_message_at'])
            
            # Create search index (for encrypted messages, we store minimal metadata)
            if message.is_encrypted:
                # For encrypted messages, we can't index the content directly
                # Store only metadata for search
                MessageSearch.objects.create(
                    message=message,
                    encrypted_search_vector="",  # Cannot search encrypted content
                    encrypted_keywords="",       # Cannot search encrypted content
                    message_type=content_type,
                    sender_id=request.user.id,
                    conversation_id=conversation.id,
                    created_date=timezone.now().date()
                )
            else:
                # For unencrypted messages, we can index the content
                MessageSearch.objects.create(
                    message=message,
                    encrypted_search_vector=content,  # Store content for search
                    encrypted_keywords=content,       # Store content for search
                    message_type=content_type,
                    sender_id=request.user.id,
                    conversation_id=conversation.id,
                    created_date=timezone.now().date()
                )
        
        # Get content for response (automatically decrypted if encrypted)
        display_content = message.get_content(conversation.encryption_key_id)
        
        return JsonResponse({
            'status': 'success',
            'message': {
                'id': message.id,
                'content': display_content,
                'sender': {
                    'id': message.sender.id,
                    'name': message.sender.get_full_name() or message.sender.email
                },
                'created_at': message.created_at.isoformat(),
                'is_encrypted': message.is_encrypted
            }
        })
        
    except Exception as e:
        logger.error(f"Error sending message: {e}")
        return JsonResponse({
            'status': 'error',
            'message': 'Failed to send message'
        }, status=500)


@csrf_exempt
@require_http_methods(["POST"])
@login_required
def edit_message(request, message_id):
    """Edit an existing message"""
    try:
        data = json.loads(request.body)
        new_content = data.get('content', '').strip()
        
        if not new_content:
            return JsonResponse({
                'status': 'error',
                'message': 'Message content cannot be empty'
            }, status=400)
        
        message = get_object_or_404(Message, id=message_id)
        
        # Check permissions
        if message.sender != request.user:
            return JsonResponse({
                'status': 'error',
                'message': 'You can only edit your own messages'
            }, status=403)
        
        # Edit message
        message.edit_message(new_content, request.user)
        
        # Handle encryption if needed
        if message.is_encrypted:
            encrypted_content = message.encrypt_message_content(new_content, message.encryption_key_id)
            if encrypted_content:
                message.set_encrypted_content(encrypted_content, message.encryption_key_id)
            else:
                # Fallback to unencrypted if encryption fails
                logger.warning(f"Failed to encrypt edited message {message.id}, storing unencrypted")
                message.is_encrypted = False
                message.save(update_fields=['is_encrypted'])
        
        return JsonResponse({
            'status': 'success',
            'message': 'Message updated successfully'
        })
        
    except Exception as e:
        logger.error(f"Error editing message: {e}")
        return JsonResponse({
            'status': 'error',
            'message': 'Failed to edit message'
        }, status=500)


@csrf_exempt
@require_http_methods(["POST"])
@login_required
def delete_message(request, message_id):
    """Soft delete a message"""
    try:
        message = get_object_or_404(Message, id=message_id)
        
        # Check permissions
        if message.sender != request.user:
            return JsonResponse({
                'status': 'error',
                'message': 'You can only delete your own messages'
            }, status=403)
        
        # Soft delete
        message.soft_delete(request.user)
        
        return JsonResponse({
            'status': 'success',
            'message': 'Message deleted successfully'
        })
        
    except Exception as e:
        logger.error(f"Error deleting message: {e}")
        return JsonResponse({
            'status': 'error',
            'message': 'Failed to delete message'
        }, status=500)


# ============================================================================
# CONVERSATION MANAGEMENT
# ============================================================================

@login_required
def create_conversation(request):
    """Create a new conversation"""
    if request.method == 'POST':
        try:
            data = json.loads(request.body)
            participant_ids = data.get('participant_ids', [])
            title = data.get('title', '').strip()
            chat_type = data.get('chat_type', 'general')
            is_encrypted = data.get('is_encrypted', True)  # Default to encrypted
            

            
            if not participant_ids:
                return JsonResponse({
                    'status': 'error',
                    'message': 'At least one participant is required'
                }, status=400)
            
            with transaction.atomic():
                # Create conversation
                conversation = Conversation.objects.create(
                    title=title,
                    chat_type=chat_type,
                    is_encrypted=is_encrypted
                )
                
                # Add participants
                conversation.participants.add(request.user)
                
                # Add other participants by fetching them from the database
                if participant_ids:
                    try:
                        # Ensure participant_ids is a list and convert to proper format
                        if isinstance(participant_ids, str):
                            participant_ids = [participant_ids]
                        
                        # Convert to integers if they're numeric strings
                        clean_participant_ids = []
                        for pid in participant_ids:
                            try:
                                clean_participant_ids.append(int(pid))
                            except (ValueError, TypeError):
                                # If it's not a number, it might be a UUID or other format
                                clean_participant_ids.append(pid)
                        
                        # Try to get users by ID
                        users = User.objects.filter(id__in=clean_participant_ids)
                        
                        if users.count() != len(clean_participant_ids):
                            missing_ids = set(clean_participant_ids) - set(users.values_list('id', flat=True))
                            logger.warning(f"Some users not found: {missing_ids}")
                        
                        conversation.participants.add(*users)
                        
                    except Exception as e:
                        logger.error(f"Error adding participants: {e}")
                        return JsonResponse({
                            'status': 'error',
                            'message': f'Error adding participants: {str(e)}'
                        }, status=400)
                
                # Set up encryption if needed
                if is_encrypted:
                    key_id = str(uuid.uuid4())
                    conversation.set_encryption_key(key_id)
                    
                    # Create encryption key record
                    encryption_key = EncryptionKey.objects.create(
                        key_id=key_id,
                        key_type='conversation',
                        created_by=request.user
                    )
                    
                    # Log security event
                    SecurityAuditLog.objects.create(
                        event_type='conversation_created',
                        user=request.user,
                        conversation=conversation,
                        encryption_key=encryption_key,  # Use the object instead of ID
                        details={'chat_type': chat_type}
                    )
                
                return JsonResponse({
                    'status': 'success',
                    'conversation_id': conversation.id,
                    'message': 'Conversation created successfully'
                })
                
        except Exception as e:
            logger.error(f"Error creating conversation: {e}")
            return JsonResponse({
                'status': 'error',
                'message': 'Failed to create conversation'
            }, status=500)
    
    # GET request - show create conversation form
    return render(request, 'messaging/create_conversation.html', {
        'user': request.user
    })


@csrf_exempt
@require_http_methods(["POST"])
@login_required
def add_participant(request, conversation_id):
    """Add a participant to a conversation"""
    try:
        data = json.loads(request.body)
        participant_id = data.get('participant_id')
        role = data.get('role', 'member')
        
        conversation = get_object_or_404(Conversation, id=conversation_id)
        
        # Check if user has permission to add participants
        participant = ConversationParticipant.objects.filter(
            conversation=conversation,
            user=request.user
        ).first()
        
        if not participant or participant.role not in ['owner', 'admin']:
            return JsonResponse({
                'status': 'error',
                'message': 'You don\'t have permission to add participants'
            }, status=403)
        
        # Add participant
        conversation.add_participant(participant_id, role)
        
        # Log security event
        SecurityAuditLog.objects.create(
            event_type='participant_added',
            user=request.user,
            conversation=conversation,
            details={'added_user_id': participant_id, 'role': role}
        )
        
        return JsonResponse({
            'status': 'success',
            'message': 'Participant added successfully'
        })
        
    except Exception as e:
        logger.error(f"Error adding participant: {e}")
        return JsonResponse({
            'status': 'error',
            'message': 'Failed to add participant'
        }, status=500)


@csrf_exempt
@require_http_methods(["POST"])
@login_required
def remove_participant(request, conversation_id):
    """Remove a participant from a conversation"""
    try:
        data = json.loads(request.body)
        participant_id = data.get('participant_id')
        
        conversation = get_object_or_404(Conversation, id=conversation_id)
        
        # Check if user has permission to remove participants
        participant = ConversationParticipant.objects.filter(
            conversation=conversation,
            user=request.user
        ).first()
        
        if not participant or participant.role not in ['owner', 'admin']:
            return JsonResponse({
                'status': 'error',
                'message': 'You don\'t have permission to remove participants'
            }, status=403)
        
        # Remove participant
        conversation.remove_participant(participant_id)
        
        # Log security event
        SecurityAuditLog.objects.create(
            event_type='participant_removed',
            user=request.user,
            conversation=conversation,
            details={'removed_user_id': participant_id}
        )
        
        return JsonResponse({
            'status': 'success',
            'message': 'Participant removed successfully'
        })
        
    except Exception as e:
        logger.error(f"Error removing participant: {e}")
        return JsonResponse({
            'status': 'error',
            'message': 'Failed to remove participant'
        }, status=500)


# ============================================================================
# SEARCH AND FILTERING
# ============================================================================

@login_required
def search_messages(request):
    """Search messages across conversations"""
    query = request.GET.get('q', '').strip()
    conversation_id = request.GET.get('conversation_id')
    date_from = request.GET.get('date_from')
    date_to = request.GET.get('date_to')
    
    if not query:
        return JsonResponse({'status': 'error', 'message': 'Search query is required'})
    
    # Build search query
    search_q = Q(conversation__participants=request.user)
    
    if conversation_id:
        search_q &= Q(conversation_id=conversation_id)
    
    if date_from:
        search_q &= Q(created_at__date__gte=date_from)
    
    if date_to:
        search_q &= Q(created_at__date__lte=date_to)
    
    # Search in message content (for non-encrypted messages)
    messages = Message.objects.filter(
        search_q & Q(content__icontains=query, is_encrypted=False)
    ).select_related('sender', 'conversation').order_by('-created_at')
    
    # For encrypted messages, we can't search content directly, but we can search metadata
    # In a real implementation, you might use encrypted search or searchable encryption
    encrypted_messages = Message.objects.filter(
        search_q & Q(is_encrypted=True)
    ).select_related('sender', 'conversation').order_by('-created_at')
    
    # Combine results
    all_messages = list(messages) + list(encrypted_messages)
    
    # Paginate results
    paginator = Paginator(all_messages, 20)
    page_number = request.GET.get('page', 1)
    results = paginator.get_page(page_number)
    
    return JsonResponse({
        'status': 'success',
        'results': [{
            'id': msg.id,
            'content': msg.get_content() if not msg.is_encrypted else '[Encrypted Message]',
            'sender': msg.sender.get_full_name() or msg.sender.email,
            'conversation': msg.conversation.title or f"Conversation {msg.conversation.id}",
            'created_at': msg.created_at.isoformat(),
            'is_encrypted': msg.is_encrypted
        } for msg in results],
        'total_count': paginator.count,
        'has_next': results.has_next(),
        'has_previous': results.has_previous()
    })


@login_required
def search_users(request):
    """Search users for conversation participants"""
    query = request.GET.get('q', '').strip()
    
    if not query or len(query) < 2:
        return JsonResponse({
            'status': 'error',
            'message': 'Search query must be at least 2 characters long'
        }, status=400)
    
    # Search users by name, email, or company name
    users = User.objects.filter(
        Q(first_name__icontains=query) |
        Q(last_name__icontains=query) |
        Q(email__icontains=query) |
        Q(company_name__icontains=query)
    ).exclude(
        id=request.user.id  # Exclude current user
    ).filter(
        is_active=True  # Only active users
    ).order_by('first_name', 'last_name', 'company_name')[:10]  # Limit to 10 results
    
    return JsonResponse({
        'status': 'success',
        'users': [{
            'id': user.id,
            'name': user.display_name,
            'email': user.email,
            'user_type': user.user_type,
            'company_name': user.company_name,
            'avatar': user.avatar.url if user.avatar else None
        } for user in users]
    })


# ============================================================================
# SECURITY AND ENCRYPTION
# ============================================================================

@login_required
def security_audit_log(request):
    """View security audit log for user's conversations"""
    user = request.user
    
    # Get audit events for user's conversations
    audit_events = SecurityAuditLog.objects.filter(
        Q(user=user) | Q(conversation__participants=user)
    ).select_related('conversation', 'message', 'encryption_key').order_by('-created_at')
    
    # Paginate results
    paginator = Paginator(audit_events, 50)
    page_number = request.GET.get('page', 1)
    events = paginator.get_page(page_number)
    
    return render(request, 'messaging/security_audit.html', {
        'user': user,
        'events': events,
        'total_events': paginator.count
    })


@login_required
def encryption_status(request, conversation_id):
    """Get encryption status for a conversation"""
    conversation = get_object_or_404(Conversation, id=conversation_id)
    
    if not conversation.can_user_access(request.user):
        return JsonResponse({
            'status': 'error',
            'message': 'Access denied'
        }, status=403)
    
    # Get encryption key info
    key_info = None
    if conversation.encryption_key_id:
        try:
            key = EncryptionKey.objects.get(key_id=conversation.encryption_key_id)
            key_info = {
                'key_id': key.key_id,
                'status': key.status,
                'algorithm': key.algorithm,
                'created_at': key.created_at.isoformat(),
                'expires_at': key.expires_at.isoformat() if key.expires_at else None
            }
        except EncryptionKey.DoesNotExist:
            pass
    
    return JsonResponse({
        'status': 'success',
        'conversation_id': conversation.id,
        'is_encrypted': conversation.is_encrypted,
        'encryption_key_id': conversation.encryption_key_id,
        'key_info': key_info,
        'encrypted_messages_count': conversation.get_encrypted_messages_count()
    })


# ============================================================================
# LEGACY COMPATIBILITY (keeping existing endpoints)
# ============================================================================

@login_required
def customer_messages_page(request):
    """Customer messages page - Legacy compatibility"""
    return redirect('chat_dashboard')

@login_required
def logistics_messages_page(request):
    """Logistics provider messages page - Legacy compatibility"""
    return redirect('chat_dashboard')

@login_required
def logistics_messages_fastapi_extracted(request):
    """Logistics provider messages page - Legacy compatibility"""
    return redirect('chat_dashboard')

@login_required
def forward_message_page(request, message_id):
    """Forward a specific message - Legacy compatibility"""
    return redirect('chat_dashboard')

@login_required 
def help_center_page(request):
    """Help center and documentation - Legacy compatibility"""
    return render(request, 'help.html', {
        'user': request.user,
        'title': "Help Center - Cloverics"
    })

@login_required
def contact_page(request):
    """Contact support page - Legacy compatibility"""
    return render(request, 'contact.html', {
        'user': request.user,
        'title': "Contact Us - Cloverics"
    })

@login_required
def contact_support_page(request):
    """Contact & Support page - Legacy compatibility"""
    return render(request, 'contact_support.html', {
        'user': request.user,
        'title': "Contact & Support - Cloverics"
    })

@csrf_exempt
@require_http_methods(["POST"])
@login_required
def submit_contact_form(request):
    """Submit contact form - Legacy compatibility"""
    try:
        data = json.loads(request.body)
        # TODO: Implement contact form submission
        return JsonResponse({
            "status": "success", 
            "message": "Your message has been submitted successfully."
        })
    except Exception as e:
        logger.error(f"Error submitting contact form: {e}")
        return JsonResponse({
            "status": "error",
            "message": "There was an error submitting your message."
        })