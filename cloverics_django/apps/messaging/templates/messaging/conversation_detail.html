{% extends 'base.html' %}
{% load static %}

{% block title %}{{ conversation.title|default:"Chat" }} - Cloverics{% endblock %}

{% block extra_css %}
<style>
    /* Modern CSS Variables */
    :root {
        --primary-color: #2196f3;
        --primary-dark: #1976d2;
        --secondary-color: #f50057;
        --success-color: #4caf50;
        --warning-color: #ff9800;
        --danger-color: #f44336;
        --light-bg: #f8f9fa;
        --white: #ffffff;
        --gray-100: #f8f9fa;
        --gray-200: #e9ecef;
        --gray-300: #dee2e6;
        --gray-400: #ced4da;
        --gray-500: #adb5bd;
        --gray-600: #6c757d;
        --gray-700: #495057;
        --gray-800: #343a40;
        --gray-900: #212529;
        --shadow-sm: 0 2px 4px rgba(0,0,0,0.1);
        --shadow-md: 0 4px 6px rgba(0,0,0,0.1);
        --shadow-lg: 0 10px 15px rgba(0,0,0,0.1);
        --border-radius: 12px;
        --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    /* Global Styles */
    * {
        box-sizing: border-box;
    }

    body {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        margin: 0;
        padding: 0;
    }

    /* Chat Container */
    .chat-container {
        display: flex;
        flex-direction: column;
        height: 100vh;
        background: var(--light-bg);
        position: relative;
    }

    /* Chat Header */
    .chat-header {
        background: var(--white);
        border-bottom: 1px solid var(--gray-200);
        padding: 1rem 1.5rem;
        display: flex;
        align-items: center;
        justify-content: space-between;
        box-shadow: var(--shadow-sm);
        position: relative;
        z-index: 10;
    }

    .chat-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    }

    .chat-info {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .chat-avatar {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: var(--white);
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 700;
        font-size: 1.25rem;
        box-shadow: var(--shadow-md);
    }

    .chat-details h4 {
        margin: 0;
        font-size: 1.25rem;
        font-weight: 700;
        color: var(--gray-800);
    }

    .chat-meta {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-top: 0.25rem;
    }

    .chat-type {
        background: var(--primary-color);
        color: var(--white);
        padding: 0.25rem 0.75rem;
        border-radius: 15px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
    }

    .encryption-status {
        display: flex;
        align-items: center;
        gap: 0.25rem;
        color: var(--success-color);
        font-size: 0.875rem;
        font-weight: 500;
    }

    .encryption-status i {
        font-size: 1rem;
    }

    .chat-actions {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        border: none;
        background: var(--gray-100);
        color: var(--gray-600);
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: var(--transition);
        position: relative;
    }

    .btn-icon:hover {
        background: var(--primary-color);
        color: var(--white);
        transform: scale(1.1);
    }

    .btn-icon.active {
        background: var(--primary-color);
        color: var(--white);
    }

    /* Messages Area */
    .messages-area {
        flex: 1;
        overflow-y: auto;
        padding: 1rem;
        background: var(--light-bg);
        position: relative;
    }

    .messages-area::-webkit-scrollbar {
        width: 8px;
    }

    .messages-area::-webkit-scrollbar-track {
        background: var(--gray-100);
        border-radius: 4px;
    }

    .messages-area::-webkit-scrollbar-thumb {
        background: var(--gray-300);
        border-radius: 4px;
    }

    .messages-area::-webkit-scrollbar-thumb:hover {
        background: var(--gray-400);
    }

    .messages-container {
        max-width: 800px;
        margin: 0 auto;
        padding-bottom: 2rem;
    }

    .message-group {
        margin-bottom: 1.5rem;
    }

    .message-date {
        text-align: center;
        margin: 2rem 0 1rem;
        position: relative;
    }

    .message-date::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 0;
        right: 0;
        height: 1px;
        background: var(--gray-300);
        z-index: 0;
    }

    .message-date span {
        background: var(--white);
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.875rem;
        color: var(--gray-600);
        font-weight: 500;
        position: relative;
        z-index: 1;
        box-shadow: var(--shadow-sm);
    }

    .message {
        display: flex;
        margin-bottom: 1rem;
        animation: slideInUp 0.3s ease-out;
    }

    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .message.sent {
        justify-content: flex-end;
    }

    .message.received {
        justify-content: flex-start;
    }

    .message-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: var(--white);
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        font-size: 0.875rem;
        margin: 0 0.75rem;
        flex-shrink: 0;
    }

    .message-content {
        max-width: 70%;
        position: relative;
    }

    .message-bubble {
        padding: 1rem 1.25rem;
        border-radius: 20px;
        position: relative;
        word-wrap: break-word;
        line-height: 1.4;
    }

    .message.sent .message-bubble {
        background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
        color: var(--white);
        border-bottom-right-radius: 5px;
    }

    .message.received .message-bubble {
        background: var(--white);
        color: var(--gray-800);
        border: 1px solid var(--gray-200);
        border-bottom-left-radius: 5px;
        box-shadow: var(--shadow-sm);
    }

    .message-bubble::before {
        content: '';
        position: absolute;
        bottom: 0;
        width: 0;
        height: 0;
        border: 8px solid transparent;
    }

    .message.sent .message-bubble::before {
        right: -8px;
        border-left-color: var(--primary-dark);
        border-bottom: none;
    }

    .message.received .message-bubble::before {
        left: -8px;
        border-right-color: var(--white);
        border-bottom: none;
    }

    .message-meta {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-top: 0.5rem;
        font-size: 0.75rem;
        color: var(--gray-500);
    }

    .message.sent .message-meta {
        justify-content: flex-end;
        color: rgba(255, 255, 255, 0.7);
    }

    .message-time {
        font-weight: 500;
    }

    .message-status {
        display: flex;
        align-items: center;
        gap: 0.25rem;
    }

    .message-status i {
        font-size: 0.875rem;
    }

    .message-actions {
        position: absolute;
        top: -10px;
        right: -10px;
        background: var(--white);
        border-radius: 20px;
        box-shadow: var(--shadow-md);
        padding: 0.25rem;
        display: none;
        gap: 0.25rem;
        z-index: 10;
    }

    .message:hover .message-actions {
        display: flex;
    }

    .message-action-btn {
        width: 28px;
        height: 28px;
        border-radius: 50%;
        border: none;
        background: var(--gray-100);
        color: var(--gray-600);
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: var(--transition);
        font-size: 0.75rem;
    }

    .message-action-btn:hover {
        background: var(--primary-color);
        color: var(--white);
        transform: scale(1.1);
    }

    .message-action-btn.edit:hover {
        background: var(--warning-color);
    }

    .message-action-btn.delete:hover {
        background: var(--danger-color);
    }

    /* Input Area */
    .input-area {
        background: var(--white);
        border-top: 1px solid var(--gray-200);
        padding: 1rem 1.5rem;
        box-shadow: var(--shadow-sm);
        position: relative;
    }

    .input-container {
        max-width: 800px;
        margin: 0 auto;
        display: flex;
        align-items: flex-end;
        gap: 1rem;
        position: relative;
    }

    .message-input {
        flex: 1;
        border: 2px solid var(--gray-200);
        border-radius: 25px;
        padding: 0.875rem 1.25rem;
        font-size: 0.875rem;
        resize: none;
        min-height: 50px;
        max-height: 120px;
        transition: var(--transition);
        font-family: inherit;
    }

    .message-input:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
    }

    .input-actions {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn-send {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        border: none;
        background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
        color: var(--white);
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: var(--transition);
        box-shadow: var(--shadow-md);
        position: relative;
        overflow: hidden;
    }

    .btn-send::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: var(--transition);
    }

    .btn-send:hover::before {
        left: 100%;
    }

    .btn-send:hover {
        transform: scale(1.1);
        box-shadow: var(--shadow-lg);
    }

    .btn-send:disabled {
        background: var(--gray-300);
        cursor: not-allowed;
        transform: none;
    }

    .btn-attachment {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        border: 2px solid var(--gray-300);
        background: var(--white);
        color: var(--gray-600);
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: var(--transition);
    }

    .btn-attachment:hover {
        border-color: var(--primary-color);
        color: var(--primary-color);
        transform: scale(1.1);
    }

    /* Typing Indicator */
    .typing-indicator {
        display: none;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem 1rem;
        color: var(--gray-600);
        font-size: 0.875rem;
        font-style: italic;
    }

    .typing-dots {
        display: flex;
        gap: 0.25rem;
    }

    .typing-dot {
        width: 6px;
        height: 6px;
        border-radius: 50%;
        background: var(--gray-400);
        animation: typing 1.4s infinite ease-in-out;
    }

    .typing-dot:nth-child(1) { animation-delay: -0.32s; }
    .typing-dot:nth-child(2) { animation-delay: -0.16s; }

    @keyframes typing {
        0%, 80%, 100% {
            transform: scale(0.8);
            opacity: 0.5;
        }
        40% {
            transform: scale(1);
            opacity: 1;
        }
    }

    /* Loading States */
    .loading-messages {
        text-align: center;
        padding: 2rem;
        color: var(--gray-600);
    }

    .loading-spinner {
        display: inline-block;
        width: 40px;
        height: 40px;
        border: 4px solid var(--gray-200);
        border-radius: 50%;
        border-top-color: var(--primary-color);
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        to { transform: rotate(360deg); }
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .chat-header {
            padding: 1rem;
        }
        
        .chat-avatar {
            width: 40px;
            height: 40px;
            font-size: 1rem;
        }
        
        .chat-details h4 {
            font-size: 1.125rem;
        }
        
        .messages-area {
            padding: 0.5rem;
        }
        
        .message-content {
            max-width: 85%;
        }
        
        .input-area {
            padding: 1rem;
        }
        
        .input-container {
            gap: 0.5rem;
        }
        
        .btn-send {
            width: 45px;
            height: 45px;
        }
        
        .btn-attachment {
            width: 35px;
            height: 35px;
        }
    }

    /* Empty State */
    .empty-messages {
        text-align: center;
        padding: 4rem 2rem;
        color: var(--gray-500);
    }

    .empty-messages i {
        font-size: 4rem;
        margin-bottom: 1rem;
        color: var(--gray-300);
    }

    .empty-messages h3 {
        margin-bottom: 0.5rem;
        color: var(--gray-700);
    }

    /* Message Reactions */
    .message-reactions {
        display: flex;
        gap: 0.25rem;
        margin-top: 0.5rem;
        flex-wrap: wrap;
    }

    .reaction {
        background: var(--gray-100);
        border: 1px solid var(--gray-200);
        border-radius: 15px;
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
        cursor: pointer;
        transition: var(--transition);
        display: flex;
        align-items: center;
        gap: 0.25rem;
    }

    .reaction:hover {
        background: var(--primary-color);
        color: var(--white);
        border-color: var(--primary-color);
    }

    .reaction.active {
        background: var(--primary-color);
        color: var(--white);
        border-color: var(--primary-color);
    }
</style>
{% endblock %}

{% block content %}
<div class="chat-container">
    <!-- Chat Header -->
    <div class="chat-header">
        <div class="chat-info">
            <div class="chat-avatar">
                {{ conversation.title|first|upper|default:"C" }}
            </div>
            <div class="chat-details">
                <h4>{{ conversation.title|default:conversation.get_participants_summary }}</h4>
                <div class="chat-meta">
                    <span class="chat-type">{{ conversation.chat_type }}</span>
                    {% if conversation.is_encrypted %}
                    <span class="encryption-status">
                        <i class="fas fa-lock"></i>
                        Encrypted
                    </span>
                    {% endif %}
                    <span class="participant-count">{{ conversation.participants.count }} participants</span>
                </div>
            </div>
        </div>
        
        <div class="chat-actions">
            <button class="btn-icon" title="Search in conversation">
                <i class="fas fa-search"></i>
            </button>
            <button class="btn-icon" title="Conversation info">
                <i class="fas fa-info-circle"></i>
            </button>
            <button class="btn-icon" title="More options">
                <i class="fas fa-ellipsis-v"></i>
            </button>
        </div>
    </div>
    
    <!-- Messages Area -->
    <div class="messages-area" id="messagesArea">
        <div class="messages-container" id="messagesContainer">
            {% if messages %}
                {% for message in messages %}
                <div class="message-group">
                    {% if forloop.first %}
                    <div class="message-date">
                        <span>{{ message.created_at|date:"F j, Y" }}</span>
                    </div>
                    {% endif %}
                    
                    <div class="message {% if message.sender == user %}sent{% else %}received{% endif %}" data-message-id="{{ message.id }}">
                        {% if message.sender != user %}
                        <div class="message-avatar">
                            {{ message.sender.get_full_name|first|upper|default:message.sender.email|first|upper }}
                        </div>
                        {% endif %}
                        
                        <div class="message-content">
                            <div class="message-bubble">
                                {{ message.decrypted_content }}
                                
                                {% if message.is_encrypted %}
                                <div style="margin-top: 0.5rem; font-size: 0.75rem; opacity: 0.7;">
                                    <i class="fas fa-lock"></i> Encrypted message
                                </div>
                                {% endif %}
                            </div>
                            
                            <div class="message-meta">
                                <span class="message-time">{{ message.created_at|time:"g:i A" }}</span>
                                {% if message.sender == user %}
                                <div class="message-status">
                                    {% if message.is_read %}
                                    <i class="fas fa-check-double" style="color: var(--primary-color);"></i>
                                    {% else %}
                                    <i class="fas fa-check"></i>
                                    {% endif %}
                                </div>
                                {% endif %}
                            </div>
                            
                            {% if message.sender == user %}
                            <div class="message-actions">
                                <button class="message-action-btn edit" title="Edit message" onclick="editMessage({{ message.id }})">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="message-action-btn delete" title="Delete message" onclick="deleteMessage({{ message.id }})">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                {% endfor %}
            {% else %}
            <div class="empty-messages">
                <i class="fas fa-comments"></i>
                <h3>No messages yet</h3>
                <p>Start the conversation by sending your first message!</p>
            </div>
            {% endif %}
        </div>
    </div>
    
    <!-- Typing Indicator -->
    <div class="typing-indicator" id="typingIndicator">
        <span>Someone is typing</span>
        <div class="typing-dots">
            <div class="typing-dot"></div>
            <div class="typing-dot"></div>
            <div class="typing-dot"></div>
        </div>
    </div>
    
    <!-- Input Area -->
    <div class="input-area">
        <div class="input-container">
            <textarea 
                class="message-input" 
                id="messageInput" 
                placeholder="Type your message..."
                rows="1"
            ></textarea>
            
            <div class="input-actions">
                <button class="btn-attachment" title="Attach file">
                    <i class="fas fa-paperclip"></i>
                </button>
                <button class="btn-send" id="sendButton" title="Send message">
                    <i class="fas fa-paper-plane"></i>
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const messageInput = document.getElementById('messageInput');
    const sendButton = document.getElementById('sendButton');
    const messagesContainer = document.getElementById('messagesContainer');
    const messagesArea = document.getElementById('messagesArea');
    const typingIndicator = document.getElementById('typingIndicator');
    
    let isTyping = false;
    let typingTimeout;
    
    // Auto-resize textarea
    messageInput.addEventListener('input', function() {
        this.style.height = 'auto';
        this.style.height = Math.min(this.scrollHeight, 120) + 'px';
        
        // Update send button state
        sendButton.disabled = !this.value.trim();
        
        // Show typing indicator
        if (!isTyping && this.value.trim()) {
            isTyping = true;
            // TODO: Send typing indicator to server
        }
        
        // Clear typing timeout
        clearTimeout(typingTimeout);
        typingTimeout = setTimeout(() => {
            isTyping = false;
            // TODO: Hide typing indicator
        }, 1000);
    });
    
    // Send message on Enter (Shift+Enter for new line)
    messageInput.addEventListener('keydown', function(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            sendMessage();
        }
    });
    
    // Send message on button click
    sendButton.addEventListener('click', sendMessage);
    
    function sendMessage() {
        const content = messageInput.value.trim();
        if (!content) return;
        
        // Disable input and show loading
        messageInput.disabled = true;
        sendButton.disabled = true;
        sendButton.innerHTML = '<div class="loading"></div>';
        
        // Create temporary message
        const tempMessage = createMessageElement(content, true);
        messagesContainer.appendChild(tempMessage);
        scrollToBottom();
        
        // Send to server
        fetch('{% url "messaging:send_message" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            },
            body: JSON.stringify({
                conversation_id: {{ conversation.id }},
                content: content
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                // Update temporary message with real data
                tempMessage.dataset.messageId = data.message.id;
                tempMessage.querySelector('.message-bubble').textContent = data.message.content;
                
                // Add encryption indicator if needed
                if (data.message.is_encrypted) {
                    const encryptionDiv = document.createElement('div');
                    encryptionDiv.style.cssText = 'margin-top: 0.5rem; font-size: 0.75rem; opacity: 0.7;';
                    encryptionDiv.innerHTML = '<i class="fas fa-lock"></i> Encrypted message';
                    tempMessage.querySelector('.message-bubble').appendChild(encryptionDiv);
                }
                
                // Update message status to sent
                const statusElement = tempMessage.querySelector('.message-status');
                if (statusElement) {
                    statusElement.innerHTML = '<i class="fas fa-check"></i>';
                }
            } else {
                // Remove temporary message and show error
                tempMessage.remove();
                showError(data.message || 'Failed to send message');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            tempMessage.remove();
            showError('Failed to send message');
        })
        .finally(() => {
            // Reset input
            messageInput.value = '';
            messageInput.style.height = 'auto';
            messageInput.disabled = false;
            sendButton.disabled = false;
            sendButton.innerHTML = '<i class="fas fa-paper-plane"></i>';
        });
    }
    
    function createMessageElement(content, isSent = false) {
        const messageGroup = document.createElement('div');
        messageGroup.className = 'message-group';
        
        const message = document.createElement('div');
        message.className = `message ${isSent ? 'sent' : 'received'}`;
        
        if (!isSent) {
            const avatar = document.createElement('div');
            avatar.className = 'message-avatar';
            avatar.textContent = 'U';
            message.appendChild(avatar);
        }
        
        const messageContent = document.createElement('div');
        messageContent.className = 'message-content';
        
        const bubble = document.createElement('div');
        bubble.className = 'message-bubble';
        bubble.textContent = content; // Keep as textContent for security
        
        const meta = document.createElement('div');
        meta.className = 'message-meta';
        meta.innerHTML = `
            <span class="message-time">${new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}</span>
            ${isSent ? '<div class="message-status"><i class="fas fa-clock"></i></div>' : ''}
        `;
        
        messageContent.appendChild(bubble);
        messageContent.appendChild(meta);
        message.appendChild(messageContent);
        messageGroup.appendChild(message);
        
        return messageGroup;
    }
    
    function scrollToBottom() {
        messagesArea.scrollTop = messagesArea.scrollHeight;
    }
    
    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }
    
    function showError(message) {
        // Create error notification
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--danger-color);
            color: white;
            padding: 1rem 1.5rem;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-lg);
            z-index: 1000;
            animation: slideInRight 0.3s ease-out;
        `;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 5000);
    }
    
    // Edit message function
    window.editMessage = function(messageId) {
        const messageElement = document.querySelector(`[data-message-id="${messageId}"]`);
        const bubble = messageElement.querySelector('.message-bubble');
        const content = bubble.textContent.trim();
        
        // Create edit input
                 const editInput = document.createElement('textarea');
         editInput.value = content;
         editInput.style.cssText = 'width: 100%; min-height: 60px; border: 2px solid var(--primary-color); border-radius: 10px; padding: 0.5rem; font-family: inherit; resize: vertical;';
        
                 const editActions = document.createElement('div');
         editActions.style.cssText = 'display: flex; gap: 0.5rem; margin-top: 0.5rem;';
        
        const saveBtn = document.createElement('button');
        saveBtn.textContent = 'Save';
        saveBtn.className = 'btn btn-primary';
                 saveBtn.style.cssText = 'padding: 0.25rem 0.75rem; font-size: 0.75rem;';
         
         const cancelBtn = document.createElement('button');
         cancelBtn.textContent = 'Cancel';
         cancelBtn.className = 'btn btn-secondary';
         cancelBtn.style.cssText = 'padding: 0.25rem 0.75rem; font-size: 0.75rem;';
        
        editActions.appendChild(saveBtn);
        editActions.appendChild(cancelBtn);
        
        // Replace content with edit form
        const originalContent = bubble.innerHTML;
        bubble.innerHTML = '';
        bubble.appendChild(editInput);
        bubble.appendChild(editActions);
        
        editInput.focus();
        editInput.select();
        
        // Handle save
        saveBtn.addEventListener('click', function() {
            const newContent = editInput.value.trim();
            if (newContent && newContent !== content) {
                // TODO: Send edit request to server
                bubble.innerHTML = newContent;
            } else {
                bubble.innerHTML = originalContent;
            }
        });
        
        // Handle cancel
        cancelBtn.addEventListener('click', function() {
            bubble.innerHTML = originalContent;
        });
    };
    
    // Delete message function
    window.deleteMessage = function(messageId) {
        if (confirm('Are you sure you want to delete this message?')) {
            const messageElement = document.querySelector(`[data-message-id="${messageId}"]`);
            
            // TODO: Send delete request to server
            messageElement.style.animation = 'slideOutRight 0.3s ease-out';
            setTimeout(() => {
                messageElement.remove();
            }, 300);
        }
    };
    
    // Scroll to bottom on load
    scrollToBottom();
    
    // Auto-refresh messages every 10 seconds
    setInterval(function() {
        // TODO: Implement AJAX refresh for real-time updates
        console.log('Refreshing messages...');
    }, 10000);
    
    // Add CSS animations
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideInRight {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        
        @keyframes slideOutRight {
            from { transform: translateX(0); opacity: 1; }
            to { transform: translateX(100%); opacity: 0; }
        }
    `;
    document.head.appendChild(style);
});
</script>
{% endblock %} 