{% extends 'base.html' %}
{% load static %}

{% block title %}Create New Conversation - Cloverics{% endblock %}

{% block extra_css %}
<style>
    .create-conversation-container {
        max-width: 600px;
        margin: 2rem auto;
        background: white;
        border-radius: 12px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }
    
    .create-header {
        background: linear-gradient(135deg, #2196f3, #1976d2);
        color: white;
        padding: 2rem;
        text-align: center;
    }
    
    .create-header h2 {
        margin: 0;
        font-size: 1.5rem;
        font-weight: 600;
    }
    
    .create-header p {
        margin: 0.5rem 0 0 0;
        opacity: 0.9;
        font-size: 0.875rem;
    }
    
    .create-form {
        padding: 2rem;
    }
    
    .form-group {
        margin-bottom: 1.5rem;
    }
    
    .form-label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 600;
        color: #333;
    }
    
    .form-input {
        width: 100%;
        padding: 0.75rem;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        font-size: 0.875rem;
        transition: border-color 0.2s, box-shadow 0.2s;
    }
    
    .form-input:focus {
        outline: none;
        border-color: #2196f3;
        box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.2);
    }
    
    .form-select {
        width: 100%;
        padding: 0.75rem;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        font-size: 0.875rem;
        background: white;
        cursor: pointer;
    }
    
    .form-select:focus {
        outline: none;
        border-color: #2196f3;
        box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.2);
    }
    
    .participants-section {
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 1rem;
        background: #f8f9fa;
    }
    
    .participants-list {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
        margin-top: 1rem;
    }
    
    .participant-tag {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem 0.75rem;
        background: #2196f3;
        color: white;
        border-radius: 20px;
        font-size: 0.875rem;
        font-weight: 500;
    }
    
    .remove-participant {
        background: none;
        border: none;
        color: white;
        cursor: pointer;
        padding: 0;
        font-size: 1rem;
        line-height: 1;
    }
    
    .remove-participant:hover {
        opacity: 0.8;
    }
    
    .user-search {
        position: relative;
    }
    
    .search-results {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        max-height: 200px;
        overflow-y: auto;
        z-index: 1000;
        display: none;
    }
    
    .search-result-item {
        padding: 0.75rem;
        cursor: pointer;
        border-bottom: 1px solid #f1f3f4;
        transition: background-color 0.2s;
    }
    
    .search-result-item:hover {
        background: #f8f9fa;
    }
    
    .search-result-item:last-child {
        border-bottom: none;
    }
    
    .search-result-item:only-child {
        text-align: center;
        color: #666;
        font-style: italic;
    }
    
    .user-info {
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }
    
    .user-avatar {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background: #2196f3;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        font-size: 0.875rem;
    }
    
    .user-details {
        flex: 1;
    }
    
    .user-name {
        font-weight: 600;
        color: #333;
        margin-bottom: 0.25rem;
    }
    
    .user-email {
        font-size: 0.75rem;
        color: #666;
    }
    
    .user-company {
        font-size: 0.75rem;
        color: #666;
        font-style: italic;
    }
    
    .user-type {
        font-size: 0.75rem;
        color: #2196f3;
        font-weight: 500;
    }
    
    .encryption-section {
        background: #e8f5e8;
        border: 1px solid #4caf50;
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 1.5rem;
    }
    
    .encryption-header {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 0.5rem;
    }
    
    .encryption-header i {
        color: #4caf50;
        font-size: 1.25rem;
    }
    
    .encryption-title {
        font-weight: 600;
        color: #2e7d32;
    }
    
    .encryption-description {
        font-size: 0.875rem;
        color: #2e7d32;
        margin-bottom: 1rem;
    }
    
    .checkbox-group {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .checkbox-input {
        width: 18px;
        height: 18px;
        accent-color: #4caf50;
    }
    
    .checkbox-label {
        font-size: 0.875rem;
        color: #2e7d32;
        cursor: pointer;
    }
    
    .form-actions {
        display: flex;
        gap: 1rem;
        justify-content: flex-end;
        margin-top: 2rem;
        padding-top: 1rem;
        border-top: 1px solid #e9ecef;
    }
    
    .btn {
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.2s;
        border: none;
        cursor: pointer;
        font-size: 0.875rem;
    }
    
    .btn-primary {
        background: #2196f3;
        color: white;
    }
    
    .btn-primary:hover {
        background: #1976d2;
        color: white;
        text-decoration: none;
    }
    
    .btn-secondary {
        background: #f8f9fa;
        color: #333;
        border: 1px solid #dee2e6;
    }
    
    .btn-secondary:hover {
        background: #e9ecef;
        color: #333;
        text-decoration: none;
    }
    
    .error-message {
        color: #dc3545;
        font-size: 0.875rem;
        margin-top: 0.5rem;
    }
    
    .success-message {
        color: #28a745;
        font-size: 0.875rem;
        margin-top: 0.5rem;
    }
    
    @media (max-width: 768px) {
        .create-conversation-container {
            margin: 1rem;
            border-radius: 8px;
        }
        
        .create-header {
            padding: 1.5rem;
        }
        
        .create-form {
            padding: 1.5rem;
        }
        
        .form-actions {
            flex-direction: column;
        }
        
        .btn {
            width: 100%;
            justify-content: center;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="create-conversation-container">
    <div class="create-header">
        <h2><i class="fas fa-plus-circle"></i> Create New Conversation</h2>
        <p>Start a secure, encrypted conversation with your team members</p>
    </div>
    
    <form class="create-form" id="createConversationForm">
        {% csrf_token %}
        
        <!-- Conversation Title -->
        <div class="form-group">
            <label for="conversationTitle" class="form-label">
                <i class="fas fa-heading"></i> Conversation Title
            </label>
            <input 
                type="text" 
                id="conversationTitle" 
                name="title" 
                class="form-input" 
                placeholder="Enter conversation title (optional)"
                maxlength="255"
            >
        </div>
        
        <!-- Chat Type -->
        <div class="form-group">
            <label for="chatType" class="form-label">
                <i class="fas fa-comments"></i> Chat Type
            </label>
            <select id="chatType" name="chat_type" class="form-select">
                <option value="general">General</option>
                <option value="support">Support Team</option>
                <option value="logistics">Logistics Partner</option>
                <option value="driver">Driver</option>
                <option value="customs">Customs Officer</option>
                <option value="group">Group Chat</option>
            </select>
        </div>
        
        <!-- Participants -->
        <div class="form-group">
            <label class="form-label">
                <i class="fas fa-users"></i> Participants
            </label>
            <div class="participants-section">
                <div class="user-search">
                    <input 
                        type="text" 
                        id="userSearch" 
                        class="form-input" 
                        placeholder="Search users by name or email..."
                        autocomplete="off"
                    >
                    <div class="search-results" id="searchResults"></div>
                </div>
                
                <div class="participants-list" id="participantsList">
                    <!-- Selected participants will appear here -->
                </div>
            </div>
        </div>
        
        <!-- Encryption Settings -->
        <div class="encryption-section">
            <div class="encryption-header">
                <i class="fas fa-lock"></i>
                <span class="encryption-title">Security & Encryption</span>
            </div>
            <div class="encryption-description">
                All conversations are encrypted by default to ensure your messages remain private and secure.
            </div>
            <div class="checkbox-group">
                <input 
                    type="checkbox" 
                    id="enableEncryption" 
                    name="is_encrypted" 
                    class="checkbox-input" 
                    checked
                >
                <label for="enableEncryption" class="checkbox-label">
                    Enable end-to-end encryption for this conversation
                </label>
            </div>
        </div>
        
        <!-- Error/Success Messages -->
        <div id="messageContainer"></div>
        
        <!-- Form Actions -->
        <div class="form-actions">
            <a href="{% url 'messaging:chat_dashboard' %}" class="btn btn-secondary">
                <i class="fas fa-times"></i>
                Cancel
            </a>
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-plus"></i>
                Create Conversation
            </button>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
let selectedParticipants = [];
let searchTimeout;

document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('createConversationForm');
    const userSearch = document.getElementById('userSearch');
    const searchResults = document.getElementById('searchResults');
    const participantsList = document.getElementById('participantsList');
    const messageContainer = document.getElementById('messageContainer');
    
    // User search functionality
    userSearch.addEventListener('input', function() {
        const query = this.value.trim();
        
        clearTimeout(searchTimeout);
        
        if (query.length < 2) {
            searchResults.style.display = 'none';
            return;
        }
        
        searchTimeout = setTimeout(() => {
            searchUsers(query);
        }, 300);
    });
    
    // Hide search results when clicking outside
    document.addEventListener('click', function(e) {
        if (!userSearch.contains(e.target) && !searchResults.contains(e.target)) {
            searchResults.style.display = 'none';
        }
    });
    
    // Form submission
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        createConversation();
    });
});

function searchUsers(query) {
    const searchResults = document.getElementById('searchResults');
    
    // Show loading state
    searchResults.innerHTML = '<div class="search-result-item">Searching...</div>';
    searchResults.style.display = 'block';
    
    // Make API call to search users
    fetch(`{% url 'messaging:search_users' %}?q=${encodeURIComponent(query)}`, {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRFToken': getCookie('csrftoken')
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        if (data.status === 'success') {
            displaySearchResults(data.users);
        } else {
            console.error('Search error:', data.message);
            displaySearchResults([]);
        }
    })
    .catch(error => {
        console.error('Search error:', error);
        searchResults.innerHTML = '<div class="search-result-item">Error searching users. Please try again.</div>';
        searchResults.style.display = 'block';
    });
}

function displaySearchResults(users) {
    const searchResults = document.getElementById('searchResults');
    
    if (users.length === 0) {
        searchResults.innerHTML = '<div class="search-result-item">No users found</div>';
    } else {
        searchResults.innerHTML = users.map(user => `
            <div class="search-result-item" onclick="addParticipant(${user.id}, '${user.name}', '${user.email}', '${user.user_type}')">
                <div class="user-info">
                    <div class="user-avatar">
                        ${user.avatar ? `<img src="${user.avatar}" alt="${user.name}" style="width: 100%; height: 100%; border-radius: 50%; object-fit: cover;">` : user.name.charAt(0).toUpperCase()}
                    </div>
                    <div class="user-details">
                        <div class="user-name">${user.name}</div>
                        <div class="user-email">${user.email}</div>
                        ${user.company_name ? `<div class="user-company" style="font-size: 0.75rem; color: #666;">${user.company_name}</div>` : ''}
                    </div>
                    <div class="user-type">${user.user_type}</div>
                </div>
            </div>
        `).join('');
    }
    
    searchResults.style.display = 'block';
}

function addParticipant(userId, name, email, userType) {
    // Check if user is already selected
    if (selectedParticipants.some(p => p.id === userId)) {
        showMessage('User is already added to the conversation', 'error');
        return;
    }
    
    // Validate input
    if (!userId || !name || !email) {
        showMessage('Invalid user data', 'error');
        return;
    }
    
    selectedParticipants.push({ 
        id: userId, 
        name: name, 
        email: email, 
        userType: userType 
    });
    updateParticipantsList();
    
    // Clear search
    document.getElementById('userSearch').value = '';
    document.getElementById('searchResults').style.display = 'none';
    
    showMessage(`Added ${name} to conversation`, 'success');
}

function removeParticipant(userId) {
    selectedParticipants = selectedParticipants.filter(p => p.id !== userId);
    updateParticipantsList();
}

function updateParticipantsList() {
    const participantsList = document.getElementById('participantsList');
    
    if (selectedParticipants.length === 0) {
        participantsList.innerHTML = '<div class="text-muted">No participants selected</div>';
        return;
    }
    
    participantsList.innerHTML = selectedParticipants.map(participant => `
        <div class="participant-tag">
            <span>${participant.name}</span>
            <button type="button" class="remove-participant" onclick="removeParticipant(${participant.id})">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `).join('');
}

function createConversation() {
    const form = document.getElementById('createConversationForm');
    const formData = new FormData(form);
    
    const data = {
        title: formData.get('title'),
        chat_type: formData.get('chat_type'),
        is_encrypted: formData.get('is_encrypted') === 'on',
        participant_ids: selectedParticipants.map(p => p.id)
    };
    

    
    fetch('{% url "messaging:create_conversation" %}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCookie('csrftoken')
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            showMessage('Conversation created successfully!', 'success');
            setTimeout(() => {
                window.location.href = `/messaging/conversation/${data.conversation_id}/`;
            }, 1000);
        } else {
            showMessage(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showMessage('Failed to create conversation', 'error');
    });
}

function showMessage(message, type) {
    const messageContainer = document.getElementById('messageContainer');
    messageContainer.innerHTML = `<div class="${type}-message">${message}</div>`;
    
    setTimeout(() => {
        messageContainer.innerHTML = '';
    }, 5000);
}

function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}
</script>
{% endblock %} 