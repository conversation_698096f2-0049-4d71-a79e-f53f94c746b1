{% extends 'base.html' %}
{% load static %}

{% block title %}Security Audit Log - Cloverics{% endblock %}

{% block extra_css %}
<style>
    .security-audit-container {
        max-width: 1200px;
        margin: 2rem auto;
        background: white;
        border-radius: 12px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }
    
    .audit-header {
        background: linear-gradient(135deg, #dc3545, #c82333);
        color: white;
        padding: 2rem;
        text-align: center;
    }
    
    .audit-header h2 {
        margin: 0;
        font-size: 1.5rem;
        font-weight: 600;
    }
    
    .audit-header p {
        margin: 0.5rem 0 0 0;
        opacity: 0.9;
        font-size: 0.875rem;
    }
    
    .audit-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        padding: 1.5rem;
        background: #f8f9fa;
        border-bottom: 1px solid #e9ecef;
    }
    
    .stat-card {
        background: white;
        padding: 1rem;
        border-radius: 8px;
        text-align: center;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    
    .stat-number {
        font-size: 1.5rem;
        font-weight: 700;
        color: #dc3545;
        margin-bottom: 0.5rem;
    }
    
    .stat-label {
        color: #666;
        font-size: 0.875rem;
    }
    
    .audit-filters {
        padding: 1.5rem;
        background: #f8f9fa;
        border-bottom: 1px solid #e9ecef;
    }
    
    .filters-row {
        display: flex;
        gap: 1rem;
        align-items: center;
        flex-wrap: wrap;
    }
    
    .filter-group {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .filter-label {
        font-weight: 600;
        color: #333;
        font-size: 0.875rem;
    }
    
    .filter-input {
        padding: 0.5rem;
        border: 1px solid #dee2e6;
        border-radius: 6px;
        font-size: 0.875rem;
    }
    
    .filter-select {
        padding: 0.5rem;
        border: 1px solid #dee2e6;
        border-radius: 6px;
        font-size: 0.875rem;
        background: white;
        cursor: pointer;
    }
    
    .filter-button {
        background: #dc3545;
        color: white;
        border: none;
        padding: 0.5rem 1rem;
        border-radius: 6px;
        cursor: pointer;
        font-size: 0.875rem;
        transition: background-color 0.2s;
    }
    
    .filter-button:hover {
        background: #c82333;
    }
    
    .audit-events {
        max-height: 600px;
        overflow-y: auto;
    }
    
    .event-item {
        padding: 1rem;
        border-bottom: 1px solid #f1f3f4;
        transition: background-color 0.2s;
    }
    
    .event-item:hover {
        background: #f8f9fa;
    }
    
    .event-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 0.5rem;
    }
    
    .event-type {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-weight: 600;
        color: #333;
    }
    
    .event-type-icon {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.75rem;
        color: white;
    }
    
    .event-type.key-created .event-type-icon { background: #28a745; }
    .event-type.key-rotated .event-type-icon { background: #ffc107; }
    .event-type.key-compromised .event-type-icon { background: #dc3545; }
    .event-type.message-encrypted .event-type-icon { background: #17a2b8; }
    .event-type.message-decrypted .event-type-icon { background: #6f42c1; }
    .event-type.access-denied .event-type-icon { background: #dc3545; }
    .event-type.conversation-created .event-type-icon { background: #28a745; }
    .event-type.participant-added .event-type-icon { background: #17a2b8; }
    .event-type.participant-removed .event-type-icon { background: #6c757d; }
    
    .event-time {
        font-size: 0.75rem;
        color: #666;
    }
    
    .event-details {
        display: flex;
        gap: 1rem;
        margin-bottom: 0.5rem;
    }
    
    .event-user {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.875rem;
        color: #666;
    }
    
    .user-avatar {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background: #2196f3;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.625rem;
        font-weight: 600;
    }
    
    .event-context {
        font-size: 0.875rem;
        color: #666;
    }
    
    .event-description {
        font-size: 0.875rem;
        color: #333;
        line-height: 1.4;
    }
    
    .event-meta {
        display: flex;
        gap: 1rem;
        margin-top: 0.5rem;
        font-size: 0.75rem;
        color: #999;
    }
    
    .meta-item {
        display: flex;
        align-items: center;
        gap: 0.25rem;
    }
    
    .pagination {
        display: flex;
        justify-content: center;
        gap: 0.5rem;
        padding: 1.5rem;
        border-top: 1px solid #e9ecef;
    }
    
    .page-link {
        padding: 0.5rem 1rem;
        border: 1px solid #dee2e6;
        border-radius: 6px;
        text-decoration: none;
        color: #333;
        transition: all 0.2s;
    }
    
    .page-link:hover {
        background: #f8f9fa;
        text-decoration: none;
    }
    
    .page-link.current {
        background: #dc3545;
        color: white;
        border-color: #dc3545;
    }
    
    .no-events {
        text-align: center;
        padding: 3rem;
        color: #666;
    }
    
    .no-events i {
        font-size: 3rem;
        margin-bottom: 1rem;
        color: #dee2e6;
    }
    
    .export-section {
        padding: 1rem 1.5rem;
        background: #f8f9fa;
        border-top: 1px solid #e9ecef;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .export-button {
        background: #28a745;
        color: white;
        border: none;
        padding: 0.5rem 1rem;
        border-radius: 6px;
        cursor: pointer;
        font-size: 0.875rem;
        transition: background-color 0.2s;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .export-button:hover {
        background: #218838;
        color: white;
        text-decoration: none;
    }
    
    @media (max-width: 768px) {
        .security-audit-container {
            margin: 1rem;
            border-radius: 8px;
        }
        
        .audit-header {
            padding: 1.5rem;
        }
        
        .filters-row {
            flex-direction: column;
            align-items: stretch;
        }
        
        .filter-group {
            width: 100%;
        }
        
        .event-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 0.5rem;
        }
        
        .event-details {
            flex-direction: column;
            gap: 0.5rem;
        }
        
        .export-section {
            flex-direction: column;
            gap: 1rem;
            align-items: stretch;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="security-audit-container">
    <div class="audit-header">
        <h2><i class="fas fa-shield-alt"></i> Security Audit Log</h2>
        <p>Monitor encryption events and security activities across your conversations</p>
    </div>
    
    <!-- Statistics -->
    <div class="audit-stats">
        <div class="stat-card">
            <div class="stat-number">{{ total_events }}</div>
            <div class="stat-label">Total Events</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ events|length }}</div>
            <div class="stat-label">Events This Page</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">100%</div>
            <div class="stat-label">Encryption Rate</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">0</div>
            <div class="stat-label">Security Incidents</div>
        </div>
    </div>
    
    <!-- Filters -->
    <div class="audit-filters">
        <form method="get" class="filters-row">
            <div class="filter-group">
                <label class="filter-label">Event Type</label>
                <select name="event_type" class="filter-select">
                    <option value="">All Events</option>
                    <option value="key_created">Key Created</option>
                    <option value="key_rotated">Key Rotated</option>
                    <option value="key_compromised">Key Compromised</option>
                    <option value="message_encrypted">Message Encrypted</option>
                    <option value="message_decrypted">Message Decrypted</option>
                    <option value="access_denied">Access Denied</option>
                    <option value="conversation_created">Conversation Created</option>
                    <option value="participant_added">Participant Added</option>
                    <option value="participant_removed">Participant Removed</option>
                </select>
            </div>
            
            <div class="filter-group">
                <label class="filter-label">Date From</label>
                <input type="date" name="date_from" class="filter-input">
            </div>
            
            <div class="filter-group">
                <label class="filter-label">Date To</label>
                <input type="date" name="date_to" class="filter-input">
            </div>
            
            <div class="filter-group">
                <label class="filter-label">&nbsp;</label>
                <button type="submit" class="filter-button">
                    <i class="fas fa-filter"></i> Filter
                </button>
            </div>
        </form>
    </div>
    
    <!-- Events List -->
    <div class="audit-events">
        {% if events %}
            {% for event in events %}
            <div class="event-item">
                <div class="event-header">
                    <div class="event-type {{ event.event_type }}">
                        <div class="event-type-icon">
                            {% if event.event_type == 'key_created' %}
                                <i class="fas fa-key"></i>
                            {% elif event.event_type == 'key_rotated' %}
                                <i class="fas fa-sync"></i>
                            {% elif event.event_type == 'key_compromised' %}
                                <i class="fas fa-exclamation-triangle"></i>
                            {% elif event.event_type == 'message_encrypted' %}
                                <i class="fas fa-lock"></i>
                            {% elif event.event_type == 'message_decrypted' %}
                                <i class="fas fa-unlock"></i>
                            {% elif event.event_type == 'access_denied' %}
                                <i class="fas fa-ban"></i>
                            {% elif event.event_type == 'conversation_created' %}
                                <i class="fas fa-plus"></i>
                            {% elif event.event_type == 'participant_added' %}
                                <i class="fas fa-user-plus"></i>
                            {% elif event.event_type == 'participant_removed' %}
                                <i class="fas fa-user-minus"></i>
                            {% else %}
                                <i class="fas fa-info"></i>
                            {% endif %}
                        </div>
                        <span>{{ event.get_event_type_display }}</span>
                    </div>
                    <div class="event-time">
                        {{ event.created_at|date:"M j, Y g:i A" }}
                    </div>
                </div>
                
                <div class="event-details">
                    {% if event.user %}
                    <div class="event-user">
                        <div class="user-avatar">
                            {{ event.user.get_full_name|first|upper|default:event.user.email|first|upper }}
                        </div>
                        <span>{{ event.user.get_full_name|default:event.user.email }}</span>
                    </div>
                    {% endif %}
                    
                    {% if event.conversation %}
                    <div class="event-context">
                        <i class="fas fa-comments"></i>
                        {{ event.conversation.title|default:"Conversation" }}
                    </div>
                    {% endif %}
                </div>
                
                <div class="event-description">
                    {% if event.event_type == 'key_created' %}
                        Encryption key created for secure communication
                    {% elif event.event_type == 'key_rotated' %}
                        Encryption key rotated for enhanced security
                    {% elif event.event_type == 'key_compromised' %}
                        Encryption key marked as compromised
                    {% elif event.event_type == 'message_encrypted' %}
                        Message encrypted using secure key
                    {% elif event.event_type == 'message_decrypted' %}
                        Message decrypted for authorized user
                    {% elif event.event_type == 'access_denied' %}
                        Access denied to encrypted content
                    {% elif event.event_type == 'conversation_created' %}
                        New encrypted conversation created
                    {% elif event.event_type == 'participant_added' %}
                        New participant added to encrypted conversation
                    {% elif event.event_type == 'participant_removed' %}
                        Participant removed from encrypted conversation
                    {% else %}
                        Security event logged
                    {% endif %}
                </div>
                
                <div class="event-meta">
                    {% if event.ip_address %}
                    <div class="meta-item">
                        <i class="fas fa-globe"></i>
                        <span>{{ event.ip_address }}</span>
                    </div>
                    {% endif %}
                    
                    {% if event.user_agent %}
                    <div class="meta-item">
                        <i class="fas fa-desktop"></i>
                        <span>{{ event.user_agent|truncatechars:50 }}</span>
                    </div>
                    {% endif %}
                    
                    {% if event.details %}
                    <div class="meta-item">
                        <i class="fas fa-info-circle"></i>
                        <span>Details available</span>
                    </div>
                    {% endif %}
                </div>
            </div>
            {% endfor %}
        {% else %}
            <div class="no-events">
                <i class="fas fa-shield-alt"></i>
                <h3>No Security Events</h3>
                <p>No security events have been logged yet. This is normal for new conversations.</p>
            </div>
        {% endif %}
    </div>
    
    <!-- Pagination -->
    {% if events.has_other_pages %}
    <div class="pagination">
        {% if events.has_previous %}
        <a href="?page={{ events.previous_page_number }}" class="page-link">
            <i class="fas fa-chevron-left"></i> Previous
        </a>
        {% endif %}
        
        {% for num in events.paginator.page_range %}
        {% if events.number == num %}
        <span class="page-link current">{{ num }}</span>
        {% elif num > events.number|add:'-3' and num < events.number|add:'3' %}
        <a href="?page={{ num }}" class="page-link">{{ num }}</a>
        {% endif %}
        {% endfor %}
        
        {% if events.has_next %}
        <a href="?page={{ events.next_page_number }}" class="page-link">
            Next <i class="fas fa-chevron-right"></i>
        </a>
        {% endif %}
    </div>
    {% endif %}
    
    <!-- Export Section -->
    <div class="export-section">
        <div>
            <strong>{{ total_events }}</strong> total security events logged
        </div>
        <a href="#" class="export-button">
            <i class="fas fa-download"></i>
            Export Audit Log
        </a>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-refresh audit log every 30 seconds
    setInterval(function() {
        // TODO: Implement AJAX refresh for real-time updates
        console.log('Refreshing security audit log...');
    }, 30000);
    
    // Filter form handling
    const filterForm = document.querySelector('.audit-filters form');
    const filterInputs = filterForm.querySelectorAll('input, select');
    
    filterInputs.forEach(input => {
        input.addEventListener('change', function() {
            filterForm.submit();
        });
    });
});
</script>
{% endblock %} 