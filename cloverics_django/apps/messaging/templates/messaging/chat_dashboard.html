{% extends 'base.html' %}
{% load static %}

{% block title %}Chat Dashboard - Cloverics{% endblock %}

{% block extra_css %}
<!-- Tailwind CSS CDN -->
<script src="https://cdn.tailwindcss.com"></script>
<script>
    tailwind.config = {
        theme: {
            extend: {
                colors: {
                    primary: {
                        50: '#eff6ff',
                        100: '#dbeafe',
                        200: '#bfdbfe',
                        300: '#93c5fd',
                        400: '#60a5fa',
                        500: '#3b82f6',
                        600: '#2563eb',
                        700: '#1d4ed8',
                        800: '#1e40af',
                        900: '#1e3a8a',
                    }
                }
            }
        }
    }
</script>
<style>
    /* Custom animations that can't be done with Tailwind */
    @keyframes float {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-20px) rotate(180deg); }
    }
    
    @keyframes bounce {
        0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
        40% { transform: translateY(-10px); }
        60% { transform: translateY(-5px); }
    }
    
    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.1); }
        100% { transform: scale(1); }
    }
    
    @keyframes spin {
        to { transform: rotate(360deg); }
    }
    
    .animate-float {
        animation: float 6s ease-in-out infinite;
    }
    
    .animate-bounce-slow {
        animation: bounce 2s infinite;
    }
    
    .animate-pulse-slow {
        animation: pulse 2s infinite;
    }
    
    .animate-spin-slow {
        animation: spin 1s ease-in-out infinite;
    }
    
    /* Custom scrollbar styles */
    .custom-scrollbar::-webkit-scrollbar {
        width: 6px;
    }
    
    .custom-scrollbar::-webkit-scrollbar-track {
        background: #f8f9fa;
    }
    
    .custom-scrollbar::-webkit-scrollbar-thumb {
        background: #dee2e6;
        border-radius: 3px;
    }
    
    .custom-scrollbar::-webkit-scrollbar-thumb:hover {
        background: #ced4da;
    }
    
    /* Text truncation for conversation preview */
    .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
</style>
{% endblock %}

{% block content %}
<!-- Mobile Toggle Button -->
<button class="lg:hidden fixed top-4 left-4 z-50 bg-primary-600 text-white rounded-full w-12 h-12 flex items-center justify-center shadow-lg hover:bg-primary-700 transition-colors duration-200" id="mobileToggle" style="display: none;">
    <i class="fas fa-bars"></i>
</button>

<div class="grid grid-cols-1 lg:grid-cols-[350px_1fr] h-screen bg-gray-50 overflow-hidden relative">
    <!-- Conversations Sidebar -->
    <div class="bg-white border-r border-gray-200 flex flex-col shadow-md z-10 lg:relative fixed lg:left-0 -left-full top-0 h-full w-80 lg:w-auto transition-all duration-300" id="conversationsSidebar">
        <div class="bg-gradient-to-br from-primary-600 to-primary-700 text-white p-6 relative overflow-hidden">
            <h5 class="text-xl font-bold relative z-10"><i class="fas fa-comments mr-2"></i> Conversations</h5>
            <div class="mt-4 relative z-10">
                <div class="relative">
                    <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 z-20"></i>
                    <input type="text" class="w-full pl-10 pr-4 py-3 border-0 rounded-full bg-white/90 backdrop-blur-sm text-sm focus:outline-none focus:bg-white focus:ring-2 focus:ring-white/30 focus:scale-105 transition-all duration-200" placeholder="Search conversations..." id="conversationSearch">
                </div>
            </div>
        </div>
        
        <div class="flex-1 overflow-y-auto p-2 custom-scrollbar" id="conversationsList">
            {% for conversation in conversations %}
            <div class="p-4 mb-2 rounded-xl cursor-pointer transition-all duration-300 bg-white border border-gray-200 hover:-translate-y-1 hover:shadow-lg hover:border-primary-500 group" data-conversation-id="{{ conversation.id }}">
                <div class="font-semibold mb-2 flex items-center gap-2">
                    {{ conversation.title|default:conversation.get_participants_summary }}
                    {% if conversation.is_encrypted %}
                    <span class="inline-flex items-center gap-1 text-xs text-green-600 px-2 py-1 bg-green-50 rounded-full border border-green-200">
                        <i class="fas fa-lock text-sm"></i>
                    </span>
                    {% endif %}
                </div>
                <div class="text-sm text-gray-600 mb-3 line-clamp-2">
                    {% if conversation.messages.last %}
                        {{ conversation.messages.last.content|truncatechars:60 }}
                    {% else %}
                        No messages yet
                    {% endif %}
                </div>
                <div class="flex justify-between items-center text-xs text-gray-500">
                    <span>{{ conversation.last_activity_at|timesince }} ago</span>
                    {% if conversation.unread_count > 0 %}
                    <span class="bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold animate-pulse-slow">{{ conversation.unread_count }}</span>
                    {% endif %}
                </div>
            </div>
            {% empty %}
            <div class="text-center py-12 text-gray-500">
                <i class="fas fa-comments text-6xl mb-4 text-gray-300"></i>
                <p class="text-lg font-medium">No conversations yet</p>
                <p class="text-sm">Start chatting with your team!</p>
            </div>
            {% endfor %}
        </div>
    </div>
    
    <!-- Main Content -->
    <div class="flex flex-col bg-gray-50 relative">
        <div class="flex-1 flex flex-col items-center justify-center text-center p-8 m-4 bg-white rounded-xl shadow-md relative overflow-hidden">
            <div class="absolute inset-0 bg-gradient-to-br from-primary-50/50 to-purple-50/50"></div>
            <div class="relative z-10">
                <div class="text-6xl text-primary-600 mb-6 animate-bounce-slow">
                    <i class="fas fa-comments"></i>
                </div>
                <h2 class="text-4xl font-extrabold text-gray-800 mb-4 bg-gradient-to-r from-primary-600 to-purple-600 bg-clip-text text-transparent">Welcome to Cloverics Chat</h2>
                <p class="text-lg text-gray-600 mb-8 max-w-2xl leading-relaxed">Secure, encrypted messaging for your logistics communications. Connect with your team, share updates, and stay informed with real-time messaging.</p>
                
                <!-- Statistics -->
                <div class="grid grid-cols-2 lg:grid-cols-4 gap-6 mb-8 w-full max-w-4xl">
                    <div class="bg-white p-6 rounded-xl shadow-sm text-center transition-all duration-300 border border-gray-200 hover:-translate-y-1 hover:shadow-lg relative overflow-hidden group">
                        <div class="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-primary-500 to-purple-500"></div>
                        <span class="text-4xl font-extrabold text-primary-600 block mb-2">{{ conversations.count }}</span>
                        <div class="text-sm text-gray-600 font-medium uppercase tracking-wide">Conversations</div>
                    </div>
                    <div class="bg-white p-6 rounded-xl shadow-sm text-center transition-all duration-300 border border-gray-200 hover:-translate-y-1 hover:shadow-lg relative overflow-hidden group">
                        <div class="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-primary-500 to-purple-500"></div>
                        <span class="text-4xl font-extrabold text-primary-600 block mb-2">{{ unread_total }}</span>
                        <div class="text-sm text-gray-600 font-medium uppercase tracking-wide">Unread Messages</div>
                    </div>
                    <div class="bg-white p-6 rounded-xl shadow-sm text-center transition-all duration-300 border border-gray-200 hover:-translate-y-1 hover:shadow-lg relative overflow-hidden group">
                        <div class="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-primary-500 to-purple-500"></div>
                        <span class="text-4xl font-extrabold text-primary-600 block mb-2">{{ conversations|length }}</span>
                        <div class="text-sm text-gray-600 font-medium uppercase tracking-wide">Active Chats</div>
                    </div>
                </div>
                
                <!-- Recent Messages -->
                {% if recent_messages %}
                <div class="bg-white rounded-xl shadow-md mb-6 w-full max-w-4xl overflow-hidden">
                    <div class="p-6 border-b border-gray-200 font-bold text-gray-800 bg-gradient-to-r from-gray-50 to-gray-100">
                        <i class="fas fa-clock mr-2"></i> Recent Messages
                    </div>
                    {% for message in recent_messages %}
                    <div class="p-4 lg:p-6 border-b border-gray-100 cursor-pointer transition-all duration-300 flex items-center gap-4 hover:bg-gray-50 hover:translate-x-2 group" data-conversation-id="{{ message.conversation.id }}">
                        <div class="w-10 h-10 rounded-full bg-gradient-to-br from-primary-500 to-purple-500 text-white flex items-center justify-center font-semibold text-sm flex-shrink-0">
                            {{ message.sender.get_full_name|first|upper|default:message.sender.email|first|upper }}
                        </div>
                        <div class="flex-1">
                            <div class="font-semibold text-gray-800 mb-1 flex items-center gap-2">
                                {{ message.sender.get_full_name|default:message.sender.email }}
                                {% if message.is_encrypted %}
                                <span class="inline-flex items-center gap-1 text-xs text-green-600 px-2 py-1 bg-green-50 rounded-full border border-green-200">
                                    <i class="fas fa-lock text-sm"></i>
                                </span>
                                {% endif %}
                            </div>
                            <div class="text-sm text-gray-600 line-clamp-2">
                                {{ message.decrypted_content|truncatechars:80 }}
                            </div>
                            <div class="text-xs text-gray-500 mt-1">
                                {{ message.created_at|timesince }} ago
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% endif %}
                
                <!-- Action Buttons -->
                <div class="flex gap-4 mt-8 flex-wrap justify-center">
                    <a href="{% url 'messaging:create_conversation' %}" class="px-6 py-3 bg-gradient-to-r from-primary-600 to-primary-700 text-white rounded-full font-semibold text-sm flex items-center gap-2 hover:-translate-y-1 hover:shadow-lg transition-all duration-200">
                        <i class="fas fa-plus"></i>
                        New Conversation
                    </a>
                    <a href="{% url 'messaging:search_messages' %}" class="px-6 py-3 bg-white text-gray-700 border-2 border-gray-300 rounded-full font-semibold text-sm flex items-center gap-2 hover:bg-gray-50 hover:border-primary-500 hover:text-primary-600 hover:-translate-y-1 hover:shadow-md transition-all duration-200">
                        <i class="fas fa-search"></i>
                        Search Messages
                    </a>
                    <a href="{% url 'messaging:security_audit' %}" class="px-6 py-3 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-full font-semibold text-sm flex items-center gap-2 hover:-translate-y-1 hover:shadow-lg transition-all duration-200">
                        <i class="fas fa-shield-alt"></i>
                        Security Log
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Mobile toggle functionality
    const mobileToggle = document.getElementById('mobileToggle');
    const conversationsSidebar = document.getElementById('conversationsSidebar');
    
    // Function to handle mobile toggle visibility
    function updateMobileToggleVisibility() {
        if (mobileToggle) {
            if (window.innerWidth <= 1024) { // lg breakpoint
                mobileToggle.style.display = 'block';
            } else {
                mobileToggle.style.display = 'none';
                // Ensure sidebar is visible on desktop
                conversationsSidebar.classList.remove('lg:left-0');
                conversationsSidebar.classList.add('lg:left-0', '-left-full');
            }
        }
    }
    
    // Initial check
    updateMobileToggleVisibility();
    
    // Update on window resize
    window.addEventListener('resize', updateMobileToggleVisibility);
    
    if (mobileToggle) {
        mobileToggle.addEventListener('click', function() {
            conversationsSidebar.classList.toggle('-left-full');
            conversationsSidebar.classList.toggle('left-0');
        });
        
        // Close sidebar when clicking outside on mobile
        document.addEventListener('click', function(e) {
            if (window.innerWidth <= 1024) { // lg breakpoint
                if (!conversationsSidebar.contains(e.target) && !mobileToggle.contains(e.target)) {
                    conversationsSidebar.classList.add('-left-full');
                    conversationsSidebar.classList.remove('left-0');
                }
            }
        });
    }

    // Conversation search functionality with debouncing
    const searchInput = document.getElementById('conversationSearch');
    const conversationsList = document.getElementById('conversationsList');
    let searchTimeout;
    
    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            const query = this.value.toLowerCase();
            const conversationItems = conversationsList.querySelectorAll('.conversation-item');
            
            conversationItems.forEach(item => {
                const title = item.querySelector('.conversation-title').textContent.toLowerCase();
                const preview = item.querySelector('.conversation-preview').textContent.toLowerCase();
                
                if (title.includes(query) || preview.includes(query)) {
                    item.style.display = 'block';
                    item.style.animation = 'fadeIn 0.3s ease-in-out';
                } else {
                    item.style.display = 'none';
                }
            });
        }, 300);
    });
    
    // Conversation click handlers with smooth animations
    const conversationItems = document.querySelectorAll('[data-conversation-id]');
    conversationItems.forEach(item => {
        item.addEventListener('click', function() {
            // Remove active styles from all items
            conversationItems.forEach(i => {
                i.classList.remove('bg-gradient-to-r', 'from-primary-600', 'to-primary-700', 'text-white', 'border-primary-500');
                i.classList.add('bg-white', 'border-gray-200');
            });
            
            // Add active styles to clicked item
            this.classList.remove('bg-white', 'border-gray-200');
            this.classList.add('bg-gradient-to-r', 'from-primary-600', 'to-primary-700', 'text-white', 'border-primary-500');
            
            // Add loading state
            const conversationId = this.dataset.conversationId;
            
            // Simulate loading
            setTimeout(() => {
                window.location.href = `/messaging/conversation/${conversationId}/`;
            }, 300);
        });
    });
    
    // Message click handlers
    const messageItems = document.querySelectorAll('[data-conversation-id]');
    messageItems.forEach(item => {
        item.addEventListener('click', function() {
            const conversationId = this.dataset.conversationId;
            
            // Add loading state
            this.classList.add('opacity-70', 'scale-95');
            
            setTimeout(() => {
                window.location.href = `/messaging/conversation/${conversationId}/`;
            }, 200);
        });
    });
    
    // Auto-refresh conversations every 30 seconds
    setInterval(function() {
        // TODO: Implement AJAX refresh for real-time updates
        console.log('Refreshing conversations...');
    }, 30000);
    
    // Add hover effects to action buttons
    const buttons = document.querySelectorAll('a[href*="messaging"]');
    buttons.forEach(button => {
        button.addEventListener('mouseenter', function() {
            this.classList.add('-translate-y-1', 'scale-105');
        });
        
        button.addEventListener('mouseleave', function() {
            this.classList.remove('-translate-y-1', 'scale-105');
        });
    });
    
    // Add typing indicator for search
    searchInput.addEventListener('focus', function() {
        this.parentElement.classList.add('scale-105');
    });
    
    searchInput.addEventListener('blur', function() {
        this.parentElement.classList.remove('scale-105');
    });
    
    // Add smooth scrolling to conversations list
    const conversationsListElement = document.getElementById('conversationsList');
    if (conversationsListElement) {
        conversationsListElement.style.scrollBehavior = 'smooth';
    }
    
    // Add notification sound for new messages (optional)
    function playNotificationSound() {
        // TODO: Implement notification sound
        console.log('New message notification');
    }
    
    // Add keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + K to focus search
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            searchInput.focus();
        }
        
        // Ctrl/Cmd + N to create new conversation
        if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
            e.preventDefault();
            window.location.href = "{% url 'messaging:create_conversation' %}";
        }
    });
    
    // Add CSS animations
    const style = document.createElement('style');
    style.textContent = `
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        [data-conversation-id] {
            animation: fadeIn 0.3s ease-out;
        }
        
        .stat-card, .bg-white.p-6.rounded-xl {
            animation: fadeIn 0.5s ease-out;
        }
        
        .bg-white.p-6.rounded-xl:nth-child(1) { animation-delay: 0.1s; }
        .bg-white.p-6.rounded-xl:nth-child(2) { animation-delay: 0.2s; }
        .bg-white.p-6.rounded-xl:nth-child(3) { animation-delay: 0.3s; }
        .bg-white.p-6.rounded-xl:nth-child(4) { animation-delay: 0.4s; }
    `;
    document.head.appendChild(style);
});
</script>
{% endblock %} 