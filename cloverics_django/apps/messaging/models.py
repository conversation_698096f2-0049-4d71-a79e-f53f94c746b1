"""
Cloverics Message Models

This module defines the database models for the messaging system.
"""

from django.db import models
from django.conf import settings
from django.utils import timezone
import hashlib
import json
from utils.encryption_utils import encryption


class EncryptionKey(models.Model):
    """
    Encryption key management for secure messaging
    """
    
    KEY_TYPES = [
        ('conversation', 'Conversation Key'),
        ('message', 'Message Key'),
        ('system', 'System Key'),
    ]
    
    KEY_STATUSES = [
        ('active', 'Active'),
        ('rotated', 'Rotated'),
        ('compromised', 'Compromised'),
        ('expired', 'Expired'),
    ]
    
    key_id = models.CharField(max_length=100, unique=True, help_text="Unique identifier for the encryption key")
    key_type = models.CharField(max_length=20, choices=KEY_TYPES, default='conversation')
    status = models.CharField(max_length=20, choices=KEY_STATUSES, default='active')
    
    # Key metadata (actual key material stored securely elsewhere)
    key_version = models.IntegerField(default=1)
    key_size = models.IntegerField(default=256)
    
    # Key lifecycle
    created_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField(null=True, blank=True)
    rotated_at = models.DateTimeField(null=True, blank=True)
    
    # Access control
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        related_name='created_encryption_keys'
    )
    authorized_users = models.ManyToManyField(
        settings.AUTH_USER_MODEL,
        related_name='authorized_encryption_keys',
        blank=True
    )
    
    class Meta:
        indexes = [
            models.Index(fields=['key_type', 'status']),
            models.Index(fields=['expires_at']),
            models.Index(fields=['created_at']),
        ]
    
    def __str__(self):
        return f"Encryption Key {self.key_id} ({self.key_type})"
    
    @property
    def algorithm(self):
        """Get algorithm from Django settings"""
        from django.conf import settings
        return getattr(settings, 'ENCRYPTION_CONFIG', {}).get('ALGORITHM', 'AES-256-GCM')
    
    def is_valid(self):
        """Check if the key is valid and not expired"""
        if self.status != 'active':
            return False
        if self.expires_at and timezone.now() > self.expires_at:
            return False
        return True
    
    def rotate_key(self):
        """Mark key as rotated and create new version"""
        self.status = 'rotated'
        self.rotated_at = timezone.now()
        self.save()


class Conversation(models.Model):
    """
    Enhanced conversation model with advanced features
    """
    
    CHAT_TYPE_CHOICES = [
        ('support', 'Support Team'),
        ('logistics', 'Logistics Partner'),
        ('driver', 'Driver'),
        ('customs', 'Customs Officer'),
        ('general', 'General User'),
        ('group', 'Group Chat'),
        ('broadcast', 'Broadcast'),
    ]
    
    # Enhanced participant system
    participants = models.ManyToManyField(
        settings.AUTH_USER_MODEL,
        through='ConversationParticipant',
        related_name='conversations'
    )
    
    # Conversation metadata
    title = models.CharField(max_length=255, blank=True, null=True)
    description = models.TextField(blank=True, null=True)
    avatar = models.ImageField(upload_to='conversation_avatars/', null=True, blank=True)
    
    # Enhanced chat type and context
    chat_type = models.CharField(
        max_length=20,
        choices=CHAT_TYPE_CHOICES,
        default='general',
        db_index=True
    )
    
    # Related business objects
    related_shipment_id = models.CharField(max_length=50, blank=True, null=True, db_index=True)
    related_declaration_id = models.CharField(max_length=50, blank=True, null=True, db_index=True)
    related_policy_id = models.CharField(max_length=50, blank=True, null=True, db_index=True)
    related_quote_id = models.CharField(max_length=50, blank=True, null=True, db_index=True)
    
    # Enhanced status and priority
    status = models.CharField(
        max_length=20,
        choices=[
            ('active', 'Active'),
            ('archived', 'Archived'),
            ('muted', 'Muted'),
            ('blocked', 'Blocked'),
            ('deleted', 'Deleted')
        ],
        default='active',
        db_index=True
    )
    
    priority = models.CharField(
        max_length=20,
        choices=[
            ('low', 'Low'),
            ('normal', 'Normal'),
            ('high', 'High'),
            ('urgent', 'Urgent'),
            ('critical', 'Critical')
        ],
        default='normal',
        db_index=True
    )
    
    # Conversation settings
    is_encrypted = models.BooleanField(default=True, help_text="Conversations are encrypted by default for security")
    encryption_key_id = models.CharField(max_length=100, blank=True, help_text="ID of the encryption key used for this conversation")
    allow_attachments = models.BooleanField(default=True)
    allow_reactions = models.BooleanField(default=True)
    allow_editing = models.BooleanField(default=True)
    auto_archive_days = models.IntegerField(default=30)
    
    # Statistics
    message_count = models.IntegerField(default=0)
    last_message_at = models.DateTimeField(null=True, blank=True)
    last_activity_at = models.DateTimeField(auto_now=True)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    archived_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        ordering = ['-last_activity_at']
        indexes = [
            models.Index(fields=['chat_type', 'status']),
            models.Index(fields=['priority', 'last_activity_at']),
            models.Index(fields=['related_shipment_id', 'status']),
        ]
    
    def __str__(self):
        if self.title:
            return self.title
        elif self.chat_type == 'group':
            return f"Group Chat: {', '.join([p.get_full_name() or p.email for p in self.participants.all()[:3]])}"
        else:
            return f"{self.chat_type.title()} Chat"
    
    def add_participant(self, user, role='member'):
        """Add a participant to the conversation"""
        ConversationParticipant.objects.get_or_create(
            conversation=self,
            user=user,
            defaults={'role': role, 'joined_at': timezone.now()}
        )
    
    def remove_participant(self, user):
        """Remove a participant from the conversation"""
        ConversationParticipant.objects.filter(
            conversation=self,
            user=user
        ).delete()
    
    def get_unread_count(self, user):
        """Get unread message count for a user"""
        return self.messages.filter(
            is_read=False,
            sender__in=self.participants.exclude(id=user.id)
        ).count()
    
    def mark_all_as_read(self, user):
        """Mark all messages as read for a user"""
        self.messages.filter(
            is_read=False,
            sender__in=self.participants.exclude(id=user.id)
        ).update(is_read=True, read_at=timezone.now())
    
    def archive_conversation(self):
        """Archive the conversation"""
        self.status = 'archived'
        self.archived_at = timezone.now()
        self.save(update_fields=['status', 'archived_at'])
    
    def get_participants_summary(self):
        """Get a summary of participants"""
        participants = self.participants.all()
        if self.chat_type == 'group':
            return f"{participants.count()} participants"
        else:
            return ', '.join([p.get_full_name() or p.email for p in participants])
    
    def set_encryption_key(self, key_id):
        """Set encryption key for the conversation"""
        self.encryption_key_id = key_id
        self.is_encrypted = True
        self.save(update_fields=['encryption_key_id', 'is_encrypted'])
    
    def can_user_access(self, user):
        """Check if user has access to this encrypted conversation"""
        if not self.is_encrypted:
            return True
        
        # Check if user is a participant
        if not self.participants.filter(id=user.id).exists():
            return False
        
        # Check if user has access to the encryption key
        if self.encryption_key_id:
            # TODO: Implement key access verification
            return True
        
        return True
    
    def get_encrypted_messages_count(self):
        """Get count of encrypted messages in this conversation"""
        return self.messages.filter(is_encrypted=True).count()


class ConversationParticipant(models.Model):
    """
    Through model for conversation participants with roles
    """
    
    PARTICIPANT_ROLES = [
        ('owner', 'Owner'),
        ('admin', 'Admin'),
        ('moderator', 'Moderator'),
        ('member', 'Member'),
        ('readonly', 'Read Only'),
    ]
    
    conversation = models.ForeignKey(Conversation, on_delete=models.CASCADE)
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    role = models.CharField(max_length=20, choices=PARTICIPANT_ROLES, default='member')
    
    # Participant-specific settings
    is_muted = models.BooleanField(default=False)
    is_blocked = models.BooleanField(default=False)
    notification_preferences = models.JSONField(default=dict)
    
    # Timestamps
    joined_at = models.DateTimeField(auto_now_add=True)
    last_read_at = models.DateTimeField(null=True, blank=True)
    last_activity_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        unique_together = ['conversation', 'user']
        indexes = [
            models.Index(fields=['conversation', 'role']),
            models.Index(fields=['user', 'last_activity_at']),
        ]
    
    def __str__(self):
        return f"{self.user} in {self.conversation} ({self.role})"


class MessageAttachment(models.Model):
    """
    Enhanced attachment model for messages
    """
    
    ATTACHMENT_TYPES = [
        ('document', 'Document'),
        ('image', 'Image'),
        ('video', 'Video'),
        ('audio', 'Audio'),
        ('archive', 'Archive'),
        ('other', 'Other'),
    ]
    
    file = models.FileField(upload_to='chat_attachments/%Y/%m/%d/')
    original_filename = models.CharField(max_length=255)
    file_type = models.CharField(max_length=20, choices=ATTACHMENT_TYPES)
    file_size = models.BigIntegerField()  # Size in bytes
    mime_type = models.CharField(max_length=100)
    
    # For images/videos
    width = models.IntegerField(null=True, blank=True)
    height = models.IntegerField(null=True, blank=True)
    duration = models.FloatField(null=True, blank=True)  # For videos/audio
    
    # Security and access control
    is_public = models.BooleanField(default=False)
    access_token = models.CharField(max_length=100, blank=True)
    
    # Upload metadata
    uploaded_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='uploaded_attachments'
    )
    uploaded_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['file_type', 'uploaded_at']),
            models.Index(fields=['uploaded_by', 'uploaded_at']),
        ]
    
    def __str__(self):
        return f"{self.original_filename} ({self.file_type})"
    
    def get_file_url(self):
        """Get secure file URL"""
        if self.is_public:
            return self.file.url
        else:
            return f"/secure-file/{self.access_token}/"


class Message(models.Model):
    """
    Enhanced message model with advanced features
    """
    
    MESSAGE_TYPES = [
        ('text', 'Text Message'),
        ('file', 'File Attachment'),
        ('system', 'System Message'),
        ('quote', 'Quote Request'),
        ('status_update', 'Status Update'),
        ('payment', 'Payment Notification'),
        ('customs', 'Customs Update'),
        ('driver', 'Driver Update'),
    ]
    
    conversation = models.ForeignKey(
        Conversation,
        on_delete=models.CASCADE,
        related_name='messages',
        db_index=True
    )
    
    sender = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='messages_sent',
        db_index=True
    )
    
    # Content type (no plaintext content field - only encrypted)
    content_type = models.CharField(
        max_length=20,
        choices=MESSAGE_TYPES,
        default='text',
        db_index=True
    )
    
    # Rich content support
    rich_content = models.JSONField(default=dict, blank=True)  # For formatted text, embeds, etc.
    
    # Enhanced attachment system
    attachments = models.ManyToManyField(MessageAttachment, blank=True)
    
    # Message metadata
    is_read = models.BooleanField(default=False, db_index=True)
    is_edited = models.BooleanField(default=False)
    is_deleted = models.BooleanField(default=False)
    is_pinned = models.BooleanField(default=False)
    
    # Reply functionality
    reply_to = models.ForeignKey(
        'self',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='replies'
    )
    
    # Message reactions
    reactions = models.JSONField(default=dict)  # {user_id: reaction_type}
    
    # Delivery and read receipts
    delivered_at = models.DateTimeField(null=True, blank=True)
    read_at = models.DateTimeField(null=True, blank=True)
    
    # Message encryption (enabled by default for security)
    is_encrypted = models.BooleanField(default=True, help_text="Messages are encrypted by default for security")
    encryption_key_id = models.CharField(max_length=100, blank=True, help_text="ID of the encryption key used for this message")
    encrypted_content = models.TextField(blank=True, help_text="Encrypted message content")
    content_hash = models.CharField(max_length=64, blank=True, help_text="SHA-256 hash of original content for integrity verification")
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True, db_index=True)
    updated_at = models.DateTimeField(auto_now=True)
    edited_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        ordering = ['created_at']
        indexes = [
            models.Index(fields=['conversation', 'created_at']),
            models.Index(fields=['sender', 'created_at']),
            models.Index(fields=['is_read', 'created_at']),
            models.Index(fields=['content_type', 'created_at']),
            models.Index(fields=['reply_to']),
        ]
    
    def __str__(self):
        return f"Message #{self.id} from {self.sender} in conversation {self.conversation.id}"
    
    def mark_as_read(self, user=None):
        """Mark this message as read by a specific user"""
        if not self.is_read:
            self.is_read = True
            self.read_at = timezone.now()
            self.save(update_fields=['is_read', 'read_at'])
    
    def add_reaction(self, user, reaction_type):
        """Add a reaction to the message"""
        reactions = self.reactions.copy()
        reactions[str(user.id)] = reaction_type
        self.reactions = reactions
        self.save(update_fields=['reactions'])
    
    def remove_reaction(self, user):
        """Remove a user's reaction from the message"""
        reactions = self.reactions.copy()
        reactions.pop(str(user.id), None)
        self.reactions = reactions
        self.save(update_fields=['reactions'])
    
    def edit_message(self, new_content, user):
        """Edit the message content"""
        if self.sender != user:
            raise PermissionError("Only the sender can edit messages")
        
        # Encrypt the new content if message is encrypted
        if self.is_encrypted:
            encrypted_content = self.encrypt_message_content(new_content, self.encryption_key_id)
            if encrypted_content:
                self.set_encrypted_content(encrypted_content, self.encryption_key_id)
            else:
                # Fallback to unencrypted if encryption fails
                import logging
                logger = logging.getLogger(__name__)
                logger.warning(f"Failed to encrypt edited message {self.id}, storing unencrypted")
                self.is_encrypted = False
                self.encrypted_content = new_content
        else:
            # For unencrypted messages, store directly
            self.encrypted_content = new_content
        
        self.is_edited = True
        self.edited_at = timezone.now()
        self.save(update_fields=['encrypted_content', 'is_encrypted', 'is_edited', 'edited_at'])
    
    def soft_delete(self, user):
        """Soft delete the message"""
        if self.sender != user:
            raise PermissionError("Only the sender can delete messages")
        
        self.is_deleted = True
        self.save(update_fields=['is_deleted'])
    
    def get_content(self, conversation_key_id=None):
        """Get message content - decrypt if encrypted, return as-is if not encrypted"""
        if self.is_encrypted and self.encrypted_content:
            return self.decrypt_message_content(conversation_key_id)
        return self.encrypted_content or ""  # Return empty string if no content
    
    def set_content(self, content, conversation_key_id=None):
        """Set message content with automatic encryption"""
        if self.is_encrypted:
            encrypted_content = self.encrypt_message_content(content, conversation_key_id)
            if encrypted_content:
                self.set_encrypted_content(encrypted_content, conversation_key_id)
            else:
                # Fallback to unencrypted if encryption fails
                import logging
                logger = logging.getLogger(__name__)
                logger.warning(f"Failed to encrypt message {self.id}, storing unencrypted")
                self.is_encrypted = False
                self.encrypted_content = content
                self.save(update_fields=['encrypted_content', 'is_encrypted'])
        else:
            # For unencrypted messages, store directly
            self.encrypted_content = content
            self.save(update_fields=['encrypted_content'])
    
    def generate_content_hash(self):
        """Generate SHA-256 hash of the content for integrity verification"""
        content = self.get_content(self.encryption_key_id)
        if content:
            return hashlib.sha256(content.encode('utf-8')).hexdigest()
        return ''
    
    def verify_content_integrity(self):
        """Verify the content hash to ensure message integrity"""
        if not self.content_hash:
            return False
        current_hash = self.generate_content_hash()
        return current_hash == self.content_hash
    
    def set_encrypted_content(self, encrypted_content, key_id):
        """Set encrypted content and update encryption metadata"""
        self.encrypted_content = encrypted_content
        self.encryption_key_id = key_id
        self.is_encrypted = True
        self.content_hash = self.generate_content_hash()
        self.save(update_fields=['encrypted_content', 'encryption_key_id', 'is_encrypted', 'content_hash'])
    
    def encrypt_message_content(self, content, conversation_key_id=None):
        """Encrypt message content using AES-256-GCM"""
        try:
            encrypted_data = encryption.encrypt_message(content, conversation_key_id)
            return json.dumps(encrypted_data)
        except Exception as e:
            # Log error but don't fail - fall back to unencrypted
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Failed to encrypt message: {e}")
            return None
    
    def decrypt_message_content(self, conversation_key_id=None):
        """Decrypt message content"""
        if not self.is_encrypted or not self.encrypted_content:
            return self.encrypted_content or ""
        
        try:
            encrypted_data = json.loads(self.encrypted_content)
            decrypted = encryption.decrypt_message(
                encrypted_data['encrypted_data'],
                encrypted_data['nonce'],
                salt=encrypted_data.get('salt'),  # Optional for conversations
                conversation_key_id=conversation_key_id
            )
            return decrypted
        except Exception as e:
            # Log error and return placeholder
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Failed to decrypt message {self.id}: {e}")
            return "[Encrypted message - decryption failed]"
    
    def get_decrypted_content(self, conversation_key_id=None):
        """Get decrypted content for display (alias for get_content)"""
        return self.get_content(conversation_key_id)


class ConversationInvitation(models.Model):
    """
    Invitations to join conversations
    """
    
    INVITATION_STATUSES = [
        ('pending', 'Pending'),
        ('accepted', 'Accepted'),
        ('declined', 'Declined'),
        ('expired', 'Expired'),
    ]
    
    conversation = models.ForeignKey(
        Conversation,
        on_delete=models.CASCADE,
        related_name='invitations'
    )
    
    invited_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='sent_invitations'
    )
    
    invited_user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='received_invitations'
    )
    
    message = models.TextField(blank=True)
    status = models.CharField(max_length=20, choices=INVITATION_STATUSES, default='pending')
    
    expires_at = models.DateTimeField()
    created_at = models.DateTimeField(auto_now_add=True)
    responded_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        unique_together = ['conversation', 'invited_user']
        indexes = [
            models.Index(fields=['invited_user', 'status']),
            models.Index(fields=['expires_at']),
        ]
    
    def __str__(self):
        return f"Invitation to {self.conversation} for {self.invited_user}"
    
    def is_expired(self):
        return timezone.now() > self.expires_at
    
    def accept(self):
        """Accept the invitation"""
        if self.is_expired():
            raise ValueError("Invitation has expired")
        
        self.status = 'accepted'
        self.responded_at = timezone.now()
        self.save()
        
        # Add user to conversation
        self.conversation.add_participant(self.invited_user)
    
    def decline(self):
        """Decline the invitation"""
        self.status = 'declined'
        self.responded_at = timezone.now()
        self.save()


class MessageSearch(models.Model):
    """
    Search index for messages to enable fast full-text search
    Note: Search vectors are encrypted for security
    """
    
    message = models.OneToOneField(Message, on_delete=models.CASCADE)
    encrypted_search_vector = models.TextField(help_text="Encrypted full-text search vector")
    encrypted_keywords = models.TextField(help_text="Encrypted extracted keywords")
    
    # Search metadata (unencrypted for indexing)
    message_type = models.CharField(max_length=20, blank=True)
    sender_id = models.IntegerField(null=True, blank=True)
    conversation_id = models.IntegerField(null=True, blank=True)
    created_date = models.DateField(null=True, blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['message_type', 'created_date']),
            models.Index(fields=['sender_id', 'created_date']),
            models.Index(fields=['conversation_id', 'created_date']),
        ]
    
    def __str__(self):
        return f"Search index for Message #{self.message.id}"


class SecurityAuditLog(models.Model):
    """
    Audit log for security-related events in messaging
    """
    
    EVENT_TYPES = [
        ('key_created', 'Encryption Key Created'),
        ('key_rotated', 'Encryption Key Rotated'),
        ('key_compromised', 'Encryption Key Compromised'),
        ('message_encrypted', 'Message Encrypted'),
        ('message_decrypted', 'Message Decrypted'),
        ('access_denied', 'Access Denied'),
        ('conversation_created', 'Encrypted Conversation Created'),
        ('participant_added', 'Participant Added to Encrypted Conversation'),
        ('participant_removed', 'Participant Removed from Encrypted Conversation'),
    ]
    
    event_type = models.CharField(max_length=50, choices=EVENT_TYPES)
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        related_name='security_audit_events'
    )
    
    # Related objects
    conversation = models.ForeignKey(
        Conversation,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='security_audit_events'
    )
    message = models.ForeignKey(
        Message,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='security_audit_events'
    )
    encryption_key = models.ForeignKey(
        EncryptionKey,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='security_audit_events'
    )
    
    # Event details
    details = models.JSONField(default=dict)
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['event_type', 'created_at']),
            models.Index(fields=['user', 'created_at']),
            models.Index(fields=['conversation', 'created_at']),
        ]
    
    def __str__(self):
        return f"{self.event_type} by {self.user} at {self.created_at}"


class EncryptionPolicy(models.Model):
    """
    Encryption policies for different types of conversations and messages
    """
    
    POLICY_TYPES = [
        ('conversation', 'Conversation Policy'),
        ('message', 'Message Policy'),
        ('attachment', 'Attachment Policy'),
        ('search', 'Search Policy'),
    ]
    
    ENCRYPTION_LEVELS = [
        ('none', 'No Encryption'),
        ('basic', 'Basic Encryption'),
        ('standard', 'Standard Encryption'),
        ('high', 'High Security'),
        ('military', 'Military Grade'),
    ]
    
    name = models.CharField(max_length=100, unique=True)
    policy_type = models.CharField(max_length=20, choices=POLICY_TYPES)
    encryption_level = models.CharField(max_length=20, choices=ENCRYPTION_LEVELS, default='standard')
    
    # Policy rules
    chat_types = models.JSONField(default=list, help_text="Chat types this policy applies to")
    user_roles = models.JSONField(default=list, help_text="User roles this policy applies to")
    content_keywords = models.JSONField(default=list, help_text="Keywords that trigger this policy")
    
    # Encryption settings
    algorithm = models.CharField(max_length=50, default='AES-256-GCM')
    key_rotation_days = models.IntegerField(default=90)
    require_key_verification = models.BooleanField(default=True)
    
    # Access control
    is_active = models.BooleanField(default=True)
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        related_name='created_encryption_policies'
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-is_active', 'name']
        indexes = [
            models.Index(fields=['policy_type', 'is_active']),
            models.Index(fields=['encryption_level', 'is_active']),
        ]
    
    def __str__(self):
        return f"{self.name} ({self.encryption_level})"
    
    def applies_to_conversation(self, conversation):
        """Check if this policy applies to a conversation"""
        if not self.is_active:
            return False
        
        # Check chat type
        if self.chat_types and conversation.chat_type not in self.chat_types:
            return False
        
        # Check participants' roles
        if self.user_roles:
            participant_roles = set()
            for participant in conversation.participants.all():
                # TODO: Get actual user roles from user model
                participant_roles.add('user')  # Placeholder
            
            if not any(role in participant_roles for role in self.user_roles):
                return False
        
        return True
    
    def applies_to_message(self, message):
        """Check if this policy applies to a message"""
        if not self.is_active:
            return False
        
        # Check content keywords
        if self.content_keywords:
            content = message.get_content()
            if content:
                content_lower = content.lower()
                if not any(keyword.lower() in content_lower for keyword in self.content_keywords):
                    return False
        
        return True