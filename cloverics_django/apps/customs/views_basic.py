"""
Basic views for Customs app - Phase 7 Dashboard Routing
Simple dashboard view for customs agents during authentication system development
"""
from django.shortcuts import render
from django.http import HttpResponse
from django.views.decorators.csrf import csrf_exempt

@csrf_exempt
def customs_dashboard(request):
    """
    Basic customs agent dashboard - Phase 7 implementation
    Simple dashboard for testing user type-based authentication routing
    """
    return HttpResponse("""
    <!DOCTYPE html>
    <html>
    <head>
        <title>Customs Agent Dashboard - Cloverics</title>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <style>
            body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
            .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #ffc107; padding-bottom: 20px; }
            .status-card { background: #fffbf0; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #ffc107; }
            .success { color: #28a745; font-weight: bold; }
            .back-link { text-align: center; margin-top: 30px; }
            .back-link a { color: #ffc107; text-decoration: none; padding: 10px 20px; border: 1px solid #ffc107; border-radius: 5px; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🛃 Customs Agent Dashboard</h1>
                <h2>🍀 Cloverics Platform</h2>
            </div>
            
            <div class="status-card">
                <h3 class="success">✅ Authentication Successful</h3>
                <p><strong>User Type:</strong> CUSTOMS_AGENT</p>
                <p><strong>Dashboard Status:</strong> Phase 7 Basic Implementation</p>
                <p><strong>Authentication System:</strong> JWT + User Type Routing Working</p>
            </div>
            
            <div class="status-card">
                <h4>🔧 Phase 7 Completion Status:</h4>
                <ul>
                    <li>✅ JSON Authentication Working</li>
                    <li>✅ Form Authentication Fixed</li>
                    <li>✅ User Type Routing Implemented</li>
                    <li>✅ JWT Cookie Management Active</li>
                    <li>✅ Dashboard Redirection Functional</li>
                </ul>
            </div>
            
            <div class="back-link">
                <a href="/">← Back to Home</a>
                <a href="/auth/login/" style="margin-left: 10px;">Login Page</a>
            </div>
        </div>
    </body>
    </html>
    """)