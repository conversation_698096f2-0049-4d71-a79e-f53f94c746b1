"""
Consolidated URLs for customs app
Includes all extracted FastAPI endpoints converted to Django patterns
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views_django_implementation as views

app_name = 'customs'

# REST API Router for ViewSets
router = DefaultRouter()
router.register(r'declarations', views.CustomsDeclarationViewSet, basename='declaration')

urlpatterns = [
    # ==================== CUSTOMS DASHBOARD ====================
    path('', views.customs_dashboard, name='dashboard'),
    path('dashboard/', views.customs_dashboard, name='dashboard_alt'),
    
    # ==================== CUSTOMS DECLARATIONS MANAGEMENT ====================
    path('declarations/', views.customs_declarations_list, name='declarations_list'),
    path('declarations/<int:declaration_id>/', views.declaration_detail, name='declaration_detail'),
    path('declarations/<int:declaration_id>/approve/', views.approve_declaration, name='approve_declaration'),
    path('declarations/<int:declaration_id>/reject/', views.reject_declaration, name='reject_declaration'),
    
    # ==================== API ENDPOINTS ====================
    # Converted from FastAPI endpoints
    path('api/filter-declarations/', views.filter_declarations_api, name='filter_declarations_api'),
    path('api/new-declaration/', views.create_declaration_api, name='create_declaration_api'),
    
    # REST API endpoints
    path('api/', include(router.urls)),
    
    # ==================== EXTRACTED FASTAPI ENDPOINTS (TO BE IMPLEMENTED) ====================
    # These were extracted from FastAPI and need Django implementation:
    # POST /customs/declaration/{declaration_id}/approve -> approve_declaration (✅ IMPLEMENTED)
    # POST /customs/declaration/{declaration_id}/reject -> reject_declaration (✅ IMPLEMENTED) 
    # POST /customs/filter-declarations -> filter_declarations_api (✅ IMPLEMENTED)
    # POST /customs/new-declaration -> create_declaration_api (✅ IMPLEMENTED)
    # POST /customs/review-declaration/{declaration_id} -> (TO BE IMPLEMENTED)
    # GET /customs/view-declaration/{declaration_id} -> declaration_detail (✅ IMPLEMENTED)
    # GET /customs/print-declaration/{declaration_id} -> (TO BE IMPLEMENTED)
    # POST /customs/appeal-declaration/{declaration_id} -> (TO BE IMPLEMENTED)
    # POST /customs/action-required/{declaration_id} -> (TO BE IMPLEMENTED)
    # POST /customs/review-clearance/{declaration_id} -> (TO BE IMPLEMENTED)
    # GET /customs/inspections -> (TO BE IMPLEMENTED)
]

# URL patterns for remaining FastAPI endpoints to be converted:
"""
Additional endpoints extracted from FastAPI that need Django implementation:

1. Review Declaration Workflow:
   path('declarations/<int:declaration_id>/review/', views.review_declaration, name='review_declaration'),

2. Print Declaration:
   path('declarations/<int:declaration_id>/print/', views.print_declaration, name='print_declaration'),

3. Appeal Declaration:
   path('declarations/<int:declaration_id>/appeal/', views.appeal_declaration, name='appeal_declaration'),

4. Action Required:
   path('declarations/<int:declaration_id>/action-required/', views.action_required, name='action_required'),

5. Review Clearance:
   path('declarations/<int:declaration_id>/review-clearance/', views.review_clearance, name='review_clearance'),

6. Inspections Management:
   path('inspections/', views.inspections_list, name='inspections_list'),
   
7. Export/CSV functionality:
   path('declarations/export/', views.export_declarations, name='export_declarations'),
"""