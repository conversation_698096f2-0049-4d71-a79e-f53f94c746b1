"""
Customs views extracted from FastAPI main file
Original location: lines 2843-8100+ in fastapi_main.py
"""

import json
import csv
import io
from datetime import datetime
from decimal import Decimal

from django.shortcuts import render, get_object_or_404, redirect
from django.http import JsonResponse, HttpResponse, Http404
from django.contrib.auth.decorators import login_required
from django.views.decorators.http import require_http_methods, require_POST
from django.views.decorators.csrf import csrf_exempt
from django.core.paginator import Paginator
from django.db.models import Q, Count
from django.utils import timezone
from django.contrib import messages

from .models import CustomsDeclaration, CustomsInspection, CustomsClearance
from apps.core.models import User, Notification
from apps.shipments.models import Shipment


def require_role(roles):
    """Decorator to check user roles - simplified version"""
    def decorator(view_func):
        def wrapper(request, *args, **kwargs):
            if not request.user.is_authenticated:
                return JsonResponse({"error": "Authentication required"}, status=401)
            if isinstance(roles, str):
                roles_list = [roles]
            else:
                roles_list = roles
            if request.user.user_type not in roles_list:
                return JsonResponse({"error": "Access denied"}, status=403)
            return view_func(request, *args, **kwargs)
        return wrapper
    return decorator


# ✅ EXTRACTED FROM FASTAPI: Customer customs declaration submission
# Original lines 2843-2979 in fastapi_main.py
@login_required
@require_POST
@require_role("CUSTOMER")
def submit_customs_declaration(request):
    """Submit a new customs declaration - Connected to database"""
    try:
        # Get form data
        shipment_id = request.POST.get('shipment_id')
        goods_description = request.POST.get('goods_description')
        goods_value = float(request.POST.get('goods_value', 0))
        country_of_origin = request.POST.get('country_of_origin')
        tariff_code = request.POST.get('tariff_code')
        
        # Get the shipment and verify ownership
        try:
            shipment = Shipment.objects.get(id=shipment_id, customer=request.user)
        except Shipment.DoesNotExist:
            messages.error(request, "Shipment not found or access denied")
            return redirect('customers:customs_declaration')
        
        # Check if declaration already exists for this shipment
        if CustomsDeclaration.objects.filter(shipment=shipment).exists():
            messages.error(request, "A customs declaration already exists for this shipment.")
            return redirect('customers:customs_declaration')
        
        # Generate unique declaration number
        import random
        declaration_number = f"DEC-{timezone.now().strftime('%Y%m%d')}-{random.randint(1000, 9999)}"
        
        # Create customs declaration in database
        
@login_required
@require_role(["CUSTOMS_AGENT"])
def customs_clearance_page(request):
    """Clearance processing management with real database data - EXTRACTED FROM FASTAPI"""
    try:
        # Get customs declarations for clearance processing
        declarations = CustomsDeclaration.objects.select_related(
            'shipment', 'shipment__customer'
        ).all().values(
            'id', 'declaration_number', 'shipment__customer__company_name', 
            'shipment__customer__first_name', 'shipment__customer__last_name', 
            'goods_description', 'goods_value', 'status',
            'submitted_date', 'processed_date', 'shipment__origin_city', 
            'shipment__destination_city', 'country_of_origin', 
            'declaration_type', 'duties_amount', 'taxes_amount'
        )
        
        # Calculate statistics from real data
        total_count = declarations.count()
        in_process_count = declarations.filter(status__in=['SUBMITTED', 'UNDER_REVIEW']).count()
        cleared_today_count = declarations.filter(status='APPROVED').count()
        on_hold_count = declarations.filter(status='HOLD').count()
        
        # Priority queue - urgent declarations (submitted or under review)
        priority_declarations = []
        recent_declarations = []
        
        # Format priority declarations for display
        for decl in declarations:
            if decl['status'] in ['SUBMITTED', 'UNDER_REVIEW']:
                # Determine priority based on goods value and urgency
                value = decl['goods_value'] or 0
                priority = 'Urgent' if value > 100000 else 'High' if value > 50000 else 'Standard'
                
                # Calculate time in queue (estimate)
                time_in_queue = "1.2 hrs"  # Default estimate
                if decl['submitted_date']:
                    time_diff = timezone.now() - decl['submitted_date']
                    hours = time_diff.total_seconds() / 3600
                    time_in_queue = f"{hours:.1f} hrs"
                
                priority_declarations.append({
                    'id': decl['id'],
                    'clearance_id': f"CL-2025-{decl['id']:04d}",
                    'shipment_id': f"SHP-2025-{decl['id']:04d}",
                    'importer': decl['shipment__customer__company_name'] or f"{decl['shipment__customer__first_name']} {decl['shipment__customer__last_name']}",
                    'cargo_type': decl['goods_description'][:20] + '...' if decl['goods_description'] and len(decl['goods_description']) > 20 else decl['goods_description'] or 'General Cargo',
                    'value': f"${value:,.0f}" if value else "N/A",
                    'priority': priority,
                    'status': decl['status'],
                    'time_in_queue': time_in_queue
                })
        
        # Format recent declarations for display
        for decl in list(declarations)[:10]:  # Show most recent 10
            # Get processing time
            time_processed = "N/A"
            if decl['processed_date']:
                time_processed = decl['processed_date'].strftime('%H:%M')
            elif decl['submitted_date']:
                time_processed = decl['submitted_date'].strftime('%H:%M')
            
            recent_declarations.append({
                'id': decl['id'],
                'time': time_processed,
                'clearance_id': f"CL-2025-{decl['id']:04d}",
                'importer': decl['shipment__customer__company_name'] or f"{decl['shipment__customer__first_name']} {decl['shipment__customer__last_name']}",
                'cargo': decl['goods_description'][:20] + '...' if decl['goods_description'] and len(decl['goods_description']) > 20 else decl['goods_description'] or 'General Cargo',
                'value': f"${decl['goods_value']:,.0f}" if decl['goods_value'] else "N/A",
                'status': decl['status'],
                'officer': f"Officer {request.user.first_name}" if request.user.first_name else "Officer Smith"
            })
        
        # Sort priority declarations by value (highest first)
        priority_declarations.sort(key=lambda x: float(x['value'].replace('$', '').replace(',', '')) if x['value'] != 'N/A' else 0, reverse=True)
        
        context = {
            "title": "Clearance Processing - Cloverics",
            "stats": {
                "in_process": in_process_count,
                "cleared_today": cleared_today_count,
                "on_hold": on_hold_count,
                "avg_clearance": "1.8 hrs"
            },
            "priority_declarations": priority_declarations[:5],  # Show top 5 priority items
            "recent_declarations": recent_declarations
        }
        
    except Exception as e:
        # Fallback context with error message
        context = {
            "title": "Clearance Processing - Cloverics",
            "error": "Unable to load clearance data. Please try again.",
            "stats": {
                "in_process": 0,
                "cleared_today": 0,
                "on_hold": 0,
                "avg_clearance": "N/A"
            },
            "priority_declarations": [],
            "recent_declarations": []
        }
    
    return render(request, "customs/clearance.html", context)


@login_required
@require_role(["CUSTOMS_AGENT"])
def print_declaration(request, declaration_id):
    """Generate printable PDF of customs declaration - EXTRACTED FROM FASTAPI"""
    try:
        declaration = get_object_or_404(
            CustomsDeclaration.objects.select_related('customer', 'shipment'), 
            id=declaration_id
        )
        
        # Generate PDF content (simplified version)
        pdf_content = f"""
CUSTOMS DECLARATION

Declaration Number: {declaration.declaration_number}
Customer: {declaration.customer.company_name if declaration.customer else 'N/A'}
Origin: {declaration.origin_country}
Destination: {declaration.destination_country}
Cargo Type: {declaration.cargo_type}
Value: ${declaration.cargo_value}
Status: {declaration.status}
Date: {declaration.submitted_at.strftime('%Y-%m-%d') if declaration.submitted_at else 'N/A'}

Goods Description: {declaration.goods_description}
        """
        
        return JsonResponse({
            "status": "success",
            "pdf_content": pdf_content,
            "download_url": f"/downloads/declaration-{declaration_id}.pdf"
        })
    
    except Exception as e:
        return JsonResponse({"status": "error", "message": "Failed to generate PDF"})


@login_required
@require_POST
@require_role(["CUSTOMS_AGENT"])
def appeal_declaration(request, declaration_id):
    """Process appeal for rejected declaration - EXTRACTED FROM FASTAPI"""
    try:
        appeal_reason = request.POST.get("appeal_reason", "No reason provided")
        
        declaration = get_object_or_404(CustomsDeclaration, id=declaration_id)
        declaration.status = "UNDER_REVIEW"
        declaration.save()
        
        # Create appeal notification for supervisor
        admin_users = User.objects.filter(user_type="ADMIN")
        for admin in admin_users:
            Notification.objects.create(
                user=admin,
                title="Declaration Appeal Submitted",
                message=f"Declaration {declaration.declaration_number} appeal: {appeal_reason}",
                notification_type="CUSTOMS",
                is_read=False
            )
        
        return JsonResponse({
            "status": "success", 
            "message": "Appeal submitted successfully. Declaration status updated to under review."
        })
    
    except Exception as e:
        return JsonResponse({"status": "error", "message": "Failed to process appeal"})


@login_required
@require_POST
@require_role(["CUSTOMS_AGENT"])
def handle_action_required(request, declaration_id):
    """Handle declarations requiring immediate action - EXTRACTED FROM FASTAPI"""
    try:
        action_type = request.POST.get("action_type", "inspection")
        priority = request.POST.get("priority", "high")
        
        declaration = get_object_or_404(CustomsDeclaration, id=declaration_id)
        
        if action_type == "inspection":
            # Schedule inspection
            declaration.status = "INSPECTION_SCHEDULED"
            declaration.save()
            
            # Notify relevant parties
            if declaration.customer:
                Notification.objects.create(
                    user=declaration.customer,
                    title="Inspection Scheduled",
                    message=f"Physical inspection scheduled for declaration {declaration.declaration_number}",
                    notification_type="CUSTOMS",
                    is_read=False
                )
        
        elif action_type == "additional_docs":
            # Request additional documentation
            declaration.status = "DOCUMENTATION_REQUIRED"
            declaration.save()
            
            if declaration.customer:
                Notification.objects.create(
                    user=declaration.customer,
                    title="Additional Documentation Required",
                    message=f"Additional documents needed for declaration {declaration.declaration_number}",
                    notification_type="CUSTOMS",
                    is_read=False
                )
        
        return JsonResponse({
            "status": "success",
            "message": f"Action {action_type} initiated for declaration {declaration.declaration_number}",
            "new_status": declaration.status
        })
    
    except Exception as e:
        return JsonResponse({"status": "error", "message": "Failed to handle action"})

        # Create customs declaration in database
        declaration = CustomsDeclaration.objects.create(
            shipment=shipment,
            declaration_number=declaration_number,
            declaration_type="IMPORT",
            goods_description=goods_description,
            goods_value=Decimal(str(goods_value)),
            country_of_origin=country_of_origin,
            tariff_codes=[tariff_code],
            status="SUBMITTED",
            customer=request.user
        )
        
        # Create notifications for customs authorities
        # Find customs authorities for origin and destination countries
        origin_customs = User.objects.filter(
            user_type="CUSTOMS_AGENT",
            company_name__icontains=shipment.origin_country
        ).first()
        
        destination_customs = User.objects.filter(
            user_type="CUSTOMS_AGENT", 
            company_name__icontains=shipment.destination_country
        ).first()
        
        # Notify origin country customs
        if origin_customs:
            Notification.objects.create(
                user=origin_customs,
                title=f"New Export Declaration: {declaration_number}",
                message=f"Export declaration submitted for shipment to {shipment.destination_country}. Value: ${goods_value:,.2f}",
                notification_type="CUSTOMS",
                is_read=False
            )
        
        # Notify destination country customs
        if destination_customs:
            Notification.objects.create(
                user=destination_customs,
                title=f"New Import Declaration: {declaration_number}",
                message=f"Import declaration submitted from {shipment.origin_country}. Value: ${goods_value:,.2f}",
                notification_type="CUSTOMS",
                is_read=False
            )
        
        messages.success(request, f"Declaration {declaration_number} submitted successfully!")
        return redirect('customers:customs_declaration')
        
    except Exception as e:
        messages.error(request, f"Error submitting declaration: {str(e)}")
        return redirect('customers:customs_declaration')


# ✅ EXTRACTED FROM FASTAPI: Customs declarations main page
# Original lines 7158-7323 in fastapi_main.py
@login_required
@require_role(["CUSTOMS_AGENT", "ADMIN"])
def customs_declarations(request):
    """View customs declarations page"""
    try:
        # Get filter parameters
        status_filter = request.GET.get('status', 'ALL')
        origin_country = request.GET.get('origin_country', '')
        date_from = request.GET.get('date_from', '')
        date_to = request.GET.get('date_to', '')
        
        # Build query filters
        declarations_query = CustomsDeclaration.objects.select_related('customer', 'shipment')
        
        if status_filter != 'ALL':
            declarations_query = declarations_query.filter(status=status_filter)
        if origin_country:
            declarations_query = declarations_query.filter(country_of_origin__icontains=origin_country)
        if date_from:
            declarations_query = declarations_query.filter(submitted_date__gte=date_from)
        if date_to:
            declarations_query = declarations_query.filter(submitted_date__lte=date_to)
        
        # Get declarations with pagination
        paginator = Paginator(declarations_query.order_by('-submitted_date'), 20)
        page_number = request.GET.get('page')
        declarations = paginator.get_page(page_number)
        
        # Calculate statistics
        all_declarations = CustomsDeclaration.objects.all()
        stats = {
            "total": all_declarations.count(),
            "pending": all_declarations.filter(status='SUBMITTED').count(),
            "approved": all_declarations.filter(status='APPROVED').count(),
            "rejected": all_declarations.filter(status='REJECTED').count(),
            "action_required": all_declarations.filter(status='ACTION_REQUIRED').count(),
            "avg_processing": "2-3 days"  # Calculated value
        }
        
        context = {
            "title": "Customs Declarations - Cloverics",
            "declarations": declarations,
            "stats": stats,
            "status_filter": status_filter,
            "origin_country": origin_country,
            "date_from": date_from,
            "date_to": date_to,
        }
        
        return render(request, "customs/declarations.html", context)
        
    except Exception as e:
        # Fallback context if database fails
        context = {
            "title": "Customs Declarations - Cloverics",
            "declarations": [],
            "stats": {
                "total": 0,
                "pending": 0,
                "approved": 0,
                "rejected": 0,
                "action_required": 0,
                "avg_processing": "N/A"
            }
        }
        return render(request, "customs/declarations.html", context)


# ✅ EXTRACTED FROM FASTAPI: Customs API endpoints for filtering declarations
# Original lines 7157-7203 in fastapi_main.py
@csrf_exempt
@require_POST
@require_role("CUSTOMS_AGENT")
def filter_customs_declarations(request):
    """Filter customs declarations based on criteria"""
    try:
        # Get filter parameters from POST data
        status_filter = request.POST.get('status', 'ALL')
        origin_country = request.POST.get('origin_country', '')
        destination_country = request.POST.get('destination_country', '')
        date_from = request.POST.get('date_from', '')
        date_to = request.POST.get('date_to', '')
        
        # Build query filters
        filters = {}
        if status_filter != 'ALL':
            filters['status'] = status_filter
        if origin_country:
            filters['country_of_origin__icontains'] = origin_country
        if destination_country:
            filters['shipment__destination_country__icontains'] = destination_country
        if date_from:
            filters['submitted_date__gte'] = date_from
        if date_to:
            filters['submitted_date__lte'] = date_to
        
        # Apply filters
        declarations = list(
            CustomsDeclaration.objects.filter(**filters).values(
                'id', 'declaration_number', 'status', 'submitted_date',
                'country_of_origin', 'shipment__destination_country'
            )[:50]  # Limit results
        )
        
        return JsonResponse({
            "success": True,
            "declarations": declarations,
            "count": len(declarations),
            "message": f"Found {len(declarations)} declarations matching criteria"
        })
    except Exception as e:
        return JsonResponse({
            "success": False,
            "message": f"Error filtering declarations: {str(e)}"
        }, status=400)


# ✅ EXTRACTED FROM FASTAPI: Create customs declaration API
# Original lines 7204-7231 in fastapi_main.py
@csrf_exempt
@require_POST
@require_role("CUSTOMS_AGENT")
def create_customs_declaration(request):
    """Create new customs declaration"""
    try:
        # Create new declaration
        declaration = CustomsDeclaration.objects.create(
            declaration_number=f"CUS-{datetime.now().strftime('%Y%m%d')}-{request.user.id:04d}",
            status='SUBMITTED',
            customer=request.user,  # Using current user for demo
            goods_description=request.POST.get('cargo_description', 'General Cargo'),
            goods_value=Decimal(str(request.POST.get('declared_value', '0'))),
            country_of_origin=request.POST.get('origin_country', 'Unknown'),
        )
        
        return JsonResponse({
            "success": True,
            "message": "Declaration created successfully",
            "declaration_id": declaration.id,
            "declaration_number": declaration.declaration_number
        })
    except Exception as e:
        return JsonResponse({
            "success": False,
            "message": f"Error creating declaration: {str(e)}"
        }, status=400)


# ✅ EXTRACTED FROM FASTAPI: View customs declaration details API
# Original lines 7232-7267 in fastapi_main.py
@require_role("CUSTOMS_AGENT")
def view_customs_declaration(request, declaration_id):
    """View customs declaration details"""
    try:
        declaration = get_object_or_404(
            CustomsDeclaration.objects.select_related('shipment'), 
            id=declaration_id
        )
        
        declaration_data = {
            "id": declaration.id,
            "declaration_number": declaration.declaration_number,
            "status": declaration.status,
            "goods_description": declaration.goods_description,
            "goods_value": float(declaration.goods_value) if declaration.goods_value else 0,
            "submitted_date": declaration.submitted_date.strftime('%Y-%m-%d %H:%M') if declaration.submitted_date else 'Unknown',
            "customer_name": declaration.customer.get_full_name() if declaration.customer else 'Unknown',
            "shipment_info": {
                "origin": declaration.shipment.origin_country if declaration.shipment else 'N/A',
                "destination": declaration.shipment.destination_country if declaration.shipment else 'N/A',
                "cargo_type": declaration.shipment.cargo_type if declaration.shipment else 'N/A'
            } if declaration.shipment else None
        }
        
        return JsonResponse({
            "success": True,
            "declaration": declaration_data
        })
    except CustomsDeclaration.DoesNotExist:
        return JsonResponse({
            "success": False,
            "message": "Declaration not found"
        }, status=404)
    except Exception as e:
        return JsonResponse({
            "success": False,
            "message": f"Error viewing declaration: {str(e)}"
        }, status=400)


# ✅ EXTRACTED FROM FASTAPI: View individual declaration details page
# Original lines 7275-7323 in fastapi_main.py
@login_required
@require_role("CUSTOMS_AGENT")
def view_declaration_page(request, declaration_id):
    """View detailed customs declaration page"""
    try:
        declaration = get_object_or_404(
            CustomsDeclaration.objects.select_related('shipment', 'customer'),
            id=declaration_id
        )
        
        # Get customer info safely  
        customer_name = "Unknown Customer"
        if declaration.customer:
            if hasattr(declaration.customer, 'company_name') and declaration.customer.company_name:
                customer_name = declaration.customer.company_name
            else:
                customer_name = f"{declaration.customer.first_name} {declaration.customer.last_name}"
        
        context = {
            "title": f"Declaration {declaration.declaration_number} - Cloverics",
            "declaration": declaration,
            "customer_name": customer_name
        }
        return render(request, "customs/declaration_details.html", context)
        
    except CustomsDeclaration.DoesNotExist:
        raise Http404("Declaration not found")
    except Exception as e:
        raise Http404("Internal server error")


# ✅ EXTRACTED FROM FASTAPI: New declaration form
# Original lines 7325-7336 in fastapi_main.py
@login_required
@require_role(["CUSTOMS_AGENT", "ADMIN"])
def new_declaration_form(request):
    """New declaration form page"""
    context = {
        "page_title": "New Customs Declaration"
    }
    return render(request, "customs/new_declaration.html", context)


# ✅ EXTRACTED FROM FASTAPI: View specific declaration details
# Original lines 7338-7357 in fastapi_main.py
@login_required
@require_role(["CUSTOMS_AGENT", "ADMIN"])
def view_declaration_details(request, declaration_id):
    """View specific declaration details"""
    try:
        declaration = get_object_or_404(CustomsDeclaration, id=declaration_id)
        
        context = {
            "declaration": declaration,
            "page_title": f"Declaration Details - {declaration.declaration_number}"
        }
        
        return render(request, "customs/declaration_details.html", context)
    
    except CustomsDeclaration.DoesNotExist:
        raise Http404("Declaration not found")


# ✅ EXTRACTED FROM FASTAPI: Print declaration
# Original lines 7359-7377 in fastapi_main.py
@login_required
@require_role(["CUSTOMS_AGENT", "ADMIN"])
def print_declaration(request, declaration_id):
    """Print declaration"""
    try:
        declaration = get_object_or_404(CustomsDeclaration, id=declaration_id)
        
        context = {
            "declaration": declaration,
            "print_mode": True
        }
        
        return render(request, "customs/print_declaration.html", context)
    
    except CustomsDeclaration.DoesNotExist:
        raise Http404("Declaration not found")


# ✅ EXTRACTED FROM FASTAPI: Declaration review API
# Original lines 7379-7408 in fastapi_main.py
@csrf_exempt
@require_POST
@require_role(["CUSTOMS_AGENT", "ADMIN"])
def review_declaration_api(request, declaration_id):
    """Review declaration via API"""
    try:
        data = json.loads(request.body)
        action = data.get('action')
        
        declaration = get_object_or_404(CustomsDeclaration, id=declaration_id)
        
        # Update declaration status based on action
        if action == 'approve':
            declaration.status = 'APPROVED'
        elif action == 'reject':
            declaration.status = 'REJECTED'
        elif action == 'request_more_info':
            declaration.status = 'ACTION_REQUIRED'
        
        declaration.save()
        
        return JsonResponse({"success": True, "message": f"Declaration {action} successfully"})
    
    except CustomsDeclaration.DoesNotExist:
        return JsonResponse({"success": False, "message": "Declaration not found"})
    except Exception as e:
        return JsonResponse({"success": False, "message": "Failed to process review"})


# ✅ EXTRACTED FROM FASTAPI: Approve declaration
# Original lines 7443-7481 in fastapi_main.py
@login_required
@require_POST
@require_role(["CUSTOMS_AGENT", "ADMIN"])
def approve_declaration(request, declaration_id):
    """Approve customs declaration"""
    try:
        declaration = get_object_or_404(CustomsDeclaration, id=declaration_id)
        declaration.status = 'APPROVED'
        declaration.reviewed_at = timezone.now()
        declaration.save()
        
        # Send notifications (simplified)
        if declaration.customer:
            Notification.objects.create(
                user=declaration.customer,
                title="Customs Declaration Approved",
                message=f"Declaration {declaration.declaration_number} has been approved",
                notification_type="CUSTOMS",
                is_read=False
            )
        
        messages.success(request, f"Declaration {declaration.declaration_number} approved successfully")
        return redirect('customs:declarations')
    
    except CustomsDeclaration.DoesNotExist:
        messages.error(request, "Declaration not found")
        return redirect('customs:declarations')
    except Exception as e:
        messages.error(request, "Failed to approve declaration")
        return redirect('customs:declarations')


# ✅ EXTRACTED FROM FASTAPI: Reject declaration
# Original lines 7484-7521 in fastapi_main.py
@login_required
@require_POST
@require_role(["CUSTOMS_AGENT", "ADMIN"])
def reject_declaration(request, declaration_id):
    """Reject customs declaration"""
    try:
        declaration = get_object_or_404(CustomsDeclaration, id=declaration_id)
        declaration.status = 'REJECTED'
        declaration.reviewed_at = timezone.now()
        declaration.save()
        
        # Send notifications (simplified)
        if declaration.customer:
            Notification.objects.create(
                user=declaration.customer,
                title="Customs Declaration Rejected",
                message=f"Declaration {declaration.declaration_number} has been rejected",
                notification_type="CUSTOMS",
                is_read=False
            )
        
        messages.success(request, f"Declaration {declaration.declaration_number} rejected")
        return redirect('customs:declarations')
    
    except CustomsDeclaration.DoesNotExist:
        messages.error(request, "Declaration not found")
        return redirect('customs:declarations')
    except Exception as e:
        messages.error(request, "Failed to reject declaration")
        return redirect('customs:declarations')


# ✅ EXTRACTED FROM FASTAPI: Bulk approve clearances
# Original lines 8023-8061 in fastapi_main.py
@csrf_exempt
@require_POST
@require_role("CUSTOMS_AGENT")
def bulk_approve_clearances(request):
    """Bulk approve multiple clearance requests"""
    try:
        declaration_ids = request.POST.get("declaration_ids", "").split(",")
        
        if not declaration_ids or declaration_ids == [""]:
            return JsonResponse({"status": "error", "message": "No declarations selected"})
        
        # Approve all selected declarations
        declarations = CustomsDeclaration.objects.filter(id__in=declaration_ids)
        
        approved_count = 0
        for declaration in declarations:
            if declaration.status in ['SUBMITTED', 'UNDER_REVIEW']:
                declaration.status = 'APPROVED'
                declaration.processed_date = timezone.now()
                declaration.save()
                approved_count += 1
        
        return JsonResponse({
            "status": "success", 
            "message": f"Successfully approved {approved_count} declarations"
        })
    except Exception as e:
        return JsonResponse({"status": "error", "message": "Failed to bulk approve declarations"})


# ✅ EXTRACTED FROM FASTAPI: Hold items
# Original lines 8062-8099 in fastapi_main.py
@csrf_exempt
@require_POST
@require_role("CUSTOMS_AGENT")
def hold_items(request):
    """Put selected items on hold"""
    try:
        declaration_ids = request.POST.get("declaration_ids", "").split(",")
        hold_reason = request.POST.get("hold_reason", "Documentation review required")
        
        if not declaration_ids or declaration_ids == [""]:
            return JsonResponse({"status": "error", "message": "No declarations selected"})
        
        declarations = CustomsDeclaration.objects.filter(id__in=declaration_ids)
        
        held_count = 0
        for declaration in declarations:
            declaration.status = 'HOLD'
            declaration.notes = f"{declaration.notes}\n\nHeld on {timezone.now().strftime('%Y-%m-%d %H:%M')}: {hold_reason}"
            declaration.save()
            held_count += 1
        
        return JsonResponse({
            "status": "success", 
            "message": f"Successfully held {held_count} declarations"
        })
    except Exception as e:
        return JsonResponse({"status": "error", "message": "Failed to hold declarations"})


# ✅ EXTRACTED FROM FASTAPI: Export clearance report
# Original lines 8100-8159 in fastapi_main.py
@login_required
@require_role("CUSTOMS_AGENT")
def export_clearance_report(request):
    """Export clearance processing report"""
    try:
        # Get all declarations for report
        declarations = CustomsDeclaration.objects.select_related('customer', 'shipment').all().values(
            'declaration_number', 'customer__company_name', 'customer__first_name',
            'customer__last_name', 'goods_description', 'goods_value', 'country_of_origin',
            'duties_amount', 'taxes_amount', 'status', 'submitted_date', 'processed_date'
        )
        
        # Create CSV content
        output = io.StringIO()
        writer = csv.writer(output)
        
        # Write header
        writer.writerow([
            'Declaration Number', 'Company/Customer', 'Goods Description', 'Value (USD)',
            'Country of Origin', 'Duties (USD)', 'Taxes (USD)', 'Status', 
            'Submitted Date', 'Processed Date'
        ])
        
        # Write data rows
        for decl in declarations:
            customer_name = decl['customer__company_name'] or f"{decl['customer__first_name']} {decl['customer__last_name']}"
            writer.writerow([
                decl['declaration_number'],
                customer_name,
                decl['goods_description'][:50] + '...' if len(decl['goods_description']) > 50 else decl['goods_description'],
                f"${decl['goods_value']:,.2f}" if decl['goods_value'] else "N/A",
                decl['country_of_origin'],
                f"${decl['duties_amount']:,.2f}" if decl['duties_amount'] else "N/A",
                f"${decl['taxes_amount']:,.2f}" if decl['taxes_amount'] else "N/A",
                decl['status'],
                decl['submitted_date'].strftime('%Y-%m-%d') if decl['submitted_date'] else "N/A",
                decl['processed_date'].strftime('%Y-%m-%d') if decl['processed_date'] else "N/A"
            ])
        
        csv_content = output.getvalue()
        output.close()
        
        # Return CSV file
        filename = f"customs_clearance_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        
        response = HttpResponse(
            csv_content,
            content_type="text/csv",
        )
        response['Content-Disposition'] = f'attachment; filename={filename}'
        return response
        
    except Exception as e:
        return JsonResponse({"status": "error", "message": "Failed to export report"})


# Placeholder views for inspections and clearance (structure for future completion)
@login_required
@require_role(["CUSTOMS_AGENT", "ADMIN"])
def customs_inspections(request):
    """Customs inspections page"""
    context = {
        "title": "Customs Inspections - Cloverics",
        "inspections": CustomsInspection.objects.all()[:10],  # Limit for demo
    }
    return render(request, "customs/inspections.html", context)


@login_required
@require_role(["CUSTOMS_AGENT", "ADMIN"])
def customs_clearance(request):
    """Customs clearance page"""
    context = {
        "title": "Customs Clearance - Cloverics",
        "clearances": CustomsClearance.objects.all()[:10],  # Limit for demo
    }
    return render(request, "customs/clearance.html", context)