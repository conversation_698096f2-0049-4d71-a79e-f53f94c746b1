from django.shortcuts import render

def dashboard(request):
    """
    Customs Agent Dashboard
    Displays customs declarations and processing statistics
    """
    # Create mock user object with proper user_type for sidebar navigation
    class MockUser:
        def __init__(self):
            self.user_type = 'CUSTOMS_AGENT'
            self.id = 2
            self.email = '<EMAIL>'
            self.company_name = 'Customs Authority'
    
    context = {
        'title': 'Customs Agent Dashboard - Cloverics',
        'user_type': 'CUSTOMS_AGENT',
        'user': <PERSON>ck<PERSON>ser(),
        'customs_stats': {
            'pending_declarations': 15,
            'processed_today': 28,
            'total_processed': 445,
            'inspection_rate': 12.5,
            'average_processing_time': '2.3 hours',
            'compliance_score': 96.8
        },
        'pending_declarations': [
            {
                'id': 'CD892H567F',
                'shipment': 'CL718F825C',
                'origin': 'Azerbaijan',
                'destination': 'Turkey',
                'priority': 'high'
            },
            {
                'id': 'CD903I678G',
                'shipment': 'CL829G934D',
                'origin': 'Germany',
                'destination': 'Turkey',
                'priority': 'normal'
            }
        ]
    }
    
    return render(request, 'customs/dashboard.html', context)