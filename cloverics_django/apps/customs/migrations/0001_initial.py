# Generated by Django 5.2.1 on 2025-08-07 12:45

import django.core.validators
import django.db.models.deletion
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('shipments', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='CustomsDeclaration',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('declaration_number', models.CharField(db_index=True, max_length=50, unique=True)),
                ('declaration_type', models.CharField(choices=[('IMPORT', 'Import'), ('EXPORT', 'Export'), ('TRANSIT', 'Transit')], default='IMPORT', max_length=20)),
                ('status', models.CharField(choices=[('PENDING', 'Pending'), ('SUBMITTED', 'Submitted'), ('UNDER_REVIEW', 'Under Review'), ('APPROVED', 'Approved'), ('REJECTED', 'Rejected'), ('HOLD', 'On Hold'), ('CLEARED', 'Cleared'), ('ACTION_REQUIRED', 'Action Required')], db_index=True, default='PENDING', max_length=20)),
                ('goods_description', models.TextField()),
                ('goods_value', models.DecimalField(decimal_places=2, max_digits=12, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('country_of_origin', models.CharField(max_length=100)),
                ('tariff_codes', models.JSONField(blank=True, default=list)),
                ('duties_amount', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('taxes_amount', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('processing_fee', models.DecimalField(blank=True, decimal_places=2, max_digits=8, null=True)),
                ('submitted_date', models.DateTimeField(auto_now_add=True)),
                ('processed_date', models.DateTimeField(blank=True, null=True)),
                ('reviewed_at', models.DateTimeField(blank=True, null=True)),
                ('notes', models.TextField(blank=True)),
                ('documents', models.JSONField(blank=True, default=list)),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='customs_declarations', to=settings.AUTH_USER_MODEL)),
                ('shipment', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='customs_declarations', to='shipments.shipment')),
            ],
            options={
                'db_table': 'customs_declarations',
                'ordering': ['-submitted_date'],
            },
        ),
        migrations.CreateModel(
            name='CustomsClearance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('clearance_number', models.CharField(max_length=50, unique=True)),
                ('status', models.CharField(choices=[('PENDING', 'Pending Clearance'), ('PROCESSING', 'Processing'), ('CLEARED', 'Cleared'), ('DETAINED', 'Detained'), ('RELEASED', 'Released')], default='PENDING', max_length=20)),
                ('total_duties', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10)),
                ('total_taxes', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10)),
                ('total_fees', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10)),
                ('payment_status', models.CharField(default='PENDING', max_length=20)),
                ('clearance_requested', models.DateTimeField(auto_now_add=True)),
                ('clearance_granted', models.DateTimeField(blank=True, null=True)),
                ('clearance_notes', models.TextField(blank=True)),
                ('customs_officer', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='processed_clearances', to=settings.AUTH_USER_MODEL)),
                ('declaration', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='clearance', to='customs.customsdeclaration')),
            ],
            options={
                'db_table': 'customs_clearances',
                'ordering': ['-clearance_requested'],
            },
        ),
        migrations.CreateModel(
            name='CustomsInspection',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('inspection_number', models.CharField(max_length=50, unique=True)),
                ('inspection_type', models.CharField(choices=[('PHYSICAL', 'Physical Inspection'), ('DOCUMENT', 'Document Review'), ('XRAY', 'X-Ray Scan'), ('RANDOM', 'Random Check')], max_length=20)),
                ('status', models.CharField(choices=[('SCHEDULED', 'Scheduled'), ('IN_PROGRESS', 'In Progress'), ('COMPLETED', 'Completed'), ('CANCELLED', 'Cancelled')], default='SCHEDULED', max_length=20)),
                ('scheduled_date', models.DateTimeField()),
                ('started_at', models.DateTimeField(blank=True, null=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('inspection_notes', models.TextField(blank=True)),
                ('findings', models.JSONField(blank=True, default=dict)),
                ('passed', models.BooleanField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('assigned_inspector', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assigned_inspections', to=settings.AUTH_USER_MODEL)),
                ('declaration', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='inspections', to='customs.customsdeclaration')),
            ],
            options={
                'db_table': 'customs_inspections',
                'ordering': ['-scheduled_date'],
            },
        ),
        migrations.AddIndex(
            model_name='customsdeclaration',
            index=models.Index(fields=['status', 'submitted_date'], name='customs_dec_status_6f9238_idx'),
        ),
        migrations.AddIndex(
            model_name='customsdeclaration',
            index=models.Index(fields=['customer', 'status'], name='customs_dec_custome_df1a69_idx'),
        ),
        migrations.AddIndex(
            model_name='customsdeclaration',
            index=models.Index(fields=['declaration_type', 'status'], name='customs_dec_declara_4a9854_idx'),
        ),
        migrations.AddIndex(
            model_name='customsclearance',
            index=models.Index(fields=['status', 'clearance_requested'], name='customs_cle_status_c1740b_idx'),
        ),
        migrations.AddIndex(
            model_name='customsclearance',
            index=models.Index(fields=['customs_officer', 'status'], name='customs_cle_customs_9950c4_idx'),
        ),
        migrations.AddIndex(
            model_name='customsinspection',
            index=models.Index(fields=['status', 'scheduled_date'], name='customs_ins_status_b5cad4_idx'),
        ),
        migrations.AddIndex(
            model_name='customsinspection',
            index=models.Index(fields=['assigned_inspector', 'status'], name='customs_ins_assigne_a5239c_idx'),
        ),
    ]
