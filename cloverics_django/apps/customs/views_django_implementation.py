"""
Django implementation of extracted customs functionality
Converting FastAPI async patterns to Django views and ViewSets
"""

from django.shortcuts import render, get_object_or_404, redirect
from django.http import JsonResponse, HttpResponseForbidden
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.utils import timezone
from django.core.paginator import Paginator
from django.db.models import Q, Count
from django.views import View
from django.views.generic import ListView, DetailView, CreateView, UpdateView
from rest_framework import viewsets, status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from payments.models import CustomsDeclaration
from django.contrib.auth import get_user_model

User = get_user_model(), Notification
from shipments.models import Shipment
from django.contrib.auth.decorators import user_passes_test

def role_required(roles):
    """Decorator to require specific user roles"""
    def check_role(user):
        return user.is_authenticated and user.user_type in roles
    return user_passes_test(check_role)

import logging
logger = logging.getLogger(__name__)


# ==================== CUSTOMS DASHBOARD VIEW ====================
@login_required
@role_required(['CUSTOMS_AGENT', 'ADMIN'])
def customs_dashboard(request):
    """
    Customs agent dashboard with country-specific filtering
    Converted from FastAPI lines 1219-1284
    """
    try:
        # Import country filtering utilities
        from utils.customs_country_filter import (
            extract_country_from_customs_email, 
            filter_declarations_for_customs
        )
        
        today = timezone.now().date()
        user = request.user
        
        # Extract country for this customs user
        customs_country = extract_country_from_customs_email(str(user.email))
        logger.info(f"Customs dashboard for {user.email} - Country: {customs_country}")
        
        # Get base queryset
        all_declarations = CustomsDeclaration.objects.all()
        
        # Apply country-specific filtering (sync version) 
        try:
            from django.apps import apps
            if apps.is_installed('utils.customs_country_filter'):
                filtered_declarations = filter_declarations_for_customs(user, all_declarations)
            else:
                filtered_declarations = all_declarations
        except:
            filtered_declarations = all_declarations
        
        # Get declarations counts by status (country-filtered)
        total_declarations = filtered_declarations.count()
        pending_declarations = filtered_declarations.filter(status='SUBMITTED').count()
        processed_today = filtered_declarations.filter(
            status='APPROVED',
            created_at__date=today
        ).count()
        approved_declarations = filtered_declarations.filter(status='APPROVED').count()
        
        # Calculate clearance rate
        clearance_rate = round((approved_declarations / max(total_declarations, 1)) * 100, 1)
        
        # Get urgent cases
        urgent_cases = filtered_declarations.filter(status='SUBMITTED').count()
        
        context = {
            'user': user,
            'pending_declarations': pending_declarations,
            'processed_today': processed_today,
            'clearance_rate': clearance_rate,
            'total_processed': total_declarations,
            'urgent_cases': urgent_cases,
            'average_processing_time': '2.1 hours',
            'customs_country': customs_country
        }
        
        return render(request, 'customs/dashboard.html', context)
        
    except Exception as e:
        logger.error(f"Error in customs dashboard: {e}")
        # Fallback context
        context = {
            'user': request.user,
            'pending_declarations': 0,
            'processed_today': 0,
            'clearance_rate': 0,
            'total_processed': 0,
            'urgent_cases': 0,
            'average_processing_time': '2.1 hours'
        }
        return render(request, 'customs/dashboard.html', context)


# ==================== CUSTOMS DECLARATIONS LIST VIEW ====================
@login_required
@role_required(['CUSTOMS_AGENT', 'ADMIN'])
def customs_declarations_list(request):
    """
    List all customs declarations with filtering
    Converted from FastAPI filtering endpoints
    """
    declarations = CustomsDeclaration.objects.select_related('customer', 'shipment').all()
    
    # Apply filters
    status_filter = request.GET.get('status')
    country_filter = request.GET.get('country')
    date_from = request.GET.get('date_from')
    
    if status_filter:
        status_map = {
            'pending': 'SUBMITTED',
            'approved': 'APPROVED', 
            'rejected': 'REJECTED',
            'action_required': 'UNDER_REVIEW'
        }
        declarations = declarations.filter(status=status_map.get(status_filter, status_filter))
    
    if country_filter:
        declarations = declarations.filter(origin_country__icontains=country_filter)
    
    if date_from:
        declarations = declarations.filter(submitted_date__gte=date_from)
    
    # Pagination
    paginator = Paginator(declarations, 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'status_filter': status_filter,
        'country_filter': country_filter,
        'date_from': date_from
    }
    
    return render(request, 'customs/declarations_list.html', context)


# ==================== DECLARATION APPROVAL/REJECTION VIEWS ====================
@login_required
@role_required(['CUSTOMS_AGENT', 'ADMIN'])
def approve_declaration(request, declaration_id):
    """
    Approve customs declaration
    Converted from FastAPI lines 7284-7323
    """
    declaration = get_object_or_404(CustomsDeclaration, id=declaration_id)
    
    try:
        declaration.status = 'APPROVED'
        declaration.reviewed_at = timezone.now()
        declaration.save()
        
        # Create notifications for customer and logistics provider
        if declaration.shipment:
            # Notify customer
            if declaration.customer:
                Notification.objects.create(
                    user=declaration.customer,
                    title="Customs Declaration Approved",
                    message=f"Your customs declaration {declaration.declaration_number} has been approved",
                    notification_type="CUSTOMS",
                    is_read=False
                )
            
            # Notify logistics provider if exists
            if hasattr(declaration.shipment, 'logistics_provider') and declaration.shipment.logistics_provider:
                Notification.objects.create(
                    user=declaration.shipment.logistics_provider,
                    title="Customs Declaration Approved",
                    message=f"Customs declaration {declaration.declaration_number} has been approved for your shipment",
                    notification_type="CUSTOMS",
                    is_read=False
                )
        
        messages.success(request, f'Declaration {declaration.declaration_number} approved successfully!')
        
    except Exception as e:
        logger.error(f"Error approving declaration: {e}")
        messages.error(request, 'Failed to approve declaration')
    
    return redirect('customs:declarations_list')


@login_required
@role_required(['CUSTOMS_AGENT', 'ADMIN'])
def reject_declaration(request, declaration_id):
    """
    Reject customs declaration
    Converted from FastAPI lines 7325-7364
    """
    declaration = get_object_or_404(CustomsDeclaration, id=declaration_id)
    
    try:
        declaration.status = 'REJECTED'
        declaration.reviewed_at = timezone.now()
        declaration.save()
        
        # Create notifications for customer and logistics provider
        if declaration.shipment:
            # Notify customer
            if declaration.customer:
                Notification.objects.create(
                    user=declaration.customer,
                    title="Customs Declaration Rejected",
                    message=f"Your customs declaration {declaration.declaration_number} has been rejected. Please review and resubmit.",
                    notification_type="CUSTOMS",
                    is_read=False
                )
        
        messages.success(request, f'Declaration {declaration.declaration_number} rejected!')
        
    except Exception as e:
        logger.error(f"Error rejecting declaration: {e}")
        messages.error(request, 'Failed to reject declaration')
    
    return redirect('customs:declarations_list')


# ==================== DECLARATION DETAIL VIEW ====================
@login_required
@role_required(['CUSTOMS_AGENT', 'ADMIN'])
def declaration_detail(request, declaration_id):
    """
    View detailed customs declaration information
    Converted from FastAPI view declaration endpoint
    """
    declaration = get_object_or_404(
        CustomsDeclaration.objects.select_related('customer', 'shipment'), 
        id=declaration_id
    )
    
    context = {
        'declaration': declaration,
        'can_approve': declaration.status in ['SUBMITTED', 'UNDER_REVIEW'],
        'can_reject': declaration.status in ['SUBMITTED', 'UNDER_REVIEW'],
    }
    
    return render(request, 'customs/declaration_detail.html', context)


# ==================== API ENDPOINTS ====================
@api_view(['POST'])
@permission_classes([IsAuthenticated])
def filter_declarations_api(request):
    """
    API endpoint for filtering declarations
    Converted from FastAPI filtering endpoint
    """
    if request.user.user_type not in ['CUSTOMS_AGENT', 'ADMIN']:
        return Response({'error': 'Access denied'}, status=status.HTTP_403_FORBIDDEN)
    
    try:
        status_filter = request.data.get('status', 'all')
        country_filter = request.data.get('country', 'all')
        date_from = request.data.get('date_from')
        
        # Build query
        query = CustomsDeclaration.objects.select_related('shipment', 'customer')
        
        if status_filter != 'all':
            status_map = {
                'pending': 'SUBMITTED',
                'approved': 'APPROVED', 
                'rejected': 'REJECTED',
                'action_required': 'UNDER_REVIEW'
            }
            query = query.filter(status=status_map.get(status_filter, status_filter))
        
        if country_filter != 'all':
            query = query.filter(origin_country=country_filter)
        
        if date_from:
            query = query.filter(submitted_date__gte=date_from)
        
        declarations = list(query.values(
            'id', 'declaration_number', 'customer__company_name', 'customer__first_name',
            'customer__last_name', 'origin_country', 'destination_country', 'cargo_type',
            'cargo_value', 'status', 'submitted_date'
        )[:50])  # Limit results
        
        return Response({
            'status': 'success',
            'count': len(declarations),
            'declarations': declarations
        })
        
    except Exception as e:
        logger.error(f"Error filtering declarations: {e}")
        return Response({
            'status': 'error',
            'message': 'Failed to filter declarations'
        }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def create_declaration_api(request):
    """
    API endpoint for creating new declarations
    Converted from FastAPI new declaration endpoint
    """
    if request.user.user_type not in ['CUSTOMS_AGENT', 'ADMIN']:
        return Response({'error': 'Access denied'}, status=status.HTTP_403_FORBIDDEN)
    
    try:
        # Get first customer for demo purposes
        first_customer = User.objects.filter(user_type='customer').first()
        
        # Create new declaration
        declaration = CustomsDeclaration.objects.create(
            declaration_number=f"CD-2025-{timezone.now().strftime('%m%d%H%M')}",
            customer=first_customer,
            origin_country=request.data.get('origin_country', 'United States'),
            destination_country=request.data.get('destination_country', 'China'),
            cargo_type=request.data.get('cargo_type', 'General Cargo'),
            cargo_value=float(request.data.get('cargo_value', '10000')),
            status='SUBMITTED',
            goods_description=request.data.get('goods_description', 'New declaration created by customs agent')
        )
        
        return Response({
            'status': 'success',
            'message': 'New declaration created successfully',
            'declaration_id': declaration.id,
            'declaration_number': declaration.declaration_number
        })
        
    except Exception as e:
        logger.error(f"Error creating declaration: {e}")
        return Response({
            'status': 'error',
            'message': 'Failed to create declaration'
        }, status=status.HTTP_400_BAD_REQUEST)


# ==================== CUSTOMS VIEWSET FOR DRF ====================
class CustomsDeclarationViewSet(viewsets.ModelViewSet):
    """
    ViewSet for customs declarations with full CRUD operations
    """
    queryset = CustomsDeclaration.objects.select_related('customer', 'shipment').all()
    serializer_class = None  # To be defined in serializers.py
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        """Filter queryset based on user permissions"""
        user = self.request.user
        if user.user_type in ['CUSTOMS_AGENT', 'ADMIN']:
            return self.queryset
        elif user.user_type == 'customer':
            return self.queryset.filter(customer=user)
        else:
            return CustomsDeclaration.objects.none()