{% extends "base.html" %}

{% block title %}Declaration Details - Cloverics{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-md-10 offset-md-1">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">
                        <i class="fas fa-file-contract me-2"></i>
                        Declaration Details - {{ declaration.declaration_number }}
                    </h4>
                    <div>
                        <button type="button" class="btn btn-outline-primary btn-sm me-2" onclick="window.print()">
                            <i class="fas fa-print me-1"></i>Print
                        </button>
                        <button type="button" class="btn btn-secondary btn-sm" onclick="history.back()">
                            <i class="fas fa-arrow-left me-1"></i>Back
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Basic Information -->
                        <div class="col-md-6">
                            <h5 class="text-primary mb-3">Basic Information</h5>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Declaration Number:</strong></td>
                                    <td>{{ declaration.declaration_number }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Status:</strong></td>
                                    <td>
                                        {% if declaration.status == 'PENDING' %}
                                            <span class="badge bg-warning">Pending</span>
                                        {% elif declaration.status == 'APPROVED' %}
                                            <span class="badge bg-success">Approved</span>
                                        {% elif declaration.status == 'REJECTED' %}
                                            <span class="badge bg-danger">Rejected</span>
                                        {% else %}
                                            <span class="badge bg-info">{{ declaration.status|title }}</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Submitted Date:</strong></td>
                                    <td>{{ declaration.submitted_date.strftime('%Y-%m-%d %H:%M') if declaration.submitted_date else 'Not submitted yet' }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Customer:</strong></td>
                                    <td>{{ declaration.customer.get_full_name() }}</td>
                                </tr>
                            </table>
                        </div>

                        <!-- Shipment Details -->
                        <div class="col-md-6">
                            <h5 class="text-primary mb-3">Shipment Details</h5>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Origin Country:</strong></td>
                                    <td>{{ declaration.origin_country }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Destination Country:</strong></td>
                                    <td>{{ declaration.destination_country }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Cargo Type:</strong></td>
                                    <td>{{ declaration.cargo_type }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Cargo Value:</strong></td>
                                    <td>${{ declaration.cargo_value }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <!-- Goods Description -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <h5 class="text-primary mb-3">Goods Description</h5>
                            <div class="bg-light p-3 rounded">
                                {{ declaration.goods_description }}
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="d-flex gap-2">
                                {% if user.user_type == 'CUSTOMS_AGENT' and declaration.status == 'PENDING' %}
                                    <button type="button" class="btn btn-success" onclick="reviewDeclaration({{ declaration.id }}, 'approve')">
                                        <i class="fas fa-check me-1"></i>Approve
                                    </button>
                                    <button type="button" class="btn btn-danger" onclick="reviewDeclaration({{ declaration.id }}, 'reject')">
                                        <i class="fas fa-times me-1"></i>Reject
                                    </button>
                                    <button type="button" class="btn btn-warning" onclick="reviewDeclaration({{ declaration.id }}, 'request_more_info')">
                                        <i class="fas fa-question-circle me-1"></i>Request More Info
                                    </button>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function reviewDeclaration(declarationId, action) {
    if (confirm('Are you sure you want to ' + action + ' this declaration?')) {
        fetch('/api/customs/declaration/' + declarationId + '/review', {
            method: 'POST',
            headers: {'Content-Type': 'application/json'},
            body: JSON.stringify({action: action})
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Declaration ' + action + ' successfully');
                location.reload();
            } else {
                alert('Error: ' + (data.message || 'Failed to ' + action));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Failed to ' + action + ' declaration');
        });
    }
}


// Auto-generated function implementations

function window.print() {
    // Generic function implementation
    console.log('window.print called');
    showAlert('Function window.print executed', 'info');
}

function history.back() {
    // Generic function implementation
    console.log('history.back called');
    showAlert('Function history.back executed', 'info');
}
</script>
{% endblock %}