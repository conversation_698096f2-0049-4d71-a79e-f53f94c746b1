{% extends "base.html" %}

{% block content %}
<div class="dashboard-container">
    <div class="dashboard-header">
        <h1>Reports</h1>
        <p>Generate and access customs processing reports and analytics</p>
    </div>

    <!-- Report Statistics - Real Database Data -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-primary">
                    <i class="fas fa-file-alt"></i>
                </div>
                <div class="stat-content">
                    <h3>{{ stats.total_processed }}</h3>
                    <p>Total Processed</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-success">
                    <i class="fas fa-dollar-sign"></i>
                </div>
                <div class="stat-content">
                    <h3>{{ stats.duties_collected }}</h3>
                    <p>Duties Collected</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="stat-content">
                    <h3>{{ stats.violations_found }}</h3>
                    <p>Violations Found</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-info">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="stat-content">
                    <h3>{{ stats.avg_processing }}</h3>
                    <p>Avg. Processing</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Report Generation -->
    <div class="card mb-4">
        <div class="card-header">
            <h5>Generate New Report</h5>
        </div>
        <div class="card-body">
            <form>
                <div class="row">
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label class="form-label">Report Type</label>
                            <select class="form-select">
                                <option>Processing Summary</option>
                                <option>Revenue Analysis</option>
                                <option>Inspection Report</option>
                                <option>Violation Summary</option>
                                <option>Performance Metrics</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label class="form-label">Date Range</label>
                            <select class="form-select">
                                <option>Last 7 Days</option>
                                <option>Last 30 Days</option>
                                <option>Last Quarter</option>
                                <option>Custom Range</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label class="form-label">Format</label>
                            <select class="form-select">
                                <option>PDF</option>
                                <option>Excel</option>
                                <option>CSV</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label class="form-label">&nbsp;</label>
                            <button type="button" class="btn btn-primary form-control" onclick="generateReport()">Generate Report</button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Recent Reports -->
    <div class="card mb-4">
        <div class="card-header">
            <h5>Recent Reports</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Report Name</th>
                            <th>Type</th>
                            <th>Generated</th>
                            <th>Period</th>
                            <th>Size</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for report in recent_reports %}
                        <tr>
                            <td>{{ report.name }}</td>
                            <td>{{ report.type }}</td>
                            <td>{{ report.generated }}</td>
                            <td>{{ report.period }}</td>
                            <td>{{ report.size }}</td>
                            <td><span class="badge bg-{{ report.status_class }}">{{ report.status }}</span></td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary" onclick="downloadReport({{ report.id }}, '{{ report.type }}')" {% if report.status != 'Ready' %}disabled{% endif %}>Download</button>
                                <button class="btn btn-sm btn-outline-info" onclick="viewReport({{ report.id }}, '{{ report.type }}')" {% if report.status != 'Ready' %}disabled{% endif %}>View</button>
                            </td>
                        </tr>
                        {% endfor %}
                        {% if not recent_reports %}
                        <tr>
                            <td colspan="7" class="text-center text-muted">No recent reports available</td>
                        </tr>
                        {% endif %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Analytics Dashboard -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5>Processing Volume Trends</h5>
                </div>
                <div class="card-body">
                    <div class="chart-placeholder bg-light border rounded" style="height: 300px; display: flex; align-items: center; justify-content: center;">
                        <div class="text-center">
                            <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                            <h6>Processing Volume Chart</h6>
                            <div class="row text-center mt-3">
                                <div class="col-2"><small>Week 1: 312</small></div>
                                <div class="col-2"><small>Week 2: 298</small></div>
                                <div class="col-2"><small>Week 3: 342</small></div>
                                <div class="col-2"><small>Week 4: 365</small></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5>Key Performance Indicators</h5>
                </div>
                <div class="card-body">
                    <div class="kpi-metrics">
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>Processing Efficiency</span>
                                <span class="text-success">{{ kpi_metrics.processing_efficiency }}%</span>
                            </div>
                            <div class="progress mt-1">
                                <div class="progress-bar bg-success" style="width: {{ kpi_metrics.processing_efficiency }}%"></div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>Compliance Rate</span>
                                <span class="text-primary">{{ kpi_metrics.compliance_rate }}%</span>
                            </div>
                            <div class="progress mt-1">
                                <div class="progress-bar bg-primary" style="width: {{ kpi_metrics.compliance_rate }}%"></div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>Revenue Target</span>
                                <span class="text-warning">{{ kpi_metrics.revenue_target }}%</span>
                            </div>
                            <div class="progress mt-1">
                                <div class="progress-bar bg-warning" style="width: {{ kpi_metrics.revenue_target }}%"></div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>Customer Satisfaction</span>
                                <span class="text-info">{{ kpi_metrics.customer_satisfaction }}%</span>
                            </div>
                            <div class="progress mt-1">
                                <div class="progress-bar bg-info" style="width: {{ kpi_metrics.customer_satisfaction }}%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Export Options -->
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>Quick Export</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button class="btn btn-outline-primary" onclick="exportTodaysActivity()">
                            <i class="fas fa-download me-2"></i>Today's Activity Report
                        </button>
                        <button class="btn btn-outline-success" onclick="exportRevenueExcel()">
                            <i class="fas fa-file-excel me-2"></i>Revenue Summary (Excel)
                        </button>
                        <button class="btn btn-outline-warning" onclick="exportCompliancePDF()">
                            <i class="fas fa-file-pdf me-2"></i>Compliance Report (PDF)
                        </button>
                        <button class="btn btn-outline-info" onclick="openPerformanceDashboard()">
                            <i class="fas fa-chart-bar me-2"></i>Performance Dashboard
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>Report Schedule</h5>
                </div>
                <div class="card-body">
                    <div class="list-group">
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <h6>Daily Processing Summary</h6>
                                <small class="text-muted">Every day at 6:00 PM</small>
                            </div>
                            <span class="badge bg-success">Active</span>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <h6>Weekly Revenue Report</h6>
                                <small class="text-muted">Every Monday at 9:00 AM</small>
                            </div>
                            <span class="badge bg-success">Active</span>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <h6>Monthly Compliance Report</h6>
                                <small class="text-muted">First day of each month</small>
                            </div>
                            <span class="badge bg-secondary">Paused</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Generate Report Function
function generateReport() {
    const reportType = document.querySelector('select[name="report_type"]')?.value || 'Processing Summary';
    const dateRange = document.querySelector('select[name="date_range"]')?.value || 'Last 7 Days';
    const format = document.querySelector('select[name="format"]')?.value || 'PDF';
    
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Generating...';
    btn.disabled = true;
    
    fetch('/api/customs/generate-report', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            reportType: reportType,
            dateRange: dateRange,
            format: format
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            Swal.fire({
                icon: 'success',
                title: 'Report Generated!',
                text: `${reportType} report has been generated successfully.`,
                timer: 2000
            });
            setTimeout(() => location.reload(), 2000);
        } else {
            Swal.fire({
                icon: 'error',
                title: 'Generation Failed',
                text: data.message || 'Failed to generate report'
            });
        }
    })
    .catch(error => {
        console.error('Error:', error);
        Swal.fire({
            icon: 'error',
            title: 'Error',
            text: 'Failed to generate report'
        });
    })
    .finally(() => {
        btn.innerHTML = originalText;
        btn.disabled = false;
    });
}

// Download Report Function
function downloadReport(reportId, reportType) {
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
    btn.disabled = true;
    
    window.open(`/api/customs/download-report/${reportId}?type=${reportType}`, '_blank');
    
    setTimeout(() => {
        btn.innerHTML = originalText;
        btn.disabled = false;
    }, 1000);
}

// View Report Function
function viewReport(reportId, reportType) {
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
    btn.disabled = true;
    
    fetch(`/api/customs/view-report/${reportId}?type=${reportType}`)
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            // Create modal to display report
            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.innerHTML = `
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Report: ${reportType}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <pre style="white-space: pre-wrap;">${data.report_content}</pre>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-primary" onclick="downloadReport(${reportId}, '${reportType}')">
                                <i class="fas fa-download me-2"></i>Download
                            </button>
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        </div>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
            new bootstrap.Modal(modal).show();
            
            // Remove modal after hide
            modal.addEventListener('hidden.bs.modal', () => {
                document.body.removeChild(modal);
            });
        } else {
            Swal.fire({
                icon: 'error',
                title: 'View Failed',
                text: data.message || 'Failed to view report'
            });
        }
    })
    .catch(error => {
        console.error('Error:', error);
        Swal.fire({
            icon: 'error',
            title: 'Error',
            text: 'Failed to view report'
        });
    })
    .finally(() => {
        btn.innerHTML = originalText;
        btn.disabled = false;
    });
}

// Quick Export Functions
function exportTodaysActivity() {
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Generating...';
    btn.disabled = true;
    
    fetch('/api/customs/export-todays-activity', {
        method: 'POST'
    })
    .then(response => {
        if (response.ok) {
            return response.blob();
        }
        throw new Error('Export failed');
    })
    .then(blob => {
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `customs-activity-${new Date().toISOString().slice(0, 10)}.pdf`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
        
        Swal.fire({
            icon: 'success',
            title: 'Export Successful!',
            text: "Today's activity report has been downloaded.",
            timer: 2000
        });
    })
    .catch(error => {
        console.error('Error:', error);
        Swal.fire({
            icon: 'error',
            title: 'Export Failed',
            text: 'Failed to generate activity report'
        });
    })
    .finally(() => {
        btn.innerHTML = originalText;
        btn.disabled = false;
    });
}

function exportRevenueExcel() {
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Generating...';
    btn.disabled = true;
    
    fetch('/api/customs/export-revenue-excel', {
        method: 'POST'
    })
    .then(response => {
        if (response.ok) {
            return response.blob();
        }
        throw new Error('Export failed');
    })
    .then(blob => {
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `customs-revenue-summary-${new Date().toISOString().slice(0, 10)}.xlsx`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
        
        Swal.fire({
            icon: 'success',
            title: 'Export Successful!',
            text: 'Revenue summary has been downloaded as Excel file.',
            timer: 2000
        });
    })
    .catch(error => {
        console.error('Error:', error);
        Swal.fire({
            icon: 'error',
            title: 'Export Failed',
            text: 'Failed to generate revenue summary'
        });
    })
    .finally(() => {
        btn.innerHTML = originalText;
        btn.disabled = false;
    });
}

function exportCompliancePDF() {
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Generating...';
    btn.disabled = true;
    
    fetch('/api/customs/export-compliance-pdf', {
        method: 'POST'
    })
    .then(response => {
        if (response.ok) {
            return response.blob();
        }
        throw new Error('Export failed');
    })
    .then(blob => {
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `customs-compliance-report-${new Date().toISOString().slice(0, 10)}.pdf`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
        
        Swal.fire({
            icon: 'success',
            title: 'Export Successful!',
            text: 'Compliance report has been downloaded as PDF.',
            timer: 2000
        });
    })
    .catch(error => {
        console.error('Error:', error);
        Swal.fire({
            icon: 'error',
            title: 'Export Failed',
            text: 'Failed to generate compliance report'
        });
    })
    .finally(() => {
        btn.innerHTML = originalText;
        btn.disabled = false;
    });
}

function openPerformanceDashboard() {
    Swal.fire({
        icon: 'info',
        title: 'Performance Dashboard',
        text: 'Opening comprehensive performance analytics dashboard...',
        timer: 1500
    });
    
    setTimeout(() => {
        window.open('/customs/performance-dashboard', '_blank');
    }, 1500);
}
</script>
{% endblock %}