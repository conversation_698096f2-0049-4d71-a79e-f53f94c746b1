{% extends "base.html" %}

{% block content %}
<div class="dashboard-container">
    <div class="dashboard-header">
        <h1>Customs Declarations</h1>
        <p>Process and manage customs declaration forms and documentation</p>
    </div>

    <!-- Declaration Statistics - Real Database Data -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-primary">
                    <i class="fas fa-file-alt"></i>
                </div>
                <div class="stat-content">
                    <h3>{{ stats.pending }}</h3>
                    <p>Pending Review</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-success">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stat-content">
                    <h3>{{ stats.approved }}</h3>
                    <p>Approved Today</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="stat-content">
                    <h3>{{ stats.action_required }}</h3>
                    <p>Require Action</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-info">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="stat-content">
                    <h3>{{ stats.avg_processing }}</h3>
                    <p>Avg. Processing</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Filter and Search -->
    <div class="card mb-4">
        <div class="card-header">
            <h5>Filter Declarations</h5>
        </div>
        <div class="card-body">
            <form id="filterForm">
                <div class="row">
                    <div class="col-md-3">
                        <select class="form-select" id="statusFilter" name="status">
                            <option value="all">All Statuses</option>
                            <option value="pending">Pending Review</option>
                            <option value="approved">Approved</option>
                            <option value="rejected">Rejected</option>
                            <option value="action_required">Requires Action</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="countryFilter" name="country">
                            <option value="all">All Countries</option>
                            <option value="United States">United States</option>
                            <option value="China">China</option>
                            <option value="Germany">Germany</option>
                            <option value="United Kingdom">United Kingdom</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <input type="date" class="form-control" id="dateFilter" name="date_from" placeholder="From Date">
                    </div>
                    <div class="col-md-3">
                        <button type="button" class="btn btn-primary" onclick="applyFilters()">Apply Filters</button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Declarations List with Real Database Data -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5>Recent Declarations</h5>
            <button class="btn btn-primary" onclick="createNewDeclaration()">New Declaration</button>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped" id="declarationsTable">
                    <thead>
                        <tr>
                            <th>Declaration ID</th>
                            <th>Importer/Exporter</th>
                            <th>Origin</th>
                            <th>Destination</th>
                            <th>Cargo Type</th>
                            <th>Value</th>
                            <th>Status</th>
                            <th>Date</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% if declarations %}
                            {% for declaration in declarations %}
                            <tr>
                                <td><strong>{{ declaration.declaration_id }}</strong></td>
                                <td>{{ declaration.company_name }}</td>
                                <td>{{ declaration.origin }}</td>
                                <td>{{ declaration.destination }}</td>
                                <td>{{ declaration.cargo_type }}</td>
                                <td>{{ declaration.cargo_value }}</td>
                                <td>
                                    {% if declaration.status == 'SUBMITTED' %}
                                        <span class="badge bg-warning">Pending Review</span>
                                    {% elif declaration.status == 'APPROVED' %}
                                        <span class="badge bg-success">Approved</span>
                                    {% elif declaration.status == 'REJECTED' %}
                                        <span class="badge bg-danger">Rejected</span>
                                    {% elif declaration.status == 'UNDER_REVIEW' %}
                                        <span class="badge bg-info">Under Review</span>
                                    {% else %}
                                        <span class="badge bg-secondary">{{ declaration.status }}</span>
                                    {% endif %}
                                </td>
                                <td>{{ declaration.submitted_date }}</td>
                                <td>
                                    {% if declaration.status == 'SUBMITTED' %}
                                        <button class="btn btn-sm btn-outline-primary" onclick="reviewDeclaration({{ declaration.id }})">Review</button>
                                    {% elif declaration.status == 'UNDER_REVIEW' %}
                                        <button class="btn btn-sm btn-outline-warning" onclick="actionRequired({{ declaration.id }})">Action Required</button>
                                    {% elif declaration.status == 'REJECTED' %}
                                        <button class="btn btn-sm btn-outline-warning" onclick="appealDeclaration({{ declaration.id }})">Appeal</button>
                                    {% endif %}
                                    <button class="btn btn-sm btn-outline-info" onclick="viewDeclaration({{ declaration.id }})">View</button>
                                    <button class="btn btn-sm btn-outline-secondary" onclick="printDeclaration({{ declaration.id }})">Print</button>
                                </td>
                            </tr>
                            {% endfor %}
                        {% else %}
                            <tr>
                                <td colspan="9" class="text-center">No customs declarations found</td>
                            </tr>
                        {% endif %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
// Apply filters function
async function applyFilters() {
    const form = document.getElementById('filterForm');
    const formData = new FormData(form);
    
    try {
        const response = await fetch('/api/customs/declaration/filter', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        if (result.success) {
            Swal.fire({
                title: 'Filters Applied',
                text: result.message,
                icon: 'success',
                timer: 2000
            });
            
            // Reload page to show filtered results
            setTimeout(() => location.reload(), 1000);
        } else {
            Swal.fire('Error', result.message || 'Failed to apply filters', 'error');
        }
    } catch (error) {
        console.error('Filter error:', error);
        Swal.fire('Error', 'Failed to apply filters', 'error');
    }
}

// Create new declaration function
async function createNewDeclaration() {
    const { value: formValues } = await Swal.fire({
        title: 'Create New Declaration',
        html: '<div class="mb-3">' +
            '<label for="origin_country" class="form-label">Origin Country</label>' +
            '<select id="origin_country" name="origin_country" class="form-select">' +
                '<option value="United States">United States</option>' +
                '<option value="China">China</option>' +
                '<option value="Germany">Germany</option>' +
                '<option value="United Kingdom">United Kingdom</option>' +
            '</select>' +
            '</div>' +
            '<div class="mb-3">' +
            '<label for="destination_country" class="form-label">Destination Country</label>' +
            '<select id="destination_country" name="destination_country" class="form-select">' +
                '<option value="United States">United States</option>' +
                '<option value="China">China</option>' +
                '<option value="Germany">Germany</option>' +
                '<option value="United Kingdom">United Kingdom</option>' +
            '</select>' +
            '</div>' +
            '<div class="mb-3">' +
            '<label for="cargo_type" class="form-label">Cargo Type</label>' +
            '<input type="text" id="cargo_type" name="cargo_type" class="form-control" placeholder="e.g., Electronics">' +
            '</div>' +
            '<div class="mb-3">' +
            '<label for="cargo_value" class="form-label">Cargo Value ($)</label>' +
            '<input type="number" id="cargo_value" name="cargo_value" class="form-control" placeholder="10000">' +
            '</div>' +
            '<div class="mb-3">' +
            '<label for="goods_description" class="form-label">Goods Description</label>' +
            '<textarea id="goods_description" name="goods_description" class="form-control" rows="3" placeholder="Detailed description of goods"></textarea>' +
            '</div>',
        focusConfirm: false,
        preConfirm: () => {
            return {
                origin_country: document.getElementById('origin_country').value,
                destination_country: document.getElementById('destination_country').value,
                cargo_type: document.getElementById('cargo_type').value,
                cargo_value: document.getElementById('cargo_value').value,
                goods_description: document.getElementById('goods_description').value
            };
        }
    });

    if (formValues) {
        try {
            const formData = new FormData();
            Object.keys(formValues).forEach(key => {
                formData.append(key, formValues[key]);
            });

            const response = await fetch('/api/customs/declaration/create', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();
            
            if (result.success) {
                Swal.fire({
                    title: 'Declaration Created',
                    text: 'New declaration ' + result.declaration_number + ' created successfully',
                    icon: 'success'
                }).then(() => location.reload());
            } else {
                Swal.fire('Error', result.message || 'Failed to create declaration', 'error');
            }
        } catch (error) {
            console.error('Create declaration error:', error);
            Swal.fire('Error', 'Failed to create declaration', 'error');
        }
    }
}

// Review declaration function
async function reviewDeclaration(declarationId) {
    const { value: action } = await Swal.fire({
        title: 'Review Declaration',
        text: 'Choose an action for this declaration:',
        icon: 'question',
        showCancelButton: true,
        showDenyButton: true,
        confirmButtonText: 'Approve',
        denyButtonText: 'Reject',
        cancelButtonText: 'Request Info',
        confirmButtonColor: '#28a745',
        denyButtonColor: '#dc3545',
        cancelButtonColor: '#ffc107'
    });

    if (action !== undefined) {
        let actionType = action === true ? 'approve' : (action === false ? 'reject' : 'request_info');
        
        try {
            const formData = new FormData();
            formData.append('action', actionType);

            const response = await fetch('/api/customs/declaration/review/' + declarationId, {
                method: 'POST',
                body: formData
            });

            const result = await response.json();
            
            if (result.success) {
                Swal.fire({
                    title: 'Success',
                    text: result.message,
                    icon: 'success'
                }).then(() => location.reload());
            } else {
                Swal.fire('Error', result.message || 'Failed to review declaration', 'error');
            }
        } catch (error) {
            console.error('Review error:', error);
            Swal.fire('Error', 'Failed to review declaration', 'error');
        }
    }
}

// View declaration function
async function viewDeclaration(declarationId) {
    try {
        const response = await fetch('/api/customs/declaration/view/' + declarationId);
        const result = await response.json();
        
        if (result.success) {
            const declaration = result.declaration;
            
            Swal.fire({
                title: 'Declaration Details - ' + declaration.declaration_number,
                html: '<div class="text-start">' +
                    '<p><strong>Customer:</strong> ' + declaration.customer_name + '</p>' +
                    '<p><strong>Company:</strong> ' + declaration.company_name + '</p>' +
                    '<p><strong>Origin:</strong> ' + declaration.origin_country + '</p>' +
                    '<p><strong>Destination:</strong> ' + declaration.destination_country + '</p>' +
                    '<p><strong>Cargo Type:</strong> ' + declaration.cargo_type + '</p>' +
                    '<p><strong>Value:</strong> $' + declaration.cargo_value + '</p>' +
                    '<p><strong>Status:</strong> ' + declaration.status + '</p>' +
                    '<p><strong>Submitted:</strong> ' + declaration.submitted_at + '</p>' +
                    '<p><strong>Description:</strong> ' + declaration.goods_description + '</p>' +
                    '</div>',
                width: '600px'
            });
        } else {
            Swal.fire('Error', result.message || 'Declaration not found', 'error');
        }
    } catch (error) {
        console.error('View error:', error);
        Swal.fire('Error', 'Failed to load declaration details', 'error');
    }
}

// Print declaration function
async function printDeclaration(declarationId) {
    try {
        const response = await fetch('/api/customs/declaration/print/' + declarationId);
        const result = await response.json();
        
        if (result.success) {
            // Create a new window and print the PDF content
            const printWindow = window.open('', '_blank');
            printWindow.document.write(
                '<html>' +
                    '<head><title>Declaration Print</title></head>' +
                    '<body>' +
                        '<pre>' + result.pdf_content + '</pre>' +
                        '<script>window.print(); window.close();</script>' +
                    '</body>' +
                '</html>'
            );
            
            Swal.fire({
                title: 'Print Ready',
                text: 'Declaration document opened in new window for printing',
                icon: 'success',
                timer: 2000
            });
        } else {
            Swal.fire('Error', result.message || 'Failed to generate print document', 'error');
        }
    } catch (error) {
        console.error('Print error:', error);
        Swal.fire('Error', 'Failed to print declaration', 'error');
    }
}

// Appeal declaration function
async function appealDeclaration(declarationId) {
    const { value: appealReason } = await Swal.fire({
        title: 'Appeal Declaration',
        input: 'textarea',
        inputLabel: 'Appeal Reason',
        inputPlaceholder: 'Please provide the reason for this appeal...',
        inputAttributes: {
            'aria-label': 'Appeal reason'
        },
        showCancelButton: true
    });

    if (appealReason) {
        try {
            const formData = new FormData();
            formData.append('appeal_reason', appealReason);

            const response = await fetch('/api/customs/declaration/appeal/' + declarationId, {
                method: 'POST',
                body: formData
            });

            const result = await response.json();
            
            if (result.success) {
                Swal.fire({
                    title: 'Appeal Submitted',
                    text: result.message,
                    icon: 'success'
                }).then(() => location.reload());
            } else {
                Swal.fire('Error', result.message || 'Failed to submit appeal', 'error');
            }
        } catch (error) {
            console.error('Appeal error:', error);
            Swal.fire('Error', 'Failed to submit appeal', 'error');
        }
    }
}

// Action required function
async function actionRequired(declarationId) {
    const { value: actionType } = await Swal.fire({
        title: 'Action Required',
        input: 'select',
        inputOptions: {
            'inspection': 'Schedule Physical Inspection',
            'additional_docs': 'Request Additional Documentation'
        },
        inputPlaceholder: 'Select action type',
        showCancelButton: true
    });

    if (actionType) {
        try {
            const formData = new FormData();
            formData.append('action_type', actionType);
            formData.append('priority', 'high');

            const response = await fetch('/api/customs/declaration/action-required/' + declarationId, {
                method: 'POST',
                body: formData
            });

            const result = await response.json();
            
            if (result.success) {
                Swal.fire({
                    title: 'Action Initiated',
                    text: result.message,
                    icon: 'success'
                }).then(() => location.reload());
            } else {
                Swal.fire('Error', result.message || 'Failed to initiate action', 'error');
            }
        } catch (error) {
            console.error('Action required error:', error);
            Swal.fire('Error', 'Failed to initiate action', 'error');
        }
    }
}

<script>
// Clean JavaScript implementation for Customs Declarations
console.log('Loading customs declarations page...');

// Apply filters function
function applyFilters() {
    console.log('Applying filters...');
    
    const form = document.getElementById('filter-form');
    if (form) {
        const formData = new FormData(form);
        const params = new URLSearchParams();
        
        for (let [key, value] of formData.entries()) {
            if (value) params.set(key, value);
        }
        
        window.location.search = params.toString();
    }
}

// Create new declaration function
function createNewDeclaration() {
    console.log('Creating new declaration...');
    window.location.href = '/customs/declaration/new';
}

// View declaration function
function viewDeclaration(declarationId) {
    console.log('Viewing declaration:', declarationId);
    window.location.href = '/customs/declaration/' + declarationId + '/view';
}

// Print declaration function
function printDeclaration(declarationId) {
    console.log('Printing declaration:', declarationId);
    window.open('/customs/declaration/' + declarationId + '/print', '_blank');
}

// Review declaration function
function reviewDeclaration(declarationId, action) {
    if (confirm('Are you sure you want to ' + action + ' this declaration?')) {
        fetch('/api/customs/declaration/' + declarationId + '/review', {
            method: 'POST',
            headers: {'Content-Type': 'application/json'},
            body: JSON.stringify({action: action})
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Declaration ' + action + ' successfully');
                location.reload();
            } else {
                alert('Error: ' + (data.message || 'Failed to ' + action));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Failed to ' + action + ' declaration');
        });
    }
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    console.log('Customs declarations page initialized');
});
</script>

{% endblock %}