{% extends "base.html" %}

{% block content %}
<div class="dashboard-container">
    <div class="dashboard-header">
        <h1>Inspections</h1>
        <p>Manage cargo inspections and physical examination procedures</p>
    </div>

    <!-- Inspection Statistics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-primary">
                    <i class="fas fa-search"></i>
                </div>
                <div class="stat-content">
                    <h3>{{ stats.scheduled }}</h3>
                    <p>Scheduled</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-success">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stat-content">
                    <h3>{{ stats.completed_today }}</h3>
                    <p>Completed Today</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="stat-content">
                    <h3>{{ stats.issues_found }}</h3>
                    <p>Issues Found</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-info">
                    <i class="fas fa-percentage"></i>
                </div>
                <div class="stat-content">
                    <h3>{{ stats.inspection_rate }}</h3>
                    <p>Inspection Rate</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Scheduled Inspections -->
    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5>Scheduled Inspections</h5>
            <button class="btn btn-primary" onclick="scheduleInspection()">Schedule Inspection</button>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Inspection ID</th>
                            <th>Shipment</th>
                            <th>Importer</th>
                            <th>Cargo Type</th>
                            <th>Risk Level</th>
                            <th>Scheduled Time</th>
                            <th>Inspector</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for inspection in scheduled_inspections %}
                        <tr>
                            <td>{{ inspection.inspection_id }}</td>
                            <td>{{ inspection.shipment_id }}</td>
                            <td>{{ inspection.importer }}</td>
                            <td>{{ inspection.cargo_type }}</td>
                            <td><span class="badge bg-{{ inspection.risk_class }}">{{ inspection.risk_level }}</span></td>
                            <td>{{ inspection.scheduled_time }}</td>
                            <td>{{ inspection.inspector }}</td>
                            <td><span class="badge bg-{{ inspection.status_class }}">{{ inspection.status }}</span></td>
                            <td>
                                {% if inspection.status == 'Pending' %}
                                    <button class="btn btn-sm btn-success" onclick="startInspection({{ inspection.id }})">Start</button>
                                {% else %}
                                    <button class="btn btn-sm btn-outline-info" onclick="viewInspection({{ inspection.id }})">View</button>
                                {% endif %}
                                <button class="btn btn-sm btn-outline-primary" onclick="rescheduleInspection({{ inspection.id }})">Reschedule</button>
                            </td>
                        </tr>
                        {% endfor %}
                        {% if not scheduled_inspections %}
                        <tr>
                            <td colspan="9" class="text-center text-muted">No scheduled inspections at this time</td>
                        </tr>
                        {% endif %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Inspection Types -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>Inspection Categories</h5>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <h6>Physical Examination</h6>
                                <small class="text-muted">Full cargo inspection</small>
                            </div>
                            <span class="badge bg-primary">{{ inspection_categories.physical_exams }}</span>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <h6>Document Review</h6>
                                <small class="text-muted">Paperwork verification</small>
                            </div>
                            <span class="badge bg-info">{{ inspection_categories.document_reviews }}</span>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <h6>X-Ray Scanning</h6>
                                <small class="text-muted">Non-invasive imaging</small>
                            </div>
                            <span class="badge bg-success">{{ inspection_categories.xray_scans }}</span>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <h6>Sample Testing</h6>
                                <small class="text-muted">Laboratory analysis</small>
                            </div>
                            <span class="badge bg-warning">{{ inspection_categories.sample_tests }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>Inspection Results</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <div class="text-center mb-3">
                                <h4 class="text-success">{{ inspection_results.passed_percentage }}</h4>
                                <small>Passed Inspections</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center mb-3">
                                <h4 class="text-warning">{{ inspection_results.minor_percentage }}</h4>
                                <small>Minor Issues</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center mb-3">
                                <h4 class="text-danger">{{ inspection_results.major_percentage }}</h4>
                                <small>Major Violations</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center mb-3">
                                <h4 class="text-info">{{ inspection_results.seized_percentage }}</h4>
                                <small>Seized Items</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Inspection Activity -->
    <div class="card">
        <div class="card-header">
            <h5>Recent Inspection Activity</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Time</th>
                            <th>Inspection ID</th>
                            <th>Type</th>
                            <th>Cargo</th>
                            <th>Inspector</th>
                            <th>Duration</th>
                            <th>Result</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for inspection in recent_inspections %}
                        <tr>
                            <td>{{ inspection.time }}</td>
                            <td>{{ inspection.inspection_id }}</td>
                            <td>{{ inspection.type }}</td>
                            <td>{{ inspection.cargo }}</td>
                            <td>{{ inspection.inspector }}</td>
                            <td>{{ inspection.duration }}</td>
                            <td><span class="badge bg-{{ inspection.result_class }}">{{ inspection.result }}</span></td>
                            <td>
                                {% if inspection.result == 'Minor Issue' or inspection.result == 'Major Issue' %}
                                    <button class="btn btn-sm btn-outline-warning" onclick="reviewInspection({{ inspection.id }})">Review</button>
                                {% endif %}
                                <button class="btn btn-sm btn-outline-info" onclick="viewInspectionReport({{ inspection.id }})">View Report</button>
                            </td>
                        </tr>
                        {% endfor %}
                        {% if not recent_inspections %}
                        <tr>
                            <td colspan="8" class="text-center text-muted">No recent inspection activity</td>
                        </tr>
                        {% endif %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
// Schedule Inspection Function
function scheduleInspection() {
    Swal.fire({
        title: 'Schedule New Inspection',
        html: `
            <div class="text-start">
                <div class="mb-3">
                    <label class="form-label">Shipment/Declaration ID:</label>
                    <input type="text" class="form-control" id="shipmentId" placeholder="Enter ID">
                </div>
                <div class="mb-3">
                    <label class="form-label">Inspection Type:</label>
                    <select class="form-control" id="inspectionType">
                        <option value="Physical Exam">Physical Examination</option>
                        <option value="Document Review">Document Review</option>
                        <option value="X-Ray Scan">X-Ray Scanning</option>
                        <option value="Sample Test">Sample Testing</option>
                    </select>
                </div>
                <div class="mb-3">
                    <label class="form-label">Scheduled Date/Time:</label>
                    <input type="datetime-local" class="form-control" id="scheduledTime">
                </div>
                <div class="mb-3">
                    <label class="form-label">Priority Level:</label>
                    <select class="form-control" id="priorityLevel">
                        <option value="Low">Low</option>
                        <option value="Medium">Medium</option>
                        <option value="High">High</option>
                    </select>
                </div>
            </div>
        `,
        showCancelButton: true,
        confirmButtonText: 'Schedule Inspection',
        cancelButtonText: 'Cancel',
        preConfirm: () => {
            const shipmentId = document.getElementById('shipmentId').value;
            const inspectionType = document.getElementById('inspectionType').value;
            const scheduledTime = document.getElementById('scheduledTime').value;
            const priorityLevel = document.getElementById('priorityLevel').value;
            
            if (!shipmentId || !scheduledTime) {
                Swal.showValidationMessage('Please fill in all required fields');
                return false;
            }
            
            return {
                shipmentId: shipmentId,
                inspectionType: inspectionType,
                scheduledTime: scheduledTime,
                priorityLevel: priorityLevel
            };
        }
    }).then((result) => {
        if (result.isConfirmed) {
            // Send request to backend
            fetch('/api/customs/schedule-inspection', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(result.value)
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    Swal.fire('Success!', 'Inspection scheduled successfully', 'success')
                        .then(() => location.reload());
                } else {
                    Swal.fire('Error!', data.message || 'Failed to schedule inspection', 'error');
                }
            })
            .catch(error => {
                Swal.fire('Error!', 'Network error occurred', 'error');
            });
        }
    });
}

// Start Inspection Function
function startInspection(inspectionId) {
    Swal.fire({
        title: 'Start Inspection',
        text: `Are you ready to begin inspection INS-2025-${inspectionId.toString().padStart(4, '0')}?`,
        icon: 'question',
        showCancelButton: true,
        confirmButtonText: 'Start Inspection',
        cancelButtonText: 'Cancel'
    }).then((result) => {
        if (result.isConfirmed) {
            fetch(`/api/customs/start-inspection/${inspectionId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    Swal.fire('Started!', 'Inspection has been started', 'success')
                        .then(() => location.reload());
                } else {
                    Swal.fire('Error!', data.message || 'Failed to start inspection', 'error');
                }
            })
            .catch(error => {
                Swal.fire('Error!', 'Network error occurred', 'error');
            });
        }
    });
}

// Reschedule Inspection Function
function rescheduleInspection(inspectionId) {
    Swal.fire({
        title: 'Reschedule Inspection',
        html: `
            <div class="text-start">
                <div class="mb-3">
                    <label class="form-label">New Date/Time:</label>
                    <input type="datetime-local" class="form-control" id="newScheduledTime">
                </div>
                <div class="mb-3">
                    <label class="form-label">Reason for Reschedule:</label>
                    <textarea class="form-control" id="rescheduleReason" rows="3" placeholder="Enter reason..."></textarea>
                </div>
            </div>
        `,
        showCancelButton: true,
        confirmButtonText: 'Reschedule',
        cancelButtonText: 'Cancel',
        preConfirm: () => {
            const newTime = document.getElementById('newScheduledTime').value;
            const reason = document.getElementById('rescheduleReason').value;
            
            if (!newTime || !reason) {
                Swal.showValidationMessage('Please fill in all fields');
                return false;
            }
            
            return { newTime: newTime, reason: reason };
        }
    }).then((result) => {
        if (result.isConfirmed) {
            fetch(`/api/customs/reschedule-inspection/${inspectionId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(result.value)
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    Swal.fire('Rescheduled!', 'Inspection has been rescheduled', 'success')
                        .then(() => location.reload());
                } else {
                    Swal.fire('Error!', data.message || 'Failed to reschedule inspection', 'error');
                }
            })
            .catch(error => {
                Swal.fire('Error!', 'Network error occurred', 'error');
            });
        }
    });
}

// View Inspection Function
function viewInspection(inspectionId) {
    fetch(`/api/customs/inspection-details/${inspectionId}`)
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                const inspection = data.inspection;
                Swal.fire({
                    title: 'Inspection Details',
                    html: `
                        <div class="text-start">
                            <p><strong>Inspection ID:</strong> ${inspection.inspection_id}</p>
                            <p><strong>Shipment:</strong> ${inspection.shipment_id}</p>
                            <p><strong>Importer:</strong> ${inspection.importer}</p>
                            <p><strong>Cargo Type:</strong> ${inspection.cargo_type}</p>
                            <p><strong>Risk Level:</strong> <span class="badge bg-${inspection.risk_class}">${inspection.risk_level}</span></p>
                            <p><strong>Scheduled Time:</strong> ${inspection.scheduled_time}</p>
                            <p><strong>Inspector:</strong> ${inspection.inspector}</p>
                            <p><strong>Status:</strong> <span class="badge bg-${inspection.status_class}">${inspection.status}</span></p>
                        </div>
                    `,
                    icon: 'info',
                    confirmButtonText: 'Close'
                });
            } else {
                Swal.fire('Error!', 'Failed to load inspection details', 'error');
            }
        })
        .catch(error => {
            Swal.fire('Error!', 'Network error occurred', 'error');
        });
}

// View Inspection Report Function
function viewInspectionReport(inspectionId) {
    fetch(`/api/customs/inspection-report/${inspectionId}`)
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                const report = data.report;
                Swal.fire({
                    title: 'Inspection Report',
                    html: `
                        <div class="text-start">
                            <p><strong>Inspection ID:</strong> ${report.inspection_id}</p>
                            <p><strong>Type:</strong> ${report.type}</p>
                            <p><strong>Result:</strong> <span class="badge bg-${report.result_class}">${report.result}</span></p>
                            <p><strong>Duration:</strong> ${report.duration}</p>
                            <p><strong>Inspector:</strong> ${report.inspector}</p>
                            <p><strong>Date Completed:</strong> ${report.completed_date}</p>
                            <hr>
                            <p><strong>Findings:</strong></p>
                            <p>${report.findings}</p>
                            ${report.recommendations ? `<p><strong>Recommendations:</strong></p><p>${report.recommendations}</p>` : ''}
                        </div>
                    `,
                    icon: 'info',
                    confirmButtonText: 'Close',
                    showCancelButton: true,
                    cancelButtonText: 'Download PDF'
                }).then((result) => {
                    if (result.dismiss === Swal.DismissReason.cancel) {
                        // Download PDF report
                        window.open(`/api/customs/inspection-report/${inspectionId}/pdf`, '_blank');
                    }
                });
            } else {
                Swal.fire('Error!', 'Failed to load inspection report', 'error');
            }
        })
        .catch(error => {
            Swal.fire('Error!', 'Network error occurred', 'error');
        });
}

// Review Inspection Function
function reviewInspection(inspectionId) {
    Swal.fire({
        title: 'Review Inspection Issues',
        html: `
            <div class="text-start">
                <div class="mb-3">
                    <label class="form-label">Review Action:</label>
                    <select class="form-control" id="reviewAction">
                        <option value="approve">Approve with Conditions</option>
                        <option value="hold">Continue Hold</option>
                        <option value="escalate">Escalate to Supervisor</option>
                        <option value="reject">Reject Shipment</option>
                    </select>
                </div>
                <div class="mb-3">
                    <label class="form-label">Review Notes:</label>
                    <textarea class="form-control" id="reviewNotes" rows="4" placeholder="Enter detailed review notes..."></textarea>
                </div>
                <div class="mb-3">
                    <label class="form-label">Follow-up Required:</label>
                    <select class="form-control" id="followupRequired">
                        <option value="none">No Follow-up</option>
                        <option value="reinspection">Schedule Re-inspection</option>
                        <option value="documentation">Request Additional Documentation</option>
                        <option value="compliance">Compliance Verification</option>
                    </select>
                </div>
            </div>
        `,
        showCancelButton: true,
        confirmButtonText: 'Submit Review',
        cancelButtonText: 'Cancel',
        preConfirm: () => {
            const action = document.getElementById('reviewAction').value;
            const notes = document.getElementById('reviewNotes').value;
            const followup = document.getElementById('followupRequired').value;
            
            if (!notes) {
                Swal.showValidationMessage('Please enter review notes');
                return false;
            }
            
            return { action: action, notes: notes, followup: followup };
        }
    }).then((result) => {
        if (result.isConfirmed) {
            fetch(`/api/customs/review-inspection/${inspectionId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(result.value)
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    Swal.fire('Reviewed!', 'Inspection review completed', 'success')
                        .then(() => location.reload());
                } else {
                    Swal.fire('Error!', data.message || 'Failed to submit review', 'error');
                }
            })
            .catch(error => {
                Swal.fire('Error!', 'Network error occurred', 'error');
            });
        }
    });
}
</script>
{% endblock %}