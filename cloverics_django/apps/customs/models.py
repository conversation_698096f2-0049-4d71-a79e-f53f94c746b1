from django.db import models
from django.conf import settings
from django.core.validators import MinValueValidator
from decimal import Decimal
from django.utils import timezone


class CustomsDeclaration(models.Model):
    """Model for customs declarations - extracted from FastAPI"""
    
    STATUS_CHOICES = [
        ('PENDING', 'Pending'),
        ('SUBMITTED', 'Submitted'),
        ('UNDER_REVIEW', 'Under Review'),
        ('APPROVED', 'Approved'),
        ('REJECTED', 'Rejected'),
        ('HOLD', 'On Hold'),
        ('CLEARED', 'Cleared'),
        ('ACTION_REQUIRED', 'Action Required'),
    ]
    
    DECLARATION_TYPE_CHOICES = [
        ('IMPORT', 'Import'),
        ('EXPORT', 'Export'),
        ('TRANSIT', 'Transit'),
    ]
    
    # Core fields
    declaration_number = models.CharField(max_length=50, unique=True, db_index=True)
    declaration_type = models.Cha<PERSON><PERSON><PERSON>(max_length=20, choices=DECLARATION_TYPE_CHOICES, default='IMPORT')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='PENDING', db_index=True)
    
    # Related objects
    customer = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='customs_declarations')
    shipment = models.ForeignKey('shipments.Shipment', on_delete=models.CASCADE, null=True, blank=True, related_name='customs_declarations')
    
    # Goods information
    goods_description = models.TextField()
    goods_value = models.DecimalField(max_digits=12, decimal_places=2, validators=[MinValueValidator(Decimal('0.01'))])
    country_of_origin = models.CharField(max_length=100)
    tariff_codes = models.JSONField(default=list, blank=True)
    
    # Customs processing
    duties_amount = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    taxes_amount = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    processing_fee = models.DecimalField(max_digits=8, decimal_places=2, null=True, blank=True)
    
    # Dates
    submitted_date = models.DateTimeField(auto_now_add=True)
    processed_date = models.DateTimeField(null=True, blank=True)
    reviewed_at = models.DateTimeField(null=True, blank=True)
    
    # Additional fields
    notes = models.TextField(blank=True)
    documents = models.JSONField(default=list, blank=True)
    
    class Meta:
        db_table = 'customs_declarations'
        ordering = ['-submitted_date']
        indexes = [
            models.Index(fields=['status', 'submitted_date']),
            models.Index(fields=['customer', 'status']),
            models.Index(fields=['declaration_type', 'status']),
        ]
    
    def __str__(self):
        return f"Declaration {self.declaration_number} - {self.status}"


class CustomsInspection(models.Model):
    """Model for customs inspections - extracted from FastAPI"""
    
    INSPECTION_STATUS_CHOICES = [
        ('SCHEDULED', 'Scheduled'),
        ('IN_PROGRESS', 'In Progress'),
        ('COMPLETED', 'Completed'),
        ('CANCELLED', 'Cancelled'),
    ]
    
    INSPECTION_TYPE_CHOICES = [
        ('PHYSICAL', 'Physical Inspection'),
        ('DOCUMENT', 'Document Review'),
        ('XRAY', 'X-Ray Scan'),
        ('RANDOM', 'Random Check'),
    ]
    
    # Core fields
    inspection_number = models.CharField(max_length=50, unique=True)
    declaration = models.ForeignKey(CustomsDeclaration, on_delete=models.CASCADE, related_name='inspections')
    inspection_type = models.CharField(max_length=20, choices=INSPECTION_TYPE_CHOICES)
    status = models.CharField(max_length=20, choices=INSPECTION_STATUS_CHOICES, default='SCHEDULED')
    
    # Inspector assignment
    assigned_inspector = models.ForeignKey(
        settings.AUTH_USER_MODEL, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True,
        related_name='assigned_inspections'
    )
    
    # Scheduling
    scheduled_date = models.DateTimeField()
    started_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    
    # Results
    inspection_notes = models.TextField(blank=True)
    findings = models.JSONField(default=dict, blank=True)
    passed = models.BooleanField(null=True, blank=True)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'customs_inspections'
        ordering = ['-scheduled_date']
        indexes = [
            models.Index(fields=['status', 'scheduled_date']),
            models.Index(fields=['assigned_inspector', 'status']),
        ]
    
    def __str__(self):
        return f"Inspection {self.inspection_number} - {self.declaration.declaration_number}"


class CustomsClearance(models.Model):
    """Model for customs clearance processing - extracted from FastAPI"""
    
    CLEARANCE_STATUS_CHOICES = [
        ('PENDING', 'Pending Clearance'),
        ('PROCESSING', 'Processing'),
        ('CLEARED', 'Cleared'),
        ('DETAINED', 'Detained'),
        ('RELEASED', 'Released'),
    ]
    
    # Core fields
    clearance_number = models.CharField(max_length=50, unique=True)
    declaration = models.OneToOneField(CustomsDeclaration, on_delete=models.CASCADE, related_name='clearance')
    status = models.CharField(max_length=20, choices=CLEARANCE_STATUS_CHOICES, default='PENDING')
    
    # Processing details
    customs_officer = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='processed_clearances'
    )
    
    # Financial
    total_duties = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    total_taxes = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    total_fees = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    payment_status = models.CharField(max_length=20, default='PENDING')
    
    # Dates
    clearance_requested = models.DateTimeField(auto_now_add=True)
    clearance_granted = models.DateTimeField(null=True, blank=True)
    
    # Additional information
    clearance_notes = models.TextField(blank=True)
    
    class Meta:
        db_table = 'customs_clearances'
        ordering = ['-clearance_requested']
        indexes = [
            models.Index(fields=['status', 'clearance_requested']),
            models.Index(fields=['customs_officer', 'status']),
        ]
    
    def __str__(self):
        return f"Clearance {self.clearance_number} - {self.status}"