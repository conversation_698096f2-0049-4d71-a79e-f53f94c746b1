from django.contrib import admin
from .models import CustomsDeclaration, CustomsInspection, CustomsClearance


@admin.register(CustomsDeclaration)
class CustomsDeclarationAdmin(admin.ModelAdmin):
    list_display = ['declaration_number', 'customer', 'status', 'goods_value', 'country_of_origin', 'submitted_date']
    list_filter = ['status', 'declaration_type', 'country_of_origin', 'submitted_date']
    search_fields = ['declaration_number', 'customer__email', 'goods_description', 'country_of_origin']
    readonly_fields = ['declaration_number', 'submitted_date', 'processed_date']
    ordering = ['-submitted_date']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('declaration_number', 'declaration_type', 'status', 'customer', 'shipment')
        }),
        ('Goods Details', {
            'fields': ('goods_description', 'goods_value', 'country_of_origin', 'tariff_codes')
        }),
        ('Processing', {
            'fields': ('duties_amount', 'taxes_amount', 'processing_fee', 'notes')
        }),
        ('Dates', {
            'fields': ('submitted_date', 'processed_date', 'reviewed_at')
        }),
    )


@admin.register(CustomsInspection)
class CustomsInspectionAdmin(admin.ModelAdmin):
    list_display = ['inspection_number', 'declaration', 'inspection_type', 'status', 'assigned_inspector', 'scheduled_date']
    list_filter = ['status', 'inspection_type', 'scheduled_date', 'passed']
    search_fields = ['inspection_number', 'declaration__declaration_number', 'assigned_inspector__email']
    ordering = ['-scheduled_date']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('inspection_number', 'declaration', 'inspection_type', 'status')
        }),
        ('Assignment', {
            'fields': ('assigned_inspector', 'scheduled_date')
        }),
        ('Progress', {
            'fields': ('started_at', 'completed_at', 'passed')
        }),
        ('Results', {
            'fields': ('inspection_notes', 'findings')
        }),
    )


@admin.register(CustomsClearance)
class CustomsClearanceAdmin(admin.ModelAdmin):
    list_display = ['clearance_number', 'declaration', 'status', 'customs_officer', 'total_duties', 'total_taxes', 'clearance_requested']
    list_filter = ['status', 'payment_status', 'clearance_requested']
    search_fields = ['clearance_number', 'declaration__declaration_number', 'customs_officer__email']
    ordering = ['-clearance_requested']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('clearance_number', 'declaration', 'status', 'customs_officer')
        }),
        ('Financial', {
            'fields': ('total_duties', 'total_taxes', 'total_fees', 'payment_status')
        }),
        ('Processing', {
            'fields': ('clearance_requested', 'clearance_granted', 'clearance_notes')
        }),
    )