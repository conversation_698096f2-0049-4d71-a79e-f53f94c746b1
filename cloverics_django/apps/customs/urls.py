from django.urls import path
from . import views

app_name = 'customs'

urlpatterns = [
    # Main customs views
    path('declarations/', views.customs_declarations, name='declarations'),
    path('inspections/', views.customs_inspections, name='inspections'),
    path('clearance/', views.customs_clearance, name='clearance'),
    
    # Declaration management
    path('declaration/new/', views.new_declaration_form, name='new_declaration'),
    path('declaration/<int:declaration_id>/', views.view_declaration_page, name='view_declaration'),
    path('declaration/<int:declaration_id>/view/', views.view_declaration_details, name='declaration_details'),
    path('declaration/<int:declaration_id>/print/', views.print_declaration, name='print_declaration'),
    path('declaration/<int:declaration_id>/approve/', views.approve_declaration, name='approve_declaration'),
    path('declaration/<int:declaration_id>/reject/', views.reject_declaration, name='reject_declaration'),
    
    # API endpoints
    path('api/declarations/filter/', views.filter_customs_declarations, name='api_filter_declarations'),
    path('api/declarations/create/', views.create_customs_declaration, name='api_create_declaration'),
    path('api/declarations/<int:declaration_id>/view/', views.view_customs_declaration, name='api_view_declaration'),
    path('api/declarations/<int:declaration_id>/review/', views.review_declaration_api, name='api_review_declaration'),
    
    # Bulk operations
    path('api/bulk-approve/', views.bulk_approve_clearances, name='api_bulk_approve'),
    path('api/hold-items/', views.hold_items, name='api_hold_items'),
    
    # Reports and exports
    path('api/export-report/', views.export_clearance_report, name='export_report'),
    
    # Customer customs declaration submission
    path('submit-declaration/', views.submit_customs_declaration, name='submit_declaration'),
]