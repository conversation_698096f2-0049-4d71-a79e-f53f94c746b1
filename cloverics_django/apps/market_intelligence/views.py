from django.shortcuts import render, get_object_or_404
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from django.contrib.auth.decorators import login_required
from django.utils.decorators import method_decorator
from django.views import View
from django.core.paginator import Paginator
from django.db.models import Q
from django.utils import timezone
import json
import asyncio

from .models import MarketOverview, RouteAnalytics, ProviderPerformanceAnalysis, PriceBenchmark, MarketIntelligenceAlert
from .services import MarketAnalyticsEngine, MarketAlertSystem


@login_required
def market_intelligence_hub(request):
    """Market Intelligence Hub dashboard page"""
    try:
        context = {
            'user': request.user,
            'title': 'Market Intelligence Hub - Cloverics',
            'page_name': 'market_intelligence'
        }
        
        return render(request, 'market_intelligence/market_hub.html', context)
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': f'Market intelligence hub error: {str(e)}'
        }, status=500)


@login_required
@require_http_methods(["GET"])
def get_market_overview(request):
    """Get comprehensive market overview analytics"""
    try:
        analytics_engine = MarketAnalyticsEngine()
        
        # Get cached overview or generate new one
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            market_data = loop.run_until_complete(
                analytics_engine.generate_market_overview()
            )
        finally:
            loop.close()
        
        return JsonResponse(market_data)
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': f'Market overview generation failed: {str(e)}',
            'fallback_data': {
                'total_active_routes': 0,
                'total_providers': 0,
                'message': 'Market data temporarily unavailable'
            }
        }, status=500)


@login_required
@require_http_methods(["GET"])
def get_route_analytics(request):
    """Get route performance analytics with filtering"""
    try:
        # Extract filter parameters
        filters = {
            'origin_country': request.GET.get('origin_country'),
            'destination_country': request.GET.get('destination_country'),
            'transport_type': request.GET.get('transport_type')
        }
        
        # Remove None values
        filters = {k: v for k, v in filters.items() if v}
        
        analytics_engine = MarketAnalyticsEngine()
        
        # Generate route analytics
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            route_data = loop.run_until_complete(
                analytics_engine.analyze_route_performance(filters)
            )
        finally:
            loop.close()
        
        return JsonResponse(route_data)
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': f'Route analytics failed: {str(e)}'
        }, status=500)


@login_required
@require_http_methods(["GET"])
def get_provider_performance(request):
    """Get provider performance analytics and rankings"""
    try:
        # Get provider performance from database
        providers = ProviderPerformanceAnalysis.objects.all().order_by('-competitive_ranking')[:20]
        
        provider_data = []
        for provider in providers:
            provider_data.append({
                'provider_id': provider.provider_id,
                'provider_name': provider.provider_name,
                'provider_type': provider.provider_type,
                'success_rate': float(provider.success_rate),
                'on_time_delivery_rate': float(provider.on_time_delivery_rate),
                'customer_rating': float(provider.customer_rating),
                'market_share': float(provider.market_share),
                'competitive_ranking': provider.competitive_ranking,
                'growth_rate': float(provider.growth_rate),
                'performance_score': float(provider.success_rate + provider.on_time_delivery_rate) / 2,
                'performance_trends': provider.performance_trends,
                'improvement_recommendations': provider.improvement_recommendations
            })
        
        return JsonResponse({
            'success': True,
            'data': {
                'providers': provider_data,
                'total_providers': len(provider_data),
                'top_performer': provider_data[0] if provider_data else None,
                'market_leader': max(provider_data, key=lambda x: x['market_share']) if provider_data else None
            }
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': f'Provider performance analysis failed: {str(e)}'
        }, status=500)


@login_required
@require_http_methods(["GET"])
def get_price_benchmarks(request):
    """Get price benchmarking data for routes"""
    try:
        route_signature = request.GET.get('route_signature', '')
        
        if route_signature:
            # Get specific route benchmark
            analytics_engine = MarketAnalyticsEngine()
            
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                benchmark_data = loop.run_until_complete(
                    analytics_engine.benchmark_market_prices(route_signature)
                )
            finally:
                loop.close()
            
            return JsonResponse(benchmark_data)
        else:
            # Get all recent benchmarks
            benchmarks = PriceBenchmark.objects.all().order_by('-benchmark_date')[:50]
            
            benchmark_data = []
            for benchmark in benchmarks:
                benchmark_data.append({
                    'route_signature': benchmark.route_signature,
                    'origin_country': benchmark.origin_country,
                    'destination_country': benchmark.destination_country,
                    'transport_mode': benchmark.transport_mode,
                    'market_rate_per_kg': float(benchmark.market_rate_per_kg),
                    'lowest_rate_per_kg': float(benchmark.lowest_rate_per_kg),
                    'highest_rate_per_kg': float(benchmark.highest_rate_per_kg),
                    'provider_count': benchmark.provider_count,
                    'price_volatility': float(benchmark.price_volatility),
                    'market_competitiveness': float(benchmark.market_competitiveness),
                    'benchmark_date': benchmark.benchmark_date.isoformat(),
                    'recommendations': benchmark.market_recommendations
                })
            
            return JsonResponse({
                'success': True,
                'data': {
                    'benchmarks': benchmark_data,
                    'total_benchmarks': len(benchmark_data)
                }
            })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': f'Price benchmarking failed: {str(e)}'
        }, status=500)


@login_required
@require_http_methods(["GET"])
def get_market_alerts(request):
    """Get market intelligence alerts for the user"""
    try:
        # Get alerts from database
        alerts = MarketIntelligenceAlert.objects.filter(
            user=request.user
        ).order_by('-created_at')[:20]
        
        alert_data = []
        for alert in alerts:
            alert_data.append({
                'alert_id': alert.alert_id,
                'alert_type': alert.alert_type,
                'severity': alert.severity,
                'title': alert.title,
                'message': alert.message,
                'route_affected': alert.route_affected,
                'provider_affected': alert.provider_affected,
                'percentage_change': float(alert.percentage_change) if alert.percentage_change else None,
                'is_read': alert.is_read,
                'is_actionable': alert.is_actionable,
                'created_at': alert.created_at.isoformat(),
                'expires_at': alert.expires_at.isoformat() if alert.expires_at else None
            })
        
        # Generate new alerts if needed
        alert_system = MarketAlertSystem()
        
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            new_alerts = loop.run_until_complete(
                alert_system.generate_market_alerts(request.user.id)
            )
        finally:
            loop.close()
        
        return JsonResponse({
            'success': True,
            'data': {
                'alerts': alert_data,
                'new_alerts': new_alerts,
                'total_alerts': len(alert_data),
                'unread_count': len([a for a in alert_data if not a['is_read']]),
                'high_priority_count': len([a for a in alert_data if a['severity'] in ['HIGH', 'CRITICAL']])
            }
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': f'Market alerts retrieval failed: {str(e)}'
        }, status=500)


@login_required
@csrf_exempt
@require_http_methods(["POST"])
def mark_alert_as_read(request, alert_id):
    """Mark a specific alert as read"""
    try:
        alert = get_object_or_404(
            MarketIntelligenceAlert,
            alert_id=alert_id,
            user=request.user
        )
        
        alert.mark_as_read()
        
        return JsonResponse({
            'success': True,
            'message': 'Alert marked as read',
            'alert_id': alert_id
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': f'Failed to mark alert as read: {str(e)}'
        }, status=500)


@login_required
@require_http_methods(["GET"])
def get_market_trends(request):
    """Get market trend analysis and forecasting"""
    try:
        # Get trend period (default 30 days)
        period_days = int(request.GET.get('period', 30))
        
        # Calculate date range
        end_date = timezone.now()
        start_date = end_date - timezone.timedelta(days=period_days)
        
        # Get market overviews in date range
        overviews = MarketOverview.objects.filter(
            date__range=[start_date, end_date]
        ).order_by('date')
        
        trend_data = []
        for overview in overviews:
            trend_data.append({
                'date': overview.date.isoformat(),
                'market_volatility': float(overview.market_volatility_index),
                'demand_growth': float(overview.demand_growth_rate),
                'avg_cost_per_kg': float(overview.avg_cost_per_kg),
                'total_volume': float(overview.total_volume_tons),
                'active_routes': overview.total_active_routes,
                'provider_count': overview.total_providers
            })
        
        # Calculate trend analysis
        if len(trend_data) >= 2:
            latest = trend_data[-1]
            previous = trend_data[0]
            
            trend_analysis = {
                'volatility_trend': latest['market_volatility'] - previous['market_volatility'],
                'growth_trend': latest['demand_growth'] - previous['demand_growth'],
                'cost_trend': latest['avg_cost_per_kg'] - previous['avg_cost_per_kg'],
                'volume_trend': latest['total_volume'] - previous['total_volume'],
                'route_expansion': latest['active_routes'] - previous['active_routes'],
                'provider_growth': latest['provider_count'] - previous['provider_count']
            }
        else:
            trend_analysis = {
                'message': 'Insufficient data for trend analysis'
            }
        
        return JsonResponse({
            'success': True,
            'data': {
                'trends': trend_data,
                'analysis': trend_analysis,
                'period_days': period_days,
                'data_points': len(trend_data)
            }
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': f'Market trend analysis failed: {str(e)}'
        }, status=500)


@login_required
@require_http_methods(["GET"])
def export_market_data(request):
    """Export market intelligence data in various formats"""
    try:
        export_type = request.GET.get('type', 'overview')
        format_type = request.GET.get('format', 'json')
        
        if export_type == 'overview':
            # Export market overview data
            overview = MarketOverview.objects.first()
            if overview:
                data = {
                    'market_overview': {
                        'date': overview.date.isoformat(),
                        'total_routes': overview.total_active_routes,
                        'total_providers': overview.total_providers,
                        'volume_tons': float(overview.total_volume_tons),
                        'market_value': float(overview.total_value_usd),
                        'volatility_index': float(overview.market_volatility_index),
                        'growth_rate': float(overview.demand_growth_rate)
                    }
                }
            else:
                data = {'message': 'No market overview data available'}
        
        elif export_type == 'routes':
            # Export route analytics
            routes = RouteAnalytics.objects.all()[:100]
            data = {
                'route_analytics': [
                    {
                        'route_name': route.route_name,
                        'total_shipments': route.total_shipments,
                        'success_rate': float(route.success_rate),
                        'avg_price_per_kg': float(route.avg_price_per_kg),
                        'market_share': float(route.market_share)
                    }
                    for route in routes
                ]
            }
        
        elif export_type == 'providers':
            # Export provider performance
            providers = ProviderPerformanceAnalysis.objects.all()[:50]
            data = {
                'provider_performance': [
                    {
                        'provider_name': provider.provider_name,
                        'success_rate': float(provider.success_rate),
                        'market_share': float(provider.market_share),
                        'competitive_ranking': provider.competitive_ranking
                    }
                    for provider in providers
                ]
            }
        
        else:
            data = {'error': 'Invalid export type'}
        
        # Set appropriate content type
        if format_type == 'json':
            response = JsonResponse(data)
            response['Content-Disposition'] = f'attachment; filename="market_data_{export_type}.json"'
        else:
            response = JsonResponse({
                'success': False,
                'error': 'Only JSON format is currently supported'
            })
        
        return response
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': f'Data export failed: {str(e)}'
        }, status=500)


# API endpoint for real-time market data updates
@login_required
@require_http_methods(["GET"])
def get_real_time_market_data(request):
    """Get real-time market data updates"""
    try:
        # This would connect to real-time data feeds in production
        current_time = timezone.now()
        
        real_time_data = {
            'timestamp': current_time.isoformat(),
            'market_status': 'active',
            'live_metrics': {
                'active_quotes': 1247,
                'processing_shipments': 8934,
                'real_time_capacity': 76.3,
                'market_volatility': 12.8,
                'avg_response_time': 2.4
            },
            'trending_routes': [
                {'route': 'US-Europe', 'trend': '+5.2%', 'volume': 2847},
                {'route': 'Asia-Europe', 'trend': '+3.1%', 'volume': 1923},
                {'route': 'US-Asia', 'trend': '-1.8%', 'volume': 1567}
            ],
            'price_movements': {
                'truck_rates': '+2.3%',
                'air_rates': '+1.8%',
                'ship_rates': '-0.5%',
                'rail_rates': '+0.9%'
            }
        }
        
        return JsonResponse({
            'success': True,
            'data': real_time_data
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': f'Real-time data retrieval failed: {str(e)}'
        }, status=500)