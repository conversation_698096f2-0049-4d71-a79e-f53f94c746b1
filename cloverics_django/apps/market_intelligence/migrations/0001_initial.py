# Generated by Django 5.2.1 on 2025-08-07 12:45

import django.db.models.deletion
import django.utils.timezone
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='MarketOverview',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('overview_id', models.CharField(default=uuid.uuid4, max_length=50, unique=True)),
                ('date', models.DateTimeField(default=django.utils.timezone.now)),
                ('total_active_routes', models.IntegerField(default=0)),
                ('total_providers', models.IntegerField(default=0)),
                ('total_volume_tons', models.DecimalField(decimal_places=2, default=0, max_digits=15)),
                ('total_value_usd', models.DecimalField(decimal_places=2, default=0, max_digits=15)),
                ('avg_transit_time', models.DecimalField(decimal_places=2, default=0, max_digits=8)),
                ('avg_cost_per_kg', models.DecimalField(decimal_places=4, default=0, max_digits=10)),
                ('market_volatility_index', models.DecimalField(decimal_places=2, default=0, max_digits=5)),
                ('demand_growth_rate', models.DecimalField(decimal_places=2, default=0, max_digits=5)),
                ('truck_percentage', models.DecimalField(decimal_places=2, default=0, max_digits=5)),
                ('rail_percentage', models.DecimalField(decimal_places=2, default=0, max_digits=5)),
                ('air_percentage', models.DecimalField(decimal_places=2, default=0, max_digits=5)),
                ('ship_percentage', models.DecimalField(decimal_places=2, default=0, max_digits=5)),
                ('top_routes', models.JSONField(default=list)),
                ('price_trends', models.JSONField(default=dict)),
                ('capacity_utilization', models.JSONField(default=dict)),
                ('seasonal_patterns', models.JSONField(default=dict)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'market_intelligence_overview',
                'ordering': ['-date'],
                'indexes': [models.Index(fields=['date'], name='market_inte_date_08ecdf_idx'), models.Index(fields=['overview_id'], name='market_inte_overvie_61759f_idx'), models.Index(fields=['created_at'], name='market_inte_created_2de95f_idx')],
            },
        ),
        migrations.CreateModel(
            name='PriceBenchmark',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('benchmark_id', models.CharField(default=uuid.uuid4, max_length=50, unique=True)),
                ('route_signature', models.CharField(max_length=300)),
                ('origin_country', models.CharField(max_length=100)),
                ('origin_city', models.CharField(max_length=100)),
                ('destination_country', models.CharField(max_length=100)),
                ('destination_city', models.CharField(max_length=100)),
                ('transport_mode', models.CharField(max_length=50)),
                ('market_rate_per_kg', models.DecimalField(decimal_places=4, default=0, max_digits=10)),
                ('lowest_rate_per_kg', models.DecimalField(decimal_places=4, default=0, max_digits=10)),
                ('highest_rate_per_kg', models.DecimalField(decimal_places=4, default=0, max_digits=10)),
                ('median_rate_per_kg', models.DecimalField(decimal_places=4, default=0, max_digits=10)),
                ('provider_count', models.IntegerField(default=0)),
                ('quote_volume', models.IntegerField(default=0)),
                ('price_volatility', models.DecimalField(decimal_places=2, default=0, max_digits=5)),
                ('market_competitiveness', models.DecimalField(decimal_places=2, default=0, max_digits=5)),
                ('standard_service_rate', models.DecimalField(decimal_places=4, default=0, max_digits=10)),
                ('express_service_rate', models.DecimalField(decimal_places=4, default=0, max_digits=10)),
                ('urgent_service_rate', models.DecimalField(decimal_places=4, default=0, max_digits=10)),
                ('price_trend_7d', models.DecimalField(decimal_places=2, default=0, max_digits=5)),
                ('price_trend_30d', models.DecimalField(decimal_places=2, default=0, max_digits=5)),
                ('price_trend_90d', models.DecimalField(decimal_places=2, default=0, max_digits=5)),
                ('seasonal_adjustment', models.DecimalField(decimal_places=2, default=0, max_digits=5)),
                ('demand_supply_ratio', models.DecimalField(decimal_places=2, default=0, max_digits=5)),
                ('market_recommendations', models.JSONField(default=list)),
                ('benchmark_date', models.DateTimeField(default=django.utils.timezone.now)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'market_intelligence_price_benchmark',
                'ordering': ['-benchmark_date', 'route_signature'],
                'indexes': [models.Index(fields=['route_signature'], name='market_inte_route_s_03f602_idx'), models.Index(fields=['transport_mode'], name='market_inte_transpo_d573ee_idx'), models.Index(fields=['market_rate_per_kg'], name='market_inte_market__a2e91c_idx'), models.Index(fields=['benchmark_date'], name='market_inte_benchma_f03077_idx'), models.Index(fields=['origin_country', 'destination_country'], name='market_inte_origin__758d23_idx')],
            },
        ),
        migrations.CreateModel(
            name='ProviderPerformanceAnalysis',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('analysis_id', models.CharField(default=uuid.uuid4, max_length=50, unique=True)),
                ('provider_id', models.IntegerField()),
                ('provider_name', models.CharField(max_length=200)),
                ('provider_type', models.CharField(max_length=50)),
                ('total_shipments_handled', models.IntegerField(default=0)),
                ('total_revenue_generated', models.DecimalField(decimal_places=2, default=0, max_digits=15)),
                ('success_rate', models.DecimalField(decimal_places=2, default=0, max_digits=5)),
                ('on_time_delivery_rate', models.DecimalField(decimal_places=2, default=0, max_digits=5)),
                ('customer_rating', models.DecimalField(decimal_places=2, default=0, max_digits=3)),
                ('damage_rate', models.DecimalField(decimal_places=4, default=0, max_digits=5)),
                ('claims_rate', models.DecimalField(decimal_places=4, default=0, max_digits=5)),
                ('response_time_hours', models.DecimalField(decimal_places=2, default=0, max_digits=6)),
                ('market_share', models.DecimalField(decimal_places=2, default=0, max_digits=5)),
                ('competitive_ranking', models.IntegerField(default=0)),
                ('growth_rate', models.DecimalField(decimal_places=2, default=0, max_digits=5)),
                ('capacity_utilization', models.DecimalField(decimal_places=2, default=0, max_digits=5)),
                ('fleet_size', models.IntegerField(default=0)),
                ('coverage_countries', models.IntegerField(default=0)),
                ('service_routes', models.IntegerField(default=0)),
                ('profit_margin', models.DecimalField(decimal_places=2, default=0, max_digits=5)),
                ('cost_efficiency_index', models.DecimalField(decimal_places=2, default=0, max_digits=5)),
                ('pricing_competitiveness', models.DecimalField(decimal_places=2, default=0, max_digits=5)),
                ('performance_trends', models.JSONField(default=dict)),
                ('route_performance', models.JSONField(default=dict)),
                ('customer_segments', models.JSONField(default=dict)),
                ('improvement_recommendations', models.JSONField(default=list)),
                ('analysis_date', models.DateTimeField(default=django.utils.timezone.now)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'market_intelligence_provider_analysis',
                'ordering': ['-competitive_ranking', '-success_rate'],
                'indexes': [models.Index(fields=['provider_id'], name='market_inte_provide_53003b_idx'), models.Index(fields=['provider_name'], name='market_inte_provide_b5600e_idx'), models.Index(fields=['competitive_ranking'], name='market_inte_competi_0ffa01_idx'), models.Index(fields=['success_rate'], name='market_inte_success_58148f_idx'), models.Index(fields=['market_share'], name='market_inte_market__c49e72_idx'), models.Index(fields=['analysis_date'], name='market_inte_analysi_5ffefc_idx')],
            },
        ),
        migrations.CreateModel(
            name='RouteAnalytics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('analytics_id', models.CharField(default=uuid.uuid4, max_length=50, unique=True)),
                ('route_name', models.CharField(max_length=200)),
                ('origin_country', models.CharField(max_length=100)),
                ('origin_city', models.CharField(max_length=100)),
                ('destination_country', models.CharField(max_length=100)),
                ('destination_city', models.CharField(max_length=100)),
                ('total_shipments', models.IntegerField(default=0)),
                ('total_volume_kg', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('total_value_usd', models.DecimalField(decimal_places=2, default=0, max_digits=15)),
                ('success_rate', models.DecimalField(decimal_places=2, default=0, max_digits=5)),
                ('avg_price_per_kg', models.DecimalField(decimal_places=4, default=0, max_digits=10)),
                ('min_price_per_kg', models.DecimalField(decimal_places=4, default=0, max_digits=10)),
                ('max_price_per_kg', models.DecimalField(decimal_places=4, default=0, max_digits=10)),
                ('price_volatility', models.DecimalField(decimal_places=2, default=0, max_digits=5)),
                ('avg_transit_days', models.DecimalField(decimal_places=2, default=0, max_digits=6)),
                ('on_time_percentage', models.DecimalField(decimal_places=2, default=0, max_digits=5)),
                ('damage_rate', models.DecimalField(decimal_places=4, default=0, max_digits=5)),
                ('customer_satisfaction', models.DecimalField(decimal_places=2, default=0, max_digits=3)),
                ('market_share', models.DecimalField(decimal_places=2, default=0, max_digits=5)),
                ('competitive_index', models.DecimalField(decimal_places=2, default=0, max_digits=5)),
                ('growth_trend', models.CharField(default='stable', max_length=20)),
                ('monthly_trends', models.JSONField(default=dict)),
                ('seasonal_patterns', models.JSONField(default=dict)),
                ('demand_forecast', models.JSONField(default=dict)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'market_intelligence_route_analytics',
                'ordering': ['-total_shipments', '-total_value_usd'],
                'indexes': [models.Index(fields=['route_name'], name='market_inte_route_n_b2d68e_idx'), models.Index(fields=['origin_country', 'destination_country'], name='market_inte_origin__3f6f7a_idx'), models.Index(fields=['success_rate'], name='market_inte_success_5a63f1_idx'), models.Index(fields=['avg_price_per_kg'], name='market_inte_avg_pri_4c6d8d_idx'), models.Index(fields=['created_at'], name='market_inte_created_293352_idx')],
            },
        ),
        migrations.CreateModel(
            name='MarketIntelligenceAlert',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('alert_id', models.CharField(default=uuid.uuid4, max_length=50, unique=True)),
                ('alert_type', models.CharField(choices=[('PRICE_DROP', 'Significant Price Drop'), ('PRICE_SPIKE', 'Price Spike Alert'), ('NEW_ROUTE', 'New Route Available'), ('CAPACITY_ALERT', 'Capacity Shortage'), ('MARKET_SHIFT', 'Market Trend Shift'), ('PROVIDER_ISSUE', 'Provider Performance Issue'), ('OPPORTUNITY', 'Market Opportunity'), ('DISRUPTION', 'Market Disruption')], max_length=20)),
                ('severity', models.CharField(choices=[('LOW', 'Low Priority'), ('MEDIUM', 'Medium Priority'), ('HIGH', 'High Priority'), ('CRITICAL', 'Critical Alert')], default='MEDIUM', max_length=10)),
                ('title', models.CharField(max_length=200)),
                ('message', models.TextField()),
                ('route_affected', models.CharField(blank=True, max_length=200)),
                ('provider_affected', models.CharField(blank=True, max_length=200)),
                ('previous_value', models.DecimalField(blank=True, decimal_places=4, max_digits=12, null=True)),
                ('current_value', models.DecimalField(blank=True, decimal_places=4, max_digits=12, null=True)),
                ('percentage_change', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True)),
                ('is_read', models.BooleanField(default=False)),
                ('is_actionable', models.BooleanField(default=False)),
                ('action_taken', models.TextField(blank=True)),
                ('expires_at', models.DateTimeField(blank=True, null=True)),
                ('alert_data', models.JSONField(default=dict)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('read_at', models.DateTimeField(blank=True, null=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='market_alerts', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'market_intelligence_alerts',
                'ordering': ['-created_at', '-severity'],
                'indexes': [models.Index(fields=['user'], name='market_inte_user_id_91ebe7_idx'), models.Index(fields=['alert_type'], name='market_inte_alert_t_9a3200_idx'), models.Index(fields=['severity'], name='market_inte_severit_6c0fe4_idx'), models.Index(fields=['is_read'], name='market_inte_is_read_cdad84_idx'), models.Index(fields=['created_at'], name='market_inte_created_b523cf_idx'), models.Index(fields=['expires_at'], name='market_inte_expires_833515_idx')],
            },
        ),
    ]
