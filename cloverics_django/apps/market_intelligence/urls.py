from django.urls import path
from . import views

app_name = 'market_intelligence'

urlpatterns = [
    # Market Intelligence Hub Dashboard
    path('hub/', views.market_intelligence_hub, name='hub'),
    
    # Market Analytics API Endpoints
    path('api/market-overview/', views.get_market_overview, name='market_overview'),
    path('api/route-analytics/', views.get_route_analytics, name='route_analytics'),
    path('api/provider-performance/', views.get_provider_performance, name='provider_performance'),
    path('api/price-benchmarks/', views.get_price_benchmarks, name='price_benchmarks'),
    path('api/market-trends/', views.get_market_trends, name='market_trends'),
    path('api/real-time-data/', views.get_real_time_market_data, name='real_time_data'),
    
    # Alert Management
    path('api/alerts/', views.get_market_alerts, name='market_alerts'),
    path('api/alerts/<str:alert_id>/read/', views.mark_alert_as_read, name='mark_alert_read'),
    
    # Data Export
    path('api/export/', views.export_market_data, name='export_data'),
]