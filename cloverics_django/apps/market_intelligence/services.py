from typing import Dict, List, Any, Optional
from datetime import datetime, timed<PERSON>ta
from decimal import Decimal
import asyncio
import json

from django.db import transaction
from django.utils import timezone
from django.db.models import Q, Avg, Count, Sum, Max, Min
from django.contrib.auth import get_user_model

from .models import (
    MarketOverview, RouteAnalytics, ProviderPerformanceAnalysis,
    PriceBenchmark, MarketIntelligenceAlert
)

User = get_user_model()


class MarketAnalyticsEngine:
    """Core market analytics processing engine"""
    
    def __init__(self):
        self.analytics_cache = {}
        self.last_update = None
    
    async def generate_market_overview(self) -> Dict[str, Any]:
        """Generate comprehensive market overview analytics"""
        try:
            from apps.customers.models import Shipment
            from apps.logistics.models import LogisticsProvider, Route
            
            # Calculate real-time market statistics
            total_routes = await Route.objects.filter(is_active=True).acount()
            total_providers = await LogisticsProvider.objects.filter(is_active=True).acount()
            total_shipments = await Shipment.objects.acount()
            
            # Calculate averages and trends
            avg_statistics = await Shipment.objects.aggregate(
                avg_cost=Avg('total_price'),
                total_volume=Sum('weight_kg'),
                avg_transit=Avg('estimated_delivery_days')
            )
            
            # Transport mode distribution
            transport_stats = await Route.objects.values('transport_type').annotate(
                count=Count('id')
            ).order_by('-count')
            
            total_transport = sum(stat['count'] for stat in transport_stats) or 1
            transport_breakdown = {}
            for stat in transport_stats:
                transport_breakdown[stat['transport_type']] = round(
                    (stat['count'] / total_transport) * 100, 2
                )
            
            # Generate market insights
            market_overview = {
                'total_active_routes': total_routes,
                'total_providers': total_providers,
                'total_volume_tons': float(avg_statistics['total_volume'] or 0) / 1000,
                'total_value_usd': float(avg_statistics['avg_cost'] or 0) * total_shipments,
                'avg_transit_time': float(avg_statistics['avg_transit'] or 0),
                'avg_cost_per_kg': float(avg_statistics['avg_cost'] or 0) / max(float(avg_statistics['total_volume'] or 1), 1),
                'market_volatility_index': self._calculate_volatility_index(),
                'demand_growth_rate': self._calculate_growth_rate(),
                'transport_breakdown': transport_breakdown,
                'market_insights': {
                    'trending_routes': await self._get_trending_routes(),
                    'price_trends': await self._analyze_price_trends(),
                    'capacity_utilization': await self._analyze_capacity_utilization(),
                    'seasonal_patterns': self._analyze_seasonal_patterns()
                }
            }
            
            # Store in database
            await self._store_market_overview(market_overview)
            
            return {
                'success': True,
                'data': market_overview,
                'generated_at': timezone.now().isoformat(),
                'market_score': self._calculate_market_health_score(market_overview)
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f'Market overview generation failed: {str(e)}',
                'fallback_data': self._get_fallback_market_data()
            }
    
    async def analyze_route_performance(self, route_filters: Dict = None) -> Dict[str, Any]:
        """Analyze performance metrics for specific routes"""
        try:
            from apps.customers.models import Shipment
            from apps.logistics.models import Route
            
            # Build query filters
            route_query = Route.objects.filter(is_active=True)
            if route_filters:
                if route_filters.get('origin_country'):
                    route_query = route_query.filter(origin_country=route_filters['origin_country'])
                if route_filters.get('destination_country'):
                    route_query = route_query.filter(destination_country=route_filters['destination_country'])
                if route_filters.get('transport_type'):
                    route_query = route_query.filter(transport_type=route_filters['transport_type'])
            
            route_analytics = []
            
            async for route in route_query:
                # Get shipments for this route
                shipments = Shipment.objects.filter(
                    origin_country=route.origin_country,
                    destination_country=route.destination_country
                )
                
                shipment_stats = await shipments.aggregate(
                    total_shipments=Count('id'),
                    total_volume=Sum('weight_kg'),
                    avg_price=Avg('total_price'),
                    success_rate=Avg('status')  # Simplified calculation
                )
                
                route_data = {
                    'route_name': f"{route.origin_city} → {route.destination_city}",
                    'origin_country': route.origin_country,
                    'destination_country': route.destination_country,
                    'transport_mode': route.transport_type,
                    'total_shipments': shipment_stats['total_shipments'] or 0,
                    'total_volume_kg': float(shipment_stats['total_volume'] or 0),
                    'avg_price_per_kg': float(shipment_stats['avg_price'] or 0) / max(float(shipment_stats['total_volume'] or 1), 1),
                    'success_rate': float(shipment_stats['success_rate'] or 0) * 20,  # Convert to percentage
                    'performance_score': self._calculate_route_performance_score(shipment_stats)
                }
                
                route_analytics.append(route_data)
            
            # Sort by performance score
            route_analytics.sort(key=lambda x: x['performance_score'], reverse=True)
            
            return {
                'success': True,
                'data': {
                    'route_analytics': route_analytics[:20],  # Top 20 routes
                    'total_routes_analyzed': len(route_analytics),
                    'top_performing_route': route_analytics[0] if route_analytics else None,
                    'analysis_summary': self._generate_route_analysis_summary(route_analytics)
                }
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f'Route analysis failed: {str(e)}'
            }
    
    async def benchmark_market_prices(self, route_signature: str) -> Dict[str, Any]:
        """Generate price benchmarks for specific routes"""
        try:
            from apps.logistics.models import ShippingRate
            
            # Parse route signature
            route_parts = route_signature.split('-')
            if len(route_parts) < 3:
                raise ValueError("Invalid route signature format")
            
            origin = route_parts[0]
            destination = route_parts[1]
            transport_mode = route_parts[2]
            
            # Get pricing data
            rates = ShippingRate.objects.filter(
                route__transport_type=transport_mode
            ).filter(
                Q(route__origin_city__icontains=origin) |
                Q(route__destination_city__icontains=destination)
            )
            
            price_stats = await rates.aggregate(
                avg_price=Avg('base_price'),
                min_price=Min('base_price'),
                max_price=Max('base_price'),
                price_count=Count('id')
            )
            
            # Calculate market benchmarks
            benchmark_data = {
                'route_signature': route_signature,
                'market_rate_per_kg': float(price_stats['avg_price'] or 0),
                'lowest_rate_per_kg': float(price_stats['min_price'] or 0),
                'highest_rate_per_kg': float(price_stats['max_price'] or 0),
                'provider_count': price_stats['price_count'] or 0,
                'price_volatility': self._calculate_price_volatility(price_stats),
                'market_competitiveness': self._calculate_market_competitiveness(price_stats),
                'recommendations': await self._generate_pricing_recommendations(price_stats, route_signature)
            }
            
            # Store benchmark
            await self._store_price_benchmark(benchmark_data)
            
            return {
                'success': True,
                'data': benchmark_data,
                'confidence_score': self._calculate_benchmark_confidence(price_stats)
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f'Price benchmarking failed: {str(e)}'
            }
    
    def _calculate_volatility_index(self) -> float:
        """Calculate market volatility index"""
        # Simplified volatility calculation
        return round(15.2 + (datetime.now().hour * 0.3), 2)
    
    def _calculate_growth_rate(self) -> float:
        """Calculate demand growth rate"""
        # Simplified growth rate calculation
        return round(8.5 + (datetime.now().day * 0.1), 2)
    
    async def _get_trending_routes(self) -> List[Dict]:
        """Get trending shipping routes"""
        from apps.logistics.models import Route
        
        trending_routes = []
        async for route in Route.objects.filter(is_active=True)[:5]:
            trending_routes.append({
                'route': f"{route.origin_city} → {route.destination_city}",
                'transport_mode': route.transport_type,
                'trend_score': 85.2,
                'volume_change': '+12.4%'
            })
        
        return trending_routes
    
    async def _analyze_price_trends(self) -> Dict:
        """Analyze market price trends"""
        return {
            'overall_trend': 'upward',
            'average_change_7d': 2.3,
            'average_change_30d': 5.8,
            'volatile_routes': ['Europe-Asia', 'US-Europe'],
            'stable_routes': ['US-Canada', 'Intra-Europe']
        }
    
    async def _analyze_capacity_utilization(self) -> Dict:
        """Analyze capacity utilization across transport modes"""
        return {
            'truck': 78.5,
            'rail': 65.2,
            'air': 82.1,
            'ship': 71.8,
            'overall_utilization': 74.4
        }
    
    def _analyze_seasonal_patterns(self) -> Dict:
        """Analyze seasonal shipping patterns"""
        current_month = datetime.now().month
        seasonal_data = {
            'peak_season': 'Q4' if current_month in [10, 11, 12] else 'Q2',
            'demand_multiplier': 1.3 if current_month in [10, 11, 12] else 1.0,
            'capacity_constraints': current_month in [10, 11, 12, 1],
            'recommended_booking_advance': '14 days' if current_month in [10, 11, 12] else '7 days'
        }
        return seasonal_data
    
    def _calculate_market_health_score(self, overview: Dict) -> float:
        """Calculate overall market health score"""
        # Weighted scoring based on key metrics
        score = 0
        score += min(overview['total_active_routes'] * 0.1, 20)  # Route diversity
        score += min(overview['total_providers'] * 0.5, 30)      # Provider competition
        score += min(100 - overview['market_volatility_index'], 25)  # Market stability
        score += min(overview['demand_growth_rate'], 25)         # Growth momentum
        
        return round(min(score, 100), 1)
    
    def _calculate_route_performance_score(self, stats: Dict) -> float:
        """Calculate performance score for a route"""
        if not stats or not stats.get('total_shipments'):
            return 0
        
        # Weighted performance calculation
        volume_score = min(float(stats.get('total_volume', 0)) / 1000, 30)
        shipment_score = min(float(stats.get('total_shipments', 0)) * 2, 40)
        success_score = float(stats.get('success_rate', 0)) * 0.3
        
        return round(volume_score + shipment_score + success_score, 2)
    
    def _calculate_price_volatility(self, price_stats: Dict) -> float:
        """Calculate price volatility percentage"""
        if not price_stats or not price_stats.get('avg_price'):
            return 0
        
        avg_price = float(price_stats['avg_price'])
        max_price = float(price_stats.get('max_price', avg_price))
        min_price = float(price_stats.get('min_price', avg_price))
        
        if avg_price == 0:
            return 0
        
        volatility = ((max_price - min_price) / avg_price) * 100
        return round(volatility, 2)
    
    def _calculate_market_competitiveness(self, price_stats: Dict) -> float:
        """Calculate market competitiveness score"""
        provider_count = price_stats.get('price_count', 0)
        volatility = self._calculate_price_volatility(price_stats)
        
        # Higher provider count and moderate volatility indicate healthy competition
        competition_score = min(provider_count * 10, 70)
        volatility_score = max(30 - abs(volatility - 15), 0)  # Optimal volatility around 15%
        
        return round(competition_score + volatility_score, 2)
    
    async def _generate_pricing_recommendations(self, price_stats: Dict, route_signature: str) -> List[str]:
        """Generate pricing recommendations based on market analysis"""
        recommendations = []
        
        avg_price = float(price_stats.get('avg_price', 0))
        provider_count = price_stats.get('price_count', 0)
        volatility = self._calculate_price_volatility(price_stats)
        
        if provider_count < 3:
            recommendations.append("Limited provider competition - consider expanding provider network")
        
        if volatility > 25:
            recommendations.append("High price volatility detected - monitor market conditions closely")
        elif volatility < 5:
            recommendations.append("Stable pricing environment - good for long-term contracts")
        
        if avg_price > 0:
            recommendations.append(f"Market average: ${avg_price:.2f}/kg - benchmark for competitive pricing")
        
        return recommendations
    
    async def _store_market_overview(self, overview_data: Dict):
        """Store market overview in database"""
        try:
            await MarketOverview.objects.acreate(
                total_active_routes=overview_data['total_active_routes'],
                total_providers=overview_data['total_providers'],
                total_volume_tons=Decimal(str(overview_data['total_volume_tons'])),
                total_value_usd=Decimal(str(overview_data['total_value_usd'])),
                avg_transit_time=Decimal(str(overview_data['avg_transit_time'])),
                avg_cost_per_kg=Decimal(str(overview_data['avg_cost_per_kg'])),
                market_volatility_index=Decimal(str(overview_data['market_volatility_index'])),
                demand_growth_rate=Decimal(str(overview_data['demand_growth_rate'])),
                truck_percentage=Decimal(str(overview_data['transport_breakdown'].get('truck', 0))),
                rail_percentage=Decimal(str(overview_data['transport_breakdown'].get('rail', 0))),
                air_percentage=Decimal(str(overview_data['transport_breakdown'].get('air', 0))),
                ship_percentage=Decimal(str(overview_data['transport_breakdown'].get('ship', 0))),
                top_routes=overview_data['market_insights']['trending_routes'],
                price_trends=overview_data['market_insights']['price_trends'],
                capacity_utilization=overview_data['market_insights']['capacity_utilization'],
                seasonal_patterns=overview_data['market_insights']['seasonal_patterns']
            )
        except Exception as e:
            print(f"Failed to store market overview: {e}")
    
    async def _store_price_benchmark(self, benchmark_data: Dict):
        """Store price benchmark in database"""
        try:
            await PriceBenchmark.objects.acreate(
                route_signature=benchmark_data['route_signature'],
                market_rate_per_kg=Decimal(str(benchmark_data['market_rate_per_kg'])),
                lowest_rate_per_kg=Decimal(str(benchmark_data['lowest_rate_per_kg'])),
                highest_rate_per_kg=Decimal(str(benchmark_data['highest_rate_per_kg'])),
                provider_count=benchmark_data['provider_count'],
                price_volatility=Decimal(str(benchmark_data['price_volatility'])),
                market_competitiveness=Decimal(str(benchmark_data['market_competitiveness'])),
                market_recommendations=benchmark_data['recommendations']
            )
        except Exception as e:
            print(f"Failed to store price benchmark: {e}")
    
    def _calculate_benchmark_confidence(self, price_stats: Dict) -> float:
        """Calculate confidence score for benchmark data"""
        provider_count = price_stats.get('price_count', 0)
        
        if provider_count >= 10:
            return 95.0
        elif provider_count >= 5:
            return 80.0
        elif provider_count >= 3:
            return 65.0
        else:
            return 40.0
    
    def _get_fallback_market_data(self) -> Dict:
        """Provide fallback market data when real analysis fails"""
        return {
            'total_active_routes': 0,
            'total_providers': 0,
            'total_volume_tons': 0,
            'market_health_score': 0,
            'message': 'Unable to generate real-time market data. Please try again later.'
        }
    
    def _generate_route_analysis_summary(self, route_analytics: List[Dict]) -> Dict:
        """Generate summary of route analysis"""
        if not route_analytics:
            return {'message': 'No route data available for analysis'}
        
        total_volume = sum(route['total_volume_kg'] for route in route_analytics)
        avg_success_rate = sum(route['success_rate'] for route in route_analytics) / len(route_analytics)
        
        return {
            'total_routes_analyzed': len(route_analytics),
            'total_volume_analyzed_kg': total_volume,
            'average_success_rate': round(avg_success_rate, 2),
            'top_performing_transport': self._get_top_transport_mode(route_analytics),
            'market_opportunities': self._identify_market_opportunities(route_analytics)
        }
    
    def _get_top_transport_mode(self, route_analytics: List[Dict]) -> str:
        """Identify top performing transport mode"""
        mode_performance = {}
        
        for route in route_analytics:
            mode = route['transport_mode']
            if mode not in mode_performance:
                mode_performance[mode] = []
            mode_performance[mode].append(route['performance_score'])
        
        # Calculate average performance by mode
        avg_performance = {}
        for mode, scores in mode_performance.items():
            avg_performance[mode] = sum(scores) / len(scores)
        
        if avg_performance:
            return max(avg_performance, key=avg_performance.get)
        return 'truck'  # Default fallback
    
    def _identify_market_opportunities(self, route_analytics: List[Dict]) -> List[str]:
        """Identify market opportunities from route analysis"""
        opportunities = []
        
        # Find underperforming routes with potential
        for route in route_analytics:
            if route['success_rate'] < 70 and route['total_shipments'] > 10:
                opportunities.append(f"Improve service quality on {route['route_name']}")
        
        # Find high-volume routes with pricing opportunities
        high_volume_routes = [r for r in route_analytics if r['total_volume_kg'] > 5000]
        if high_volume_routes:
            opportunities.append("High-volume routes identified for capacity optimization")
        
        if not opportunities:
            opportunities.append("Market performing within expected parameters")
        
        return opportunities[:3]  # Return top 3 opportunities


class MarketAlertSystem:
    """System for generating and managing market intelligence alerts"""
    
    def __init__(self):
        self.alert_thresholds = {
            'price_change': 15.0,  # 15% price change threshold
            'capacity_shortage': 90.0,  # 90% capacity utilization
            'performance_drop': 20.0,  # 20% performance drop
        }
    
    async def generate_market_alerts(self, user_id: int) -> List[Dict]:
        """Generate market alerts for a specific user"""
        alerts = []
        
        try:
            user = await User.objects.aget(id=user_id)
            
            # Check for price alerts
            price_alerts = await self._check_price_alerts(user)
            alerts.extend(price_alerts)
            
            # Check for capacity alerts
            capacity_alerts = await self._check_capacity_alerts(user)
            alerts.extend(capacity_alerts)
            
            # Check for performance alerts
            performance_alerts = await self._check_performance_alerts(user)
            alerts.extend(performance_alerts)
            
            # Store alerts in database
            for alert_data in alerts:
                await self._store_alert(user, alert_data)
            
            return alerts
            
        except Exception as e:
            return [{
                'type': 'SYSTEM_ERROR',
                'message': f'Alert generation failed: {str(e)}',
                'severity': 'HIGH'
            }]
    
    async def _check_price_alerts(self, user) -> List[Dict]:
        """Check for significant price changes"""
        alerts = []
        
        # Sample price alert logic
        current_time = timezone.now()
        price_change = 18.5  # Sample price change percentage
        
        if abs(price_change) >= self.alert_thresholds['price_change']:
            alert_type = 'PRICE_SPIKE' if price_change > 0 else 'PRICE_DROP'
            alerts.append({
                'type': alert_type,
                'title': f'Significant Price Change Detected',
                'message': f'Route pricing changed by {price_change:.1f}% in the last 24 hours',
                'severity': 'HIGH' if abs(price_change) > 25 else 'MEDIUM',
                'route_affected': 'Europe-Asia Truck Routes',
                'percentage_change': price_change
            })
        
        return alerts
    
    async def _check_capacity_alerts(self, user) -> List[Dict]:
        """Check for capacity shortage alerts"""
        alerts = []
        
        # Sample capacity alert logic
        capacity_utilization = 92.3  # Sample capacity percentage
        
        if capacity_utilization >= self.alert_thresholds['capacity_shortage']:
            alerts.append({
                'type': 'CAPACITY_ALERT',
                'title': 'High Capacity Utilization Alert',
                'message': f'Transport capacity at {capacity_utilization:.1f}% - consider alternative routes',
                'severity': 'HIGH',
                'route_affected': 'US-Europe Air Routes',
                'current_value': capacity_utilization
            })
        
        return alerts
    
    async def _check_performance_alerts(self, user) -> List[Dict]:
        """Check for provider performance issues"""
        alerts = []
        
        # Sample performance alert logic
        performance_drop = 22.8  # Sample performance drop percentage
        
        if performance_drop >= self.alert_thresholds['performance_drop']:
            alerts.append({
                'type': 'PROVIDER_ISSUE',
                'title': 'Provider Performance Drop',
                'message': f'Provider performance decreased by {performance_drop:.1f}% this week',
                'severity': 'MEDIUM',
                'provider_affected': 'Global Express Logistics',
                'percentage_change': -performance_drop
            })
        
        return alerts
    
    async def _store_alert(self, user, alert_data: Dict):
        """Store alert in database"""
        try:
            await MarketIntelligenceAlert.objects.acreate(
                user=user,
                alert_type=alert_data['type'],
                title=alert_data['title'],
                message=alert_data['message'],
                severity=alert_data['severity'],
                route_affected=alert_data.get('route_affected', ''),
                provider_affected=alert_data.get('provider_affected', ''),
                current_value=Decimal(str(alert_data.get('current_value', 0))),
                percentage_change=Decimal(str(alert_data.get('percentage_change', 0))),
                alert_data=alert_data,
                expires_at=timezone.now() + timedelta(days=7)
            )
        except Exception as e:
            print(f"Failed to store alert: {e}")