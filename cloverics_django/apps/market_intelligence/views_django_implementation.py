from django.http import JsonResponse
from django.contrib.auth import get_user_model
from django.db.models import Avg, Count
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from django.utils import timezone
from asgiref.sync import sync_to_async
import json
from decimal import Decimal

from .models import MarketOverview, RouteAnalytics, ProviderPerformanceAnalysis, PriceBenchmark, MarketIntelligenceAlert

User = get_user_model()

@csrf_exempt
@require_http_methods(["GET"])
async def market_overview_api(request):
    """
    Django implementation of /api/market/overview endpoint
    Extracted from FastAPI lines 2079-2159 (81 lines)
    """
    try:
        # Get query parameters
        transport_type = request.GET.get('transport_type')
        origin_country = request.GET.get('origin_country')
        destination_country = request.GET.get('destination_country')
        
        # Import required models
        from shipments.models import ShippingRate, Shipment, TransportType
        
        # Build queryset with filters
        rate_queryset = ShippingRate.objects.all()
        if transport_type:
            rate_queryset = rate_queryset.filter(transport_type__type=transport_type)
        if origin_country:
            rate_queryset = rate_queryset.filter(route__origin_country=origin_country)
        if destination_country:
            rate_queryset = rate_queryset.filter(route__destination_country=destination_country)
        
        # Get real market data
        total_rates = await sync_to_async(rate_queryset.count)()
        avg_price = await sync_to_async(
            rate_queryset.aggregate(avg_price=Avg('base_price_per_kg')).get
        )('avg_price')
        
        # Get transport mode distribution
        transport_distribution = {}
        for transport in ['truck', 'ship', 'air', 'rail']:
            try:
                transport_type_obj = await sync_to_async(
                    TransportType.objects.get
                )(type=transport)
                count = await sync_to_async(
                    rate_queryset.filter(transport_type=transport_type_obj).count
                )()
                transport_distribution[transport] = count
            except TransportType.DoesNotExist:
                transport_distribution[transport] = 0
        
        # Get recent shipments for trends
        recent_shipments = await sync_to_async(list)(
            Shipment.objects.all().order_by('-created_at')[:50].values(
                'transport_type', 'total_price', 'weight_kg', 'status'
            )
        )
        
        # Build market data response
        market_data = {
            'overview': {
                'total_routes': total_routes,
                'avg_price_per_kg': float(avg_price) if avg_price else 0.0,
                'transport_distribution': transport_distribution,
                'recent_activity': len(recent_shipments)
            },
            'trends': {
                'popular_transport': max(transport_distribution.items(), key=lambda x: x[1])[0] if transport_distribution else 'truck',
                'market_activity': 'Active' if total_routes > 0 else 'Low',
                'price_trend': 'Stable'
            },
            'insights': {
                'market_competitiveness': 'High' if total_rates > 10 else 'Medium',
                'capacity_utilization': 'Good' if recent_shipments else 'Low'
            }
        }
        
        # Save to MarketOverview model for analytics
        await sync_to_async(MarketOverview.objects.create)(
            total_routes=total_rates,
            avg_price_per_kg=Decimal(str(avg_price)) if avg_price else Decimal('0.00'),
            transport_distribution=transport_distribution,
            market_activity='ACTIVE' if total_rates > 0 else 'LOW',
            market_competitiveness='High' if total_rates > 10 else 'Medium',
            capacity_utilization='Good' if recent_shipments else 'Low',
            recent_activity_count=len(recent_shipments)
        )
        
        return JsonResponse({
            'success': True,
            'data': market_data
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)

@csrf_exempt
@require_http_methods(["GET"])
async def route_analytics_api(request, origin_country, destination_country):
    """
    Django implementation of /api/market/route-analytics/{origin}/{destination} endpoint
    Extracted from FastAPI lines 2161-2184 (24 lines)
    """
    try:
        from shipments.models import ShippingRate, Route
        from django.db.models import Avg, Count, Min, Max, StdDev
        
        # Get route analytics from database
        routes = Route.objects.filter(
            origin_country=origin_country,
            destination_country=destination_country
        )
        
        if not await sync_to_async(routes.exists)():
            return JsonResponse({
                'success': False,
                'error': 'No routes found for this origin-destination pair'
            }, status=404)
        
        # Get shipping rates for this route
        rates = ShippingRate.objects.filter(route__in=routes)
        
        # Calculate analytics
        analytics = await sync_to_async(rates.aggregate)(
            avg_price=Avg('base_price_per_kg'),
            min_price=Min('base_price_per_kg'),
            max_price=Max('base_price_per_kg'),
            price_std=StdDev('base_price_per_kg'),
            provider_count=Count('logistics_provider', distinct=True),
            total_rates=Count('id')
        )
        
        route_data = {
            'route': f"{origin_country} → {destination_country}",
            'analytics': {
                'total_providers': analytics['provider_count'] or 0,
                'total_rates': analytics['total_rates'] or 0,
                'avg_price_per_kg': float(analytics['avg_price'] or 0),
                'min_price_per_kg': float(analytics['min_price'] or 0),
                'max_price_per_kg': float(analytics['max_price'] or 0),
                'price_variance': float(analytics['price_std'] or 0),
                'demand_level': 'High' if analytics['total_rates'] > 5 else 'Medium',
                'competition_level': 'High' if analytics['provider_count'] > 3 else 'Medium'
            },
            'market_insights': {
                'competitiveness': 'High' if analytics['provider_count'] > 3 else 'Medium',
                'price_stability': 'Stable' if (analytics['price_std'] or 0) < 0.5 else 'Volatile'
            }
        }
        
        # Save to RouteAnalytics model
        await sync_to_async(RouteAnalytics.objects.update_or_create)(
            origin_country=origin_country,
            destination_country=destination_country,
            defaults={
                'total_providers': analytics['provider_count'] or 0,
                'avg_price_per_kg': Decimal(str(analytics['avg_price'] or 0)),
                'price_variance': Decimal(str(analytics['price_std'] or 0)),
                'demand_level': 'High' if analytics['total_rates'] > 5 else 'Medium',
                'competition_level': 'High' if analytics['provider_count'] > 3 else 'Medium',
                'reliability_score': Decimal('85.00')
            }
        )
        
        return JsonResponse({
            'success': True,
            'data': route_data
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)

@csrf_exempt
@require_http_methods(["GET"])
async def provider_performance_api(request):
    """
    Django implementation of /api/market/provider-performance endpoint
    Extracted from FastAPI lines 2186-2209 (24 lines)
    """
    try:
        provider_id = request.GET.get('provider_id')
        
        if not provider_id:
            return JsonResponse({
                'success': False,
                'error': 'provider_id parameter is required'
            }, status=400)
        
        try:
            provider = await sync_to_async(User.objects.get)(
                id=provider_id,
                user_type='LOGISTICS_PROVIDER'
            )
        except User.DoesNotExist:
            return JsonResponse({
                'success': False,
                'error': 'Provider not found'
            }, status=404)
        
        # Get provider performance from shipments
        from shipments.models import Shipment
        from django.db.models import Q, Count, Avg
        
        shipments = Shipment.objects.filter(logistics_provider=provider)
        
        performance_data = await sync_to_async(shipments.aggregate)(
            total_shipments=Count('id'),
            completed_shipments=Count('id', filter=Q(status='delivered')),
            avg_rating=Avg('customer_rating')
        )
        
        # Calculate performance metrics
        total = performance_data['total_shipments'] or 0
        completed = performance_data['completed_shipments'] or 0
        on_time_rate = (completed / total * 100) if total > 0 else 0
        customer_satisfaction = float(performance_data['avg_rating'] or 4.0) * 20  # Convert to percentage
        
        overall_score = (on_time_rate + customer_satisfaction + 85) / 3  # Include service quality baseline
        
        performance_analysis = {
            'provider_info': {
                'id': provider.id,
                'company_name': provider.company_name,
                'email': provider.email
            },
            'performance_metrics': {
                'overall_score': round(overall_score, 2),
                'on_time_delivery': round(on_time_rate, 2),
                'customer_satisfaction': round(customer_satisfaction, 2),
                'total_shipments': total,
                'successful_deliveries': completed,
                'performance_grade': 'A' if overall_score >= 90 else 'B+' if overall_score >= 85 else 'B'
            },
            'analysis': {
                'strengths': ['Reliable delivery', 'Good communication'] if overall_score > 80 else ['Active service'],
                'improvement_areas': ['Response time'] if overall_score < 85 else [],
                'market_reputation': 'Excellent' if overall_score >= 90 else 'Good'
            }
        }
        
        # Save to ProviderPerformanceAnalysis model
        await sync_to_async(ProviderPerformanceAnalysis.objects.update_or_create)(
            provider=provider,
            analysis_period_start=timezone.now().date().replace(day=1),
            analysis_period_end=timezone.now().date(),
            defaults={
                'overall_score': Decimal(str(overall_score)),
                'on_time_delivery': Decimal(str(on_time_rate)),
                'customer_satisfaction': Decimal(str(customer_satisfaction)),
                'total_shipments': total,
                'successful_deliveries': completed,
                'performance_grade': 'A' if overall_score >= 90 else 'B+' if overall_score >= 85 else 'B',
                'strengths': ['Reliable delivery', 'Good communication'] if overall_score > 80 else ['Active service'],
                'improvement_areas': ['Response time'] if overall_score < 85 else [],
                'market_reputation': 'Excellent' if overall_score >= 90 else 'Good'
            }
        )
        
        return JsonResponse({
            'success': True,
            'data': performance_analysis
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)

@csrf_exempt
@require_http_methods(["GET"])
async def price_benchmarks_api(request):
    """
    Django implementation of /api/market/price-benchmarks endpoint
    Extracted from FastAPI lines 2211-2259 (49 lines)
    """
    try:
        transport_type = request.GET.get('transport_type')
        origin_country = request.GET.get('origin_country')
        destination_country = request.GET.get('destination_country')
        
        from shipments.models import ShippingRate
        from django.db.models import Min, Max, Avg, StdDev, Count
        
        # Build queryset with filters
        queryset = ShippingRate.objects.all()
        if transport_type:
            queryset = queryset.filter(transport_type__type=transport_type)
        if origin_country:
            queryset = queryset.filter(route__origin_country=origin_country)
        if destination_country:
            queryset = queryset.filter(route__destination_country=destination_country)
        
        # Calculate price benchmarks
        benchmarks = await sync_to_async(queryset.aggregate)(
            min_price=Min('base_price_per_kg'),
            max_price=Max('base_price_per_kg'),
            avg_price=Avg('base_price_per_kg'),
            price_std=StdDev('base_price_per_kg'),
            sample_size=Count('id')
        )
        
        benchmark_data = {
            'filters': {
                'transport_type': transport_type,
                'origin_country': origin_country,
                'destination_country': destination_country
            },
            'pricing': {
                'min_price_per_kg': float(benchmarks['min_price'] or 0),
                'max_price_per_kg': float(benchmarks['max_price'] or 0),
                'avg_price_per_kg': float(benchmarks['avg_price'] or 0),
                'price_variance': float(benchmarks['price_std'] or 0),
                'sample_size': benchmarks['sample_size'] or 0
            },
            'market_analysis': {
                'competitiveness': 'High' if benchmarks['sample_size'] > 10 else 'Medium',
                'price_stability': 'Stable' if (benchmarks['price_std'] or 0) < 0.5 else 'Volatile',
                'market_position': 'Market Average'
            },
            'recommendations': {
                'pricing_strategy': 'Competitive pricing recommended' if benchmarks['sample_size'] > 5 else 'Market leader pricing possible',
                'market_entry': 'Good opportunity' if benchmarks['sample_size'] < 15 else 'Saturated market'
            }
        }
        
        # Save to PriceBenchmark model
        if benchmarks['sample_size'] > 0:
            await sync_to_async(PriceBenchmark.objects.create)(
                transport_type=transport_type or 'all',
                origin_country=origin_country,
                destination_country=destination_country,
                min_price_per_kg=Decimal(str(benchmarks['min_price'] or 0)),
                max_price_per_kg=Decimal(str(benchmarks['max_price'] or 0)),
                avg_price_per_kg=Decimal(str(benchmarks['avg_price'] or 0)),
                standard_deviation=Decimal(str(benchmarks['price_std'] or 0)),
                sample_size=benchmarks['sample_size'] or 0,
                market_position='Market Average',
                price_trend_30d='Stable'
            )
        
        return JsonResponse({
            'success': True,
            'data': benchmark_data
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)