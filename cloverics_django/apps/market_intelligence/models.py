from django.db import models
from django.conf import settings
from django.utils import timezone
import uuid


class MarketOverview(models.Model):
    """Global market overview and statistics"""
    overview_id = models.CharField(max_length=50, unique=True, default=uuid.uuid4)
    date = models.DateTimeField(default=timezone.now)
    
    # Global Statistics
    total_active_routes = models.IntegerField(default=0)
    total_providers = models.IntegerField(default=0)
    total_volume_tons = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    total_value_usd = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    
    # Market Performance
    avg_transit_time = models.DecimalField(max_digits=8, decimal_places=2, default=0)
    avg_cost_per_kg = models.DecimalField(max_digits=10, decimal_places=4, default=0)
    market_volatility_index = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    demand_growth_rate = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    
    # Transport Mode Breakdown
    truck_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    rail_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    air_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    ship_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    
    # Market Insights
    top_routes = models.JSONField(default=list)
    price_trends = models.JSONField(default=dict)
    capacity_utilization = models.JSONField(default=dict)
    seasonal_patterns = models.JSONField(default=dict)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'market_intelligence_overview'
        ordering = ['-date']
        indexes = [
            models.Index(fields=['date']),
            models.Index(fields=['overview_id']),
            models.Index(fields=['created_at']),
        ]


class RouteAnalytics(models.Model):
    """Route-specific analytics and performance metrics"""
    analytics_id = models.CharField(max_length=50, unique=True, default=uuid.uuid4)
    route_name = models.CharField(max_length=200)
    origin_country = models.CharField(max_length=100)
    origin_city = models.CharField(max_length=100)
    destination_country = models.CharField(max_length=100)
    destination_city = models.CharField(max_length=100)
    
    # Route Performance
    total_shipments = models.IntegerField(default=0)
    total_volume_kg = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    total_value_usd = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    success_rate = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    
    # Pricing Analytics
    avg_price_per_kg = models.DecimalField(max_digits=10, decimal_places=4, default=0)
    min_price_per_kg = models.DecimalField(max_digits=10, decimal_places=4, default=0)
    max_price_per_kg = models.DecimalField(max_digits=10, decimal_places=4, default=0)
    price_volatility = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    
    # Service Performance
    avg_transit_days = models.DecimalField(max_digits=6, decimal_places=2, default=0)
    on_time_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    damage_rate = models.DecimalField(max_digits=5, decimal_places=4, default=0)
    customer_satisfaction = models.DecimalField(max_digits=3, decimal_places=2, default=0)
    
    # Market Position
    market_share = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    competitive_index = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    growth_trend = models.CharField(max_length=20, default='stable')
    
    # Historical Data
    monthly_trends = models.JSONField(default=dict)
    seasonal_patterns = models.JSONField(default=dict)
    demand_forecast = models.JSONField(default=dict)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'market_intelligence_route_analytics'
        ordering = ['-total_shipments', '-total_value_usd']
        indexes = [
            models.Index(fields=['route_name']),
            models.Index(fields=['origin_country', 'destination_country']),
            models.Index(fields=['success_rate']),
            models.Index(fields=['avg_price_per_kg']),
            models.Index(fields=['created_at']),
        ]


class ProviderPerformanceAnalysis(models.Model):
    """Comprehensive provider performance analytics and rankings"""
    analysis_id = models.CharField(max_length=50, unique=True, default=uuid.uuid4)
    provider_id = models.IntegerField()
    provider_name = models.CharField(max_length=200)
    provider_type = models.CharField(max_length=50)  # logistics, freight_forwarder, carrier
    
    # Performance Metrics
    total_shipments_handled = models.IntegerField(default=0)
    total_revenue_generated = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    success_rate = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    on_time_delivery_rate = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    
    # Quality Metrics
    customer_rating = models.DecimalField(max_digits=3, decimal_places=2, default=0)
    damage_rate = models.DecimalField(max_digits=5, decimal_places=4, default=0)
    claims_rate = models.DecimalField(max_digits=5, decimal_places=4, default=0)
    response_time_hours = models.DecimalField(max_digits=6, decimal_places=2, default=0)
    
    # Market Position
    market_share = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    competitive_ranking = models.IntegerField(default=0)
    growth_rate = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    
    # Operational Metrics
    capacity_utilization = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    fleet_size = models.IntegerField(default=0)
    coverage_countries = models.IntegerField(default=0)
    service_routes = models.IntegerField(default=0)
    
    # Financial Performance
    profit_margin = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    cost_efficiency_index = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    pricing_competitiveness = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    
    # Analytics Data
    performance_trends = models.JSONField(default=dict)
    route_performance = models.JSONField(default=dict)
    customer_segments = models.JSONField(default=dict)
    improvement_recommendations = models.JSONField(default=list)
    
    analysis_date = models.DateTimeField(default=timezone.now)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'market_intelligence_provider_analysis'
        ordering = ['-competitive_ranking', '-success_rate']
        indexes = [
            models.Index(fields=['provider_id']),
            models.Index(fields=['provider_name']),
            models.Index(fields=['competitive_ranking']),
            models.Index(fields=['success_rate']),
            models.Index(fields=['market_share']),
            models.Index(fields=['analysis_date']),
        ]


class PriceBenchmark(models.Model):
    """Price benchmarking and market rate analysis"""
    benchmark_id = models.CharField(max_length=50, unique=True, default=uuid.uuid4)
    route_signature = models.CharField(max_length=300)  # origin-destination-transport_mode
    
    # Route Details
    origin_country = models.CharField(max_length=100)
    origin_city = models.CharField(max_length=100)
    destination_country = models.CharField(max_length=100)
    destination_city = models.CharField(max_length=100)
    transport_mode = models.CharField(max_length=50)
    
    # Price Analysis
    market_rate_per_kg = models.DecimalField(max_digits=10, decimal_places=4, default=0)
    lowest_rate_per_kg = models.DecimalField(max_digits=10, decimal_places=4, default=0)
    highest_rate_per_kg = models.DecimalField(max_digits=10, decimal_places=4, default=0)
    median_rate_per_kg = models.DecimalField(max_digits=10, decimal_places=4, default=0)
    
    # Market Statistics
    provider_count = models.IntegerField(default=0)
    quote_volume = models.IntegerField(default=0)
    price_volatility = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    market_competitiveness = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    
    # Service Levels
    standard_service_rate = models.DecimalField(max_digits=10, decimal_places=4, default=0)
    express_service_rate = models.DecimalField(max_digits=10, decimal_places=4, default=0)
    urgent_service_rate = models.DecimalField(max_digits=10, decimal_places=4, default=0)
    
    # Historical Trends
    price_trend_7d = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    price_trend_30d = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    price_trend_90d = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    
    # Market Intelligence
    seasonal_adjustment = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    demand_supply_ratio = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    market_recommendations = models.JSONField(default=list)
    
    benchmark_date = models.DateTimeField(default=timezone.now)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'market_intelligence_price_benchmark'
        ordering = ['-benchmark_date', 'route_signature']
        indexes = [
            models.Index(fields=['route_signature']),
            models.Index(fields=['transport_mode']),
            models.Index(fields=['market_rate_per_kg']),
            models.Index(fields=['benchmark_date']),
            models.Index(fields=['origin_country', 'destination_country']),
        ]


class MarketIntelligenceAlert(models.Model):
    """Market intelligence alerts and notifications"""
    ALERT_TYPES = [
        ('PRICE_DROP', 'Significant Price Drop'),
        ('PRICE_SPIKE', 'Price Spike Alert'),
        ('NEW_ROUTE', 'New Route Available'),
        ('CAPACITY_ALERT', 'Capacity Shortage'),
        ('MARKET_SHIFT', 'Market Trend Shift'),
        ('PROVIDER_ISSUE', 'Provider Performance Issue'),
        ('OPPORTUNITY', 'Market Opportunity'),
        ('DISRUPTION', 'Market Disruption')
    ]
    
    SEVERITY_LEVELS = [
        ('LOW', 'Low Priority'),
        ('MEDIUM', 'Medium Priority'),
        ('HIGH', 'High Priority'),
        ('CRITICAL', 'Critical Alert')
    ]
    
    alert_id = models.CharField(max_length=50, unique=True, default=uuid.uuid4)
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='market_alerts')
    alert_type = models.CharField(max_length=20, choices=ALERT_TYPES)
    severity = models.CharField(max_length=10, choices=SEVERITY_LEVELS, default='MEDIUM')
    
    # Alert Content
    title = models.CharField(max_length=200)
    message = models.TextField()
    route_affected = models.CharField(max_length=200, blank=True)
    provider_affected = models.CharField(max_length=200, blank=True)
    
    # Alert Data
    previous_value = models.DecimalField(max_digits=12, decimal_places=4, null=True, blank=True)
    current_value = models.DecimalField(max_digits=12, decimal_places=4, null=True, blank=True)
    percentage_change = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    
    # Alert Management
    is_read = models.BooleanField(default=False)
    is_actionable = models.BooleanField(default=False)
    action_taken = models.TextField(blank=True)
    expires_at = models.DateTimeField(null=True, blank=True)
    
    # Metadata
    alert_data = models.JSONField(default=dict)
    created_at = models.DateTimeField(auto_now_add=True)
    read_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        db_table = 'market_intelligence_alerts'
        ordering = ['-created_at', '-severity']
        indexes = [
            models.Index(fields=['user']),
            models.Index(fields=['alert_type']),
            models.Index(fields=['severity']),
            models.Index(fields=['is_read']),
            models.Index(fields=['created_at']),
            models.Index(fields=['expires_at']),
        ]

    def mark_as_read(self):
        """Mark alert as read"""
        self.is_read = True
        self.read_at = timezone.now()
        self.save(update_fields=['is_read', 'read_at'])