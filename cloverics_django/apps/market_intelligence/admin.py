from django.contrib import admin
from .models import MarketOverview, RouteAnalytics, ProviderPerformanceAnalysis, PriceBenchmark, MarketIntelligenceAlert


@admin.register(MarketOverview)
class MarketOverviewAdmin(admin.ModelAdmin):
    list_display = [
        'overview_id', 'date', 'total_active_routes', 'total_providers',
        'market_volatility_index', 'demand_growth_rate', 'created_at'
    ]
    list_filter = ['date', 'market_volatility_index', 'demand_growth_rate']
    search_fields = ['overview_id']
    ordering = ['-date']
    readonly_fields = ['overview_id', 'created_at', 'updated_at']
    
    fieldsets = (
        ('Overview Information', {
            'fields': ('overview_id', 'date')
        }),
        ('Market Statistics', {
            'fields': (
                'total_active_routes', 'total_providers', 'total_volume_tons',
                'total_value_usd', 'avg_transit_time', 'avg_cost_per_kg'
            )
        }),
        ('Market Performance', {
            'fields': ('market_volatility_index', 'demand_growth_rate')
        }),
        ('Transport Breakdown', {
            'fields': ('truck_percentage', 'rail_percentage', 'air_percentage', 'ship_percentage')
        }),
        ('Insights Data', {
            'fields': ('top_routes', 'price_trends', 'capacity_utilization', 'seasonal_patterns'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )


@admin.register(RouteAnalytics)
class RouteAnalyticsAdmin(admin.ModelAdmin):
    list_display = [
        'route_name', 'origin_country', 'destination_country',
        'total_shipments', 'success_rate', 'avg_price_per_kg', 'market_share'
    ]
    list_filter = ['origin_country', 'destination_country', 'growth_trend']
    search_fields = ['route_name', 'origin_city', 'destination_city']
    ordering = ['-total_shipments', '-success_rate']
    readonly_fields = ['analytics_id', 'created_at', 'updated_at']
    
    fieldsets = (
        ('Route Information', {
            'fields': (
                'analytics_id', 'route_name', 'origin_country', 'origin_city',
                'destination_country', 'destination_city'
            )
        }),
        ('Performance Metrics', {
            'fields': (
                'total_shipments', 'total_volume_kg', 'total_value_usd', 'success_rate'
            )
        }),
        ('Pricing Analytics', {
            'fields': (
                'avg_price_per_kg', 'min_price_per_kg', 'max_price_per_kg', 'price_volatility'
            )
        }),
        ('Service Performance', {
            'fields': (
                'avg_transit_days', 'on_time_percentage', 'damage_rate', 'customer_satisfaction'
            )
        }),
        ('Market Position', {
            'fields': ('market_share', 'competitive_index', 'growth_trend')
        }),
        ('Analytics Data', {
            'fields': ('monthly_trends', 'seasonal_patterns', 'demand_forecast'),
            'classes': ('collapse',)
        })
    )


@admin.register(ProviderPerformanceAnalysis)
class ProviderPerformanceAnalysisAdmin(admin.ModelAdmin):
    list_display = [
        'provider_name', 'provider_type', 'competitive_ranking',
        'success_rate', 'market_share', 'customer_rating', 'analysis_date'
    ]
    list_filter = ['provider_type', 'competitive_ranking', 'analysis_date']
    search_fields = ['provider_name', 'provider_id']
    ordering = ['competitive_ranking', '-success_rate']
    readonly_fields = ['analysis_id', 'created_at', 'updated_at']
    
    fieldsets = (
        ('Provider Information', {
            'fields': ('analysis_id', 'provider_id', 'provider_name', 'provider_type')
        }),
        ('Performance Metrics', {
            'fields': (
                'total_shipments_handled', 'total_revenue_generated',
                'success_rate', 'on_time_delivery_rate'
            )
        }),
        ('Quality Metrics', {
            'fields': (
                'customer_rating', 'damage_rate', 'claims_rate', 'response_time_hours'
            )
        }),
        ('Market Position', {
            'fields': ('market_share', 'competitive_ranking', 'growth_rate')
        }),
        ('Operational Metrics', {
            'fields': (
                'capacity_utilization', 'fleet_size', 'coverage_countries', 'service_routes'
            )
        }),
        ('Financial Performance', {
            'fields': ('profit_margin', 'cost_efficiency_index', 'pricing_competitiveness')
        }),
        ('Analytics Data', {
            'fields': (
                'performance_trends', 'route_performance', 'customer_segments',
                'improvement_recommendations'
            ),
            'classes': ('collapse',)
        }),
        ('Analysis Info', {
            'fields': ('analysis_date', 'created_at', 'updated_at')
        })
    )


@admin.register(PriceBenchmark)
class PriceBenchmarkAdmin(admin.ModelAdmin):
    list_display = [
        'route_signature', 'transport_mode', 'market_rate_per_kg',
        'provider_count', 'price_volatility', 'benchmark_date'
    ]
    list_filter = ['transport_mode', 'origin_country', 'destination_country', 'benchmark_date']
    search_fields = ['route_signature', 'origin_city', 'destination_city']
    ordering = ['-benchmark_date', 'route_signature']
    readonly_fields = ['benchmark_id', 'created_at', 'updated_at']
    
    fieldsets = (
        ('Benchmark Information', {
            'fields': ('benchmark_id', 'route_signature', 'benchmark_date')
        }),
        ('Route Details', {
            'fields': (
                'origin_country', 'origin_city', 'destination_country',
                'destination_city', 'transport_mode'
            )
        }),
        ('Price Analysis', {
            'fields': (
                'market_rate_per_kg', 'lowest_rate_per_kg', 'highest_rate_per_kg',
                'median_rate_per_kg'
            )
        }),
        ('Market Statistics', {
            'fields': ('provider_count', 'quote_volume', 'price_volatility', 'market_competitiveness')
        }),
        ('Service Levels', {
            'fields': ('standard_service_rate', 'express_service_rate', 'urgent_service_rate')
        }),
        ('Trends', {
            'fields': ('price_trend_7d', 'price_trend_30d', 'price_trend_90d')
        }),
        ('Market Intelligence', {
            'fields': ('seasonal_adjustment', 'demand_supply_ratio', 'market_recommendations'),
            'classes': ('collapse',)
        })
    )


@admin.register(MarketIntelligenceAlert)
class MarketIntelligenceAlertAdmin(admin.ModelAdmin):
    list_display = [
        'alert_id', 'user', 'alert_type', 'severity', 'title',
        'is_read', 'created_at', 'expires_at'
    ]
    list_filter = ['alert_type', 'severity', 'is_read', 'created_at']
    search_fields = ['alert_id', 'title', 'user__email']
    ordering = ['-created_at', 'severity']
    readonly_fields = ['alert_id', 'created_at', 'read_at']
    
    fieldsets = (
        ('Alert Information', {
            'fields': ('alert_id', 'user', 'alert_type', 'severity')
        }),
        ('Alert Content', {
            'fields': ('title', 'message', 'route_affected', 'provider_affected')
        }),
        ('Alert Data', {
            'fields': ('previous_value', 'current_value', 'percentage_change')
        }),
        ('Alert Management', {
            'fields': ('is_read', 'is_actionable', 'action_taken', 'expires_at')
        }),
        ('Metadata', {
            'fields': ('alert_data',),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'read_at')
        })
    )
    
    actions = ['mark_as_read', 'mark_as_unread']
    
    def mark_as_read(self, request, queryset):
        updated = queryset.update(is_read=True, read_at=timezone.now())
        self.message_user(request, f'{updated} alerts marked as read.')
    mark_as_read.short_description = 'Mark selected alerts as read'
    
    def mark_as_unread(self, request, queryset):
        updated = queryset.update(is_read=False, read_at=None)
        self.message_user(request, f'{updated} alerts marked as unread.')
    mark_as_unread.short_description = 'Mark selected alerts as unread'