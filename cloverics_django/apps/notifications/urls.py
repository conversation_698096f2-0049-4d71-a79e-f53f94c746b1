from django.urls import path
from . import views_django_implementation as views

app_name = 'notifications'

urlpatterns = [
    # Main notifications page
    path('', views.notifications_view, name='notifications_page'),
    
    # Notification Management
    path('api/notifications/', views.get_notifications, name='get_notifications'),
    path('api/notifications/<int:notification_id>/read/', views.mark_notification_read, name='mark_notification_read'),
    path('api/notifications/mark-all-read/', views.mark_all_notifications_read, name='mark_all_notifications_read'),
    path('api/notifications/stats/', views.get_notification_stats, name='get_notification_stats'),
    
    # Notification Preferences
    path('api/preferences/update/', views.update_notification_preferences, name='update_preferences'),
]