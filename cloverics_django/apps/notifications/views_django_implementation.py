from django.shortcuts import render
from django.http import JsonResponse
from django.contrib.auth.decorators import login_required
from django.views.decorators.http import require_http_methods
from django.db import transaction
from django.utils import timezone
from asgiref.sync import sync_to_async
import json

from .models import Notification, CrossUserEvent
from django.contrib.auth import get_user_model
from apps.core_api.models import UserNotificationPreference

User = get_user_model()

# ============================================================================
# DJANGO IMPLEMENTATION OF NOTIFICATIONS SYSTEM
# Extracted from FastAPI Lines 244-291 and enhanced
# ============================================================================

@sync_to_async
@transaction.atomic
def notify_users_django(event_type: str, source_user, target_users: list, shipment_id: int = None, data: dict = None):
    """
    Django implementation of the central notification system
    Enhanced version of the original FastAPI notify_users function
    """
    try:
        notifications_created = []
        
        # Create notifications for each target user
        for user in target_users:
            notification = Notification.objects.create(
                user=user,
                title=f"Shipment Update: {event_type.replace('_', ' ').title()}",
                message=f"Update from {source_user.company_name or getattr(source_user, 'username', source_user.email)}",
                notification_type='SYSTEM',
                related_object_type=event_type,
                related_object_id=str(shipment_id) if shipment_id else None,
                is_read=False
            )
            notifications_created.append(notification)
        
        # Log to cross-user events audit trail
        target_user_ids = [user.id for user in target_users]
        
        cross_event = CrossUserEvent.objects.create(
            event_type=event_type,
            source_user=source_user,
            source_user_role=source_user.user_type,
            target_user_ids=target_user_ids,
            shipment_id=shipment_id,
            data=data or {},
            processed=False
        )
        
        print(f"✅ Django notification created: {event_type} from {source_user.user_type} to {len(target_users)} users")
        
        # WebSocket notifications disabled
        
        return True
        
    except Exception as e:
        print(f"❌ Django notification creation failed: {e}")
        return False

# WebSocket notification functionality removed

@login_required
@require_http_methods(["GET"])
def get_notifications(request):
    """
    Get user notifications with pagination and filtering
    """
    try:
        user = request.user
        page = int(request.GET.get('page', 1))
        limit = int(request.GET.get('limit', 20))
        notification_type = request.GET.get('type', None)
        is_read = request.GET.get('is_read', None)
        
        # Build query
        notifications = Notification.objects.filter(user=user)
        
        if notification_type:
            notifications = notifications.filter(notification_type=notification_type)
        
        if is_read is not None:
            notifications = notifications.filter(is_read=is_read.lower() == 'true')
        
        # Pagination
        offset = (page - 1) * limit
        total_count = notifications.count()
        notifications = notifications[offset:offset + limit]
        
        # Serialize
        notifications_data = []
        for notification in notifications:
            notifications_data.append({
                'id': notification.id,
                'title': notification.title,
                'message': notification.message,
                'notification_type': notification.notification_type,
                'is_read': notification.is_read,
                'created_at': notification.created_at.isoformat(),
                'read_at': notification.read_at.isoformat() if notification.read_at else None,
                'related_object_type': notification.related_object_type,
                'related_object_id': notification.related_object_id,
            })
        
        return JsonResponse({
            'status': 'success',
            'data': {
                'notifications': notifications_data,
                'total_count': total_count,
                'page': page,
                'has_next': offset + limit < total_count,
                'unread_count': Notification.objects.filter(user=user, is_read=False).count()
            }
        })
        
    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=500)

@login_required
@require_http_methods(["POST"])
def mark_notification_read(request, notification_id):
    """
    Mark a notification as read
    """
    try:
        notification = Notification.objects.get(
            id=notification_id,
            user=request.user
        )
        
        if not notification.is_read:
            notification.is_read = True
            notification.read_at = timezone.now()
            notification.save()
        
        return JsonResponse({
            'status': 'success',
            'message': 'Notification marked as read'
        })
        
    except Notification.DoesNotExist:
        return JsonResponse({
            'status': 'error',
            'message': 'Notification not found'
        }, status=404)
    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=500)

@login_required
@require_http_methods(["POST"])
def mark_all_notifications_read(request):
    """
    Mark all user notifications as read
    """
    try:
        updated_count = Notification.objects.filter(
            user=request.user,
            is_read=False
        ).update(
            is_read=True,
            read_at=timezone.now()
        )
        
        return JsonResponse({
            'status': 'success',
            'message': f'{updated_count} notifications marked as read'
        })
        
    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=500)

@login_required
@require_http_methods(["GET"])
def get_notification_stats(request):
    """
    Get notification statistics for user dashboard
    """
    try:
        user = request.user
        
        total_notifications = Notification.objects.filter(user=user).count()
        unread_notifications = Notification.objects.filter(user=user, is_read=False).count()
        
        # Get counts by type
        type_counts = {}
        for notification_type, _ in Notification.NOTIFICATION_TYPES:
            count = Notification.objects.filter(
                user=user,
                notification_type=notification_type
            ).count()
            type_counts[notification_type] = count
        
        # Recent activity (last 7 days)
        from datetime import datetime, timedelta
        week_ago = timezone.now() - timedelta(days=7)
        recent_count = Notification.objects.filter(
            user=user,
            created_at__gte=week_ago
        ).count()
        
        return JsonResponse({
            'status': 'success',
            'data': {
                'total_notifications': total_notifications,
                'unread_notifications': unread_notifications,
                'read_notifications': total_notifications - unread_notifications,
                'recent_activity': recent_count,
                'type_breakdown': type_counts,
                'read_percentage': round((total_notifications - unread_notifications) / max(total_notifications, 1) * 100, 1)
            }
        })
        
    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=500)

# ============================================================================
# WEBSOCKET CONNECTION MANAGEMENT - REMOVED
# ============================================================================

# ============================================================================
# REAL-TIME EVENT PROCESSING
# ============================================================================

@sync_to_async
def process_pending_cross_user_events():
    """
    Process pending cross-user events for real-time synchronization
    """
    try:
        pending_events = CrossUserEvent.objects.filter(processed=False)
        
        for event in pending_events:
            # Mark as processed
            event.processed = True
            event.save()
            
            print(f"Processed cross-user event: {event.event_type}")
        
        return len(pending_events)
        
    except Exception as e:
        print(f"Cross-user event processing failed: {e}")
        return 0

# ============================================================================
# NOTIFICATION TEMPLATE VIEWS
# ============================================================================

def notifications_view(request):
    """Main notifications page view with real data and pagination"""
    try:
        user = request.user
        page = int(request.GET.get('page', 1))
        limit = int(request.GET.get('limit', 20))
        notification_type = request.GET.get('type', None)
        is_read = request.GET.get('is_read', None)
        
        # Build query
        notifications = Notification.objects.filter(user=user)
        
        if notification_type:
            notifications = notifications.filter(notification_type=notification_type)
        
        if is_read is not None:
            notifications = notifications.filter(is_read=is_read.lower() == 'true')
        
        # Pagination
        offset = (page - 1) * limit
        total_count = notifications.count()
        notifications = notifications.order_by('-created_at')[offset:offset + limit]
        
        # Get unread count
        unread_count = Notification.objects.filter(user=user, is_read=False).count()
        
        # Get notification statistics
        total_notifications = Notification.objects.filter(user=user).count()
        read_notifications = total_notifications - unread_count
        
        # Get counts by type
        type_counts = {}
        for notification_type_code, _ in Notification.NOTIFICATION_TYPES:
            count = Notification.objects.filter(
                user=user,
                notification_type=notification_type_code
            ).count()
            type_counts[notification_type_code] = count
        
        # Recent activity (last 7 days)
        from datetime import timedelta
        week_ago = timezone.now() - timedelta(days=7)
        recent_count = Notification.objects.filter(
            user=user,
            created_at__gte=week_ago
        ).count()
        
        # Calculate read percentage
        read_percentage = round((read_notifications / max(total_notifications, 1)) * 100, 1)
        
        # Calculate average notifications per day (last 30 days)
        month_ago = timezone.now() - timedelta(days=30)
        monthly_count = Notification.objects.filter(
            user=user,
            created_at__gte=month_ago
        ).count()
        avg_per_day = round(monthly_count / 30, 1)
        
        # Get user notification preferences
        try:
            notification_preferences = UserNotificationPreference.objects.get(user=user)
        except UserNotificationPreference.DoesNotExist:
            # Create default preferences if they don't exist
            notification_preferences = UserNotificationPreference.objects.create(user=user)
        
        # Prepare context data
        notifications_data = []
        for notification in notifications:
            notifications_data.append({
                'id': notification.id,
                'title': notification.title,
                'message': notification.message,
                'notification_type': notification.notification_type,
                'is_read': notification.is_read,
                'created_at': notification.created_at,
                'read_at': notification.read_at,
                'related_object_type': notification.related_object_type,
                'related_object_id': notification.related_object_id,
            })
        
        # Calculate pagination info
        total_pages = (total_count + limit - 1) // limit
        has_next = page < total_pages
        has_previous = page > 1
        
        context = {
            'title': 'Notifications - Cloverics',
            'user': user,
            'notifications': notifications_data,
            'pagination': {
                'current_page': page,
                'total_pages': total_pages,
                'total_count': total_count,
                'has_next': has_next,
                'has_previous': has_previous,
                'limit': limit,
            },
            'unread_count': unread_count,
            'filters': {
                'type': notification_type,
                'is_read': is_read,
            },
            'statistics': {
                'total_notifications': total_notifications,
                'unread_notifications': unread_count,
                'read_notifications': read_notifications,
                'recent_activity': recent_count,
                'type_breakdown': type_counts,
                'read_percentage': read_percentage,
                'avg_per_day': avg_per_day,
            },
            'notification_preferences': notification_preferences
        }
        
        return render(request, 'notifications/notifications.html', context)
        
    except Exception as e:
        # Show error screen with retry option instead of mock data
        context = {
            'title': 'Notifications - Cloverics',
            'error': {
                'message': 'Unable to load notifications at this time.',
                'details': str(e),
                'retry_url': request.get_full_path(),
            }
        }
        return render(request, 'notifications/error.html', context, status=500)

@login_required
@require_http_methods(["POST"])
def update_notification_preferences(request):
    """Update user notification preferences"""
    try:
        user = request.user
        data = json.loads(request.body)
        
        # Get or create notification preferences
        preferences, created = UserNotificationPreference.objects.get_or_create(
            user=user,
            defaults={
                'email_notifications': True,
                'sms_notifications': False,
                'push_notifications': True,
                'marketing_emails': False,
                'shipment_updates': True,
                'quote_notifications': True,
                'payment_alerts': True,
                'security_alerts': True,
                'notification_frequency': 'immediate'
            }
        )
        
        # Update preferences based on form data
        if 'email_notifications' in data:
            preferences.email_notifications = data['email_notifications']
        if 'sms_notifications' in data:
            preferences.sms_notifications = data['sms_notifications']
        if 'push_notifications' in data:
            preferences.push_notifications = data['push_notifications']
        if 'marketing_emails' in data:
            preferences.marketing_emails = data['marketing_emails']
        if 'shipment_updates' in data:
            preferences.shipment_updates = data['shipment_updates']
        if 'quote_notifications' in data:
            preferences.quote_notifications = data['quote_notifications']
        if 'payment_alerts' in data:
            preferences.payment_alerts = data['payment_alerts']
        if 'security_alerts' in data:
            preferences.security_alerts = data['security_alerts']
        if 'notification_frequency' in data:
            preferences.notification_frequency = data['notification_frequency']
        
        preferences.save()
        
        return JsonResponse({
            'status': 'success',
            'message': 'Notification preferences updated successfully',
            'preferences': {
                'email_notifications': preferences.email_notifications,
                'sms_notifications': preferences.sms_notifications,
                'push_notifications': preferences.push_notifications,
                'marketing_emails': preferences.marketing_emails,
                'shipment_updates': preferences.shipment_updates,
                'quote_notifications': preferences.quote_notifications,
                'payment_alerts': preferences.payment_alerts,
                'security_alerts': preferences.security_alerts,
                'notification_frequency': preferences.notification_frequency,
            }
        })
        
    except json.JSONDecodeError:
        return JsonResponse({
            'status': 'error',
            'message': 'Invalid JSON data'
        }, status=400)
    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=500)
        