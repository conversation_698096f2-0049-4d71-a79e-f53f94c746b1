# Generated by Django 5.2.1 on 2025-08-07 12:45

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='CrossUserEvent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('event_type', models.CharField(choices=[('customs_declaration_submitted', 'Customs Declaration Submitted'), ('customs_declaration_approved', 'Customs Declaration Approved'), ('customs_declaration_rejected', 'Customs Declaration Rejected'), ('driver_assigned', 'Driver Assigned'), ('driver_location_updated', 'Driver Location Updated'), ('shipment_status_updated', 'Shipment Status Updated'), ('payment_processed', 'Payment Processed'), ('insurance_claim_filed', 'Insurance Claim Filed')], max_length=50)),
                ('source_user_role', models.CharField(max_length=50)),
                ('target_user_ids', models.JSONField()),
                ('shipment_id', models.IntegerField(blank=True, null=True)),
                ('data', models.JSONField(default=dict)),
                ('processed', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('source_user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='triggered_events', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'notifications_cross_user_events',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['event_type', 'processed'], name='notificatio_event_t_1ee936_idx'), models.Index(fields=['created_at'], name='notificatio_created_4fcb33_idx')],
            },
        ),
        migrations.CreateModel(
            name='Notification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=255)),
                ('message', models.TextField()),
                ('notification_type', models.CharField(choices=[('SYSTEM', 'System'), ('SHIPMENT', 'Shipment'), ('PAYMENT', 'Payment'), ('CUSTOMS', 'Customs'), ('DRIVER', 'Driver'), ('INSURANCE', 'Insurance')], default='SYSTEM', max_length=20)),
                ('priority', models.CharField(choices=[('LOW', 'Low'), ('MEDIUM', 'Medium'), ('HIGH', 'High')], db_index=True, default='MEDIUM', max_length=10)),
                ('related_object_type', models.CharField(blank=True, max_length=50, null=True)),
                ('related_object_id', models.CharField(blank=True, max_length=50, null=True)),
                ('is_read', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('read_at', models.DateTimeField(blank=True, null=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='user_notifications', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'apps_notifications',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['user', 'is_read'], name='apps_notifi_user_id_6d3838_idx'), models.Index(fields=['notification_type', 'priority'], name='apps_notifi_notific_dd8d6e_idx'), models.Index(fields=['related_object_type', 'related_object_id'], name='apps_notifi_related_2f66a2_idx'), models.Index(fields=['created_at'], name='apps_notifi_created_9cfbd2_idx')],
            },
        ),
    ]
