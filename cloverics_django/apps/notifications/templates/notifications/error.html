{% extends "base.html" %}
{% load translation_tags %}

{% block content %}
<div class="page-container">
    <div class="page-header">
        <h1>{% translate 'notifications' %}</h1>
        <p>{% translate 'manage_your_notifications_and_alerts' %}</p>
    </div>

    <!-- Error Screen -->
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card border-danger">
                <div class="card-body text-center py-5">
                    <!-- Error Icon -->
                    <div class="mb-4">
                        <i class="fas fa-exclamation-triangle fa-4x text-danger"></i>
                    </div>
                    
                    <!-- Error Message -->
                    <h3 class="text-danger mb-3">{% translate 'error_loading_notifications' %}</h3>
                    <p class="text-muted mb-4">{{ error.message }}</p>
                    
                    <!-- Error Details (for debugging) -->
                    {% if error.details %}
                    <div class="alert alert-light border mb-4">
                        <small class="text-muted">
                            <strong>{% translate 'error_details' %}:</strong><br>
                            <code>{{ error.details }}</code>
                        </small>
                    </div>
                    {% endif %}
                    
                    <!-- Action Buttons -->
                    <div class="d-flex justify-content-center gap-3">
                        <button class="btn btn-primary" onclick="retryLoad()">
                            <i class="fas fa-redo"></i> {% translate 'retry' %}
                        </button>
                        <a href="{% url 'notifications:notifications_page' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-home"></i> {% translate 'go_to_notifications' %}
                        </a>
                    </div>
                    
                    <!-- Additional Help -->
                    <div class="mt-4">
                        <small class="text-muted">
                            {% translate 'if_problem_persists' %} 
                            <a href="/customer/contact-support" class="text-decoration-none">{% translate 'contact_support' %}</a>
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.card.border-danger {
    border-width: 2px;
}

.fa-exclamation-triangle {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.alert code {
    word-break: break-all;
    font-size: 0.8rem;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
function retryLoad() {
    // Show loading state
    const retryBtn = document.querySelector('button[onclick="retryLoad()"]');
    const originalText = retryBtn.innerHTML;
    retryBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> {% translate "loading" %}...';
    retryBtn.disabled = true;
    
    // Reload the page
    setTimeout(() => {
        window.location.reload();
    }, 500);
}

// Auto-retry after 30 seconds if user doesn't take action
setTimeout(() => {
    const autoRetryBtn = document.createElement('div');
    autoRetryBtn.className = 'alert alert-info mt-3';
    autoRetryBtn.innerHTML = `
        <i class="fas fa-clock"></i> 
        <strong>{% translate 'auto_retry' %}</strong> - 
        {% translate 'retrying_in' %} <span id="countdown">30</span> {% translate 'seconds' %}
        <button class="btn btn-sm btn-outline-primary ms-2" onclick="retryLoad()">
            {% translate 'retry_now' %}
        </button>
    `;
    
    document.querySelector('.card-body').appendChild(autoRetryBtn);
    
    // Countdown
    let countdown = 30;
    const countdownElement = document.getElementById('countdown');
    const countdownInterval = setInterval(() => {
        countdown--;
        countdownElement.textContent = countdown;
        
        if (countdown <= 0) {
            clearInterval(countdownInterval);
            retryLoad();
        }
    }, 1000);
}, 10000); // Show auto-retry after 10 seconds
</script>
{% endblock %} 