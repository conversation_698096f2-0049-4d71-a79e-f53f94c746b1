{% extends "base.html" %}
{% load translation_tags %}

{% block content %}
<div class="page-container">
    <div class="page-header">
        <h1>{% translate 'notifications' %}</h1>
        <p>{% translate 'manage_your_notifications_and_alerts' %}</p>
    </div>

    <!-- Filters and Stats -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="card">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-3">
                            <label for="type" class="form-label">{% translate 'type' %}</label>
                            <select name="type" id="type" class="form-select">
                                <option value="">{% translate 'all_types' %}</option>
                                <option value="SYSTEM" {% if filters.type == 'SYSTEM' %}selected{% endif %}>{% translate 'system' %}</option>
                                <option value="SHIPMENT" {% if filters.type == 'SHIPMENT' %}selected{% endif %}>{% translate 'shipment' %}</option>
                                <option value="QUOTE" {% if filters.type == 'QUOTE' %}selected{% endif %}>{% translate 'quote' %}</option>
                                <option value="PAYMENT" {% if filters.type == 'PAYMENT' %}selected{% endif %}>{% translate 'payment' %}</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="is_read" class="form-label">{% translate 'status' %}</label>
                            <select name="is_read" id="is_read" class="form-select">
                                <option value="">{% translate 'all' %}</option>
                                <option value="false" {% if filters.is_read == 'false' %}selected{% endif %}>{% translate 'unread' %}</option>
                                <option value="true" {% if filters.is_read == 'true' %}selected{% endif %}>{% translate 'read' %}</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="limit" class="form-label">{% translate 'per_page' %}</label>
                            <select name="limit" id="limit" class="form-select">
                                <option value="10" {% if pagination.limit == 10 %}selected{% endif %}>10</option>
                                <option value="20" {% if pagination.limit == 20 %}selected{% endif %}>20</option>
                                <option value="50" {% if pagination.limit == 50 %}selected{% endif %}>50</option>
                            </select>
                        </div>
                        <div class="col-md-3 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="fas fa-filter"></i> {% translate 'filter' %}
                            </button>
                            <a href="{% url 'notifications:notifications_page' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-times"></i> {% translate 'clear' %}
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card">
                <div class="card-body text-center">
                    <h4 class="text-primary">{{ unread_count }}</h4>
                    <p class="mb-0">{% translate 'unread_notifications' %}</p>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8 col-md-12">
            <!-- Notification List -->
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5><i class="fas fa-bell"></i> {% translate 'notifications' %} ({{ pagination.total_count }})</h5>
                    <div class="notification-actions-header">
                        <button class="btn btn-sm btn-outline-primary" onclick="markAllAsRead()">
                            <i class="fas fa-check-double"></i> <span class="btn-text">{% translate 'mark_all_read' %}</span>
                        </button>
                        <button class="btn btn-sm btn-outline-secondary" onclick="clearAll()">
                            <i class="fas fa-trash"></i> <span class="btn-text">{% translate 'clear_all' %}</span>
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="notification-list">
                        {% if notifications %}
                            {% for notification in notifications %}
                            <div class="notification-item {% if not notification.is_read %}unread{% endif %}" data-id="{{ notification.id }}">
                                <div class="notification-icon bg-{% if notification.notification_type == 'SYSTEM' %}info{% elif notification.notification_type == 'SHIPMENT' %}primary{% elif notification.notification_type == 'QUOTE' %}success{% elif notification.notification_type == 'PAYMENT' %}warning{% else %}secondary{% endif %}">
                                    <i class="fas fa-{% if notification.notification_type == 'SYSTEM' %}info-circle{% elif notification.notification_type == 'SHIPMENT' %}truck{% elif notification.notification_type == 'QUOTE' %}quote-left{% elif notification.notification_type == 'PAYMENT' %}credit-card{% else %}bell{% endif %}"></i>
                                </div>
                                <div class="notification-content">
                                    <div class="notification-header">
                                        <h6>{{ notification.title }}</h6>
                                        <span class="notification-time">{{ notification.created_at|timesince }} ago</span>
                                    </div>
                                    <p>{{ notification.message }}</p>
                                    <div class="notification-actions">
                                        {% if notification.related_object_type and notification.related_object_id %}
                                            <button class="btn btn-sm btn-outline-primary" onclick="viewRelatedObject('{{ notification.related_object_type }}', '{{ notification.related_object_id }}')">
                                                {% translate 'view_details' %}
                                            </button>
                                        {% endif %}
                                        {% if not notification.is_read %}
                                            <button class="btn btn-sm btn-outline-secondary" onclick="markAsRead({{ notification.id }})">
                                                {% translate 'mark_read' %}
                                            </button>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        {% else %}
                            <div class="empty-state text-center py-5">
                                <i class="fas fa-bell-slash fa-3x text-muted mb-3"></i>
                                <h5>{% translate 'no_notifications' %}</h5>
                                <p class="text-muted">{% translate 'no_notifications_found' %}</p>
                            </div>
                        {% endif %}
                    </div>

                    <!-- Pagination -->
                    {% if pagination.total_pages > 1 %}
                    <nav aria-label="Notification pagination" class="mt-4">
                        <ul class="pagination justify-content-center">
                            {% if pagination.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ pagination.current_page|add:'-1' }}{% if filters.type %}&type={{ filters.type }}{% endif %}{% if filters.is_read %}&is_read={{ filters.is_read }}{% endif %}{% if pagination.limit != 20 %}&limit={{ pagination.limit }}{% endif %}">
                                        <i class="fas fa-chevron-left"></i>
                                    </a>
                                </li>
                            {% endif %}
                            
                            {% for page_num in pagination.total_pages|get_range %}
                                {% if page_num == pagination.current_page %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                {% else %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_num }}{% if filters.type %}&type={{ filters.type }}{% endif %}{% if filters.is_read %}&is_read={{ filters.is_read }}{% endif %}{% if pagination.limit != 20 %}&limit={{ pagination.limit }}{% endif %}">{{ page_num }}</a>
                                    </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if pagination.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ pagination.current_page|add:'1' }}{% if filters.type %}&type={{ filters.type }}{% endif %}{% if filters.is_read %}&is_read={{ filters.is_read }}{% endif %}{% if pagination.limit != 20 %}&limit={{ pagination.limit }}{% endif %}">
                                        <i class="fas fa-chevron-right"></i>
                                    </a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Notification Settings -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-cog"></i> {% translate 'notification_settings' %}</h5>
                </div>
                <div class="card-body">
                    <form id="notificationSettingsForm">
                        {% csrf_token %}
                        <div class="mb-3">
                            <h6>{% translate 'email_notifications' %}</h6>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="email_notifications" 
                                       {% if notification_preferences.email_notifications %}checked{% endif %}>
                                <label class="form-check-label" for="email_notifications">
                                    {% translate 'email_notifications' %}
                                </label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="email_shipment_updates" 
                                       {% if notification_preferences.shipment_updates %}checked{% endif %}>
                                <label class="form-check-label" for="email_shipment_updates">
                                    {% translate 'shipment_status_updates' %}
                                </label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="email_quotes" 
                                       {% if notification_preferences.quote_notifications %}checked{% endif %}>
                                <label class="form-check-label" for="email_quotes">
                                    {% translate 'new_quotes' %}
                                </label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="email_payments" 
                                       {% if notification_preferences.payment_alerts %}checked{% endif %}>
                                <label class="form-check-label" for="email_payments">
                                    {% translate 'payment_confirmation' %}
                                </label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="email_system" 
                                       {% if notification_preferences.security_alerts %}checked{% endif %}>
                                <label class="form-check-label" for="email_system">
                                    {% translate 'system_notifications' %}
                                </label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="marketing_emails" 
                                       {% if notification_preferences.marketing_emails %}checked{% endif %}>
                                <label class="form-check-label" for="marketing_emails">
                                    {% translate 'marketing_emails' %}
                                </label>
                            </div>
                        </div>

                        <div class="mb-3">
                            <h6>{% translate 'sms_notifications' %}</h6>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="sms_notifications" 
                                       {% if notification_preferences.sms_notifications %}checked{% endif %}>
                                <label class="form-check-label" for="sms_notifications">
                                    {% translate 'sms_notifications' %}
                                </label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="sms_shipment_updates" 
                                       {% if notification_preferences.shipment_updates %}checked{% endif %}>
                                <label class="form-check-label" for="sms_shipment_updates">
                                    {% translate 'shipment_status_updates' %}
                                </label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="sms_urgent" 
                                       {% if notification_preferences.security_alerts %}checked{% endif %}>
                                <label class="form-check-label" for="sms_urgent">
                                    {% translate 'urgent_notifications' %}
                                </label>
                            </div>
                        </div>

                        <div class="mb-3">
                            <h6>{% translate 'push_notifications' %}</h6>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="push_notifications" 
                                       {% if notification_preferences.push_notifications %}checked{% endif %}>
                                <label class="form-check-label" for="push_notifications">
                                    {% translate 'push_notifications' %}
                                </label>
                            </div>
                        </div>

                        <div class="mb-3">
                            <h6>{% translate 'notification_frequency' %}</h6>
                            <select class="form-control" id="notification_frequency">
                                <option value="immediate" {% if notification_preferences.notification_frequency == 'immediate' %}selected{% endif %}>{% translate 'immediate' %}</option>
                                <option value="hourly" {% if notification_preferences.notification_frequency == 'hourly' %}selected{% endif %}>{% translate 'hourly' %}</option>
                                <option value="daily" {% if notification_preferences.notification_frequency == 'daily' %}selected{% endif %}>{% translate 'daily_digest' %}</option>
                                <option value="weekly" {% if notification_preferences.notification_frequency == 'weekly' %}selected{% endif %}>{% translate 'weekly_summary' %}</option>
                            </select>
                        </div>

                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-save"></i> {% translate 'save_settings' %}
                        </button>
                    </form>
                </div>
            </div>

            <!-- Notification Statistics -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5><i class="fas fa-chart-bar"></i> {% translate 'notification_statistics' %}</h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <h4 class="text-primary">{{ statistics.total_notifications }}</h4>
                            <small class="text-muted">{% translate 'total_notifications' %}</small>
                        </div>
                        <div class="col-6">
                            <h4 class="text-warning">{{ statistics.unread_notifications }}</h4>
                            <small class="text-muted">{% translate 'unread' %}</small>
                        </div>
                    </div>
                    <div class="row text-center mt-3">
                        <div class="col-6">
                            <h4 class="text-success">{{ statistics.read_percentage }}%</h4>
                            <small class="text-muted">{% translate 'read_rate' %}</small>
                        </div>
                        <div class="col-6">
                            <h4 class="text-info">{{ statistics.avg_per_day }}</h4>
                            <small class="text-muted">{% translate 'avg_per_day' %}</small>
                        </div>
                    </div>
                    
                    <!-- Type Breakdown -->
                    {% if statistics.type_breakdown %}
                    <div class="mt-3">
                        <h6 class="text-muted mb-2">{% translate 'type_breakdown' %}</h6>
                        <div class="row text-center">
                            {% for type_code, count in statistics.type_breakdown.items %}
                                {% if count > 0 %}
                                <div class="col-4 mb-2">
                                    <div class="badge bg-{% if type_code == 'SYSTEM' %}info{% elif type_code == 'SHIPMENT' %}primary{% elif type_code == 'PAYMENT' %}warning{% elif type_code == 'CUSTOMS' %}success{% elif type_code == 'DRIVER' %}secondary{% elif type_code == 'INSURANCE' %}dark{% else %}light{% endif %}">
                                        {{ count }}
                                    </div>
                                    <small class="d-block text-muted">{{ type_code|title }}</small>
                                </div>
                                {% endif %}
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}
                    
                    <!-- Recent Activity -->
                    <div class="mt-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="text-muted">{% translate 'recent_activity' %}</span>
                            <span class="badge bg-primary">{{ statistics.recent_activity }}</span>
                        </div>
                        <small class="text-muted">{% translate 'last_7_days' %}</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.notification-item {
    display: flex;
    padding: 1rem;
    border-bottom: 1px solid #eee;
    transition: background-color 0.2s ease;
}

.notification-item:hover {
    background-color: #f8f9fa;
}

.notification-item.unread {
    background-color: #e3f2fd;
    border-left: 4px solid #2196f3;
}

.notification-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    margin-right: 1rem;
    flex-shrink: 0;
}

.notification-content {
    flex: 1;
}

.notification-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.notification-header h6 {
    margin: 0;
    font-weight: 600;
}

.notification-time {
    font-size: 0.8rem;
    color: #666;
}

.notification-content p {
    margin: 0 0 0.5rem 0;
    color: #333;
}

.notification-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.notification-actions .btn {
    font-size: 0.8rem;
    padding: 0.25rem 0.5rem;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .card {
        margin-bottom: 1.5rem;
    }
}

@media (max-width: 992px) {
    .card {
        margin-bottom: 1rem;
    }
    
    .card-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .notification-actions-header {
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;
    }
}

@media (max-width: 768px) {
    .card {
        margin-bottom: 1rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .notification-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .notification-icon {
        align-self: flex-start;
    }
    
    .notification-content {
        width: 100%;
    }
    
    .notification-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .notification-actions {
        flex-direction: column;
        width: 100%;
    }
    
    .notification-actions .btn {
        width: 100%;
        margin-bottom: 0.25rem;
    }
    
    .btn-text {
        display: none;
    }
}

@media (max-width: 576px) {
    .card-body {
        padding: 0.75rem;
    }
    
    .card-header {
        padding: 0.75rem;
    }
    
    .card-header h5 {
        font-size: 0.9rem;
    }
    
    .notification-item {
        padding: 0.75rem;
    }
    
    .notification-content h6 {
        font-size: 0.9rem;
    }
    
    .notification-content p {
        font-size: 0.85rem;
    }
    
    .notification-time {
        font-size: 0.75rem;
    }
    
    .notification-actions .btn {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }
}

/* Landscape orientation adjustments for mobile */
@media (max-width: 768px) and (orientation: landscape) {
    .notification-item {
        flex-direction: row;
        align-items: flex-start;
    }
    
    .notification-actions {
        flex-direction: row;
        flex-wrap: wrap;
    }
    
    .notification-actions .btn {
        width: auto;
        margin-bottom: 0;
    }
}
</style>
{% endblock %}

{% block extra_js %}
<script>
function markAsRead(notificationId) {
    const notification = document.querySelector(`[data-id="${notificationId}"]`);
    notification.classList.remove('unread');
    // Here you would typically make an API call to mark as read
    console.log(`Marked notification ${notificationId} as read`);
}

function markAllAsRead() {
    const unreadNotifications = document.querySelectorAll('.notification-item.unread');
    unreadNotifications.forEach(notification => {
        notification.classList.remove('unread');
    });
    // Here you would typically make an API call to mark all as read
    console.log('Marked all notifications as read');
}

function clearAll() {
    if (confirm('Are you sure you want to clear all notifications?')) {
        const notificationList = document.querySelector('.notification-list');
        notificationList.innerHTML = '<p class="text-muted text-center py-4">No notifications</p>';
        // Here you would typically make an API call to clear all notifications
        console.log('Cleared all notifications');
    }
}

function viewRelatedObject(type, id) {
    let url = '';
    if (type === 'SHIPMENT') {
        url = `/customer/shipment-details/${id}`;
    } else if (type === 'QUOTE') {
        url = `/customer/quotes/${id}`;
    } else if (type === 'PAYMENT') {
        url = `/customer/invoice-reconciliation`;
    }
    if (url) {
        window.location.href = url;
    }
}

document.getElementById('notificationSettingsForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    // Collect form data
    const formData = {
        email_notifications: document.getElementById('email_notifications').checked,
        sms_notifications: document.getElementById('sms_notifications').checked,
        push_notifications: document.getElementById('push_notifications').checked,
        marketing_emails: document.getElementById('marketing_emails').checked,
        shipment_updates: document.getElementById('email_shipment_updates').checked,
        quote_notifications: document.getElementById('email_quotes').checked,
        payment_alerts: document.getElementById('email_payments').checked,
        security_alerts: document.getElementById('email_system').checked,
        notification_frequency: document.getElementById('notification_frequency').value
    };
    
    // Show loading state
    const submitBtn = e.target.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> {% translate "saving" %}...';
    submitBtn.disabled = true;
    
    // Send update request
    fetch('{% url "notifications:update_preferences" %}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            // Show success message
            showAlert('success', '{% translate "notification_settings_updated" %}');
        } else {
            // Show error message
            showAlert('error', data.message || '{% translate "error_updating_settings" %}');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('error', '{% translate "error_updating_settings" %}');
    })
    .finally(() => {
        // Restore button state
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
});

function showAlert(type, message) {
    // Create alert element
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    // Insert at top of form
    const form = document.getElementById('notificationSettingsForm');
    form.insertBefore(alertDiv, form.firstChild);
    
    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// Responsive behavior for button text
document.addEventListener('DOMContentLoaded', function() {
    function handleResize() {
        const isMobile = window.innerWidth <= 576;
        const btnTexts = document.querySelectorAll('.btn-text');
        
        btnTexts.forEach(text => {
            text.style.display = isMobile ? 'none' : 'inline';
        });
    }
    
    // Initial call
    handleResize();
    
    // Listen for window resize
    window.addEventListener('resize', handleResize);
});
</script>
{% endblock %} 