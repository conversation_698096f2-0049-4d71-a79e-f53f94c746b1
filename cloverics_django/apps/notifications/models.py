from django.db import models
from django.contrib.auth import get_user_model

User = get_user_model()

class Notification(models.Model):
    NOTIFICATION_TYPES = [
        ('SYSTEM', 'System'),
        ('SHIPMENT', 'Shipment'),
        ('PAYMENT', 'Payment'),
        ('CUSTOMS', 'Customs'),
        ('DRIVER', 'Driver'),
        ('INSURANCE', 'Insurance'),
    ]
    
    PRIORITY_CHOICES = [
        ('LOW', 'Low'),
        ('MEDIUM', 'Medium'),
        ('HIGH', 'High'),
    ]
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='user_notifications')
    title = models.CharField(max_length=255)
    message = models.TextField()
    notification_type = models.CharField(max_length=20, choices=NOTIFICATION_TYPES, default='SYSTEM')
    priority = models.CharField(max_length=10, choices=PRIORITY_CHOICES, default='MEDIUM', db_index=True)
    related_object_type = models.CharField(max_length=50, null=True, blank=True)
    related_object_id = models.CharField(max_length=50, null=True, blank=True)
    is_read = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    read_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        db_table = 'apps_notifications'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'is_read']),
            models.Index(fields=['notification_type', 'priority']),
            models.Index(fields=['related_object_type', 'related_object_id']),
            models.Index(fields=['created_at']),
        ]
        
    def __str__(self):
        return f"{self.title} - {self.user.company_name} - {self.created_at.strftime('%Y-%m-%d %H:%M')}"
    
    @classmethod
    def create_shipment_notification(cls, user, shipment, title, message):
        """Create a notification for a shipment update."""
        return cls.objects.create(
            user=user,
            title=title,
            message=message,
            notification_type='SHIPMENT',
            related_object_type='shipment',
            related_object_id=str(shipment.id)
        )
    
    @classmethod
    def create_insurance_notification(cls, user, insurance, title, message):
        """Create a notification for an insurance update."""
        return cls.objects.create(
            user=user,
            title=title,
            message=message,
            notification_type='INSURANCE',
            related_object_type='insurance',
            related_object_id=str(insurance.id)
        )
    
    @classmethod
    def create_customs_notification(cls, user, customs_declaration, title, message):
        """Create a notification for a customs declaration update."""
        return cls.objects.create(
            user=user,
            title=title,
            message=message,
            notification_type='CUSTOMS',
            related_object_type='customs_declaration',
            related_object_id=str(customs_declaration.id)
        )
    
    @classmethod
    def create_payment_notification(cls, user, payment, title, message):
        """Create a notification for a payment update."""
        return cls.objects.create(
            user=user,
            title=title,
            message=message,
            notification_type='PAYMENT',
            related_object_type='payment',
            related_object_id=str(payment.id)
        )    

class CrossUserEvent(models.Model):
    EVENT_TYPES = [
        ('customs_declaration_submitted', 'Customs Declaration Submitted'),
        ('customs_declaration_approved', 'Customs Declaration Approved'),
        ('customs_declaration_rejected', 'Customs Declaration Rejected'),
        ('driver_assigned', 'Driver Assigned'),
        ('driver_location_updated', 'Driver Location Updated'),
        ('shipment_status_updated', 'Shipment Status Updated'),
        ('payment_processed', 'Payment Processed'),
        ('insurance_claim_filed', 'Insurance Claim Filed'),
    ]
    
    event_type = models.CharField(max_length=50, choices=EVENT_TYPES)
    source_user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='triggered_events')
    source_user_role = models.CharField(max_length=50)
    target_user_ids = models.JSONField()  # List of target user IDs
    shipment_id = models.IntegerField(null=True, blank=True)
    data = models.JSONField(default=dict)  # Additional event data
    processed = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'notifications_cross_user_events'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['event_type', 'processed']),
            models.Index(fields=['created_at']),
        ]

# WebSocketConnection model removed - WebSocket functionality disabled