from django.contrib import admin
from .models import (
    AdvancedRoute, ContainerPartner, PrivateRouteAccess, 
    RouteRateCalculation, DriverManagement
)


@admin.register(AdvancedRoute)
class AdvancedRouteAdmin(admin.ModelAdmin):
    list_display = [
        'origin_city', 'destination_city', 'transport_type', 
        'logistics_provider', 'is_private', 'is_container_sharing',
        'base_rate', 'capacity_utilization', 'is_active'
    ]
    list_filter = [
        'transport_type', 'is_private', 'is_container_sharing', 
        'is_active', 'container_status'
    ]
    search_fields = [
        'origin_city', 'destination_city', 'origin_country', 
        'destination_country', 'logistics_provider__email'
    ]
    readonly_fields = ['created_at', 'updated_at', 'capacity_utilization']
    fieldsets = (
        ('Route Information', {
            'fields': (
                'logistics_provider', 'origin_country', 'origin_city',
                'destination_country', 'destination_city', 'transport_type'
            )
        }),
        ('Configuration', {
            'fields': (
                'is_private', 'is_container_sharing', 'base_rate', 
                'estimated_days', 'is_active'
            )
        }),
        ('Container Sharing', {
            'fields': (
                'total_capacity_kg', 'available_capacity_kg',
                'min_fill_threshold', 'container_status'
            ),
            'classes': ('collapse',)
        }),
        ('Metadata', {
            'fields': ('last_used', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )


@admin.register(ContainerPartner)
class ContainerPartnerAdmin(admin.ModelAdmin):
    list_display = [
        'customer', 'route', 'reserved_capacity_kg', 
        'cost_share_percentage', 'booking_status', 'requested_at'
    ]
    list_filter = ['booking_status', 'requested_at']
    search_fields = [
        'customer__email', 'route__origin_city', 'route__destination_city'
    ]
    readonly_fields = ['requested_at', 'approved_at']


@admin.register(PrivateRouteAccess)
class PrivateRouteAccessAdmin(admin.ModelAdmin):
    list_display = [
        'client', 'route', 'invitation_code', 'is_active',
        'times_used', 'access_granted_at'
    ]
    list_filter = ['is_active', 'access_granted_at']
    search_fields = [
        'client__email', 'invitation_code', 
        'route__origin_city', 'route__destination_city'
    ]
    readonly_fields = ['access_granted_at', 'last_used_at']


@admin.register(RouteRateCalculation)
class RouteRateCalculationAdmin(admin.ModelAdmin):
    list_display = [
        'route', 'calculated_by', 'weight_kg', 'total_cost',
        'rate_per_kg', 'calculated_at'
    ]
    list_filter = ['calculated_at']
    search_fields = [
        'route__origin_city', 'route__destination_city',
        'calculated_by__email'
    ]
    readonly_fields = ['calculated_at']


@admin.register(DriverManagement)
class DriverManagementAdmin(admin.ModelAdmin):
    list_display = [
        'logistics_provider', 'view_type', 'total_drivers_managed',
        'active_assignments', 'last_refreshed'
    ]
    list_filter = ['view_type', 'last_refreshed']
    search_fields = ['logistics_provider__email']
    readonly_fields = ['last_refreshed']