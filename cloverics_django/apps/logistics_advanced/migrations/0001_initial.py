# Generated by Django 5.2.1 on 2025-08-07 12:45

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='AdvancedRoute',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('origin_country', models.CharField(max_length=100)),
                ('origin_city', models.CharField(max_length=100)),
                ('destination_country', models.CharField(max_length=100)),
                ('destination_city', models.CharField(max_length=100)),
                ('transport_type', models.CharField(default='truck', max_length=50)),
                ('is_private', models.BooleanField(default=False)),
                ('is_container_sharing', models.BooleanField(default=False)),
                ('base_rate', models.DecimalField(decimal_places=2, max_digits=10)),
                ('estimated_days', models.IntegerField(default=7)),
                ('total_capacity_kg', models.IntegerField(blank=True, null=True)),
                ('available_capacity_kg', models.IntegerField(blank=True, null=True)),
                ('min_fill_threshold', models.IntegerField(default=70)),
                ('container_status', models.CharField(default='available', max_length=20)),
                ('is_active', models.BooleanField(default=True)),
                ('last_used', models.DateTimeField(default=django.utils.timezone.now)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('logistics_provider', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='advanced_routes', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='ContainerPartner',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('reserved_capacity_kg', models.IntegerField()),
                ('cost_share_percentage', models.DecimalField(decimal_places=2, max_digits=5)),
                ('booking_status', models.CharField(default='pending', max_length=20)),
                ('requested_at', models.DateTimeField(auto_now_add=True)),
                ('approved_at', models.DateTimeField(blank=True, null=True)),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_partnerships', to=settings.AUTH_USER_MODEL)),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='container_partnerships', to=settings.AUTH_USER_MODEL)),
                ('route', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='partners', to='logistics_advanced.advancedroute')),
            ],
        ),
        migrations.CreateModel(
            name='DriverManagement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('view_type', models.CharField(default='card', max_length=10)),
                ('filters_applied', models.JSONField(default=dict)),
                ('last_refreshed', models.DateTimeField(auto_now=True)),
                ('total_drivers_managed', models.IntegerField(default=0)),
                ('active_assignments', models.IntegerField(default=0)),
                ('logistics_provider', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='managed_drivers', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='PrivateRouteAccess',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('invitation_code', models.CharField(max_length=20, unique=True)),
                ('access_granted_at', models.DateTimeField(auto_now_add=True)),
                ('expires_at', models.DateTimeField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('times_used', models.IntegerField(default=0)),
                ('last_used_at', models.DateTimeField(blank=True, null=True)),
                ('client', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='private_route_access', to=settings.AUTH_USER_MODEL)),
                ('route', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='private_access', to='logistics_advanced.advancedroute')),
            ],
        ),
        migrations.CreateModel(
            name='RouteRateCalculation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('base_rate', models.DecimalField(decimal_places=2, max_digits=10)),
                ('fuel_surcharge_percentage', models.DecimalField(decimal_places=2, default=15.0, max_digits=5)),
                ('handling_fee', models.DecimalField(decimal_places=2, default=25.0, max_digits=8)),
                ('weight_kg', models.DecimalField(decimal_places=2, max_digits=10)),
                ('subtotal', models.DecimalField(decimal_places=2, max_digits=12)),
                ('fuel_cost', models.DecimalField(decimal_places=2, max_digits=10)),
                ('total_cost', models.DecimalField(decimal_places=2, max_digits=12)),
                ('rate_per_kg', models.DecimalField(decimal_places=2, max_digits=8)),
                ('calculated_at', models.DateTimeField(auto_now_add=True)),
                ('calculated_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='rate_calculations', to=settings.AUTH_USER_MODEL)),
                ('route', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='rate_calculations', to='logistics_advanced.advancedroute')),
            ],
        ),
        migrations.AddIndex(
            model_name='advancedroute',
            index=models.Index(fields=['logistics_provider', 'is_active'], name='logistics_a_logisti_6753d9_idx'),
        ),
        migrations.AddIndex(
            model_name='advancedroute',
            index=models.Index(fields=['origin_country', 'destination_country'], name='logistics_a_origin__e679ac_idx'),
        ),
        migrations.AddIndex(
            model_name='advancedroute',
            index=models.Index(fields=['transport_type', 'is_active'], name='logistics_a_transpo_432f3b_idx'),
        ),
        migrations.AddIndex(
            model_name='advancedroute',
            index=models.Index(fields=['is_container_sharing', 'container_status'], name='logistics_a_is_cont_cd5ef2_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='advancedroute',
            unique_together={('logistics_provider', 'origin_country', 'origin_city', 'destination_country', 'destination_city', 'transport_type')},
        ),
        migrations.AddIndex(
            model_name='containerpartner',
            index=models.Index(fields=['route', 'booking_status'], name='logistics_a_route_i_f7f6e1_idx'),
        ),
        migrations.AddIndex(
            model_name='containerpartner',
            index=models.Index(fields=['customer', 'booking_status'], name='logistics_a_custome_7c3189_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='containerpartner',
            unique_together={('route', 'customer')},
        ),
        migrations.AlterUniqueTogether(
            name='drivermanagement',
            unique_together={('logistics_provider',)},
        ),
        migrations.AddIndex(
            model_name='privaterouteaccess',
            index=models.Index(fields=['invitation_code'], name='logistics_a_invitat_780d76_idx'),
        ),
        migrations.AddIndex(
            model_name='privaterouteaccess',
            index=models.Index(fields=['route', 'is_active'], name='logistics_a_route_i_3e521a_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='privaterouteaccess',
            unique_together={('route', 'client')},
        ),
        migrations.AddIndex(
            model_name='routeratecalculation',
            index=models.Index(fields=['route', 'calculated_at'], name='logistics_a_route_i_120942_idx'),
        ),
        migrations.AddIndex(
            model_name='routeratecalculation',
            index=models.Index(fields=['calculated_by', 'calculated_at'], name='logistics_a_calcula_e66984_idx'),
        ),
    ]
