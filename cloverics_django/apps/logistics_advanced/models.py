from django.db import models
from django.conf import settings
from django.utils import timezone


class AdvancedRoute(models.Model):
    """Advanced route management with container sharing and private route capabilities"""
    logistics_provider = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='advanced_routes'
    )
    origin_country = models.CharField(max_length=100)
    origin_city = models.CharField(max_length=100)
    destination_country = models.CharField(max_length=100) 
    destination_city = models.CharField(max_length=100)
    transport_type = models.CharField(max_length=50, default='truck')
    
    # Route configuration
    is_private = models.BooleanField(default=False)
    is_container_sharing = models.BooleanField(default=False)
    base_rate = models.DecimalField(max_digits=10, decimal_places=2)
    estimated_days = models.IntegerField(default=7)
    
    # Container sharing fields
    total_capacity_kg = models.IntegerField(null=True, blank=True)
    available_capacity_kg = models.IntegerField(null=True, blank=True)
    min_fill_threshold = models.IntegerField(default=70)  # Percentage
    container_status = models.CharField(max_length=20, default='available')
    
    # Metadata
    is_active = models.BooleanField(default=True)
    last_used = models.DateTimeField(default=timezone.now)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        unique_together = [
            'logistics_provider', 'origin_country', 'origin_city', 
            'destination_country', 'destination_city', 'transport_type'
        ]
        indexes = [
            models.Index(fields=['logistics_provider', 'is_active']),
            models.Index(fields=['origin_country', 'destination_country']),
            models.Index(fields=['transport_type', 'is_active']),
            models.Index(fields=['is_container_sharing', 'container_status']),
        ]

    def __str__(self):
        return f"{self.origin_city} → {self.destination_city} ({self.transport_type})"

    @property
    def capacity_utilization(self):
        """Calculate current capacity utilization percentage"""
        if not self.total_capacity_kg or not self.available_capacity_kg:
            return 0
        used_capacity = self.total_capacity_kg - self.available_capacity_kg
        return round((used_capacity / self.total_capacity_kg) * 100, 1)


class ContainerPartner(models.Model):
    """Partners sharing container space on routes"""
    route = models.ForeignKey(AdvancedRoute, on_delete=models.CASCADE, related_name='partners')
    customer = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='container_partnerships'
    )
    
    # Partnership details
    reserved_capacity_kg = models.IntegerField()
    cost_share_percentage = models.DecimalField(max_digits=5, decimal_places=2)
    booking_status = models.CharField(max_length=20, default='pending')
    
    # Request details
    requested_at = models.DateTimeField(auto_now_add=True)
    approved_at = models.DateTimeField(null=True, blank=True)
    approved_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='approved_partnerships'
    )
    
    class Meta:
        unique_together = ['route', 'customer']
        indexes = [
            models.Index(fields=['route', 'booking_status']),
            models.Index(fields=['customer', 'booking_status']),
        ]

    def __str__(self):
        return f"{self.customer.email} on {self.route} ({self.booking_status})"


class PrivateRouteAccess(models.Model):
    """Private route access management for invited clients"""
    route = models.ForeignKey(AdvancedRoute, on_delete=models.CASCADE, related_name='private_access')
    client = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='private_route_access'
    )
    
    # Access details
    invitation_code = models.CharField(max_length=20, unique=True)
    access_granted_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField(null=True, blank=True)
    is_active = models.BooleanField(default=True)
    
    # Usage tracking
    times_used = models.IntegerField(default=0)
    last_used_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        unique_together = ['route', 'client']
        indexes = [
            models.Index(fields=['invitation_code']),
            models.Index(fields=['route', 'is_active']),
        ]

    def __str__(self):
        return f"Private access for {self.client.email} to {self.route}"


class RouteRateCalculation(models.Model):
    """Advanced rate calculations for logistics routes"""
    route = models.ForeignKey(AdvancedRoute, on_delete=models.CASCADE, related_name='rate_calculations')
    
    # Rate components
    base_rate = models.DecimalField(max_digits=10, decimal_places=2)
    fuel_surcharge_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=15.0)
    handling_fee = models.DecimalField(max_digits=8, decimal_places=2, default=25.0)
    
    # Calculated values
    weight_kg = models.DecimalField(max_digits=10, decimal_places=2)
    subtotal = models.DecimalField(max_digits=12, decimal_places=2)
    fuel_cost = models.DecimalField(max_digits=10, decimal_places=2)
    total_cost = models.DecimalField(max_digits=12, decimal_places=2)
    rate_per_kg = models.DecimalField(max_digits=8, decimal_places=2)
    
    # Metadata
    calculated_at = models.DateTimeField(auto_now_add=True)
    calculated_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='rate_calculations'
    )
    
    class Meta:
        indexes = [
            models.Index(fields=['route', 'calculated_at']),
            models.Index(fields=['calculated_by', 'calculated_at']),
        ]

    def __str__(self):
        return f"Rate calculation for {self.route} - ${self.total_cost}"


class DriverManagement(models.Model):
    """Enhanced driver management for logistics providers"""
    logistics_provider = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='managed_drivers'
    )
    
    # Driver interface settings
    view_type = models.CharField(max_length=10, default='card')  # 'card' or 'list'
    filters_applied = models.JSONField(default=dict)
    last_refreshed = models.DateTimeField(auto_now=True)
    
    # Management statistics
    total_drivers_managed = models.IntegerField(default=0)
    active_assignments = models.IntegerField(default=0)
    
    class Meta:
        unique_together = ['logistics_provider']

    def __str__(self):
        return f"Driver management for {self.logistics_provider.email}"