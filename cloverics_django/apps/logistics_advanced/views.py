from django.shortcuts import render, get_object_or_404, redirect
from django.http import JsonResponse
from django.contrib.auth.decorators import login_required
from django.views.decorators.http import require_http_methods
from django.utils import timezone
from django.db import transaction
from django.core.exceptions import ValidationError
from django.contrib import messages
import json
import logging

from .models import (
    AdvancedRoute, ContainerPartner, PrivateRouteAccess, 
    RouteRateCalculation, DriverManagement
)
from shipments.models import ShippingRate, Route, TransportType, ContainerType

logger = logging.getLogger(__name__)


@login_required
@require_http_methods(["POST"])
def create_unified_shipment(request):
    """
    ✅ EXTRACTED FROM FASTAPI: /logistics/shipment/create endpoint  
    Original Lines: 3155-3340 (185+ lines) - EXTRACTED
    
    Create new shipment service (public, private, or container sharing)
    with comprehensive validation and database integration.
    """
    try:
        # Parse JSON data
        data = json.loads(request.body)
        
        service_type = data.get('service_type', 'public')
        transport_type = data.get('transport_type', 'truck')
        origin_country = data.get('origin_country', '')
        destination_country = data.get('destination_country', '')
        origin_city = data.get('origin_city', '')
        destination_city = data.get('destination_city', '')
        base_rate = float(data.get('base_rate', 0))
        estimated_days = int(data.get('estimated_days', 7))
        max_capacity = int(data.get('max_capacity', 5000))
        private_client = data.get('private_client', '')
        min_fill_threshold = int(data.get('min_fill_threshold', 70))
        
        # Validate required fields
        if not all([origin_country, destination_country, base_rate > 0]):
            return JsonResponse({"success": False, "error": "Missing required fields"})
        
        # Create advanced route with container sharing capabilities
        with transaction.atomic():
            # Create or get advanced route
            advanced_route, created = AdvancedRoute.objects.get_or_create(
                logistics_provider=request.user,
                origin_country=origin_country,
                origin_city=origin_city,
                destination_country=destination_country,
                destination_city=destination_city,
                transport_type=transport_type,
                defaults={
                    'is_private': service_type == 'private',
                    'is_container_sharing': service_type == 'container_sharing',
                    'base_rate': base_rate,
                    'estimated_days': estimated_days,
                    'total_capacity_kg': max_capacity,
                    'available_capacity_kg': max_capacity,
                    'min_fill_threshold': min_fill_threshold,
                    'container_status': 'available',
                    'is_active': True,
                    'last_used': timezone.now()
                }
            )
            
            if not created:
                # Update existing route
                advanced_route.base_rate = base_rate
                advanced_route.estimated_days = estimated_days
                advanced_route.total_capacity_kg = max_capacity
                advanced_route.available_capacity_kg = max_capacity
                advanced_route.last_used = timezone.now()
                advanced_route.save()
        
        return JsonResponse({
            "success": True,
            "message": f"{service_type.title()} shipment service created successfully",
            "route_id": advanced_route.id,
            "service_type": service_type
        })
        
    except Exception as e:
        logger.error(f"Unified shipment creation failed: {e}")
        return JsonResponse({
            "success": False,
            "error": f"Failed to create shipment service: {str(e)}"
        }, status_code=500)


@login_required
@require_http_methods(["POST"])  
def refresh_driver_data(request):
    """
    ✅ EXTRACTED FROM FASTAPI: /api/driver/refresh-data endpoint
    Original Lines: 2980-2986 (7 lines) - EXTRACTED
    
    Refresh driver data for logistics provider
    """
    try:
        # Update driver management record
        driver_mgmt, created = DriverManagement.objects.get_or_create(
            logistics_provider=request.user,
            defaults={'view_type': 'card', 'filters_applied': {}}
        )
        driver_mgmt.last_refreshed = timezone.now()
        driver_mgmt.save()
        
        return JsonResponse({"status": "success", "message": "Driver data refreshed successfully"})
    except Exception as e:
        logger.error(f"Driver data refresh failed: {e}")
        return JsonResponse({"status": "error", "message": str(e)})


@login_required  
@require_http_methods(["POST"])
def reset_driver_form(request):
    """
    ✅ EXTRACTED FROM FASTAPI: /api/driver/reset-form endpoint
    Original Lines: 2988-2994 (7 lines) - EXTRACTED
    
    Reset driver creation form
    """
    try:
        return JsonResponse({"status": "success", "message": "Form reset successfully"})
    except Exception as e:
        return JsonResponse({"status": "error", "message": str(e)})


@login_required
@require_http_methods(["POST"])
def toggle_driver_view(request):
    """
    ✅ EXTRACTED FROM FASTAPI: /api/driver/toggle-view endpoint  
    Original Lines: 2996-3005 (10 lines) - EXTRACTED
    
    Toggle between card and list view for driver management
    """
    try:
        data = json.loads(request.body)
        view_type = data.get('view_type', 'card')
        
        # Update driver management settings
        driver_mgmt, created = DriverManagement.objects.get_or_create(
            logistics_provider=request.user,
            defaults={'view_type': view_type, 'filters_applied': {}}
        )
        driver_mgmt.view_type = view_type
        driver_mgmt.save()
        
        return JsonResponse({
            "status": "success", 
            "view_type": view_type, 
            "message": f"Switched to {view_type} view"
        })
    except Exception as e:
        return JsonResponse({"status": "error", "message": str(e)})


@login_required
@require_http_methods(["POST"])
def clear_driver_filters(request):
    """
    ✅ EXTRACTED FROM FASTAPI: /api/driver/clear-filters endpoint
    Original Lines: 3007-3013 (7 lines) - EXTRACTED
    
    Clear all driver filters
    """
    try:
        # Clear filters in driver management
        driver_mgmt, created = DriverManagement.objects.get_or_create(
            logistics_provider=request.user,
            defaults={'view_type': 'card', 'filters_applied': {}}
        )
        driver_mgmt.filters_applied = {}
        driver_mgmt.save()
        
        return JsonResponse({"status": "success", "message": "Filters cleared successfully"})
    except Exception as e:
        return JsonResponse({"status": "error", "message": str(e)})


@login_required
@require_http_methods(["POST"])  
def refresh_driver_list(request):
    """
    ✅ EXTRACTED FROM FASTAPI: /api/driver/refresh-list endpoint
    Original Lines: 3015-3021 (7 lines) - EXTRACTED
    
    Refresh driver list
    """
    try:
        return JsonResponse({"status": "success", "message": "Driver list refreshed successfully"})
    except Exception as e:
        return JsonResponse({"status": "error", "message": str(e)})


@login_required
@require_http_methods(["POST"])
def edit_shipping_rate(request, rate_id):
    """
    ✅ EXTRACTED FROM FASTAPI: /logistics/rate/{rate_id}/edit endpoint
    Original Lines: 5944-5976 (33 lines) - EXTRACTED
    
    Edit shipping rate with all fields including fuel surcharge and handling fee
    """
    try:
        data = json.loads(request.body)
        base_rate = float(data.get('base_rate', 0))
        fuel_surcharge = float(data.get('fuel_surcharge', 15))
        handling_fee = float(data.get('handling_fee', 25))
        is_active = data.get('is_active', False)
        
        # Get and verify ownership of the rate
        shipping_rate = get_object_or_404(
            ShippingRate,
            id=rate_id,
            logistics_provider=request.user
        )
        
        # Update all rate fields
        shipping_rate.base_rate = base_rate
        shipping_rate.price_per_kg = base_rate  # Keep both fields in sync
        shipping_rate.fuel_surcharge = fuel_surcharge
        shipping_rate.handling_fee = handling_fee
        shipping_rate.is_active = is_active
        shipping_rate.save()
        
        return JsonResponse({"success": True, "message": "Rate updated successfully"})
        
    except ShippingRate.DoesNotExist:
        return JsonResponse({"success": False, "error": "Rate not found"})
    except Exception as e:
        logger.error(f"Error updating rate: {e}")
        return JsonResponse({"success": False, "error": "Failed to update rate"})


@login_required
@require_http_methods(["POST"])
def calculate_shipping_rate(request):
    """
    ✅ EXTRACTED FROM FASTAPI: /logistics/rate/calculate endpoint
    Original Lines: 5978-6005 (28 lines) - EXTRACTED
    
    Calculate total shipping cost with fuel surcharge and handling fee
    """
    try:
        data = json.loads(request.body)
        base_rate = float(data.get('base_rate', 0))
        fuel_surcharge = float(data.get('fuel_surcharge', 15))
        handling_fee = float(data.get('handling_fee', 25))
        weight = float(data.get('weight', 100))
        
        # Calculate total cost
        subtotal = base_rate * weight
        fuel_cost = subtotal * (fuel_surcharge / 100)
        total_cost = subtotal + fuel_cost + handling_fee
        
        # Store calculation for audit trail
        calculation = RouteRateCalculation.objects.create(
            route_id=1,  # Default route, to be improved
            base_rate=base_rate,
            fuel_surcharge_percentage=fuel_surcharge,
            handling_fee=handling_fee,
            weight_kg=weight,
            subtotal=subtotal,
            fuel_cost=fuel_cost,
            total_cost=total_cost,
            rate_per_kg=total_cost / weight,
            calculated_by=request.user
        )
        
        return JsonResponse({
            "success": True,
            "calculation": {
                "subtotal": round(subtotal, 2),
                "fuel_cost": round(fuel_cost, 2),
                "handling_fee": handling_fee,
                "total_cost": round(total_cost, 2),
                "rate_per_kg": round(total_cost / weight, 2)
            }
        })
        
    except Exception as e:
        logger.error(f"Error calculating rate: {e}")
        return JsonResponse({"success": False, "error": "Calculation failed"})


@login_required
def edit_route_page(request, route_id):
    """
    ✅ EXTRACTED FROM FASTAPI: /logistics/route/{route_id} endpoint
    Original Lines: 6007-6050+ (40+ lines) - EXTRACTED
    
    Edit specific route page with comprehensive route data
    """
    try:
        # Get the shipping rate from database with related route data
        shipping_rate = get_object_or_404(
            ShippingRate.objects.select_related('route', 'transport_type'),
            id=route_id,
            logistics_provider=request.user
        )
        
        context = {
            "user": request.user,
            "route": {
                "id": shipping_rate.id,
                "origin_country": shipping_rate.route.origin_country,
                "origin_city": shipping_rate.route.origin_city,
                "destination_country": shipping_rate.route.destination_country,
                "destination_city": shipping_rate.route.destination_city,
                "transport_type": shipping_rate.transport_type.type,
                "base_rate": shipping_rate.base_rate,
                "estimated_days": shipping_rate.estimated_days,
                "is_active": shipping_rate.is_active,
                "fuel_surcharge": getattr(shipping_rate, 'fuel_surcharge', 15.0),
                "handling_fee": getattr(shipping_rate, 'handling_fee', 25.0)
            },
            "title": "Edit Route - Cloverics"
        }
        
        return render(request, 'logistics/edit_route.html', context)
        
    except Exception as e:
        logger.error(f"Error loading route edit page: {e}")
        messages.error(request, "Route not found or access denied")
        return redirect('logistics:routes')