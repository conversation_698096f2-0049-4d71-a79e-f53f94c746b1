from django.urls import path
from . import views

app_name = 'logistics_advanced'

urlpatterns = [
    # Unified shipment management
    path('shipment/create/', views.create_unified_shipment, name='create_unified_shipment'),
    
    # Driver management endpoints
    path('api/driver/refresh-data/', views.refresh_driver_data, name='refresh_driver_data'),
    path('api/driver/reset-form/', views.reset_driver_form, name='reset_driver_form'),
    path('api/driver/toggle-view/', views.toggle_driver_view, name='toggle_driver_view'),
    path('api/driver/clear-filters/', views.clear_driver_filters, name='clear_driver_filters'),
    path('api/driver/refresh-list/', views.refresh_driver_list, name='refresh_driver_list'),
    
    # Rate management endpoints
    path('rate/<int:rate_id>/edit/', views.edit_shipping_rate, name='edit_shipping_rate'),
    path('rate/calculate/', views.calculate_shipping_rate, name='calculate_shipping_rate'),
    
    # Route management endpoints
    path('route/<int:route_id>/', views.edit_route_page, name='edit_route_page'),
]