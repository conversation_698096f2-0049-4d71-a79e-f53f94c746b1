from django.shortcuts import render, redirect
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from .models import InstantQuoteBatch, InstantQuoteProvider, MultiModalOptimization
from .services import InstantQuoteAggregator, MultiModalOptimizer
import json
import logging

logger = logging.getLogger(__name__)


@login_required
def instant_quotes_page(request):
    """
    Instant multi-modal quotes page
    Extracted from FastAPI lines 1470-1482
    """
    from utils.countries import COUNTRIES, US_STATES
    
    context = {
        "user": request.user,
        "title": "Instant Multi-Modal Quotes - Cloverics",
        "countries": COUNTRIES,
        "us_states": US_STATES
    }
    
    return render(request, 'customer/instant_quotes.html', context)


@login_required
def instant_multi_modal_quotes_page(request):
    """
    Enhanced instant multi-modal quotes page
    Extracted from FastAPI lines 1485-1497
    """
    from utils.countries import COUNTRIES, US_STATES
    
    context = {
        "user": request.user,
        "title": "Instant Multi-Modal Quotes - Cloverics",
        "countries": COUNTRIES,
        "us_states": US_STATES
    }
    
    return render(request, 'customer/instant_quotes.html', context)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def request_instant_quotes(request):
    """
    Request instant quotes from all active providers
    Extracted from FastAPI lines 1499-1552
    Core Freightos-competing functionality
    """
    try:
        # Extract form data
        data = request.data
        quote_request_data = {
            'customer': request.user,
            'origin_country': data.get('origin_country'),
            'origin_city': data.get('origin_city'),
            'destination_country': data.get('destination_country'),
            'destination_city': data.get('destination_city'),
            'origin_state': data.get('origin_state'),
            'destination_state': data.get('destination_state'),
            'weight_kg': float(data.get('weight_kg', 0)),
            'cargo_type': data.get('cargo_type', 'general'),
            'transport_type': data.get('transport_type', 'truck'),
            'urgency': data.get('urgency', 'standard')
        }
        
        # Create instant quote batch
        quote_batch = InstantQuoteBatch.objects.create(**quote_request_data)
        
        # Initialize quote aggregator service
        aggregator = InstantQuoteAggregator()
        quote_results = aggregator.get_instant_quotes(quote_batch)
        
        return Response({
            'success': True,
            'data': {
                'batch_id': str(quote_batch.batch_id),
                'processing_status': quote_batch.status,
                'quotes_requested': quote_batch.total_quotes_requested,
                'estimated_completion': '2-5 minutes'
            }
        })
        
    except Exception as e:
        logger.error(f"Instant quotes request failed: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=500)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_live_quote_updates(request, batch_id):
    """
    Get live updates for quote batch - real-time quote monitoring
    Extracted from FastAPI lines 1554-1605
    """
    try:
        # Get quote batch
        quote_batch = InstantQuoteBatch.objects.filter(
            batch_id=batch_id,
            customer=request.user
        ).first()
        
        if not quote_batch:
            return Response({
                'success': False,
                'error': 'Quote batch not found'
            }, status=404)
        
        # Get current quotes and insights
        quotes = quote_batch.get_quotes()
        insights = quote_batch.get_market_insights()
        
        return Response({
            'success': True,
            'data': {
                'batch_id': str(quote_batch.batch_id),
                'status': quote_batch.status,
                'quotes': quotes,
                'insights': insights,
                'total_providers': quote_batch.total_quotes_requested,
                'responded_providers': quote_batch.quotes_received,
                'response_time': float(quote_batch.response_time_seconds) if quote_batch.response_time_seconds else None,
                'completed_at': quote_batch.completed_at.isoformat() if quote_batch.completed_at else None
            }
        })
        
    except Exception as e:
        logger.error(f"Live quote updates failed: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=500)


@login_required
def multi_modal_optimizer_page(request):
    """
    Multi-Modal Route Optimizer page
    Extracted from FastAPI lines 1633-1653
    """
    from utils.countries import COUNTRIES
    
    context = {
        "user": request.user,
        "title": "Multi-Modal Route Optimizer - Cloverics",
        "countries": COUNTRIES
    }
    
    return render(request, 'customer/multi_modal_optimizer.html', context)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def optimize_multi_modal_routes(request):
    """
    Optimize multi-modal shipping routes for best efficiency
    Extracted from FastAPI lines 1774-1807
    """
    try:
        # Extract query parameters
        origin = request.GET.get('origin')
        destination = request.GET.get('destination')
        weight_kg = float(request.GET.get('weight_kg', 100))
        urgency = request.GET.get('urgency', 'standard')
        cargo_type = request.GET.get('cargo_type', 'general')
        
        requirements = {
            'weight_kg': weight_kg,
            'urgency': urgency,
            'cargo_type': cargo_type
        }
        
        # Create optimization record
        optimization = MultiModalOptimization.objects.create(
            customer=request.user,
            origin=origin,
            destination=destination,
            weight_kg=weight_kg,
            urgency=urgency,
            cargo_type=cargo_type
        )
        
        # Initialize optimizer service
        optimizer = MultiModalOptimizer()
        optimization_result = optimizer.optimize_routes(optimization, requirements)
        
        return Response({
            'success': True,
            'data': optimization_result
        })
        
    except Exception as e:
        logger.error(f"Multi-modal optimization failed: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=500)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def optimize_multi_modal_routes_post(request):
    """
    Advanced multi-modal route optimization API
    Extracted from FastAPI lines 1848-1900
    """
    try:
        data = request.data
        
        # Create optimization record
        optimization = MultiModalOptimization.objects.create(
            customer=request.user,
            origin=data.get('origin'),
            destination=data.get('destination'),
            weight_kg=float(data.get('weight_kg', 100)),
            urgency=data.get('urgency', 'standard'),
            cargo_type=data.get('cargo_type', 'general'),
            optimization_type=data.get('optimization_type', 'COST'),
            max_transit_time=data.get('max_transit_time'),
            max_budget=data.get('max_budget')
        )
        
        # Process optimization
        optimizer = MultiModalOptimizer()
        result = optimizer.process_advanced_optimization(optimization)
        
        return Response({
            'success': True,
            'data': {
                'optimization_id': str(optimization.optimization_id),
                'recommended_routes': result.get('routes', []),
                'analysis': result.get('analysis', {}),
                'savings_potential': result.get('savings', 0)
            }
        })
        
    except Exception as e:
        logger.error(f"Advanced optimization failed: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=500)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_real_time_carrier_rates(request):
    """
    Get real-time rates from integrated carrier network
    Extracted from FastAPI lines 1809-1842
    """
    try:
        origin = request.GET.get('origin')
        destination = request.GET.get('destination')
        weight_kg = float(request.GET.get('weight_kg', 100))
        urgency = request.GET.get('urgency', 'standard')
        
        shipment_details = {
            'weight_kg': weight_kg,
            'urgency': urgency,
            'origin': origin,
            'destination': destination
        }
        
        # Use carrier integration service
        from .services import CarrierNetworkIntegration
        carrier_network = CarrierNetworkIntegration()
        rates_result = carrier_network.get_real_time_rates(origin, destination, shipment_details)
        
        return Response({
            'success': True,
            'data': rates_result
        })
        
    except Exception as e:
        logger.error(f"Carrier rates request failed: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=500)