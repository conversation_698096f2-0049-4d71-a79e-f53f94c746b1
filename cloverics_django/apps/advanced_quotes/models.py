from django.db import models
from django.conf import settings
import uuid
import json
from datetime import datetime


class InstantQuoteBatch(models.Model):
    """
    Instant quote batch processing system
    Extracted from FastAPI lines 1499-1605
    """
    STATUS_CHOICES = [
        ('PENDING', 'Pending'),
        ('PROCESSING', 'Processing'),
        ('COMPLETED', 'Completed'),
        ('FAILED', 'Failed'),
    ]
    
    batch_id = models.CharField(max_length=50, unique=True, default=uuid.uuid4)
    customer = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='instant_quote_batches')
    
    # Route information
    origin_country = models.CharField(max_length=100)
    origin_city = models.CharField(max_length=100)
    origin_state = models.CharField(max_length=100, blank=True, null=True)
    destination_country = models.CharField(max_length=100)
    destination_city = models.Char<PERSON>ield(max_length=100)
    destination_state = models.Cha<PERSON><PERSON><PERSON>(max_length=100, blank=True, null=True)
    
    # Cargo details
    weight_kg = models.DecimalField(max_digits=10, decimal_places=2)
    cargo_type = models.CharField(max_length=50, default='general')
    transport_type = models.CharField(max_length=50, default='truck')
    urgency = models.CharField(max_length=20, default='standard')
    
    # Processing status
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='PENDING')
    total_quotes_requested = models.IntegerField(default=0)
    quotes_received = models.IntegerField(default=0)
    successful_quotes = models.IntegerField(default=0)
    failed_quotes = models.IntegerField(default=0)
    
    # Timing
    created_at = models.DateTimeField(auto_now_add=True)
    processing_started_at = models.DateTimeField(blank=True, null=True)
    completed_at = models.DateTimeField(blank=True, null=True)
    response_time_seconds = models.DecimalField(max_digits=10, decimal_places=3, blank=True, null=True)
    
    # Results
    quotes_data = models.JSONField(default=dict, blank=True)
    market_insights = models.JSONField(default=dict, blank=True)
    error_log = models.TextField(blank=True)
    
    class Meta:
        db_table = 'advanced_quotes_instant_batch'
        indexes = [
            models.Index(fields=['customer']),
            models.Index(fields=['status']),
            models.Index(fields=['created_at']),
            models.Index(fields=['batch_id']),
        ]
    
    def __str__(self):
        return f"Batch {self.batch_id} - {self.origin_city} to {self.destination_city}"
    
    def get_quotes(self):
        """Return processed quotes data"""
        return self.quotes_data.get('quotes', [])
    
    def get_market_insights(self):
        """Return market insights from quote analysis"""
        return self.market_insights.get('insights', {})
    
    def update_status(self, status, error_message=None):
        """Update batch processing status"""
        self.status = status
        if status == 'PROCESSING' and not self.processing_started_at:
            self.processing_started_at = datetime.now()
        elif status == 'COMPLETED':
            self.completed_at = datetime.now()
            if self.processing_started_at:
                delta = self.completed_at - self.processing_started_at
                self.response_time_seconds = delta.total_seconds()
        
        if error_message:
            self.error_log = error_message
        
        self.save()


class InstantQuoteProvider(models.Model):
    """
    Provider responses for instant quotes
    Extracted from quote aggregator system
    """
    batch = models.ForeignKey(InstantQuoteBatch, on_delete=models.CASCADE, related_name='provider_quotes')
    provider_id = models.IntegerField()
    provider_name = models.CharField(max_length=200)
    
    # Quote details
    quoted_price = models.DecimalField(max_digits=12, decimal_places=2)
    estimated_days = models.IntegerField()
    transport_mode = models.CharField(max_length=50)
    service_level = models.CharField(max_length=50)
    
    # Provider metrics
    provider_rating = models.DecimalField(max_digits=3, decimal_places=2, default=0.00)
    on_time_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0.00)
    response_time_minutes = models.IntegerField(default=0)
    
    # Quote validity
    valid_until = models.DateTimeField()
    terms_conditions = models.TextField(blank=True)
    special_requirements = models.TextField(blank=True)
    
    # Status
    is_available = models.BooleanField(default=True)
    confidence_score = models.DecimalField(max_digits=5, decimal_places=2, default=0.00)
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'advanced_quotes_instant_provider'
        indexes = [
            models.Index(fields=['batch']),
            models.Index(fields=['provider_id']),
            models.Index(fields=['quoted_price']),
            models.Index(fields=['confidence_score']),
        ]
    
    def __str__(self):
        return f"{self.provider_name} - ${self.quoted_price}"


class MultiModalOptimization(models.Model):
    """
    Multi-modal route optimization results
    Extracted from FastAPI lines 1774-1842
    """
    OPTIMIZATION_TYPES = [
        ('COST', 'Cost Optimization'),
        ('TIME', 'Time Optimization'),
        ('RELIABILITY', 'Reliability Optimization'),
        ('ENVIRONMENTAL', 'Environmental Optimization'),
    ]
    
    optimization_id = models.CharField(max_length=50, unique=True, default=uuid.uuid4)
    customer = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    
    # Route details
    origin = models.CharField(max_length=200)
    destination = models.CharField(max_length=200)
    weight_kg = models.DecimalField(max_digits=10, decimal_places=2)
    urgency = models.CharField(max_length=20)
    cargo_type = models.CharField(max_length=50)
    
    # Optimization parameters
    optimization_type = models.CharField(max_length=20, choices=OPTIMIZATION_TYPES, default='COST')
    max_transit_time = models.IntegerField(blank=True, null=True)  # days
    max_budget = models.DecimalField(max_digits=12, decimal_places=2, blank=True, null=True)
    
    # Results
    recommended_routes = models.JSONField(default=list)
    cost_analysis = models.JSONField(default=dict)
    time_analysis = models.JSONField(default=dict)
    risk_analysis = models.JSONField(default=dict)
    environmental_impact = models.JSONField(default=dict)
    
    # Processing
    processing_status = models.CharField(max_length=20, default='PENDING')
    optimization_score = models.DecimalField(max_digits=5, decimal_places=2, default=0.00)
    
    created_at = models.DateTimeField(auto_now_add=True)
    completed_at = models.DateTimeField(blank=True, null=True)
    
    class Meta:
        db_table = 'advanced_quotes_multimodal_optimization'
        indexes = [
            models.Index(fields=['customer']),
            models.Index(fields=['optimization_id']),
            models.Index(fields=['processing_status']),
            models.Index(fields=['created_at']),
        ]
    
    def __str__(self):
        return f"Optimization {self.optimization_id} - {self.origin} to {self.destination}"