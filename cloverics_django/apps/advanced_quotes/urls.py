from django.urls import path
from . import views

app_name = 'advanced_quotes'

urlpatterns = [
    # Instant Quotes System - Phase 9 extracted from FastAPI
    path('customer/instant-quotes/', views.instant_quotes_page, name='instant_quotes'),
    path('customer/instant-multi-modal-quotes/', views.instant_multi_modal_quotes_page, name='instant_multi_modal_quotes'),
    
    # Multi-Modal Optimizer
    path('customer/multi-modal-optimizer/', views.multi_modal_optimizer_page, name='multi_modal_optimizer'),
    
    # API Endpoints - Phase 9 extracted from FastAPI
    path('api/quotes/instant-request/', views.request_instant_quotes, name='request_instant_quotes'),
    path('api/quotes/live-updates/<str:batch_id>/', views.get_live_quote_updates, name='live_quote_updates'),
    
    # Multi-Modal Optimization APIs
    path('api/optimize/multi-modal-routes/', views.optimize_multi_modal_routes, name='optimize_routes_get'),
    path('api/multi-modal/optimize/', views.optimize_multi_modal_routes_post, name='optimize_routes_post'),
    
    # Carrier Integration APIs
    path('api/carriers/real-time-rates/', views.get_real_time_carrier_rates, name='real_time_rates'),
    
    # Working endpoints only - remove missing functions
    # TODO: Re-implement missing analytics, templates, and automation views
]