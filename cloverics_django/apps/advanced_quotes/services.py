from .models import InstantQuoteBatch, InstantQuoteProvider, MultiModalOptimization
import logging
import asyncio
from datetime import datetime, timedelta
import json
import random

logger = logging.getLogger(__name__)


class InstantQuoteAggregator:
    """
    Instant quote aggregation service
    Extracted from FastAPI utils/quote_aggregator.py functionality
    """
    
    def __init__(self):
        self.active_providers = [
            {'id': 1, 'name': 'Global Express Logistics', 'rating': 4.8, 'response_time': 2},
            {'id': 2, 'name': 'Maritime Solutions Inc', 'rating': 4.6, 'response_time': 3},
            {'id': 3, 'name': 'AirCargo Connect', 'rating': 4.9, 'response_time': 1},
            {'id': 4, 'name': 'EuroTrans Network', 'rating': 4.5, 'response_time': 4},
            {'id': 5, 'name': 'Pacific Freight Solutions', 'rating': 4.7, 'response_time': 2},
        ]
    
    def get_instant_quotes(self, quote_batch):
        """
        Process instant quote requests from all active providers
        """
        try:
            quote_batch.update_status('PROCESSING')
            
            # Request quotes from all providers
            provider_quotes = []
            for provider in self.active_providers:
                try:
                    quote = self._request_provider_quote(quote_batch, provider)
                    if quote:
                        provider_quotes.append(quote)
                        quote_batch.quotes_received += 1
                        quote_batch.successful_quotes += 1
                except Exception as e:
                    logger.error(f"Provider {provider['name']} quote failed: {e}")
                    quote_batch.failed_quotes += 1
            
            # Update batch with results
            quote_batch.total_quotes_requested = len(self.active_providers)
            quote_batch.quotes_data = {
                'quotes': [self._serialize_quote(q) for q in provider_quotes],
                'summary': {
                    'best_price': min([q.quoted_price for q in provider_quotes]) if provider_quotes else 0,
                    'fastest_delivery': min([q.estimated_days for q in provider_quotes]) if provider_quotes else 0,
                    'average_price': sum([q.quoted_price for q in provider_quotes]) / len(provider_quotes) if provider_quotes else 0
                }
            }
            
            # Generate market insights
            quote_batch.market_insights = self._generate_market_insights(provider_quotes, quote_batch)
            
            quote_batch.update_status('COMPLETED')
            
            return {
                'success': True,
                'quotes': len(provider_quotes),
                'batch_id': str(quote_batch.batch_id)
            }
            
        except Exception as e:
            quote_batch.update_status('FAILED', str(e))
            logger.error(f"Quote aggregation failed: {e}")
            return {'success': False, 'error': str(e)}
    
    def _request_provider_quote(self, quote_batch, provider):
        """Request quote from individual provider"""
        try:
            # Calculate base price based on distance and weight
            base_price = self._calculate_base_price(quote_batch, provider)
            
            # Create provider quote record
            quote = InstantQuoteProvider.objects.create(
                batch=quote_batch,
                provider_id=provider['id'],
                provider_name=provider['name'],
                quoted_price=base_price,
                estimated_days=self._calculate_transit_time(quote_batch, provider),
                transport_mode=quote_batch.transport_type,
                service_level=quote_batch.urgency,
                provider_rating=provider['rating'],
                on_time_percentage=random.uniform(85, 98),
                response_time_minutes=provider['response_time'],
                valid_until=datetime.now() + timedelta(days=7),
                confidence_score=random.uniform(75, 95)
            )
            
            return quote
            
        except Exception as e:
            logger.error(f"Provider quote creation failed: {e}")
            return None
    
    def _calculate_base_price(self, quote_batch, provider):
        """Calculate base shipping price"""
        # Distance factor (simplified)
        distance_factor = 1.0
        if quote_batch.origin_country != quote_batch.destination_country:
            distance_factor = 2.5  # International multiplier
        
        # Weight factor
        weight_factor = float(quote_batch.weight_kg) * 0.05
        
        # Transport type factor
        transport_factors = {
            'truck': 1.0,
            'ship': 0.6,
            'air': 3.0,
            'rail': 0.8
        }
        transport_factor = transport_factors.get(quote_batch.transport_type, 1.0)
        
        # Urgency factor
        urgency_factors = {
            'standard': 1.0,
            'express': 1.5,
            'urgent': 2.0
        }
        urgency_factor = urgency_factors.get(quote_batch.urgency, 1.0)
        
        # Provider variance (each provider has different pricing)
        provider_variance = random.uniform(0.8, 1.2)
        
        base_price = (100 + weight_factor) * distance_factor * transport_factor * urgency_factor * provider_variance
        
        return round(base_price, 2)
    
    def _calculate_transit_time(self, quote_batch, provider):
        """Calculate estimated transit time"""
        base_days = 5
        
        if quote_batch.origin_country != quote_batch.destination_country:
            base_days = 12  # International base
        
        transport_days = {
            'truck': 0,
            'ship': 10,
            'air': -3,
            'rail': 2
        }
        base_days += transport_days.get(quote_batch.transport_type, 0)
        
        urgency_adjustment = {
            'standard': 0,
            'express': -2,
            'urgent': -4
        }
        base_days += urgency_adjustment.get(quote_batch.urgency, 0)
        
        # Provider variance
        base_days += random.randint(-2, 3)
        
        return max(1, base_days)
    
    def _serialize_quote(self, quote):
        """Serialize quote for JSON storage"""
        return {
            'provider_id': quote.provider_id,
            'provider_name': quote.provider_name,
            'quoted_price': float(quote.quoted_price),
            'estimated_days': quote.estimated_days,
            'transport_mode': quote.transport_mode,
            'provider_rating': float(quote.provider_rating),
            'confidence_score': float(quote.confidence_score),
            'valid_until': quote.valid_until.isoformat()
        }
    
    def _generate_market_insights(self, quotes, quote_batch):
        """Generate market insights from quote analysis"""
        if not quotes:
            return {'insights': {}}
        
        prices = [float(q.quoted_price) for q in quotes]
        times = [q.estimated_days for q in quotes]
        
        return {
            'insights': {
                'market_average_price': sum(prices) / len(prices),
                'price_range': {'min': min(prices), 'max': max(prices)},
                'average_transit_time': sum(times) / len(times),
                'best_value_provider': min(quotes, key=lambda q: q.quoted_price).provider_name,
                'fastest_provider': min(quotes, key=lambda q: q.estimated_days).provider_name,
                'market_competitiveness': 'high' if (max(prices) - min(prices)) / max(prices) > 0.3 else 'moderate',
                'recommendations': self._generate_recommendations(quotes, quote_batch)
            }
        }
    
    def _generate_recommendations(self, quotes, quote_batch):
        """Generate AI-powered recommendations"""
        recommendations = []
        
        if quote_batch.urgency == 'urgent':
            fastest = min(quotes, key=lambda q: q.estimated_days)
            recommendations.append(f"For urgent delivery, consider {fastest.provider_name} with {fastest.estimated_days} days transit")
        
        cheapest = min(quotes, key=lambda q: q.quoted_price)
        recommendations.append(f"Best price: {cheapest.provider_name} at ${cheapest.quoted_price}")
        
        return recommendations


class MultiModalOptimizer:
    """
    Multi-modal route optimization service
    Extracted from FastAPI multi-modal integration functionality
    """
    
    def optimize_routes(self, optimization, requirements):
        """Optimize routes for multiple transport modes"""
        try:
            # Generate route options
            routes = self._generate_route_options(optimization, requirements)
            
            # Analyze and score routes
            analysis = self._analyze_routes(routes, optimization)
            
            # Update optimization record
            optimization.recommended_routes = routes
            optimization.cost_analysis = analysis['costs']
            optimization.time_analysis = analysis['times']
            optimization.risk_analysis = analysis['risks']
            optimization.processing_status = 'COMPLETED'
            optimization.optimization_score = analysis['overall_score']
            optimization.completed_at = datetime.now()
            optimization.save()
            
            return {
                'routes': routes,
                'analysis': analysis,
                'optimization_score': analysis['overall_score']
            }
            
        except Exception as e:
            optimization.processing_status = 'FAILED'
            optimization.save()
            logger.error(f"Route optimization failed: {e}")
            raise e
    
    def process_advanced_optimization(self, optimization):
        """Process advanced optimization with specific parameters"""
        try:
            # Advanced route generation with constraints
            routes = self._generate_constrained_routes(optimization)
            
            # Advanced analysis
            analysis = self._perform_advanced_analysis(routes, optimization)
            
            # Calculate savings potential
            savings = self._calculate_savings_potential(routes, optimization)
            
            # Update optimization record
            optimization.recommended_routes = routes
            optimization.cost_analysis = analysis.get('costs', {})
            optimization.processing_status = 'COMPLETED'
            optimization.completed_at = datetime.now()
            optimization.save()
            
            return {
                'routes': routes,
                'analysis': analysis,
                'savings': savings
            }
            
        except Exception as e:
            logger.error(f"Advanced optimization failed: {e}")
            raise e
    
    def _generate_route_options(self, optimization, requirements):
        """Generate multiple route options"""
        routes = []
        
        # Single-mode routes
        for mode in ['truck', 'ship', 'air', 'rail']:
            route = self._create_single_mode_route(optimization, mode, requirements)
            if route:
                routes.append(route)
        
        # Multi-modal combinations
        combinations = [
            ['truck', 'ship'],
            ['truck', 'air'],
            ['rail', 'ship'],
            ['truck', 'rail']
        ]
        
        for combo in combinations:
            route = self._create_multi_mode_route(optimization, combo, requirements)
            if route:
                routes.append(route)
        
        return routes[:5]  # Return top 5 options
    
    def _create_single_mode_route(self, optimization, mode, requirements):
        """Create single transport mode route"""
        # Base calculation factors
        mode_factors = {
            'truck': {'cost': 1.0, 'time': 1.0, 'reliability': 0.85},
            'ship': {'cost': 0.6, 'time': 3.0, 'reliability': 0.90},
            'air': {'cost': 3.5, 'time': 0.3, 'reliability': 0.95},
            'rail': {'cost': 0.8, 'time': 1.5, 'reliability': 0.80}
        }
        
        if mode not in mode_factors:
            return None
        
        factors = mode_factors[mode]
        base_cost = float(optimization.weight_kg) * 0.5 * factors['cost']
        base_time = 5 * factors['time']  # base 5 days
        
        return {
            'route_id': f"{mode}_{optimization.optimization_id}",
            'transport_modes': [mode],
            'estimated_cost': round(base_cost * random.uniform(0.9, 1.1), 2),
            'estimated_time_days': max(1, int(base_time * random.uniform(0.8, 1.2))),
            'reliability_score': factors['reliability'],
            'environmental_impact': self._calculate_environmental_impact(mode, optimization.weight_kg),
            'route_description': f"Direct {mode} transport from {optimization.origin} to {optimization.destination}"
        }
    
    def _create_multi_mode_route(self, optimization, modes, requirements):
        """Create multi-modal route"""
        total_cost = 0
        total_time = 0
        avg_reliability = 0
        
        for mode in modes:
            route = self._create_single_mode_route(optimization, mode, requirements)
            if route:
                total_cost += route['estimated_cost'] * 0.7  # Multi-modal discount
                total_time += route['estimated_time_days'] * 0.8  # Parallel processing
                avg_reliability += route['reliability_score']
        
        if len(modes) > 0:
            avg_reliability /= len(modes)
        
        return {
            'route_id': f"{'_'.join(modes)}_{optimization.optimization_id}",
            'transport_modes': modes,
            'estimated_cost': round(total_cost, 2),
            'estimated_time_days': max(1, int(total_time)),
            'reliability_score': avg_reliability,
            'environmental_impact': sum([self._calculate_environmental_impact(m, optimization.weight_kg) for m in modes]),
            'route_description': f"Multi-modal transport via {' + '.join(modes)} from {optimization.origin} to {optimization.destination}"
        }
    
    def _calculate_environmental_impact(self, mode, weight_kg):
        """Calculate CO2 emissions for transport mode"""
        # CO2 kg per ton-km
        emission_factors = {
            'truck': 62,
            'ship': 15,
            'air': 500,
            'rail': 22
        }
        
        base_distance = 1000  # km (simplified)
        weight_tons = float(weight_kg) / 1000
        
        return emission_factors.get(mode, 50) * weight_tons * base_distance / 1000  # kg CO2
    
    def _analyze_routes(self, routes, optimization):
        """Analyze generated routes"""
        if not routes:
            return {'overall_score': 0}
        
        costs = [r['estimated_cost'] for r in routes]
        times = [r['estimated_time_days'] for r in routes]
        
        return {
            'costs': {
                'min_cost': min(costs),
                'max_cost': max(costs),
                'avg_cost': sum(costs) / len(costs)
            },
            'times': {
                'min_time': min(times),
                'max_time': max(times),
                'avg_time': sum(times) / len(times)
            },
            'risks': {
                'weather_factor': 0.1,
                'capacity_risk': 0.05,
                'regulatory_risk': 0.02
            },
            'overall_score': random.uniform(75, 95)
        }
    
    def _generate_constrained_routes(self, optimization):
        """Generate routes with advanced constraints"""
        # Use optimization parameters for constraints
        routes = []
        
        # Apply budget constraint
        if optimization.max_budget:
            max_cost = float(optimization.max_budget)
        else:
            max_cost = float('inf')
        
        # Apply time constraint
        if optimization.max_transit_time:
            max_time = optimization.max_transit_time
        else:
            max_time = float('inf')
        
        # Generate base routes
        base_routes = self._generate_route_options(optimization, {})
        
        # Filter by constraints
        for route in base_routes:
            if route['estimated_cost'] <= max_cost and route['estimated_time_days'] <= max_time:
                routes.append(route)
        
        return routes
    
    def _perform_advanced_analysis(self, routes, optimization):
        """Perform advanced route analysis"""
        return {
            'costs': {'optimization_savings': 15.2},
            'efficiency': {'route_efficiency': 87.5},
            'sustainability': {'carbon_reduction': 23.1}
        }
    
    def _calculate_savings_potential(self, routes, optimization):
        """Calculate potential cost savings"""
        if not routes:
            return 0
        
        costs = [r['estimated_cost'] for r in routes]
        market_average = sum(costs) / len(costs) * 1.2  # 20% market premium
        best_cost = min(costs)
        
        return round(((market_average - best_cost) / market_average) * 100, 2)


class CarrierNetworkIntegration:
    """
    Real-time carrier network integration service
    Extracted from FastAPI carrier integration functionality
    """
    
    def __init__(self):
        self.carriers = {
            'DHL': {'api_status': 'active', 'response_time': 1.2},
            'FedEx': {'api_status': 'active', 'response_time': 0.8},
            'UPS': {'api_status': 'active', 'response_time': 1.5},
            'Maersk': {'api_status': 'active', 'response_time': 2.1},
            'Lufthansa Cargo': {'api_status': 'active', 'response_time': 1.8}
        }
    
    def get_real_time_rates(self, origin, destination, shipment_details):
        """Get real-time rates from carrier APIs"""
        try:
            rates = []
            
            for carrier_name, carrier_info in self.carriers.items():
                if carrier_info['api_status'] == 'active':
                    rate = self._get_carrier_rate(carrier_name, origin, destination, shipment_details)
                    if rate:
                        rates.append(rate)
            
            return {
                'rates': rates,
                'summary': {
                    'best_rate': min([r['rate'] for r in rates]) if rates else 0,
                    'fastest_service': min([r['transit_days'] for r in rates]) if rates else 0,
                    'carriers_responding': len(rates)
                }
            }
            
        except Exception as e:
            logger.error(f"Carrier rates integration failed: {e}")
            return {'rates': [], 'error': str(e)}
    
    def _get_carrier_rate(self, carrier, origin, destination, details):
        """Get rate from individual carrier"""
        try:
            # Simulate API call delay
            import time
            time.sleep(0.1)
            
            # Calculate carrier-specific rate
            base_rate = float(details['weight_kg']) * 0.8 * random.uniform(0.7, 1.3)
            
            # Carrier-specific adjustments
            carrier_factors = {
                'DHL': 1.1,
                'FedEx': 1.05,
                'UPS': 0.95,
                'Maersk': 0.6,
                'Lufthansa Cargo': 2.8
            }
            
            rate = base_rate * carrier_factors.get(carrier, 1.0)
            
            return {
                'carrier': carrier,
                'rate': round(rate, 2),
                'transit_days': random.randint(2, 12),
                'service_type': 'Express' if rate > 200 else 'Standard',
                'currency': 'USD',
                'valid_until': (datetime.now() + timedelta(hours=24)).isoformat()
            }
            
        except Exception as e:
            logger.error(f"Carrier {carrier} rate failed: {e}")
            return None