# Generated by Django 5.2.1 on 2025-08-07 12:45

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('advanced_quotes', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='instantquotebatch',
            name='customer',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='instant_quote_batches', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='instantquoteprovider',
            name='batch',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='provider_quotes', to='advanced_quotes.instantquotebatch'),
        ),
        migrations.AddField(
            model_name='multimodaloptimization',
            name='customer',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddIndex(
            model_name='instantquotebatch',
            index=models.Index(fields=['customer'], name='advanced_qu_custome_d8f53f_idx'),
        ),
        migrations.AddIndex(
            model_name='instantquotebatch',
            index=models.Index(fields=['status'], name='advanced_qu_status_2a0b39_idx'),
        ),
        migrations.AddIndex(
            model_name='instantquotebatch',
            index=models.Index(fields=['created_at'], name='advanced_qu_created_a1c715_idx'),
        ),
        migrations.AddIndex(
            model_name='instantquotebatch',
            index=models.Index(fields=['batch_id'], name='advanced_qu_batch_i_78f75c_idx'),
        ),
        migrations.AddIndex(
            model_name='instantquoteprovider',
            index=models.Index(fields=['batch'], name='advanced_qu_batch_i_08602a_idx'),
        ),
        migrations.AddIndex(
            model_name='instantquoteprovider',
            index=models.Index(fields=['provider_id'], name='advanced_qu_provide_2a6a26_idx'),
        ),
        migrations.AddIndex(
            model_name='instantquoteprovider',
            index=models.Index(fields=['quoted_price'], name='advanced_qu_quoted__b56ab4_idx'),
        ),
        migrations.AddIndex(
            model_name='instantquoteprovider',
            index=models.Index(fields=['confidence_score'], name='advanced_qu_confide_07e6f4_idx'),
        ),
        migrations.AddIndex(
            model_name='multimodaloptimization',
            index=models.Index(fields=['customer'], name='advanced_qu_custome_0b6ebd_idx'),
        ),
        migrations.AddIndex(
            model_name='multimodaloptimization',
            index=models.Index(fields=['optimization_id'], name='advanced_qu_optimiz_cd6710_idx'),
        ),
        migrations.AddIndex(
            model_name='multimodaloptimization',
            index=models.Index(fields=['processing_status'], name='advanced_qu_process_91f969_idx'),
        ),
        migrations.AddIndex(
            model_name='multimodaloptimization',
            index=models.Index(fields=['created_at'], name='advanced_qu_created_8ec834_idx'),
        ),
    ]
