# Generated by Django 5.2.1 on 2025-08-07 12:45

import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='InstantQuoteBatch',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('batch_id', models.CharField(default=uuid.uuid4, max_length=50, unique=True)),
                ('origin_country', models.CharField(max_length=100)),
                ('origin_city', models.CharField(max_length=100)),
                ('origin_state', models.CharField(blank=True, max_length=100, null=True)),
                ('destination_country', models.CharField(max_length=100)),
                ('destination_city', models.CharField(max_length=100)),
                ('destination_state', models.CharField(blank=True, max_length=100, null=True)),
                ('weight_kg', models.DecimalField(decimal_places=2, max_digits=10)),
                ('cargo_type', models.CharField(default='general', max_length=50)),
                ('transport_type', models.CharField(default='truck', max_length=50)),
                ('urgency', models.CharField(default='standard', max_length=20)),
                ('status', models.CharField(choices=[('PENDING', 'Pending'), ('PROCESSING', 'Processing'), ('COMPLETED', 'Completed'), ('FAILED', 'Failed')], default='PENDING', max_length=20)),
                ('total_quotes_requested', models.IntegerField(default=0)),
                ('quotes_received', models.IntegerField(default=0)),
                ('successful_quotes', models.IntegerField(default=0)),
                ('failed_quotes', models.IntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('processing_started_at', models.DateTimeField(blank=True, null=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('response_time_seconds', models.DecimalField(blank=True, decimal_places=3, max_digits=10, null=True)),
                ('quotes_data', models.JSONField(blank=True, default=dict)),
                ('market_insights', models.JSONField(blank=True, default=dict)),
                ('error_log', models.TextField(blank=True)),
            ],
            options={
                'db_table': 'advanced_quotes_instant_batch',
            },
        ),
        migrations.CreateModel(
            name='InstantQuoteProvider',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('provider_id', models.IntegerField()),
                ('provider_name', models.CharField(max_length=200)),
                ('quoted_price', models.DecimalField(decimal_places=2, max_digits=12)),
                ('estimated_days', models.IntegerField()),
                ('transport_mode', models.CharField(max_length=50)),
                ('service_level', models.CharField(max_length=50)),
                ('provider_rating', models.DecimalField(decimal_places=2, default=0.0, max_digits=3)),
                ('on_time_percentage', models.DecimalField(decimal_places=2, default=0.0, max_digits=5)),
                ('response_time_minutes', models.IntegerField(default=0)),
                ('valid_until', models.DateTimeField()),
                ('terms_conditions', models.TextField(blank=True)),
                ('special_requirements', models.TextField(blank=True)),
                ('is_available', models.BooleanField(default=True)),
                ('confidence_score', models.DecimalField(decimal_places=2, default=0.0, max_digits=5)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'db_table': 'advanced_quotes_instant_provider',
            },
        ),
        migrations.CreateModel(
            name='MultiModalOptimization',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('optimization_id', models.CharField(default=uuid.uuid4, max_length=50, unique=True)),
                ('origin', models.CharField(max_length=200)),
                ('destination', models.CharField(max_length=200)),
                ('weight_kg', models.DecimalField(decimal_places=2, max_digits=10)),
                ('urgency', models.CharField(max_length=20)),
                ('cargo_type', models.CharField(max_length=50)),
                ('optimization_type', models.CharField(choices=[('COST', 'Cost Optimization'), ('TIME', 'Time Optimization'), ('RELIABILITY', 'Reliability Optimization'), ('ENVIRONMENTAL', 'Environmental Optimization')], default='COST', max_length=20)),
                ('max_transit_time', models.IntegerField(blank=True, null=True)),
                ('max_budget', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True)),
                ('recommended_routes', models.JSONField(default=list)),
                ('cost_analysis', models.JSONField(default=dict)),
                ('time_analysis', models.JSONField(default=dict)),
                ('risk_analysis', models.JSONField(default=dict)),
                ('environmental_impact', models.JSONField(default=dict)),
                ('processing_status', models.CharField(default='PENDING', max_length=20)),
                ('optimization_score', models.DecimalField(decimal_places=2, default=0.0, max_digits=5)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'db_table': 'advanced_quotes_multimodal_optimization',
            },
        ),
    ]
