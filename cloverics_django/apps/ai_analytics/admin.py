from django.contrib import admin
from .models import AIInsight, PricePrediction, DemandForecast, MarketIntelligence, RouteOptimization

@admin.register(AIInsight)
class AIInsightAdmin(admin.ModelAdmin):
    list_display = ['title', 'insight_type', 'user', 'priority', 'confidence_score', 'generated_at', 'viewed']
    list_filter = ['insight_type', 'priority', 'viewed', 'acted_upon', 'generated_at']
    search_fields = ['title', 'description', 'user__username']
    readonly_fields = ['generated_at']
    ordering = ['-generated_at']

@admin.register(PricePrediction)
class PricePredictionAdmin(admin.ModelAdmin):
    list_display = ['origin_country', 'destination_country', 'transport_mode', 'predicted_price', 'model_accuracy', 'created_at']
    list_filter = ['transport_mode', 'cargo_type', 'created_at']
    search_fields = ['origin_country', 'destination_country', 'cargo_type']
    readonly_fields = ['created_at']

@admin.register(DemandForecast)
class DemandForecastAdmin(admin.ModelAdmin):
    list_display = ['route', 'transport_mode', 'forecast_period', 'predicted_volume', 'confidence_score', 'created_at']
    list_filter = ['transport_mode', 'forecast_period', 'created_at']
    search_fields = ['route']
    readonly_fields = ['created_at']

@admin.register(MarketIntelligence)
class MarketIntelligenceAdmin(admin.ModelAdmin):
    list_display = ['region', 'market_size', 'growth_rate', 'last_updated']
    list_filter = ['region', 'last_updated']
    search_fields = ['region']
    readonly_fields = ['last_updated']

@admin.register(RouteOptimization)
class RouteOptimizationAdmin(admin.ModelAdmin):
    list_display = ['user', 'origin', 'destination', 'cost_savings', 'created_at']
    list_filter = ['created_at']
    search_fields = ['origin', 'destination', 'user__username']
    readonly_fields = ['created_at']