from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone

User = get_user_model()

class AIInsight(models.Model):
    """AI-generated insights and recommendations"""
    INSIGHT_TYPES = [
        ('cost_optimization', 'Cost Optimization'),
        ('route_optimization', 'Route Optimization'),
        ('demand_forecasting', 'Demand Forecasting'),
        ('price_prediction', 'Price Prediction'),
        ('performance_analysis', 'Performance Analysis'),
        ('market_intelligence', 'Market Intelligence'),
    ]
    
    PRIORITY_LEVELS = [
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('critical', 'Critical'),
    ]
    
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    insight_type = models.CharField(max_length=50, choices=INSIGHT_TYPES)
    title = models.CharField(max_length=200)
    description = models.TextField()
    priority = models.Char<PERSON>ield(max_length=20, choices=PRIORITY_LEVELS, default='medium')
    confidence_score = models.FloatField(help_text="AI confidence score 0-1")
    impact_assessment = models.TextField()
    recommendations = models.JSONField(default=list)
    metadata = models.JSONField(default=dict)
    generated_at = models.DateTimeField(default=timezone.now)
    viewed = models.BooleanField(default=False)
    acted_upon = models.BooleanField(default=False)
    feedback_rating = models.IntegerField(null=True, blank=True, help_text="User feedback 1-5")
    
    class Meta:
        ordering = ['-generated_at']
        indexes = [
            models.Index(fields=['user', 'insight_type']),
            models.Index(fields=['priority', 'viewed']),
        ]

class PricePrediction(models.Model):
    """AI price prediction models and results"""
    origin_country = models.CharField(max_length=100)
    destination_country = models.CharField(max_length=100)
    transport_mode = models.CharField(max_length=50)
    cargo_type = models.CharField(max_length=100)
    weight_kg = models.FloatField()
    predicted_price = models.DecimalField(max_digits=12, decimal_places=2)
    confidence_interval_low = models.DecimalField(max_digits=12, decimal_places=2)
    confidence_interval_high = models.DecimalField(max_digits=12, decimal_places=2)
    model_accuracy = models.FloatField(help_text="Model accuracy percentage")
    factors_considered = models.JSONField(default=list)
    created_at = models.DateTimeField(default=timezone.now)
    actual_price = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True)
    
    class Meta:
        ordering = ['-created_at']

class DemandForecast(models.Model):
    """Demand forecasting predictions"""
    route = models.CharField(max_length=200)
    transport_mode = models.CharField(max_length=50)
    forecast_period = models.CharField(max_length=50)
    predicted_volume = models.IntegerField()
    confidence_score = models.FloatField()
    seasonal_factors = models.JSONField(default=dict)
    economic_indicators = models.JSONField(default=dict)
    historical_data_points = models.IntegerField()
    created_at = models.DateTimeField(default=timezone.now)
    
    class Meta:
        ordering = ['-created_at']

class MarketIntelligence(models.Model):
    """Market intelligence data and analysis"""
    region = models.CharField(max_length=100)
    market_size = models.DecimalField(max_digits=15, decimal_places=2)
    growth_rate = models.FloatField()
    key_trends = models.JSONField(default=list)
    competitor_analysis = models.JSONField(default=dict)
    pricing_benchmarks = models.JSONField(default=dict)
    regulatory_updates = models.JSONField(default=list)
    data_sources = models.JSONField(default=list)
    last_updated = models.DateTimeField(default=timezone.now)
    
    class Meta:
        ordering = ['-last_updated']

class RouteOptimization(models.Model):
    """Route optimization analyses and recommendations"""
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    origin = models.CharField(max_length=200)
    destination = models.CharField(max_length=200)
    cargo_details = models.JSONField(default=dict)
    optimization_criteria = models.JSONField(default=list)
    recommended_route = models.JSONField(default=dict)
    cost_savings = models.DecimalField(max_digits=10, decimal_places=2)
    time_savings = models.DurationField()
    environmental_impact = models.JSONField(default=dict)
    created_at = models.DateTimeField(default=timezone.now)
    
    class Meta:
        ordering = ['-created_at']