from django.shortcuts import render
from django.http import JsonResponse
from django.contrib.auth.decorators import login_required
from django.utils.decorators import method_decorator
from django.views import View
from django.utils import timezone
from django.db.models import Avg, Count, Sum
from datetime import timed<PERSON><PERSON>, datetime
import json
import logging

from .models import AIInsight, PricePrediction, DemandForecast, MarketIntelligence, RouteOptimization

logger = logging.getLogger(__name__)

@method_decorator(login_required, name='dispatch')
class AIAnalyticsView(View):
    """AI Analytics dashboard and comprehensive analytics"""
    
    def get(self, request):
        """AI Analytics dashboard"""
        try:
            # Get user insights
            insights = AIInsight.objects.filter(user=request.user).order_by('-generated_at')[:5]
            
            # Get recent predictions
            predictions = PricePrediction.objects.order_by('-created_at')[:10]
            
            # Get market intelligence
            market_data = MarketIntelligence.objects.order_by('-last_updated').first()
            
            context = {
                'insights': insights,
                'predictions': predictions,
                'market_data': market_data,
                'title': 'AI Analytics & Intelligence - Cloverics'
            }
            
            return render(request, 'ai_analytics/dashboard.html', context)
            
        except Exception as e:
            logger.error(f"Error in AI analytics dashboard: {e}")
            return JsonResponse({'error': 'Failed to load AI analytics'}, status=500)

@method_decorator(login_required, name='dispatch')
class ComprehensiveAnalyticsView(View):
    """Comprehensive analytics with AI-powered insights"""
    
    def get(self, request):
        """Get comprehensive analytics data"""
        try:
            # Demand forecasting
            demand_forecasts = DemandForecast.objects.filter(
                created_at__gte=timezone.now() - timedelta(days=30)
            ).order_by('-confidence_score')[:5]
            
            # Price predictions
            price_predictions = PricePrediction.objects.filter(
                created_at__gte=timezone.now() - timedelta(days=7)
            ).order_by('-confidence_interval_high')[:10]
            
            # Route optimizations
            route_optimizations = RouteOptimization.objects.filter(
                user=request.user,
                created_at__gte=timezone.now() - timedelta(days=14)
            ).order_by('-cost_savings')[:5]
            
            # Generate insights summary
            insights_summary = {
                'total_insights': AIInsight.objects.filter(user=request.user).count(),
                'high_priority_insights': AIInsight.objects.filter(
                    user=request.user, priority='high'
                ).count(),
                'avg_confidence': AIInsight.objects.filter(
                    user=request.user
                ).aggregate(avg_conf=Avg('confidence_score'))['avg_conf'] or 0,
                'recent_insights': AIInsight.objects.filter(
                    user=request.user,
                    generated_at__gte=timezone.now() - timedelta(days=7)
                ).count()
            }
            
            return JsonResponse({
                'success': True,
                'data': {
                    'demand_forecasts': [
                        {
                            'route': forecast.route,
                            'transport_mode': forecast.transport_mode,
                            'predicted_volume': forecast.predicted_volume,
                            'confidence_score': forecast.confidence_score,
                            'forecast_period': forecast.forecast_period
                        } for forecast in demand_forecasts
                    ],
                    'price_predictions': [
                        {
                            'route': f"{pred.origin_country} → {pred.destination_country}",
                            'transport_mode': pred.transport_mode,
                            'predicted_price': float(pred.predicted_price),
                            'confidence_low': float(pred.confidence_interval_low),
                            'confidence_high': float(pred.confidence_interval_high),
                            'model_accuracy': pred.model_accuracy
                        } for pred in price_predictions
                    ],
                    'route_optimizations': [
                        {
                            'route': f"{opt.origin} → {opt.destination}",
                            'cost_savings': float(opt.cost_savings),
                            'time_savings': str(opt.time_savings),
                            'environmental_impact': opt.environmental_impact
                        } for opt in route_optimizations
                    ],
                    'insights_summary': insights_summary
                }
            })
            
        except Exception as e:
            logger.error(f"Comprehensive analytics error: {e}")
            return JsonResponse({'error': 'Analytics generation failed'}, status=500)

@method_decorator(login_required, name='dispatch')
class DemandForecastView(View):
    """Demand forecasting API"""
    
    def post(self, request):
        """Generate demand forecast"""
        try:
            data = json.loads(request.body)
            route = data.get('route')
            transport_mode = data.get('transport_mode', 'truck')
            forecast_period = data.get('forecast_period', 'monthly')
            
            # AI-powered demand forecasting (simplified for demo)
            import random
            predicted_volume = random.randint(50, 500)
            confidence_score = random.uniform(0.75, 0.95)
            
            forecast = DemandForecast.objects.create(
                route=route,
                transport_mode=transport_mode,
                forecast_period=forecast_period,
                predicted_volume=predicted_volume,
                confidence_score=confidence_score,
                seasonal_factors={'seasonal_adjustment': 1.1},
                economic_indicators={'gdp_growth': 2.3, 'inflation': 1.8},
                historical_data_points=120
            )
            
            return JsonResponse({
                'success': True,
                'forecast': {
                    'id': forecast.id,
                    'route': forecast.route,
                    'predicted_volume': forecast.predicted_volume,
                    'confidence_score': forecast.confidence_score,
                    'forecast_period': forecast.forecast_period
                }
            })
            
        except Exception as e:
            logger.error(f"Demand forecast error: {e}")
            return JsonResponse({'error': 'Forecast generation failed'}, status=500)

@method_decorator(login_required, name='dispatch')
class PricePredictionView(View):
    """Price prediction API"""
    
    def post(self, request):
        """Generate price prediction"""
        try:
            data = json.loads(request.body)
            origin = data.get('origin_country')
            destination = data.get('destination_country')
            transport_mode = data.get('transport_mode', 'truck')
            cargo_type = data.get('cargo_type', 'general')
            weight_kg = float(data.get('weight_kg', 1000))
            
            # AI price prediction algorithm (simplified)
            import random
            base_price = random.uniform(2.0, 8.0) * weight_kg
            confidence_low = base_price * 0.85
            confidence_high = base_price * 1.15
            
            prediction = PricePrediction.objects.create(
                origin_country=origin,
                destination_country=destination,
                transport_mode=transport_mode,
                cargo_type=cargo_type,
                weight_kg=weight_kg,
                predicted_price=base_price,
                confidence_interval_low=confidence_low,
                confidence_interval_high=confidence_high,
                model_accuracy=random.uniform(82.0, 94.0),
                factors_considered=['distance', 'fuel_costs', 'demand', 'seasonality']
            )
            
            return JsonResponse({
                'success': True,
                'prediction': {
                    'id': prediction.id,
                    'predicted_price': float(prediction.predicted_price),
                    'confidence_low': float(prediction.confidence_interval_low),
                    'confidence_high': float(prediction.confidence_interval_high),
                    'model_accuracy': prediction.model_accuracy
                }
            })
            
        except Exception as e:
            logger.error(f"Price prediction error: {e}")
            return JsonResponse({'error': 'Price prediction failed'}, status=500)

@method_decorator(login_required, name='dispatch')
class RouteOptimizationView(View):
    """Route optimization API"""
    
    def post(self, request):
        """Optimize route"""
        try:
            data = json.loads(request.body)
            origin = data.get('origin')
            destination = data.get('destination')
            cargo_details = data.get('cargo_details', {})
            criteria = data.get('optimization_criteria', ['cost', 'time'])
            
            # AI route optimization (simplified)
            import random
            cost_savings = random.uniform(500, 2000)
            time_savings = timedelta(hours=random.randint(2, 12))
            
            optimization = RouteOptimization.objects.create(
                user=request.user,
                origin=origin,
                destination=destination,
                cargo_details=cargo_details,
                optimization_criteria=criteria,
                recommended_route={
                    'mode': 'multimodal',
                    'segments': ['truck', 'rail', 'truck'],
                    'total_distance': random.randint(800, 2500),
                    'estimated_time': '2-3 days'
                },
                cost_savings=cost_savings,
                time_savings=time_savings,
                environmental_impact={
                    'co2_reduction': f"{random.randint(15, 40)}%",
                    'fuel_savings': f"{random.randint(200, 500)} liters"
                }
            )
            
            return JsonResponse({
                'success': True,
                'optimization': {
                    'id': optimization.id,
                    'cost_savings': float(optimization.cost_savings),
                    'time_savings': str(optimization.time_savings),
                    'recommended_route': optimization.recommended_route,
                    'environmental_impact': optimization.environmental_impact
                }
            })
            
        except Exception as e:
            logger.error(f"Route optimization error: {e}")
            return JsonResponse({'error': 'Route optimization failed'}, status=500)