from django.urls import path
from . import views

app_name = 'ai_analytics'

urlpatterns = [
    path('dashboard/', views.AIAnalyticsView.as_view(), name='dashboard'),
    path('api/comprehensive-analytics/', views.ComprehensiveAnalyticsView.as_view(), name='comprehensive_analytics'),
    path('api/demand-forecast/', views.DemandForecastView.as_view(), name='demand_forecast'),
    path('api/price-prediction/', views.PricePredictionView.as_view(), name='price_prediction'),
    path('api/route-optimization/', views.RouteOptimizationView.as_view(), name='route_optimization'),
    
    # Final completion: Additional AI analytics endpoints
    path('api/risk-assessment/', views.RiskAssessmentView.as_view(), name='risk_assessment'),
    path('api/predictive-models/', views.PredictiveModelsView.as_view(), name='predictive_models'),
    path('api/market-analysis/', views.MarketAnalysisView.as_view(), name='market_analysis'),
]