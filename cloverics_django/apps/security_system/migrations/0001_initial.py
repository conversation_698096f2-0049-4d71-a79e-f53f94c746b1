# Generated by Django 5.2.1 on 2025-08-07 12:45

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ThreatDetection',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('threat_type', models.CharField(choices=[('sql_injection', 'SQL Injection'), ('xss_attack', 'XSS Attack'), ('brute_force', 'Brute Force'), ('ddos_attempt', 'DDoS Attempt'), ('malware_upload', 'Malware Upload'), ('suspicious_pattern', 'Suspicious Pattern')], max_length=50)),
                ('confidence_score', models.FloatField(help_text='Confidence score 0-1')),
                ('source_ip', models.GenericIPAddressField()),
                ('target_endpoint', models.CharField(max_length=500)),
                ('request_data', models.JSONField(default=dict)),
                ('detection_timestamp', models.DateTimeField(default=django.utils.timezone.now)),
                ('blocked', models.BooleanField(default=False)),
                ('false_positive', models.BooleanField(default=False)),
            ],
            options={
                'ordering': ['-detection_timestamp'],
            },
        ),
        migrations.CreateModel(
            name='RateLimit',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('identifier', models.CharField(help_text='IP address or user ID', max_length=100)),
                ('endpoint', models.CharField(max_length=200)),
                ('request_count', models.IntegerField(default=1)),
                ('window_start', models.DateTimeField(default=django.utils.timezone.now)),
                ('blocked_until', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'unique_together': {('identifier', 'endpoint', 'window_start')},
            },
        ),
        migrations.CreateModel(
            name='SecurityEvent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('event_type', models.CharField(choices=[('login_attempt', 'Login Attempt'), ('failed_login', 'Failed Login'), ('suspicious_activity', 'Suspicious Activity'), ('rate_limit_exceeded', 'Rate Limit Exceeded'), ('unauthorized_access', 'Unauthorized Access'), ('data_breach_attempt', 'Data Breach Attempt')], max_length=50)),
                ('severity', models.CharField(choices=[('low', 'Low'), ('medium', 'Medium'), ('high', 'High'), ('critical', 'Critical')], default='medium', max_length=20)),
                ('ip_address', models.GenericIPAddressField()),
                ('user_agent', models.TextField(blank=True)),
                ('description', models.TextField()),
                ('additional_data', models.JSONField(blank=True, default=dict)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('resolved', models.BooleanField(default=False)),
                ('resolved_at', models.DateTimeField(blank=True, null=True)),
                ('resolved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='resolved_security_events', to=settings.AUTH_USER_MODEL)),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['event_type', 'created_at'], name='security_sy_event_t_ae4ced_idx'), models.Index(fields=['severity', 'resolved'], name='security_sy_severit_79e846_idx'), models.Index(fields=['ip_address'], name='security_sy_ip_addr_ecb005_idx')],
            },
        ),
        migrations.CreateModel(
            name='SessionAudit',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('session_key', models.CharField(max_length=40)),
                ('ip_address', models.GenericIPAddressField()),
                ('user_agent', models.TextField()),
                ('login_time', models.DateTimeField(default=django.utils.timezone.now)),
                ('logout_time', models.DateTimeField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('last_activity', models.DateTimeField(default=django.utils.timezone.now)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-login_time'],
                'indexes': [models.Index(fields=['user', 'is_active'], name='security_sy_user_id_64d1b7_idx'), models.Index(fields=['session_key'], name='security_sy_session_6330c6_idx')],
            },
        ),
    ]
