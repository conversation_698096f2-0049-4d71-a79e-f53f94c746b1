from django.urls import path
from . import views

app_name = 'security_system'

urlpatterns = [
    path('monitoring/', views.SecurityMonitoringView.as_view(), name='monitoring'),
    path('api/test/', views.SecurityTestView.as_view(), name='security_test'),
    path('api/health/', views.SystemHealthView.as_view(), name='system_health'),
    path('api/optimization-report/', views.OptimizationReportView.as_view(), name='optimization_report'),
    
    # Final completion: Additional security endpoints
    path('dashboard/', views.security_dashboard, name='dashboard'),
    path('alerts/', views.security_alerts, name='security_alerts'),
    path('logs/', views.security_logs, name='security_logs'),
    path('threats/', views.threat_dashboard, name='threat_dashboard'),
]