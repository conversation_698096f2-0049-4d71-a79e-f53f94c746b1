# Security Middleware System extracted from FastAPI
from django.utils.deprecation import MiddlewareMixin
from django.http import HttpResponse
from django.utils import timezone
from typing import Callable
import logging

from .models import SecurityEvent, SessionAudit, RateLimit
from .views import SecurityEventManager

logger = logging.getLogger(__name__)

class SecurityHeadersMiddleware(MiddlewareMixin):
    """Add comprehensive security headers to all responses"""
    
    def process_response(self, request, response):
        """Add security headers extracted from FastAPI"""
        
        # Security headers from FastAPI implementation
        security_headers = {
            'X-Content-Type-Options': 'nosniff',
            'X-Frame-Options': 'DENY',
            'X-XSS-Protection': '1; mode=block',
            'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
            'Referrer-Policy': 'strict-origin-when-cross-origin',
            'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' https:; connect-src 'self' https:; frame-ancestors 'none';",
            'Permissions-Policy': 'camera=(), microphone=(), geolocation=()',
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0'
        }
        
        for header_name, header_value in security_headers.items():
            response[header_name] = header_value
        
        return response


class RateLimitMiddleware(MiddlewareMixin):
    """Rate limiting middleware for API protection"""
    
    def __init__(self, get_response):
        self.get_response = get_response
        super().__init__(get_response)
    
    def __call__(self, request):
        # Check rate limit before processing request
        client_ip = self.get_client_ip(request)
        endpoint = request.path
        
        # Check if rate limited
        if self.is_rate_limited(client_ip, endpoint):
            # Log security event
            SecurityEventManager.log_security_event(
                'RATE_LIMIT_EXCEEDED',
                client_ip,
                severity='MEDIUM',
                description=f'Rate limit exceeded for endpoint: {endpoint}',
                event_data={'endpoint': endpoint, 'user_agent': request.META.get('HTTP_USER_AGENT', '')}
            )
            
            response = HttpResponse('Rate limit exceeded', status=429)
            response['Retry-After'] = '60'
            return response
        
        # Track request
        self.track_request(client_ip, endpoint)
        
        response = self.get_response(request)
        return response
    
    def get_client_ip(self, request):
        """Get client IP address"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip
    
    def is_rate_limited(self, client_ip: str, endpoint: str) -> bool:
        """Check if client is rate limited"""
        try:
            rate_limit, created = RateLimit.objects.get_or_create(
                ip_address=client_ip,
                endpoint=endpoint,
                defaults={'request_count': 0, 'window_start': timezone.now()}
            )
            
            # Check if window has expired (5 minutes)
            window_duration = timezone.now() - rate_limit.window_start
            if window_duration.total_seconds() > 300:  # 5 minutes
                rate_limit.request_count = 0
                rate_limit.window_start = timezone.now()
                rate_limit.save()
            
            # Rate limit thresholds by endpoint type
            limits = {
                '/api/': 100,  # API endpoints
                '/login': 10,  # Login attempts
                '/register': 5,  # Registration attempts
                'default': 200  # Default limit
            }
            
            # Find applicable limit
            limit = limits.get('default')
            for pattern, pattern_limit in limits.items():
                if pattern in endpoint:
                    limit = pattern_limit
                    break
            
            return rate_limit.request_count >= limit
            
        except Exception as e:
            logger.error(f"Rate limit check failed: {e}")
            return False
    
    def track_request(self, client_ip: str, endpoint: str):
        """Track request for rate limiting"""
        try:
            rate_limit, created = RateLimit.objects.get_or_create(
                ip_address=client_ip,
                endpoint=endpoint,
                defaults={'request_count': 0, 'window_start': timezone.now()}
            )
            
            rate_limit.request_count += 1
            rate_limit.last_request = timezone.now()
            rate_limit.save()
            
        except Exception as e:
            logger.error(f"Request tracking failed: {e}")


class SecurityEventMiddleware(MiddlewareMixin):
    """Middleware to detect and log security events"""
    
    def process_request(self, request):
        """Analyze request for security threats"""
        client_ip = self.get_client_ip(request)
        user_agent = request.META.get('HTTP_USER_AGENT', '')
        
        # Check for suspicious patterns
        suspicious_patterns = [
            # SQL Injection attempts
            'union select', 'drop table', 'delete from', 'insert into',
            # XSS attempts
            '<script>', 'javascript:', 'onerror=', 'onload=',
            # Path traversal
            '../', '..\\', '/etc/passwd', '/etc/shadow',
            # Command injection
            '; cat ', '| cat ', '&& cat ', '|| cat '
        ]
        
        request_data = str(request.GET) + str(request.POST) + request.path
        request_data_lower = request_data.lower()
        
        for pattern in suspicious_patterns:
            if pattern in request_data_lower:
                SecurityEventManager.log_security_event(
                    'MALICIOUS_REQUEST',
                    client_ip,
                    severity='HIGH',
                    description=f'Suspicious pattern detected: {pattern}',
                    event_data={
                        'pattern': pattern,
                        'endpoint': request.path,
                        'user_agent': user_agent,
                        'method': request.method
                    }
                )
                break
        
        # Check for bot/crawler activity
        bot_patterns = ['bot', 'crawler', 'spider', 'scraper', 'curl', 'wget']
        if any(pattern in user_agent.lower() for pattern in bot_patterns):
            SecurityEventManager.log_security_event(
                'BOT_ACTIVITY',
                client_ip,
                severity='LOW',
                description='Bot/crawler activity detected',
                event_data={'user_agent': user_agent, 'endpoint': request.path}
            )
        
        return None
    
    def get_client_ip(self, request):
        """Get client IP address"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class SessionSecurityMiddleware(MiddlewareMixin):
    """Enhanced session security middleware"""
    
    def process_request(self, request):
        """Process request for session security"""
        if hasattr(request, 'user') and request.user.is_authenticated:
            client_ip = self.get_client_ip(request)
            user_agent = request.META.get('HTTP_USER_AGENT', '')
            
            # Create session audit trail
            from .authentication import SessionManager
            SessionManager.create_session_audit(
                user=request.user,
                session_key=request.session.session_key or 'no-session',
                event_type='REQUEST',
                ip_address=client_ip,
                user_agent=user_agent
            )
        
        return None
    
    def get_client_ip(self, request):
        """Get client IP address"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class InputSanitizationMiddleware(MiddlewareMixin):
    """Sanitize user inputs to prevent XSS and injection attacks"""
    
    def process_request(self, request):
        """Sanitize request data"""
        # Sanitize GET parameters
        if request.GET:
            sanitized_get = {}
            for key, value in request.GET.items():
                sanitized_get[key] = self.sanitize_input(value)
            request.GET = sanitized_get
        
        # Sanitize POST parameters
        if request.POST:
            sanitized_post = {}
            for key, value in request.POST.items():
                sanitized_post[key] = self.sanitize_input(value)
            request.POST = sanitized_post
        
        return None
    
    def sanitize_input(self, value: str) -> str:
        """Sanitize input value"""
        if not isinstance(value, str):
            return value
        
        # Remove potentially dangerous characters
        dangerous_chars = ['<', '>', '"', "'", '&', ';', '(', ')', '{', '}']
        sanitized = value
        
        for char in dangerous_chars:
            sanitized = sanitized.replace(char, '')
        
        return sanitized[:1000]  # Limit length