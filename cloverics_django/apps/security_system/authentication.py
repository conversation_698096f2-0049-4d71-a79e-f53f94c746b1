# JWT Authentication System extracted from FastAPI
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import datetime, timedelta
from typing import Optional, Dict
from jose import JWTError, jwt
from passlib.context import CryptContext
import hashlib
import logging

from .models import SecurityEvent, SessionAudit, RateLimit

User = get_user_model()
logger = logging.getLogger(__name__)

# Security configuration
SECRET_KEY = "cloverics-secret-key-change-in-production"
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

class AuthenticationService:
    """Django-based authentication service extracted from FastAPI"""
    
    @staticmethod
    def verify_password(plain_password: str, hashed_password: str) -> bool:
        """Verify password against hash"""
        return pwd_context.verify(plain_password, hashed_password)
    
    @staticmethod
    def get_password_hash(password: str) -> str:
        """Generate password hash"""
        return pwd_context.hash(password)
    
    @staticmethod
    def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
        """Create JWT access token"""
        to_encode = data.copy()
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        to_encode.update({"exp": expire})
        encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
        return encoded_jwt
    
    @staticmethod
    def verify_token(token: str) -> Optional[dict]:
        """Verify JWT token"""
        try:
            payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
            return payload
        except JWTError:
            return None
    
    @staticmethod
    def validate_password_strength(password: str) -> bool:
        """Validate password strength requirements"""
        return len(password) >= 8
    
    @staticmethod
    def validate_email(email: str) -> bool:
        """Validate email format"""
        return "@" in email and "." in email


class DemoAccountManager:
    """Manage demo accounts with fallback authentication"""
    
    # Demo account configuration
    DEMO_ACCOUNTS = {
        90: {"email": "<EMAIL>", "user_type": "CUSTOMER"},
        89: {"email": "<EMAIL>", "user_type": "LOGISTICS_PROVIDER"},
        4: {"email": "<EMAIL>", "user_type": "INSURANCE_PROVIDER"},
        83: {"email": "<EMAIL>", "user_type": "INDEPENDENT_DRIVER"}
    }
    
    @staticmethod
    def get_demo_user(user_id: int, email: str = None, user_type: str = None):
        """Create MockUser for demo accounts"""
        
        class MockUser:
            def __init__(self, user_id, user_type, email):
                self.id = user_id
                self.user_type = user_type
                self.email = email
                self.first_name = email.split('@')[0].title()
                self.last_name = "Demo"
                self.company_name = "Demo Company"
                self.is_active = True
        
        # Get demo account info
        if user_id in DemoAccountManager.DEMO_ACCOUNTS:
            demo_info = DemoAccountManager.DEMO_ACCOUNTS[user_id]
            return MockUser(user_id, demo_info["user_type"], demo_info["email"])
        
        # Handle customs accounts with generated IDs
        if user_type == "CUSTOMS_AGENT" and user_id and user_id >= 1000:
            customs_email = DemoAccountManager._find_customs_email_by_id(user_id)
            if customs_email:
                return MockUser(user_id, "CUSTOMS_AGENT", customs_email)
        
        # Fallback for provided data
        if email and user_type:
            return MockUser(user_id, user_type, email)
        
        return None
    
    @staticmethod
    def _find_customs_email_by_id(user_id: int) -> Optional[str]:
        """Find customs email by user ID hash"""
        countries = [
            'albania', 'afghanistan', 'algeria', 'andorra', 'angola', 'antiguaandbarbuda',
            'argentina', 'armenia', 'australia', 'austria', 'azerbaijan', 'bahamas',
            'bahrain', 'bangladesh', 'barbados', 'belarus', 'belgium', 'belize',
            'benin', 'bhutan', 'bolivia', 'bosniaandherzegovina', 'botswana', 'brazil',
            'brunei', 'bulgaria', 'burkinafaso', 'burundi', 'caboverde', 'cambodia',
            'cameroon', 'canada', 'centralafricanrepublic', 'chad', 'chile', 'china',
            'colombia', 'comoros', 'congo', 'costarica', 'croatia', 'cuba', 'cyprus',
            'czechrepublic', 'democraticrepublicofthecongo', 'denmark', 'djibouti',
            'dominica', 'dominicanrepublic', 'easttimor', 'ecuador', 'egypt',
            'elsalvador', 'equatorialguinea', 'eritrea', 'estonia', 'eswatini',
            'ethiopia', 'fiji', 'finland', 'france', 'gabon', 'gambia', 'georgia',
            'germany', 'ghana', 'greece', 'grenada', 'guatemala', 'guinea',
            'guineabissau', 'guyana', 'haiti', 'honduras', 'hungary', 'iceland',
            'india', 'indonesia', 'iran', 'iraq', 'ireland', 'israel', 'italy',
            'ivorycoast', 'jamaica', 'japan', 'jordan', 'kazakhstan', 'kenya',
            'kiribati', 'kuwait', 'kyrgyzstan', 'laos', 'latvia', 'lebanon',
            'lesotho', 'liberia', 'libya', 'liechtenstein', 'lithuania', 'luxembourg',
            'madagascar', 'malawi', 'malaysia', 'maldives', 'mali', 'malta',
            'marshallislands', 'mauritania', 'mauritius', 'mexico', 'micronesia',
            'moldova', 'monaco', 'mongolia', 'montenegro', 'morocco', 'mozambique',
            'myanmar', 'namibia', 'nauru', 'nepal', 'netherlands', 'newzealand',
            'nicaragua', 'niger', 'nigeria', 'northkorea', 'northmacedonia',
            'norway', 'oman', 'pakistan', 'palau', 'palestine', 'panama',
            'papuanewguinea', 'paraguay', 'peru', 'philippines', 'poland',
            'portugal', 'qatar', 'romania', 'russia', 'rwanda', 'saintkittsandnevis',
            'saintlucia', 'saintvincentandthegrenadines', 'samoa', 'sanmarino',
            'saotomeandprincipe', 'saudiarabia', 'senegal', 'serbia', 'seychelles',
            'sierraleone', 'singapore', 'slovakia', 'slovenia', 'solomonislands',
            'somalia', 'southafrica', 'southkorea', 'southsudan', 'spain',
            'srilanka', 'sudan', 'suriname', 'sweden', 'switzerland', 'syria',
            'taiwan', 'tajikistan', 'tanzania', 'thailand', 'togo', 'tonga',
            'trinidadandtobago', 'tunisia', 'turkey', 'turkmenistan', 'tuvalu',
            'uganda', 'ukraine', 'unitedarabemirates', 'unitedkingdom', 'unitedstates',
            'uruguay', 'uzbekistan', 'vanuatu', 'vaticancity', 'venezuela',
            'vietnam', 'yemen', 'zambia', 'zimbabwe'
        ]
        
        for country in countries:
            test_email = f"customsof{country}@demo.com"
            test_id = 1000 + (int(hashlib.md5(test_email.encode()).hexdigest(), 16) % 900)
            if test_id == user_id:
                return test_email
        
        return None


class SessionManager:
    """Session and security management"""
    
    @staticmethod
    def create_session_audit(user, session_key: str, event_type: str, ip_address: str, user_agent: str = ''):
        """Create session audit record"""
        from .views import SecurityEventManager
        
        # Check for suspicious activity
        is_suspicious = SessionManager._is_suspicious_session(user, ip_address, user_agent)
        risk_score = SessionManager._calculate_session_risk(user, ip_address)
        
        # Create session audit
        audit = SessionAudit.objects.create(
            session_key=session_key,
            user=user,
            event_type=event_type,
            ip_address=ip_address,
            user_agent=user_agent[:500],
            is_suspicious=is_suspicious,
            risk_score=risk_score,
            device_fingerprint=SessionManager._generate_device_fingerprint(user_agent),
            browser_info=SessionManager._extract_browser_info(user_agent)
        )
        
        # Log security event if suspicious
        if is_suspicious:
            SecurityEventManager.log_security_event(
                'SUSPICIOUS_ACTIVITY',
                ip_address,
                user=user,
                severity='HIGH',
                description=f'Suspicious session activity detected: {event_type}',
                event_data={'session_audit_id': str(audit.audit_id)}
            )
        
        return audit
    
    @staticmethod
    def _is_suspicious_session(user, ip_address: str, user_agent: str) -> bool:
        """Check for suspicious session patterns"""
        # Check for concurrent sessions from different IPs
        recent_sessions = SessionAudit.objects.filter(
            user=user,
            created_at__gte=timezone.now() - timedelta(hours=1)
        ).values_list('ip_address', flat=True).distinct()
        
        if len(recent_sessions) > 3:
            return True
        
        # Check for rapid login attempts
        recent_logins = SessionAudit.objects.filter(
            user=user,
            event_type='LOGIN',
            created_at__gte=timezone.now() - timedelta(minutes=10)
        ).count()
        
        return recent_logins > 5
    
    @staticmethod
    def _calculate_session_risk(user, ip_address: str) -> float:
        """Calculate risk score for session"""
        risk_score = 0.0
        
        # Check IP reputation (recent security events from this IP)
        recent_threats = SecurityEvent.objects.filter(
            ip_address=ip_address,
            severity__in=['HIGH', 'CRITICAL'],
            created_at__gte=timezone.now() - timedelta(days=7)
        ).count()
        
        if recent_threats > 5:
            risk_score += 30.0
        elif recent_threats > 2:
            risk_score += 15.0
        
        # Check for unusual login times
        current_hour = timezone.now().hour
        if current_hour < 6 or current_hour > 23:  # Late night/early morning
            risk_score += 10.0
        
        # Check user behavior patterns
        normal_ips = SessionAudit.objects.filter(
            user=user,
            created_at__gte=timezone.now() - timedelta(days=30)
        ).values_list('ip_address', flat=True).distinct()
        
        if ip_address not in normal_ips and len(normal_ips) > 0:
            risk_score += 20.0
        
        return min(risk_score, 100.0)
    
    @staticmethod
    def _generate_device_fingerprint(user_agent: str) -> str:
        """Generate device fingerprint"""
        fingerprint_data = f"{user_agent}"
        return hashlib.sha256(fingerprint_data.encode()).hexdigest()[:32]
    
    @staticmethod
    def _extract_browser_info(user_agent: str) -> Dict:
        """Extract browser information from user agent"""
        # Simple browser detection
        browser_info = {
            'user_agent': user_agent,
            'browser': 'Unknown',
            'version': 'Unknown',
            'os': 'Unknown',
            'device': 'Unknown'
        }
        
        if 'Chrome' in user_agent:
            browser_info['browser'] = 'Chrome'
        elif 'Firefox' in user_agent:
            browser_info['browser'] = 'Firefox'
        elif 'Safari' in user_agent:
            browser_info['browser'] = 'Safari'
        elif 'Edge' in user_agent:
            browser_info['browser'] = 'Edge'
        
        if 'Windows' in user_agent:
            browser_info['os'] = 'Windows'
        elif 'Mac' in user_agent:
            browser_info['os'] = 'macOS'
        elif 'Linux' in user_agent:
            browser_info['os'] = 'Linux'
        elif 'Android' in user_agent:
            browser_info['os'] = 'Android'
        elif 'iOS' in user_agent:
            browser_info['os'] = 'iOS'
        
        return browser_info


class InputValidator:
    """Input validation and sanitization"""
    
    @staticmethod
    def validate_email(email: str) -> bool:
        """Validate email format"""
        return "@" in email and "." in email
    
    @staticmethod
    def sanitize_input(input_str: str, max_length: int) -> str:
        """Sanitize input string"""
        if not input_str:
            return ""
        
        # Remove potentially dangerous characters
        dangerous_chars = ['<', '>', '"', "'", '&', ';', '(', ')', '{', '}']
        sanitized = str(input_str)
        
        for char in dangerous_chars:
            sanitized = sanitized.replace(char, '')
        
        return sanitized[:max_length]
    
    @staticmethod
    def validate_password_strength(password: str) -> Dict:
        """Validate password strength with detailed feedback"""
        errors = []
        
        if len(password) < 8:
            errors.append("Password must be at least 8 characters long")
        
        if not any(c.isupper() for c in password):
            errors.append("Password must contain at least one uppercase letter")
        
        if not any(c.islower() for c in password):
            errors.append("Password must contain at least one lowercase letter")
        
        if not any(c.isdigit() for c in password):
            errors.append("Password must contain at least one number")
        
        special_chars = "!@#$%^&*()_+-=[]{}|;:,.<>?"
        if not any(c in special_chars for c in password):
            errors.append("Password must contain at least one special character")
        
        return {
            'is_valid': len(errors) == 0,
            'errors': errors,
            'strength_score': max(0, 100 - len(errors) * 20)
        }