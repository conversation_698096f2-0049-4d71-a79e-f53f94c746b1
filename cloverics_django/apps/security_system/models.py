from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone

User = get_user_model()

class SecurityEvent(models.Model):
    """Security events and threat detection logs"""
    EVENT_TYPES = [
        ('login_attempt', 'Login Attempt'),
        ('failed_login', 'Failed Login'),
        ('suspicious_activity', 'Suspicious Activity'),
        ('rate_limit_exceeded', 'Rate Limit Exceeded'),
        ('unauthorized_access', 'Unauthorized Access'),
        ('data_breach_attempt', 'Data Breach Attempt'),
    ]
    
    SEVERITY_LEVELS = [
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('critical', 'Critical'),
    ]
    
    event_type = models.CharField(max_length=50, choices=EVENT_TYPES)
    severity = models.CharField(max_length=20, choices=SEVERITY_LEVELS, default='medium')
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    ip_address = models.GenericIPAddressField()
    user_agent = models.TextField(blank=True)
    description = models.TextField()
    additional_data = models.JSONField(default=dict, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    resolved = models.BooleanField(default=False)
    resolved_at = models.DateTimeField(null=True, blank=True)
    resolved_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='resolved_security_events')
    
    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['event_type', 'created_at']),
            models.Index(fields=['severity', 'resolved']),
            models.Index(fields=['ip_address']),
        ]

class ThreatDetection(models.Model):
    """Automated threat detection and analysis"""
    THREAT_TYPES = [
        ('sql_injection', 'SQL Injection'),
        ('xss_attack', 'XSS Attack'),
        ('brute_force', 'Brute Force'),
        ('ddos_attempt', 'DDoS Attempt'),
        ('malware_upload', 'Malware Upload'),
        ('suspicious_pattern', 'Suspicious Pattern'),
    ]
    
    threat_type = models.CharField(max_length=50, choices=THREAT_TYPES)
    confidence_score = models.FloatField(help_text="Confidence score 0-1")
    source_ip = models.GenericIPAddressField()
    target_endpoint = models.CharField(max_length=500)
    request_data = models.JSONField(default=dict)
    detection_timestamp = models.DateTimeField(default=timezone.now)
    blocked = models.BooleanField(default=False)
    false_positive = models.BooleanField(default=False)
    
    class Meta:
        ordering = ['-detection_timestamp']

class RateLimit(models.Model):
    """Rate limiting tracking and enforcement"""
    identifier = models.CharField(max_length=100, help_text="IP address or user ID")
    endpoint = models.CharField(max_length=200)
    request_count = models.IntegerField(default=1)
    window_start = models.DateTimeField(default=timezone.now)
    blocked_until = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        unique_together = ['identifier', 'endpoint', 'window_start']

class SessionAudit(models.Model):
    """User session audit trail"""
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    session_key = models.CharField(max_length=40)
    ip_address = models.GenericIPAddressField()
    user_agent = models.TextField()
    login_time = models.DateTimeField(default=timezone.now)
    logout_time = models.DateTimeField(null=True, blank=True)
    is_active = models.BooleanField(default=True)
    last_activity = models.DateTimeField(default=timezone.now)
    
    class Meta:
        ordering = ['-login_time']
        indexes = [
            models.Index(fields=['user', 'is_active']),
            models.Index(fields=['session_key']),
        ]