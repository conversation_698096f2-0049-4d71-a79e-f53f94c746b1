from django.contrib import admin
from .models import SecurityEvent, ThreatDetection, RateLimit, SessionAudit

@admin.register(SecurityEvent)
class SecurityEventAdmin(admin.ModelAdmin):
    list_display = ['event_type', 'severity', 'user', 'ip_address', 'created_at', 'resolved']
    list_filter = ['event_type', 'severity', 'resolved', 'created_at']
    search_fields = ['description', 'ip_address', 'user__username']
    readonly_fields = ['created_at']
    ordering = ['-created_at']

@admin.register(ThreatDetection)
class ThreatDetectionAdmin(admin.ModelAdmin):
    list_display = ['threat_type', 'confidence_score', 'source_ip', 'detection_timestamp', 'blocked']
    list_filter = ['threat_type', 'blocked', 'false_positive', 'detection_timestamp']
    search_fields = ['source_ip', 'target_endpoint']
    readonly_fields = ['detection_timestamp']

@admin.register(RateLimit)
class RateLimitAdmin(admin.ModelAdmin):
    list_display = ['identifier', 'endpoint', 'request_count', 'window_start', 'blocked_until']
    list_filter = ['endpoint', 'window_start']
    search_fields = ['identifier', 'endpoint']

@admin.register(SessionAudit)
class SessionAuditAdmin(admin.ModelAdmin):
    list_display = ['user', 'ip_address', 'login_time', 'logout_time', 'is_active']
    list_filter = ['is_active', 'login_time']
    search_fields = ['user__username', 'ip_address']
    readonly_fields = ['login_time', 'session_key']