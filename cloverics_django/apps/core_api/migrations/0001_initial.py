# Generated by Django 5.2.1 on 2025-08-07 12:45

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='SystemConfiguration',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('key', models.CharField(db_index=True, max_length=100, unique=True)),
                ('value', models.TextField()),
                ('description', models.TextField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'core_api_system_config',
                'ordering': ['key'],
            },
        ),
        migrations.CreateModel(
            name='UserNotificationPreference',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('email_notifications', models.BooleanField(default=True)),
                ('sms_notifications', models.BooleanField(default=False)),
                ('push_notifications', models.BooleanField(default=True)),
                ('marketing_emails', models.BooleanField(default=False)),
                ('shipment_updates', models.BooleanField(default=True)),
                ('quote_notifications', models.BooleanField(default=True)),
                ('payment_alerts', models.BooleanField(default=True)),
                ('security_alerts', models.BooleanField(default=True)),
                ('notification_frequency', models.CharField(choices=[('immediate', 'Immediate'), ('hourly', 'Hourly'), ('daily', 'Daily Digest'), ('weekly', 'Weekly Summary')], default='immediate', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='notification_preferences', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'core_api_notification_preferences',
            },
        ),
        migrations.CreateModel(
            name='APIEndpointAccess',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('endpoint', models.CharField(db_index=True, max_length=255)),
                ('method', models.CharField(choices=[('GET', 'GET'), ('POST', 'POST'), ('PUT', 'PUT'), ('DELETE', 'DELETE'), ('PATCH', 'PATCH')], max_length=10)),
                ('accessed_at', models.DateTimeField(auto_now_add=True)),
                ('response_status', models.IntegerField()),
                ('response_time_ms', models.IntegerField(blank=True, null=True)),
                ('ip_address', models.GenericIPAddressField()),
                ('user_agent', models.TextField(blank=True, null=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='api_accesses', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'core_api_endpoint_access',
                'ordering': ['-accessed_at'],
                'indexes': [models.Index(fields=['user', 'accessed_at'], name='core_api_en_user_id_02e1b9_idx'), models.Index(fields=['endpoint', 'accessed_at'], name='core_api_en_endpoin_aacba9_idx'), models.Index(fields=['response_status'], name='core_api_en_respons_7f273e_idx')],
            },
        ),
        migrations.CreateModel(
            name='AuthSession',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('session_id', models.UUIDField(default=uuid.uuid4, unique=True)),
                ('jwt_token_hash', models.CharField(db_index=True, max_length=255)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('expires_at', models.DateTimeField()),
                ('is_active', models.BooleanField(default=True)),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('user_agent', models.TextField(blank=True, null=True)),
                ('last_activity', models.DateTimeField(auto_now=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='auth_sessions', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'core_api_auth_sessions',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['user', 'is_active'], name='core_api_au_user_id_fe509e_idx'), models.Index(fields=['expires_at'], name='core_api_au_expires_2eb0ba_idx'), models.Index(fields=['jwt_token_hash'], name='core_api_au_jwt_tok_35561e_idx')],
            },
        ),
        migrations.CreateModel(
            name='LoginAttempt',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('email', models.EmailField(db_index=True, max_length=254)),
                ('ip_address', models.GenericIPAddressField()),
                ('user_agent', models.TextField(blank=True, null=True)),
                ('success', models.BooleanField(default=False)),
                ('attempted_at', models.DateTimeField(auto_now_add=True)),
                ('failure_reason', models.CharField(blank=True, max_length=100, null=True)),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'core_api_login_attempts',
                'ordering': ['-attempted_at'],
                'indexes': [models.Index(fields=['email', 'attempted_at'], name='core_api_lo_email_6d5f9e_idx'), models.Index(fields=['ip_address', 'attempted_at'], name='core_api_lo_ip_addr_d57266_idx'), models.Index(fields=['success', 'attempted_at'], name='core_api_lo_success_a734fa_idx')],
            },
        ),
        migrations.CreateModel(
            name='TokenBlacklist',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('token_hash', models.CharField(db_index=True, max_length=255, unique=True)),
                ('blacklisted_at', models.DateTimeField(auto_now_add=True)),
                ('reason', models.CharField(choices=[('logout', 'User Logout'), ('security', 'Security Breach'), ('expired', 'Token Expired'), ('admin', 'Admin Action')], default='logout', max_length=100)),
                ('expires_at', models.DateTimeField()),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='blacklisted_tokens', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'core_api_token_blacklist',
                'ordering': ['-blacklisted_at'],
                'indexes': [models.Index(fields=['token_hash'], name='core_api_to_token_h_f022e5_idx'), models.Index(fields=['expires_at'], name='core_api_to_expires_b68575_idx')],
            },
        ),
    ]
