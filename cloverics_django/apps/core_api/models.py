"""
Core API Models - Django Implementation
Phase 7: Authentication and Core API System
Extracted from FastAPI lines 533-798, 1286-1583
"""

from django.db import models
from django.contrib.auth.models import AbstractUser
from django.utils import timezone
from django.core.validators import MinLengthValidator
import uuid
import hashlib

class AuthSession(models.Model):
    """
    Tracks user authentication sessions and JWT tokens
    Extracted from FastAPI AuthService functionality
    """
    user = models.ForeignKey('authentication.User', on_delete=models.CASCADE, related_name='auth_sessions')
    session_id = models.UUIDField(default=uuid.uuid4, unique=True)
    jwt_token_hash = models.CharField(max_length=255, db_index=True)
    created_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField()
    is_active = models.BooleanField(default=True)
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(null=True, blank=True)
    last_activity = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'core_api_auth_sessions'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'is_active']),
            models.Index(fields=['expires_at']),
            models.Index(fields=['jwt_token_hash']),
        ]
    
    def __str__(self):
        return f"Session {self.session_id} for {self.user.email}"
    
    def is_expired(self):
        return timezone.now() > self.expires_at
    
    def deactivate(self):
        self.is_active = False
        self.save()

class TokenBlacklist(models.Model):
    """
    Manages blacklisted JWT tokens for security
    Handles token revocation and security controls
    """
    token_hash = models.CharField(max_length=255, unique=True, db_index=True)
    user = models.ForeignKey('authentication.User', on_delete=models.CASCADE, related_name='blacklisted_tokens')
    blacklisted_at = models.DateTimeField(auto_now_add=True)
    reason = models.CharField(max_length=100, choices=[
        ('logout', 'User Logout'),
        ('security', 'Security Breach'),
        ('expired', 'Token Expired'),
        ('admin', 'Admin Action'),
    ], default='logout')
    expires_at = models.DateTimeField()
    
    class Meta:
        db_table = 'core_api_token_blacklist'
        ordering = ['-blacklisted_at']
        indexes = [
            models.Index(fields=['token_hash']),
            models.Index(fields=['expires_at']),
        ]
    
    def __str__(self):
        return f"Blacklisted token for {self.user.email}"
    
    @classmethod
    def is_blacklisted(cls, token_hash):
        return cls.objects.filter(
            token_hash=token_hash,
            expires_at__gt=timezone.now()
        ).exists()

class LoginAttempt(models.Model):
    """
    Tracks login attempts for security monitoring
    Implements rate limiting and brute force protection
    """
    email = models.EmailField(db_index=True)
    ip_address = models.GenericIPAddressField()
    user_agent = models.TextField(null=True, blank=True)
    success = models.BooleanField(default=False)
    attempted_at = models.DateTimeField(auto_now_add=True)
    failure_reason = models.CharField(max_length=100, null=True, blank=True)
    user = models.ForeignKey('authentication.User', on_delete=models.SET_NULL, null=True, blank=True)
    
    class Meta:
        db_table = 'core_api_login_attempts'
        ordering = ['-attempted_at']
        indexes = [
            models.Index(fields=['email', 'attempted_at']),
            models.Index(fields=['ip_address', 'attempted_at']),
            models.Index(fields=['success', 'attempted_at']),
        ]
    
    def __str__(self):
        status = "Success" if self.success else "Failed"
        return f"{status} login attempt for {self.email}"
    
    @classmethod
    def get_recent_failed_attempts(cls, email, minutes=15):
        """Get failed login attempts in last N minutes"""
        since = timezone.now() - timezone.timedelta(minutes=minutes)
        return cls.objects.filter(
            email=email,
            success=False,
            attempted_at__gte=since
        ).count()
    
    @classmethod
    def is_rate_limited(cls, email, max_attempts=5, minutes=15):
        """Check if email is rate limited"""
        failed_attempts = cls.get_recent_failed_attempts(email, minutes)
        return failed_attempts >= max_attempts

class APIEndpointAccess(models.Model):
    """
    Tracks API endpoint access for monitoring and analytics
    Extracted from FastAPI endpoint monitoring functionality
    """
    user = models.ForeignKey('authentication.User', on_delete=models.CASCADE, related_name='api_accesses')
    endpoint = models.CharField(max_length=255, db_index=True)
    method = models.CharField(max_length=10, choices=[
        ('GET', 'GET'),
        ('POST', 'POST'),
        ('PUT', 'PUT'),
        ('DELETE', 'DELETE'),
        ('PATCH', 'PATCH'),
    ])
    accessed_at = models.DateTimeField(auto_now_add=True)
    response_status = models.IntegerField()
    response_time_ms = models.IntegerField(null=True, blank=True)
    ip_address = models.GenericIPAddressField()
    user_agent = models.TextField(null=True, blank=True)
    
    class Meta:
        db_table = 'core_api_endpoint_access'
        ordering = ['-accessed_at']
        indexes = [
            models.Index(fields=['user', 'accessed_at']),
            models.Index(fields=['endpoint', 'accessed_at']),
            models.Index(fields=['response_status']),
        ]
    
    def __str__(self):
        return f"{self.method} {self.endpoint} by {self.user.email}"

class SystemConfiguration(models.Model):
    """
    Stores system-wide configuration settings
    Manages feature flags and system parameters
    """
    key = models.CharField(max_length=100, unique=True, db_index=True)
    value = models.TextField()
    description = models.TextField(null=True, blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    updated_by = models.ForeignKey('authentication.User', on_delete=models.SET_NULL, null=True, blank=True)
    
    class Meta:
        db_table = 'core_api_system_config'
        ordering = ['key']
    
    def __str__(self):
        return f"{self.key}: {self.value[:50]}"
    
    @classmethod
    def get_value(cls, key, default=None):
        """Get configuration value by key"""
        try:
            config = cls.objects.get(key=key, is_active=True)
            return config.value
        except cls.DoesNotExist:
            return default
    
    @classmethod
    def set_value(cls, key, value, description=None, user=None):
        """Set configuration value"""
        config, created = cls.objects.update_or_create(
            key=key,
            defaults={
                'value': value,
                'description': description,
                'updated_by': user,
                'is_active': True,
            }
        )
        return config

class UserNotificationPreference(models.Model):
    """
    Manages user notification preferences
    Extracted from notification system configuration
    """
    user = models.OneToOneField('authentication.User', on_delete=models.CASCADE, related_name='notification_preferences')
    email_notifications = models.BooleanField(default=True)
    sms_notifications = models.BooleanField(default=False)
    push_notifications = models.BooleanField(default=True)
    marketing_emails = models.BooleanField(default=False)
    shipment_updates = models.BooleanField(default=True)
    quote_notifications = models.BooleanField(default=True)
    payment_alerts = models.BooleanField(default=True)
    security_alerts = models.BooleanField(default=True)
    notification_frequency = models.CharField(
        max_length=20,
        choices=[
            ('immediate', 'Immediate'),
            ('hourly', 'Hourly'),
            ('daily', 'Daily Digest'),
            ('weekly', 'Weekly Summary'),
        ],
        default='immediate'
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'core_api_notification_preferences'
    
    def __str__(self):
        return f"Notification preferences for {self.user.email}"