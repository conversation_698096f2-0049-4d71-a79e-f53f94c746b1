"""
Core API Admin - Django Implementation
Phase 7: Authentication and Core API System
Admin interfaces for authentication and core API models
"""

from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils import timezone
from .models import (
    AuthSession, TokenBlacklist, LoginAttempt, 
    APIEndpointAccess, SystemConfiguration, UserNotificationPreference
)

@admin.register(AuthSession)
class AuthSessionAdmin(admin.ModelAdmin):
    list_display = ['user', 'session_id', 'ip_address', 'is_active', 'created_at', 'expires_at', 'status_display']
    list_filter = ['is_active', 'created_at', 'expires_at']
    search_fields = ['user__email', 'ip_address', 'session_id']
    readonly_fields = ['session_id', 'jwt_token_hash', 'created_at', 'expires_at']
    ordering = ['-created_at']
    
    def status_display(self, obj):
        if not obj.is_active:
            return format_html('<span style="color: red;">Inactive</span>')
        elif obj.is_expired():
            return format_html('<span style="color: orange;">Expired</span>')
        else:
            return format_html('<span style="color: green;">Active</span>')
    status_display.short_description = 'Status'
    
    actions = ['deactivate_sessions']
    
    def deactivate_sessions(self, request, queryset):
        queryset.update(is_active=False)
        self.message_user(request, f"Deactivated {queryset.count()} sessions.")
    deactivate_sessions.short_description = "Deactivate selected sessions"

@admin.register(TokenBlacklist)
class TokenBlacklistAdmin(admin.ModelAdmin):
    list_display = ['user', 'token_hash_short', 'reason', 'blacklisted_at', 'expires_at']
    list_filter = ['reason', 'blacklisted_at', 'expires_at']
    search_fields = ['user__email', 'token_hash']
    readonly_fields = ['token_hash', 'blacklisted_at']
    ordering = ['-blacklisted_at']
    
    def token_hash_short(self, obj):
        return f"{obj.token_hash[:10]}..." if obj.token_hash else ""
    token_hash_short.short_description = 'Token Hash'

@admin.register(LoginAttempt)
class LoginAttemptAdmin(admin.ModelAdmin):
    list_display = ['email', 'ip_address', 'success', 'attempted_at', 'failure_reason', 'success_display']
    list_filter = ['success', 'attempted_at', 'failure_reason']
    search_fields = ['email', 'ip_address', 'user__email']
    readonly_fields = ['attempted_at']
    ordering = ['-attempted_at']
    
    def success_display(self, obj):
        if obj.success:
            return format_html('<span style="color: green;">✓ Success</span>')
        else:
            return format_html('<span style="color: red;">✗ Failed</span>')
    success_display.short_description = 'Result'
    
    def has_add_permission(self, request):
        return False  # Login attempts are created automatically
    
    def has_change_permission(self, request, obj=None):
        return False  # Login attempts should not be modified

@admin.register(APIEndpointAccess)
class APIEndpointAccessAdmin(admin.ModelAdmin):
    list_display = ['user', 'endpoint', 'method', 'response_status', 'accessed_at', 'response_time_ms', 'status_color']
    list_filter = ['method', 'response_status', 'accessed_at']
    search_fields = ['user__email', 'endpoint', 'ip_address']
    readonly_fields = ['accessed_at']
    ordering = ['-accessed_at']
    
    def status_color(self, obj):
        if 200 <= obj.response_status < 300:
            color = 'green'
        elif 400 <= obj.response_status < 500:
            color = 'orange'
        else:
            color = 'red'
        return format_html(f'<span style="color: {color};">{obj.response_status}</span>')
    status_color.short_description = 'Status'
    
    def has_add_permission(self, request):
        return False  # API access logs are created automatically
    
    def has_change_permission(self, request, obj=None):
        return False  # API access logs should not be modified

@admin.register(SystemConfiguration)
class SystemConfigurationAdmin(admin.ModelAdmin):
    list_display = ['key', 'value_short', 'is_active', 'updated_at', 'updated_by']
    list_filter = ['is_active', 'updated_at']
    search_fields = ['key', 'value', 'description']
    readonly_fields = ['created_at', 'updated_at']
    ordering = ['key']
    
    def value_short(self, obj):
        return f"{obj.value[:50]}..." if len(obj.value) > 50 else obj.value
    value_short.short_description = 'Value'
    
    fieldsets = (
        (None, {
            'fields': ('key', 'value', 'description', 'is_active')
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at', 'updated_by'),
            'classes': ('collapse',)
        }),
    )

@admin.register(UserNotificationPreference)
class UserNotificationPreferenceAdmin(admin.ModelAdmin):
    list_display = [
        'user', 'email_notifications', 'sms_notifications', 
        'push_notifications', 'marketing_emails', 'notification_frequency', 'updated_at'
    ]
    list_filter = [
        'email_notifications', 'sms_notifications', 'push_notifications', 
        'marketing_emails', 'notification_frequency', 'updated_at'
    ]
    search_fields = ['user__email']
    readonly_fields = ['created_at', 'updated_at']
    ordering = ['user__email']
    
    fieldsets = (
        ('User', {
            'fields': ('user',)
        }),
        ('Communication Preferences', {
            'fields': ('email_notifications', 'sms_notifications', 'push_notifications', 'marketing_emails')
        }),
        ('Alert Preferences', {
            'fields': ('shipment_updates', 'quote_notifications', 'payment_alerts', 'security_alerts')
        }),
        ('Frequency Settings', {
            'fields': ('notification_frequency',)
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )