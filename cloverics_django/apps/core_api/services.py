"""
Core API Services - Django Implementation
Phase 7: Authentication and Core API System
Authentication services extracted from FastAPI
"""

import jwt
import hashlib
import asyncio
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from django.conf import settings
from django.contrib.auth import get_user_model
from django.db import transaction
from django.utils import timezone
from asgiref.sync import sync_to_async
from passlib.context import CryptContext

from .models import AuthSession, TokenBlacklist, LoginAttempt, APIEndpointAccess
from ..authentication.models import User

# Password hashing context
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# JWT Configuration
SECRET_KEY = settings.SECRET_KEY
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 60 * 24 * 7  # 7 days

# Demo account configuration
DEMO_ACCOUNTS = {
    "<EMAIL>": {
        "password": "demo123",
        "user_type": "CUSTOMER",
        "user_id": 90,
        "first_name": "<PERSON><PERSON><PERSON>",
        "last_name": "Demo"
    },
    "<EMAIL>": {
        "password": "demo123", 
        "user_type": "LOGISTICS_PROVIDER",
        "user_id": 89,
        "first_name": "Malik",
        "last_name": "Demo"
    },
    "<EMAIL>": {
        "password": "demo123",
        "user_type": "INSURANCE_PROVIDER", 
        "user_id": 4,
        "first_name": "Insurance",
        "last_name": "Demo"
    },
    "<EMAIL>": {
        "password": "demo123",
        "user_type": "INDEPENDENT_DRIVER",
        "user_id": 83,
        "first_name": "Driver",
        "last_name": "Demo"
    }
}

class AuthService:
    """
    Authentication service extracted from FastAPI
    Handles password verification, JWT token creation/verification
    """
    
    @staticmethod
    def verify_password(plain_password: str, hashed_password: str) -> bool:
        """Verify a password against its hash"""
        return pwd_context.verify(plain_password, hashed_password)
    
    @staticmethod
    def get_password_hash(password: str) -> str:
        """Generate password hash"""
        return pwd_context.hash(password)
    
    @staticmethod
    def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
        """Create JWT access token"""
        to_encode = data.copy()
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        to_encode.update({"exp": expire})
        encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
        return encoded_jwt
    
    @staticmethod
    def verify_token(token: str) -> Optional[dict]:
        """Verify JWT token and return payload"""
        try:
            payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
            return payload
        except jwt.JWTError:
            return None

class MockUser:
    """
    Mock user class for demo accounts when database is unavailable
    Mirrors the structure expected by templates and authentication system
    """
    def __init__(self, user_id: int, user_type: str, email: str, first_name: str = "", last_name: str = ""):
        self.id = user_id
        self.user_type = user_type
        self.email = email
        self.first_name = first_name or email.split('@')[0].title()
        self.last_name = last_name or "Demo"
        self.company_name = f"{self.first_name} Demo Company"
        self.is_active = True
        self.is_authenticated = True

class CoreAuthenticationService:
    """
    Core authentication service that handles user authentication
    Extracted from FastAPI get_current_user function
    """
    
    @staticmethod
    async def get_current_user_from_token(token: str) -> Optional[User]:
        """
        Extract and authenticate user from JWT token
        Includes fallback logic for demo accounts
        """
        if not token:
            return None
            
        # Remove 'Bearer ' prefix if present
        if token.startswith("Bearer "):
            token = token[7:]
        
        try:
            # Decode JWT token
            payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
            user_id = payload.get("user_id")
            email = payload.get("sub") or payload.get("email")  # Try both sub and email fields
            user_type = payload.get("user_type")
            
            print(f"🔧 JWT decoded: user_id={user_id}, email={email}, user_type={user_type}")
            
            # Demo account IDs for extra security - using actual database IDs
            DEMO_USER_IDS = [2, 3, 4, 5, 90, 89, 83]  # Include both old and new IDs for compatibility
            
            # Primary authentication: use email if available
            if email:
                try:
                    user = await sync_to_async(User.objects.get)(email=email)
                    print(f"✅ Email auth successful: {user.email} (ID: {user.id})")
                    return user
                except User.DoesNotExist:
                    print(f"❌ User not found by email: {email}")
                    # Fallback to user_id for demo accounts
                    if user_id and user_id in DEMO_USER_IDS:
                        try:
                            user = await sync_to_async(User.objects.get)(id=user_id)
                            print(f"✅ ID fallback successful: {user.email} (ID: {user.id})")
                            return user
                        except User.DoesNotExist:
                            print(f"❌ User not found by ID: {user_id}")
                            return None
            
            # Fallback for demo accounts with email=None but valid user_id
            elif not email and user_id and user_id in DEMO_USER_IDS:
                try:
                    user = await sync_to_async(User.objects.get)(id=user_id)
                    print(f"✅ Fallback user found: {user.email} (ID: {user.id})")
                    return user
                except Exception as e:
                    print(f"❌ Fallback failed: User ID {user_id} not found in database. Error: {e}")
                    return None
            
            # Handle customs accounts with generated user_ids
            if user_type == "CUSTOMS_AGENT" and user_id and user_id >= 1000:
                # Generate customs email based on user_id pattern
                customs_email = CoreAuthenticationService._generate_customs_email(user_id)
                if customs_email:
                    return MockUser(user_id, user_type, customs_email, 
                                  customs_email.split('@')[0].replace('customsof', '').title(), 
                                  "Customs")
            
            # If no email provided, check demo accounts by user_id and user_type
            if user_id and user_type and not email:
                for demo_email, demo_data in DEMO_ACCOUNTS.items():
                    if demo_data["user_id"] == user_id and demo_data["user_type"] == user_type:
                        return MockUser(user_id, user_type, demo_email, 
                                      demo_data["first_name"], demo_data["last_name"])
            
            print(f"❌ Invalid token payload: user_id={user_id}, email={email}")
            return None
            
        except jwt.JWTError as e:
            print(f"❌ JWT decode error: {e}")
            return None
        except Exception as e:
            print(f"❌ Authentication error: {e}")
            return None
    
    @staticmethod
    def _generate_customs_email(user_id: int) -> Optional[str]:
        """Generate customs email based on user_id pattern"""
        countries = ['albania', 'afghanistan', 'algeria', 'andorra', 'angola', 'antiguaandbarbuda', 
                    'argentina', 'armenia', 'australia', 'austria', 'azerbaijan', 'bahamas', 
                    'bahrain', 'bangladesh', 'barbados', 'belarus', 'belgium', 'belize', 'benin', 
                    'bhutan', 'bolivia', 'bosniaandherzegovina', 'botswana', 'brazil', 'brunei', 
                    'bulgaria', 'burkinafaso', 'burundi', 'caboverde', 'cambodia', 'cameroon', 
                    'canada', 'centralafricanrepublic', 'chad', 'chile', 'china', 'colombia', 
                    'comoros', 'congo', 'costarica', 'croatia', 'cuba', 'cyprus', 'czechrepublic']
        
        for country in countries:
            test_email = f"customsof{country}@demo.com"
            test_id = 1000 + (int(hashlib.md5(test_email.encode()).hexdigest(), 16) % 900)
            if test_id == user_id:
                return test_email
        return None

def track_api_access(user, endpoint: str, method: str, status_code: int, request):
    """
    Track API endpoint access for monitoring and analytics
    """
    try:
        APIEndpointAccess.objects.create(
            user=user if hasattr(user, 'id') else None,
            endpoint=endpoint,
            method=method,
            response_status=status_code,
            ip_address=request.META.get('REMOTE_ADDR', ''),
            user_agent=request.META.get('HTTP_USER_AGENT', ''),
            access_time=timezone.now(),
            response_time_ms=0  # Will be calculated in middleware
        )
    except Exception as e:
        print(f"Failed to track API access: {e}")

def track_login_attempt(email: str, success: bool, ip_address: str, 
                       failure_reason: str = None, user: User = None):
    """
    Track login attempts for security monitoring
    """
    try:
        LoginAttempt.objects.create(
            email=email,
            success=success,
            ip_address=ip_address,
            failure_reason=failure_reason,
            user=user,
            attempted_at=timezone.now()
        )
    except Exception as e:
        print(f"Failed to track login attempt: {e}")