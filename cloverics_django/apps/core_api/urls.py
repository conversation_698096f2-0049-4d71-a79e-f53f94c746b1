"""
Core API URLs - Django Implementation
Phase 7: Authentication and Core API System
URL patterns for authentication and core API endpoints
"""

from django.urls import path
from . import views

app_name = 'core_api'

urlpatterns = [
    # Landing page and core routes
    path('', views.landing_page, name='landing_page'),
    path('dashboard/', views.dashboard, name='dashboard'),
    
    # Authentication endpoints
    path('auth/login/', views.login_page, name='login_page'),
    path('auth/register/', views.register_page, name='register_page'),
    path('auth/login-submit/', views.login_submit, name='login_submit'),
    path('auth/register-submit/', views.register_submit, name='register_submit'),  # ADD: Missing registration submit
    # path('auth/logout/', views.logout_view, name='logout_view'),
    # path('auth/forgot-password/', views.forgot_password, name='forgot_password'),
    # path('auth/reset-password/<str:token>/', views.reset_password, name='reset_password'),
    
    # # Customer endpoints
    # path('customer/dashboard/', views.customer_dashboard, name='customer_dashboard'),
    # path('customer/search-shipping/', views.search_shipping, name='search_shipping'),
    # path('customer/track-shipment/', views.track_shipment, name='track_shipment'),
    # path('customer/manage-shipments/', views.manage_shipments, name='manage_shipments'),
    # path('customer/instant-multi-modal-quotes/', views.instant_multi_modal_quotes, name='instant_multi_modal_quotes'),
    # path('customer/multi-modal-optimizer/', views.multi_modal_optimizer, name='multi_modal_optimizer'),
    # path('customer/pricing-calculator/', views.pricing_calculator, name='pricing_calculator'),
    # path('customer/market-intelligence/', views.market_intelligence, name='market_intelligence'),
    # path('customer/ai-analytics/', views.ai_analytics, name='ai_analytics'),
    # path('customer/logistics-index/', views.logistics_index, name='logistics_index'),
    # path('customer/payment-checkout/', views.payment_checkout, name='payment_checkout'),
    # path('customer/invoice-reconciliation/', views.invoice_reconciliation, name='invoice_reconciliation'),
    # path('customer/rfq-automation/', views.rfq_automation, name='rfq_automation'),
    # path('customer/saved-searches/', views.saved_searches, name='saved_searches'),
    # path('customer/customs-declaration/', views.customs_declaration, name='customs_declaration'),
    # path('customer/document-automation/', views.document_automation, name='document_automation'),
    # path('customer/contract-lifecycle/', views.contract_lifecycle, name='contract_lifecycle'),
    # path('customer/compliance-automation/', views.compliance_automation, name='compliance_automation'),
    # path('customer/file-export-center/', views.file_export_center, name='file_export_center'),
    # path('customer/profile/', views.customer_profile, name='customer_profile'),
    # path('customer/notifications/', views.customer_notifications, name='customer_notifications'),
    # path('customer/messages/', views.customer_messages, name='customer_messages'),
    # path('customer/contact-support/', views.contact_support, name='contact_support'),
    
    # # Logistics endpoints
    # path('logistics/dashboard/', views.logistics_dashboard, name='logistics_dashboard'),
    # path('logistics/carrier-integration/', views.carrier_integration, name='carrier_integration'),
    # path('logistics/clients/', views.logistics_clients, name='logistics_clients'),
    # path('logistics/commission-settings/', views.commission_settings, name='commission_settings'),
    # path('logistics/performance/', views.logistics_performance, name='logistics_performance'),
    # path('logistics/reports/', views.logistics_reports, name='logistics_reports'),
    # path('logistics/analytics/', views.logistics_analytics, name='logistics_analytics'),
    # path('logistics/settings/', views.logistics_settings, name='logistics_settings'),
    
    # # Customs endpoints
    # path('customs/dashboard/', views.customs_dashboard, name='customs_dashboard'),
    # path('customs/clearance/', views.customs_clearance, name='customs_clearance'),
    # path('customs/declaration-details/', views.declaration_details, name='declaration_details'),
    # path('customs/compliance/', views.customs_compliance, name='customs_compliance'),
    # path('customs/reports/', views.customs_reports, name='customs_reports'),
    # path('customs/settings/', views.customs_settings, name='customs_settings'),
    
    # # Driver endpoints
    # path('driver/dashboard/', views.driver_dashboard, name='driver_dashboard'),
    # path('driver/available-jobs/', views.available_jobs, name='available_jobs'),
    # path('driver/route-optimization/', views.route_optimization, name='route_optimization'),
    # path('driver/earnings/', views.driver_earnings, name='driver_earnings'),
    # path('driver/profile/', views.driver_profile, name='driver_profile'),
    
    # # Insurance endpoints
    # path('insurance/dashboard/', views.insurance_dashboard, name='insurance_dashboard'),
    # path('insurance/policies/', views.insurance_policies, name='insurance_policies'),
    # path('insurance/claims/', views.insurance_claims, name='insurance_claims'),
    # path('insurance/reports/', views.insurance_reports, name='insurance_reports'),
    
    # # Admin endpoints
    # path('admin/dashboard/', views.admin_dashboard, name='admin_dashboard'),
    # path('admin/performance/', views.admin_performance, name='admin_performance'),
    # path('admin/reports/', views.admin_reports, name='admin_reports'),
    # path('admin/settings/', views.admin_settings, name='admin_settings'),
    
    # # API endpoints
    # path('api/shipments/', views.api_shipments, name='api_shipments'),
    # path('api/quotes/', views.api_quotes, name='api_quotes'),
    # path('api/tracking/', views.api_tracking, name='api_tracking'),
    # path('api/analytics/', views.api_analytics, name='api_analytics'),
]