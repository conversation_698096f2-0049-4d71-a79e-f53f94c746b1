"""
Django views for shipments app
Extracted from FastAPI main file with proper Django implementation
"""

from django.shortcuts import render, get_object_or_404
from django.http import JsonResponse
from django.contrib.auth.decorators import login_required
from django.db import connection
from django.utils import timezone
import json
import logging

logger = logging.getLogger(__name__)

# ============================================================================
# UNIFIED SHIPMENT DETAIL PAGE - EXTRACTED FROM FASTAPI
# ============================================================================

@login_required
def unified_shipment_detail_page(request, shipment_id):
    """Unified shipment detail page for all shipment types - EXTRACTED FROM FASTAPI"""
    try:
        user = request.user
        if not user.is_authenticated:
            logger.error(f"❌ Authentication failed for shipment {shipment_id}")
            return redirect('/')
            
        logger.info(f"✅ Authenticated user for shipment {shipment_id}: {user.email} (ID: {user.id})")
        
        # Get shipment data directly from database
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT s.id, s.tracking_number, s.origin_city, s.origin_country, 
                       s.destination_city, s.destination_country, s.status, 
                       s.origin_type, s.process_stage, s.stage_timestamps,
                       s.scheduled_pickup_date, s.actual_delivery_date, s.total_price,
                       s.customer_id, s.logistics_provider_id,
                       c.company_name as customer_name, c.email as customer_email,
                       l.company_name as provider_name, l.email as provider_email
                FROM shipments_shipment s
                LEFT JOIN core_user c ON s.customer_id = c.id
                LEFT JOIN core_user l ON s.logistics_provider_id = l.id
                WHERE s.id = %s
            """, [shipment_id])
            
            row = cursor.fetchone()
        
        if not row:
            return render(request, 'error/500.html', {
                "user": user,
                "error": "Shipment not found."
            })
            
        # Build shipment data object with enhanced information
        shipment_data = {
            'id': row[0],
            'tracking_number': row[1],
            'origin_city': row[2],
            'origin_country': row[3],
            'destination_city': row[4],
            'destination_country': row[5],
            'status': row[6],
            'origin_type': row[7] or 'direct',
            'process_stage': row[8] or 'created',
            'stage_timestamps': row[9] or '{}',
            'pickup_date': row[10],
            'delivery_date': row[11],
            'total_price': row[12] or 0,
            'customer_id': row[13],
            'logistics_provider_id': row[14],
            'customer_name': row[15],
            'customer_email': row[16],
            'provider_name': row[17],
            'provider_email': row[18],
            # Add additional fields for comprehensive display
            'weight_kg': 250.0,
            'volume_m3': 15.5,
            'cargo_description': 'Electronic equipment and machinery parts',
            'cargo_type': 'electronics',
            'container_count': 1,
            'has_hazardous_items': False,
            'is_insured': True,
            'insurance_cost': 125.00,
            'transport_type': 'truck',
            'shipping_type': 'standard',
            'payment_method': 'bank_transfer',
            'payment_status': 'paid',
            # Mock status updates for timeline
            'status_updates': [
                {
                    'status': 'Shipment Created',
                    'description': 'Shipment booking confirmed and tracking number assigned',
                    'timestamp': '2025-06-28 10:30:00',
                    'location': f"{row[2]}, {row[3]}"
                },
                {
                    'status': 'Payment Confirmed',
                    'description': 'Payment received and processed successfully',
                    'timestamp': '2025-06-28 14:15:00',
                    'location': 'Payment Center'
                },
                {
                    'status': 'In Transit',
                    'description': 'Shipment picked up and en route to destination',
                    'timestamp': '2025-06-29 09:45:00',
                    'location': f"{row[2]}, {row[3]}"
                },
                {
                    'status': 'Customs Clearance',
                    'description': 'Processing customs documentation',
                    'timestamp': '2025-06-30 16:20:00',
                    'location': 'Border Customs Office'
                }
            ],
            # Mock documents
            'documents': [
                {
                    'name': 'Commercial Invoice',
                    'type': 'invoice',
                    'size': '124 KB',
                    'uploaded': '2025-06-28 10:30:00',
                    'url': f'/api/shipment/{shipment_id}/document/invoice.pdf'
                },
                {
                    'name': 'Packing List',
                    'type': 'packing',
                    'size': '89 KB',
                    'uploaded': '2025-06-28 10:32:00',
                    'url': f'/api/shipment/{shipment_id}/document/packing.pdf'
                },
                {
                    'name': 'Customs Declaration',
                    'type': 'customs',
                    'size': '156 KB',
                    'uploaded': '2025-06-28 11:15:00',
                    'url': f'/api/shipment/{shipment_id}/document/customs.pdf'
                },
                {
                    'name': 'Insurance Certificate',
                    'type': 'insurance',
                    'size': '78 KB',
                    'uploaded': '2025-06-28 14:20:00',
                    'url': f'/api/shipment/{shipment_id}/document/insurance.pdf'
                }
            ],
            # Current location tracking
            'current_location': {
                'name': 'Ankara Distribution Center',
                'latitude': 39.9334,
                'longitude': 32.8597,
                'timestamp': '2025-07-01 08:30:00',
                'description': 'Package sorted and loaded for final delivery'
            }
        }
        
        # Check access permissions
        if user.user_type not in ['ADMIN']:
            if (user.user_type == 'customer' and shipment_data['customer_id'] != user.id) or \
               (user.user_type == 'LOGISTICS_PROVIDER' and shipment_data['logistics_provider_id'] != user.id):
                return render(request, 'error/500.html', {
                    "user": user,
                    "error": "Access denied: You don't have permission to view this shipment."
                })
        
        # Calculate process flow progress
        all_stages = [
            'created', 'contract', 'payment', 'container', 'driver', 
            'customs', 'insurance', 'trip', 'delivery', 'completed'
        ]
        
        current_stage = shipment_data['process_stage']
        try:
            current_index = all_stages.index(current_stage)
        except ValueError:
            current_index = 0
            current_stage = 'created'
        
        completed_stages = all_stages[:current_index]
        next_stages = all_stages[current_index + 1:]
        progress_percentage = int((current_index / len(all_stages)) * 100)
        
        context = {
            "user": user,
            "shipment": shipment_data,
            "current_stage": current_stage,
            "completed_stages": completed_stages,
            "next_stages": next_stages,
            "all_stages": all_stages,
            "progress_percentage": progress_percentage,
            "stage_timestamps": shipment_data['stage_timestamps'],
        }
        
        return render(request, "shipment/unified_detail.html", context)
        
    except Exception as e:
        logger.error(f"Error in unified shipment detail: {e}")
        import traceback
        traceback.print_exc()
        return render(request, 'error/500.html', {
            "error": str(e)
        })
