# Shipments URLs - Core shipment functionality
from django.urls import path
from . import views

app_name = 'shipments'

urlpatterns = [
    # Core shipment management
    path('', views.shipment_list, name='shipment_list'),
    path('create/', views.create_shipment, name='create_shipment'),
    path('<int:shipment_id>/', views.shipment_detail, name='shipment_detail'),
    path('<int:shipment_id>/edit/', views.edit_shipment, name='edit_shipment'),
    path('<int:shipment_id>/update/', views.update_shipment, name='update_shipment'),
    path('<int:shipment_id>/cancel/', views.cancel_shipment, name='cancel_shipment'),
    path('<int:shipment_id>/track/', views.track_shipment, name='track_shipment'),
    
    # Quote management
    path('quotes/', views.quote_list, name='quote_list'),
    path('quotes/create/', views.create_quote, name='create_quote'),
    path('quotes/<int:quote_id>/', views.quote_detail, name='quote_detail'),
    path('quotes/<int:quote_id>/accept/', views.accept_quote, name='accept_quote'),
    path('quotes/<int:quote_id>/decline/', views.decline_quote, name='decline_quote'),
    
    # Route management
    path('routes/', views.route_list, name='route_list'),
    path('routes/create/', views.create_route, name='create_route'),
    path('routes/<int:route_id>/', views.route_detail, name='route_detail'),
    path('routes/<int:route_id>/edit/', views.edit_route, name='edit_route'),
    path('routes/<int:route_id>/delete/', views.delete_route, name='delete_route'),
    
    # Rate management
    path('rates/', views.rate_list, name='rate_list'),
    path('rates/calculate/', views.calculate_rate, name='calculate_rate'),
    path('rates/quote/', views.request_rate_quote, name='request_rate_quote'),
    
    # Tracking and status
    path('tracking/<str:tracking_number>/', views.track_by_number, name='track_by_number'),
    path('status/<int:shipment_id>/', views.shipment_status, name='shipment_status'),
    path('updates/<int:shipment_id>/', views.shipment_updates, name='shipment_updates'),
    
    # API endpoints for mobile and AJAX
    path('api/search/', views.api_search_shipments, name='api_search_shipments'),
    path('api/<int:shipment_id>/status/', views.api_shipment_status, name='api_shipment_status'),
    path('api/<int:shipment_id>/update-status/', views.api_update_status, name='api_update_status'),
    path('api/quotes/batch/', views.api_batch_quotes, name='api_batch_quotes'),
    path('api/routes/optimize/', views.api_optimize_route, name='api_optimize_route'),
    
    # Document management
    path('<int:shipment_id>/documents/', views.shipment_documents, name='shipment_documents'),
    path('<int:shipment_id>/documents/upload/', views.upload_document, name='upload_document'),
    path('documents/<int:document_id>/download/', views.download_document, name='download_document'),
    
    # Advanced features
    path('batch/', views.batch_operations, name='batch_operations'),
    path('analytics/', views.shipment_analytics, name='shipment_analytics'),
    path('export/', views.export_shipments, name='export_shipments'),
    path('import/', views.import_shipments, name='import_shipments'),
]