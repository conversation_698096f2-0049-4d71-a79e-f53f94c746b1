# Generated by Django 5.2.1 on 2025-08-07 12:45

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='CargoType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('description', models.TextField(blank=True)),
                ('hazardous', models.BooleanField(default=False)),
                ('requires_refrigeration', models.BooleanField(default=False)),
                ('requires_special_handling', models.BooleanField(default=False)),
                ('special_requirements', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name='ContainerType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('length_m', models.DecimalField(decimal_places=2, max_digits=5)),
                ('width_m', models.DecimalField(decimal_places=2, max_digits=5)),
                ('height_m', models.DecimalField(decimal_places=2, max_digits=5)),
                ('max_weight_kg', models.DecimalField(decimal_places=2, max_digits=10)),
                ('description', models.TextField(blank=True)),
            ],
        ),
        migrations.CreateModel(
            name='TransportType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('type', models.CharField(choices=[('SEA', 'Sea'), ('AIR', 'Air'), ('ROAD', 'Road'), ('RAIL', 'Rail')], max_length=10, unique=True)),
                ('description', models.TextField(blank=True)),
                ('avg_speed_kmh', models.DecimalField(blank=True, decimal_places=2, max_digits=7, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='CustomerOffer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(help_text='Brief description of your shipping need', max_length=255)),
                ('description', models.TextField(blank=True, help_text='Additional details about your shipment')),
                ('origin_country', models.CharField(db_index=True, max_length=100)),
                ('origin_city', models.CharField(max_length=100)),
                ('destination_country', models.CharField(db_index=True, max_length=100)),
                ('destination_city', models.CharField(max_length=100)),
                ('shipment_type', models.CharField(choices=[('GENERAL', 'General Cargo'), ('HAZARDOUS', 'Hazardous Materials'), ('REFRIGERATED', 'Refrigerated Goods'), ('BULK', 'Bulk Cargo'), ('LIQUID', 'Liquid Cargo'), ('OVERSIZED', 'Oversized Cargo'), ('ELECTRONICS', 'Electronics'), ('AUTOMOTIVE', 'Automotive Parts'), ('TEXTILES', 'Textiles'), ('MACHINERY', 'Machinery')], db_index=True, default='GENERAL', max_length=20)),
                ('transport_type', models.CharField(choices=[('SEA', 'Sea Freight'), ('AIR', 'Air Freight'), ('ROAD', 'Road Transport'), ('RAIL', 'Rail Transport'), ('MULTIMODAL', 'Multimodal'), ('ANY', 'Any Transport Type')], db_index=True, default='ANY', max_length=20)),
                ('weight_kg', models.DecimalField(blank=True, decimal_places=2, help_text='Weight in kilograms', max_digits=10, null=True)),
                ('volume_cbm', models.DecimalField(blank=True, decimal_places=3, help_text='Volume in cubic meters', max_digits=10, null=True)),
                ('budget_min', models.DecimalField(blank=True, decimal_places=2, help_text='Minimum budget (USD)', max_digits=10, null=True)),
                ('budget_max', models.DecimalField(blank=True, decimal_places=2, help_text='Maximum budget (USD)', max_digits=10, null=True)),
                ('preferred_timeframe', models.CharField(choices=[('ASAP', 'As Soon As Possible'), ('1_WEEK', 'Within 1 Week'), ('2_WEEKS', 'Within 2 Weeks'), ('1_MONTH', 'Within 1 Month'), ('FLEXIBLE', 'Flexible Timing')], db_index=True, default='FLEXIBLE', max_length=20)),
                ('pickup_date_earliest', models.DateField(blank=True, null=True)),
                ('pickup_date_latest', models.DateField(blank=True, null=True)),
                ('requires_insurance', models.BooleanField(default=False)),
                ('requires_customs_clearance', models.BooleanField(default=False)),
                ('special_requirements', models.TextField(blank=True)),
                ('status', models.CharField(choices=[('PENDING', 'Pending'), ('CONTACTED', 'Contacted'), ('ACCEPTED', 'Accepted'), ('EXPIRED', 'Expired'), ('CANCELLED', 'Cancelled')], db_index=True, default='PENDING', max_length=20)),
                ('expires_at', models.DateTimeField(db_index=True)),
                ('contact_phone', models.CharField(blank=True, max_length=20)),
                ('contact_email', models.EmailField(blank=True, max_length=254)),
                ('created_at', models.DateTimeField(auto_now_add=True, db_index=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='shipping_offers', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='LogisticsInterest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('message', models.TextField(help_text='Your message to the customer')),
                ('quoted_price', models.DecimalField(blank=True, decimal_places=2, help_text='Your quoted price (USD)', max_digits=10, null=True)),
                ('estimated_days', models.PositiveIntegerField(blank=True, help_text='Estimated transit time in days', null=True)),
                ('transport_mode', models.CharField(blank=True, max_length=50)),
                ('service_level', models.CharField(choices=[('STANDARD', 'Standard Service'), ('EXPRESS', 'Express Service'), ('ECONOMY', 'Economy Service'), ('PREMIUM', 'Premium Service')], default='STANDARD', max_length=50)),
                ('includes_insurance', models.BooleanField(default=False)),
                ('includes_customs', models.BooleanField(default=False)),
                ('includes_pickup', models.BooleanField(default=False)),
                ('includes_delivery', models.BooleanField(default=False)),
                ('status', models.CharField(choices=[('INTERESTED', 'Interested'), ('QUOTED', 'Quote Provided'), ('SELECTED', 'Selected by Customer'), ('REJECTED', 'Rejected'), ('WITHDRAWN', 'Withdrawn')], db_index=True, default='INTERESTED', max_length=20)),
                ('contact_person', models.CharField(blank=True, max_length=100)),
                ('contact_phone', models.CharField(blank=True, max_length=20)),
                ('contact_email', models.EmailField(blank=True, max_length=254)),
                ('created_at', models.DateTimeField(auto_now_add=True, db_index=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('logistics_provider', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='offer_responses', to=settings.AUTH_USER_MODEL)),
                ('offer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='logistics_responses', to='shipments.customeroffer')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='OfferDocument',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('document_type', models.CharField(choices=[('CARGO_IMAGE', 'Cargo Image'), ('SPECIFICATION', 'Technical Specification'), ('PACKING_LIST', 'Packing List'), ('INVOICE', 'Commercial Invoice'), ('OTHER', 'Other Document')], default='OTHER', max_length=20)),
                ('file', models.FileField(upload_to='offer_documents/')),
                ('filename', models.CharField(max_length=255)),
                ('file_size', models.PositiveIntegerField(help_text='File size in bytes')),
                ('description', models.CharField(blank=True, max_length=255)),
                ('uploaded_at', models.DateTimeField(auto_now_add=True)),
                ('offer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='documents', to='shipments.customeroffer')),
            ],
            options={
                'ordering': ['-uploaded_at'],
            },
        ),
        migrations.CreateModel(
            name='PrivateShipment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('container_id', models.CharField(max_length=50, unique=True)),
                ('container_type', models.CharField(default='40ft', max_length=50)),
                ('max_participants', models.PositiveIntegerField(default=5)),
                ('current_participants', models.PositiveIntegerField(default=1)),
                ('origin_country', models.CharField(max_length=100)),
                ('origin_city', models.CharField(max_length=100)),
                ('destination_country', models.CharField(max_length=100)),
                ('destination_city', models.CharField(max_length=100)),
                ('total_capacity_m3', models.DecimalField(decimal_places=2, default=67.7, max_digits=10)),
                ('total_capacity_kg', models.DecimalField(decimal_places=2, default=26000, max_digits=10)),
                ('used_capacity_m3', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('used_capacity_kg', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('invitation_deadline', models.DateTimeField()),
                ('container_finalization_deadline', models.DateTimeField()),
                ('scheduled_departure_date', models.DateTimeField()),
                ('estimated_delivery_date', models.DateTimeField()),
                ('status', models.CharField(choices=[('active', 'Active - Accepting Invitations'), ('invitation_closed', 'Invitation Period Closed'), ('finalized', 'Container Finalized'), ('shipped', 'Shipped'), ('delivered', 'Delivered'), ('cancelled', 'Cancelled')], default='active', max_length=20)),
                ('is_private', models.BooleanField(default=True)),
                ('container_finalized', models.BooleanField(default=False)),
                ('all_payments_completed', models.BooleanField(default=False)),
                ('base_cost_per_m3', models.DecimalField(decimal_places=2, max_digits=10)),
                ('shared_fees', models.JSONField(default=list)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('finalized_at', models.DateTimeField(blank=True, null=True)),
                ('initiating_customer', models.ForeignKey(limit_choices_to={'user_type': 'CUSTOMER'}, on_delete=django.db.models.deletion.CASCADE, related_name='private_shipments_initiated', to=settings.AUTH_USER_MODEL)),
                ('logistics_provider', models.ForeignKey(limit_choices_to={'user_type': 'LOGISTICS'}, on_delete=django.db.models.deletion.CASCADE, related_name='private_shipments_managed', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='PrivateShipmentInvitation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('pending', 'Pending Response'), ('accepted', 'Accepted'), ('declined', 'Declined'), ('removed', 'Removed by Logistics'), ('expired', 'Expired'), ('waitlisted', 'Waitlisted'), ('cancelled', 'Cancelled'), ('cancel_pending', 'Cancellation Pending Approval')], default='pending', max_length=20)),
                ('invitation_message', models.TextField(blank=True)),
                ('is_waitlist', models.BooleanField(default=False)),
                ('cargo_description', models.TextField(blank=True)),
                ('weight_kg', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('volume_m3', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('has_hazardous_items', models.BooleanField(default=False)),
                ('special_requirements', models.TextField(blank=True)),
                ('individual_cost', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('cost_savings', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('invited_at', models.DateTimeField(auto_now_add=True)),
                ('responded_at', models.DateTimeField(blank=True, null=True)),
                ('expires_at', models.DateTimeField()),
                ('cancel_reason', models.TextField(blank=True, null=True)),
                ('cancelled_by', models.CharField(blank=True, choices=[('customer', 'Customer'), ('logistics', 'Logistics'), ('system', 'System')], max_length=20, null=True)),
                ('cancelled_at', models.DateTimeField(blank=True, null=True)),
                ('penalty_applied', models.BooleanField(default=False)),
                ('penalty_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('refund_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('is_ai_suggested', models.BooleanField(default=False, help_text='Whether this invitation was generated by AI recommendations')),
                ('ai_match_score', models.DecimalField(blank=True, decimal_places=2, help_text='AI match score percentage', max_digits=5, null=True)),
                ('ai_recommendation_reasons', models.JSONField(blank=True, help_text='Reasons for AI recommendation', null=True)),
                ('invited_customer', models.ForeignKey(limit_choices_to={'user_type': 'CUSTOMER'}, on_delete=django.db.models.deletion.CASCADE, related_name='private_shipment_invitations', to=settings.AUTH_USER_MODEL)),
                ('logistics_provider', models.ForeignKey(limit_choices_to={'user_type': 'LOGISTICS'}, on_delete=django.db.models.deletion.CASCADE, related_name='private_invitations_sent', to=settings.AUTH_USER_MODEL)),
                ('private_shipment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='invitations', to='shipments.privateshipment')),
            ],
            options={
                'ordering': ['-invited_at'],
            },
        ),
        migrations.CreateModel(
            name='PrivateShipmentPayment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount_due', models.DecimalField(decimal_places=2, max_digits=10)),
                ('payment_method', models.CharField(blank=True, choices=[('stripe', 'Credit/Debit Card (Stripe)'), ('bank_transfer', 'Bank Transfer'), ('paypal', 'PayPal')], max_length=20)),
                ('payment_status', models.CharField(choices=[('pending', 'Payment Pending'), ('processing', 'Processing'), ('paid', 'Paid'), ('failed', 'Payment Failed'), ('overdue', 'Payment Overdue'), ('cancelled', 'Payment Cancelled')], default='pending', max_length=20)),
                ('payment_deadline', models.DateTimeField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('paid_at', models.DateTimeField(blank=True, null=True)),
                ('stripe_payment_intent_id', models.CharField(blank=True, max_length=255, null=True)),
                ('bank_transfer_reference', models.CharField(blank=True, max_length=255, null=True)),
                ('payment_proof_url', models.URLField(blank=True, null=True)),
                ('payment_notes', models.TextField(blank=True)),
                ('late_fee_applied', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('customer', models.ForeignKey(limit_choices_to={'user_type': 'CUSTOMER'}, on_delete=django.db.models.deletion.CASCADE, related_name='private_shipment_payments', to=settings.AUTH_USER_MODEL)),
                ('invitation', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='payment', to='shipments.privateshipmentinvitation')),
                ('private_shipment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payments', to='shipments.privateshipment')),
            ],
        ),
        migrations.CreateModel(
            name='QuoteBatch',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('origin_country', models.CharField(db_index=True, max_length=100)),
                ('origin_city', models.CharField(db_index=True, max_length=100)),
                ('destination_country', models.CharField(db_index=True, max_length=100)),
                ('destination_city', models.CharField(db_index=True, max_length=100)),
                ('cargo_weight', models.DecimalField(decimal_places=2, max_digits=10)),
                ('cargo_type', models.CharField(default='general', max_length=50)),
                ('transport_mode', models.CharField(default='truck', max_length=20)),
                ('urgency', models.CharField(default='standard', max_length=20)),
                ('status', models.CharField(choices=[('processing', 'Processing Quotes'), ('completed', 'Completed'), ('failed', 'Failed'), ('timeout', 'Timeout')], db_index=True, default='processing', max_length=20)),
                ('total_quotes', models.PositiveIntegerField(default=0)),
                ('successful_quotes', models.PositiveIntegerField(default=0)),
                ('response_time_seconds', models.DecimalField(blank=True, decimal_places=3, max_digits=8, null=True)),
                ('quote_data', models.JSONField(default=list, help_text='Normalized quote responses from all providers')),
                ('market_insights', models.JSONField(default=dict, help_text='Market analysis and recommendations')),
                ('created_at', models.DateTimeField(auto_now_add=True, db_index=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('customer', models.ForeignKey(limit_choices_to={'user_type': 'CUSTOMER'}, on_delete=django.db.models.deletion.CASCADE, related_name='shipment_quote_batches', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Route',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('origin_country', models.CharField(max_length=100)),
                ('origin_city', models.CharField(max_length=100)),
                ('destination_country', models.CharField(max_length=100)),
                ('destination_city', models.CharField(max_length=100)),
                ('shipping_type', models.CharField(choices=[('domestic', 'Domestic'), ('international', 'International')], db_index=True, default='international', help_text='Type of shipping: domestic (within same country) or international', max_length=15)),
                ('distance_km', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('typical_duration_days', models.DecimalField(decimal_places=1, help_text='Typical shipment duration in days', max_digits=5)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('last_used', models.DateTimeField(blank=True, help_text='Last time this route was used for a shipment', null=True)),
            ],
            options={
                'unique_together': {('origin_country', 'origin_city', 'destination_country', 'destination_city')},
            },
        ),
        migrations.CreateModel(
            name='SharedContainer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('container_id', models.CharField(max_length=50, unique=True)),
                ('origin_country', models.CharField(max_length=100)),
                ('origin_city', models.CharField(max_length=100)),
                ('destination_country', models.CharField(max_length=100)),
                ('destination_city', models.CharField(max_length=100)),
                ('total_capacity_m3', models.DecimalField(decimal_places=2, max_digits=10)),
                ('total_capacity_kg', models.DecimalField(decimal_places=2, max_digits=10)),
                ('used_capacity_m3', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('used_capacity_kg', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('capacity_percentage', models.DecimalField(decimal_places=2, default=0, max_digits=5)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('scheduled_departure_date', models.DateTimeField()),
                ('estimated_delivery_date', models.DateTimeField()),
                ('status', models.CharField(choices=[('OPEN', 'Open for Booking'), ('FILLING', 'Filling Up'), ('READY', 'Ready to Ship'), ('SHIPPED', 'Shipped'), ('DELIVERED', 'Delivered'), ('CANCELLED', 'Cancelled')], default='OPEN', max_length=20)),
                ('min_capacity_reached', models.BooleanField(default=False)),
                ('base_price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('admin_fee_percentage', models.DecimalField(decimal_places=2, default=7.0, max_digits=5)),
                ('current_location', models.JSONField(blank=True, null=True)),
                ('status_updates', models.JSONField(default=list)),
                ('container_type', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='shipments.containertype')),
                ('logistics_provider', models.ForeignKey(limit_choices_to={'user_type': 'LOGISTICS'}, on_delete=django.db.models.deletion.CASCADE, related_name='provider_shared_containers', to=settings.AUTH_USER_MODEL)),
                ('transport_type', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='shipments.transporttype')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='SharedContainerBooking',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('cargo_description', models.TextField()),
                ('weight_kg', models.DecimalField(decimal_places=2, max_digits=10)),
                ('volume_m3', models.DecimalField(decimal_places=2, max_digits=10)),
                ('has_hazardous_items', models.BooleanField(default=False)),
                ('space_percentage', models.DecimalField(decimal_places=2, max_digits=5)),
                ('base_price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('admin_fee', models.DecimalField(decimal_places=2, max_digits=10)),
                ('total_price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('booking_confirmed', models.BooleanField(default=False)),
                ('payment_authorized', models.BooleanField(default=False)),
                ('payment_charged', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('cargo_type', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='shipments.cargotype')),
                ('customer', models.ForeignKey(limit_choices_to={'user_type': 'CUSTOMER'}, on_delete=django.db.models.deletion.CASCADE, related_name='shared_container_bookings', to=settings.AUTH_USER_MODEL)),
                ('shared_container', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='bookings', to='shipments.sharedcontainer')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Shipment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_shared_container', models.BooleanField(db_index=True, default=False)),
                ('container_space_percentage', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True)),
                ('origin_type', models.CharField(choices=[('direct', 'Direct Shipment'), ('quote', 'Quote-based Shipment'), ('private', 'Private Shipment')], db_index=True, default='direct', help_text='How this shipment was created (direct selection, quote acceptance, or private invitation)', max_length=20)),
                ('process_stage', models.CharField(choices=[('created', 'Shipment Created'), ('contract', 'Contract Signing'), ('payment', 'Payment Processing'), ('container', 'Container Assignment'), ('driver', 'Driver Assignment'), ('customs', 'Customs Declaration'), ('insurance', 'Insurance Handling'), ('trip', 'Trip Progress'), ('delivery', 'Delivery Confirmation'), ('feedback', 'Rating & Feedback'), ('completed', 'Completed')], db_index=True, default='created', help_text='Current stage in the unified shipment process flow', max_length=30)),
                ('source_quote_id', models.IntegerField(blank=True, help_text='Quote ID if this shipment originated from quote acceptance', null=True)),
                ('source_invitation_id', models.IntegerField(blank=True, help_text='Private invitation ID if this shipment originated from invitation acceptance', null=True)),
                ('stage_timestamps', models.JSONField(default=dict, help_text='Timestamps for each process stage completion')),
                ('process_metadata', models.JSONField(default=dict, help_text='Additional process flow data - ready for audit logging, AI insights, etc.')),
                ('origin_country', models.CharField(db_index=True, max_length=100)),
                ('origin_city', models.CharField(db_index=True, max_length=100)),
                ('destination_country', models.CharField(db_index=True, max_length=100)),
                ('destination_city', models.CharField(db_index=True, max_length=100)),
                ('shipping_type', models.CharField(choices=[('domestic', 'Domestic'), ('international', 'International')], db_index=True, default='international', help_text='Type of shipping: domestic (within same country) or international', max_length=15)),
                ('cargo_description', models.TextField()),
                ('weight_kg', models.DecimalField(decimal_places=2, max_digits=10)),
                ('volume_m3', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('container_count', models.PositiveIntegerField(default=1)),
                ('has_hazardous_items', models.BooleanField(db_index=True, default=False)),
                ('booking_date', models.DateTimeField(auto_now_add=True, db_index=True)),
                ('scheduled_pickup_date', models.DateTimeField(db_index=True)),
                ('estimated_delivery_date', models.DateTimeField(db_index=True)),
                ('actual_delivery_date', models.DateTimeField(blank=True, null=True)),
                ('tracking_number', models.CharField(db_index=True, max_length=50, unique=True)),
                ('status', models.CharField(choices=[('BOOKED', 'Booked'), ('PENDING', 'Pending'), ('CONFIRMED', 'Confirmed'), ('IN_TRANSIT', 'In Transit'), ('CUSTOMS', 'In Customs'), ('DELIVERED', 'Delivered'), ('CANCELLED', 'Cancelled')], db_index=True, default='PENDING', max_length=20)),
                ('status_updates', models.JSONField(default=list)),
                ('current_location', models.JSONField(blank=True, null=True)),
                ('customs_clearance_required', models.BooleanField(db_index=True, default=True)),
                ('customs_cleared', models.BooleanField(db_index=True, default=False)),
                ('customs_clearance_date', models.DateTimeField(blank=True, null=True)),
                ('documents', models.JSONField(default=list)),
                ('base_price', models.DecimalField(decimal_places=2, default=0.0, max_digits=10)),
                ('additional_fees', models.JSONField(default=list)),
                ('total_price', models.DecimalField(decimal_places=2, default=0.0, max_digits=10)),
                ('insurance_cost', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('is_insured', models.BooleanField(db_index=True, default=False)),
                ('contract_signed', models.BooleanField(db_index=True, default=False)),
                ('contract_file', models.CharField(blank=True, max_length=255)),
                ('payment_status', models.CharField(db_index=True, default='PENDING', max_length=20)),
                ('payment_method', models.CharField(choices=[('stripe', 'Stripe Payment'), ('bank_transfer', 'Bank Transfer')], db_index=True, default='stripe', help_text='Payment method chosen by customer', max_length=15)),
                ('bank_transfer_proof', models.FileField(blank=True, help_text='Bank transfer receipt uploaded by customer', null=True, upload_to='bank_transfer_proofs/')),
                ('bank_transfer_status', models.CharField(choices=[('pending', 'Pending Review'), ('approved', 'Approved'), ('rejected', 'Rejected')], db_index=True, default='pending', help_text='Status of bank transfer verification', max_length=10)),
                ('platform_fee_paid', models.BooleanField(db_index=True, default=False, help_text='Whether platform fee has been successfully charged')),
                ('platform_fee_attempts', models.PositiveIntegerField(default=0, help_text='Number of attempts to charge platform fee')),
                ('suspension_status', models.BooleanField(db_index=True, default=False, help_text='Whether booking is suspended due to payment issues')),
                ('bank_transfer_approved_at', models.DateTimeField(blank=True, help_text='When bank transfer was approved by logistics provider', null=True)),
                ('platform_fee_charged_at', models.DateTimeField(blank=True, help_text='When platform fee was successfully charged', null=True)),
                ('is_paid', models.BooleanField(db_index=True, default=False, help_text='Whether booking payment has been completed')),
                ('payment_due_date', models.DateTimeField(blank=True, help_text='Payment deadline for bank transfers', null=True)),
                ('cancellation_status', models.CharField(choices=[('none', 'None'), ('requested_by_customer', 'Requested by Customer'), ('mutually_cancelled', 'Cancelled by Agreement'), ('declined_by_logistics', 'Declined by Logistics'), ('cancelled_by_logistics', 'Cancelled by Logistics (Unpaid)')], db_index=True, default='none', help_text='Current cancellation status', max_length=50)),
                ('cancellation_reason', models.TextField(blank=True, help_text='Reason for cancellation', null=True)),
                ('refund_issued', models.BooleanField(db_index=True, default=False, help_text='Whether refund has been processed')),
                ('penalty_deducted', models.BooleanField(default=False, help_text='Whether cancellation penalty has been deducted')),
                ('cancellation_requested_at', models.DateTimeField(blank=True, help_text='When cancellation was requested', null=True)),
                ('cancellation_processed_at', models.DateTimeField(blank=True, help_text='When cancellation was processed', null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True, db_index=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('cargo_type', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='shipments.cargotype')),
                ('container_type', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='shipments.containertype')),
                ('customer', models.ForeignKey(limit_choices_to={'user_type': 'CUSTOMER'}, on_delete=django.db.models.deletion.CASCADE, related_name='customer_shipments', to=settings.AUTH_USER_MODEL)),
                ('logistics_provider', models.ForeignKey(limit_choices_to={'user_type': 'LOGISTICS'}, on_delete=django.db.models.deletion.CASCADE, related_name='provider_shipments', to=settings.AUTH_USER_MODEL)),
                ('route', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='shipments', to='shipments.route')),
                ('shared_container_booking', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='shipments', to='shipments.sharedcontainerbooking')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Review',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('rating', models.PositiveSmallIntegerField(choices=[(1, '1 Star'), (2, '2 Stars'), (3, '3 Stars'), (4, '4 Stars'), (5, '5 Stars')])),
                ('comment', models.TextField(blank=True)),
                ('delivery_on_time', models.BooleanField()),
                ('cargo_condition', models.PositiveSmallIntegerField(blank=True, choices=[(1, 'Poor'), (2, 'Fair'), (3, 'Good'), (4, 'Excellent')], null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('customer', models.ForeignKey(limit_choices_to={'user_type': 'CUSTOMER'}, on_delete=django.db.models.deletion.CASCADE, related_name='reviews_given', to=settings.AUTH_USER_MODEL)),
                ('logistics_provider', models.ForeignKey(limit_choices_to={'user_type': 'LOGISTICS'}, on_delete=django.db.models.deletion.CASCADE, related_name='reviews_received', to=settings.AUTH_USER_MODEL)),
                ('shipment', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='review', to='shipments.shipment')),
            ],
        ),
        migrations.AddField(
            model_name='privateshipmentinvitation',
            name='accepted_shipment',
            field=models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='private_invitation', to='shipments.shipment'),
        ),
        migrations.CreateModel(
            name='ShipmentTracking',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('timestamp', models.DateTimeField(auto_now_add=True, db_index=True)),
                ('location_name', models.CharField(max_length=255)),
                ('location_country', models.CharField(blank=True, max_length=100)),
                ('latitude', models.DecimalField(blank=True, decimal_places=6, max_digits=9, null=True)),
                ('longitude', models.DecimalField(blank=True, decimal_places=6, max_digits=9, null=True)),
                ('status', models.CharField(choices=[('BOOKING_RECEIVED', 'Booking Received'), ('BOOKING_CONFIRMED', 'Booking Confirmed'), ('PAYMENT_RECEIVED', 'Payment Received'), ('PICKUP_SCHEDULED', 'Pickup Scheduled'), ('PICKED_UP', 'Picked Up from Origin'), ('ARRIVED_ORIGIN_WAREHOUSE', 'Arrived at Origin Warehouse'), ('PROCESSING_AT_ORIGIN', 'Processing at Origin'), ('DEPARTED_ORIGIN', 'Departed Origin'), ('IN_TRANSIT', 'In Transit'), ('ARRIVED_MIDDLE_PORT', 'Arrived at Transit Port'), ('DEPARTED_MIDDLE_PORT', 'Departed Transit Port'), ('CUSTOMS_PROCESSING', 'Customs Processing'), ('CUSTOMS_INSPECTION', 'Customs Inspection'), ('CUSTOMS_CLEARANCE', 'Customs Cleared'), ('CUSTOMS_HOLD', 'Customs Hold'), ('ARRIVED_DESTINATION_COUNTRY', 'Arrived at Destination Country'), ('OUT_FOR_DELIVERY', 'Out for Delivery'), ('DELIVERY_ATTEMPT', 'Delivery Attempted'), ('DELIVERED', 'Delivered'), ('DELAYED', 'Delayed'), ('ON_HOLD', 'On Hold'), ('CANCELLED', 'Cancelled'), ('RETURNED', 'Returned to Sender')], max_length=30)),
                ('description', models.TextField(blank=True)),
                ('update_source', models.CharField(choices=[('API', 'Carrier API'), ('MANUAL', 'Manual Update'), ('SYSTEM', 'System Generated')], default='MANUAL', max_length=10)),
                ('carrier_code', models.CharField(blank=True, help_text='Carrier-specific status code', max_length=20)),
                ('carrier_description', models.TextField(blank=True, help_text='Original status description from carrier')),
                ('is_customer_visible', models.BooleanField(default=True, help_text='Whether this update is visible to customers')),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('shipment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='tracking_details', to='shipments.shipment')),
                ('updated_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='tracking_updates', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-timestamp'],
            },
        ),
        migrations.CreateModel(
            name='ShippingRate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('base_price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('price_per_kg', models.DecimalField(decimal_places=2, max_digits=10)),
                ('price_per_km', models.DecimalField(decimal_places=2, max_digits=10)),
                ('weight_factor', models.DecimalField(decimal_places=2, default=0.5, help_text='Price multiplier per kg', max_digits=10)),
                ('volume_factor', models.DecimalField(decimal_places=2, default=10.0, help_text='Price multiplier per cubic meter', max_digits=10)),
                ('base_rate', models.DecimalField(blank=True, decimal_places=2, help_text='Alternative base rate field', max_digits=10, null=True)),
                ('fuel_surcharge', models.DecimalField(decimal_places=2, default=15.0, help_text='Fuel surcharge percentage', max_digits=5)),
                ('handling_fee', models.DecimalField(decimal_places=2, default=25.0, help_text='Handling fee in USD', max_digits=8)),
                ('estimated_days', models.PositiveIntegerField(help_text='Estimated delivery time in days')),
                ('available_space', models.PositiveIntegerField(help_text='Number of available containers/spaces')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('container_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='shipments.containertype')),
                ('logistics_provider', models.ForeignKey(limit_choices_to={'user_type': 'LOGISTICS'}, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('route', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='shipments.route')),
                ('transport_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='shipments.transporttype')),
            ],
        ),
        migrations.AddField(
            model_name='shipment',
            name='shipping_rate',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='shipments.shippingrate'),
        ),
        migrations.CreateModel(
            name='QuoteRequest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quote_reference', models.CharField(db_index=True, max_length=50, unique=True)),
                ('cargo_weight', models.DecimalField(decimal_places=2, max_digits=10)),
                ('cargo_dimensions', models.CharField(max_length=200)),
                ('pickup_date', models.DateField()),
                ('delivery_date', models.DateField()),
                ('special_requirements', models.TextField(blank=True)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('quoted', 'Quote Received'), ('accepted', 'Accepted'), ('declined', 'Declined'), ('expired', 'Expired')], default='pending', max_length=20)),
                ('quoted_price', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('provider_response', models.TextField(blank=True)),
                ('estimated_delivery_days', models.PositiveIntegerField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('expires_at', models.DateTimeField()),
                ('customer', models.ForeignKey(limit_choices_to={'user_type': 'CUSTOMER'}, on_delete=django.db.models.deletion.CASCADE, related_name='shipment_quote_requests', to=settings.AUTH_USER_MODEL)),
                ('logistics_provider', models.ForeignKey(limit_choices_to={'user_type': 'LOGISTICS_PROVIDER'}, on_delete=django.db.models.deletion.CASCADE, related_name='received_quote_requests', to=settings.AUTH_USER_MODEL)),
                ('shipping_rate', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='quote_requests', to='shipments.shippingrate')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='TrackingNumberCounter',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField(db_index=True, unique=True)),
                ('counter', models.PositiveIntegerField(default=0)),
            ],
            options={
                'indexes': [models.Index(fields=['date'], name='shipments_t_date_b7840c_idx')],
            },
        ),
        migrations.AddField(
            model_name='shipment',
            name='transport_type',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='shipments.transporttype'),
        ),
        migrations.AddIndex(
            model_name='customeroffer',
            index=models.Index(fields=['status', 'expires_at'], name='shipments_c_status_d1f9b8_idx'),
        ),
        migrations.AddIndex(
            model_name='customeroffer',
            index=models.Index(fields=['origin_country', 'destination_country'], name='shipments_c_origin__da95f2_idx'),
        ),
        migrations.AddIndex(
            model_name='customeroffer',
            index=models.Index(fields=['shipment_type', 'transport_type'], name='shipments_c_shipmen_1410e9_idx'),
        ),
        migrations.AddIndex(
            model_name='customeroffer',
            index=models.Index(fields=['customer', 'status'], name='shipments_c_custome_452efa_idx'),
        ),
        migrations.AddIndex(
            model_name='customeroffer',
            index=models.Index(fields=['budget_min', 'budget_max'], name='shipments_c_budget__95d5b6_idx'),
        ),
        migrations.AddIndex(
            model_name='customeroffer',
            index=models.Index(fields=['created_at'], name='shipments_c_created_d321ad_idx'),
        ),
        migrations.AddIndex(
            model_name='logisticsinterest',
            index=models.Index(fields=['offer', 'status'], name='shipments_l_offer_i_76471b_idx'),
        ),
        migrations.AddIndex(
            model_name='logisticsinterest',
            index=models.Index(fields=['logistics_provider', 'status'], name='shipments_l_logisti_f71dc2_idx'),
        ),
        migrations.AddIndex(
            model_name='logisticsinterest',
            index=models.Index(fields=['quoted_price'], name='shipments_l_quoted__5e33e2_idx'),
        ),
        migrations.AddIndex(
            model_name='logisticsinterest',
            index=models.Index(fields=['created_at'], name='shipments_l_created_b25def_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='logisticsinterest',
            unique_together={('offer', 'logistics_provider')},
        ),
        migrations.AddIndex(
            model_name='privateshipmentpayment',
            index=models.Index(fields=['payment_status'], name='shipments_p_payment_bc02af_idx'),
        ),
        migrations.AddIndex(
            model_name='privateshipmentpayment',
            index=models.Index(fields=['payment_deadline'], name='shipments_p_payment_9904c3_idx'),
        ),
        migrations.AddIndex(
            model_name='privateshipmentpayment',
            index=models.Index(fields=['created_at'], name='shipments_p_created_7a7bb9_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='privateshipmentpayment',
            unique_together={('private_shipment', 'customer')},
        ),
        migrations.AddIndex(
            model_name='quotebatch',
            index=models.Index(fields=['customer', 'status'], name='shipments_q_custome_8b290e_idx'),
        ),
        migrations.AddIndex(
            model_name='quotebatch',
            index=models.Index(fields=['origin_country', 'destination_country'], name='shipments_q_origin__c4a782_idx'),
        ),
        migrations.AddIndex(
            model_name='quotebatch',
            index=models.Index(fields=['created_at'], name='shipments_q_created_7cf9b7_idx'),
        ),
        migrations.AddIndex(
            model_name='quotebatch',
            index=models.Index(fields=['status', 'created_at'], name='shipments_q_status_d67842_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='privateshipmentinvitation',
            unique_together={('private_shipment', 'invited_customer')},
        ),
        migrations.AddIndex(
            model_name='quoterequest',
            index=models.Index(fields=['customer', 'status'], name='shipments_q_custome_27fd1b_idx'),
        ),
        migrations.AddIndex(
            model_name='quoterequest',
            index=models.Index(fields=['logistics_provider', 'status'], name='shipments_q_logisti_749c3b_idx'),
        ),
        migrations.AddIndex(
            model_name='quoterequest',
            index=models.Index(fields=['quote_reference'], name='shipments_q_quote_r_5229f8_idx'),
        ),
        migrations.AddIndex(
            model_name='quoterequest',
            index=models.Index(fields=['created_at'], name='shipments_q_created_b32542_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='shippingrate',
            unique_together={('logistics_provider', 'transport_type', 'route', 'container_type')},
        ),
        migrations.AddIndex(
            model_name='shipment',
            index=models.Index(fields=['customer', 'status'], name='shipments_s_custome_4b2da2_idx'),
        ),
        migrations.AddIndex(
            model_name='shipment',
            index=models.Index(fields=['logistics_provider', 'status'], name='shipments_s_logisti_bbf22d_idx'),
        ),
        migrations.AddIndex(
            model_name='shipment',
            index=models.Index(fields=['tracking_number'], name='shipments_s_trackin_80db53_idx'),
        ),
        migrations.AddIndex(
            model_name='shipment',
            index=models.Index(fields=['status', 'customs_cleared'], name='shipments_s_status_7a139b_idx'),
        ),
        migrations.AddIndex(
            model_name='shipment',
            index=models.Index(fields=['origin_country', 'destination_country'], name='shipments_s_origin__6b4ea6_idx'),
        ),
        migrations.AddIndex(
            model_name='shipment',
            index=models.Index(fields=['booking_date'], name='shipments_s_booking_48c8dd_idx'),
        ),
        migrations.AddIndex(
            model_name='shipment',
            index=models.Index(fields=['scheduled_pickup_date'], name='shipments_s_schedul_22c5f9_idx'),
        ),
        migrations.AddIndex(
            model_name='shipment',
            index=models.Index(fields=['estimated_delivery_date'], name='shipments_s_estimat_5c786c_idx'),
        ),
        migrations.AddIndex(
            model_name='shipment',
            index=models.Index(fields=['payment_status', 'is_insured'], name='shipments_s_payment_025a63_idx'),
        ),
        migrations.AddIndex(
            model_name='shipment',
            index=models.Index(fields=['customs_clearance_required', 'customs_cleared'], name='shipments_s_customs_8e08fc_idx'),
        ),
    ]
