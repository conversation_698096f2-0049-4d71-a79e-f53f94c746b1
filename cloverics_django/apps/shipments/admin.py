from django.contrib import admin
from .models import (
    Route, CargoType, TransportType, ContainerType, 
    ShippingRate, Shipment, ShipmentTracking, Review
)

@admin.register(Route)
class RouteAdmin(admin.ModelAdmin):
    list_display = ('origin_city', 'origin_country', 'destination_city', 'destination_country', 'distance_km', 'typical_duration_days')
    list_filter = ('origin_country', 'destination_country')
    search_fields = ('origin_city', 'origin_country', 'destination_city', 'destination_country')

@admin.register(CargoType)
class CargoTypeAdmin(admin.ModelAdmin):
    list_display = ('name', 'hazardous', 'requires_refrigeration', 'requires_special_handling')
    list_filter = ('hazardous', 'requires_refrigeration', 'requires_special_handling')
    search_fields = ('name', 'description')

@admin.register(TransportType)
class TransportTypeAdmin(admin.ModelAdmin):
    list_display = ('type', 'avg_speed_kmh')
    search_fields = ('type', 'description')

@admin.register(ContainerType)
class ContainerTypeAdmin(admin.ModelAdmin):
    list_display = ('name', 'length_m', 'width_m', 'height_m', 'max_weight_kg', 'volume_m3')
    search_fields = ('name', 'description')

@admin.register(ShippingRate)
class ShippingRateAdmin(admin.ModelAdmin):
    list_display = ('logistics_provider', 'transport_type', 'route', 'container_type', 'base_price', 'estimated_days', 'available_space', 'is_active')
    list_filter = ('transport_type', 'is_active')
    search_fields = ('logistics_provider__company_name', 'route__origin_city', 'route__destination_city')

@admin.register(Shipment)
class ShipmentAdmin(admin.ModelAdmin):
    list_display = ('tracking_number', 'customer', 'logistics_provider', 'origin_city', 'destination_city', 'status', 'booking_date', 'scheduled_pickup_date')
    list_filter = ('status', 'transport_type', 'has_hazardous_items', 'is_insured', 'customs_clearance_required')
    search_fields = ('tracking_number', 'customer__company_name', 'logistics_provider__company_name', 'origin_city', 'destination_city')
    readonly_fields = ('booking_date', 'created_at', 'updated_at')
    fieldsets = (
        ('Basic Info', {
            'fields': ('tracking_number', 'customer', 'logistics_provider', 'shipping_rate', 'status')
        }),
        ('Route', {
            'fields': ('origin_country', 'origin_city', 'destination_country', 'destination_city')
        }),
        ('Cargo Details', {
            'fields': ('cargo_type', 'cargo_description', 'weight_kg', 'volume_m3', 'container_type', 'container_count', 'has_hazardous_items')
        }),
        ('Transport', {
            'fields': ('transport_type',)
        }),
        ('Dates', {
            'fields': ('booking_date', 'scheduled_pickup_date', 'estimated_delivery_date', 'actual_delivery_date')
        }),
        ('Tracking', {
            'fields': ('status_updates', 'current_location')
        }),
        ('Customs', {
            'fields': ('customs_clearance_required', 'customs_cleared', 'customs_clearance_date', 'documents')
        }),
        ('Billing', {
            'fields': ('base_price', 'additional_fees', 'total_price', 'insurance_cost', 'is_insured', 'payment_status')
        }),
        ('Contract', {
            'fields': ('contract_signed', 'contract_file')
        })
    )

@admin.register(ShipmentTracking)
class ShipmentTrackingAdmin(admin.ModelAdmin):
    list_display = ('shipment', 'timestamp', 'location_name', 'status', 'updated_by')
    list_filter = ('status',)
    search_fields = ('shipment__tracking_number', 'location_name')
    readonly_fields = ('timestamp',)

@admin.register(Review)
class ReviewAdmin(admin.ModelAdmin):
    list_display = ('shipment', 'customer', 'logistics_provider', 'rating', 'delivery_on_time', 'created_at')
    list_filter = ('rating', 'delivery_on_time')
    search_fields = ('shipment__tracking_number', 'customer__company_name', 'logistics_provider__company_name', 'comment')
    readonly_fields = ('created_at',)
