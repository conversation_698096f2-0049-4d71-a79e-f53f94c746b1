"""
Cloverics Offer Models

This module defines the database models for the reverse marketplace system
where customers post offers and logistics providers respond.
"""

from django.db import models
from django.conf import settings
from decimal import Decimal

class CustomerOffer(models.Model):
    """Model for customer shipping offers that logistics companies can bid on."""
    
    STATUS_CHOICES = [
        ('PENDING', 'Pending'),
        ('CONTACTED', 'Contacted'),
        ('ACCEPTED', 'Accepted'),
        ('EXPIRED', 'Expired'),
        ('CANCELLED', 'Cancelled'),
    ]
    
    SHIPMENT_TYPE_CHOICES = [
        ('GENERAL', 'General Cargo'),
        ('HAZARDOUS', 'Hazardous Materials'),
        ('REFRIGERATED', 'Refrigerated Goods'),
        ('BULK', 'Bulk Cargo'),
        ('LIQUID', 'Liquid Cargo'),
        ('OVERSIZED', 'Oversized Cargo'),
        ('ELECTRONICS', 'Electronics'),
        ('AUTOMOTIVE', 'Automotive Parts'),
        ('TEXTILES', 'Textiles'),
        ('MACHINERY', 'Machinery'),
    ]
    
    TIMEFRAME_CHOICES = [
        ('ASAP', 'As Soon As Possible'),
        ('1_WEEK', 'Within 1 Week'),
        ('2_WEEKS', 'Within 2 Weeks'),
        ('1_MONTH', 'Within 1 Month'),
        ('FLEXIBLE', 'Flexible Timing'),
    ]
    
    TRANSPORT_TYPE_CHOICES = [
        ('SEA', 'Sea Freight'),
        ('AIR', 'Air Freight'),
        ('ROAD', 'Road Transport'),
        ('RAIL', 'Rail Transport'),
        ('MULTIMODAL', 'Multimodal'),
        ('ANY', 'Any Transport Type'),
    ]
    
    # Basic offer information
    customer = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='shipping_offers',
        db_index=True
    )
    
    title = models.CharField(max_length=255, help_text="Brief description of your shipping need")
    description = models.TextField(blank=True, help_text="Additional details about your shipment")
    
    # Route information
    origin_country = models.CharField(max_length=100, db_index=True)
    origin_city = models.CharField(max_length=100)
    destination_country = models.CharField(max_length=100, db_index=True)
    destination_city = models.CharField(max_length=100)
    
    # Shipment details
    shipment_type = models.CharField(
        max_length=20,
        choices=SHIPMENT_TYPE_CHOICES,
        default='GENERAL',
        db_index=True
    )
    transport_type = models.CharField(
        max_length=20,
        choices=TRANSPORT_TYPE_CHOICES,
        default='ANY',
        db_index=True
    )
    
    # Weight and volume
    weight_kg = models.DecimalField(
        max_digits=10, 
        decimal_places=2, 
        null=True, 
        blank=True,
        help_text="Weight in kilograms"
    )
    volume_cbm = models.DecimalField(
        max_digits=10, 
        decimal_places=3, 
        null=True, 
        blank=True,
        help_text="Volume in cubic meters"
    )
    
    # Budget and timing
    budget_min = models.DecimalField(
        max_digits=10, 
        decimal_places=2, 
        null=True, 
        blank=True,
        help_text="Minimum budget (USD)"
    )
    budget_max = models.DecimalField(
        max_digits=10, 
        decimal_places=2, 
        null=True, 
        blank=True,
        help_text="Maximum budget (USD)"
    )
    preferred_timeframe = models.CharField(
        max_length=20,
        choices=TIMEFRAME_CHOICES,
        default='FLEXIBLE',
        db_index=True
    )
    pickup_date_earliest = models.DateField(null=True, blank=True)
    pickup_date_latest = models.DateField(null=True, blank=True)
    
    # Special requirements
    requires_insurance = models.BooleanField(default=False)
    requires_customs_clearance = models.BooleanField(default=False)
    special_requirements = models.TextField(blank=True)
    
    # Status tracking
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='PENDING',
        db_index=True
    )
    expires_at = models.DateTimeField(db_index=True)
    
    # Contact preferences
    contact_phone = models.CharField(max_length=20, blank=True)
    contact_email = models.EmailField(blank=True)
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True, db_index=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['status', 'expires_at']),
            models.Index(fields=['origin_country', 'destination_country']),
            models.Index(fields=['shipment_type', 'transport_type']),
            models.Index(fields=['customer', 'status']),
            models.Index(fields=['budget_min', 'budget_max']),
            models.Index(fields=['created_at']),
        ]
    
    def __str__(self):
        return f"{self.title} - {self.origin_city} to {self.destination_city}"
    
    def is_expired(self):
        """Check if the offer has expired."""
        from django.utils import timezone
        return timezone.now() > self.expires_at
    
    def get_budget_display(self):
        """Get a formatted budget range."""
        if self.budget_min and self.budget_max:
            return f"${self.budget_min:,.2f} - ${self.budget_max:,.2f}"
        elif self.budget_max:
            return f"Up to ${self.budget_max:,.2f}"
        elif self.budget_min:
            return f"From ${self.budget_min:,.2f}"
        else:
            return "Budget negotiable"
    
    def get_weight_volume_display(self):
        """Get a formatted weight/volume display."""
        parts = []
        if self.weight_kg:
            parts.append(f"{self.weight_kg} kg")
        if self.volume_cbm:
            parts.append(f"{self.volume_cbm} m³")
        return " | ".join(parts) if parts else "Not specified"


class LogisticsInterest(models.Model):
    """Model for logistics companies expressing interest in customer offers."""
    
    STATUS_CHOICES = [
        ('INTERESTED', 'Interested'),
        ('QUOTED', 'Quote Provided'),
        ('SELECTED', 'Selected by Customer'),
        ('REJECTED', 'Rejected'),
        ('WITHDRAWN', 'Withdrawn'),
    ]
    
    # Relationships
    offer = models.ForeignKey(
        CustomerOffer,
        on_delete=models.CASCADE,
        related_name='logistics_responses',
        db_index=True
    )
    logistics_provider = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='offer_responses',
        db_index=True
    )
    
    # Response details
    message = models.TextField(help_text="Your message to the customer")
    quoted_price = models.DecimalField(
        max_digits=10, 
        decimal_places=2,
        null=True,
        blank=True,
        help_text="Your quoted price (USD)"
    )
    estimated_days = models.PositiveIntegerField(
        null=True,
        blank=True,
        help_text="Estimated transit time in days"
    )
    
    # Service details
    transport_mode = models.CharField(max_length=50, blank=True)
    service_level = models.CharField(
        max_length=50, 
        choices=[
            ('STANDARD', 'Standard Service'),
            ('EXPRESS', 'Express Service'),
            ('ECONOMY', 'Economy Service'),
            ('PREMIUM', 'Premium Service'),
        ],
        default='STANDARD'
    )
    
    # Additional services offered
    includes_insurance = models.BooleanField(default=False)
    includes_customs = models.BooleanField(default=False)
    includes_pickup = models.BooleanField(default=False)
    includes_delivery = models.BooleanField(default=False)
    
    # Status and metadata
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='INTERESTED',
        db_index=True
    )
    
    # Contact information
    contact_person = models.CharField(max_length=100, blank=True)
    contact_phone = models.CharField(max_length=20, blank=True)
    contact_email = models.EmailField(blank=True)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True, db_index=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        unique_together = ['offer', 'logistics_provider']
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['offer', 'status']),
            models.Index(fields=['logistics_provider', 'status']),
            models.Index(fields=['quoted_price']),
            models.Index(fields=['created_at']),
        ]
    
    def __str__(self):
        return f"{self.logistics_provider.company_name} -> {self.offer.title}"
    
    def get_service_summary(self):
        """Get a summary of included services."""
        services = []
        if self.includes_insurance:
            services.append("Insurance")
        if self.includes_customs:
            services.append("Customs Clearance")
        if self.includes_pickup:
            services.append("Pickup")
        if self.includes_delivery:
            services.append("Delivery")
        
        return ", ".join(services) if services else "Basic transport only"


class OfferDocument(models.Model):
    """Model for documents attached to customer offers."""
    
    DOCUMENT_TYPE_CHOICES = [
        ('CARGO_IMAGE', 'Cargo Image'),
        ('SPECIFICATION', 'Technical Specification'),
        ('PACKING_LIST', 'Packing List'),
        ('INVOICE', 'Commercial Invoice'),
        ('OTHER', 'Other Document'),
    ]
    
    offer = models.ForeignKey(
        CustomerOffer,
        on_delete=models.CASCADE,
        related_name='documents'
    )
    
    document_type = models.CharField(
        max_length=20,
        choices=DOCUMENT_TYPE_CHOICES,
        default='OTHER'
    )
    
    file = models.FileField(upload_to='offer_documents/')
    filename = models.CharField(max_length=255)
    file_size = models.PositiveIntegerField(help_text="File size in bytes")
    
    description = models.CharField(max_length=255, blank=True)
    
    uploaded_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        ordering = ['-uploaded_at']
    
    def __str__(self):
        return f"{self.filename} ({self.get_document_type_display()})"
    
    def get_file_size_display(self):
        """Get a human-readable file size."""
        size = self.file_size
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024.0:
                return f"{size:.1f} {unit}"
            size /= 1024.0
        return f"{size:.1f} TB"