from django.db import models
from django.conf import settings
from django.utils.translation import gettext_lazy as _
from django.db.models import F
import datetime
import re
import json

# Import offer models
from .offer_models import CustomerOffer, LogisticsInterest, OfferDocument

class Route(models.Model):
    """Routes for shipments, showing origin and destination."""
    
    SHIPPING_TYPE_CHOICES = [
        ('domestic', 'Domestic'),
        ('international', 'International'),
    ]
    
    origin_country = models.CharField(max_length=100)
    origin_city = models.CharField(max_length=100)
    destination_country = models.CharField(max_length=100)
    destination_city = models.CharField(max_length=100)
    shipping_type = models.CharField(
        max_length=15, 
        choices=SHIPPING_TYPE_CHOICES, 
        default='international',
        db_index=True,
        help_text="Type of shipping: domestic (within same country) or international"
    )
    distance_km = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True)
    typical_duration_days = models.DecimalField(
        max_digits=5, decimal_places=1, 
        help_text="Typical shipment duration in days"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    last_used = models.DateTimeField(blank=True, null=True, help_text="Last time this route was used for a shipment")
    
    class Meta:
        unique_together = [['origin_country', 'origin_city', 'destination_country', 'destination_city']]
        
    def __str__(self):
        return f"{self.origin_city}, {self.origin_country} → {self.destination_city}, {self.destination_country}"

class CargoType(models.Model):
    """Types of cargo that can be shipped."""
    
    name = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True)
    hazardous = models.BooleanField(default=False)
    requires_refrigeration = models.BooleanField(default=False)
    requires_special_handling = models.BooleanField(default=False)
    special_requirements = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    def __str__(self):
        return self.name

class TransportType(models.Model):
    """Types of transport (sea, air, road, rail)."""
    
    TYPE_CHOICES = [
        ('SEA', 'Sea'),
        ('AIR', 'Air'),
        ('ROAD', 'Road'),
        ('RAIL', 'Rail'),
    ]
    
    type = models.CharField(max_length=10, choices=TYPE_CHOICES, unique=True)
    description = models.TextField(blank=True)
    avg_speed_kmh = models.DecimalField(max_digits=7, decimal_places=2, blank=True, null=True)
    
    def __str__(self):
        return self.get_type_display()

class ContainerType(models.Model):
    """Standard container types for shipping."""
    
    name = models.CharField(max_length=100, unique=True)
    length_m = models.DecimalField(max_digits=5, decimal_places=2)
    width_m = models.DecimalField(max_digits=5, decimal_places=2)
    height_m = models.DecimalField(max_digits=5, decimal_places=2)
    max_weight_kg = models.DecimalField(max_digits=10, decimal_places=2)
    description = models.TextField(blank=True)
    
    def __str__(self):
        return self.name
    
    @property
    def volume_m3(self):
        """Calculate container volume in cubic meters."""
        return self.length_m * self.width_m * self.height_m

class ShippingRate(models.Model):
    """Rates for shipping based on transport type and route."""
    
    logistics_provider = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        limit_choices_to={'user_type': 'LOGISTICS'}
    )
    transport_type = models.ForeignKey(TransportType, on_delete=models.CASCADE)
    route = models.ForeignKey(Route, on_delete=models.CASCADE)
    container_type = models.ForeignKey(ContainerType, on_delete=models.CASCADE)
    base_price = models.DecimalField(max_digits=10, decimal_places=2)
    price_per_kg = models.DecimalField(max_digits=10, decimal_places=2)
    price_per_km = models.DecimalField(max_digits=10, decimal_places=2)
    
    # Additional pricing factors
    weight_factor = models.DecimalField(max_digits=10, decimal_places=2, default=0.5, help_text="Price multiplier per kg")
    volume_factor = models.DecimalField(max_digits=10, decimal_places=2, default=10.0, help_text="Price multiplier per cubic meter")
    base_rate = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, help_text="Alternative base rate field")
    
    # Surcharge and fee fields
    fuel_surcharge = models.DecimalField(max_digits=5, decimal_places=2, default=15.00, help_text="Fuel surcharge percentage")
    handling_fee = models.DecimalField(max_digits=8, decimal_places=2, default=25.00, help_text="Handling fee in USD")
    
    estimated_days = models.PositiveIntegerField(help_text="Estimated delivery time in days")
    available_space = models.PositiveIntegerField(help_text="Number of available containers/spaces")
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        unique_together = [
            ['logistics_provider', 'transport_type', 'route', 'container_type']
        ]
    
    def __str__(self):
        return f"{self.logistics_provider.company_name} - {self.transport_type} - {self.route}"

class SharedContainer(models.Model):
    """Model for container sharing sessions."""
    
    STATUS_CHOICES = [
        ('OPEN', 'Open for Booking'),
        ('FILLING', 'Filling Up'),
        ('READY', 'Ready to Ship'),
        ('SHIPPED', 'Shipped'),
        ('DELIVERED', 'Delivered'),
        ('CANCELLED', 'Cancelled'),
    ]
    
    container_id = models.CharField(max_length=50, unique=True)
    logistics_provider = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='provider_shared_containers',
        limit_choices_to={'user_type': 'LOGISTICS'}
    )
    container_type = models.ForeignKey(ContainerType, on_delete=models.SET_NULL, null=True)
    transport_type = models.ForeignKey(TransportType, on_delete=models.SET_NULL, null=True)
    
    # Route information
    origin_country = models.CharField(max_length=100)
    origin_city = models.CharField(max_length=100)
    destination_country = models.CharField(max_length=100)
    destination_city = models.CharField(max_length=100)
    
    # Container capacity and usage
    total_capacity_m3 = models.DecimalField(max_digits=10, decimal_places=2)
    total_capacity_kg = models.DecimalField(max_digits=10, decimal_places=2)
    used_capacity_m3 = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    used_capacity_kg = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    capacity_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    
    # Dates
    created_at = models.DateTimeField(auto_now_add=True)
    scheduled_departure_date = models.DateTimeField()
    estimated_delivery_date = models.DateTimeField()
    
    # Status
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='OPEN')
    min_capacity_reached = models.BooleanField(default=False)  # 80% as per 2025 doc
    
    # Pricing
    base_price = models.DecimalField(max_digits=10, decimal_places=2)
    admin_fee_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=7.0)  # 7-10% as per 2025 doc
    
    # Tracking and updates
    current_location = models.JSONField(blank=True, null=True)
    status_updates = models.JSONField(default=list)
    
    class Meta:
        ordering = ['-created_at']
        
    def __str__(self):
        return f"Shared Container {self.container_id} - {self.status}"
    
    @property
    def space_available_percentage(self):
        """Calculate percentage of space still available"""
        return 100 - float(self.capacity_percentage)
    
    @property
    def is_booking_open(self):
        """Check if container is open for booking"""
        return self.status == 'OPEN' or (self.status == 'FILLING' and self.capacity_percentage < 80)

class SharedContainerBooking(models.Model):
    """Model for bookings in a shared container."""
    
    shared_container = models.ForeignKey(SharedContainer, on_delete=models.CASCADE, related_name='bookings')
    customer = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='shared_container_bookings',
        limit_choices_to={'user_type': 'CUSTOMER'}
    )
    
    # Cargo details
    cargo_type = models.ForeignKey(CargoType, on_delete=models.SET_NULL, null=True)
    cargo_description = models.TextField()
    weight_kg = models.DecimalField(max_digits=10, decimal_places=2)
    volume_m3 = models.DecimalField(max_digits=10, decimal_places=2)
    has_hazardous_items = models.BooleanField(default=False)
    
    # Space and pricing
    space_percentage = models.DecimalField(max_digits=5, decimal_places=2)
    base_price = models.DecimalField(max_digits=10, decimal_places=2)
    admin_fee = models.DecimalField(max_digits=10, decimal_places=2)
    total_price = models.DecimalField(max_digits=10, decimal_places=2)
    
    # Status
    booking_confirmed = models.BooleanField(default=False)
    payment_authorized = models.BooleanField(default=False)
    payment_charged = models.BooleanField(default=False)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-created_at']
        
    def __str__(self):
        return f"Booking in {self.shared_container.container_id} by {self.customer.company_name}"

class Shipment(models.Model):
    """Shipment model for tracking goods from origin to destination."""
    
    STATUS_CHOICES = [
        ('BOOKED', 'Booked'),
        ('PENDING', 'Pending'),
        ('CONFIRMED', 'Confirmed'),
        ('IN_TRANSIT', 'In Transit'),
        ('CUSTOMS', 'In Customs'),
        ('DELIVERED', 'Delivered'),
        ('CANCELLED', 'Cancelled'),
    ]
    
    SHIPPING_TYPE_CHOICES = [
        ('domestic', 'Domestic'),
        ('international', 'International'),
    ]
    
    # Detailed tracking status choices for more granular updates
    TRACKING_STATUS_CHOICES = [
        # Initial statuses
        ('BOOKING_RECEIVED', 'Booking Received'),
        ('BOOKING_CONFIRMED', 'Booking Confirmed'),
        ('PAYMENT_RECEIVED', 'Payment Received'),
        
        # Origin statuses
        ('PICKUP_SCHEDULED', 'Pickup Scheduled'),
        ('PICKED_UP', 'Picked Up from Origin'),
        ('ARRIVED_ORIGIN_WAREHOUSE', 'Arrived at Origin Warehouse'),
        ('PROCESSING_AT_ORIGIN', 'Processing at Origin'),
        ('DEPARTED_ORIGIN', 'Departed Origin'),
        
        # Transit statuses
        ('IN_TRANSIT', 'In Transit'),
        ('ARRIVED_MIDDLE_PORT', 'Arrived at Transit Port'),
        ('DEPARTED_MIDDLE_PORT', 'Departed Transit Port'),
        
        # Customs statuses
        ('CUSTOMS_PROCESSING', 'Customs Processing'),
        ('CUSTOMS_INSPECTION', 'Customs Inspection'),
        ('CUSTOMS_CLEARANCE', 'Customs Cleared'),
        ('CUSTOMS_HOLD', 'Customs Hold'),
        
        # Final delivery statuses
        ('ARRIVED_DESTINATION_COUNTRY', 'Arrived at Destination Country'),
        ('OUT_FOR_DELIVERY', 'Out for Delivery'),
        ('DELIVERY_ATTEMPT', 'Delivery Attempted'),
        ('DELIVERED', 'Delivered'),
        
        # Exception statuses
        ('DELAYED', 'Delayed'),
        ('ON_HOLD', 'On Hold'),
        ('CANCELLED', 'Cancelled'),
        ('RETURNED', 'Returned to Sender'),
    ]
    
    customer = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='customer_shipments',
        limit_choices_to={'user_type': 'CUSTOMER'},
        db_index=True
    )
    logistics_provider = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='provider_shipments',
        limit_choices_to={'user_type': 'LOGISTICS'},
        db_index=True
    )
    shipping_rate = models.ForeignKey(ShippingRate, on_delete=models.SET_NULL, null=True)
    route = models.ForeignKey(Route, on_delete=models.CASCADE, related_name='shipments', null=True, blank=True)
    cargo_type = models.ForeignKey(CargoType, on_delete=models.SET_NULL, null=True)
    container_type = models.ForeignKey(ContainerType, on_delete=models.SET_NULL, null=True)
    transport_type = models.ForeignKey(TransportType, on_delete=models.SET_NULL, null=True)
    
    # Container sharing information
    is_shared_container = models.BooleanField(default=False, db_index=True)
    shared_container_booking = models.ForeignKey(
        SharedContainerBooking, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True, 
        related_name='shipments'
    )
    container_space_percentage = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    
    # Process Flow Management - Core unified shipment process tracking
    ORIGIN_TYPE_CHOICES = [
        ('direct', 'Direct Shipment'),
        ('quote', 'Quote-based Shipment'),
        ('private', 'Private Shipment'),
    ]
    
    PROCESS_STAGE_CHOICES = [
        ('created', 'Shipment Created'),
        ('contract', 'Contract Signing'),
        ('payment', 'Payment Processing'),
        ('container', 'Container Assignment'),
        ('driver', 'Driver Assignment'),
        ('customs', 'Customs Declaration'),
        ('insurance', 'Insurance Handling'),
        ('trip', 'Trip Progress'),
        ('delivery', 'Delivery Confirmation'),
        ('feedback', 'Rating & Feedback'),
        ('completed', 'Completed'),
    ]
    
    origin_type = models.CharField(
        max_length=20,
        choices=ORIGIN_TYPE_CHOICES,
        default='direct',
        db_index=True,
        help_text="How this shipment was created (direct selection, quote acceptance, or private invitation)"
    )
    
    process_stage = models.CharField(
        max_length=30,
        choices=PROCESS_STAGE_CHOICES,
        default='created',
        db_index=True,
        help_text="Current stage in the unified shipment process flow"
    )
    
    # Source tracking for different origin types
    source_quote_id = models.IntegerField(
        null=True, 
        blank=True,
        help_text="Quote ID if this shipment originated from quote acceptance"
    )
    source_invitation_id = models.IntegerField(
        null=True,
        blank=True, 
        help_text="Private invitation ID if this shipment originated from invitation acceptance"
    )
    
    # Process stage timestamps for analytics
    stage_timestamps = models.JSONField(
        default=dict,
        help_text="Timestamps for each process stage completion"
    )
    
    # Process flow metadata (ready for future enhancements)
    process_metadata = models.JSONField(
        default=dict,
        help_text="Additional process flow data - ready for audit logging, AI insights, etc."
    )
    
    # Route information
    origin_country = models.CharField(max_length=100, db_index=True)
    origin_city = models.CharField(max_length=100, db_index=True)
    destination_country = models.CharField(max_length=100, db_index=True)
    destination_city = models.CharField(max_length=100, db_index=True)
    shipping_type = models.CharField(
        max_length=15, 
        choices=SHIPPING_TYPE_CHOICES, 
        default='international',
        db_index=True,
        help_text="Type of shipping: domestic (within same country) or international"
    )
    
    # Cargo details
    cargo_description = models.TextField()
    weight_kg = models.DecimalField(max_digits=10, decimal_places=2)
    volume_m3 = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True)
    container_count = models.PositiveIntegerField(default=1)
    has_hazardous_items = models.BooleanField(default=False, db_index=True)
    
    # Dates
    booking_date = models.DateTimeField(auto_now_add=True, db_index=True)
    scheduled_pickup_date = models.DateTimeField(db_index=True)
    estimated_delivery_date = models.DateTimeField(db_index=True)
    actual_delivery_date = models.DateTimeField(blank=True, null=True)
    
    # Status and tracking
    tracking_number = models.CharField(max_length=50, unique=True, db_index=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='PENDING', db_index=True)
    status_updates = models.JSONField(default=list)  # List of status update objects with timestamp
    current_location = models.JSONField(blank=True, null=True)  # GPS coordinates and location name
    
    # Customs and documentation
    customs_clearance_required = models.BooleanField(default=True, db_index=True)
    customs_cleared = models.BooleanField(default=False, db_index=True)
    customs_clearance_date = models.DateTimeField(blank=True, null=True)
    documents = models.JSONField(default=list)  # List of document references
    
    # Costs and billing
    base_price = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    additional_fees = models.JSONField(default=list)  # List of fee objects
    total_price = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    insurance_cost = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    is_insured = models.BooleanField(default=False, db_index=True)
    
    # Contract
    contract_signed = models.BooleanField(default=False, db_index=True)
    contract_file = models.CharField(max_length=255, blank=True)
    
    # Payment
    payment_status = models.CharField(max_length=20, default='PENDING', db_index=True)
    
    # Hybrid Payment Flow fields
    PAYMENT_METHOD_CHOICES = [
        ('stripe', 'Stripe Payment'),
        ('bank_transfer', 'Bank Transfer'),
    ]
    
    BANK_TRANSFER_STATUS_CHOICES = [
        ('pending', 'Pending Review'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
    ]
    
    payment_method = models.CharField(
        max_length=15, 
        choices=PAYMENT_METHOD_CHOICES, 
        default='stripe',
        db_index=True,
        help_text="Payment method chosen by customer"
    )
    bank_transfer_proof = models.FileField(
        upload_to='bank_transfer_proofs/', 
        blank=True, 
        null=True,
        help_text="Bank transfer receipt uploaded by customer"
    )
    bank_transfer_status = models.CharField(
        max_length=10, 
        choices=BANK_TRANSFER_STATUS_CHOICES, 
        default='pending',
        db_index=True,
        help_text="Status of bank transfer verification"
    )
    platform_fee_paid = models.BooleanField(
        default=False, 
        db_index=True,
        help_text="Whether platform fee has been successfully charged"
    )
    platform_fee_attempts = models.PositiveIntegerField(
        default=0,
        help_text="Number of attempts to charge platform fee"
    )
    suspension_status = models.BooleanField(
        default=False, 
        db_index=True,
        help_text="Whether booking is suspended due to payment issues"
    )
    bank_transfer_approved_at = models.DateTimeField(
        blank=True, 
        null=True,
        help_text="When bank transfer was approved by logistics provider"
    )
    platform_fee_charged_at = models.DateTimeField(
        blank=True, 
        null=True,
        help_text="When platform fee was successfully charged"
    )
    
    # Booking Cancellation Management fields
    is_paid = models.BooleanField(default=False, db_index=True, help_text="Whether booking payment has been completed")
    payment_due_date = models.DateTimeField(blank=True, null=True, help_text="Payment deadline for bank transfers")
    
    CANCELLATION_STATUS_CHOICES = [
        ('none', 'None'),
        ('requested_by_customer', 'Requested by Customer'),
        ('mutually_cancelled', 'Cancelled by Agreement'),
        ('declined_by_logistics', 'Declined by Logistics'),
        ('cancelled_by_logistics', 'Cancelled by Logistics (Unpaid)'),
    ]
    
    cancellation_status = models.CharField(
        max_length=50, 
        choices=CANCELLATION_STATUS_CHOICES, 
        default='none',
        db_index=True,
        help_text="Current cancellation status"
    )
    cancellation_reason = models.TextField(blank=True, null=True, help_text="Reason for cancellation")
    refund_issued = models.BooleanField(default=False, db_index=True, help_text="Whether refund has been processed")
    penalty_deducted = models.BooleanField(default=False, help_text="Whether cancellation penalty has been deducted")
    cancellation_requested_at = models.DateTimeField(blank=True, null=True, help_text="When cancellation was requested")
    cancellation_processed_at = models.DateTimeField(blank=True, null=True, help_text="When cancellation was processed")
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True, db_index=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['customer', 'status']),
            models.Index(fields=['logistics_provider', 'status']),
            models.Index(fields=['tracking_number']),
            models.Index(fields=['status', 'customs_cleared']),
            models.Index(fields=['origin_country', 'destination_country']),
            models.Index(fields=['booking_date']),
            models.Index(fields=['scheduled_pickup_date']),
            models.Index(fields=['estimated_delivery_date']),
            models.Index(fields=['payment_status', 'is_insured']),
            models.Index(fields=['customs_clearance_required', 'customs_cleared']),
        ]
    
    def __str__(self):
        return f"Shipment {self.tracking_number} - {self.status}"

class ShipmentTracking(models.Model):
    """Detailed tracking information for shipments."""
    
    # Source of the tracking update
    UPDATE_SOURCE_CHOICES = [
        ('API', 'Carrier API'),
        ('MANUAL', 'Manual Update'),
        ('SYSTEM', 'System Generated'),
    ]
    
    shipment = models.ForeignKey(Shipment, on_delete=models.CASCADE, related_name='tracking_details')
    timestamp = models.DateTimeField(auto_now_add=True, db_index=True)
    location_name = models.CharField(max_length=255)
    location_country = models.CharField(max_length=100, blank=True)
    latitude = models.DecimalField(max_digits=9, decimal_places=6, blank=True, null=True)
    longitude = models.DecimalField(max_digits=9, decimal_places=6, blank=True, null=True)
    status = models.CharField(max_length=30, choices=Shipment.TRACKING_STATUS_CHOICES)
    description = models.TextField(blank=True)
    
    # Additional fields for tracking update source and verification
    update_source = models.CharField(max_length=10, choices=UPDATE_SOURCE_CHOICES, default='MANUAL')
    carrier_code = models.CharField(max_length=20, blank=True, help_text="Carrier-specific status code")
    carrier_description = models.TextField(blank=True, help_text="Original status description from carrier")
    is_customer_visible = models.BooleanField(default=True, help_text="Whether this update is visible to customers")
    
    # Who created this tracking update
    updated_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        related_name='tracking_updates'
    )
    
    # IP address for manual updates (audit purposes)
    ip_address = models.GenericIPAddressField(blank=True, null=True)
    
    class Meta:
        ordering = ['-timestamp']
    
    def __str__(self):
        return f"{self.shipment.tracking_number} - {self.timestamp} - {self.location_name}"

class Review(models.Model):
    """Reviews left by customers for logistics providers after shipment."""
    
    shipment = models.OneToOneField(Shipment, on_delete=models.CASCADE, related_name='review')
    customer = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='reviews_given',
        limit_choices_to={'user_type': 'CUSTOMER'}
    )
    logistics_provider = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='reviews_received',
        limit_choices_to={'user_type': 'LOGISTICS'}
    )
    rating = models.PositiveSmallIntegerField(
        choices=[(1, '1 Star'), (2, '2 Stars'), (3, '3 Stars'), (4, '4 Stars'), (5, '5 Stars')]
    )
    comment = models.TextField(blank=True)
    delivery_on_time = models.BooleanField()
    cargo_condition = models.PositiveSmallIntegerField(
        choices=[(1, 'Poor'), (2, 'Fair'), (3, 'Good'), (4, 'Excellent')],
        blank=True, null=True
    )
    created_at = models.DateTimeField(auto_now_add=True)
    
    def __str__(self):
        return f"Review for {self.shipment.tracking_number} - {self.rating} stars"


class TrackingNumberCounter(models.Model):
    """Counter for generating unique sequential tracking numbers."""
    
    date = models.DateField(unique=True, db_index=True)
    counter = models.PositiveIntegerField(default=0)
    
    class Meta:
        indexes = [
            models.Index(fields=['date']),
        ]
    
    @classmethod
    def get_next_serial_number(cls):
        """
        Get the next available tracking number serial in an atomic way.
        Returns an 8-digit padded string (e.g., 00000123).
        """
        today = datetime.date.today()
        
        # Using get_or_create with an atomic transaction to avoid race conditions
        counter, created = cls.objects.get_or_create(
            date=today,
            defaults={'counter': 0}
        )
        
        # Increment the counter atomically
        counter.counter = F('counter') + 1
        counter.save(update_fields=['counter'])
        
        # Refresh from database to get the updated counter value
        counter.refresh_from_db()
        
        # Pad the counter to 8 digits
        return f"{counter.counter:08d}"

    def __str__(self):
        return f"Tracking counter for {self.date}: {self.counter}"


def generate_tracking_number(origin_country_code):
    """
    Generate a tracking number following the LogistiLink format.
    
    Format: LGL + date code (YYMMDD) + 8-digit serial + origin country code
    Example: LGL24051400004523AZ
    
    Args:
        origin_country_code (str): ISO Alpha-2 country code for the origin
        
    Returns:
        str: A unique tracking number
    """
    # Fixed platform prefix
    prefix = "LGL"
    
    # Date code (YYMMDD)
    today = datetime.date.today()
    date_code = today.strftime("%y%m%d")
    
    # Get the next sequential serial number
    serial_number = TrackingNumberCounter.get_next_serial_number()
    
    # Ensure country code is valid (2 uppercase letters)
    country_code = origin_country_code.upper()
    if not re.match(r'^[A-Z]{2}$', country_code):
        country_code = "XX"  # Default if invalid
    
    # Create the tracking number
    tracking_number = f"{prefix}{date_code}{serial_number}{country_code}"
    
    return tracking_number


def validate_tracking_number(tracking_number):
    """
    Validate that a tracking number follows the LogistiLink format.
    
    Args:
        tracking_number (str): The tracking number to validate
        
    Returns:
        bool: True if valid, False otherwise
    """
    # Regular expression pattern for tracking number:
    # LGL + 6 digits (date) + 8 digits (serial) + 2 uppercase letters (country)
    pattern = r'^LGL\d{6}\d{8}[A-Z]{2}$'
    
    return bool(re.match(pattern, tracking_number))


class PrivateShipment(models.Model):
    """
    Private shipment container managed by logistics provider with invitation-only access.
    """
    
    STATUS_CHOICES = [
        ('active', 'Active - Accepting Invitations'),
        ('invitation_closed', 'Invitation Period Closed'),
        ('finalized', 'Container Finalized'),
        ('shipped', 'Shipped'),
        ('delivered', 'Delivered'),
        ('cancelled', 'Cancelled'),
    ]
    
    # Core relationships
    logistics_provider = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='private_shipments_managed',
        limit_choices_to={'user_type': 'LOGISTICS'}
    )
    initiating_customer = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='private_shipments_initiated',
        limit_choices_to={'user_type': 'CUSTOMER'}
    )
    
    # Container details
    container_id = models.CharField(max_length=50, unique=True)
    container_type = models.CharField(max_length=50, default='40ft')
    max_participants = models.PositiveIntegerField(default=5)
    current_participants = models.PositiveIntegerField(default=1)
    
    # Route information
    origin_country = models.CharField(max_length=100)
    origin_city = models.CharField(max_length=100)
    destination_country = models.CharField(max_length=100)
    destination_city = models.CharField(max_length=100)
    
    # Capacity management
    total_capacity_m3 = models.DecimalField(max_digits=10, decimal_places=2, default=67.7)
    total_capacity_kg = models.DecimalField(max_digits=10, decimal_places=2, default=26000)
    used_capacity_m3 = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    used_capacity_kg = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    
    # Timeline
    invitation_deadline = models.DateTimeField()
    container_finalization_deadline = models.DateTimeField()
    scheduled_departure_date = models.DateTimeField()
    estimated_delivery_date = models.DateTimeField()
    
    # Status and control
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active')
    is_private = models.BooleanField(default=True)
    container_finalized = models.BooleanField(default=False)
    all_payments_completed = models.BooleanField(default=False)
    
    # Cost sharing
    base_cost_per_m3 = models.DecimalField(max_digits=10, decimal_places=2)
    shared_fees = models.JSONField(default=list)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    finalized_at = models.DateTimeField(blank=True, null=True)
    
    class Meta:
        ordering = ['-created_at']
        
    def __str__(self):
        return f"Private Container {self.container_id} - {self.logistics_provider.company_name}"
    
    @property
    def capacity_utilization_percentage(self):
        """Calculate percentage of container capacity used"""
        if self.total_capacity_m3 > 0:
            return (self.used_capacity_m3 / self.total_capacity_m3) * 100
        return 0
    
    @property
    def can_accept_invitations(self):
        """Check if container can still accept new participants"""
        from django.utils import timezone
        return (
            self.status == 'active' and 
            not self.container_finalized and
            self.current_participants < self.max_participants and
            timezone.now() < self.invitation_deadline
        )
    
    def get_available_space_m3(self):
        """Get remaining space in cubic meters"""
        return self.total_capacity_m3 - self.used_capacity_m3
    
    def get_available_space_kg(self):
        """Get remaining weight capacity in kilograms"""
        return self.total_capacity_kg - self.used_capacity_kg
    
    def has_accepted_invitations(self):
        """Check if there are any accepted invitations"""
        return self.invitations.filter(status='accepted').exists()
    
    def can_be_finalized(self):
        """Check if container can be finalized"""
        return (
            self.status in ['active', 'invitation_closed'] and
            not self.container_finalized and
            self.current_participants > 1 and
            self.has_accepted_invitations()
        )
    
    def finalize_container(self):
        """Finalize container and create payment requests for all accepted participants"""
        from django.utils import timezone
        from datetime import timedelta
        
        if not self.can_be_finalized():
            raise ValueError("Container cannot be finalized at this time")
        
        # Update container status
        self.container_finalized = True
        self.finalized_at = timezone.now()
        self.status = 'finalized'
        
        # Set payment deadline (3 days from finalization)
        payment_deadline = timezone.now() + timedelta(days=3)
        
        # Get all accepted invitations
        accepted_invitations = self.invitations.filter(status='accepted')
        
        # Create payment requests for each accepted participant
        for invitation in accepted_invitations:
            PrivateShipmentPayment.objects.create(
                private_shipment=self,
                customer=invitation.invited_customer,
                invitation=invitation,
                amount_due=invitation.individual_cost,
                payment_deadline=payment_deadline
            )
        
        self.save()
        
        # Send notifications to all participants
        self._notify_participants_of_finalization()
        
        return True
    
    def _notify_participants_of_finalization(self):
        """Send notifications to all participants about container finalization"""
        from core.notification_models import Notification
        
        # Notify all accepted participants
        accepted_invitations = self.invitations.filter(status='accepted')
        
        for invitation in accepted_invitations:
            Notification.objects.create(
                user=invitation.invited_customer,
                title=f"Container {self.container_id} Finalized - Payment Required",
                message=f"Your container has been finalized! Please make your payment of ${invitation.individual_cost:.2f} within 3 days to secure your shipment.",
                notification_type='payment_required',
                priority='high'
            )
        
        # Notify logistics provider
        Notification.objects.create(
            user=self.logistics_provider,
            title=f"Container {self.container_id} Finalized Successfully",
            message=f"Container finalized with {self.current_participants} participants. Payment requests sent to all customers.",
            notification_type='container_finalized',
            priority='medium'
        )
    
    def check_all_payments_completed(self):
        """Check if all payments are completed and update status accordingly"""
        from django.db.models import Count
        
        total_payments = self.payments.count()
        paid_payments = self.payments.filter(payment_status='paid').count()
        
        if total_payments > 0 and paid_payments == total_payments:
            self.all_payments_completed = True
            self.status = 'shipped'  # Ready for shipping
            self.save()
            
            # Notify logistics provider
            from core.notification_models import Notification
            Notification.objects.create(
                user=self.logistics_provider,
                title=f"All Payments Completed - {self.container_id}",
                message=f"All participants have completed their payments. Container is ready for shipping.",
                notification_type='payments_completed',
                priority='high'
            )
            
            return True
        
        return False
    
    def has_capacity_for_cargo(self, weight_kg=0, volume_m3=0):
        """Check if container has capacity for additional cargo"""
        available_weight = self.get_available_space_kg()
        available_volume = self.get_available_space_m3()
        
        return (weight_kg <= available_weight and volume_m3 <= available_volume)
    
    def get_confirmed_invitations(self):
        """Get all confirmed (accepted, non-waitlisted) invitations"""
        return self.invitations.filter(status='accepted', is_waitlist=False)
    
    def get_waitlisted_invitations(self):
        """Get all waitlisted invitations ordered by invitation date"""
        return self.invitations.filter(is_waitlist=True, status='waitlisted').order_by('invited_at')
    
    def promote_from_waitlist(self, invitation_id=None):
        """Promote next waitlisted customer to active status"""
        if invitation_id:
            # Promote specific invitation
            try:
                invitation = self.invitations.get(id=invitation_id, is_waitlist=True, status='waitlisted')
            except PrivateShipmentInvitation.DoesNotExist:
                return False
        else:
            # Promote next in line
            invitation = self.get_waitlisted_invitations().first()
            
        if not invitation:
            return False
        
        # Check if there's available capacity
        if self.has_capacity_for_cargo():
            invitation.is_waitlist = False
            invitation.status = 'pending'
            invitation.save()
            
            # Send notification to promoted customer
            from core.notification_models import Notification
            Notification.objects.create(
                user=invitation.invited_customer,
                title=f"Promoted from Waitlist - Container {self.container_id}",
                message=f"Great news! A spot has opened in the private shipment. Please confirm your participation and submit your cargo details by {self.invitation_deadline.strftime('%Y-%m-%d')}.",
                notification_type='waitlist_promotion',
                priority='high'
            )
            
            return True
        
        return False
    
    def remove_participant_and_promote(self, invitation):
        """Remove a participant and promote next waitlisted customer"""
        # Update container capacity
        if invitation.volume_m3:
            self.used_capacity_m3 -= invitation.volume_m3
        if invitation.weight_kg:
            self.used_capacity_kg -= invitation.weight_kg
        if invitation.status == 'accepted':
            self.current_participants -= 1
        
        # Remove the invitation
        invitation.delete()
        
        self.save()
        
        # Try to promote from waitlist
        promoted = self.promote_from_waitlist()
        
        return promoted


class QuoteRequest(models.Model):
    """
    Quote requests submitted by customers for specific shipping routes.
    """
    
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('quoted', 'Quote Received'),
        ('accepted', 'Accepted'),
        ('declined', 'Declined'),
        ('expired', 'Expired'),
    ]
    
    # Core relationships
    customer = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='shipment_quote_requests',
        limit_choices_to={'user_type': 'CUSTOMER'}
    )
    logistics_provider = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='received_quote_requests',
        limit_choices_to={'user_type': 'LOGISTICS_PROVIDER'}
    )
    shipping_rate = models.ForeignKey(
        'ShippingRate',
        on_delete=models.CASCADE,
        related_name='quote_requests'
    )
    
    # Quote details
    quote_reference = models.CharField(max_length=50, unique=True, db_index=True)
    cargo_weight = models.DecimalField(max_digits=10, decimal_places=2)
    cargo_dimensions = models.CharField(max_length=200)
    pickup_date = models.DateField()
    delivery_date = models.DateField()
    special_requirements = models.TextField(blank=True)
    
    # Status and response
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    quoted_price = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True)
    provider_response = models.TextField(blank=True)
    estimated_delivery_days = models.PositiveIntegerField(blank=True, null=True)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    expires_at = models.DateTimeField()
    
    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['customer', 'status']),
            models.Index(fields=['logistics_provider', 'status']),
            models.Index(fields=['quote_reference']),
            models.Index(fields=['created_at']),
        ]
    
    def __str__(self):
        return f"Quote {self.quote_reference} - {self.customer.email}"
    
    @property
    def is_expired(self):
        """Check if quote request has expired"""
        from django.utils import timezone
        return timezone.now() > self.expires_at
    
    def generate_quote_reference(self):
        """Generate unique quote reference number"""
        import random
        import string
        
        # Format: QT + route_id + customer_id + random_4_digits
        route_id = self.shipping_rate.route.id if self.shipping_rate and self.shipping_rate.route else 0
        customer_id = self.customer.id
        random_digits = ''.join(random.choices(string.digits, k=4))
        
        return f"QT{route_id:03d}{customer_id:03d}{random_digits}"


class PrivateShipmentInvitation(models.Model):
    """
    Invitation from logistics provider to customer for joining private shipment.
    """
    
    STATUS_CHOICES = [
        ('pending', 'Pending Response'),
        ('accepted', 'Accepted'),
        ('declined', 'Declined'),
        ('removed', 'Removed by Logistics'),
        ('expired', 'Expired'),
        ('waitlisted', 'Waitlisted'),
        ('cancelled', 'Cancelled'),
        ('cancel_pending', 'Cancellation Pending Approval'),
    ]
    
    # Core relationships
    private_shipment = models.ForeignKey(
        PrivateShipment,
        on_delete=models.CASCADE,
        related_name='invitations'
    )
    invited_customer = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='private_shipment_invitations',
        limit_choices_to={'user_type': 'CUSTOMER'}
    )
    logistics_provider = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='private_invitations_sent',
        limit_choices_to={'user_type': 'LOGISTICS'}
    )
    
    # Invitation details
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    invitation_message = models.TextField(blank=True)
    is_waitlist = models.BooleanField(default=False)
    
    # Shipment details (filled when accepted)
    cargo_description = models.TextField(blank=True)
    weight_kg = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True)
    volume_m3 = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True)
    has_hazardous_items = models.BooleanField(default=False)
    special_requirements = models.TextField(blank=True)
    
    # Pricing
    individual_cost = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True)
    cost_savings = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    
    # Timestamps
    invited_at = models.DateTimeField(auto_now_add=True)
    responded_at = models.DateTimeField(blank=True, null=True)
    expires_at = models.DateTimeField()
    
    # Cancellation tracking
    cancel_reason = models.TextField(null=True, blank=True)
    cancelled_by = models.CharField(
        max_length=20,
        choices=[
            ('customer', 'Customer'),
            ('logistics', 'Logistics'),
            ('system', 'System')
        ],
        null=True, blank=True
    )
    cancelled_at = models.DateTimeField(null=True, blank=True)
    penalty_applied = models.BooleanField(default=False)
    penalty_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    refund_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    
    # AI recommendation tracking (Phase 7)
    is_ai_suggested = models.BooleanField(default=False, help_text="Whether this invitation was generated by AI recommendations")
    ai_match_score = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True, help_text="AI match score percentage")
    ai_recommendation_reasons = models.JSONField(null=True, blank=True, help_text="Reasons for AI recommendation")
    
    # Related shipment
    accepted_shipment = models.OneToOneField(
        Shipment,
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='private_invitation'
    )
    
    class Meta:
        unique_together = ['private_shipment', 'invited_customer']
        ordering = ['-invited_at']
        
    def __str__(self):
        return f"Invitation to {self.invited_customer.company_name} for {self.private_shipment.container_id}"
    
    @property
    def is_expired(self):
        """Check if invitation has expired"""
        from django.utils import timezone
        return timezone.now() > self.expires_at
    
    @property
    def can_respond(self):
        """Check if customer can still respond to invitation"""
        return (
            self.status == 'pending' and 
            not self.is_expired and
            self.private_shipment.can_accept_invitations
        )
    
    def accept_invitation(self, cargo_details):
        """Accept invitation and create associated shipment"""
        from django.utils import timezone
        from django.core.exceptions import ValidationError
        
        if not self.can_respond:
            raise ValidationError("Cannot accept this invitation")
        
        # Extract cargo details
        weight_kg = cargo_details.get('weight_kg', 0)
        volume_m3 = cargo_details.get('volume_m3', 0)
        
        # Check if adding this cargo would exceed capacity
        if not self.private_shipment.has_capacity_for_cargo(weight_kg, volume_m3):
            # Move to waitlist instead of accepting
            self.status = 'waitlisted'
            self.is_waitlist = True
            self.responded_at = timezone.now()
            self.cargo_description = cargo_details.get('cargo_description', '')
            self.weight_kg = weight_kg
            self.volume_m3 = volume_m3
            self.has_hazardous_items = cargo_details.get('has_hazardous_items', False)
            self.special_requirements = cargo_details.get('special_requirements', '')
            self.save()
            
            # Notify customer about waitlist status
            from core.notification_models import Notification
            Notification.objects.create(
                user=self.invited_customer,
                title=f"Added to Waitlist - Container {self.private_shipment.container_id}",
                message=f"Thank you for your interest! The container is currently at capacity, but you've been added to the waitlist. You'll be notified immediately if a spot opens.",
                notification_type='waitlist_added',
                priority='medium'
            )
            
            return "waitlisted"
        
        # Update invitation details
        self.status = 'accepted'
        self.responded_at = timezone.now()
        self.cargo_description = cargo_details.get('cargo_description', '')
        self.weight_kg = weight_kg
        self.volume_m3 = volume_m3
        self.has_hazardous_items = cargo_details.get('has_hazardous_items', False)
        self.special_requirements = cargo_details.get('special_requirements', '')
        
        # Calculate individual cost
        volume_percentage = self.volume_m3 / self.private_shipment.total_capacity_m3
        self.individual_cost = self.private_shipment.base_cost_per_m3 * self.volume_m3
        
        # Calculate savings
        estimated_individual_cost = self.individual_cost * 1.3
        self.cost_savings = estimated_individual_cost - self.individual_cost
        
        self.save()
        
        # Update private shipment capacity
        self.private_shipment.used_capacity_m3 += self.volume_m3
        self.private_shipment.used_capacity_kg += self.weight_kg
        self.private_shipment.current_participants += 1
        self.private_shipment.save()
        
        return True
    
    def decline_invitation(self, reason=""):
        """Decline invitation"""
        from django.utils import timezone
        from django.core.exceptions import ValidationError
        
        if not self.can_respond:
            raise ValidationError("Cannot decline this invitation")
        
        self.status = 'declined'
        self.responded_at = timezone.now()
        if reason:
            self.invitation_message += f"\nDeclined reason: {reason}"
        self.save()
        
        return True


class PrivateShipmentPayment(models.Model):
    """
    Payment tracking for private shipment participants after container finalization.
    """
    
    PAYMENT_METHOD_CHOICES = [
        ('stripe', 'Credit/Debit Card (Stripe)'),
        ('bank_transfer', 'Bank Transfer'),
        ('paypal', 'PayPal'),
    ]
    
    PAYMENT_STATUS_CHOICES = [
        ('pending', 'Payment Pending'),
        ('processing', 'Processing'),
        ('paid', 'Paid'),
        ('failed', 'Payment Failed'),
        ('overdue', 'Payment Overdue'),
        ('cancelled', 'Payment Cancelled'),
    ]
    
    # Core relationships
    private_shipment = models.ForeignKey(
        PrivateShipment,
        on_delete=models.CASCADE,
        related_name='payments'
    )
    customer = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='private_shipment_payments',
        limit_choices_to={'user_type': 'CUSTOMER'}
    )
    invitation = models.OneToOneField(
        'PrivateShipmentInvitation',
        on_delete=models.CASCADE,
        related_name='payment'
    )
    
    # Payment details
    amount_due = models.DecimalField(max_digits=10, decimal_places=2)
    payment_method = models.CharField(
        max_length=20, 
        choices=PAYMENT_METHOD_CHOICES,
        blank=True
    )
    payment_status = models.CharField(
        max_length=20, 
        choices=PAYMENT_STATUS_CHOICES, 
        default='pending'
    )
    
    # Payment timeline
    payment_deadline = models.DateTimeField()
    created_at = models.DateTimeField(auto_now_add=True)
    paid_at = models.DateTimeField(null=True, blank=True)
    
    # Payment references
    stripe_payment_intent_id = models.CharField(max_length=255, blank=True, null=True)
    bank_transfer_reference = models.CharField(max_length=255, blank=True, null=True)
    payment_proof_url = models.URLField(blank=True, null=True)
    
    # Additional details
    payment_notes = models.TextField(blank=True)
    late_fee_applied = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    
    class Meta:
        unique_together = ['private_shipment', 'customer']
        indexes = [
            models.Index(fields=['payment_status']),
            models.Index(fields=['payment_deadline']),
            models.Index(fields=['created_at']),
        ]
    
    def __str__(self):
        return f"Payment: {self.customer.company_name} - {self.private_shipment.container_id} (${self.amount_due})"
    
    @property
    def is_overdue(self):
        """Check if payment is overdue"""
        from django.utils import timezone
        return (
            self.payment_status == 'pending' and 
            timezone.now() > self.payment_deadline
        )
    
    @property
    def days_until_deadline(self):
        """Calculate days until payment deadline"""
        from django.utils import timezone
        delta = self.payment_deadline - timezone.now()
        return delta.days if delta.days > 0 else 0
    
    @property
    def total_amount_with_fees(self):
        """Get total amount including any late fees"""
        return self.amount_due + self.late_fee_applied
    
    def mark_as_paid(self, payment_reference=None):
        """Mark payment as completed"""
        from django.utils import timezone
        
        self.payment_status = 'paid'
        self.paid_at = timezone.now()
        
        if payment_reference:
            if self.payment_method == 'stripe':
                self.stripe_payment_intent_id = payment_reference
            elif self.payment_method == 'bank_transfer':
                self.bank_transfer_reference = payment_reference
        
        self.save()
        
        # Check if all payments for the container are complete
        self.private_shipment.check_all_payments_completed()
    
    def mark_as_overdue(self):
        """Mark payment as overdue and apply late fees if applicable"""
        if self.payment_status == 'pending':
            self.payment_status = 'overdue'
            # Apply 5% late fee
            self.late_fee_applied = self.amount_due * 0.05
            self.save()
    
    def cancel_payment(self, reason=""):
        """Cancel payment request"""
        self.payment_status = 'cancelled'
        if reason:
            self.payment_notes += f"\nCancellation reason: {reason}"
        self.save()
    
    def request_cancellation(self, reason="", cancelled_by="customer"):
        """Request cancellation - may require approval if already finalized"""
        from django.utils import timezone
        from core.models import Notification
        
        # Check if shipment is finalized
        if self.private_shipment.status == 'finalized':
            # Require approval for finalized shipments
            self.status = 'cancel_pending'
        else:
            # Allow direct cancellation for non-finalized shipments
            self.status = 'cancelled'
            self.cancelled_at = timezone.now()
        
        self.cancel_reason = reason
        self.cancelled_by = cancelled_by
        self.save()
        
        # Notify relevant parties
        if cancelled_by == 'customer':
            # Notify logistics provider
            Notification.objects.create(
                user=self.private_shipment.logistics_provider,
                title="Cancellation Request",
                message=f"Customer {self.invited_customer.company_name} requested to cancel their spot in {self.private_shipment.container_id}",
                notification_type="cancellation"
            )
        elif cancelled_by == 'logistics':
            # Notify customer
            Notification.objects.create(
                user=self.invited_customer,
                title="Invitation Cancelled",
                message=f"Your invitation for {self.private_shipment.container_id} has been cancelled by the logistics provider",
                notification_type="cancellation"
            )
        
        return self.status == 'cancelled'  # Returns True if cancellation was immediate
    
    def approve_cancellation(self, refund_percentage=90):
        """Approve cancellation request and handle refunds/penalties"""
        from django.utils import timezone
        
        if self.status != 'cancel_pending':
            return False
        
        # Check if payment exists
        try:
            payment = PrivateShipmentPayment.objects.get(invitation=self)
            if payment.payment_status == 'completed':
                # Calculate refund and penalty
                total_paid = payment.amount_due
                penalty_amount = total_paid * (100 - refund_percentage) / 100
                refund_amount = total_paid - penalty_amount
                
                self.penalty_amount = penalty_amount
                self.refund_amount = refund_amount
                self.penalty_applied = penalty_amount > 0
                
                # Update payment status
                payment.payment_status = 'refunded'
                payment.save()
                
        except PrivateShipmentPayment.DoesNotExist:
            pass
        
        # Complete cancellation
        self.status = 'cancelled'
        self.cancelled_at = timezone.now()
        self.save()
        
        # Update container capacity
        self.private_shipment.remove_participant_and_promote(self)
        
        # Notify customer
        from core.models import Notification
        Notification.objects.create(
            user=self.invited_customer,
            title="Cancellation Approved",
            message=f"Your cancellation for {self.private_shipment.container_id} has been approved. "
                   f"Refund amount: ${self.refund_amount:.2f}",
            notification_type="cancellation"
        )
        
        return True
    
    def reject_cancellation(self, reason=""):
        """Reject cancellation request"""
        if self.status != 'cancel_pending':
            return False
        
        self.status = 'accepted'  # Return to accepted status
        self.cancel_reason = None
        self.cancelled_by = None
        self.save()
        
        # Notify customer
        from core.models import Notification
        Notification.objects.create(
            user=self.invited_customer,
            title="Cancellation Rejected",
            message=f"Your cancellation request for {self.private_shipment.container_id} was rejected. "
                   f"Reason: {reason}" if reason else f"Your cancellation request for {self.private_shipment.container_id} was rejected.",
            notification_type="cancellation"
        )
        
        return True


class QuoteBatch(models.Model):
    """
    Quote batch tracking for instant multi-modal quoting system
    Core component for Freightos-level instant quote aggregation
    """
    
    STATUS_CHOICES = [
        ('processing', 'Processing Quotes'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('timeout', 'Timeout'),
    ]
    
    # Customer information
    customer = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='shipment_quote_batches',
        limit_choices_to={'user_type': 'CUSTOMER'}
    )
    
    # Route information
    origin_country = models.CharField(max_length=100, db_index=True)
    origin_city = models.CharField(max_length=100, db_index=True)
    destination_country = models.CharField(max_length=100, db_index=True)
    destination_city = models.CharField(max_length=100, db_index=True)
    
    # Cargo details
    cargo_weight = models.DecimalField(max_digits=10, decimal_places=2)
    cargo_type = models.CharField(max_length=50, default='general')
    transport_mode = models.CharField(max_length=20, default='truck')
    urgency = models.CharField(max_length=20, default='standard')
    
    # Quote processing status
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='processing', db_index=True)
    total_quotes = models.PositiveIntegerField(default=0)
    successful_quotes = models.PositiveIntegerField(default=0)
    response_time_seconds = models.DecimalField(max_digits=8, decimal_places=3, blank=True, null=True)
    
    # Quote data storage
    quote_data = models.JSONField(default=list, help_text="Normalized quote responses from all providers")
    market_insights = models.JSONField(default=dict, help_text="Market analysis and recommendations")
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True, db_index=True)
    completed_at = models.DateTimeField(blank=True, null=True)
    
    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['customer', 'status']),
            models.Index(fields=['origin_country', 'destination_country']),
            models.Index(fields=['created_at']),
            models.Index(fields=['status', 'created_at']),
        ]
    
    def __str__(self):
        return f"Quote Batch QB{self.id:08d} - {self.customer.email}"
    
    @property
    def batch_id(self):
        """Generate formatted batch ID"""
        return f"QB{self.id:08d}"
    
    @property
    def success_rate(self):
        """Calculate quote success rate"""
        if self.total_quotes == 0:
            return 0
        return (self.successful_quotes / self.total_quotes) * 100
    
    @property
    def route_summary(self):
        """Get route summary for display"""
        return f"{self.origin_city}, {self.origin_country} → {self.destination_city}, {self.destination_country}"
    
    def get_quotes(self):
        """Get parsed quote data"""
        if isinstance(self.quote_data, list):
            return self.quote_data
        elif isinstance(self.quote_data, str):
            try:
                return json.loads(self.quote_data)
            except (json.JSONDecodeError, TypeError):
                return []
        return []
    
    def get_successful_quotes(self):
        """Get only successful quotes"""
        quotes = self.get_quotes()
        return [q for q in quotes if q.get('status') == 'success' and q.get('total_price')]
    
    def get_lowest_quote(self):
        """Get quote with lowest price"""
        successful_quotes = self.get_successful_quotes()
        if not successful_quotes:
            return None
        return min(successful_quotes, key=lambda x: x.get('total_price', float('inf')))
    
    def get_highest_rated_quote(self):
        """Get quote from highest rated provider"""
        successful_quotes = self.get_successful_quotes()
        if not successful_quotes:
            return None
        return max(successful_quotes, key=lambda x: x.get('provider_rating', 0))
    
    def get_fastest_quote(self):
        """Get quote with fastest delivery"""
        successful_quotes = self.get_successful_quotes()
        if not successful_quotes:
            return None
        return min(successful_quotes, key=lambda x: x.get('estimated_delivery_days', 999))
    
    def get_market_insights(self):
        """Get parsed market insights data"""
        if isinstance(self.market_insights, dict):
            return self.market_insights
        elif isinstance(self.market_insights, str):
            try:
                return json.loads(self.market_insights)
            except (json.JSONDecodeError, TypeError):
                return {}
        return {}
    
    def mark_completed(self, quotes_data, insights_data=None):
        """Mark batch as completed with quote results"""
        from django.utils import timezone
        
        self.status = 'completed'
        self.completed_at = timezone.now()
        self.quote_data = quotes_data if isinstance(quotes_data, list) else []
        self.market_insights = insights_data if insights_data else {}
        self.total_quotes = len(self.quote_data)
        self.successful_quotes = len([q for q in self.quote_data if q.get('status') == 'success'])
        self.save()
    
    def mark_failed(self, error_message):
        """Mark batch as failed"""
        from django.utils import timezone
        
        self.status = 'failed'
        self.completed_at = timezone.now()
        self.market_insights = {'error': error_message}
        self.save()
