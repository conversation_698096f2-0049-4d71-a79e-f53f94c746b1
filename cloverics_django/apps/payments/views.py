# Payment views - extracted from fastapi_main.py
from django.shortcuts import render, redirect, get_object_or_404
from django.http import JsonResponse, HttpResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.conf import settings
import json
import stripe
from datetime import datetime
from .models import Payment, Contract, Insurance

# Configure Stripe
stripe.api_key = getattr(settings, 'STRIPE_SECRET_KEY', 'sk_test_placeholder')

@login_required
def payment_checkout(request):
    """Payment checkout page"""
    context = {
        'stripe_public_key': getattr(settings, 'STRIPE_PUBLIC_KEY', 'pk_test_placeholder'),
        'page_title': 'Payment Checkout'
    }
    return render(request, 'payments/checkout.html', context)

@csrf_exempt
@require_http_methods(["POST"])
def process_payment(request):
    """Process payment submission"""
    try:
        data = json.loads(request.body)
        payment_method = data.get('payment_method', 'stripe')
        amount = float(data.get('amount', 0))
        
        if payment_method == 'stripe':
            # Create payment intent
            intent = stripe.PaymentIntent.create(
                amount=int(amount * 100),  # Stripe uses cents
                currency='usd',
                automatic_payment_methods={
                    'enabled': True,
                },
            )
            
            return JsonResponse({
                'success': True,
                'client_secret': intent.client_secret,
                'payment_method': 'stripe'
            })
        
        elif payment_method == 'bank_transfer':
            # Handle bank transfer
            return JsonResponse({
                'success': True,
                'payment_method': 'bank_transfer',
                'instructions': 'Bank transfer instructions will be provided'
            })
        
        else:
            return JsonResponse({
                'success': False,
                'error': 'Invalid payment method'
            }, status=400)
            
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)

@login_required
def payment_confirmation(request):
    """Payment confirmation page"""
    return render(request, 'payments/confirmation.html')

@csrf_exempt
@require_http_methods(["POST"])
def create_payment_intent(request):
    """Create Stripe payment intent"""
    try:
        data = json.loads(request.body)
        amount = int(float(data.get('amount', 0)) * 100)  # Convert to cents
        
        intent = stripe.PaymentIntent.create(
            amount=amount,
            currency='usd',
            automatic_payment_methods={
                'enabled': True,
            },
        )
        
        return JsonResponse({
            'client_secret': intent.client_secret
        })
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)

@csrf_exempt
@require_http_methods(["POST"])
def stripe_webhook(request):
    """Handle Stripe webhooks"""
    payload = request.body
    sig_header = request.META.get('HTTP_STRIPE_SIGNATURE')
    endpoint_secret = getattr(settings, 'STRIPE_WEBHOOK_SECRET', '')
    
    try:
        event = stripe.Webhook.construct_event(
            payload, sig_header, endpoint_secret
        )
    except ValueError:
        return HttpResponse(status=400)
    except stripe.error.SignatureVerificationError:
        return HttpResponse(status=400)
    
    # Handle the event
    if event['type'] == 'payment_intent.succeeded':
        payment_intent = event['data']['object']
        # Handle successful payment
        pass
    
    return HttpResponse(status=200)

@login_required
def stripe_success(request):
    """Stripe payment success page"""
    return render(request, 'payments/stripe_success.html')

@login_required
def stripe_cancel(request):
    """Stripe payment cancel page"""
    return render(request, 'payments/stripe_cancel.html')

@login_required
def bank_transfer(request):
    """Bank transfer payment option"""
    return render(request, 'payments/bank_transfer.html')

@csrf_exempt
@require_http_methods(["POST"])
def verify_bank_transfer(request):
    """Verify bank transfer payment"""
    return JsonResponse({
        'success': True,
        'message': 'Bank transfer verification - implementation in progress'
    })

@csrf_exempt
@require_http_methods(["POST"])
def upload_bank_receipt(request):
    """Upload bank transfer receipt"""
    return JsonResponse({
        'success': True,
        'message': 'Bank receipt upload - implementation in progress'
    })

@login_required
def payment_history(request):
    """Payment history page"""
    payments = Payment.objects.filter(customer=request.user).order_by('-created_at')
    return render(request, 'payments/history.html', {'payments': payments})

@login_required
def payment_invoice(request, payment_id):
    """Generate payment invoice"""
    payment = get_object_or_404(Payment, id=payment_id, customer=request.user)
    return render(request, 'payments/invoice.html', {'payment': payment})

@login_required
def payment_receipt(request, payment_id):
    """Generate payment receipt"""
    payment = get_object_or_404(Payment, id=payment_id, customer=request.user)
    return render(request, 'payments/receipt.html', {'payment': payment})

@login_required
def contract_list(request):
    """List user contracts"""
    contracts = Contract.objects.filter(customer=request.user).order_by('-created_at')
    return render(request, 'payments/contracts.html', {'contracts': contracts})

@login_required
def contract_detail(request, contract_id):
    """Contract detail page"""
    contract = get_object_or_404(Contract, id=contract_id, customer=request.user)
    return render(request, 'payments/contract_detail.html', {'contract': contract})

@csrf_exempt
@require_http_methods(["POST"])
def sign_contract(request, contract_id):
    """Sign contract digitally"""
    contract = get_object_or_404(Contract, id=contract_id, customer=request.user)
    # Digital signature logic here
    return JsonResponse({
        'success': True,
        'message': 'Contract signed successfully'
    })

@login_required
def download_contract(request, contract_id):
    """Download contract PDF"""
    contract = get_object_or_404(Contract, id=contract_id, customer=request.user)
    # PDF generation logic here
    return HttpResponse("Contract PDF download - implementation in progress")

@login_required
def insurance_options(request):
    """Insurance options page"""
    return render(request, 'payments/insurance_options.html')

@csrf_exempt
@require_http_methods(["POST"])
def insurance_quote(request):
    """Get insurance quote"""
    return JsonResponse({
        'quote': 'Insurance quote - implementation in progress'
    })

@csrf_exempt
@require_http_methods(["POST"])
def purchase_insurance(request):
    """Purchase insurance"""
    return JsonResponse({
        'success': True,
        'message': 'Insurance purchase - implementation in progress'
    })

@login_required
def insurance_claims(request):
    """Insurance claims page"""
    return render(request, 'payments/insurance_claims.html')

@login_required
def insurance_claim_detail(request, claim_id):
    """Insurance claim detail"""
    return render(request, 'payments/insurance_claim_detail.html')

@csrf_exempt
@require_http_methods(["POST"])
def request_refund(request):
    """Request payment refund"""
    return JsonResponse({
        'success': True,
        'message': 'Refund request - implementation in progress'
    })

@login_required
def refund_status(request, refund_id):
    """Check refund status"""
    return render(request, 'payments/refund_status.html')

@csrf_exempt
@require_http_methods(["POST"])
def payment_dispute(request, payment_id):
    """File payment dispute"""
    return JsonResponse({
        'success': True,
        'message': 'Payment dispute - implementation in progress'
    })

@login_required
def revenue_report(request):
    """Revenue report page"""
    return render(request, 'payments/revenue_report.html')

@login_required
def fees_report(request):
    """Fees report page"""
    return render(request, 'payments/fees_report.html')

@login_required
def export_financial_report(request):
    """Export financial report"""
    return HttpResponse("Financial report export - implementation in progress")