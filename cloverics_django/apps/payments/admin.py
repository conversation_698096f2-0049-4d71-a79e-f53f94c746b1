from django.contrib import admin
from .models import Contract, Insurance, Payment, CustomsDeclaration

@admin.register(Contract)
class ContractAdmin(admin.ModelAdmin):
    list_display = ('contract_number', 'customer', 'logistics_provider', 'shipment', 'is_active', 'created_at')
    list_filter = ('is_active', 'terms_accepted_by_customer', 'terms_accepted_by_provider')
    search_fields = ('contract_number', 'title', 'customer__company_name', 'logistics_provider__company_name', 'shipment__tracking_number')
    readonly_fields = ('created_at', 'updated_at')

@admin.register(Insurance)
class InsuranceAdmin(admin.ModelAdmin):
    list_display = ('policy_number', 'shipment', 'insurance_provider', 'coverage_amount', 'premium_amount', 'status', 'start_date', 'end_date')
    list_filter = ('status', 'terms_accepted')
    search_fields = ('policy_number', 'shipment__tracking_number', 'insurance_provider__company_name')
    readonly_fields = ('created_at', 'updated_at')

@admin.register(Payment)
class PaymentAdmin(admin.ModelAdmin):
    list_display = ('invoice_number', 'shipment', 'payment_type', 'amount', 'currency', 'status', 'payment_method', 'created_at')
    list_filter = ('status', 'payment_type', 'payment_method', 'currency')
    search_fields = ('invoice_number', 'transaction_id', 'shipment__tracking_number', 'payer__company_name', 'payee__company_name')
    readonly_fields = ('created_at', 'updated_at', 'platform_fee')

@admin.register(CustomsDeclaration)
class CustomsDeclarationAdmin(admin.ModelAdmin):
    list_display = ('declaration_number', 'shipment', 'status', 'country_of_origin', 'goods_value', 'duties_amount', 'taxes_amount', 'submitted_date')
    list_filter = ('status', 'country_of_origin')
    search_fields = ('declaration_number', 'shipment__tracking_number', 'customs_authority__company_name', 'goods_description')
    readonly_fields = ('created_at', 'updated_at')