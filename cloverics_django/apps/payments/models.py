from django.db import models
from django.conf import settings
from django.utils.translation import gettext_lazy as _

class Contract(models.Model):
    """Contract model for shipment agreements between customers and logistics providers."""
    
    customer = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='customer_contracts',
        limit_choices_to={'user_type': 'CUSTOMER'},
        db_index=True
    )
    logistics_provider = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='provider_contracts',
        limit_choices_to={'user_type': 'LOGISTICS'},
        db_index=True
    )
    shipment = models.OneToOneField(
        'shipments.Shipment',
        on_delete=models.CASCADE,
        related_name='contract',
        db_index=True
    )
    contract_number = models.CharField(max_length=50, unique=True, db_index=True)
    title = models.CharField(max_length=255)
    content = models.TextField()

    terms_accepted_by_customer = models.<PERSON>olean<PERSON>ield(default=False, db_index=True)
    terms_accepted_by_provider = models.BooleanField(default=False, db_index=True)
    customer_signature_date = models.DateTimeField(blank=True, null=True)
    provider_signature_date = models.DateTimeField(blank=True, null=True)
    contract_file = models.CharField(max_length=255, blank=True)  # Path to stored contract file
    is_active = models.BooleanField(default=True, db_index=True)
    created_at = models.DateTimeField(auto_now_add=True, db_index=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['customer', 'is_active']),
            models.Index(fields=['logistics_provider', 'is_active']),
            models.Index(fields=['terms_accepted_by_customer', 'terms_accepted_by_provider']),
            models.Index(fields=['created_at']),
        ]
    
    def __str__(self):
        return f"Contract {self.contract_number} - {self.shipment.tracking_number}"
    
    @property
    def is_signed(self):
        """Check if the contract is signed by both parties."""
        return self.terms_accepted_by_customer and self.terms_accepted_by_provider

class Insurance(models.Model):
    """Insurance coverage for shipments."""
    
    STATUS_CHOICES = [
        ('PENDING', 'Pending'),
        ('ACTIVE', 'Active'),
        ('CLAIMED', 'Claimed'),
        ('EXPIRED', 'Expired'),
        ('CANCELLED', 'Cancelled'),
    ]
    
    shipment = models.OneToOneField(
        'shipments.Shipment',
        on_delete=models.CASCADE,
        related_name='insurance',
        db_index=True
    )
    insurance_provider = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='provided_insurances',
        limit_choices_to={'user_type': 'INSURANCE'},
        db_index=True
    )
    policy_number = models.CharField(max_length=50, unique=True, db_index=True)
    coverage_amount = models.DecimalField(max_digits=12, decimal_places=2)
    premium_amount = models.DecimalField(max_digits=10, decimal_places=2)
    deductible_amount = models.DecimalField(max_digits=10, decimal_places=2)
    start_date = models.DateTimeField(db_index=True)
    end_date = models.DateTimeField(db_index=True)
    policy_document = models.CharField(max_length=255, blank=True)  # Path to stored policy document
    terms_conditions = models.TextField(blank=True, default="Standard insurance terms and conditions apply.")
    notes = models.TextField(blank=True, help_text="Internal notes for this insurance policy")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='PENDING', db_index=True)
    terms_accepted = models.BooleanField(default=False, db_index=True)
    created_at = models.DateTimeField(auto_now_add=True, db_index=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['insurance_provider', 'status']),
            models.Index(fields=['policy_number']),
            models.Index(fields=['start_date', 'end_date']),
            models.Index(fields=['status', 'terms_accepted']),
            models.Index(fields=['created_at']),
        ]
    
    def __str__(self):
        return f"Insurance {self.policy_number} - {self.shipment.tracking_number}"

class Payment(models.Model):
    """Payment model for tracking payments for shipments."""
    
    STATUS_CHOICES = [
        ('PENDING', 'Pending'),
        ('PROCESSING', 'Processing'),
        ('COMPLETED', 'Completed'),
        ('FAILED', 'Failed'),
        ('REFUNDED', 'Refunded'),
        ('AWAITING_PROOF', 'Awaiting Payment Proof'),
        ('PROOF_SUBMITTED', 'Payment Proof Submitted'),
        ('PROOF_REJECTED', 'Payment Proof Rejected'),
        ('PROOF_VERIFIED', 'Payment Proof Verified'),
    ]
    
    TYPE_CHOICES = [
        ('SHIPMENT', 'Shipment Payment'),
        ('INSURANCE', 'Insurance Payment'),
        ('CUSTOMS', 'Customs Fee'),
        ('ADDITIONAL', 'Additional Fee'),
        ('COMMISSION', 'Logistics Commission'),
        ('INSURANCE_COMMISSION', 'Insurance Commission'),
        ('PREMIUM_SUBSCRIPTION', 'Premium Feature Subscription'),
        ('DEPOSIT', 'Security Deposit'),
    ]
    
    METHOD_CHOICES = [
        ('CREDIT_CARD', 'Credit/Debit Card'),
        ('BANK_TRANSFER', 'Bank Transfer'),
        ('PAYPAL', 'PayPal'),
        ('STRIPE', 'Stripe'),
        ('DEPOSIT_BALANCE', 'Deposit Balance'),
        ('PREPAID_COMMISSION', 'Prepaid Commission Credit'),
    ]
    
    COMMISSION_MODEL_CHOICES = [
        ('PER_ORDER', 'Per-Order Automatic Payment'),
        ('MONTHLY', 'Monthly Invoice'),
    ]
    
    shipment = models.ForeignKey(
        'shipments.Shipment',
        on_delete=models.CASCADE,
        related_name='payments',
        db_index=True,
        null=True,  # Allow null for subscription payments not tied to a shipment
        blank=True
    )
    payer = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='payments_made',
        db_index=True
    )
    payee = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='payments_received',
        db_index=True
    )
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    currency = models.CharField(max_length=3, default='USD')
    payment_type = models.CharField(max_length=30, choices=TYPE_CHOICES, db_index=True)
    payment_method = models.CharField(max_length=30, choices=METHOD_CHOICES, db_index=True)
    transaction_id = models.CharField(max_length=100, unique=True, blank=True, null=True, db_index=True)
    platform_fee = models.DecimalField(max_digits=10, decimal_places=2)  # 0.1% of amount
    insurance_commission = models.DecimalField(max_digits=10, decimal_places=2, default=0)  # 1% for insurance
    status = models.CharField(max_length=30, choices=STATUS_CHOICES, default='PENDING', db_index=True)
    payment_date = models.DateTimeField(blank=True, null=True, db_index=True)
    invoice_number = models.CharField(max_length=50, unique=True, db_index=True)
    payment_proof = models.CharField(max_length=255, blank=True, null=True)  # Path to uploaded payment proof
    proof_submitted_date = models.DateTimeField(blank=True, null=True)
    proof_verified_date = models.DateTimeField(blank=True, null=True)
    verified_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        related_name='verified_payments',
        null=True,
        blank=True
    )
    commission_model = models.CharField(max_length=20, choices=COMMISSION_MODEL_CHOICES, blank=True, null=True)
    stripe_payment_intent_id = models.CharField(max_length=100, blank=True, null=True)
    stripe_checkout_session_id = models.CharField(max_length=100, blank=True, null=True)
    bank_details = models.JSONField(default=dict, blank=True, null=True)  # Store bank account details for transfers
    note = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True, db_index=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['shipment', 'status']),
            models.Index(fields=['payer', 'status']),
            models.Index(fields=['payee', 'status']),
            models.Index(fields=['payment_type', 'payment_method']),
            models.Index(fields=['status', 'payment_date']),
            models.Index(fields=['created_at']),
            models.Index(fields=['invoice_number']),
        ]
    
    def __str__(self):
        return f"Payment {self.invoice_number} - {self.amount} {self.currency} - {self.status}"
    
    def save(self, *args, **kwargs):
        """Override save to calculate platform fee if not set."""
        if self.amount and not self.platform_fee:
            # Calculate platform fee as 0.1% of the amount
            from decimal import Decimal
            self.platform_fee = round(self.amount * Decimal('0.001'), 2)
        super().save(*args, **kwargs)

class CustomsDeclaration(models.Model):
    """Customs declaration for international shipments."""
    
    STATUS_CHOICES = [
        ('PENDING', 'Pending'),
        ('SUBMITTED', 'Submitted'),
        ('APPROVED', 'Approved'),
        ('INSPECTION', 'Requires Inspection'),
        ('REJECTED', 'Rejected'),
    ]
    
    shipment = models.OneToOneField(
        'shipments.Shipment',
        on_delete=models.CASCADE,
        related_name='customs_declaration',
        db_index=True
    )
    declaration_number = models.CharField(max_length=50, unique=True, db_index=True)
    customs_authority = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        related_name='processed_declarations',
        limit_choices_to={'user_type': 'CUSTOMS'},
        null=True,
        db_index=True
    )
    declaration_type = models.CharField(max_length=50, db_index=True)
    goods_description = models.TextField()
    goods_value = models.DecimalField(max_digits=12, decimal_places=2)
    country_of_origin = models.CharField(max_length=100, db_index=True)
    tariff_codes = models.JSONField(default=list)  # List of tariff codes for items
    duties_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    taxes_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='PENDING', db_index=True)
    submitted_date = models.DateTimeField(blank=True, null=True, db_index=True)
    processed_date = models.DateTimeField(blank=True, null=True, db_index=True)
    documents = models.JSONField(default=list)  # List of document references
    notes = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True, db_index=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['shipment', 'status']),
            models.Index(fields=['customs_authority', 'status']),
            models.Index(fields=['declaration_number']),
            models.Index(fields=['declaration_type']),
            models.Index(fields=['country_of_origin']),
            models.Index(fields=['status', 'submitted_date']),
            models.Index(fields=['created_at']),
        ]
    
    def __str__(self):
        return f"Customs Declaration {self.declaration_number} - {self.status}"

class UserSubscription(models.Model):
    """Track premium feature subscriptions for users"""
    
    STATUS_CHOICES = [
        ('TRIAL', 'Free Trial'),
        ('ACTIVE', 'Active Subscription'),
        ('CANCELLED', 'Cancelled'),
        ('EXPIRED', 'Expired'),
    ]
    
    PLAN_CHOICES = [
        ('BASIC', 'Basic (Free)'),
        ('STANDARD', 'Standard Premium'),
        ('PROFESSIONAL', 'Professional'),
        ('ENTERPRISE', 'Enterprise'),
    ]
    
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='subscriptions',
        db_index=True
    )
    plan_type = models.CharField(max_length=20, choices=PLAN_CHOICES, default='BASIC', db_index=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='TRIAL', db_index=True)
    trial_start_date = models.DateTimeField(auto_now_add=True, db_index=True)
    trial_end_date = models.DateTimeField(blank=True, null=True, db_index=True)
    subscription_start_date = models.DateTimeField(blank=True, null=True, db_index=True) 
    subscription_end_date = models.DateTimeField(blank=True, null=True, db_index=True)
    auto_renew = models.BooleanField(default=False)
    payment_method_id = models.CharField(max_length=100, blank=True, null=True)  # Reference to stored payment method
    features = models.JSONField(default=dict, blank=True, null=True)  # Enabled features
    created_at = models.DateTimeField(auto_now_add=True, db_index=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'status']),
            models.Index(fields=['plan_type']),
            models.Index(fields=['trial_end_date']),
            models.Index(fields=['subscription_end_date']),
            models.Index(fields=['created_at']),
        ]
    
    def __str__(self):
        return f"{self.user.company_name} - {self.get_plan_type_display()} - {self.get_status_display()}"
    
    def save(self, *args, **kwargs):
        """Set trial_end_date to 3 months after trial_start_date if not set"""
        if not self.trial_end_date and self.trial_start_date:
            from datetime import timedelta
            self.trial_end_date = self.trial_start_date + timedelta(days=90)  # 3 months trial
        super().save(*args, **kwargs)
    
    @property
    def is_trial(self):
        """Check if subscription is in trial period"""
        return self.status == 'TRIAL'
    
    @property
    def is_active(self):
        """Check if subscription is active"""
        return self.status in ['TRIAL', 'ACTIVE']

class LogisticsCompanySettings(models.Model):
    """Track logistics company commission settings and payment preferences"""
    
    logistics_company = models.OneToOneField(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='commission_settings',
        limit_choices_to={'user_type': 'LOGISTICS'},
        db_index=True
    )
    commission_model = models.CharField(
        max_length=20, 
        choices=Payment.COMMISSION_MODEL_CHOICES,
        default='PER_ORDER',
        db_index=True
    )
    deposit_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    deposit_paid = models.BooleanField(default=False, db_index=True)
    deposit_pending = models.BooleanField(default=False, db_index=True)
    deposit_payment_date = models.DateTimeField(blank=True, null=True)
    deposit_invoice_number = models.CharField(max_length=50, blank=True, null=True)
    has_payment_method = models.BooleanField(default=False, db_index=True)
    payment_method_id = models.CharField(max_length=100, blank=True, null=True)
    payment_method_last4 = models.CharField(max_length=4, blank=True, null=True)
    payment_method_brand = models.CharField(max_length=20, blank=True, null=True)
    monthly_invoice_day = models.IntegerField(default=1)  # Day of month to generate invoice
    trusted_lc_status = models.BooleanField(default=False, db_index=True)  # After 6 months good standing
    trusted_lc_date = models.DateTimeField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True, db_index=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name_plural = "Logistics company settings"
        indexes = [
            models.Index(fields=['logistics_company', 'commission_model']),
            models.Index(fields=['deposit_paid']),
            models.Index(fields=['trusted_lc_status']),
            models.Index(fields=['created_at']),
        ]
    
    def __str__(self):
        return f"{self.logistics_company.company_name} - Commission Settings"

class AdvancedPayment(models.Model):
    """Advanced payment processing model for hybrid Stripe + Bank Transfer system"""
    
    PAYMENT_TYPE_CHOICES = [
        ('STRIPE', 'Stripe Card Payment'),
        ('BANK_TRANSFER', 'Bank Transfer'),
        ('HYBRID', 'Hybrid Payment'),
    ]
    
    STATUS_CHOICES = [
        ('PENDING', 'Pending'),
        ('PROCESSING', 'Processing'),
        ('COMPLETED', 'Completed'),
        ('FAILED', 'Failed'),
        ('CANCELLED', 'Cancelled'),
        ('REQUIRES_APPROVAL', 'Requires Approval'),
    ]
    
    shipment = models.ForeignKey(
        'shipments.Shipment',
        on_delete=models.CASCADE,
        related_name='advanced_payments',
        db_index=True
    )
    payment_id = models.CharField(max_length=100, unique=True, db_index=True)
    payment_type = models.CharField(max_length=20, choices=PAYMENT_TYPE_CHOICES, db_index=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='PENDING', db_index=True)
    amount = models.DecimalField(max_digits=12, decimal_places=2)
    currency = models.CharField(max_length=3, default='USD')
    
    # Stripe-specific fields
    stripe_payment_intent_id = models.CharField(max_length=200, blank=True, null=True)
    stripe_charge_id = models.CharField(max_length=200, blank=True, null=True)
    
    # Bank transfer-specific fields
    bank_reference_number = models.CharField(max_length=100, blank=True, null=True)
    bank_approval_required = models.BooleanField(default=False, db_index=True)
    bank_approved_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='approved_bank_transfers'
    )
    bank_approved_at = models.DateTimeField(null=True, blank=True)
    bank_proof_document = models.CharField(max_length=255, blank=True, null=True)
    
    # Payment metadata
    platform_fee = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    net_amount = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True)
    processing_fee = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True, db_index=True)
    updated_at = models.DateTimeField(auto_now=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['shipment', 'status']),
            models.Index(fields=['payment_type', 'status']),
            models.Index(fields=['bank_approval_required']),
            models.Index(fields=['created_at']),
        ]
    
    def __str__(self):
        return f"Advanced Payment {self.payment_id} - {self.amount} {self.currency} - {self.status}"

class LogisticsWallet(models.Model):
    """Wallet system for logistics providers to manage earnings and payments"""
    
    logistics_provider = models.OneToOneField(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='logistics_wallet',
        limit_choices_to={'user_type': 'LOGISTICS'},
        db_index=True
    )
    balance = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    reserved_amount = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    total_earned = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    total_withdrawn = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    
    # Bank account information for withdrawals
    bank_account_number = models.CharField(max_length=50, blank=True, null=True)
    bank_routing_number = models.CharField(max_length=20, blank=True, null=True)
    bank_name = models.CharField(max_length=100, blank=True, null=True)
    account_holder_name = models.CharField(max_length=100, blank=True, null=True)
    
    # Wallet status
    is_active = models.BooleanField(default=True, db_index=True)
    is_verified = models.BooleanField(default=False, db_index=True)
    verification_date = models.DateTimeField(null=True, blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True, db_index=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['logistics_provider', 'is_active']),
            models.Index(fields=['is_verified']),
            models.Index(fields=['created_at']),
        ]
    
    def __str__(self):
        return f"{self.logistics_provider.company_name} Wallet - Balance: {self.balance}"

class BankTransferApproval(models.Model):
    """Model to track bank transfer approvals by logistics providers"""
    
    STATUS_CHOICES = [
        ('PENDING', 'Pending Review'),
        ('APPROVED', 'Approved'),
        ('REJECTED', 'Rejected'),
        ('REQUIRES_INFO', 'Requires Additional Information'),
    ]
    
    advanced_payment = models.OneToOneField(
        AdvancedPayment,
        on_delete=models.CASCADE,
        related_name='bank_approval',
        db_index=True
    )
    logistics_provider = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='bank_transfer_approvals',
        limit_choices_to={'user_type': 'LOGISTICS'},
        db_index=True
    )
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='PENDING', db_index=True)
    proof_document_path = models.CharField(max_length=255, blank=True, null=True)
    transfer_reference = models.CharField(max_length=100, blank=True, null=True)
    notes = models.TextField(blank=True, null=True)
    
    # Approval details
    reviewed_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='reviewed_bank_transfers'
    )
    reviewed_at = models.DateTimeField(null=True, blank=True)
    rejection_reason = models.TextField(blank=True, null=True)
    
    created_at = models.DateTimeField(auto_now_add=True, db_index=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['logistics_provider', 'status']),
            models.Index(fields=['status', 'created_at']),
            models.Index(fields=['reviewed_at']),
        ]
    
    def __str__(self):
        return f"Bank Transfer Approval - {self.advanced_payment.payment_id} - {self.status}"

class InsuranceClaim(models.Model):
    """Insurance claim model for tracking claim submissions and processing"""
    
    STATUS_CHOICES = [
        ('SUBMITTED', 'Submitted'),
        ('UNDER_REVIEW', 'Under Review'),
        ('APPROVED', 'Approved'),
        ('REJECTED', 'Rejected'),
        ('SETTLED', 'Settled'),
        ('CLOSED', 'Closed'),
    ]
    
    CLAIM_TYPE_CHOICES = [
        ('DAMAGE', 'Cargo Damage'),
        ('LOSS', 'Cargo Loss'),
        ('THEFT', 'Theft'),
        ('DELAY', 'Delivery Delay'),
        ('WATER_DAMAGE', 'Water Damage'),
        ('FIRE_DAMAGE', 'Fire Damage'),
        ('ACCIDENT', 'Accident'),
        ('OTHER', 'Other'),
    ]
    
    shipment = models.ForeignKey(
        'shipments.Shipment',
        on_delete=models.CASCADE,
        related_name='insurance_claims',
        db_index=True
    )
    insurance_provider = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='handled_claims',
        limit_choices_to={'user_type': 'INSURANCE'},
        db_index=True
    )
    customer = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='submitted_claims',
        limit_choices_to={'user_type': 'CUSTOMER'},
        db_index=True
    )
    claim_number = models.CharField(max_length=50, unique=True, db_index=True)
    claim_type = models.CharField(max_length=20, choices=CLAIM_TYPE_CHOICES, db_index=True)
    claim_amount = models.DecimalField(max_digits=12, decimal_places=2, db_index=True)
    submitted_date = models.DateTimeField(auto_now_add=True, db_index=True)
    processed_date = models.DateTimeField(blank=True, null=True, db_index=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='SUBMITTED', db_index=True)
    denial_reason = models.TextField(blank=True, null=True)
    settlement_amount = models.DecimalField(max_digits=12, decimal_places=2, blank=True, null=True)
    documents = models.JSONField(default=list, blank=True)  # List of document file paths
    notes = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True, db_index=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'insurance_claims'
        ordering = ['-submitted_date']
        indexes = [
            models.Index(fields=['status', '-submitted_date']),
            models.Index(fields=['insurance_provider', 'status']),
            models.Index(fields=['customer', '-submitted_date']),
            models.Index(fields=['claim_type', 'status']),
        ]
    
    def __str__(self):
        return f"Claim {self.claim_number} - {self.customer.company_name or self.customer.get_full_name()}"
    
    @property
    def is_high_value(self):
        """Check if claim is high value (>$50,000)"""
        return self.claim_amount > 50000
    
    @property
    def days_since_submitted(self):
        """Calculate days since claim submission"""
        from django.utils import timezone
        return (timezone.now() - self.submitted_date).days

