# Generated by Django 5.2.1 on 2025-08-07 12:45

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('shipments', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='AdvancedPayment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('payment_id', models.CharField(db_index=True, max_length=100, unique=True)),
                ('payment_type', models.CharField(choices=[('STRIPE', 'Stripe Card Payment'), ('BANK_TRANSFER', 'Bank Transfer'), ('HYBRID', 'Hybrid Payment')], db_index=True, max_length=20)),
                ('status', models.Char<PERSON>ield(choices=[('PENDING', 'Pending'), ('PROCESSING', 'Processing'), ('COMPLETED', 'Completed'), ('FAILED', 'Failed'), ('CANCELLED', 'Cancelled'), ('REQUIRES_APPROVAL', 'Requires Approval')], db_index=True, default='PENDING', max_length=20)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=12)),
                ('currency', models.CharField(default='USD', max_length=3)),
                ('stripe_payment_intent_id', models.CharField(blank=True, max_length=200, null=True)),
                ('stripe_charge_id', models.CharField(blank=True, max_length=200, null=True)),
                ('bank_reference_number', models.CharField(blank=True, max_length=100, null=True)),
                ('bank_approval_required', models.BooleanField(db_index=True, default=False)),
                ('bank_approved_at', models.DateTimeField(blank=True, null=True)),
                ('bank_proof_document', models.CharField(blank=True, max_length=255, null=True)),
                ('platform_fee', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('net_amount', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True)),
                ('processing_fee', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('created_at', models.DateTimeField(auto_now_add=True, db_index=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('bank_approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_bank_transfers', to=settings.AUTH_USER_MODEL)),
                ('shipment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='advanced_payments', to='shipments.shipment')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='BankTransferApproval',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('PENDING', 'Pending Review'), ('APPROVED', 'Approved'), ('REJECTED', 'Rejected'), ('REQUIRES_INFO', 'Requires Additional Information')], db_index=True, default='PENDING', max_length=20)),
                ('proof_document_path', models.CharField(blank=True, max_length=255, null=True)),
                ('transfer_reference', models.CharField(blank=True, max_length=100, null=True)),
                ('notes', models.TextField(blank=True, null=True)),
                ('reviewed_at', models.DateTimeField(blank=True, null=True)),
                ('rejection_reason', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True, db_index=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('advanced_payment', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='bank_approval', to='payments.advancedpayment')),
                ('logistics_provider', models.ForeignKey(limit_choices_to={'user_type': 'LOGISTICS'}, on_delete=django.db.models.deletion.CASCADE, related_name='bank_transfer_approvals', to=settings.AUTH_USER_MODEL)),
                ('reviewed_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='reviewed_bank_transfers', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Contract',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('contract_number', models.CharField(db_index=True, max_length=50, unique=True)),
                ('title', models.CharField(max_length=255)),
                ('content', models.TextField()),
                ('terms_accepted_by_customer', models.BooleanField(db_index=True, default=False)),
                ('terms_accepted_by_provider', models.BooleanField(db_index=True, default=False)),
                ('customer_signature_date', models.DateTimeField(blank=True, null=True)),
                ('provider_signature_date', models.DateTimeField(blank=True, null=True)),
                ('contract_file', models.CharField(blank=True, max_length=255)),
                ('is_active', models.BooleanField(db_index=True, default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True, db_index=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('customer', models.ForeignKey(limit_choices_to={'user_type': 'CUSTOMER'}, on_delete=django.db.models.deletion.CASCADE, related_name='customer_contracts', to=settings.AUTH_USER_MODEL)),
                ('logistics_provider', models.ForeignKey(limit_choices_to={'user_type': 'LOGISTICS'}, on_delete=django.db.models.deletion.CASCADE, related_name='provider_contracts', to=settings.AUTH_USER_MODEL)),
                ('shipment', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='contract', to='shipments.shipment')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='CustomsDeclaration',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('declaration_number', models.CharField(db_index=True, max_length=50, unique=True)),
                ('declaration_type', models.CharField(db_index=True, max_length=50)),
                ('goods_description', models.TextField()),
                ('goods_value', models.DecimalField(decimal_places=2, max_digits=12)),
                ('country_of_origin', models.CharField(db_index=True, max_length=100)),
                ('tariff_codes', models.JSONField(default=list)),
                ('duties_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('taxes_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('status', models.CharField(choices=[('PENDING', 'Pending'), ('SUBMITTED', 'Submitted'), ('APPROVED', 'Approved'), ('INSPECTION', 'Requires Inspection'), ('REJECTED', 'Rejected')], db_index=True, default='PENDING', max_length=20)),
                ('submitted_date', models.DateTimeField(blank=True, db_index=True, null=True)),
                ('processed_date', models.DateTimeField(blank=True, db_index=True, null=True)),
                ('documents', models.JSONField(default=list)),
                ('notes', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True, db_index=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('customs_authority', models.ForeignKey(limit_choices_to={'user_type': 'CUSTOMS'}, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='processed_declarations', to=settings.AUTH_USER_MODEL)),
                ('shipment', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='customs_declaration', to='shipments.shipment')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Insurance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('policy_number', models.CharField(db_index=True, max_length=50, unique=True)),
                ('coverage_amount', models.DecimalField(decimal_places=2, max_digits=12)),
                ('premium_amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('deductible_amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('start_date', models.DateTimeField(db_index=True)),
                ('end_date', models.DateTimeField(db_index=True)),
                ('policy_document', models.CharField(blank=True, max_length=255)),
                ('terms_conditions', models.TextField(blank=True, default='Standard insurance terms and conditions apply.')),
                ('notes', models.TextField(blank=True, help_text='Internal notes for this insurance policy')),
                ('status', models.CharField(choices=[('PENDING', 'Pending'), ('ACTIVE', 'Active'), ('CLAIMED', 'Claimed'), ('EXPIRED', 'Expired'), ('CANCELLED', 'Cancelled')], db_index=True, default='PENDING', max_length=20)),
                ('terms_accepted', models.BooleanField(db_index=True, default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True, db_index=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('insurance_provider', models.ForeignKey(limit_choices_to={'user_type': 'INSURANCE'}, on_delete=django.db.models.deletion.CASCADE, related_name='provided_insurances', to=settings.AUTH_USER_MODEL)),
                ('shipment', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='insurance', to='shipments.shipment')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='InsuranceClaim',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('claim_number', models.CharField(db_index=True, max_length=50, unique=True)),
                ('claim_type', models.CharField(choices=[('DAMAGE', 'Cargo Damage'), ('LOSS', 'Cargo Loss'), ('THEFT', 'Theft'), ('DELAY', 'Delivery Delay'), ('WATER_DAMAGE', 'Water Damage'), ('FIRE_DAMAGE', 'Fire Damage'), ('ACCIDENT', 'Accident'), ('OTHER', 'Other')], db_index=True, max_length=20)),
                ('claim_amount', models.DecimalField(db_index=True, decimal_places=2, max_digits=12)),
                ('submitted_date', models.DateTimeField(auto_now_add=True, db_index=True)),
                ('processed_date', models.DateTimeField(blank=True, db_index=True, null=True)),
                ('status', models.CharField(choices=[('SUBMITTED', 'Submitted'), ('UNDER_REVIEW', 'Under Review'), ('APPROVED', 'Approved'), ('REJECTED', 'Rejected'), ('SETTLED', 'Settled'), ('CLOSED', 'Closed')], db_index=True, default='SUBMITTED', max_length=20)),
                ('denial_reason', models.TextField(blank=True, null=True)),
                ('settlement_amount', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True)),
                ('documents', models.JSONField(blank=True, default=list)),
                ('notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True, db_index=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('customer', models.ForeignKey(limit_choices_to={'user_type': 'CUSTOMER'}, on_delete=django.db.models.deletion.CASCADE, related_name='submitted_claims', to=settings.AUTH_USER_MODEL)),
                ('insurance_provider', models.ForeignKey(limit_choices_to={'user_type': 'INSURANCE'}, on_delete=django.db.models.deletion.CASCADE, related_name='handled_claims', to=settings.AUTH_USER_MODEL)),
                ('shipment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='insurance_claims', to='shipments.shipment')),
            ],
            options={
                'db_table': 'insurance_claims',
                'ordering': ['-submitted_date'],
            },
        ),
        migrations.CreateModel(
            name='LogisticsCompanySettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('commission_model', models.CharField(choices=[('PER_ORDER', 'Per-Order Automatic Payment'), ('MONTHLY', 'Monthly Invoice')], db_index=True, default='PER_ORDER', max_length=20)),
                ('deposit_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('deposit_paid', models.BooleanField(db_index=True, default=False)),
                ('deposit_pending', models.BooleanField(db_index=True, default=False)),
                ('deposit_payment_date', models.DateTimeField(blank=True, null=True)),
                ('deposit_invoice_number', models.CharField(blank=True, max_length=50, null=True)),
                ('has_payment_method', models.BooleanField(db_index=True, default=False)),
                ('payment_method_id', models.CharField(blank=True, max_length=100, null=True)),
                ('payment_method_last4', models.CharField(blank=True, max_length=4, null=True)),
                ('payment_method_brand', models.CharField(blank=True, max_length=20, null=True)),
                ('monthly_invoice_day', models.IntegerField(default=1)),
                ('trusted_lc_status', models.BooleanField(db_index=True, default=False)),
                ('trusted_lc_date', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True, db_index=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('logistics_company', models.OneToOneField(limit_choices_to={'user_type': 'LOGISTICS'}, on_delete=django.db.models.deletion.CASCADE, related_name='commission_settings', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name_plural': 'Logistics company settings',
            },
        ),
        migrations.CreateModel(
            name='LogisticsWallet',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('balance', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('reserved_amount', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('total_earned', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('total_withdrawn', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('bank_account_number', models.CharField(blank=True, max_length=50, null=True)),
                ('bank_routing_number', models.CharField(blank=True, max_length=20, null=True)),
                ('bank_name', models.CharField(blank=True, max_length=100, null=True)),
                ('account_holder_name', models.CharField(blank=True, max_length=100, null=True)),
                ('is_active', models.BooleanField(db_index=True, default=True)),
                ('is_verified', models.BooleanField(db_index=True, default=False)),
                ('verification_date', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True, db_index=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('logistics_provider', models.OneToOneField(limit_choices_to={'user_type': 'LOGISTICS'}, on_delete=django.db.models.deletion.CASCADE, related_name='logistics_wallet', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='Payment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('currency', models.CharField(default='USD', max_length=3)),
                ('payment_type', models.CharField(choices=[('SHIPMENT', 'Shipment Payment'), ('INSURANCE', 'Insurance Payment'), ('CUSTOMS', 'Customs Fee'), ('ADDITIONAL', 'Additional Fee'), ('COMMISSION', 'Logistics Commission'), ('INSURANCE_COMMISSION', 'Insurance Commission'), ('PREMIUM_SUBSCRIPTION', 'Premium Feature Subscription'), ('DEPOSIT', 'Security Deposit')], db_index=True, max_length=30)),
                ('payment_method', models.CharField(choices=[('CREDIT_CARD', 'Credit/Debit Card'), ('BANK_TRANSFER', 'Bank Transfer'), ('PAYPAL', 'PayPal'), ('STRIPE', 'Stripe'), ('DEPOSIT_BALANCE', 'Deposit Balance'), ('PREPAID_COMMISSION', 'Prepaid Commission Credit')], db_index=True, max_length=30)),
                ('transaction_id', models.CharField(blank=True, db_index=True, max_length=100, null=True, unique=True)),
                ('platform_fee', models.DecimalField(decimal_places=2, max_digits=10)),
                ('insurance_commission', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('status', models.CharField(choices=[('PENDING', 'Pending'), ('PROCESSING', 'Processing'), ('COMPLETED', 'Completed'), ('FAILED', 'Failed'), ('REFUNDED', 'Refunded'), ('AWAITING_PROOF', 'Awaiting Payment Proof'), ('PROOF_SUBMITTED', 'Payment Proof Submitted'), ('PROOF_REJECTED', 'Payment Proof Rejected'), ('PROOF_VERIFIED', 'Payment Proof Verified')], db_index=True, default='PENDING', max_length=30)),
                ('payment_date', models.DateTimeField(blank=True, db_index=True, null=True)),
                ('invoice_number', models.CharField(db_index=True, max_length=50, unique=True)),
                ('payment_proof', models.CharField(blank=True, max_length=255, null=True)),
                ('proof_submitted_date', models.DateTimeField(blank=True, null=True)),
                ('proof_verified_date', models.DateTimeField(blank=True, null=True)),
                ('commission_model', models.CharField(blank=True, choices=[('PER_ORDER', 'Per-Order Automatic Payment'), ('MONTHLY', 'Monthly Invoice')], max_length=20, null=True)),
                ('stripe_payment_intent_id', models.CharField(blank=True, max_length=100, null=True)),
                ('stripe_checkout_session_id', models.CharField(blank=True, max_length=100, null=True)),
                ('bank_details', models.JSONField(blank=True, default=dict, null=True)),
                ('note', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True, db_index=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('payee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payments_received', to=settings.AUTH_USER_MODEL)),
                ('payer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payments_made', to=settings.AUTH_USER_MODEL)),
                ('shipment', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='payments', to='shipments.shipment')),
                ('verified_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='verified_payments', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='UserSubscription',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('plan_type', models.CharField(choices=[('BASIC', 'Basic (Free)'), ('STANDARD', 'Standard Premium'), ('PROFESSIONAL', 'Professional'), ('ENTERPRISE', 'Enterprise')], db_index=True, default='BASIC', max_length=20)),
                ('status', models.CharField(choices=[('TRIAL', 'Free Trial'), ('ACTIVE', 'Active Subscription'), ('CANCELLED', 'Cancelled'), ('EXPIRED', 'Expired')], db_index=True, default='TRIAL', max_length=20)),
                ('trial_start_date', models.DateTimeField(auto_now_add=True, db_index=True)),
                ('trial_end_date', models.DateTimeField(blank=True, db_index=True, null=True)),
                ('subscription_start_date', models.DateTimeField(blank=True, db_index=True, null=True)),
                ('subscription_end_date', models.DateTimeField(blank=True, db_index=True, null=True)),
                ('auto_renew', models.BooleanField(default=False)),
                ('payment_method_id', models.CharField(blank=True, max_length=100, null=True)),
                ('features', models.JSONField(blank=True, default=dict, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True, db_index=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='subscriptions', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddIndex(
            model_name='advancedpayment',
            index=models.Index(fields=['shipment', 'status'], name='payments_ad_shipmen_bab289_idx'),
        ),
        migrations.AddIndex(
            model_name='advancedpayment',
            index=models.Index(fields=['payment_type', 'status'], name='payments_ad_payment_c17ef6_idx'),
        ),
        migrations.AddIndex(
            model_name='advancedpayment',
            index=models.Index(fields=['bank_approval_required'], name='payments_ad_bank_ap_3d27cf_idx'),
        ),
        migrations.AddIndex(
            model_name='advancedpayment',
            index=models.Index(fields=['created_at'], name='payments_ad_created_41fa91_idx'),
        ),
        migrations.AddIndex(
            model_name='banktransferapproval',
            index=models.Index(fields=['logistics_provider', 'status'], name='payments_ba_logisti_627d45_idx'),
        ),
        migrations.AddIndex(
            model_name='banktransferapproval',
            index=models.Index(fields=['status', 'created_at'], name='payments_ba_status_95b738_idx'),
        ),
        migrations.AddIndex(
            model_name='banktransferapproval',
            index=models.Index(fields=['reviewed_at'], name='payments_ba_reviewe_2963bc_idx'),
        ),
        migrations.AddIndex(
            model_name='contract',
            index=models.Index(fields=['customer', 'is_active'], name='payments_co_custome_9ac4d5_idx'),
        ),
        migrations.AddIndex(
            model_name='contract',
            index=models.Index(fields=['logistics_provider', 'is_active'], name='payments_co_logisti_c0e050_idx'),
        ),
        migrations.AddIndex(
            model_name='contract',
            index=models.Index(fields=['terms_accepted_by_customer', 'terms_accepted_by_provider'], name='payments_co_terms_a_40cf77_idx'),
        ),
        migrations.AddIndex(
            model_name='contract',
            index=models.Index(fields=['created_at'], name='payments_co_created_61ff54_idx'),
        ),
        migrations.AddIndex(
            model_name='customsdeclaration',
            index=models.Index(fields=['shipment', 'status'], name='payments_cu_shipmen_7acb29_idx'),
        ),
        migrations.AddIndex(
            model_name='customsdeclaration',
            index=models.Index(fields=['customs_authority', 'status'], name='payments_cu_customs_7b166e_idx'),
        ),
        migrations.AddIndex(
            model_name='customsdeclaration',
            index=models.Index(fields=['declaration_number'], name='payments_cu_declara_f8840f_idx'),
        ),
        migrations.AddIndex(
            model_name='customsdeclaration',
            index=models.Index(fields=['declaration_type'], name='payments_cu_declara_d20b61_idx'),
        ),
        migrations.AddIndex(
            model_name='customsdeclaration',
            index=models.Index(fields=['country_of_origin'], name='payments_cu_country_70d59f_idx'),
        ),
        migrations.AddIndex(
            model_name='customsdeclaration',
            index=models.Index(fields=['status', 'submitted_date'], name='payments_cu_status_a00ad2_idx'),
        ),
        migrations.AddIndex(
            model_name='customsdeclaration',
            index=models.Index(fields=['created_at'], name='payments_cu_created_186591_idx'),
        ),
        migrations.AddIndex(
            model_name='insurance',
            index=models.Index(fields=['insurance_provider', 'status'], name='payments_in_insuran_8302b4_idx'),
        ),
        migrations.AddIndex(
            model_name='insurance',
            index=models.Index(fields=['policy_number'], name='payments_in_policy__a0aa3c_idx'),
        ),
        migrations.AddIndex(
            model_name='insurance',
            index=models.Index(fields=['start_date', 'end_date'], name='payments_in_start_d_009a3e_idx'),
        ),
        migrations.AddIndex(
            model_name='insurance',
            index=models.Index(fields=['status', 'terms_accepted'], name='payments_in_status_2666ea_idx'),
        ),
        migrations.AddIndex(
            model_name='insurance',
            index=models.Index(fields=['created_at'], name='payments_in_created_73c69e_idx'),
        ),
        migrations.AddIndex(
            model_name='insuranceclaim',
            index=models.Index(fields=['status', '-submitted_date'], name='insurance_c_status_46bd11_idx'),
        ),
        migrations.AddIndex(
            model_name='insuranceclaim',
            index=models.Index(fields=['insurance_provider', 'status'], name='insurance_c_insuran_bce28b_idx'),
        ),
        migrations.AddIndex(
            model_name='insuranceclaim',
            index=models.Index(fields=['customer', '-submitted_date'], name='insurance_c_custome_7beb6b_idx'),
        ),
        migrations.AddIndex(
            model_name='insuranceclaim',
            index=models.Index(fields=['claim_type', 'status'], name='insurance_c_claim_t_09d7d4_idx'),
        ),
        migrations.AddIndex(
            model_name='logisticscompanysettings',
            index=models.Index(fields=['logistics_company', 'commission_model'], name='payments_lo_logisti_6f5d26_idx'),
        ),
        migrations.AddIndex(
            model_name='logisticscompanysettings',
            index=models.Index(fields=['deposit_paid'], name='payments_lo_deposit_fa00ea_idx'),
        ),
        migrations.AddIndex(
            model_name='logisticscompanysettings',
            index=models.Index(fields=['trusted_lc_status'], name='payments_lo_trusted_8c0717_idx'),
        ),
        migrations.AddIndex(
            model_name='logisticscompanysettings',
            index=models.Index(fields=['created_at'], name='payments_lo_created_bdc833_idx'),
        ),
        migrations.AddIndex(
            model_name='logisticswallet',
            index=models.Index(fields=['logistics_provider', 'is_active'], name='payments_lo_logisti_797433_idx'),
        ),
        migrations.AddIndex(
            model_name='logisticswallet',
            index=models.Index(fields=['is_verified'], name='payments_lo_is_veri_6d5766_idx'),
        ),
        migrations.AddIndex(
            model_name='logisticswallet',
            index=models.Index(fields=['created_at'], name='payments_lo_created_4a4013_idx'),
        ),
        migrations.AddIndex(
            model_name='payment',
            index=models.Index(fields=['shipment', 'status'], name='payments_pa_shipmen_754e22_idx'),
        ),
        migrations.AddIndex(
            model_name='payment',
            index=models.Index(fields=['payer', 'status'], name='payments_pa_payer_i_ebdbf4_idx'),
        ),
        migrations.AddIndex(
            model_name='payment',
            index=models.Index(fields=['payee', 'status'], name='payments_pa_payee_i_80dac9_idx'),
        ),
        migrations.AddIndex(
            model_name='payment',
            index=models.Index(fields=['payment_type', 'payment_method'], name='payments_pa_payment_a66fc9_idx'),
        ),
        migrations.AddIndex(
            model_name='payment',
            index=models.Index(fields=['status', 'payment_date'], name='payments_pa_status_3f89d4_idx'),
        ),
        migrations.AddIndex(
            model_name='payment',
            index=models.Index(fields=['created_at'], name='payments_pa_created_b8a300_idx'),
        ),
        migrations.AddIndex(
            model_name='payment',
            index=models.Index(fields=['invoice_number'], name='payments_pa_invoice_601934_idx'),
        ),
        migrations.AddIndex(
            model_name='usersubscription',
            index=models.Index(fields=['user', 'status'], name='payments_us_user_id_7651b7_idx'),
        ),
        migrations.AddIndex(
            model_name='usersubscription',
            index=models.Index(fields=['plan_type'], name='payments_us_plan_ty_fd76ef_idx'),
        ),
        migrations.AddIndex(
            model_name='usersubscription',
            index=models.Index(fields=['trial_end_date'], name='payments_us_trial_e_1b0553_idx'),
        ),
        migrations.AddIndex(
            model_name='usersubscription',
            index=models.Index(fields=['subscription_end_date'], name='payments_us_subscri_bd69aa_idx'),
        ),
        migrations.AddIndex(
            model_name='usersubscription',
            index=models.Index(fields=['created_at'], name='payments_us_created_c6761f_idx'),
        ),
    ]
