# Payment URLs - extracted from fastapi_main.py
from django.urls import path
from . import views

app_name = 'payments'

urlpatterns = [
    # Payment processing
    path('checkout/', views.payment_checkout, name='payment_checkout'),
    path('process/', views.process_payment, name='process_payment'),
    path('confirm/', views.payment_confirmation, name='payment_confirmation'),
    
    # Stripe integration
    path('stripe/create-intent/', views.create_payment_intent, name='create_payment_intent'),
    path('stripe/webhook/', views.stripe_webhook, name='stripe_webhook'),
    path('stripe/success/', views.stripe_success, name='stripe_success'),
    path('stripe/cancel/', views.stripe_cancel, name='stripe_cancel'),
    
    # Bank transfer
    path('bank-transfer/', views.bank_transfer, name='bank_transfer'),
    path('bank-transfer/verify/', views.verify_bank_transfer, name='verify_bank_transfer'),
    path('bank-transfer/upload/', views.upload_bank_receipt, name='upload_bank_receipt'),
    
    # Payment history
    path('history/', views.payment_history, name='payment_history'),
    path('invoice/<int:payment_id>/', views.payment_invoice, name='payment_invoice'),
    path('receipt/<int:payment_id>/', views.payment_receipt, name='payment_receipt'),
    
    # Contract management
    path('contracts/', views.contract_list, name='contract_list'),
    path('contract/<int:contract_id>/', views.contract_detail, name='contract_detail'),
    path('contract/<int:contract_id>/sign/', views.sign_contract, name='sign_contract'),
    path('contract/<int:contract_id>/download/', views.download_contract, name='download_contract'),
    
    # Insurance
    path('insurance/', views.insurance_options, name='insurance_options'),
    path('insurance/quote/', views.insurance_quote, name='insurance_quote'),
    path('insurance/purchase/', views.purchase_insurance, name='purchase_insurance'),
    path('insurance/claims/', views.insurance_claims, name='insurance_claims'),
    path('insurance/claim/<int:claim_id>/', views.insurance_claim_detail, name='insurance_claim_detail'),
    
    # Refunds and disputes
    path('refund/request/', views.request_refund, name='request_refund'),
    path('refund/<int:refund_id>/', views.refund_status, name='refund_status'),
    path('dispute/<int:payment_id>/', views.payment_dispute, name='payment_dispute'),
    
    # Financial reports
    path('reports/revenue/', views.revenue_report, name='revenue_report'),
    path('reports/fees/', views.fees_report, name='fees_report'),
    path('reports/export/', views.export_financial_report, name='export_financial_report'),
]