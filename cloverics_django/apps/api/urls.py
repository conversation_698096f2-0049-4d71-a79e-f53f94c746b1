# API endpoints - extracted from fastapi_main.py
from django.urls import path, include
from . import views

app_name = 'api'

urlpatterns = [
    # Core API endpoints
    path('health/', views.health_check, name='health_check'),
    path('version/', views.version_info, name='version_info'),
    
    # Authentication API
    path('auth/login/', views.api_login, name='api_login'),
    path('auth/register/', views.api_register, name='api_register'),
    path('auth/logout/', views.api_logout, name='api_logout'),
    path('auth/verify/', views.verify_token, name='verify_token'),
    
    # Customer API endpoints
    path('customer/profile/', views.customer_profile, name='customer_profile'),
    path('customer/shipments/', views.customer_shipments, name='customer_shipments'),
    path('customer/quotes/', views.customer_quotes, name='customer_quotes'),
    
    # Logistics Provider API endpoints
    path('logistics/dashboard/', views.logistics_dashboard_api, name='logistics_dashboard_api'),
    path('logistics/routes/', views.logistics_routes_api, name='logistics_routes_api'),
    path('logistics/shipments/', views.logistics_shipments_api, name='logistics_shipments_api'),
    
    # Admin API endpoints
    path('admin/users/', views.admin_users_api, name='admin_users_api'),
    path('admin/statistics/', views.admin_statistics_api, name='admin_statistics_api'),
    path('admin/verification/', views.admin_verification_api, name='admin_verification_api'),
    
    # Customs API endpoints
    path('customs/declarations/', views.customs_declarations_api, name='customs_declarations_api'),
    path('customs/approve/<int:declaration_id>/', views.customs_approve_api, name='customs_approve_api'),
    path('customs/reject/<int:declaration_id>/', views.customs_reject_api, name='customs_reject_api'),
    
    # Driver API endpoints
    path('drivers/jobs/', views.driver_jobs_api, name='driver_jobs_api'),
    path('drivers/profile/', views.driver_profile_api, name='driver_profile_api'),
    path('drivers/location/', views.driver_location_api, name='driver_location_api'),
    
    # Insurance API endpoints
    path('insurance/policies/', views.insurance_policies_api, name='insurance_policies_api'),
    path('insurance/claims/', views.insurance_claims_api, name='insurance_claims_api'),
    
    # Payment API endpoints
    path('payments/process/', views.process_payment_api, name='process_payment_api'),
    path('payments/stripe-webhook/', views.stripe_webhook, name='stripe_webhook'),
    path('payments/bank-transfer/', views.bank_transfer_api, name='bank_transfer_api'),
    
    # Notification API endpoints
    path('notifications/list/', views.notifications_list_api, name='notifications_list_api'),
    path('notifications/mark-read/', views.mark_notifications_read_api, name='mark_notifications_read_api'),
    path('notifications/realtime/', views.realtime_notifications_api, name='realtime_notifications_api'),
    
    # Analytics API endpoints
    path('analytics/dashboard/', views.analytics_dashboard_api, name='analytics_dashboard_api'),
    path('analytics/performance/', views.performance_analytics_api, name='performance_analytics_api'),
    path('analytics/reports/', views.analytics_reports_api, name='analytics_reports_api'),
    
    # WebSocket endpoints - REMOVED
    
    # Enterprise Integration API
    path('enterprise/erp-sync/', views.erp_sync_api, name='erp_sync_api'),
    path('enterprise/rfq-batch/', views.rfq_batch_api, name='rfq_batch_api'),
    
    # Document Processing API
    path('documents/generate/', views.generate_document_api, name='generate_document_api'),
    path('documents/download/<str:document_id>/', views.download_document_api, name='download_document_api'),
    
    # Market Intelligence API
    path('market/overview/', views.market_overview_api, name='market_overview_api'),
    path('market/rates/', views.market_rates_api, name='market_rates_api'),
    path('market/trends/', views.market_trends_api, name='market_trends_api'),
    
    # Critical real-time endpoints
    path('events/poll/', views.events_poll_api, name='events_poll_api'),
    
    # Mobile API endpoints
    path('mobile/auth/', views.mobile_auth_api, name='mobile_auth_api'),
    path('mobile/dashboard/', views.mobile_dashboard_api, name='mobile_dashboard_api'),
    path('mobile/shipments/', views.mobile_shipments_api, name='mobile_shipments_api'),
    path('mobile/tracking/', views.mobile_tracking_api, name='mobile_tracking_api'),
    
    # Legacy FastAPI compatibility routes
    path('v1/auth/login/', views.v1_login_api, name='v1_login_api'),
    path('v1/shipments/', views.v1_shipments_api, name='v1_shipments_api'),
    path('v1/quotes/', views.v1_quotes_api, name='v1_quotes_api'),
    path('v1/tracking/', views.v1_tracking_api, name='v1_tracking_api'),
    
    # Advanced analytics endpoints
    path('analytics/real-time/', views.realtime_analytics_api, name='realtime_analytics_api'),
    path('analytics/shipment-flow/', views.shipment_flow_analytics, name='shipment_flow_analytics'),
    path('analytics/performance-metrics/', views.performance_metrics_api, name='performance_metrics_api'),
    
    # Integration endpoints
    path('integrations/webhook/', views.webhook_handler, name='webhook_handler'),
    path('integrations/sync/', views.sync_integrations, name='sync_integrations'),
    path('integrations/status/', views.integration_status, name='integration_status'),
    
    # Saved Searches & Price Alerts API endpoints
    path('saved-searches/', include('apps.saved_searches.urls')),
    path('countries/', views.countries_api, name='countries_api'),
    path('price-alerts/check/', views.check_price_alerts_api, name='check_price_alerts_api'),
    path('search-analytics/', views.search_analytics_api, name='search_analytics_api'),
]