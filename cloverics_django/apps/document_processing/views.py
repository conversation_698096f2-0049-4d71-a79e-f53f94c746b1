from django.shortcuts import render, get_object_or_404
from django.http import JsonResponse, HttpResponse, Http404
from django.contrib.auth.decorators import login_required
from django.views.decorators.http import require_http_methods
from django.utils import timezone
from django.conf import settings
from django.db import transaction
from io import BytesIO
import json
import uuid
import os

from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
from reportlab.lib.styles import getSampleStyleSheet

from .models import DocumentTemplate, GeneratedDocument, DocumentGenerationQueue, DocumentDownloadLog
from apps.shipments.models import Shipment

class DocumentProcessingService:
    """Service class for document processing operations"""
    
    @staticmethod
    def generate_shipment_document(shipment_data: dict, document_type: str) -> bytes:
        """Generate PDF document for shipment"""
        buffer = BytesIO()
        
        # Create the PDF document
        doc = SimpleDocTemplate(buffer, pagesize=letter)
        styles = getSampleStyleSheet()
        story = []
        
        # Header
        title = f"Cloverics - {document_type.title()} Document"
        story.append(Paragraph(title, styles['Title']))
        story.append(Spacer(1, 20))
        
        # Shipment information
        story.append(Paragraph(f"<b>Shipment ID:</b> {shipment_data.get('tracking_number', 'N/A')}", styles['Normal']))
        story.append(Paragraph(f"<b>From:</b> {shipment_data.get('origin_city', 'N/A')}, {shipment_data.get('origin_country', 'N/A')}", styles['Normal']))
        story.append(Paragraph(f"<b>To:</b> {shipment_data.get('destination_city', 'N/A')}, {shipment_data.get('destination_country', 'N/A')}", styles['Normal']))
        story.append(Paragraph(f"<b>Weight:</b> {shipment_data.get('weight', 0)} kg", styles['Normal']))
        story.append(Paragraph(f"<b>Status:</b> {shipment_data.get('status', 'N/A')}", styles['Normal']))
        story.append(Spacer(1, 20))
        
        # Document specific content
        if document_type == 'invoice':
            story.append(Paragraph("<b>INVOICE DETAILS</b>", styles['Heading2']))
            story.append(Paragraph(f"Total Amount: ${shipment_data.get('total_price', 0):.2f}", styles['Normal']))
            story.append(Paragraph("Payment Terms: Net 30 days", styles['Normal']))
        elif document_type == 'packing':
            story.append(Paragraph("<b>PACKING LIST</b>", styles['Heading2']))
            story.append(Paragraph(f"Items: {shipment_data.get('items_description', 'General cargo')}", styles['Normal']))
            story.append(Paragraph(f"Cargo Type: {shipment_data.get('cargo_type', 'Standard')}", styles['Normal']))
        elif document_type == 'customs':
            story.append(Paragraph("<b>CUSTOMS DECLARATION</b>", styles['Heading2']))
            story.append(Paragraph(f"Declared Value: ${shipment_data.get('declared_value', shipment_data.get('total_price', 0)):.2f}", styles['Normal']))
            story.append(Paragraph("Purpose: Commercial shipment", styles['Normal']))
        elif document_type == 'insurance':
            story.append(Paragraph("<b>INSURANCE CERTIFICATE</b>", styles['Heading2']))
            story.append(Paragraph(f"Coverage Amount: ${shipment_data.get('insurance_value', shipment_data.get('total_price', 0)):.2f}", styles['Normal']))
            story.append(Paragraph("Coverage Type: All risks", styles['Normal']))
        elif document_type == 'bill_of_lading':
            story.append(Paragraph("<b>BILL OF LADING</b>", styles['Heading2']))
            story.append(Paragraph(f"Shipper: {shipment_data.get('shipper_name', 'N/A')}", styles['Normal']))
            story.append(Paragraph(f"Consignee: {shipment_data.get('consignee_name', 'N/A')}", styles['Normal']))
            story.append(Paragraph(f"Vessel: {shipment_data.get('vessel_name', 'N/A')}", styles['Normal']))
        elif document_type == 'commercial_invoice':
            story.append(Paragraph("<b>COMMERCIAL INVOICE</b>", styles['Heading2']))
            story.append(Paragraph(f"Seller: {shipment_data.get('seller_name', 'N/A')}", styles['Normal']))
            story.append(Paragraph(f"Buyer: {shipment_data.get('buyer_name', 'N/A')}", styles['Normal']))
            story.append(Paragraph(f"Terms of Sale: {shipment_data.get('terms_of_sale', 'CIF')}", styles['Normal']))
        
        story.append(Spacer(1, 30))
        story.append(Paragraph("Generated by Cloverics Platform", styles['Normal']))
        story.append(Paragraph(f"Date: {timezone.now().strftime('%Y-%m-%d %H:%M:%S')}", styles['Normal']))
        
        # Build PDF
        doc.build(story)
        buffer.seek(0)
        return buffer.getvalue()
    
    @staticmethod
    def create_document_generation_task(shipment_id: int, document_type: str, user, priority='normal'):
        """Create a document generation task"""
        try:
            shipment = Shipment.objects.get(id=shipment_id)
            template = DocumentTemplate.objects.get(document_type=document_type)
            
            # Generate unique document ID
            document_id = f"DOC_{uuid.uuid4().hex[:12].upper()}"
            
            # Create generated document record
            document = GeneratedDocument.objects.create(
                document_id=document_id,
                shipment=shipment,
                template=template,
                user=user,
                status='pending'
            )
            
            # Add to generation queue
            DocumentGenerationQueue.objects.create(
                document=document,
                priority=priority,
                scheduled_at=timezone.now()
            )
            
            return document
            
        except (Shipment.DoesNotExist, DocumentTemplate.DoesNotExist) as e:
            raise ValueError(f"Failed to create document generation task: {str(e)}")

@login_required
@require_http_methods(["GET"])
def get_document_template(request, document_type):
    """Get document template information and requirements"""
    try:
        template = DocumentTemplate.objects.get(document_type=document_type, is_active=True)
        
        return JsonResponse({
            'success': True,
            'template': {
                'name': template.name,
                'description': template.description,
                'required_fields': template.required_fields,
                'optional_fields': template.optional_fields,
                'estimated_generation_time': template.estimated_generation_time,
                'document_type': template.document_type
            }
        })
        
    except DocumentTemplate.DoesNotExist:
        return JsonResponse({
            'success': False,
            'error': f'Unknown document type: {document_type}'
        }, status=404)

@login_required
@require_http_methods(["GET"])
def get_shipment_documents(request, shipment_id):
    """Get all generated documents for a shipment"""
    try:
        shipment = get_object_or_404(Shipment, id=shipment_id)
        
        # Check user permissions
        if request.user.user_type == 'CUSTOMER' and shipment.customer != request.user:
            return JsonResponse({'error': 'Permission denied'}, status=403)
        elif request.user.user_type == 'LOGISTICS' and shipment.logistics_provider != request.user:
            return JsonResponse({'error': 'Permission denied'}, status=403)
        
        documents = GeneratedDocument.objects.filter(shipment=shipment).select_related('template')
        
        document_list = []
        for doc in documents:
            document_list.append({
                'document_id': doc.document_id,
                'document_type': doc.template.document_type,
                'document_name': doc.template.name,
                'status': doc.status,
                'created_at': doc.created_at.isoformat(),
                'file_size': doc.file_size,
                'download_url': f'/api/documents/download/{doc.document_id}/' if doc.status == 'completed' else None
            })
        
        return JsonResponse({
            'success': True,
            'shipment_id': shipment_id,
            'documents': document_list
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)

@login_required 
@require_http_methods(["GET"])
def download_document(request, document_id):
    """Download generated document PDF"""
    try:
        document = get_object_or_404(GeneratedDocument, document_id=document_id)
        
        # Check permissions
        if request.user.user_type == 'CUSTOMER' and document.shipment.customer != request.user:
            return JsonResponse({'error': 'Permission denied'}, status=403)
        elif request.user.user_type == 'LOGISTICS' and document.shipment.logistics_provider != request.user:
            return JsonResponse({'error': 'Permission denied'}, status=403)
        
        if document.status != 'completed':
            return JsonResponse({
                'error': f'Document not ready for download. Status: {document.status}'
            }, status=400)
        
        # Generate PDF if not already generated
        if not document.file_path or not os.path.exists(document.file_path):
            # Get shipment data
            shipment_data = {
                'tracking_number': document.shipment.tracking_number,
                'origin_city': document.shipment.origin_city,
                'origin_country': document.shipment.origin_country,
                'destination_city': document.shipment.destination_city,
                'destination_country': document.shipment.destination_country,
                'weight': document.shipment.weight,
                'status': document.shipment.status,
                'total_price': document.shipment.total_price,
                'cargo_type': document.shipment.cargo_type,
            }
            
            # Add document-specific data
            if document.document_data:
                shipment_data.update(document.document_data)
            
            # Generate PDF
            pdf_data = DocumentProcessingService.generate_shipment_document(
                shipment_data, document.template.document_type
            )
            
            # Create HTTP response
            response = HttpResponse(pdf_data, content_type='application/pdf')
            response['Content-Disposition'] = f'attachment; filename="{document.document_id}_{document.template.document_type}.pdf"'
            
            # Log download
            DocumentDownloadLog.objects.create(
                document=document,
                user=request.user,
                ip_address=request.META.get('REMOTE_ADDR'),
                user_agent=request.META.get('HTTP_USER_AGENT', '')[:1000]
            )
            
            return response
        
        # If file exists, serve it
        with open(document.file_path, 'rb') as f:
            response = HttpResponse(f.read(), content_type='application/pdf')
            response['Content-Disposition'] = f'attachment; filename="{document.document_id}_{document.template.document_type}.pdf"'
            
            # Log download
            DocumentDownloadLog.objects.create(
                document=document,
                user=request.user,
                ip_address=request.META.get('REMOTE_ADDR'),
                user_agent=request.META.get('HTTP_USER_AGENT', '')[:1000]
            )
            
            return response
            
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)

@login_required
@require_http_methods(["POST"])
def generate_document_request(request, shipment_id, document_type):
    """Request document generation for a shipment"""
    try:
        with transaction.atomic():
            # Create document generation task
            document = DocumentProcessingService.create_document_generation_task(
                shipment_id=shipment_id,
                document_type=document_type,
                user=request.user,
                priority=request.POST.get('priority', 'normal')
            )
            
            return JsonResponse({
                'success': True,
                'document_id': document.document_id,
                'status': document.status,
                'estimated_completion': '2-3 minutes'
            })
            
    except ValueError as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=400)
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': f'Failed to create document generation request: {str(e)}'
        }, status=500)

@login_required
@require_http_methods(["GET"])
def document_processing_dashboard(request):
    """Document processing dashboard"""
    if request.user.user_type not in ['ADMIN', 'LOGISTICS_PROVIDER']:
        return JsonResponse({'error': 'Permission denied'}, status=403)
    
    # Get processing statistics
    total_documents = GeneratedDocument.objects.count()
    pending_documents = GeneratedDocument.objects.filter(status='pending').count()
    completed_documents = GeneratedDocument.objects.filter(status='completed').count()
    failed_documents = GeneratedDocument.objects.filter(status='failed').count()
    
    # Recent documents
    recent_documents = GeneratedDocument.objects.select_related('template', 'shipment', 'user')\
        .order_by('-created_at')[:10]
    
    context = {
        'total_documents': total_documents,
        'pending_documents': pending_documents,
        'completed_documents': completed_documents,
        'failed_documents': failed_documents,
        'recent_documents': recent_documents,
        'success_rate': (completed_documents / max(total_documents, 1)) * 100
    }
    
    return render(request, 'document_processing/dashboard.html', context)