from django.db import models
from django.conf import settings
from django.utils import timezone
import json

class DocumentTemplate(models.Model):
    """Document template definitions"""
    DOCUMENT_TYPES = [
        ('bill_of_lading', 'Bill of Lading'),
        ('commercial_invoice', 'Commercial Invoice'),
        ('packing_list', 'Packing List'),
        ('certificate_of_origin', 'Certificate of Origin'),
        ('customs_declaration', 'Customs Declaration'),
        ('insurance_certificate', 'Insurance Certificate'),
        ('delivery_receipt', 'Delivery Receipt'),
        ('freight_invoice', 'Freight Invoice'),
    ]
    
    document_type = models.CharField(max_length=50, choices=DOCUMENT_TYPES, unique=True)
    name = models.CharField(max_length=200)
    description = models.TextField()
    required_fields = models.JSONField(default=list)
    optional_fields = models.JSONField(default=list)
    estimated_generation_time = models.CharField(max_length=50, default='2-3 minutes')
    template_content = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'document_templates'
    
    def __str__(self):
        return f"{self.name} ({self.document_type})"

class GeneratedDocument(models.Model):
    """Generated documents for shipments"""
    DOCUMENT_STATUS = [
        ('pending', 'Pending'),
        ('generating', 'Generating'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('expired', 'Expired'),
    ]
    
    document_id = models.CharField(max_length=50, unique=True)
    shipment_id = models.IntegerField()  # Reference to shipment ID (temporary until shipments app is enabled)
    template = models.ForeignKey(DocumentTemplate, on_delete=models.CASCADE)
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    
    # Document data
    document_data = models.JSONField(default=dict)
    file_path = models.CharField(max_length=500, blank=True)
    file_size = models.IntegerField(default=0)
    
    # Status tracking
    status = models.CharField(max_length=20, choices=DOCUMENT_STATUS, default='pending')
    generation_started = models.DateTimeField(null=True, blank=True)
    generation_completed = models.DateTimeField(null=True, blank=True)
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    expires_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        db_table = 'generated_documents'
        indexes = [
            models.Index(fields=['document_id']),
            models.Index(fields=['shipment_id', 'template']),
            models.Index(fields=['user', 'status']),
            models.Index(fields=['created_at']),
        ]
    
    def __str__(self):
        return f"{self.template.name} - {self.document_id}"

class DocumentGenerationQueue(models.Model):
    """Queue for document generation tasks"""
    PRIORITY_LEVELS = [
        ('low', 'Low'),
        ('normal', 'Normal'),
        ('high', 'High'),
        ('urgent', 'Urgent'),
    ]
    
    document = models.ForeignKey(GeneratedDocument, on_delete=models.CASCADE)
    priority = models.CharField(max_length=10, choices=PRIORITY_LEVELS, default='normal')
    attempts = models.IntegerField(default=0)
    max_attempts = models.IntegerField(default=3)
    
    scheduled_at = models.DateTimeField(default=timezone.now)
    started_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    
    error_message = models.TextField(blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'document_generation_queue'
        indexes = [
            models.Index(fields=['priority', 'scheduled_at']),
            models.Index(fields=['document', 'attempts']),
        ]
    
    def __str__(self):
        return f"Queue #{self.id} - {self.document.template.name}"

class DocumentDownloadLog(models.Model):
    """Track document downloads for audit purposes"""
    document = models.ForeignKey(GeneratedDocument, on_delete=models.CASCADE, related_name='download_logs')
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    
    downloaded_at = models.DateTimeField(auto_now_add=True)
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(blank=True)
    
    class Meta:
        db_table = 'document_download_logs'
        indexes = [
            models.Index(fields=['document_id']),
            models.Index(fields=['user_id']),
        ]
    
    def __str__(self):
        return f"{self.user.email} downloaded {self.document.document_id}"

class DocumentProcessingMetrics(models.Model):
    """Metrics for document processing performance"""
    date = models.DateField()
    document_type = models.CharField(max_length=50)
    
    total_generated = models.IntegerField(default=0)
    successful_generations = models.IntegerField(default=0)
    failed_generations = models.IntegerField(default=0)
    
    avg_generation_time = models.FloatField(default=0.0)  # in seconds
    total_downloads = models.IntegerField(default=0)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'document_processing_metrics'
        unique_together = ['date', 'document_type']
        indexes = [
            models.Index(fields=['date', 'document_type']),
        ]
    
    def __str__(self):
        return f"{self.document_type} metrics for {self.date}"