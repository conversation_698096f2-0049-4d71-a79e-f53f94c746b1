from django.urls import path
from . import views

app_name = 'document_processing'

urlpatterns = [
    # Template information
    path('api/documents/<str:document_type>/template/', views.get_document_template, name='get_document_template'),
    
    # Document management
    path('api/documents/shipment/<int:shipment_id>/', views.get_shipment_documents, name='get_shipment_documents'),
    path('api/documents/download/<str:document_id>/', views.download_document, name='download_document'),
    path('api/documents/generate/<int:shipment_id>/<str:document_type>/', views.generate_document_request, name='generate_document_request'),
    
    # Dashboard
    path('dashboard/', views.document_processing_dashboard, name='dashboard'),
    
    # Phase 3: Additional document endpoints for 100% coverage
    path('templates/', views.document_templates, name='document_templates'),
    path('templates/<int:template_id>/', views.template_detail, name='template_detail'),
    path('templates/create/', views.create_template, name='create_template'),
    path('queue/', views.document_queue, name='document_queue'),
    path('queue/<int:queue_id>/', views.queue_detail, name='queue_detail'),
    path('metrics/', views.document_metrics, name='document_metrics'),
    path('api/templates/list/', views.api_template_list, name='api_template_list'),
    path('api/queue/status/', views.api_queue_status, name='api_queue_status'),
    path('api/metrics/stats/', views.api_metrics_stats, name='api_metrics_stats'),
]