from django.contrib import admin
from .models import DocumentTemplate, GeneratedDocument, DocumentGenerationQueue, DocumentDownloadLog, DocumentProcessingMetrics

@admin.register(DocumentTemplate)
class DocumentTemplateAdmin(admin.ModelAdmin):
    list_display = ['name', 'document_type', 'is_active', 'estimated_generation_time', 'created_at']
    list_filter = ['document_type', 'is_active', 'created_at']
    search_fields = ['name', 'document_type', 'description']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('document_type', 'name', 'description', 'is_active')
        }),
        ('Template Configuration', {
            'fields': ('required_fields', 'optional_fields', 'estimated_generation_time', 'template_content')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

@admin.register(GeneratedDocument)
class GeneratedDocumentAdmin(admin.ModelAdmin):
    list_display = ['document_id', 'template', 'shipment_id', 'user', 'status', 'created_at']
    list_filter = ['status', 'template__document_type', 'created_at']
    search_fields = ['document_id', 'shipment__tracking_number', 'user__email']
    readonly_fields = ['document_id', 'created_at', 'updated_at', 'generation_started', 'generation_completed']
    
    fieldsets = (
        ('Document Information', {
            'fields': ('document_id', 'shipment_id', 'template', 'user', 'status')
        }),
        ('File Information', {
            'fields': ('file_path', 'file_size', 'expires_at')
        }),
        ('Generation Tracking', {
            'fields': ('generation_started', 'generation_completed'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

@admin.register(DocumentGenerationQueue)
class DocumentGenerationQueueAdmin(admin.ModelAdmin):
    list_display = ['id', 'document', 'priority', 'attempts', 'scheduled_at', 'started_at', 'completed_at']
    list_filter = ['priority', 'attempts', 'scheduled_at']
    search_fields = ['document__document_id', 'document__shipment__tracking_number']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('Queue Information', {
            'fields': ('document', 'priority', 'attempts', 'max_attempts')
        }),
        ('Scheduling', {
            'fields': ('scheduled_at', 'started_at', 'completed_at')
        }),
        ('Error Tracking', {
            'fields': ('error_message',),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

@admin.register(DocumentDownloadLog)
class DocumentDownloadLogAdmin(admin.ModelAdmin):
    list_display = ['document', 'user', 'downloaded_at', 'ip_address']
    list_filter = ['downloaded_at']
    search_fields = ['document__document_id', 'user__email', 'ip_address']
    readonly_fields = ['downloaded_at']
    
    fieldsets = (
        ('Download Information', {
            'fields': ('document', 'user', 'downloaded_at')
        }),
        ('Request Details', {
            'fields': ('ip_address', 'user_agent'),
            'classes': ('collapse',)
        }),
    )

@admin.register(DocumentProcessingMetrics)
class DocumentProcessingMetricsAdmin(admin.ModelAdmin):
    list_display = ['date', 'document_type', 'total_generated', 'successful_generations', 'failed_generations', 'avg_generation_time']
    list_filter = ['date', 'document_type']
    search_fields = ['document_type']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('Metrics Information', {
            'fields': ('date', 'document_type')
        }),
        ('Generation Statistics', {
            'fields': ('total_generated', 'successful_generations', 'failed_generations', 'avg_generation_time')
        }),
        ('Usage Statistics', {
            'fields': ('total_downloads',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )