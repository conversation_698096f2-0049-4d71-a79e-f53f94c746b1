"""
Encryption utilities for secure messaging
Handles AES-256-GCM encryption for messages and conversations
"""

import base64
import hashlib
import os
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.backends import default_backend
import json
import logging
from django.conf import settings

logger = logging.getLogger(__name__)

class MessageEncryption:
    """Handles encryption and decryption of messages"""
    
    def __init__(self, master_key=None):
        """
        Initialize encryption with a master key
        If no master key is provided, uses a default one (for development)
        """
        if master_key is None:
            # In production, this should come from environment variables
            self.master_key = b"cloverics-master-key-change-in-production-32bytes!!"
        else:
            self.master_key = master_key.encode() if isinstance(master_key, str) else master_key
        
        # Get encryption configuration from Django settings
        self.config = getattr(settings, 'ENCRYPTION_CONFIG', {})
        self.algorithm = self.config.get('ALGORITHM', 'AES-256-GCM')
        self.key_derivation_algorithm = self.config.get('KEY_DERIVATION_ALGORITHM', 'PBKDF2HMAC')
        self.key_derivation_iterations = self.config.get('KEY_DERIVATION_ITERATIONS', 100000)
        self.salt_length = self.config.get('SALT_LENGTH', 16)
        self.nonce_length = self.config.get('NONCE_LENGTH', 12)
        self.key_length = self.config.get('KEY_LENGTH', 32)
        self.auth_tag_length = self.config.get('AUTHENTICATION_TAG_LENGTH', 16)
    
    def generate_key(self, salt=None):
        """Generate a unique encryption key for a conversation"""
        if salt is None:
            salt = os.urandom(self.salt_length)
        
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=self.key_length,
            salt=salt,
            iterations=self.key_derivation_iterations,
            backend=default_backend()
        )
        
        key = kdf.derive(self.master_key)
        return key, salt
    
    def encrypt_message(self, message_content, conversation_key_id=None):
        """
        Encrypt a message using AES-256-GCM
        
        Args:
            message_content (str): The message to encrypt
            conversation_key_id (str): Optional conversation key ID for key derivation
            
        Returns:
            dict: Contains encrypted_data, nonce (salt is deterministic for conversations)
        """
        try:
            # Convert message to bytes
            message_bytes = message_content.encode('utf-8')
            
            # Generate or derive key
            if conversation_key_id:
                # Use conversation key ID as salt for consistent key derivation
                salt = hashlib.sha256(conversation_key_id.encode()).digest()[:self.salt_length]
                key, _ = self.generate_key(salt)
                # Don't store salt for conversations (can be regenerated)
                store_salt = False
            else:
                key, salt = self.generate_key()
                store_salt = True
            
            # Generate nonce for GCM mode
            nonce = os.urandom(self.nonce_length)
            
            # Create cipher
            cipher = Cipher(
                algorithms.AES(key),
                modes.GCM(nonce),
                backend=default_backend()
            )
            
            # Encrypt
            encryptor = cipher.encryptor()
            ciphertext = encryptor.update(message_bytes) + encryptor.finalize()
            
            # Get authentication tag
            tag = encryptor.tag
            
            # Combine ciphertext and tag
            encrypted_data = ciphertext + tag
            
            result = {
                'encrypted_data': base64.b64encode(encrypted_data).decode('utf-8'),
                'nonce': base64.b64encode(nonce).decode('utf-8'),
            }
            
            # Only store salt for standalone messages (not conversations)
            if store_salt:
                result['salt'] = base64.b64encode(salt).decode('utf-8')
            
            return result
            
        except Exception as e:
            logger.error(f"Error encrypting message: {e}")
            raise
    
    def decrypt_message(self, encrypted_data, nonce, salt=None, conversation_key_id=None):
        """
        Decrypt a message using AES-256-GCM
        
        Args:
            encrypted_data (str): Base64 encoded encrypted data
            nonce (str): Base64 encoded nonce used for encryption
            salt (str): Base64 encoded salt (only for standalone messages)
            conversation_key_id (str): Optional conversation key ID for key derivation
            
        Returns:
            str: Decrypted message content
        """
        try:
            # Decode base64 data
            encrypted_bytes = base64.b64decode(encrypted_data)
            nonce_bytes = base64.b64decode(nonce)
            
            # Separate ciphertext and tag
            ciphertext = encrypted_bytes[:-self.auth_tag_length]
            tag = encrypted_bytes[-self.auth_tag_length:]
            
            # Derive key
            if conversation_key_id:
                # Use conversation key ID as salt for consistent key derivation
                derived_salt = hashlib.sha256(conversation_key_id.encode()).digest()[:self.salt_length]
                key, _ = self.generate_key(derived_salt)
            elif salt:
                # Use provided salt for standalone messages
                salt_bytes = base64.b64decode(salt)
                key, _ = self.generate_key(salt_bytes)
            else:
                raise ValueError("Either salt or conversation_key_id must be provided")
            
            # Create cipher
            cipher = Cipher(
                algorithms.AES(key),
                modes.GCM(nonce_bytes, tag),
                backend=default_backend()
            )
            
            # Decrypt
            decryptor = cipher.decryptor()
            plaintext = decryptor.update(ciphertext) + decryptor.finalize()
            
            return plaintext.decode('utf-8')
            
        except Exception as e:
            logger.error(f"Error decrypting message: {e}")
            raise
    
    def encrypt_for_storage(self, data):
        """
        Encrypt data for storage in database
        Returns a JSON string with encrypted data and metadata
        """
        if isinstance(data, str):
            encrypted = self.encrypt_message(data)
        else:
            # Convert dict/list to JSON string first
            json_data = json.dumps(data)
            encrypted = self.encrypt_message(json_data)
        
        return json.dumps(encrypted)
    
    def decrypt_from_storage(self, encrypted_json):
        """
        Decrypt data from storage
        Expects a JSON string with encrypted data and metadata
        """
        try:
            encrypted_data = json.loads(encrypted_json)
            decrypted = self.decrypt_message(
                encrypted_data['encrypted_data'],
                encrypted_data['nonce'],
                salt=encrypted_data.get('salt')  # Optional for conversations
            )
            
            # Try to parse as JSON, if it fails return as string
            try:
                return json.loads(decrypted)
            except json.JSONDecodeError:
                return decrypted
                
        except Exception as e:
            logger.error(f"Error decrypting from storage: {e}")
            raise

# Global encryption instance
encryption = MessageEncryption() 