"""
Advanced Features Admin - Django admin interface
"""

from django.contrib import admin
from .models import (
    CrossUserEvent, AIAnalyticsCache,
    DriverAssignment, DriverPerformanceMetrics
)

# WebSocketConnection admin removed - WebSocket functionality disabled

@admin.register(CrossUserEvent)
class CrossUserEventAdmin(admin.ModelAdmin):
    list_display = ['event_type', 'source_user', 'shipment_id', 'created_at']
    list_filter = ['event_type', 'created_at']
    search_fields = ['source_user__email', 'event_type']
    readonly_fields = ['created_at']

@admin.register(AIAnalyticsCache)
class AIAnalyticsCacheAdmin(admin.ModelAdmin):
    list_display = ['analytics_type', 'user', 'generated_at', 'expires_at', 'confidence_score']
    list_filter = ['analytics_type', 'generated_at']
    search_fields = ['analytics_type', 'user__email']
    readonly_fields = ['generated_at']

@admin.register(DriverAssignment)
class DriverAssignmentAdmin(admin.ModelAdmin):
    list_display = ['shipment_id', 'driver', 'assigned_by', 'status', 'assigned_at']
    list_filter = ['status', 'assigned_at']
    search_fields = ['driver__email', 'assigned_by__email']
    readonly_fields = ['assigned_at']

@admin.register(DriverPerformanceMetrics)
class DriverPerformanceMetricsAdmin(admin.ModelAdmin):
    list_display = ['driver', 'total_assignments', 'completion_rate', 'average_rating', 'updated_at']
    search_fields = ['driver__email']
    readonly_fields = ['updated_at']
    
    def completion_rate(self, obj):
        if obj.total_assignments > 0:
            return f"{(obj.completed_assignments / obj.total_assignments * 100):.1f}%"
        return "0%"
    completion_rate.short_description = "Completion Rate"