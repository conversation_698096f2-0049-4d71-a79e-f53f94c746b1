"""
Advanced Features Views - Extracted from fastapi_main.py Phase 5
Contains: AI analytics, Driver assignment
"""

from django.shortcuts import render
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.db import transaction
from datetime import datetime, timedelta
import json
import logging

from .models import (
    CrossUserEvent, AIAnalyticsCache, 
    DriverAssignment, DriverPerformanceMetrics
)

User = get_user_model()
logger = logging.getLogger(__name__)

def test_dashboard(request):
    """Test dashboard for advanced features"""
    return render(request, 'advanced_features/test_dashboard.html')

def admin_required(view_func):
    """Decorator to require admin user"""
    def wrapper(request, *args, **kwargs):
        if not request.user.is_authenticated or request.user.user_type != 'ADMIN':
            return JsonResponse({'error': 'Admin access required'}, status=403)
        return view_func(request, *args, **kwargs)
    return wrapper

# =======================
# WEBSOCKET NOTIFICATIONS - REMOVED
# =======================

# =======================
# AI ANALYTICS - Extracted from lines 4630-4687
# =======================

@csrf_exempt
@require_http_methods(["GET"])
def ai_comprehensive_analytics(request):
    """Get comprehensive AI analytics - extracted from fastapi_main.py lines 4630-4659"""
    try:
        # Check cache first
        cache_key = 'comprehensive_analytics'
        cached_result = AIAnalyticsCache.objects.filter(
            analytics_type='comprehensive',
            expires_at__gt=timezone.now()
        ).first()
        
        if cached_result:
            return JsonResponse({
                'status': 'success', 
                'data': cached_result.results,
                'cached': True,
                'generated_at': cached_result.generated_at.isoformat()
            })
        
        # Generate new analytics (extracted logic from lines 4634-4656)
        analytics_data = {
            "demand_forecast": {
                "trend": "increasing",
                "confidence": 0.87,
                "next_month_prediction": 1250,
                "seasonal_factors": ["holiday_season", "economic_growth"]
            },
            "price_prediction": {
                "average_increase": 0.12,
                "market_volatility": "moderate", 
                "recommended_adjustments": ["fuel_surcharge", "peak_season_rates"]
            },
            "route_optimization": {
                "efficiency_gain": 0.23,
                "cost_savings": 1890.50,
                "optimized_routes": 15
            },
            "market_intelligence": {
                "competitor_analysis": "favorable",
                "market_position": "strong",
                "growth_opportunities": 3
            }
        }
        
        # Cache the result for 1 hour
        AIAnalyticsCache.objects.create(
            analytics_type='comprehensive',
            results=analytics_data,
            expires_at=timezone.now() + timedelta(hours=1),
            confidence_score=0.87
        )
        
        return JsonResponse({
            'status': 'success', 
            'data': analytics_data,
            'cached': False,
            'generated_at': timezone.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"AI analytics error: {e}")
        return JsonResponse({'status': 'error', 'message': str(e)}, status=500)

@csrf_exempt
@require_http_methods(["GET"])
def ai_demand_forecast(request):
    """Get demand forecast - extracted from fastapi_main.py lines 4661-4686"""
    try:
        period = request.GET.get('period', '30_days')
        route = request.GET.get('route', 'all')
        
        # Check cache
        cache_params = {'period': period, 'route': route}
        cached_result = AIAnalyticsCache.objects.filter(
            analytics_type='demand_forecast',
            parameters=cache_params,
            expires_at__gt=timezone.now()
        ).first()
        
        if cached_result:
            return JsonResponse({
                'status': 'success',
                'data': cached_result.results,
                'cached': True
            })
        
        # Generate forecast (extracted from lines 4669-4683)
        forecast_data = {
            "period": period,
            "route": route,
            "forecast": {
                "current_demand": 1200,
                "predicted_demand": 1350,
                "growth_rate": 0.125,
                "confidence_interval": [1280, 1420],
                "factors": [
                    {"name": "seasonal", "impact": 0.15},
                    {"name": "economic", "impact": 0.08},
                    {"name": "competition", "impact": -0.03}
                ]
            }
        }
        
        # Cache for 30 minutes
        AIAnalyticsCache.objects.create(
            analytics_type='demand_forecast',
            parameters=cache_params,
            results=forecast_data,
            expires_at=timezone.now() + timedelta(minutes=30),
            confidence_score=0.84
        )
        
        return JsonResponse({'status': 'success', 'data': forecast_data})
        
    except Exception as e:
        logger.error(f"Demand forecast error: {e}")
        return JsonResponse({'status': 'error', 'message': str(e)}, status=500)

# =======================
# DRIVER ASSIGNMENT - Extracted from lines 14299-14405
# =======================

@csrf_exempt
@require_http_methods(["POST"])
def assign_driver_to_shipment(request):
    """Assign optimal driver to shipment - extracted from lines 14299-14333"""
    try:
        data = json.loads(request.body)
        
        # Extract shipment details (from lines 14305-14314)
        shipment_details = {
            'weight_kg': data.get('weight_kg', 1000),
            'pickup_latitude': data.get('pickup_latitude'),
            'pickup_longitude': data.get('pickup_longitude'),
            'pickup_address': data.get('pickup_address', ''),
            'delivery_latitude': data.get('delivery_latitude'),
            'delivery_longitude': data.get('delivery_longitude'),
            'delivery_address': data.get('delivery_address', ''),
            'urgency_level': data.get('urgency_level', 1)
        }
        
        shipment_id = data.get('shipment_id')
        
        if not shipment_id:
            return JsonResponse({
                'success': False,
                'error': 'Shipment ID is required'
            }, status=400)
        
        # Find available drivers based on criteria
        available_drivers = User.objects.filter(
            user_type='INDEPENDENT_DRIVER',
            is_active=True
        )
        
        if not available_drivers.exists():
            return JsonResponse({
                'success': False,
                'error': 'No available drivers found'
            })
        
        # Simple assignment logic - assign to first available driver
        # In real implementation, this would use sophisticated matching algorithm
        selected_driver = available_drivers.first()
        
        # Create driver assignment
        assignment = DriverAssignment.objects.create(
            shipment_id=shipment_id,
            driver=selected_driver,
            assigned_by_id=request.user.id if request.user.is_authenticated else 1,
            estimated_pickup_time=timezone.now() + timedelta(hours=2),
            estimated_delivery_time=timezone.now() + timedelta(days=1),
            assignment_criteria=shipment_details
        )
        
        # Update driver performance metrics
        metrics, created = DriverPerformanceMetrics.objects.get_or_create(
            driver=selected_driver,
            defaults={'total_assignments': 0}
        )
        metrics.total_assignments += 1
        metrics.save()
        
        return JsonResponse({
            'success': True,
            'assignment_id': assignment.id,
            'driver_id': selected_driver.id,
            'driver_name': f"{selected_driver.first_name} {selected_driver.last_name}",
            'estimated_pickup': assignment.estimated_pickup_time.isoformat(),
            'estimated_delivery': assignment.estimated_delivery_time.isoformat()
        })
        
    except Exception as e:
        logger.error(f"Driver assignment error: {e}")
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)

@csrf_exempt  
@require_http_methods(["GET"])
def get_shipment_tracking(request, shipment_id):
    """Get real-time tracking data - extracted from lines 14335-14347"""
    try:
        # Get driver assignment for shipment
        assignment = DriverAssignment.objects.filter(
            shipment_id=shipment_id
        ).select_related('driver').first()
        
        if not assignment:
            return JsonResponse({
                'error': 'No driver assigned to this shipment'
            }, status=404)
        
        # Mock tracking data (extracted pattern from line 14339)
        tracking_data = {
            'shipment_id': shipment_id,
            'driver_id': assignment.driver.id,
            'driver_name': f"{assignment.driver.first_name} {assignment.driver.last_name}",
            'status': assignment.status,
            'current_location': {
                'latitude': 41.0082,
                'longitude': 28.9784,
                'address': 'Istanbul, Turkey'
            },
            'estimated_arrival': assignment.estimated_delivery_time.isoformat(),
            'last_update': timezone.now().isoformat()
        }
        
        return JsonResponse(tracking_data)
        
    except Exception as e:
        logger.error(f"Tracking data error: {e}")
        return JsonResponse({'error': str(e)}, status=500)

@admin_required
@csrf_exempt
@require_http_methods(["GET"])
def driver_performance_analytics(request):
    """Get driver performance analytics for admin"""
    try:
        # Get all driver performance metrics
        metrics = DriverPerformanceMetrics.objects.select_related('driver').all()
        
        analytics_data = []
        for metric in metrics:
            analytics_data.append({
                'driver_id': metric.driver.id,
                'driver_name': f"{metric.driver.first_name} {metric.driver.last_name}",
                'total_assignments': metric.total_assignments,
                'completion_rate': (metric.completed_assignments / metric.total_assignments * 100) if metric.total_assignments > 0 else 0,
                'average_rating': metric.average_rating,
                'on_time_delivery_rate': metric.on_time_delivery_rate,
                'total_distance_km': metric.total_distance_km,
                'safety_score': metric.safety_score
            })
        
        return JsonResponse({
            'success': True,
            'data': analytics_data,
            'total_drivers': len(analytics_data)
        })
        
    except Exception as e:
        logger.error(f"Driver analytics error: {e}")
        return JsonResponse({'error': str(e)}, status=500)