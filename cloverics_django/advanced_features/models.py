"""
Advanced Features Models - Extracted from fastapi_main.py Phase 5
Contains: AI analytics, Driver assignment
"""

from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
import json

User = get_user_model()

# WebSocketConnection model removed - WebSocket functionality disabled

class CrossUserEvent(models.Model):
    """Audit trail for cross-user events and notifications"""
    EVENT_TYPES = [
        ('driver_assignment', 'Driver Assignment'),
        ('customs_declaration', 'Customs Declaration'),
        ('shipment_update', 'Shipment Update'),
        ('payment_processed', 'Payment Processed'),
        ('insurance_claim', 'Insurance Claim'),
        ('private_shipment', 'Private Shipment'),
        ('container_sharing', 'Container Sharing'),
    ]
    
    event_type = models.CharField(max_length=50, choices=EVENT_TYPES)
    source_user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='initiated_events')
    target_users = models.ManyToManyField(User, related_name='received_events')
    shipment_id = models.IntegerField(null=True, blank=True)
    event_data = models.JSONField(default=dict)
    created_at = models.DateTimeField(default=timezone.now)
    # websocket_sent = models.BooleanField(default=False)  # Removed - WebSocket functionality disabled
    database_notified = models.BooleanField(default=False)
    
    class Meta:
        db_table = 'cross_user_events'
        indexes = [
            models.Index(fields=['event_type', 'created_at']),
            models.Index(fields=['source_user', 'created_at']),
            models.Index(fields=['shipment_id']),
        ]

class AIAnalyticsCache(models.Model):
    """Cache AI analytics results for improved performance"""
    ANALYTICS_TYPES = [
        ('demand_forecast', 'Demand Forecast'),
        ('price_prediction', 'Price Prediction'),
        ('route_optimization', 'Route Optimization'),
        ('market_intelligence', 'Market Intelligence'),
        ('comprehensive', 'Comprehensive Analytics'),
    ]
    
    analytics_type = models.CharField(max_length=50, choices=ANALYTICS_TYPES)
    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)
    parameters = models.JSONField(default=dict)  # Store query parameters
    results = models.JSONField(default=dict)     # Store analytics results
    generated_at = models.DateTimeField(default=timezone.now)
    expires_at = models.DateTimeField()
    confidence_score = models.FloatField(default=0.0)
    
    class Meta:
        db_table = 'ai_analytics_cache'
        indexes = [
            models.Index(fields=['analytics_type', 'expires_at']),
            models.Index(fields=['user', 'analytics_type']),
        ]

class DriverAssignment(models.Model):
    """Track driver assignments and performance"""
    ASSIGNMENT_STATUS = [
        ('pending', 'Pending'),
        ('accepted', 'Accepted'),
        ('in_transit', 'In Transit'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
    ]
    
    shipment_id = models.IntegerField()
    driver = models.ForeignKey(User, on_delete=models.CASCADE)
    assigned_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='driver_assignments_made')
    assigned_at = models.DateTimeField(default=timezone.now)
    status = models.CharField(max_length=20, choices=ASSIGNMENT_STATUS, default='pending')
    estimated_pickup_time = models.DateTimeField()
    estimated_delivery_time = models.DateTimeField()
    actual_pickup_time = models.DateTimeField(null=True, blank=True)
    actual_delivery_time = models.DateTimeField(null=True, blank=True)
    driver_rating = models.FloatField(null=True, blank=True)  # 1-5 scale
    assignment_criteria = models.JSONField(default=dict)  # Store matching criteria used
    gps_tracking_data = models.JSONField(default=dict)   # Store GPS coordinates
    
    class Meta:
        db_table = 'driver_assignments'
        indexes = [
            models.Index(fields=['shipment_id']),
            models.Index(fields=['driver', 'status']),
            models.Index(fields=['assigned_at']),
        ]

class DriverPerformanceMetrics(models.Model):
    """Track driver performance metrics for optimization"""
    driver = models.OneToOneField(User, on_delete=models.CASCADE)
    total_assignments = models.IntegerField(default=0)
    completed_assignments = models.IntegerField(default=0)
    cancelled_assignments = models.IntegerField(default=0)
    average_rating = models.FloatField(default=0.0)
    total_distance_km = models.FloatField(default=0.0)
    total_hours = models.FloatField(default=0.0)
    on_time_delivery_rate = models.FloatField(default=0.0)  # Percentage
    fuel_efficiency_rating = models.FloatField(default=0.0)
    safety_score = models.FloatField(default=0.0)
    customer_satisfaction = models.FloatField(default=0.0)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'driver_performance_metrics'
        indexes = [
            models.Index(fields=['average_rating']),
            models.Index(fields=['on_time_delivery_rate']),
        ]