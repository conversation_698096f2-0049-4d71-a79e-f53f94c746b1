"""
Advanced Features URLs - Extracted from fastapi_main.py Phase 5
Contains: AI Analytics, Driver Assignment endpoints
"""

from django.urls import path
from . import views

app_name = 'advanced_features'

urlpatterns = [
    # Test dashboard
    path('test/', views.test_dashboard, name='test_dashboard'),
    
    # WebSocket Notifications - REMOVED
    
    # AI Analytics - extracted from lines 4630-4687
    path('api/ai/comprehensive-analytics/', views.ai_comprehensive_analytics, name='ai_comprehensive_analytics'),
    path('api/ai/demand-forecast/', views.ai_demand_forecast, name='ai_demand_forecast'),
    
    # Driver Assignment - extracted from lines 14299-14405
    path('api/driver/assign-shipment/', views.assign_driver_to_shipment, name='assign_driver_to_shipment'),
    path('api/driver/tracking/<int:shipment_id>/', views.get_shipment_tracking, name='get_shipment_tracking'),
    path('api/driver/performance-analytics/', views.driver_performance_analytics, name='driver_performance_analytics'),
]