"""
Temporary URLs configuration for Phase 7 - Core API & Authentication System
Minimal configuration to enable migration and testing during refactoring
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static

urlpatterns = [
    # Admin interface
    path('admin/', admin.site.urls),
    
    # Phase 7: Core API & Authentication System
    path('', include('apps.core_api.urls')),
    path('auth/', include('apps.authentication.urls')),
    
    # CRITICAL: API endpoints that were missing
    path('api/', include('apps.api.urls')),
    
    # WebSocket endpoints for real-time features - REMOVED
    
    # User Dashboard Routes - Role-based routing
    path('customer/', include('apps.customers.urls_minimal')),
    path('logistics/', include('apps.logistics.urls_basic')),
    path('admin-panel/', include('apps.admin_panel.urls_basic')),
    path('customs/', include('apps.customs.urls_basic')),
    path('insurance/', include('apps.insurance.urls_basic')),
    path('drivers/', include('apps.drivers.urls_basic')),
]

# Serve static and media files in development
if settings.DEBUG:
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)