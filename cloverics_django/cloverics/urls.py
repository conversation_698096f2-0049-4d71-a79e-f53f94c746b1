"""
URL configuration for cloverics project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from django.views.generic import TemplateView

urlpatterns = [
    # Admin interface
    path('admin/', admin.site.urls),
    
    # Core infrastructure - Phase 7 extracted (landing, auth, API)
    path('', include('apps.core_api.urls')),
    
    # Phase 8: Customer Management System
    path('customer/', include('apps.customers.urls')),
    
    # Phase 9: Advanced Quotes System  
    # path('', include('apps.advanced_quotes.urls')),  # COMMENTED: Authentication issues causing redirects
    
    # Authentication URLs - Phase 2 re-enabled
    path('auth/', include('apps.authentication.urls')),
    # path('logistics/', include('apps.logistics.urls')),  # COMMENTED: fleet_management missing
    
    # Admin panel module - Phase 4 extracted
    # path('admin-panel/', include('apps.admin_panel.urls')),  # COMMENTED: views need fixing
    # path('advanced/', include('advanced_features.urls')),  # COMMENTED: module doesn't exist
    
    # Phase 3 & 4 extracted apps - COMMENTED until views are fixed
    path('notifications/', include('apps.notifications.urls')),  # ENABLED: notifications page
    # path('', include('apps.analytics.urls')),  # COMMENTED: views need verification  
    # path('', include('apps.market_intelligence.urls')),  # COMMENTED: views need verification
    # path('', include('apps.websockets.urls')),  # REMOVED: WebSocket functionality disabled
    # path('', include('apps.enterprise_integration.urls')),  # COMMENTED: views need verification
    # path('', include('apps.logistics_advanced.urls')),  # COMMENTED: views need verification
    
    # Phase 12: Shipment Processing & Workflow System - COMMENTED until views are fixed
    # path('shipment-processing/', include('apps.shipment_processing.urls')),  # COMMENTED: views need verification
    
    # Phase 13: Security & Authentication System - COMMENTED until views are fixed
    # path('security/', include('apps.security_system.urls')),  # COMMENTED: views need verification
    
    # Phase 14: Document Processing & Generation System - COMMENTED until views are fixed
    # path('document-processing/', include('apps.document_processing.urls')),  # COMMENTED: views need verification
    # path('ai-analytics/', include('apps.ai_analytics.urls')),  # COMMENTED: views need verification
    
    # Phase 15: Logistics Operations System - COMMENTED until views are fixed
    # path('logistics-operations/', include('apps.logistics_operations.urls')),  # COMMENTED: views need verification
    
    # Phase 2 URL Completion: Re-enabling extracted modules - COMMENTED until views are fixed
    # path('customs/', include('apps.customs.urls')),  # COMMENTED: views need verification
    # path('customs-advanced/', include('apps.customs_advanced.urls')),  # COMMENTED: views need verification
    # path('insurance/', include('apps.insurance.urls')),  # COMMENTED: views need verification
    # path('drivers/', include('apps.drivers.urls')),  # COMMENTED: views need verification
    # path('payments/', include('apps.payments.urls')),  # COMMENTED: views need verification
    
    # API endpoints - Phase 2 completion
    path('api/', include('apps.api.urls')),  # KEEP: Critical for authentication and real-time features
    
    # Additional Phase 2 URL completions - COMMENTED until views are fixed
    path('messaging/', include('apps.messaging.urls')),  # ENABLED: Complete chat system
    # path('performance/', include('apps.performance_scorecards.urls')),  # COMMENTED: views need verification
    # path('saved-searches/', include('apps.saved_searches.urls')),  # COMMENTED: views need verification
    
    # Phase 3: Core shipments module - COMMENTED until views are fixed
    # path('shipments/', include('shipments.urls')),  # COMMENTED: views need verification
    
    # Phase 3: WebSocket routing - REMOVED
    # path('ws/', include('apps.websockets.urls')),  # REMOVED: WebSocket functionality disabled
    
    # WebSocket routing - REMOVED
]

# Serve static and media files in development
if settings.DEBUG:
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)

# Custom error handlers (temporarily disabled during extraction)
# handler404 = 'apps.authentication.views.handler404'
# handler500 = 'apps.authentication.views.handler500'