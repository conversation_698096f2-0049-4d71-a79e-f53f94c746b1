"""
Django settings for cloverics project.

Generated by 'django-admin startproject' using Django 5.2.1.

For more information on this file, see
https://docs.djangoproject.com/en/5.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.2/ref/settings/
"""

import os
from pathlib import Path

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = os.environ.get('SECRET_KEY', 'django-insecure-development-key-change-in-production')

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = os.environ.get('DEBUG', 'True') == 'True'

ALLOWED_HOSTS = ['*']  # Configure properly for production

# CSRF Trusted Origins (for Replit deployment)
CSRF_TRUSTED_ORIGINS = [
    'https://*.replit.dev',
    'https://*.replit.app',
    'http://localhost:8000',
    'http://127.0.0.1:8000',
]

# Application definition
INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    
    # Third party apps
    'rest_framework',
    # 'rest_framework.authtoken',
    # 'channels',
    # 'corsheaders',
    
    # FastAPI extracted apps (primary) - Re-enabling with fixes
          # Shipments module - User imports fixed
    # Cloverics apps (secondary) - Temporarily minimal during Phase 7 setup
    'apps.shipments',
    'apps.authentication',  # Re-enabled for Phase 7 - Core User model
    'apps.customers',  # Phase 8 - Customer Management System
    'apps.advanced_quotes',  # Phase 9 - Advanced Quotes System
    'apps.market_intelligence',  # Phase 10 - Market Intelligence & Analytics System
    'apps.customs_advanced',  # Phase 11 - Advanced Customs Processing System
    'apps.shipment_processing',  # Phase 12 - Shipment Processing & Workflow System
    'apps.security_system',  # Phase 13 - Security & Authentication System
    'apps.document_processing',  # Phase 14 - Document Processing & Generation System
    'apps.ai_analytics',  # Phase 14 - AI Analytics & Intelligence System
    'apps.saved_searches',  # Phase 14 - Saved Searches & Price Alerts System
    'apps.performance_scorecards',  # Phase 14 - Performance Scorecards System
    'apps.invoice_reconciliation',  # Phase 14 - Invoice Reconciliation System
    'apps.logistics_operations',  # Phase 15 - Logistics Operations System
    # Re-enabled for sidebar navigation system (template loading only):
    'apps.logistics',      # Logistics provider dashboard
    # Progressive app enabling - Phase 1 Complete: Model conflicts resolved
    'apps.admin_panel',    # Admin dashboard  
    'apps.customs',        # Customs agent dashboard
    'apps.insurance',      # Insurance provider dashboard
    'apps.drivers',        # Independent driver dashboard
    'apps.payments',       # Payment processing
    
    # Phase 2: Re-enabling remaining apps with fixed User imports
    'apps.notifications',  # Notification system
    'apps.analytics',      # Analytics system
    'apps.enterprise_integration',  # Enterprise features
    'apps.logistics_advanced',      # Advanced logistics
    'apps.api',           # API endpoints
    'apps.core_api',      # Core API system - Phase 7
    'apps.messaging',     # Secure messaging system
]

MIDDLEWARE = [
    # 'corsheaders.middleware.CorsMiddleware',
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    # Authentication middleware temporarily disabled during extraction
    # 'apps.authentication.middleware.JWTAuthenticationMiddleware',
    # 'apps.authentication.middleware.SecurityHeadersMiddleware',
    # 'apps.authentication.middleware.SessionTrackingMiddleware',
]

ROOT_URLCONF = 'cloverics.urls'  # Full URL configuration with commented broken routes

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [BASE_DIR / 'templates'],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
                'apps.authentication.context_processors.user_context',
                'apps.authentication.context_processors.translation_context',
            ],
        },
    },
]

WSGI_APPLICATION = 'cloverics.wsgi.application'
# ASGI_APPLICATION = 'cloverics.asgi.application'

# Database
# https://docs.djangoproject.com/en/5.2/ref/settings/#databases

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': os.environ.get('PGDATABASE', 'cloverics'),
        'USER': os.environ.get('PGUSER', 'postgres'),
        'PASSWORD': os.environ.get('PGPASSWORD', 'opzx1254/*-+.'),
        'HOST': os.environ.get('PGHOST', 'localhost'),
        'PORT': os.environ.get('PGPORT', '5432'),
        'OPTIONS': {
            'sslmode': 'prefer',
        },
        'CONN_MAX_AGE': 60,
        'CONN_HEALTH_CHECKS': True,
    }
}

# Authentication
AUTH_USER_MODEL = 'authentication.User'

# Ensure proper User model handling
AUTHENTICATION_BACKENDS = [
    'apps.authentication.backends.EmailOrUsernameModelBackend',
    'django.contrib.auth.backends.ModelBackend',  # Fallback
]

# JWT Configuration for mobile compatibility
JWT_AUTH = {
    'JWT_SECRET_KEY': SECRET_KEY,
    'JWT_ALGORITHM': 'HS256',
    'JWT_EXPIRATION_DELTA': 60 * 60 * 24 * 7,  # 7 days in seconds
    'JWT_ALLOW_REFRESH': True,
}

# Encryption Configuration for secure messaging
ENCRYPTION_CONFIG = {
    'ALGORITHM': os.environ.get('ENCRYPTION_ALGORITHM', 'AES-256-GCM'),
    'KEY_DERIVATION_ALGORITHM': os.environ.get('KEY_DERIVATION_ALGORITHM', 'PBKDF2HMAC'),
    'KEY_DERIVATION_ITERATIONS': int(os.environ.get('KEY_DERIVATION_ITERATIONS', '100000')),
    'SALT_LENGTH': int(os.environ.get('SALT_LENGTH', '16')),  # 16 bytes = 128 bits
    'NONCE_LENGTH': int(os.environ.get('NONCE_LENGTH', '12')),  # 12 bytes = 96 bits for AES-GCM
    'KEY_LENGTH': int(os.environ.get('KEY_LENGTH', '32')),  # 32 bytes = 256 bits for AES-256
    'AUTHENTICATION_TAG_LENGTH': int(os.environ.get('AUTHENTICATION_TAG_LENGTH', '16')),  # 16 bytes for GCM
}

# Django REST Framework
REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': [
        'apps.authentication.authentication.JWTAuthentication',
        'rest_framework.authentication.SessionAuthentication',
    ],
    'DEFAULT_PERMISSION_CLASSES': [
        'rest_framework.permissions.IsAuthenticated',
    ],
    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.PageNumberPagination',
    'PAGE_SIZE': 20,
}

# Channels Configuration for WebSocket - REMOVED

# Password validation
# https://docs.djangoproject.com/en/5.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

# Authentication settings
LOGIN_URL = '/auth/login/'
LOGIN_REDIRECT_URL = '/dashboard/'
LOGOUT_REDIRECT_URL = '/'

# Internationalization
# https://docs.djangoproject.com/en/5.2/topics/i18n/

LANGUAGE_CODE = 'en-us'
TIME_ZONE = 'UTC'
USE_I18N = True
USE_TZ = True

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/5.2/howto/static-files/

STATIC_URL = '/static/'
STATIC_ROOT = BASE_DIR / 'staticfiles'
STATICFILES_DIRS = [
    BASE_DIR / 'static',
]

# Media files
MEDIA_URL = '/media/'
MEDIA_ROOT = BASE_DIR / 'media'

# Default primary key field type
# https://docs.djangoproject.com/en/5.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# CORS settings
CORS_ALLOW_ALL_ORIGINS = DEBUG
CORS_ALLOWED_ORIGINS = [
    "http://localhost:3000",
    "http://127.0.0.1:3000",
    "http://localhost:5000",
    "http://127.0.0.1:5000",
]

# Security settings
if not DEBUG:
    SECURE_SSL_REDIRECT = True
    SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')
    SECURE_HSTS_SECONDS = 31536000
    SECURE_HSTS_INCLUDE_SUBDOMAINS = True
    SECURE_HSTS_PRELOAD = True
    SECURE_CONTENT_TYPE_NOSNIFF = True
    SECURE_BROWSER_XSS_FILTER = True
    X_FRAME_OPTIONS = 'DENY'

# Logging configuration
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': BASE_DIR / 'django.log',
            'formatter': 'verbose',
        },
        'console': {
            'level': 'INFO',
            'class': 'logging.StreamHandler',
            'formatter': 'verbose',
        },
    },
    'loggers': {
        'django': {
            'handlers': ['file', 'console'],
            'level': 'INFO',
            'propagate': True,
        },
        'cloverics': {
            'handlers': ['file', 'console'],
            'level': 'INFO',
            'propagate': True,
        },
    },
}

# Email configuration
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = os.environ.get('EMAIL_HOST', 'smtp.gmail.com')
EMAIL_PORT = int(os.environ.get('EMAIL_PORT', '587'))
EMAIL_USE_TLS = True
EMAIL_HOST_USER = os.environ.get('EMAIL_HOST_USER', '')
EMAIL_HOST_PASSWORD = os.environ.get('EMAIL_HOST_PASSWORD', '')
DEFAULT_FROM_EMAIL = os.environ.get('DEFAULT_FROM_EMAIL', EMAIL_HOST_USER)

# Session settings
SESSION_COOKIE_AGE = 60 * 60 * 24 * 7  # 7 days
SESSION_SAVE_EVERY_REQUEST = True
SESSION_EXPIRE_AT_BROWSER_CLOSE = False
SESSION_COOKIE_SECURE = False  # Set to True in production with HTTPS
SESSION_COOKIE_HTTPONLY = True
SESSION_COOKIE_SAMESITE = 'Lax'
SESSION_COOKIE_NAME = 'sessionid'
SESSION_ENGINE = 'django.contrib.sessions.backends.db'
SESSION_FILE_PATH = None
SESSION_SERIALIZER = 'django.contrib.sessions.serializers.JSONSerializer'

# Cache configuration (disabled for now)
# CACHES = {
#     'default': {
#         'BACKEND': 'django.core.cache.backends.redis.RedisCache',
#         'LOCATION': 'redis://127.0.0.1:6379/1',
#     }
# }