
import os
import shutil

def delete_pycache_folders():
    """
    Deletes all __pycache__ folders in the current directory and its subdirectories.
    """
    for dirpath, dirnames, filenames in os.walk('.'):
        if '__pycache__' in dirnames:
            pycache_path = os.path.join(dirpath, '__pycache__')
            print(f'Deleting {pycache_path}')
            shutil.rmtree(pycache_path)

if __name__ == "__main__":
    delete_pycache_folders()
