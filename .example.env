# =============================================================================
# CLOVERICS - GLOBAL LOGISTICS PLATFORM
# Environment Variables Configuration
# =============================================================================
# Copy this file to .env and fill in your actual values
# Never commit the actual .env file to version control

# =============================================================================
# DJANGO CORE SETTINGS
# =============================================================================

# Django Secret Key (generate a new one for production)
# You can generate one using: python -c "from django.core.management.utils import get_random_secret_key; print(get_random_secret_key())"
SECRET_KEY=your-secret-key-here-change-in-production

# Debug Mode (True for development, False for production)
DEBUG=True

# =============================================================================
# DATABASE CONFIGURATION (PostgreSQL)
# =============================================================================

# PostgreSQL Database Settings
PGDATABASE=cloverics
PGUSER=postgres
PGPASSWORD=your-database-password
PGHOST=localhost
PGPORT=5432

# =============================================================================
# EMAIL CONFIGURATION
# =============================================================================

# SMTP Server Settings
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password
DEFAULT_FROM_EMAIL=<EMAIL>

# =============================================================================
# PAYMENT PROCESSING (STRIPE)
# =============================================================================

# Stripe API Keys
# Get these from your Stripe Dashboard: https://dashboard.stripe.com/apikeys
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key_here
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key_here

# =============================================================================
# AI SERVICES INTEGRATION
# =============================================================================

# OpenAI API (for AI analytics and insights)
# Get from: https://platform.openai.com/api-keys
OPENAI_API_KEY=sk-your-openai-api-key-here

# Google AI API (for alternative AI services)
# Get from: https://makersuite.google.com/app/apikey
GOOGLE_AI_API_KEY=your-google-ai-api-key-here

# =============================================================================
# CARRIER API INTEGRATIONS
# =============================================================================

# DHL API
# Get from: https://developer.dhl.com/
DHL_API_KEY=your-dhl-api-key-here

# FedEx API
# Get from: https://developer.fedex.com/
FEDEX_API_KEY=your-fedex-api-key-here

# Maersk API
# Get from: https://developer.maersk.com/
MAERSK_API_KEY=your-maersk-api-key-here

# =============================================================================
# TRACKING AGGREGATOR APIs
# =============================================================================

# FourKites API
# Get from: https://www.fourkites.com/developer/
FOURKITES_API_KEY=your-fourkites-api-key-here

# Project44 API
# Get from: https://project44.com/developer/
PROJECT44_API_KEY=your-project44-api-key-here

# =============================================================================
# SECURITY & AUTHENTICATION
# =============================================================================

# JWT Settings (optional - uses SECRET_KEY if not set)
JWT_SECRET_KEY=your-jwt-secret-key-here
JWT_ALGORITHM=HS256
JWT_EXPIRATION_DELTA=604800  # 7 days in seconds

# =============================================================================
# REDIS CONFIGURATION (Optional - for caching and WebSockets)
# =============================================================================

# Redis Settings (uncomment if using Redis)
# REDIS_URL=redis://localhost:6379/0
# REDIS_HOST=localhost
# REDIS_PORT=6379
# REDIS_PASSWORD=your-redis-password
# REDIS_DB=0

# =============================================================================
# THIRD-PARTY SERVICE INTEGRATIONS
# =============================================================================

# Google Maps API (for geocoding and distance calculations)
# Get from: https://console.cloud.google.com/apis/credentials
GOOGLE_MAPS_API_KEY=your-google-maps-api-key-here

# SendGrid API (alternative email service)
# Get from: https://app.sendgrid.com/settings/api_keys
SENDGRID_API_KEY=your-sendgrid-api-key-here

# Twilio API (for SMS notifications)
# Get from: https://console.twilio.com/
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_PHONE_NUMBER=your-twilio-phone-number

# =============================================================================
# MONITORING & ANALYTICS
# =============================================================================

# Sentry DSN (for error tracking)
# Get from: https://sentry.io/
SENTRY_DSN=your-sentry-dsn-here

# Google Analytics
# Get from: https://analytics.google.com/
GOOGLE_ANALYTICS_ID=your-google-analytics-id-here

# =============================================================================
# DEVELOPMENT & TESTING
# =============================================================================

# Test Database (for running tests)
TEST_DATABASE_URL=postgresql://test_user:test_password@localhost:5432/cloverics_test

# Test Stripe Keys (for testing payment flows)
STRIPE_TEST_SECRET_KEY=sk_test_your_test_stripe_secret_key
STRIPE_TEST_PUBLISHABLE_KEY=pk_test_your_test_stripe_publishable_key

# =============================================================================
# DEPLOYMENT SETTINGS
# =============================================================================

# Production Settings
# ALLOWED_HOSTS=your-domain.com,www.your-domain.com
# CSRF_TRUSTED_ORIGINS=https://your-domain.com,https://www.your-domain.com

# Static Files (for production)
# AWS_S3_ACCESS_KEY_ID=your-aws-access-key
# AWS_S3_SECRET_ACCESS_KEY=your-aws-secret-key
# AWS_S3_BUCKET_NAME=your-s3-bucket-name
# AWS_S3_REGION=us-east-1

# =============================================================================
# NOTES
# =============================================================================
# 
# 1. Replace all placeholder values with your actual API keys and credentials
# 2. Never commit the actual .env file to version control
# 3. Use different keys for development, staging, and production
# 4. Regularly rotate your API keys and secrets
# 5. Consider using a secrets management service for production
# 
# For local development, you can start with just the basic settings:
# - SECRET_KEY
# - DEBUG=True
# - Database settings (PGDATABASE, PGUSER, PGPASSWORD, PGHOST, PGPORT)
# - Email settings (if you need email functionality)
#
# Add other API keys as you integrate those services. 