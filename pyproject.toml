[project]
name = "repl-nix-workspace"
version = "0.1.0"
description = "Add your description here"
requires-python = ">=3.11"
dependencies = [
    "altair>=5.5.0",
    "django>=5.2.1",
    "django-cors-headers>=4.7.0",
    "djangorestframework>=3.16.0",
    "openai>=1.78.0",
    "psycopg2-binary>=2.9.10",
    "pyjwt>=2.10.1",
    "python-dotenv>=1.1.0",
    "reportlab>=4.4.1",
    "xlsxwriter>=3.2.3",
    "psutil>=7.0.0",
    "pyperclip>=1.9.0",
    "stripe>=12.2.0",
    "anthropic>=0.52.1",
    "geopy>=2.4.1",
    "openpyxl>=3.1.5",
    "plotly>=6.0.1",
    "python-multipart>=0.0.20",
    "fastapi>=0.115.13",
    "uvicorn[standard]>=0.34.3",
    "jinja2>=3.1.6",
    "python-jose[cryptography]>=3.5.0",
    "aiofiles>=24.1.0",
    "passlib[bcrypt]>=1.7.4",
    # "websockets>=15.0.1",  # Removed - WebSocket functionality disabled
    "pandas>=2.2.3",
    "pytest>=8.4.1",
    "pytest-asyncio>=1.0.0",
    "asgiref>=3.8.1",
    "jose>=1.0.0",
    "aiohttp>=3.12.13",
    "requests>=2.32.3",
    "itsdangerous>=2.2.0",
    "starlette>=0.46.2",
    "redis>=6.2.0",
]
