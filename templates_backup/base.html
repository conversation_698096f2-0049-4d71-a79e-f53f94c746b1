<!DOCTYPE html>
<html lang="{{ current_language }}" dir="{{ direction }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}{{ title }}{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="/static/css/main.css?v=20250710-2" rel="stylesheet">
    {% if direction == 'rtl' %}
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="/static/css/rtl.css" rel="stylesheet">
    {% endif %}
    {% block extra_css %}{% endblock %}
</head>
<body {% if user %}data-user-id="{{ user.id }}"{% endif %}>
    <div class="wrapper">
        {% if user %}
        <!-- Sidebar Navigation -->
        <nav id="sidebar" class="sidebar">
            <div class="sidebar-header">
                <h3>🍀 Cloverics</h3>
                <p class="sidebar-subtitle">{{ _('tagline') }}</p>
                <div class="sidebar-language-selector">
                    {% include 'components/language_selector.html' %}
                </div>
            </div>

            <ul class="list-unstyled components">
                <!-- Dashboard - Always visible -->
                <li class="nav-item">
                    <a href="/dashboard" class="nav-link">
                        <i class="fas fa-tachometer-alt"></i> {{ _('dashboard') }}
                    </a>
                </li>

                {% if user.user_type == "CUSTOMER" %}
                <!-- Shipping & Logistics Group -->
                <li class="nav-item nav-dropdown">
                    <a href="javascript:void(0)" class="nav-link dropdown-toggle" data-bs-toggle="collapse" data-bs-target="#shipping-logistics" aria-expanded="false">
                        <i class="fas fa-shipping-fast"></i> {{ _('shipping_logistics') }}
                        <i class="fas fa-chevron-down float-end"></i>
                    </a>
                    <ul class="collapse list-unstyled" id="shipping-logistics">
                        <li><a href="/customer/search-shipping" class="nav-link sub-link"><i class="fas fa-search"></i> {{ _('search_shipping') }}</a></li>
                        <li><a href="/customer/track-shipment" class="nav-link sub-link"><i class="fas fa-map-marked-alt"></i> {{ _('track_shipment') }}</a></li>
                        <li><a href="/customer/manage-shipments" class="nav-link sub-link"><i class="fas fa-boxes"></i> {{ _('manage_shipments') }}</a></li>
                        <li><a href="/customer/instant-multi-modal-quotes" class="nav-link sub-link"><i class="fas fa-shuttle-van"></i> {{ _('instant_multi_modal_quotes') }}</a></li>
                        <li><a href="/customer/multi-modal-optimizer" class="nav-link sub-link"><i class="fas fa-route"></i> {{ _('multi_modal_optimizer') }}</a></li>
                        <li><a href="/customer/pricing-calculator" class="nav-link sub-link"><i class="fas fa-calculator"></i> {{ _('pricing_calculator') }}</a></li>
                    </ul>
                </li>

                <!-- Intelligence & Analytics Group -->
                <li class="nav-item nav-dropdown">
                    <a href="javascript:void(0)" class="nav-link dropdown-toggle" data-bs-toggle="collapse" data-bs-target="#intelligence-analytics" aria-expanded="false">
                        <i class="fas fa-chart-line"></i> {{ _('intelligence_analytics') }}
                        <i class="fas fa-chevron-down float-end"></i>
                    </a>
                    <ul class="collapse list-unstyled" id="intelligence-analytics">
                        <li><a href="/customer/market-intelligence" class="nav-link sub-link"><i class="fas fa-chart-line"></i> {{ _('market_intelligence_hub') }}</a></li>
                        <li><a href="/customer/ai-analytics" class="nav-link sub-link"><i class="fas fa-robot"></i> {{ _('ai_analytics_intelligence') }}</a></li>
                        <li><a href="/customer/logistics-index" class="nav-link sub-link"><i class="fas fa-chart-area"></i> {{ _('cloverics_logistics_index') }}</a></li>
                    </ul>
                </li>

                <!-- Business Operations Group -->
                <li class="nav-item nav-dropdown">
                    <a href="javascript:void(0)" class="nav-link dropdown-toggle" data-bs-toggle="collapse" data-bs-target="#business-operations" aria-expanded="false">
                        <i class="fas fa-briefcase"></i> {{ _('business_operations') }}
                        <i class="fas fa-chevron-down float-end"></i>
                    </a>
                    <ul class="collapse list-unstyled" id="business-operations">
                        <li><a href="/customer/payment-checkout" class="nav-link sub-link"><i class="fas fa-credit-card"></i> {{ _('payment_checkout') }}</a></li>
                        <li><a href="/customer/invoice-reconciliation" class="nav-link sub-link"><i class="fas fa-file-invoice-dollar"></i> {{ _('invoice_reconciliation') }}</a></li>
                        <li><a href="/customer/rfq-automation" class="nav-link sub-link"><i class="fas fa-cogs"></i> {{ _('rfq_automation') }}</a></li>
                        <li><a href="/customer/saved-searches" class="nav-link sub-link"><i class="fas fa-search-plus"></i> {{ _('saved_searches') }}</a></li>
                    </ul>
                </li>

                <!-- Documents & Compliance  Group -->
                <li class="nav-item nav-dropdown">
                    <a href="javascript:void(0)" class="nav-link dropdown-toggle" data-bs-toggle="collapse" data-bs-target="#documents-compliance" aria-expanded="false">
                        <i class="fas fa-file-contract"></i> {{ _('documents_compliance') }}
                        <i class="fas fa-chevron-down float-end"></i>
                    </a>
                    <ul class="collapse list-unstyled" id="documents-compliance">
                        <li><a href="/customer/customs-declaration" class="nav-link sub-link"><i class="fas fa-file-signature"></i> {{ _('customs_declaration') }}</a></li>
                        <li><a href="/customer/document-automation" class="nav-link sub-link"><i class="fas fa-file-alt"></i> {{ _('document_automation') }}</a></li>
                        <li><a href="/customer/contract-lifecycle" class="nav-link sub-link"><i class="fas fa-file-contract"></i> {{ _('contract_lifecycle') }}</a></li>
                        <li><a href="/customer/compliance-automation" class="nav-link sub-link"><i class="fas fa-shield-alt"></i> {{ _('compliance_automation') }}</a></li>
                        <li><a href="/customer/file-export-center" class="nav-link sub-link"><i class="fas fa-file-export"></i> {{ _('file_export_center') }}</a></li>
                    </ul>
                </li>

                <!-- Account Group -->
                <li class="nav-item nav-dropdown">
                    <a href="javascript:void(0)" class="nav-link dropdown-toggle" data-bs-toggle="collapse" data-bs-target="#account-customer" aria-expanded="false">
                        <i class="fas fa-user-circle"></i> {{ _('account') }}
                        <i class="fas fa-chevron-down float-end"></i>
                    </a>
                    <ul class="collapse list-unstyled" id="account-customer">
                        <li><a href="/customer/profile" class="nav-link sub-link"><i class="fas fa-user"></i> {{ _('profile') }}</a></li>
                        <li><a href="/customer/notifications" class="nav-link sub-link"><i class="fas fa-bell"></i> {{ _('notifications') }}</a></li>
                        <li><a href="/customer/messages" class="nav-link sub-link"><i class="fas fa-envelope"></i> {{ _('messages') }}</a></li>
                        <li><a href="/customer/contact-support" class="nav-link sub-link"><i class="fas fa-headset"></i> {{ _('contact_support') }}</a></li>
                    </ul>
                </li>

                {% elif user.user_type == "LOGISTICS_PROVIDER" %}
                <!-- Fleet & Operations Group -->
                <li class="nav-item nav-dropdown">
                    <a href="javascript:void(0)" class="nav-link dropdown-toggle" data-bs-toggle="collapse" data-bs-target="#fleet-operations" aria-expanded="false">
                        <i class="fas fa-truck"></i> Fleet & Operations
                        <i class="fas fa-chevron-down float-end"></i>
                    </a>
                    <ul class="collapse list-unstyled" id="fleet-operations">
                        <li><a href="/logistics/manage-shipments" class="nav-link sub-link"><i class="fas fa-shipping-fast"></i> Manage Shipments</a></li>
                        <li><a href="/logistics/driver-management" class="nav-link sub-link"><i class="fas fa-truck"></i> Driver Management</a></li>
                        <li><a href="/logistics/pricing-quotes" class="nav-link sub-link"><i class="fas fa-file-invoice-dollar"></i> Quote & Rate Management</a></li>
                        <li><a href="/logistics/performance-metrics" class="nav-link sub-link"><i class="fas fa-chart-line"></i> Performance Metrics</a></li>
                    </ul>
                </li>

                <!-- Business & Clients Group -->
                <li class="nav-item nav-dropdown">
                    <a href="javascript:void(0)" class="nav-link dropdown-toggle" data-bs-toggle="collapse" data-bs-target="#business-clients" aria-expanded="false">
                        <i class="fas fa-handshake"></i> Business & Clients
                        <i class="fas fa-chevron-down float-end"></i>
                    </a>
                    <ul class="collapse list-unstyled" id="business-clients">
                        <li><a href="/logistics/clients" class="nav-link sub-link"><i class="fas fa-users"></i> My Clients</a></li>
                        <li><a href="/logistics/revenue-pricing-control" class="nav-link sub-link"><i class="fas fa-chart-line"></i> Revenue & Pricing Control</a></li>
                    </ul>
                </li>

                <!-- Intelligence & Analytics Group -->
                <li class="nav-item nav-dropdown">
                    <a href="javascript:void(0)" class="nav-link dropdown-toggle" data-bs-toggle="collapse" data-bs-target="#intelligence-analytics-logistics" aria-expanded="false">
                        <i class="fas fa-chart-bar"></i> Intelligence & Analytics
                        <i class="fas fa-chevron-down float-end"></i>
                    </a>
                    <ul class="collapse list-unstyled" id="intelligence-analytics-logistics">
                        <li><a href="/logistics/market-intelligence" class="nav-link sub-link"><i class="fas fa-chart-bar"></i> Market Intelligence</a></li>
                        <li><a href="/logistics/ai-analytics" class="nav-link sub-link"><i class="fas fa-robot"></i> AI Analytics & Intelligence</a></li>
                    </ul>
                </li>

                <!-- Advanced Tools Group -->
                <li class="nav-item nav-dropdown">
                    <a href="javascript:void(0)" class="nav-link dropdown-toggle" data-bs-toggle="collapse" data-bs-target="#advanced-tools" aria-expanded="false">
                        <i class="fas fa-cogs"></i> Advanced Tools
                        <i class="fas fa-chevron-down float-end"></i>
                    </a>
                    <ul class="collapse list-unstyled" id="advanced-tools">
                        <li><a href="/logistics/rfq-automation" class="nav-link sub-link"><i class="fas fa-cogs"></i> RFQ Automation</a></li>
                        <li><a href="/logistics/developer-portal" class="nav-link sub-link"><i class="fas fa-code"></i> Developer Portal</a></li>
                        <li><a href="/logistics/carrier-integration" class="nav-link sub-link"><i class="fas fa-shipping-fast"></i> Carrier Integration</a></li>
                        <li><a href="/logistics/erp-finance" class="nav-link sub-link"><i class="fas fa-calculator"></i> ERP & Finance</a></li>
                        <li><a href="/logistics/documentation" class="nav-link sub-link"><i class="fas fa-book"></i> Documentation</a></li>
                    </ul>
                </li>

                <!-- Documents & Compliance  Group -->
                <li class="nav-item nav-dropdown">
                    <a href="javascript:void(0)" class="nav-link dropdown-toggle" data-bs-toggle="collapse" data-bs-target="#documents-compliance-logistics" aria-expanded="false">
                        <i class="fas fa-file-contract"></i> Documents & Compliance 
                        <i class="fas fa-chevron-down float-end"></i>
                    </a>
                    <ul class="collapse list-unstyled" id="documents-compliance-logistics">
                        <li><a href="/logistics/document-automation" class="nav-link sub-link"><i class="fas fa-file-alt"></i> Document Automation</a></li>
                        <li><a href="/logistics/contract-lifecycle" class="nav-link sub-link"><i class="fas fa-file-contract"></i> Contract Lifecycle</a></li>
                        <li><a href="/logistics/compliance-automation" class="nav-link sub-link"><i class="fas fa-shield-alt"></i> Compliance Automation</a></li>
                        <li><a href="/logistics/export-center" class="nav-link sub-link"><i class="fas fa-file-export"></i> File Export Center</a></li>
                    </ul>
                </li>

                <!-- Account Group -->
                <li class="nav-item nav-dropdown">
                    <a href="javascript:void(0)" class="nav-link dropdown-toggle" data-bs-toggle="collapse" data-bs-target="#account-logistics" aria-expanded="false">
                        <i class="fas fa-user-circle"></i> Account
                        <i class="fas fa-chevron-down float-end"></i>
                    </a>
                    <ul class="collapse list-unstyled" id="account-logistics">
                        <li><a href="/profile" class="nav-link sub-link"><i class="fas fa-user"></i> Profile</a></li>
                        <li><a href="/notifications" class="nav-link sub-link"><i class="fas fa-bell"></i> Notifications</a></li>
                        <li><a href="/messages" class="nav-link sub-link"><i class="fas fa-envelope"></i> Messages</a></li>
                        <li><a href="/contact" class="nav-link sub-link"><i class="fas fa-headset"></i> Contact & Support</a></li>
                    </ul>
                </li>

                {% elif user.user_type == "CUSTOMS_AGENT" %}
                <!-- Customs Operations Group -->
                <li class="nav-item nav-dropdown">
                    <a href="javascript:void(0)" class="nav-link dropdown-toggle" data-bs-toggle="collapse" data-bs-target="#customs-operations" aria-expanded="false">
                        <i class="fas fa-passport"></i> Customs Operations
                        <i class="fas fa-chevron-down float-end"></i>
                    </a>
                    <ul class="collapse list-unstyled" id="customs-operations">
                        <li><a href="/customs/declaration" class="nav-link sub-link"><i class="fas fa-file-alt"></i> Customs Declarations</a></li>
                        <li><a href="/customs/clearance" class="nav-link sub-link"><i class="fas fa-check-circle"></i> Clearance Processing</a></li>
                        <li><a href="/customs/inspections" class="nav-link sub-link"><i class="fas fa-search"></i> Inspections</a></li>
                        <li><a href="/customs/reports" class="nav-link sub-link"><i class="fas fa-chart-bar"></i> Reports</a></li>
                    </ul>
                </li>

                {% elif user.user_type == "INSURANCE_PROVIDER" %}
                <!-- Insurance Management Group -->
                <li class="nav-item nav-dropdown">
                    <a href="javascript:void(0)" class="nav-link dropdown-toggle" data-bs-toggle="collapse" data-bs-target="#insurance-management" aria-expanded="false">
                        <i class="fas fa-shield-check"></i> Insurance Management
                        <i class="fas fa-chevron-down float-end"></i>
                    </a>
                    <ul class="collapse list-unstyled" id="insurance-management">
                        <li><a href="/insurance/policies" class="nav-link sub-link"><i class="fas fa-shield-alt"></i> Policies</a></li>
                        <li><a href="/insurance/claims" class="nav-link sub-link"><i class="fas fa-exclamation-triangle"></i> Claims</a></li>
                        <li><a href="/insurance/underwriting" class="nav-link sub-link"><i class="fas fa-clipboard-check"></i> Underwriting</a></li>
                        <li><a href="/insurance/dashboard" class="nav-link sub-link"><i class="fas fa-chart-line"></i> Reports</a></li>
                    </ul>
                </li>

                {% elif user.user_type == "INDEPENDENT_DRIVER" %}
                <!-- Driver Operations Group -->
                <li class="nav-item nav-dropdown">
                    <a href="javascript:void(0)" class="nav-link dropdown-toggle" data-bs-toggle="collapse" data-bs-target="#driver-operations" aria-expanded="false">
                        <i class="fas fa-truck-moving"></i> Driver Operations
                        <i class="fas fa-chevron-down float-end"></i>
                    </a>
                    <ul class="collapse list-unstyled" id="driver-operations">
                        <li><a href="/driver/available-jobs" class="nav-link sub-link"><i class="fas fa-clipboard-list"></i> Available Jobs</a></li>
                        <li><a href="/driver/my-trips" class="nav-link sub-link"><i class="fas fa-truck"></i> My Trips</a></li>
                        <li><a href="/driver/earnings" class="nav-link sub-link"><i class="fas fa-dollar-sign"></i> Earnings</a></li>
                        <li><a href="/driver/vehicle" class="nav-link sub-link"><i class="fas fa-cog"></i> Vehicle Management</a></li>
                        <li><a href="/driver/location" class="nav-link sub-link"><i class="fas fa-map-marker-alt"></i> Location Tracking</a></li>
                    </ul>
                </li>

                {% elif user.user_type == "ADMIN" %}
                <!-- User Operations Group -->
                <li class="nav-item nav-dropdown">
                    <a href="javascript:void(0)" class="nav-link dropdown-toggle" data-bs-toggle="collapse" data-bs-target="#user-operations" aria-expanded="false">
                        <i class="fas fa-users-cog"></i> User Operations
                        <i class="fas fa-chevron-down float-end"></i>
                    </a>
                    <ul class="collapse list-unstyled" id="user-operations">
                        <li><a href="/admin/users" class="nav-link sub-link"><i class="fas fa-users"></i> User Management</a></li>
                        <li><a href="/admin/verification" class="nav-link sub-link"><i class="fas fa-check-circle"></i> Verification Requests</a></li>
                        <li><a href="/admin/support-tickets" class="nav-link sub-link"><i class="fas fa-headset"></i> Support Tickets</a></li>
                    </ul>
                </li>

                <!-- System Administration Group -->
                <li class="nav-item nav-dropdown">
                    <a href="javascript:void(0)" class="nav-link dropdown-toggle" data-bs-toggle="collapse" data-bs-target="#system-administration" aria-expanded="false">
                        <i class="fas fa-server"></i> System Administration
                        <i class="fas fa-chevron-down float-end"></i>
                    </a>
                    <ul class="collapse list-unstyled" id="system-administration">
                        <li><a href="/admin/system-settings" class="nav-link sub-link"><i class="fas fa-cogs"></i> System Settings</a></li>
                        <li><a href="/admin/system" class="nav-link sub-link"><i class="fas fa-server"></i> System Management</a></li>
                        <li><a href="/admin/performance" class="nav-link sub-link"><i class="fas fa-tachometer-alt"></i> Performance Dashboard</a></li>
                    </ul>
                </li>

                {% endif %}
            </ul>

            <ul class="list-unstyled components mt-auto">
                <li class="nav-item">
                    <form method="POST" action="/logout" class="d-inline">
                        <button type="submit" class="nav-link btn btn-link text-start w-100">
                            <i class="fas fa-sign-out-alt"></i> Logout
                        </button>
                    </form>
                </li>
            </ul>
        </nav>
        {% endif %}

        <!-- Page Content -->
        <div id="content" class="{% if user %}content-with-sidebar{% else %}content-full{% endif %}">
            {% if user %}
            <!-- Top Navigation Bar -->
            <nav class="navbar navbar-expand-lg navbar-light bg-light top-navbar">
                <div class="container-fluid">
                    <button type="button" id="sidebarCollapse" class="btn btn-info">
                        <i class="fas fa-align-left"></i>
                    </button>
                    <div class="ms-auto d-flex align-items-center">
                        <span class="navbar-text me-3">
                            Welcome, {{ user.company_name or user.email }}
                        </span>
                        <span class="badge bg-primary">{{ user.user_type.replace('_', ' ').title() }}</span>
                    </div>
                </div>
            </nav>
            {% endif %}

            <!-- Main Content Area -->
            <div class="main-content">
                {% if error %}
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    {{ error }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                {% endif %}

                {% if success %}
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    {{ success }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                {% endif %}

                {% block content %}{% endblock %}
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="/static/js/main.js"></script>
    
    <!-- PHASE C: Real-Time Cross-User Event Polling -->
    <script>
    // Real-time polling for cross-user events
    let eventPollingActive = true;
    let lastEventCheck = Date.now();
    let websocket = null;
    let wsReconnectAttempts = 0;
    let maxReconnectAttempts = 5;
    
    function pollCrossUserEvents() {
        if (!eventPollingActive) return;
        
        fetch('/api/events/poll')
            .then(response => response.json())
            .then(data => {
                if (data.success && data.events.length > 0) {
                    data.events.forEach(event => {
                        handleCrossUserEvent(event);
                    });
                    
                    // Update notification badge
                    updateNotificationCount();
                }
            })
            .catch(error => {
                console.log('Polling temporarily unavailable');
            });
    }
    
    function handleCrossUserEvent(event) {
        // Show toast notification for different event types
        let title = '';
        let message = '';
        let icon = 'info';
        
        switch(event.type) {
            case 'customs_declared':
                title = 'Customs Declaration Update';
                message = `New customs declaration submitted for shipment ${event.data.tracking_number || 'N/A'}`;
                icon = 'info';
                break;
                
            case 'driver_assigned':
                title = 'Driver Assigned';
                message = `Driver ${event.data.driver_name || 'Unknown'} assigned to your shipment`;
                icon = 'success';
                break;
                
            case 'shipment_status_updated':
                title = 'Shipment Update';
                message = `Shipment ${event.data.tracking_number || 'N/A'} status changed to ${event.data.status || 'Updated'}`;
                icon = 'info';
                break;
                
            case 'insurance_claim_created':
                title = 'Insurance Claim';
                message = `New insurance claim created for shipment ${event.data.tracking_number || 'N/A'}`;
                icon = 'warning';
                break;
                
            default:
                title = 'Platform Update';
                message = 'You have a new platform notification';
                icon = 'info';
        }
        
        // Show SweetAlert toast
        const Toast = Swal.mixin({
            toast: true,
            position: 'top-end',
            showConfirmButton: false,
            timer: 4000,
            timerProgressBar: true,
            didOpen: (toast) => {
                toast.addEventListener('mouseenter', Swal.stopTimer)
                toast.addEventListener('mouseleave', Swal.resumeTimer)
            }
        });
        
        Toast.fire({
            icon: icon,
            title: title,
            text: message
        });
        
        // Update page-specific elements based on event
        updatePageElements(event);
    }
    
    function updatePageElements(event) {
        // Update shipment tables if visible
        if (event.type === 'driver_assigned' && $('.shipment-table').length > 0) {
            // Highlight the affected row
            $(`.shipment-table tr[data-shipment-id="${event.shipment_id}"]`).addClass('table-success');
        }
        
        // Update customs declaration lists
        if (event.type === 'customs_declared' && $('.customs-table').length > 0) {
            // Add visual indicator for new declarations
            $(`.customs-table`).prepend(`
                <div class="alert alert-info alert-dismissible fade show mt-2" role="alert">
                    <strong>New Declaration:</strong> ${event.data.tracking_number || 'Unknown'} has been processed
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `);
        }
    }
    
    function updateNotificationCount() {
        fetch('/api/notifications/realtime')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const badge = document.querySelector('.notification-badge');
                    if (badge && data.unread_count > 0) {
                        badge.textContent = data.unread_count;
                        badge.style.display = 'inline';
                    } else if (badge) {
                        badge.style.display = 'none';
                    }
                }
            })
            .catch(error => console.log('Notification update failed'));
    }
    
    // PHASE D: WebSocket Real-Time Connection
    function initWebSocket() {
        // Only initialize if user is authenticated and user ID is available
        const userElement = document.querySelector('[data-user-id]');
        if (!userElement) return;
        
        const userId = userElement.getAttribute('data-user-id');
        const wsProtocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${wsProtocol}//${window.location.host}/ws/notifications/${userId}`;
        
        try {
            websocket = new WebSocket(wsUrl);
            
            websocket.onopen = function(event) {
                console.log('🚀 WebSocket connected - Real-time notifications active');
                wsReconnectAttempts = 0;
                
                // Send ping every 30 seconds to keep connection alive
                setInterval(() => {
                    if (websocket && websocket.readyState === WebSocket.OPEN) {
                        websocket.send('ping');
                    }
                }, 30000);
            };
            
            websocket.onmessage = function(event) {
                const message = JSON.parse(event.data);
                
                if (message.type === 'pong') {
                    return; // Keep-alive response
                }
                
                if (message.type === 'cross_user_event') {
                    // Handle real-time cross-user events instantly
                    handleCrossUserEvent({
                        type: message.event_type,
                        data: message.data,
                        shipment_id: message.shipment_id
                    });
                    
                    // Update notification count
                    updateNotificationCount();
                    
                    console.log(`📡 Real-time event received: ${message.event_type}`);
                }
            };
            
            websocket.onclose = function(event) {
                console.log('❌ WebSocket disconnected');
                websocket = null;
                
                // Attempt reconnection with exponential backoff
                if (wsReconnectAttempts < maxReconnectAttempts) {
                    const delay = Math.pow(2, wsReconnectAttempts) * 1000; // Exponential backoff
                    setTimeout(initWebSocket, delay);
                    wsReconnectAttempts++;
                    console.log(`🔄 WebSocket reconnecting in ${delay}ms (attempt ${wsReconnectAttempts})`);
                } else {
                    console.log('⚠️ WebSocket max reconnection attempts reached, falling back to polling');
                }
            };
            
            websocket.onerror = function(error) {
                console.log('WebSocket error:', error);
            };
            
        } catch (error) {
            console.log('WebSocket connection failed, using polling fallback');
        }
    }

    // Start polling when page loads
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize WebSocket for instant notifications
        initWebSocket();
        
        // Initial poll (fallback)
        setTimeout(pollCrossUserEvents, 2000);
        
        // Poll every 30 seconds as fallback for WebSocket
        setInterval(pollCrossUserEvents, 30000);
        
        // Update notification count every 30 seconds
        setInterval(updateNotificationCount, 30000);
        
        // Stop polling when page becomes hidden (performance optimization)
        document.addEventListener('visibilitychange', function() {
            eventPollingActive = !document.hidden;
        });
    });
    
    // Enhanced notification display in header
    function enhanceNotificationBadge() {
        const notificationIcon = document.querySelector('a[href="/notifications"]');
        if (notificationIcon && !notificationIcon.querySelector('.notification-badge')) {
            notificationIcon.innerHTML += '<span class="notification-badge badge bg-danger rounded-pill ms-1" style="display: none;">0</span>';
        }
    }
    
    // Initialize enhanced notifications
    document.addEventListener('DOMContentLoaded', function() {
        enhanceNotificationBadge();
        updateNotificationCount();
    });
    </script>
    
    <!-- AI Chatbot Widget -->
    <div id="ai-chatbot-widget" class="position-fixed" style="bottom: 20px; right: 20px; z-index: 1050;">
        <!-- Chat Toggle Button -->
        <button id="chat-toggle-btn" class="btn btn-primary rounded-circle shadow-lg" style="width: 60px; height: 60px;">
            <i class="fas fa-comments fa-lg"></i>
        </button>
        
        <!-- Chat Window -->
        <div id="chat-window" class="bg-white rounded shadow-lg" style="width: 350px; height: 500px; display: none; position: absolute; bottom: 70px; right: 0;">
            <!-- Chat Header -->
            <div class="chat-header bg-primary text-white p-3 rounded-top d-flex justify-content-between align-items-center">
                <div>
                    <strong>🤖 Cloverics AI Assistant</strong>
                    <small class="d-block opacity-75">Online - Ready to help</small>
                </div>
                <button id="chat-close-btn" class="btn btn-link text-white p-0">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <!-- Chat Messages Container -->
            <div id="chat-messages" class="chat-messages p-3" style="height: 350px; overflow-y: auto; background: #f8f9fa;">
                <div class="message bot-message mb-2">
                    <div class="message-content bg-primary text-white p-2 rounded">
                        👋 Hello! I'm your AI logistics assistant. I can help you with:
                        <ul class="mb-0 mt-2">
                            <li>Track shipments</li>
                            <li>Get quotes</li>
                            <li>Documentation help</li>
                            <li>Customs questions</li>
                        </ul>
                        How can I assist you today?
                    </div>
                </div>
            </div>
            
            <!-- Chat Input -->
            <div class="chat-input p-3 border-top">
                <div class="input-group">
                    <input type="text" id="chat-input" class="form-control" placeholder="Type your message..." maxlength="500">
                    <button id="chat-send-btn" class="btn btn-primary" type="button">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- AI Chatbot JavaScript -->
    <script>
    // AI Chatbot functionality
    let chatSessionId = null;
    let chatActive = false;
    
    document.addEventListener('DOMContentLoaded', function() {
        initializeChatbot();
    });
    
    function initializeChatbot() {
        const toggleBtn = document.getElementById('chat-toggle-btn');
        const closeBtn = document.getElementById('chat-close-btn');
        const chatWindow = document.getElementById('chat-window');
        const chatInput = document.getElementById('chat-input');
        const sendBtn = document.getElementById('chat-send-btn');
        
        // Generate session ID
        chatSessionId = 'chat_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        
        // Toggle chat window
        toggleBtn.addEventListener('click', function() {
            if (chatWindow.style.display === 'none') {
                chatWindow.style.display = 'block';
                chatActive = true;
                chatInput.focus();
            } else {
                chatWindow.style.display = 'none';
                chatActive = false;
            }
        });
        
        // Close chat window
        closeBtn.addEventListener('click', function() {
            chatWindow.style.display = 'none';
            chatActive = false;
        });
        
        // Send message on Enter key
        chatInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendChatMessage();
            }
        });
        
        // Send message on button click
        sendBtn.addEventListener('click', sendChatMessage);
    }
    
    async function sendChatMessage() {
        const chatInput = document.getElementById('chat-input');
        const message = chatInput.value.trim();
        
        if (!message) return;
        
        // Clear input
        chatInput.value = '';
        
        // Add user message to chat
        addMessageToChat(message, 'user');
        
        // Show typing indicator
        showTypingIndicator();
        
        try {
            // Send to AI chatbot
            const response = await fetch('/api/chatbot/message', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    message: message,
                    session_id: chatSessionId,
                    user_id: {{ user.id if user else 'null' }},
                    user_type: '{{ user.user_type if user else "GUEST" }}'
                })
            });
            
            const result = await response.json();
            
            // Remove typing indicator
            removeTypingIndicator();
            
            if (result.success) {
                // Add bot response to chat
                addMessageToChat(result.response, 'bot');
                
                // Handle any special actions
                if (result.actions) {
                    handleBotActions(result.actions);
                }
            } else {
                addMessageToChat('Sorry, I encountered an error. Please try again.', 'bot');
            }
            
        } catch (error) {
            removeTypingIndicator();
            addMessageToChat('Sorry, I\'m having trouble connecting. Please try again later.', 'bot');
        }
    }
    
    function addMessageToChat(message, sender) {
        const chatMessages = document.getElementById('chat-messages');
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}-message mb-2`;
        
        const isBot = sender === 'bot';
        const bgClass = isBot ? 'bg-primary text-white' : 'bg-light text-dark';
        const alignClass = isBot ? 'me-4' : 'ms-4';
        
        messageDiv.innerHTML = `
            <div class="message-content ${bgClass} p-2 rounded ${alignClass}">
                ${escapeHtml(message)}
            </div>
        `;
        
        chatMessages.appendChild(messageDiv);
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }
    
    function showTypingIndicator() {
        const chatMessages = document.getElementById('chat-messages');
        const typingDiv = document.createElement('div');
        typingDiv.id = 'typing-indicator';
        typingDiv.className = 'message bot-message mb-2';
        typingDiv.innerHTML = `
            <div class="message-content bg-light text-muted p-2 rounded me-4">
                <i class="fas fa-circle blink me-1"></i>
                <i class="fas fa-circle blink me-1" style="animation-delay: 0.2s;"></i>
                <i class="fas fa-circle blink" style="animation-delay: 0.4s;"></i>
                AI is typing...
            </div>
        `;
        
        chatMessages.appendChild(typingDiv);
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }
    
    function removeTypingIndicator() {
        const typingIndicator = document.getElementById('typing-indicator');
        if (typingIndicator) {
            typingIndicator.remove();
        }
    }
    
    function handleBotActions(actions) {
        actions.forEach(action => {
            switch(action.type) {
                case 'redirect':
                    setTimeout(() => {
                        window.location.href = action.url;
                    }, 2000);
                    break;
                case 'open_modal':
                    // Handle modal opening
                    break;
                case 'update_page':
                    // Handle page updates
                    break;
            }
        });
    }
    
    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    </script>

    <!-- Chatbot CSS -->
    <style>
    .chat-messages {
        font-size: 14px;
    }
    
    .message-content {
        max-width: 85%;
        word-wrap: break-word;
    }
    
    .user-message .message-content {
        margin-left: auto;
        margin-right: 0;
    }
    
    .bot-message .message-content {
        margin-left: 0;
        margin-right: auto;
    }
    
    .blink {
        animation: blink 1.4s infinite both;
        font-size: 0.5em;
    }
    
    @keyframes blink {
        0%, 80%, 100% { opacity: 0; }
        40% { opacity: 1; }
    }
    
    #chat-toggle-btn:hover {
        transform: scale(1.05);
        transition: transform 0.2s;
    }
    
    .chat-header {
        cursor: move;
    }
    
    .chat-input input:focus {
        border-color: #0d6efd;
        box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
    }
    </style>

    <!-- AI Chatbot Widget Integration -->
    {% if user %}
    <div id="chatbot-widget" class="chatbot-widget">
        <div id="chatbot-toggle" class="chatbot-toggle" onclick="toggleChatbot()">
            <i class="fas fa-robot"></i>
            <span class="chat-notification" id="chat-notification" style="display: none;">!</span>
        </div>
        
        <div id="chatbot-window" class="chatbot-window" style="display: none;">
            <div class="chatbot-header">
                <div class="chatbot-avatar">
                    <i class="fas fa-robot"></i>
                </div>
                <div class="chatbot-title">
                    <h6 class="mb-0">AI Assistant</h6>
                    <small class="text-muted">Always here to help</small>
                </div>
                <button class="chatbot-close" onclick="toggleChatbot()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <div class="chatbot-messages" id="chatbot-messages">
                <div class="message bot-message">
                    <div class="message-avatar">
                        <i class="fas fa-robot"></i>
                    </div>
                    <div class="message-content">
                        <p>Hello! I'm your AI logistics assistant. I can help you with:</p>
                        <ul class="chat-capabilities">
                            <li>📦 Tracking shipments</li>
                            <li>💰 Quote calculations</li>
                            <li>🗺️ Route optimization</li>
                            <li>📄 Documentation assistance</li>
                            <li>🧭 Platform navigation</li>
                        </ul>
                        <p>How can I assist you today?</p>
                    </div>
                </div>
            </div>
            
            <div class="chatbot-typing" id="chatbot-typing" style="display: none;">
                <div class="typing-indicator">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
                <small>AI Assistant is typing...</small>
            </div>
            
            <div class="chatbot-input">
                <div class="quick-actions" id="quick-actions">
                    <button class="quick-action" onclick="sendQuickMessage('Track my shipment')">
                        📦 Track Shipment
                    </button>
                    <button class="quick-action" onclick="sendQuickMessage('Get a quote')">
                        💰 Get Quote
                    </button>
                    <button class="quick-action" onclick="sendQuickMessage('Help with documentation')">
                        📄 Documentation
                    </button>
                </div>
                <div class="input-group">
                    <input type="text" id="chatbot-input" class="form-control" 
                           placeholder="Type your message..." 
                           onkeypress="handleChatKeyPress(event)">
                    <button class="btn btn-primary" onclick="sendChatMessage()">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <style>
    .chatbot-widget {
        position: fixed;
        bottom: 20px;
        right: 20px;
        z-index: 1050;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }
    
    .chatbot-toggle {
        width: 60px;
        height: 60px;
        background: linear-gradient(135deg, #007bff, #0056b3);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
        transition: all 0.3s ease;
        position: relative;
    }
    
    .chatbot-toggle:hover {
        transform: scale(1.1);
        box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
    }
    
    .chatbot-toggle i {
        color: white;
        font-size: 24px;
    }
    
    .chat-notification {
        position: absolute;
        top: -5px;
        right: -5px;
        background: #dc3545;
        color: white;
        border-radius: 50%;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        font-weight: bold;
    }
    
    .chatbot-window {
        width: 350px;
        height: 500px;
        background: white;
        border-radius: 12px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        position: absolute;
        bottom: 80px;
        right: 0;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        border: 1px solid #e9ecef;
    }
    
    .chatbot-header {
        background: linear-gradient(135deg, #007bff, #0056b3);
        color: white;
        padding: 15px;
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    .chatbot-avatar {
        width: 40px;
        height: 40px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .chatbot-title {
        flex: 1;
    }
    
    .chatbot-close {
        background: none;
        border: none;
        color: white;
        cursor: pointer;
        padding: 5px;
        border-radius: 4px;
        transition: background 0.2s;
    }
    
    .chatbot-close:hover {
        background: rgba(255, 255, 255, 0.2);
    }
    
    .chatbot-messages {
        flex: 1;
        padding: 15px;
        overflow-y: auto;
        background: #f8f9fa;
    }
    
    .message {
        display: flex;
        gap: 10px;
        margin-bottom: 15px;
        animation: messageSlideIn 0.3s ease-out;
    }
    
    @keyframes messageSlideIn {
        from {
            opacity: 0;
            transform: translateY(10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    .user-message {
        justify-content: flex-end;
    }
    
    .user-message .message-content {
        background: #007bff;
        color: white;
        order: -1;
    }
    
    .bot-message .message-avatar {
        width: 32px;
        height: 32px;
        background: #6c757d;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 14px;
        flex-shrink: 0;
    }
    
    .user-message .message-avatar {
        width: 32px;
        height: 32px;
        background: #007bff;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 14px;
        flex-shrink: 0;
    }
    
    .message-content {
        background: white;
        padding: 12px 15px;
        border-radius: 18px;
        max-width: 70%;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }
    
    .message-content p {
        margin: 0 0 8px 0;
    }
    
    .message-content p:last-child {
        margin-bottom: 0;
    }
    
    .chat-capabilities {
        margin: 8px 0;
        padding-left: 16px;
    }
    
    .chat-capabilities li {
        margin-bottom: 4px;
        font-size: 14px;
    }
    
    .chatbot-typing {
        padding: 10px 15px;
        background: #f8f9fa;
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    .typing-indicator {
        display: flex;
        gap: 4px;
    }
    
    .typing-indicator span {
        width: 8px;
        height: 8px;
        background: #6c757d;
        border-radius: 50%;
        animation: typing 1.5s infinite;
    }
    
    .typing-indicator span:nth-child(2) {
        animation-delay: 0.2s;
    }
    
    .typing-indicator span:nth-child(3) {
        animation-delay: 0.4s;
    }
    
    @keyframes typing {
        0%, 60%, 100% {
            transform: scale(1);
            opacity: 0.7;
        }
        30% {
            transform: scale(1.2);
            opacity: 1;
        }
    }
    
    .chatbot-input {
        padding: 15px;
        background: white;
        border-top: 1px solid #e9ecef;
    }
    
    .quick-actions {
        display: flex;
        gap: 8px;
        margin-bottom: 10px;
        flex-wrap: wrap;
    }
    
    .quick-action {
        background: #e9ecef;
        border: none;
        padding: 6px 10px;
        border-radius: 12px;
        font-size: 12px;
        cursor: pointer;
        transition: background 0.2s;
    }
    
    .quick-action:hover {
        background: #dee2e6;
    }
    
    .input-group {
        display: flex;
        gap: 8px;
    }
    
    .input-group .form-control {
        flex: 1;
        border-radius: 20px;
        border: 1px solid #ced4da;
        padding: 8px 15px;
    }
    
    .input-group .btn {
        border-radius: 50%;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0;
    }
    
    @media (max-width: 768px) {
        .chatbot-window {
            width: 300px;
            height: 450px;
        }
    }
    </style>

    <!-- Clean Bootstrap Navigation JavaScript -->
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        initializeActiveNavigation();
        initializeSidebarCollapse();
    });

    function initializeActiveNavigation() {
        // Handle sub-link clicks to maintain active state
        const subLinks = document.querySelectorAll('.sub-link');
        subLinks.forEach(link => {
            link.addEventListener('click', function() {
                // Remove active class from all sub-links
                subLinks.forEach(l => l.classList.remove('active'));
                // Add active class to clicked link
                this.classList.add('active');
                
                // Save active link to localStorage
                localStorage.setItem('activeNavLink', this.getAttribute('href'));
            });
        });
        
        // Restore active link on page load
        const activeLink = localStorage.getItem('activeNavLink');
        if (activeLink) {
            const currentLink = document.querySelector(`a[href="${activeLink}"]`);
            if (currentLink && currentLink.classList.contains('sub-link')) {
                currentLink.classList.add('active');
            }
        }
    }

    function initializeSidebarCollapse() {
        // Initialize sidebar collapse functionality
        const sidebarCollapse = document.getElementById('sidebarCollapse');
        if (sidebarCollapse) {
            sidebarCollapse.addEventListener('click', function() {
                const sidebar = document.getElementById('sidebar');
                const content = document.getElementById('content');
                
                if (sidebar && content) {
                    sidebar.classList.toggle('collapsed');
                    content.classList.toggle('sidebar-collapsed');
                }
            });
        }
    }
    </script>

    <script>
    let chatbotOpen = false;
    let messageHistory = [];
    
    function toggleChatbot() {
        const window = document.getElementById('chatbot-window');
        const notification = document.getElementById('chat-notification');
        
        chatbotOpen = !chatbotOpen;
        window.style.display = chatbotOpen ? 'flex' : 'none';
        
        if (chatbotOpen) {
            notification.style.display = 'none';
            document.getElementById('chatbot-input').focus();
        }
    }
    
    function sendChatMessage() {
        const input = document.getElementById('chatbot-input');
        const message = input.value.trim();
        
        if (message) {
            addMessage(message, 'user');
            input.value = '';
            processUserMessage(message);
        }
    }
    
    function sendQuickMessage(message) {
        addMessage(message, 'user');
        processUserMessage(message);
    }
    
    function handleChatKeyPress(event) {
        if (event.key === 'Enter') {
            sendChatMessage();
        }
    }
    
    function addMessage(content, sender) {
        const messagesContainer = document.getElementById('chatbot-messages');
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}-message`;
        
        const avatar = document.createElement('div');
        avatar.className = 'message-avatar';
        
        if (sender === 'bot') {
            avatar.innerHTML = '<i class="fas fa-robot"></i>';
        } else {
            avatar.innerHTML = '<i class="fas fa-user"></i>';
        }
        
        const messageContent = document.createElement('div');
        messageContent.className = 'message-content';
        messageContent.innerHTML = content;
        
        messageDiv.appendChild(avatar);
        messageDiv.appendChild(messageContent);
        messagesContainer.appendChild(messageDiv);
        
        // Scroll to bottom
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
        
        // Store in history
        messageHistory.push({ content, sender, timestamp: new Date() });
    }
    
    function showTyping() {
        document.getElementById('chatbot-typing').style.display = 'flex';
        const messagesContainer = document.getElementById('chatbot-messages');
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }
    
    function hideTyping() {
        document.getElementById('chatbot-typing').style.display = 'none';
    }
    
    async function processUserMessage(message) {
        showTyping();
        
        try {
            // Simulate processing delay
            await new Promise(resolve => setTimeout(resolve, 1500));
            
            const response = await fetch('/api/chatbot/query', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    message: message,
                    history: messageHistory.slice(-10) // Last 10 messages for context
                })
            });
            
            const result = await response.json();
            
            hideTyping();
            
            if (result.success) {
                addMessage(result.response, 'bot');
                
                // Handle special actions
                if (result.actions) {
                    handleChatbotActions(result.actions);
                }
            } else {
                addMessage('I apologize, but I encountered an error. Please try again or contact support.', 'bot');
            }
        } catch (error) {
            hideTyping();
            console.error('Chatbot error:', error);
            addMessage('I\'m having trouble connecting right now. Please try again in a moment.', 'bot');
        }
    }
    
    function handleChatbotActions(actions) {
        actions.forEach(action => {
            switch (action.type) {
                case 'show_notification':
                    showChatNotification();
                    break;
                case 'redirect':
                    setTimeout(() => {
                        window.location.href = action.url;
                    }, 2000);
                    break;
                case 'open_modal':
                    // Handle modal opening if needed
                    break;
            }
        });
    }
    
    function showChatNotification() {
        if (!chatbotOpen) {
            document.getElementById('chat-notification').style.display = 'flex';
        }
    }
    
    // Initialize chatbot
    document.addEventListener('DOMContentLoaded', function() {
        // Show welcome notification after 3 seconds
        setTimeout(() => {
            if (!chatbotOpen) {
                showChatNotification();
            }
        }, 3000);
    });
    </script>
    {% endif %}

    {% block extra_js %}{% endblock %}

<script>

function generateReport(id) {
    console.log('generateReport called with id:', id);
    // TODO: Implement generateReport functionality
    alert('generateReport functionality not yet implemented');
}

function downloadReport(id) {
    console.log('downloadReport called with id:', id);
    // TODO: Implement downloadReport functionality
    alert('downloadReport functionality not yet implemented');
}

function viewReport(id) {
    console.log('viewReport called with id:', id);
    // TODO: Implement viewReport functionality
    alert('viewReport functionality not yet implemented');
}

function openReviewQueue(id) {
    console.log('openReviewQueue called with id:', id);
    // TODO: Implement openReviewQueue functionality
    alert('openReviewQueue functionality not yet implemented');
}

function reviewApplication(id) {
    console.log('reviewApplication called with id:', id);
    // TODO: Implement reviewApplication functionality
    alert('reviewApplication functionality not yet implemented');
}

function viewApplicationDetails(id) {
    console.log('viewApplicationDetails called with id:', id);
    // TODO: Implement viewApplicationDetails functionality
    alert('viewApplicationDetails functionality not yet implemented');
}

function approveApplication(id) {
    console.log('approveApplication called with id:', id);
    // TODO: Implement approveApplication functionality
    alert('approveApplication functionality not yet implemented');
}

function calculateRisk(id) {
    console.log('calculateRisk called with id:', id);
    // TODO: Implement calculateRisk functionality
    alert('calculateRisk functionality not yet implemented');
}

</script>

<script>

// Utility functions for button functionality
function showAlert(message, type = 'info') {
    // Create or update alert
    let alertDiv = document.getElementById('dynamicAlert');
    if (!alertDiv) {
        alertDiv = document.createElement('div');
        alertDiv.id = 'dynamicAlert';
        alertDiv.style.cssText = `
            position: fixed; top: 20px; right: 20px; z-index: 9999;
            padding: 15px 20px; border-radius: 5px; color: white;
            font-weight: bold; max-width: 300px;
        `;
        document.body.appendChild(alertDiv);
    }
    
    const colors = {
        'success': '#28a745',
        'error': '#dc3545', 
        'warning': '#ffc107',
        'info': '#17a2b8'
    };
    
    alertDiv.style.backgroundColor = colors[type] || colors.info;
    alertDiv.textContent = message;
    alertDiv.style.display = 'block';
    
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.parentNode.removeChild(alertDiv);
        }
    }, 3000);
}

function getCSRFToken() {
    return document.querySelector('[name=csrfmiddlewaretoken]')?.value || 
           document.querySelector('meta[name="csrf-token"]')?.content || '';
}

function showLoadingSpinner() {
    let spinner = document.getElementById('loadingSpinner');
    if (!spinner) {
        spinner = document.createElement('div');
        spinner.id = 'loadingSpinner';
        spinner.innerHTML = '<div style="border: 4px solid #f3f3f3; border-top: 4px solid #3498db; border-radius: 50%; width: 40px; height: 40px; animation: spin 1s linear infinite; margin: 20px auto;"></div>';
        spinner.style.cssText = 'position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); z-index: 10000; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 0 10px rgba(0,0,0,0.3);';
        document.body.appendChild(spinner);
    }
    spinner.style.display = 'block';
}

function hideLoadingSpinner() {
    const spinner = document.getElementById('loadingSpinner');
    if (spinner) spinner.style.display = 'none';
}

function validateForm(form) {
    const requiredFields = form.querySelectorAll('[required]');
    for (let field of requiredFields) {
        if (!field.value.trim()) {
            showAlert(`Please fill in ${field.name || 'required field'}`, 'warning');
            field.focus();
            return false;
        }
    }
    return true;
}

function getCurrentPageData() {
    // Extract data from current page for export
    const tables = document.querySelectorAll('table');
    const data = [];
    
    if (tables.length > 0) {
        const table = tables[0];
        const headers = Array.from(table.querySelectorAll('th')).map(th => th.textContent.trim());
        const rows = Array.from(table.querySelectorAll('tbody tr'));
        
        data.push(headers);
        rows.forEach(row => {
            const cells = Array.from(row.querySelectorAll('td')).map(td => td.textContent.trim());
            data.push(cells);
        });
    }
    
    return data;
}

function downloadCSV(data, filename) {
    const csvContent = data.map(row => row.map(cell => `"${cell}"`).join(',')).join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
}

function performSearch(query) {
    // Generic search functionality
    const searchResults = document.querySelectorAll('[data-searchable], .searchable-item, tbody tr');
    let found = 0;
    
    searchResults.forEach(item => {
        const text = item.textContent.toLowerCase();
        if (text.includes(query.toLowerCase())) {
            item.style.display = '';
            found++;
        } else {
            item.style.display = 'none';
        }
    });
    
    showAlert(`Found ${found} results for "${query}"`, 'info');
}

function applyFilters() {
    // Generic filter functionality
    const filterInputs = document.querySelectorAll('.filter-input, [data-filter]');
    const filterableItems = document.querySelectorAll('[data-filterable], tbody tr');
    
    let activeFilters = {};
    filterInputs.forEach(input => {
        if (input.value) {
            activeFilters[input.name || input.dataset.filter] = input.value.toLowerCase();
        }
    });
    
    let visibleCount = 0;
    filterableItems.forEach(item => {
        let shouldShow = true;
        
        for (let [filterName, filterValue] of Object.entries(activeFilters)) {
            const itemText = item.textContent.toLowerCase();
            if (!itemText.includes(filterValue)) {
                shouldShow = false;
                break;
            }
        }
        
        item.style.display = shouldShow ? '' : 'none';
        if (shouldShow) visibleCount++;
    });
    
    showAlert(`Showing ${visibleCount} filtered results`, 'info');
}

</script>
</body>
</html>