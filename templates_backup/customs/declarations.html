{% extends "base.html" %}

{% block title %}Customs Declarations - Cloverics{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-file-contract me-2"></i>
                        Customs Declarations Management
                    </h4>
                </div>
                <div class="card-body">
                    <!-- Statistics Row -->
                    <div class="row mb-4">
                        <div class="col-md-2">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h5>{{ stats.total }}</h5>
                                    <small>Total</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    <h5>{{ stats.pending }}</h5>
                                    <small>Pending</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h5>{{ stats.approved }}</h5>
                                    <small>Approved</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="card bg-danger text-white">
                                <div class="card-body text-center">
                                    <h5>{{ stats.rejected }}</h5>
                                    <small>Rejected</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <h5>{{ stats.action_required }}</h5>
                                    <small>Action Required</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="card bg-secondary text-white">
                                <div class="card-body text-center">
                                    <h5>{{ stats.avg_processing }}</h5>
                                    <small>Avg Processing</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Filter Form -->
                    <form method="GET" action="/customs/declaration" class="mb-4">
    <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <select class="form-select" name="status">
                                    <option value="">All Status</option>
                                    <option value="SUBMITTED" {% if request.query_params.get('status') == 'SUBMITTED' %}selected{% endif %}>Submitted</option>
                                    <option value="APPROVED" {% if request.query_params.get('status') == 'APPROVED' %}selected{% endif %}>Approved</option>
                                    <option value="REJECTED" {% if request.query_params.get('status') == 'REJECTED' %}selected{% endif %}>Rejected</option>
                                    <option value="UNDER_REVIEW" {% if request.query_params.get('status') == 'UNDER_REVIEW' %}selected{% endif %}>Under Review</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <input type="text" class="form-control" name="origin_country" placeholder="Origin Country" value="{{ request.query_params.get('origin_country', '') }}">
                            </div>
                            <div class="col-md-3">
                                <input type="date" class="form-control" name="created_after" value="{{ request.query_params.get('created_after', '') }}">
                            </div>
                            <div class="col-md-3">
                                <button type="submit" class="btn btn-outline-primary" onclick="applyCustomsFilters()">Apply Filters</button>
                                <a href="/customs/declaration" class="btn btn-outline-secondary">Clear</a>
                            </div>
                        </div>
                    </form>

                    <!-- Declarations Table -->
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>Declaration #</th>
                                    <th>Customer</th>
                                    <th>Origin → Destination</th>
                                    <th>Cargo Type</th>
                                    <th>Value</th>
                                    <th>Status</th>
                                    <th>Submitted</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for declaration in declarations %}
                                <tr>
                                    <td><strong>{{ declaration.declaration_id }}</strong></td>
                                    <td>{{ declaration.company_name }}</td>
                                    <td>{{ declaration.origin }} → {{ declaration.destination }}</td>
                                    <td>{{ declaration.cargo_type }}</td>
                                    <td>{{ declaration.cargo_value }}</td>
                                    <td>
                                        {% if declaration.status == 'SUBMITTED' %}
                                            <span class="badge bg-warning">Pending</span>
                                        {% elif declaration.status == 'APPROVED' %}
                                            <span class="badge bg-success">Approved</span>
                                        {% elif declaration.status == 'REJECTED' %}
                                            <span class="badge bg-danger">Rejected</span>
                                        {% elif declaration.status == 'UNDER_REVIEW' %}
                                            <span class="badge bg-info">Action Required</span>
                                        {% else %}
                                            <span class="badge bg-secondary">{{ declaration.status|title }}</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ declaration.submitted_date }}</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button class="btn btn-sm btn-outline-info" onclick="viewDeclaration({{ declaration.id }})">View</button>
                                            <button class="btn btn-sm btn-outline-secondary" onclick="printDeclaration({{ declaration.id }})">Print</button>
                                            
                                            {% if user.user_type == 'CUSTOMS_AGENT' and declaration.status == 'SUBMITTED' %}
                                                <!-- Approval/Rejection Forms as per instructions -->
                                                <form method="post" action="/customs/declaration/{{ declaration.id }}/approve" style="display:inline;">
    <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                                                    <button type="submit" class="btn btn-sm btn-success" onclick="approveDeclaration(this)">Approve</button>
                                                </form>
                                                <form method="post" action="/customs/declaration/{{ declaration.id }}/reject" style="display:inline;">
    <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                                                    <button type="submit" class="btn btn-sm btn-danger" onclick="rejectDeclaration(this)">Reject</button>
                                                </form>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Clean JavaScript implementation for Customs Declarations
console.log('Loading customs declarations page...');

// View declaration function
async function viewDeclaration(id) {
    console.log('Viewing declaration:', id);
    window.location.href = '/customs/declaration/' + id;
}

// Print declaration function
async function printDeclaration(id) {
    console.log('Printing declaration:', id);
    try {
        const res = await fetch('/api/customs/declaration/print/' + id);
        const data = await res.json();
        if (data.success && data.print_data) {
            // Create a new window for printing
            const printWindow = window.open('', '_blank');
            printWindow.document.write(`
                <html>
                <head><title>Print Declaration ${data.print_data.declaration_number}</title></head>
                <body style="font-family: Arial, sans-serif; padding: 20px;">
                    <h2>Customs Declaration</h2>
                    <p><strong>Declaration Number:</strong> ${data.print_data.declaration_number}</p>
                    <p><strong>Customer:</strong> ${data.print_data.customer_name}</p>
                    <p><strong>Origin:</strong> ${data.print_data.origin}</p>
                    <p><strong>Destination:</strong> ${data.print_data.destination}</p>
                    <p><strong>Cargo Value:</strong> ${data.print_data.cargo_value}</p>
                    <p><strong>Status:</strong> ${data.print_data.status}</p>
                    <p><strong>Generated:</strong> ${data.print_data.timestamp}</p>
                </body>
                </html>
            `);
            printWindow.document.close();
            printWindow.print();
            Swal.fire("Print Ready", "Document opened for printing", "success");
        } else {
            Swal.fire("Error", data.message || "Print failed", "error");
        }
    } catch (err) {
        console.error(err);
        Swal.fire("Error", "Print failed", "error");
    }
}

// Approve declaration function
async function approveDeclaration(id) {
    const confirmed = await Swal.fire({
        title: 'Approve this declaration?',
        text: 'This action will approve the customs declaration.',
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#28a745',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'Yes, approve it!'
    });
    
    if (confirmed.isConfirmed) {
        try {
            const res = await fetch('/customs/declaration/' + id + '/approve', { 
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                }
            });
            
            if (res.ok) {
                await Swal.fire('Approved!', 'Declaration has been approved successfully.', 'success');
                location.reload();
            } else {
                const data = await res.json();
                Swal.fire("Error", data.message || "Approval failed", "error");
            }
        } catch (err) {
            console.error('Approval error:', err);
            Swal.fire("Error", "Failed to approve declaration", "error");
        }
    }
}

// Reject declaration function
async function rejectDeclaration(id) {
    const { value: reason } = await Swal.fire({
        title: 'Reject Declaration',
        input: 'textarea',
        inputLabel: 'Rejection Reason',
        inputPlaceholder: 'Enter the reason for rejection...',
        showCancelButton: true,
        confirmButtonColor: '#dc3545',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'Reject',
        inputValidator: (value) => {
            if (!value) {
                return 'You need to provide a reason for rejection!';
            }
        }
    });
    
    if (reason) {
        try {
            const res = await fetch('/customs/declaration/' + id + '/reject', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'reason=' + encodeURIComponent(reason)
            });
            
            if (res.ok) {
                await Swal.fire('Rejected!', 'Declaration has been rejected.', 'success');
                location.reload();
            } else {
                const data = await res.json();
                Swal.fire("Error", data.message || "Rejection failed", "error");
            }
        } catch (err) {
            console.error('Rejection error:', err);
            Swal.fire("Error", "Failed to reject declaration", "error");
        }
    }
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    console.log('Customs declarations page initialized');
});


// Auto-generated button handlers

function applyCustomsFilters() {
    const filters = {
        status: document.querySelector('#statusFilter')?.value,
        country: document.querySelector('#countryFilter')?.value,
        date: document.querySelector('#dateFilter')?.value
    };
    
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
        if (value) params.append(key, value);
    });
    
    window.location.search = params.toString();
}
</script>
{% endblock %}