{% extends "base.html" %}

{% block content %}
<div class="dashboard-container">
    <div class="dashboard-header">
        <h1>Clearance Processing</h1>
        <p>Manage customs clearance procedures and cargo release authorizations</p>
    </div>

    <!-- Clearance Statistics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-primary">
                    <i class="fas fa-shipping-fast"></i>
                </div>
                <div class="stat-content">
                    <h3>{{ stats.in_process }}</h3>
                    <p>In Process</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-success">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stat-content">
                    <h3>{{ stats.cleared_today }}</h3>
                    <p>Cleared Today</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-warning">
                    <i class="fas fa-pause-circle"></i>
                </div>
                <div class="stat-content">
                    <h3>{{ stats.on_hold }}</h3>
                    <p>On Hold</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-info">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="stat-content">
                    <h3>{{ stats.avg_clearance }}</h3>
                    <p>Avg. Clearance</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Priority Queue -->
    <div class="card mb-4">
        <div class="card-header">
            <h5>Priority Clearance Queue</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Clearance ID</th>
                            <th>Shipment</th>
                            <th>Importer</th>
                            <th>Cargo Type</th>
                            <th>Value</th>
                            <th>Priority</th>
                            <th>Status</th>
                            <th>Time in Queue</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for decl in priority_declarations %}
                        <tr>
                            <td>{{ decl.clearance_id }}</td>
                            <td>{{ decl.shipment_id }}</td>
                            <td>{{ decl.importer }}</td>
                            <td>{{ decl.cargo_type }}</td>
                            <td>{{ decl.value }}</td>
                            <td>
                                {% if decl.priority == 'Urgent' %}
                                    <span class="badge bg-danger">Urgent</span>
                                {% elif decl.priority == 'High' %}
                                    <span class="badge bg-warning">High</span>
                                {% else %}
                                    <span class="badge bg-success">Standard</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if decl.status == 'SUBMITTED' %}
                                    <span class="badge bg-info">Submitted</span>
                                {% elif decl.status == 'UNDER_REVIEW' %}
                                    <span class="badge bg-warning">In Review</span>
                                {% elif decl.status == 'APPROVED' %}
                                    <span class="badge bg-success">Approved</span>
                                {% elif decl.status == 'HOLD' %}
                                    <span class="badge bg-danger">Hold</span>
                                {% else %}
                                    <span class="badge bg-secondary">{{ decl.status }}</span>
                                {% endif %}
                            </td>
                            <td>{{ decl.time_in_queue }}</td>
                            <td>
                                <button class="btn btn-sm btn-success" onclick="processClearance({{ decl.id }})">Process</button>
                                <button class="btn btn-sm btn-outline-info" onclick="viewClearance({{ decl.id }})">View</button>
                            </td>
                        </tr>
                        {% endfor %}
                        {% if not priority_declarations %}
                        <tr>
                            <td colspan="9" class="text-center text-muted">No priority clearances at this time</td>
                        </tr>
                        {% endif %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Clearance Actions -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button class="btn btn-primary" onclick="newClearanceRequest()">
                            <i class="fas fa-plus me-2"></i>New Clearance Request
                        </button>
                        <button class="btn btn-success" onclick="bulkApprove()">
                            <i class="fas fa-check me-2"></i>Bulk Approve
                        </button>
                        <button class="btn btn-warning" onclick="holdItems()">
                            <i class="fas fa-pause me-2"></i>Hold Items
                        </button>
                        <button class="btn btn-info" onclick="exportReport()">
                            <i class="fas fa-download me-2"></i>Export Report
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>Processing Performance</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <div class="text-center">
                                <h4 class="text-success">94.2%</h4>
                                <small>Same-Day Clearance</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center">
                                <h4 class="text-primary">1.8 hrs</h4>
                                <small>Average Time</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center">
                                <h4 class="text-warning">2.1%</h4>
                                <small>Hold Rate</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center">
                                <h4 class="text-info">328</h4>
                                <small>Today's Total</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Clearances -->
    <div class="card">
        <div class="card-header">
            <h5>Recent Clearance Activity</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Time</th>
                            <th>Clearance ID</th>
                            <th>Importer</th>
                            <th>Cargo</th>
                            <th>Value</th>
                            <th>Status</th>
                            <th>Officer</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for decl in recent_declarations %}
                        <tr>
                            <td>{{ decl.time }}</td>
                            <td>{{ decl.clearance_id }}</td>
                            <td>{{ decl.importer }}</td>
                            <td>{{ decl.cargo }}</td>
                            <td>{{ decl.value }}</td>
                            <td>
                                {% if decl.status == 'APPROVED' %}
                                    <span class="badge bg-success">Cleared</span>
                                {% elif decl.status == 'HOLD' %}
                                    <span class="badge bg-danger">Hold</span>
                                {% elif decl.status == 'UNDER_REVIEW' %}
                                    <span class="badge bg-warning">Review</span>
                                {% elif decl.status == 'SUBMITTED' %}
                                    <span class="badge bg-info">Submitted</span>
                                {% else %}
                                    <span class="badge bg-secondary">{{ decl.status }}</span>
                                {% endif %}
                            </td>
                            <td>{{ decl.officer }}</td>
                            <td>
                                {% if decl.status == 'HOLD' or decl.status == 'UNDER_REVIEW' %}
                                    <button class="btn btn-sm btn-outline-warning" onclick="reviewClearance({{ decl.id }})">Review</button>
                                {% endif %}
                                <button class="btn btn-sm btn-outline-info" onclick="viewClearance({{ decl.id }})">View</button>
                            </td>
                        </tr>
                        {% endfor %}
                        {% if not recent_declarations %}
                        <tr>
                            <td colspan="8" class="text-center text-muted">No recent clearance activity</td>
                        </tr>
                        {% endif %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
// Priority clearance processing functions
async function processClearance(declarationId) {
    try {
        const response = await fetch(`/customs/process-clearance/${declarationId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });
        
        const result = await response.json();
        
        if (result.status === 'success') {
            Swal.fire({
                title: 'Success!',
                text: result.message,
                icon: 'success',
                confirmButtonColor: '#28a745'
            }).then(() => {
                location.reload(); // Refresh page to show updated data
            });
        } else {
            Swal.fire({
                title: 'Error!',
                text: result.message,
                icon: 'error',
                confirmButtonColor: '#dc3545'
            });
        }
    } catch (error) {
        console.error('Error processing clearance:', error);
        Swal.fire({
            title: 'Error!',
            text: 'Failed to process clearance',
            icon: 'error',
            confirmButtonColor: '#dc3545'
        });
    }
}

async function viewClearance(declarationId) {
    try {
        const response = await fetch(`/customs/view-clearance/${declarationId}`);
        const result = await response.json();
        
        if (result.status === 'success') {
            const data = result.data;
            
            Swal.fire({
                title: 'Clearance Details',
                html: `
                    <div class="text-left">
                        <p><strong>Declaration Number:</strong> ${data.declaration_number}</p>
                        <p><strong>Customer:</strong> ${data.customer}</p>
                        <p><strong>Goods Description:</strong> ${data.goods_description}</p>
                        <p><strong>Goods Value:</strong> $${data.goods_value.toLocaleString()}</p>
                        <p><strong>Country of Origin:</strong> ${data.country_of_origin}</p>
                        <p><strong>Duties Amount:</strong> $${data.duties_amount.toLocaleString()}</p>
                        <p><strong>Taxes Amount:</strong> $${data.taxes_amount.toLocaleString()}</p>
                        <p><strong>Status:</strong> <span class="badge bg-info">${data.status}</span></p>
                        <p><strong>Submitted Date:</strong> ${data.submitted_date ? new Date(data.submitted_date).toLocaleDateString() : 'N/A'}</p>
                        <p><strong>Processed Date:</strong> ${data.processed_date ? new Date(data.processed_date).toLocaleDateString() : 'N/A'}</p>
                        ${data.notes ? `<p><strong>Notes:</strong> ${data.notes}</p>` : ''}
                    </div>
                `,
                width: '600px',
                confirmButtonColor: '#007bff'
            });
        } else {
            Swal.fire({
                title: 'Error!',
                text: result.message,
                icon: 'error',
                confirmButtonColor: '#dc3545'
            });
        }
    } catch (error) {
        console.error('Error viewing clearance:', error);
        Swal.fire({
            title: 'Error!',
            text: 'Failed to load clearance details',
            icon: 'error',
            confirmButtonColor: '#dc3545'
        });
    }
}

// Quick actions functions
async function newClearanceRequest() {
    const { value: formValues } = await Swal.fire({
        title: 'New Clearance Request',
        html: `
            <div class="row">
                <div class="col-md-6">
                    <label class="form-label">Declaration Type:</label>
                    <select id="declaration_type" class="form-select">
                        <option value="STANDARD">Standard</option>
                        <option value="EXPRESS">Express</option>
                        <option value="TEMPORARY">Temporary</option>
                    </select>
                </div>
                <div class="col-md-6">
                    <label class="form-label">Country of Origin:</label>
                    <input id="country_of_origin" class="form-control" placeholder="e.g., United States">
                </div>
                <div class="col-12 mt-2">
                    <label class="form-label">Goods Description:</label>
                    <textarea id="goods_description" class="form-control" rows="3" placeholder="Describe the goods being imported"></textarea>
                </div>
                <div class="col-md-6 mt-2">
                    <label class="form-label">Goods Value ($):</label>
                    <input id="goods_value" type="number" class="form-control" placeholder="0.00" step="0.01">
                </div>
                <div class="col-md-6 mt-2">
                    <label class="form-label">Duties Amount ($):</label>
                    <input id="duties_amount" type="number" class="form-control" placeholder="0.00" step="0.01">
                </div>
                <div class="col-md-6 mt-2">
                    <label class="form-label">Taxes Amount ($):</label>
                    <input id="taxes_amount" type="number" class="form-control" placeholder="0.00" step="0.01">
                </div>
                <div class="col-12 mt-2">
                    <label class="form-label">Notes:</label>
                    <textarea id="notes" class="form-control" rows="2" placeholder="Additional notes (optional)"></textarea>
                </div>
            </div>
        `,
        showCancelButton: true,
        confirmButtonText: 'Create Request',
        confirmButtonColor: '#007bff',
        cancelButtonColor: '#6c757d',
        width: '600px',
        preConfirm: () => {
            return {
                declaration_type: document.getElementById('declaration_type').value,
                country_of_origin: document.getElementById('country_of_origin').value,
                goods_description: document.getElementById('goods_description').value,
                goods_value: document.getElementById('goods_value').value,
                duties_amount: document.getElementById('duties_amount').value,
                taxes_amount: document.getElementById('taxes_amount').value,
                notes: document.getElementById('notes').value
            }
        }
    });

    if (formValues) {
        try {
            const formData = new FormData();
            Object.keys(formValues).forEach(key => {
                formData.append(key, formValues[key]);
            });

            const response = await fetch('/customs/new-clearance-request', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.status === 'success') {
                Swal.fire({
                    title: 'Success!',
                    text: result.message,
                    icon: 'success',
                    confirmButtonColor: '#28a745'
                }).then(() => {
                    location.reload();
                });
            } else {
                Swal.fire({
                    title: 'Error!',
                    text: result.message,
                    icon: 'error',
                    confirmButtonColor: '#dc3545'
                });
            }
        } catch (error) {
            console.error('Error creating clearance request:', error);
            Swal.fire({
                title: 'Error!',
                text: 'Failed to create clearance request',
                icon: 'error',
                confirmButtonColor: '#dc3545'
            });
        }
    }
}

async function bulkApprove() {
    const { value: declarationIds } = await Swal.fire({
        title: 'Bulk Approve Declarations',
        html: `
            <p>Enter declaration IDs to approve (comma-separated):</p>
            <input id="declaration_ids" class="form-control" placeholder="e.g., 1,2,3">
            <small class="form-text text-muted">Leave blank to approve all pending declarations</small>
        `,
        showCancelButton: true,
        confirmButtonText: 'Approve Selected',
        confirmButtonColor: '#28a745',
        cancelButtonColor: '#6c757d',
        preConfirm: () => {
            return document.getElementById('declaration_ids').value;
        }
    });

    if (declarationIds !== undefined) {
        try {
            const formData = new FormData();
            formData.append('declaration_ids', declarationIds);

            const response = await fetch('/customs/bulk-approve', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.status === 'success') {
                Swal.fire({
                    title: 'Success!',
                    text: result.message,
                    icon: 'success',
                    confirmButtonColor: '#28a745'
                }).then(() => {
                    location.reload();
                });
            } else {
                Swal.fire({
                    title: 'Error!',
                    text: result.message,
                    icon: 'error',
                    confirmButtonColor: '#dc3545'
                });
            }
        } catch (error) {
            console.error('Error bulk approving:', error);
            Swal.fire({
                title: 'Error!',
                text: 'Failed to bulk approve declarations',
                icon: 'error',
                confirmButtonColor: '#dc3545'
            });
        }
    }
}

async function holdItems() {
    const { value: formValues } = await Swal.fire({
        title: 'Hold Items',
        html: `
            <div class="mb-3">
                <label class="form-label">Declaration IDs to hold (comma-separated):</label>
                <input id="declaration_ids" class="form-control" placeholder="e.g., 1,2,3">
            </div>
            <div class="mb-3">
                <label class="form-label">Hold Reason:</label>
                <select id="hold_reason" class="form-select">
                    <option value="Documentation review required">Documentation review required</option>
                    <option value="Additional inspection needed">Additional inspection needed</option>
                    <option value="Customs valuation required">Customs valuation required</option>
                    <option value="Security concerns">Security concerns</option>
                    <option value="Other">Other</option>
                </select>
            </div>
        `,
        showCancelButton: true,
        confirmButtonText: 'Place on Hold',
        confirmButtonColor: '#ffc107',
        cancelButtonColor: '#6c757d',
        preConfirm: () => {
            return {
                declaration_ids: document.getElementById('declaration_ids').value,
                hold_reason: document.getElementById('hold_reason').value
            }
        }
    });

    if (formValues) {
        try {
            const formData = new FormData();
            formData.append('declaration_ids', formValues.declaration_ids);
            formData.append('hold_reason', formValues.hold_reason);

            const response = await fetch('/customs/hold-items', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.status === 'success') {
                Swal.fire({
                    title: 'Success!',
                    text: result.message,
                    icon: 'success',
                    confirmButtonColor: '#28a745'
                }).then(() => {
                    location.reload();
                });
            } else {
                Swal.fire({
                    title: 'Error!',
                    text: result.message,
                    icon: 'error',
                    confirmButtonColor: '#dc3545'
                });
            }
        } catch (error) {
            console.error('Error holding items:', error);
            Swal.fire({
                title: 'Error!',
                text: 'Failed to hold items',
                icon: 'error',
                confirmButtonColor: '#dc3545'
            });
        }
    }
}

async function exportReport() {
    try {
        Swal.fire({
            title: 'Generating Report...',
            text: 'Please wait while we generate your clearance report',
            icon: 'info',
            allowOutsideClick: false,
            showConfirmButton: false,
            willOpen: () => {
                Swal.showLoading();
            }
        });

        const response = await fetch('/customs/export-report');
        
        if (response.ok) {
            // Get the CSV content
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            
            // Create a temporary link to download the file
            const a = document.createElement('a');
            a.style.display = 'none';
            a.href = url;
            a.download = 'customs_clearance_report.csv';
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);

            Swal.close();
            Swal.fire({
                title: 'Success!',
                text: 'Clearance report has been downloaded',
                icon: 'success',
                confirmButtonColor: '#28a745'
            });
        } else {
            Swal.close();
            Swal.fire({
                title: 'Error!',
                text: 'Failed to generate report',
                icon: 'error',
                confirmButtonColor: '#dc3545'
            });
        }
    } catch (error) {
        console.error('Error exporting report:', error);
        Swal.close();
        Swal.fire({
            title: 'Error!',
            text: 'Failed to export report',
            icon: 'error',
            confirmButtonColor: '#dc3545'
        });
    }
}

async function reviewClearance(declarationId) {
    const { value: formValues } = await Swal.fire({
        title: 'Review Clearance',
        html: `
            <div class="mb-3">
                <label class="form-label">New Status:</label>
                <select id="status" class="form-select">
                    <option value="APPROVED">Approve</option>
                    <option value="REJECTED">Reject</option>
                    <option value="HOLD">Place on Hold</option>
                    <option value="UNDER_REVIEW">Continue Review</option>
                </select>
            </div>
            <div class="mb-3">
                <label class="form-label">Review Notes:</label>
                <textarea id="review_notes" class="form-control" rows="3" placeholder="Enter review comments..."></textarea>
            </div>
        `,
        showCancelButton: true,
        confirmButtonText: 'Update Status',
        confirmButtonColor: '#007bff',
        cancelButtonColor: '#6c757d',
        preConfirm: () => {
            return {
                status: document.getElementById('status').value,
                review_notes: document.getElementById('review_notes').value
            }
        }
    });

    if (formValues) {
        try {
            const formData = new FormData();
            formData.append('status', formValues.status);
            formData.append('review_notes', formValues.review_notes);

            const response = await fetch(`/customs/review-clearance/${declarationId}`, {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.status === 'success') {
                Swal.fire({
                    title: 'Success!',
                    text: result.message,
                    icon: 'success',
                    confirmButtonColor: '#28a745'
                }).then(() => {
                    location.reload();
                });
            } else {
                Swal.fire({
                    title: 'Error!',
                    text: result.message,
                    icon: 'error',
                    confirmButtonColor: '#dc3545'
                });
            }
        } catch (error) {
            console.error('Error reviewing clearance:', error);
            Swal.fire({
                title: 'Error!',
                text: 'Failed to review clearance',
                icon: 'error',
                confirmButtonColor: '#dc3545'
            });
        }
    }
}
</script>
{% endblock %}