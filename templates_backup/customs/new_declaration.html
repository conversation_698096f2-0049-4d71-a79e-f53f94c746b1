{% extends "base.html" %}

{% block title %}New Customs Declaration - Cloverics{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-file-contract me-2"></i>
                        Create New Customs Declaration
                    </h4>
                </div>
                <div class="card-body">
                    <form id="new-declaration-form" method="post" action="/api/customs/declaration/create">
                        <!-- Shipment Selection -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="shipment_id" class="form-label">Select Shipment</label>
                                <select class="form-select" id="shipment_id" name="shipment_id" required>
                                    <option value="">Choose a shipment...</option>
                                    <option value="1">Shipment #SH001 - Electronics</option>
                                    <option value="2">Shipment #SH002 - Textiles</option>
                                    <option value="3">Shipment #SH003 - Machinery</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="declaration_type" class="form-label">Declaration Type</label>
                                <select class="form-select" id="declaration_type" name="declaration_type" required>
                                    <option value="">Select type...</option>
                                    <option value="IMPORT">Import Declaration</option>
                                    <option value="EXPORT">Export Declaration</option>
                                    <option value="TRANSIT">Transit Declaration</option>
                                </select>
                            </div>
                        </div>

                        <!-- Origin and Destination -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="origin_country" class="form-label">Origin Country</label>
                                <select class="form-select" id="origin_country" name="origin_country" required>
                                    <option value="">Select origin...</option>
                                    <option value="United States">United States</option>
                                    <option value="China">China</option>
                                    <option value="Germany">Germany</option>
                                    <option value="United Kingdom">United Kingdom</option>
                                    <option value="Turkey">Turkey</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="destination_country" class="form-label">Destination Country</label>
                                <select class="form-select" id="destination_country" name="destination_country" required>
                                    <option value="">Select destination...</option>
                                    <option value="United States">United States</option>
                                    <option value="China">China</option>
                                    <option value="Germany">Germany</option>
                                    <option value="United Kingdom">United Kingdom</option>
                                    <option value="Turkey">Turkey</option>
                                </select>
                            </div>
                        </div>

                        <!-- Cargo Details -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="cargo_type" class="form-label">Cargo Type</label>
                                <input type="text" class="form-control" id="cargo_type" name="cargo_type" 
                                       placeholder="e.g., Electronics, Textiles" required>
                            </div>
                            <div class="col-md-6">
                                <label for="cargo_value" class="form-label">Cargo Value (USD)</label>
                                <input type="number" class="form-control" id="cargo_value" name="cargo_value" 
                                       placeholder="25000" step="0.01" required>
                            </div>
                        </div>

                        <!-- Description -->
                        <div class="mb-3">
                            <label for="goods_description" class="form-label">Goods Description</label>
                            <textarea class="form-control" id="goods_description" name="goods_description" 
                                      rows="4" placeholder="Detailed description of goods being shipped" required></textarea>
                        </div>

                        <!-- HS Code and Customs Value -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="hs_code" class="form-label">HS Code</label>
                                <input type="text" class="form-control" id="hs_code" name="hs_code" 
                                       placeholder="8471.30.01" pattern="[0-9]{4}\.[0-9]{2}\.[0-9]{2}">
                                <small class="form-text text-muted">Format: XXXX.XX.XX</small>
                            </div>
                            <div class="col-md-6">
                                <label for="customs_value" class="form-label">Customs Value (USD)</label>
                                <input type="number" class="form-control" id="customs_value" name="customs_value" 
                                       placeholder="24500" step="0.01">
                            </div>
                        </div>

                        <!-- Additional Information -->
                        <div class="mb-3">
                            <label for="additional_notes" class="form-label">Additional Notes</label>
                            <textarea class="form-control" id="additional_notes" name="additional_notes" 
                                      rows="3" placeholder="Any additional information for customs processing"></textarea>
                        </div>

                        <!-- Form Actions -->
                        <div class="d-flex justify-content-between">
                            <button type="button" class="btn btn-secondary" onclick="history.back()">
                                <i class="fas fa-arrow-left me-2"></i>Cancel
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Create Declaration
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.getElementById('new-declaration-form').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    
    fetch('/api/customs/declaration/create', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Declaration created successfully!');
            window.location.href = '/customs/declaration';
        } else {
            alert('Error: ' + (data.message || 'Failed to create declaration'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Failed to create declaration');
    });
});


// Auto-generated function implementations

function history.back() {
    // Generic function implementation
    console.log('history.back called');
    showAlert('Function history.back executed', 'info');
}
</script>
{% endblock %}