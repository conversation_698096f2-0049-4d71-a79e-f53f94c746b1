{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <h1><i class="fas fa-envelope"></i> Messages</h1>
                <p class="text-muted">Manage your communications and correspondence</p>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Message Filters -->
        <div class="col-md-3">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-filter"></i> Filters</h5>
                </div>
                <div class="list-group list-group-flush">
                    <a href="javascript:void(0)" class="list-group-item list-group-item-action active" onclick="filterMessages('all')">
                        <i class="fas fa-inbox"></i> All Messages
                        <span class="badge badge-primary float-right">{{ messages|length }}</span>
                    </a>
                    <a href="javascript:void(0)" class="list-group-item list-group-item-action" onclick="filterMessages('unread')">
                        <i class="fas fa-envelope"></i> Unread
                        <span class="badge badge-warning float-right">{% set unread_count = messages|selectattr("unread")|list|length %}{{ unread_count }}</span>
                    </a>
                    <a href="javascript:void(0)" class="list-group-item list-group-item-action" onclick="filterMessages('shipment')">
                        <i class="fas fa-truck"></i> Shipment Updates
                    </a>
                    <a href="javascript:void(0)" class="list-group-item list-group-item-action" onclick="filterMessages('quote')">
                        <i class="fas fa-dollar-sign"></i> Quotes
                    </a>
                    <a href="javascript:void(0)" class="list-group-item list-group-item-action" onclick="filterMessages('system')">
                        <i class="fas fa-cog"></i> System Messages
                    </a>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5><i class="fas fa-bolt"></i> Quick Actions</h5>
                </div>
                <div class="card-body">
                    <button class="btn btn-primary btn-block mb-2" onclick="composeMessage()">
                        <i class="fas fa-edit"></i> Compose Message
                    </button>
                    <button class="btn btn-outline-secondary btn-block mb-2" onclick="markAllRead()">
                        <i class="fas fa-check-double"></i> Mark All Read
                    </button>
                    <button class="btn btn-outline-danger btn-block" onclick="deleteSelected()">
                        <i class="fas fa-trash"></i> Delete Selected
                    </button>
                </div>
            </div>
        </div>

        <!-- Messages List -->
        <div class="col-md-9">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5><i class="fas fa-list"></i> Message List</h5>
                    <div class="btn-group" role="group">
                        <button class="btn btn-sm btn-outline-secondary" onclick="refreshMessages()">
                            <i class="fas fa-sync"></i> Refresh
                        </button>
                        <button class="btn btn-sm btn-outline-secondary" onclick="exportMessages()">
                            <i class="fas fa-download"></i> Export
                        </button>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="message-list">
                        {% for message in messages %}
                        <div class="message-item {% if message.unread %}unread{% endif %}" data-type="{{ message.type }}" data-id="{{ message.id }}">
                            <div class="message-checkbox">
                                <input type="checkbox" class="form-check-input" id="msg-{{ message.id }}">
                            </div>
                            <div class="message-content" onclick="openMessage({{ message.id }})">
                                <div class="message-header">
                                    <div class="sender">
                                        <strong>{{ message.sender }}</strong>
                                        {% if message.type == 'shipment' %}
                                            <span class="badge badge-info">Shipment</span>
                                        {% elif message.type == 'quote' %}
                                            <span class="badge badge-success">Quote</span>
                                        {% elif message.type == 'system' %}
                                            <span class="badge badge-warning">System</span>
                                        {% endif %}
                                    </div>
                                    <div class="timestamp">{{ message.timestamp }}</div>
                                </div>
                                <div class="subject">{{ message.subject }}</div>
                                <div class="preview">{{ message.preview }}</div>
                            </div>
                            <div class="message-actions">
                                <button class="btn btn-sm btn-outline-primary" onclick="replyToMessage({{ message.id }})" title="Reply">
                                    <i class="fas fa-reply"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-secondary" onclick="forwardMessage({{ message.id }})" title="Forward">
                                    <i class="fas fa-share"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-danger" onclick="deleteMessage({{ message.id }})" title="Delete">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>

            <!-- Pagination -->
            <nav aria-label="Messages pagination" class="mt-3">
                <ul class="pagination justify-content-center">
                    <li class="page-item disabled">
                        <a class="page-link" href="javascript:void(0)" tabindex="-1">Previous</a>
                    </li>
                    <li class="page-item active">
                        <a class="page-link" href="javascript:void(0)">1</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="javascript:void(0)">2</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="javascript:void(0)">3</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="javascript:void(0)">Next</a>
                    </li>
                </ul>
            </nav>
        </div>
    </div>
</div>

<!-- Message Detail Modal -->
<div class="modal fade" id="messageModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="messageModalTitle">Message Details</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body" id="messageModalBody">
                <!-- Message content will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" onclick="replyToCurrentMessage()">
                    <i class="fas fa-reply"></i> Reply
                </button>
                <button type="button" class="btn btn-outline-secondary" onclick="forwardCurrentMessage()">
                    <i class="fas fa-share"></i> Forward
                </button>
                <button type="button" class="btn btn-outline-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<style>
.message-item {
    display: flex;
    align-items: center;
    padding: 15px;
    border-bottom: 1px solid #eee;
    cursor: pointer;
    transition: background-color 0.2s;
}

.message-item:hover {
    background-color: #f8f9fa;
}

.message-item.unread {
    background-color: #fff3cd;
    border-left: 4px solid #ffc107;
}

.message-checkbox {
    width: 30px;
    margin-right: 15px;
}

.message-content {
    flex: 1;
    min-width: 0;
}

.message-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 5px;
}

.sender {
    flex: 1;
    font-weight: 600;
}

.timestamp {
    color: #6c757d;
    font-size: 0.875rem;
}

.subject {
    font-weight: 500;
    margin-bottom: 5px;
    color: #495057;
}

.preview {
    color: #6c757d;
    font-size: 0.875rem;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.message-actions {
    display: flex;
    gap: 5px;
    margin-left: 15px;
}

.message-list {
    max-height: 600px;
    overflow-y: auto;
}

.list-group-item {
    border: none;
    padding: 12px 15px;
}

.list-group-item.active {
    background-color: #007bff;
    border-color: #007bff;
}
</style>

<script>
function filterMessages(type) {
    const messages = document.querySelectorAll('.message-item');
    const filters = document.querySelectorAll('.list-group-item');
    
    // Update active filter
    filters.forEach(filter => filter.classList.remove('active'));
    event.target.classList.add('active');
    
    // Filter messages
    messages.forEach(message => {
        if (type === 'all') {
            message.style.display = 'flex';
        } else if (type === 'unread') {
            message.style.display = message.classList.contains('unread') ? 'flex' : 'none';
        } else {
            message.style.display = message.dataset.type === type ? 'flex' : 'none';
        }
    });
}

function openMessage(messageId) {
    // Mock message detail content
    const messageContent = `
        <div class="message-detail">
            <div class="message-meta">
                <strong>From:</strong> John Miller (Independent Driver)<br>
                <strong>To:</strong> ${user.email}<br>
                <strong>Date:</strong> January 26, 2025 2:30 PM<br>
                <strong>Subject:</strong> Shipment CLV-2025-001 - Ready for Pickup
            </div>
            <hr>
            <div class="message-body">
                <p>Hello,</p>
                <p>I hope this message finds you well. I'm writing to inform you that your shipment CLV-2025-001 is ready for pickup at the warehouse.</p>
                <p>Pickup Details:</p>
                <ul>
                    <li><strong>Time:</strong> Today at 2:00 PM</li>
                    <li><strong>Location:</strong> 123 Warehouse St, Industrial District</li>
                    <li><strong>Contact:</strong> (*************</li>
                </ul>
                <p>Please ensure someone is available to receive the shipment. If you need to reschedule, please let me know as soon as possible.</p>
                <p>Best regards,<br>John Miller<br>Independent Driver</p>
            </div>
        </div>
    `;
    
    document.getElementById('messageModalTitle').textContent = 'Shipment CLV-2025-001 - Ready for Pickup';
    document.getElementById('messageModalBody').innerHTML = messageContent;
    $('#messageModal').modal('show');
    
    // Mark as read
    const messageItem = document.querySelector(`[data-id="${messageId}"]`);
    messageItem.classList.remove('unread');
}

function composeMessage() {
    // Navigate to compose message page
    window.location.href = '/messages/compose';
}

function replyToMessage(messageId) {
    // Navigate to reply page with message ID
    window.location.href = `/messages/reply/${messageId}`;
}

function forwardMessage(messageId) {
    // Navigate to forward page with message ID
    window.location.href = `/messages/forward/${messageId}`;
}

function deleteMessage(messageId) {
    if (confirm('Are you sure you want to delete this message?')) {
        // Remove message from UI immediately for better UX
        const messageItem = document.querySelector(`[data-id="${messageId}"]`);
        if (messageItem) {
            messageItem.remove();
            showAlert('Message deleted successfully', 'success');
        }
    }
}

function markAllRead() {
    // Submit form to mark all messages as read
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '/messages/mark-all-read';
    document.body.appendChild(form);
    form.submit();
}

function deleteSelected() {
    const selectedCheckboxes = document.querySelectorAll('.message-item input[type="checkbox"]:checked');
    if (selectedCheckboxes.length === 0) {
        showAlert('Please select messages to delete', 'warning');
        return;
    }
    
    if (confirm(`Are you sure you want to delete ${selectedCheckboxes.length} selected message(s)?`)) {
        selectedCheckboxes.forEach(checkbox => {
            checkbox.closest('.message-item').remove();
        });
        showAlert(`${selectedCheckboxes.length} message(s) deleted successfully`, 'success');
    }
}

function refreshMessages() {
    // Reload the page to refresh messages
    window.location.reload();
}

function exportMessages() {
    // Navigate to export endpoint
    window.location.href = '/messages/export';
}

function replyToCurrentMessage() {
    $('#messageModal').modal('hide');
    showAlert('Reply editor will open', 'info');
}

function forwardCurrentMessage() {
    $('#messageModal').modal('hide');
    showAlert('Forward editor will open', 'info');
}

function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="close" data-dismiss="alert">
            <span>&times;</span>
        </button>
    `;
    
    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);
    
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}
</script>
{% endblock %}