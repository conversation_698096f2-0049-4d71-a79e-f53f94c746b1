{% extends "base.html" %}

{% block title %}Shipment {{ shipment.get('tracking_number', 'N/A') }} - Cloverics{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="page-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="h3 mb-2">
                            <i class="fas fa-shipping-fast text-primary"></i>
                            Shipment Details
                        </h1>
                        <p class="text-muted mb-0">
                            Unified view for {{ shipment.get('tracking_number', 'N/A') }} - {{ shipment.get('origin_type', 'direct').title() }}
                        </p>
                    </div>
                    <div class="page-actions">
                        <button class="btn btn-outline-primary" onclick="window.print()">
                            <i class="fas fa-print"></i> Print
                        </button>
                        <button class="btn btn-primary" onclick="refreshProgress()">
                            <i class="fas fa-sync-alt"></i> Refresh
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Process Tracker Component -->
    {% include "components/process_tracker.html" %}

    <!-- Shipment Information Grid -->
    <div class="row">
        <!-- Basic Information -->
        <div class="col-lg-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle"></i> Basic Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="info-item mb-3">
                                <label class="text-muted small">Tracking Number</label>
                                <div class="font-weight-bold h5 text-primary">{{ shipment.get('tracking_number', 'N/A') }}</div>
                            </div>
                            <div class="info-item mb-3">
                                <label class="text-muted small">Status</label>
                                <div>
                                    <span class="badge badge-{{ 'success' if shipment.get('status') == 'DELIVERED' else 'primary' if shipment.get('status') == 'IN_TRANSIT' else 'warning' }}">
                                        {{ shipment.get('status', 'N/A').title() }}
                                    </span>
                                </div>
                            </div>
                            <div class="info-item mb-3">
                                <label class="text-muted small">Customer</label>
                                <div class="font-weight-bold">{{ shipment.get('customer_name', 'N/A') }}</div>
                                <small class="text-muted">{{ shipment.get('customer_email', 'N/A') }}</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-item mb-3">
                                <label class="text-muted small">Logistics Provider</label>
                                <div class="font-weight-bold">{{ shipment.get('logistics_provider_name', 'N/A') }}</div>
                                <small class="text-muted">{{ shipment.get('logistics_provider_email', 'N/A') }}</small>
                            </div>
                            <div class="info-item mb-3">
                                <label class="text-muted small">Booking Date</label>
                                <div>
                                    {% if shipment.get('booking_date') and shipment.get('booking_date').__class__.__name__ != 'str' %}
                                        {{ shipment.booking_date.strftime('%b %d, %Y %H:%M') }}
                                    {% else %}
                                        {{ shipment.get('booking_date', 'Not set') }}
                                    {% endif %}
                                </div>
                            </div>
                            <div class="info-item mb-3">
                                <label class="text-muted small">Estimated Delivery</label>
                                <div>
                                    {% if shipment.get('estimated_delivery_date') and shipment.get('estimated_delivery_date').__class__.__name__ != 'str' %}
                                        {{ shipment.estimated_delivery_date.strftime('%b %d, %Y %H:%M') }}
                                    {% else %}
                                        {{ shipment.get('estimated_delivery_date', 'Not set') }}
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Route Information -->
        <div class="col-lg-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-route"></i> Route Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="route-display">
                        <div class="d-flex align-items-center mb-3">
                            <div class="route-point text-center">
                                <div class="route-icon bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                    <i class="fas fa-map-marker-alt"></i>
                                </div>
                                <div class="mt-2">
                                    <div class="font-weight-bold">{{ shipment.get('origin_city', 'N/A') }}</div>
                                    <small class="text-muted">{{ shipment.get('origin_country', 'N/A') }}</small>
                                </div>
                            </div>
                            <div class="route-line flex-grow-1 mx-3">
                                <div class="border-top border-primary border-2" style="position: relative;">
                                    <i class="fas fa-arrow-right text-primary" style="position: absolute; top: -8px; left: 50%; transform: translateX(-50%);"></i>
                                </div>
                            </div>
                            <div class="route-point text-center">
                                <div class="route-icon bg-success text-white rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                    <i class="fas fa-flag-checkered"></i>
                                </div>
                                <div class="mt-2">
                                    <div class="font-weight-bold">{{ shipment.get('destination_city', 'N/A') }}</div>
                                    <small class="text-muted">{{ shipment.get('destination_country', 'N/A') }}</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-6">
                            <div class="info-item">
                                <label class="text-muted small">Shipping Type</label>
                                <div class="font-weight-bold">{{ shipment.get('shipping_type', 'N/A').title() }}</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="info-item">
                                <label class="text-muted small">Transport</label>
                                <div class="font-weight-bold">
                                    {% if shipment.get('transport_type') %}
                                        {{ shipment.get('transport_type', 'N/A')|title }}
                                    {% else %}
                                        Not specified
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Cargo Details -->
        <div class="col-lg-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-boxes"></i> Cargo Details
                    </h5>
                </div>
                <div class="card-body">
                    <div class="info-item mb-3">
                        <label class="text-muted small">Description</label>
                        <div>{{ shipment.get('cargo_description', 'N/A') }}</div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="info-item mb-3">
                                <label class="text-muted small">Weight</label>
                                <div class="font-weight-bold">{{ shipment.get('weight_kg', 0) }} kg</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="info-item mb-3">
                                <label class="text-muted small">Volume</label>
                                <div class="font-weight-bold">
                                    {% if shipment.get('volume_m3') %}
                                        {{ shipment.get('volume_m3') }} m³
                                    {% else %}
                                        Not specified
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="info-item mb-3">
                                <label class="text-muted small">Containers</label>
                                <div class="font-weight-bold">{{ shipment.get('container_count', 0) }}</div>
                            </div>
                        </div>
                    </div>

                    {% if shipment.get('has_hazardous_items', False) %}
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            <strong>Hazardous Materials:</strong> This shipment contains hazardous items requiring special handling.
                        </div>
                    {% endif %}

                    {% if shipment.get('cargo_type') %}
                        <div class="info-item">
                            <label class="text-muted small">Cargo Type</label>
                            <div class="font-weight-bold">{{ shipment.get('cargo_type_name', 'N/A') }}</div>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Financial Information -->
        <div class="col-lg-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-danger text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-dollar-sign"></i> Financial Details
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="info-item mb-3">
                                <label class="text-muted small">Base Price</label>
                                <div class="font-weight-bold h5 text-success">${{ shipment.get('base_price', 0) }}</div>
                            </div>
                            <div class="info-item mb-3">
                                <label class="text-muted small">Total Price</label>
                                <div class="font-weight-bold h4 text-primary">${{ shipment.get('total_price', 0) }}</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-item mb-3">
                                <label class="text-muted small">Payment Status</label>
                                <div>
                                    <span class="badge badge-{{ 'success' if shipment.get('payment_status', 'N/A') == 'COMPLETED' else 'warning' }}">
                                        {{ shipment.get('payment_status', 'N/A')|title }}
                                    </span>
                                </div>
                            </div>
                            <div class="info-item mb-3">
                                <label class="text-muted small">Payment Method</label>
                                <div class="font-weight-bold">{{ shipment.get('payment_method', 'N/A').title() }}</div>
                            </div>
                        </div>
                    </div>

                    {% if shipment.get('is_insured', False) %}
                        <div class="alert alert-info">
                            <i class="fas fa-shield-alt"></i>
                            <strong>Insured Shipment:</strong> Coverage amount ${{ shipment.get('insurance_cost', 0) }}
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Additional Details Tabs -->
    <div class="card mt-4">
        <div class="card-header">
            <ul class="nav nav-tabs card-header-tabs" id="shipmentTabs" role="tablist">
                <li class="nav-item">
                    <a class="nav-link active" id="timeline-tab" onclick="switchTab('timeline')" href="#timeline" role="tab">
                        <i class="fas fa-history"></i> Timeline
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" id="documents-tab" onclick="switchTab('documents')" href="#documents" role="tab">
                        <i class="fas fa-file-alt"></i> Documents
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" id="tracking-tab" onclick="switchTab('tracking')" href="#tracking" role="tab">
                        <i class="fas fa-map-marked-alt"></i> Tracking
                    </a>
                </li>
                {% if shipment.get('origin_type', 'direct') != 'direct' %}
                    <li class="nav-item">
                        <a class="nav-link" id="source-tab" onclick="switchTab('source')" href="#source" role="tab">
                            <i class="fas fa-link"></i> Source Details
                        </a>
                    </li>
                {% endif %}
            </ul>
        </div>
        <div class="card-body">
            <div class="tab-content" id="shipmentTabsContent">
                <!-- Timeline Tab -->
                <div class="tab-pane fade show active" id="timeline" role="tabpanel">
                    <div class="timeline">
                        {% for update in shipment.get('status_updates', []) %}
                            <div class="timeline-item">
                                <div class="timeline-marker bg-primary"></div>
                                <div class="timeline-content">
                                    <h6 class="mb-1">{{ update.status }}</h6>
                                    <p class="mb-1">{{ update.description }}</p>
                                    <small class="text-muted">{{ update.timestamp if update.timestamp else 'Not set' }}</small>
                                </div>
                            </div>
                        {% else %}
                            <p class="text-muted">No status updates available yet.</p>
                        {% endfor %}
                    </div>
                </div>

                <!-- Documents Tab -->
                <div class="tab-pane fade" id="documents" role="tabpanel">
                    <div class="documents-list">
                        {% for document in shipment.get('documents', []) %}
                            <div class="document-item border rounded p-3 mb-2">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="mb-1">{{ document.name }}</h6>
                                        <small class="text-muted">{{ document.type }} - {{ document.size }}</small>
                                    </div>
                                    <div>
                                        <a href="{{ document.url }}" class="btn btn-sm btn-outline-primary" target="_blank">
                                            <i class="fas fa-download"></i> Download
                                        </a>
                                    </div>
                                </div>
                            </div>
                        {% else %}
                            <p class="text-muted">No documents uploaded yet.</p>
                        {% endfor %}
                    </div>
                </div>

                <!-- Tracking Tab -->
                <div class="tab-pane fade" id="tracking" role="tabpanel">
                    <div class="tracking-info">
                        {% if shipment.get('current_location') %}
                            <div class="current-location mb-4">
                                <h6>Current Location</h6>
                                <p class="mb-1"><strong>{{ shipment.get('current_location', {}).get('name', 'N/A') }}</strong></p>
                                <p class="text-muted">
                                    Lat: {{ shipment.get('current_location', {}).get('latitude', 'N/A') }}, 
                                    Lng: {{ shipment.get('current_location', {}).get('longitude', 'N/A') }}
                                </p>
                                <small class="text-muted">Last updated: {{ shipment.get('current_location', {}).get('timestamp') if shipment.get('current_location', {}).get('timestamp') else 'Not set' }}</small>
                            </div>
                        {% else %}
                            <p class="text-muted">Location tracking not available.</p>
                        {% endif %}
                    </div>
                </div>

                <!-- Source Details Tab -->
                {% if shipment.get('origin_type', 'direct') != 'direct' %}
                    <div class="tab-pane fade" id="source" role="tabpanel">
                        <div class="source-details">
                            <h6>{{ shipment.get('origin_type', 'direct').title() }} Details</h6>
                            
                            {% if shipment.get('origin_type', 'direct') == 'quote' and shipment.get('source_quote_id') %}
                                <div class="alert alert-info">
                                    <i class="fas fa-quote-left"></i>
                                    <strong>Created from Quote:</strong> This shipment was created by accepting quote #{{ shipment.get('source_quote_id') }}.
                                    <div class="mt-2">
                                        <a href="/customer/quote-details/{{ shipment.get('source_quote_id') }}" class="btn btn-sm btn-info">
                                            View Original Quote
                                        </a>
                                    </div>
                                </div>
                            {% elif shipment.get('origin_type', 'direct') == 'private' and shipment.get('source_invitation_id') %}
                                <div class="alert alert-warning">
                                    <i class="fas fa-user-friends"></i>
                                    <strong>Created from Private Invitation:</strong> This shipment was created by accepting a private shipping invitation #{{ shipment.get('source_invitation_id') }}.
                                </div>
                            {% endif %}
                        </div>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<style>
.page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    border-radius: 10px;
    margin-bottom: 2rem;
}

.info-item label {
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.route-display .route-line {
    height: 2px;
}

.timeline {
    position: relative;
    padding-left: 2rem;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 1rem;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 1.5rem;
}

.timeline-marker {
    position: absolute;
    left: -2rem;
    top: 0.25rem;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid white;
}

.timeline-content {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 5px;
    border-left: 3px solid #007bff;
}

.document-item:hover {
    background-color: #f8f9fa;
}

.card-header-tabs .nav-link {
    border: none;
    color: #6c757d;
}

.card-header-tabs .nav-link.active {
    background-color: transparent;
    border-bottom: 2px solid #007bff;
    color: #007bff;
    font-weight: 600;
}
</style>


<script>
function switchTab(tabName) {
    // Remove active class from all tabs and content
    document.querySelectorAll('.nav-link').forEach(tab => {
        tab.classList.remove('active');
    });
    document.querySelectorAll('.tab-pane').forEach(content => {
        content.classList.remove('show', 'active');
    });
    
    // Add active class to clicked tab and corresponding content
    document.getElementById(tabName + '-tab').classList.add('active');
    document.getElementById(tabName).classList.add('show', 'active');
    
    // Prevent default link behavior
    event.preventDefault();
    return false;
}

function refreshProgress() {
    const shipmentId = {{ shipment.get('id', 0) }};
    
    fetch(`/api/shipment/${shipmentId}/progress`)
        .then(response => response.json())
        .then(data => {
            if (!data.error) {
                location.reload(); // Simple refresh for now
            } else {
                console.error('Error refreshing progress:', data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
        });
}

function showStageActionModal(stage, action) {
    const shipmentId = {{ shipment.get('id', 0) }};
    
    // Map action to valid backend actions
    const actionMap = {
        'advance': 'complete',
        'start': 'start',
        'approve': 'approve',
        'reject': 'reject'
    };
    
    const validAction = actionMap[action] || 'complete';
    const actionLabel = action === 'advance' ? 'advance to next stage' : action;
    
    // Create modal HTML with Bootstrap 5 structure
    const modalHtml = `
        <div class="modal fade" id="stageActionModal" tabindex="-1" aria-labelledby="stageActionModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="stageActionModalLabel">Stage Action: ${stage.charAt(0).toUpperCase() + stage.slice(1)}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <p>Are you sure you want to ${actionLabel} the <strong>${stage}</strong> stage for this shipment?</p>
                        <div class="mb-3">
                            <label for="actionNotes" class="form-label">Notes (optional):</label>
                            <textarea class="form-control" id="actionNotes" rows="3" placeholder="Add any notes about this action..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-primary" onclick="executeStageAction('${stage}', '${validAction}')">
                            ${action.charAt(0).toUpperCase() + action.slice(1)}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // Remove existing modal and add new one
    const existingModal = document.getElementById('stageActionModal');
    if (existingModal) {
        existingModal.remove();
    }
    document.body.insertAdjacentHTML('beforeend', modalHtml);
    
    // Show modal using Bootstrap 5 API
    const modal = new bootstrap.Modal(document.getElementById('stageActionModal'));
    modal.show();
}

function executeStageAction(stage, action) {
    const shipmentId = {{ shipment.get('id', 0) }};
    const notes = document.getElementById('actionNotes').value;
    
    console.log('🔄 Executing stage action:', {
        shipmentId: shipmentId,
        stage: stage,
        action: action,
        notes: notes
    });
    
    fetch(`/api/shipment/${shipmentId}/stage/${stage}/${action}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            notes: notes,
            stage: stage,
            action: action
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Hide modal using Bootstrap 5 API
            const modalElement = document.getElementById('stageActionModal');
            if (modalElement) {
                const modal = bootstrap.Modal.getInstance(modalElement);
                if (modal) {
                    modal.hide();
                } else {
                    modalElement.remove();
                }
            }
            // Show success message
            showAlert('success', `Stage ${stage} ${action} successfully completed!`);
            // Refresh the page after 2 seconds
            setTimeout(() => {
                location.reload();
            }, 2000);
        } else {
            showAlert('error', data.error || 'Failed to execute stage action');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('error', 'An error occurred while executing the stage action');
    });
}

function showAlert(type, message) {
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert" style="position: fixed; top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
            ${message}
            <button type="button" class="btn-close" aria-label="Close" onclick="this.parentElement.remove()">
                <span>&times;</span>
            </button>
        </div>
    `;
    
    document.body.insertAdjacentHTML('beforeend', alertHtml);
    
    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(alert => alert.remove());
    }, 5000);
}

// Initialize Bootstrap tabs and tooltips when document is ready
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Bootstrap tabs
    const tabLinks = document.querySelectorAll('#shipmentTabs a');
    tabLinks.forEach(tab => {
        tab.addEventListener('click', function(e) {
            e.preventDefault();
            const tabInstance = new bootstrap.Tab(this);
            tabInstance.show();
        });
    });
    
    // Initialize tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    const tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});


// Auto-generated function implementations

function window.print() {
    // Generic function implementation
    console.log('window.print called');
    showAlert('Function window.print executed', 'info');
}

function this.parentElement.remove(id) {
    // Delete functionality with confirmation
    if (confirm('Are you sure you want to delete this item?')) {
        fetch(`/api/delete/${id || 'item'}`, {
            method: 'DELETE',
            headers: {'X-CSRFToken': getCSRFToken()}
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('Item deleted successfully', 'success');
                location.reload();
            } else {
                showAlert('Delete failed: ' + data.error, 'error');
            }
        })
        .catch(error => showAlert('Delete failed', 'error'));
    }
}
</script>
{% endblock %}