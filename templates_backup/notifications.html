{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <h1><i class="fas fa-bell"></i> Notifications</h1>
                <p class="text-muted">Stay updated with your shipments and account activities</p>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5><i class="fas fa-inbox"></i> Your Notifications</h5>
                    <div class="btn-group">
                        <button class="btn btn-outline-primary btn-sm" onclick="markAllAsRead()">
                            <i class="fas fa-check-double"></i> Mark All Read
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" onclick="filterNotifications('unread')">
                            <i class="fas fa-filter"></i> Show Unread
                        </button>
                        <button class="btn btn-outline-info btn-sm" onclick="filterNotifications('all')">
                            <i class="fas fa-list"></i> Show All
                        </button>
                    </div>
                </div>
                <div class="card-body p-0">
                    {% if notifications %}
                        <div class="notifications-list">
                            {% for notification in notifications %}
                            <div class="notification-item {% if not notification.is_read %}notification-unread{% endif %}" 
                                 data-notification-id="{{ notification.id }}" 
                                 data-read="{{ notification.is_read|lower }}">
                                <div class="notification-content">
                                    <div class="notification-header">
                                        <div class="notification-title">
                                            {% if notification.type == 'SHIPMENT_UPDATE' %}
                                                <i class="fas fa-shipping-fast text-primary"></i>
                                            {% elif notification.type == 'PAYMENT_CONFIRMED' %}
                                                <i class="fas fa-check-circle text-success"></i>
                                            {% elif notification.type == 'NEW_QUOTE' %}
                                                <i class="fas fa-file-invoice-dollar text-info"></i>
                                            {% elif notification.type == 'ACCOUNT_VERIFIED' %}
                                                <i class="fas fa-user-check text-success"></i>
                                            {% elif notification.type == 'SYSTEM_ALERT' %}
                                                <i class="fas fa-exclamation-triangle text-warning"></i>
                                            {% else %}
                                                <i class="fas fa-info-circle text-secondary"></i>
                                            {% endif %}
                                            <strong>{{ notification.title }}</strong>
                                            {% if notification.priority == 'HIGH' %}
                                                <span class="badge badge-danger ml-2">High Priority</span>
                                            {% elif notification.priority == 'LOW' %}
                                                <span class="badge badge-secondary ml-2">Low Priority</span>
                                            {% endif %}
                                        </div>
                                        <div class="notification-meta">
                                            <small class="text-muted">{{ notification.timestamp }}</small>
                                            {% if not notification.is_read %}
                                                <span class="unread-indicator"></span>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="notification-message">
                                        {{ notification.message }}
                                    </div>
                                    <div class="notification-actions">
                                        {% if not notification.is_read %}
                                            <button class="btn btn-sm btn-outline-primary" 
                                                    onclick="markAsRead({{ notification.id }})">
                                                <i class="fas fa-check"></i> Mark as Read
                                            </button>
                                        {% endif %}
                                        {% if notification.type == 'SHIPMENT_UPDATE' %}
                                            <a href="/customer/track-shipment" class="btn btn-sm btn-outline-info">
                                                <i class="fas fa-search"></i> Track Shipment
                                            </a>
                                        {% elif notification.type == 'NEW_QUOTE' %}
                                            <a href="/customer/manage-shipments" class="btn btn-sm btn-outline-success">
                                                <i class="fas fa-eye"></i> View Quote
                                            </a>
                                        {% elif notification.type == 'PAYMENT_CONFIRMED' %}
                                            <a href="/customer/manage-shipments" class="btn btn-sm btn-outline-info">
                                                <i class="fas fa-receipt"></i> View Details
                                            </a>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="empty-state text-center py-5">
                            <i class="fas fa-bell-slash fa-3x text-muted mb-3"></i>
                            <h5>No notifications yet</h5>
                            <p class="text-muted">You'll see updates about your shipments and account here</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Notification Statistics -->
    <div class="row mt-4">
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 class="text-primary">{{ notifications|length }}</h3>
                    <p class="card-text">Total Notifications</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 class="text-danger">
                        {% set unread_count = notifications|selectattr('is_read', 'equalto', false)|list|length %}
                        {{ unread_count }}
                    </h3>
                    <p class="card-text">Unread</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 class="text-warning">
                        {% set high_priority_count = notifications|selectattr('priority', 'equalto', 'HIGH')|list|length %}
                        {{ high_priority_count }}
                    </h3>
                    <p class="card-text">High Priority</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 class="text-info">
                        {% set shipment_count = notifications|selectattr('type', 'equalto', 'SHIPMENT_UPDATE')|list|length %}
                        {{ shipment_count }}
                    </h3>
                    <p class="card-text">Shipment Updates</p>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.notifications-list {
    max-height: 600px;
    overflow-y: auto;
}

.notification-item {
    border-bottom: 1px solid #eee;
    padding: 15px 20px;
    transition: background-color 0.2s;
}

.notification-item:hover {
    background-color: #f8f9fa;
}

.notification-unread {
    background-color: #f0f8ff;
    border-left: 4px solid #007bff;
}

.notification-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 8px;
}

.notification-title {
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
}

.notification-meta {
    display: flex;
    align-items: center;
    gap: 8px;
}

.unread-indicator {
    width: 8px;
    height: 8px;
    background-color: #dc3545;
    border-radius: 50%;
    display: inline-block;
}

.notification-message {
    color: #666;
    margin-bottom: 10px;
    line-height: 1.4;
}

.notification-actions {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.empty-state {
    padding: 60px 20px;
}
</style>

<script>
function markAsRead(notificationId) {
    fetch(`/notifications/${notificationId}/mark-read`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            // Update UI to show notification as read
            const notificationItem = document.querySelector(`[data-notification-id="${notificationId}"]`);
            notificationItem.classList.remove('notification-unread');
            notificationItem.setAttribute('data-read', 'true');
            
            // Remove the "Mark as Read" button
            const markReadBtn = notificationItem.querySelector('button[onclick*="markAsRead"]');
            if (markReadBtn) {
                markReadBtn.remove();
            }
            
            // Remove unread indicator
            const unreadIndicator = notificationItem.querySelector('.unread-indicator');
            if (unreadIndicator) {
                unreadIndicator.remove();
            }
            
            // Update notification count in the header
            updateNotificationCount();
        }
    })
    .catch(error => {
        console.error('Error marking notification as read:', error);
    });
}

function markAllAsRead() {
    fetch('/notifications/mark-all-read', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            // Update all notifications to show as read
            document.querySelectorAll('.notification-unread').forEach(item => {
                item.classList.remove('notification-unread');
                item.setAttribute('data-read', 'true');
            });
            
            // Remove all "Mark as Read" buttons
            document.querySelectorAll('button[onclick*="markAsRead"]').forEach(btn => {
                btn.remove();
            });
            
            // Remove all unread indicators
            document.querySelectorAll('.unread-indicator').forEach(indicator => {
                indicator.remove();
            });
            
            // Update notification count
            updateNotificationCount();
        }
    })
    .catch(error => {
        console.error('Error marking all notifications as read:', error);
    });
}

function filterNotifications(filter) {
    const notifications = document.querySelectorAll('.notification-item');
    
    notifications.forEach(notification => {
        const isRead = notification.getAttribute('data-read') === 'true';
        
        if (filter === 'all') {
            notification.style.display = 'block';
        } else if (filter === 'unread') {
            notification.style.display = isRead ? 'none' : 'block';
        }
    });
}

function updateNotificationCount() {
    // This would typically update the notification count in the header
    // For now, we'll just update the page statistics
    const unreadCount = document.querySelectorAll('.notification-unread').length;
    // Update the header badge if it exists
    const notificationBadge = document.querySelector('.notification-count');
    if (notificationBadge) {
        notificationBadge.textContent = unreadCount;
        if (unreadCount === 0) {
            notificationBadge.style.display = 'none';
        }
    }
}
</script>
{% endblock %}