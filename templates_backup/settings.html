{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <h1><i class="fas fa-cogs"></i> Account Settings</h1>
                <p class="text-muted">Configure your account preferences and security settings</p>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Settings Navigation -->
        <div class="col-md-3">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-list"></i> Settings Categories</h5>
                </div>
                <div class="list-group list-group-flush">
                    <a href="#security" class="list-group-item list-group-item-action active" onclick="showSection('security')">
                        <i class="fas fa-shield-alt"></i> Security
                    </a>
                    <a href="#notifications" class="list-group-item list-group-item-action" onclick="showSection('notifications')">
                        <i class="fas fa-bell"></i> Notifications
                    </a>
                    <a href="#privacy" class="list-group-item list-group-item-action" onclick="showSection('privacy')">
                        <i class="fas fa-user-secret"></i> Privacy
                    </a>
                    <a href="#billing" class="list-group-item list-group-item-action" onclick="showSection('billing')">
                        <i class="fas fa-credit-card"></i> Billing & Payments
                    </a>
                    <a href="#integrations" class="list-group-item list-group-item-action" onclick="showSection('integrations')">
                        <i class="fas fa-plug"></i> Integrations
                    </a>
                    <a href="#advanced" class="list-group-item list-group-item-action" onclick="showSection('advanced')">
                        <i class="fas fa-wrench"></i> Advanced
                    </a>
                </div>
            </div>
        </div>

        <!-- Settings Content -->
        <div class="col-md-9">
            <!-- Security Settings -->
            <div id="security-section" class="settings-section">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-shield-alt"></i> Security Settings</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Two-Factor Authentication</h6>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="two_factor_enabled" 
                                           {% if settings.security.two_factor_enabled %}checked{% endif %}>
                                    <label class="form-check-label" for="two_factor_enabled">
                                        Enable Two-Factor Authentication
                                    </label>
                                </div>
                                <small class="text-muted">Add an extra layer of security to your account</small>
                                
                                <div class="mt-3">
                                    <button class="btn btn-outline-primary btn-sm" onclick="setup2FA()">
                                        <i class="fas fa-mobile-alt"></i> Setup 2FA
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6>Login Alerts</h6>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="login_alerts" 
                                           {% if settings.security.login_alerts %}checked{% endif %}>
                                    <label class="form-check-label" for="login_alerts">
                                        Email me about new logins
                                    </label>
                                </div>
                                <small class="text-muted">Get notified when your account is accessed</small>
                            </div>
                        </div>
                        
                        <hr>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Session Timeout</h6>
                                <select class="form-control" id="session_timeout">
                                    <option value="15" {% if settings.security.session_timeout == '15 minutes' %}selected{% endif %}>15 minutes</option>
                                    <option value="30" {% if settings.security.session_timeout == '30 minutes' %}selected{% endif %}>30 minutes</option>
                                    <option value="60">1 hour</option>
                                    <option value="240">4 hours</option>
                                    <option value="480">8 hours</option>
                                </select>
                                <small class="text-muted">Automatically log out after inactivity</small>
                            </div>
                            <div class="col-md-6">
                                <h6>Password Security</h6>
                                <button class="btn btn-outline-warning" onclick="changePassword()">
                                    <i class="fas fa-key"></i> Change Password
                                </button>
                                <br><small class="text-muted">Last changed: 30 days ago</small>
                            </div>
                        </div>
                        
                        <div class="mt-3">
                            <button class="btn btn-primary" onclick="saveSettings('security')">
                                <i class="fas fa-save"></i> Save Security Settings
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Notification Settings -->
            <div id="notifications-section" class="settings-section" style="display: none;">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-bell"></i> Notification Preferences</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Email Notifications</h6>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="email_shipment_updates" 
                                           {% if settings.notifications.email_shipment_updates %}checked{% endif %}>
                                    <label class="form-check-label" for="email_shipment_updates">
                                        Shipment Updates
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="email_payment_confirmations" 
                                           {% if settings.notifications.email_payment_confirmations %}checked{% endif %}>
                                    <label class="form-check-label" for="email_payment_confirmations">
                                        Payment Confirmations
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="email_marketing" 
                                           {% if settings.notifications.email_marketing %}checked{% endif %}>
                                    <label class="form-check-label" for="email_marketing">
                                        Marketing & Promotions
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6>Mobile Notifications</h6>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="sms_alerts" 
                                           {% if settings.notifications.sms_alerts %}checked{% endif %}>
                                    <label class="form-check-label" for="sms_alerts">
                                        SMS Alerts
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="push_notifications" 
                                           {% if settings.notifications.push_notifications %}checked{% endif %}>
                                    <label class="form-check-label" for="push_notifications">
                                        Push Notifications
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <hr>
                        
                        <h6>Notification Frequency</h6>
                        <div class="form-group">
                            <label for="digest_frequency">Email Digest Frequency</label>
                            <select class="form-control" id="digest_frequency">
                                <option value="realtime">Real-time</option>
                                <option value="daily" selected>Daily Summary</option>
                                <option value="weekly">Weekly Summary</option>
                                <option value="never">Never</option>
                            </select>
                        </div>
                        
                        <div class="mt-3">
                            <button class="btn btn-primary" onclick="saveSettings('notifications')">
                                <i class="fas fa-save"></i> Save Notification Settings
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Privacy Settings -->
            <div id="privacy-section" class="settings-section" style="display: none;">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-user-secret"></i> Privacy Settings</h5>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <label for="profile_visibility">Profile Visibility</label>
                            <select class="form-control" id="profile_visibility">
                                <option value="public">Public</option>
                                <option value="business" {% if settings.privacy.profile_visibility == 'Business Partners Only' %}selected{% endif %}>Business Partners Only</option>
                                <option value="private">Private</option>
                            </select>
                            <small class="text-muted">Control who can see your profile information</small>
                        </div>
                        
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="data_sharing" 
                                   {% if settings.privacy.data_sharing %}checked{% endif %}>
                            <label class="form-check-label" for="data_sharing">
                                Allow data sharing with trusted partners
                            </label>
                        </div>
                        
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="analytics_tracking" 
                                   {% if settings.privacy.analytics_tracking %}checked{% endif %}>
                            <label class="form-check-label" for="analytics_tracking">
                                Enable analytics tracking for better experience
                            </label>
                        </div>
                        
                        <hr>
                        
                        <h6>Data Export & Deletion</h6>
                        <p class="text-muted">Manage your personal data according to privacy regulations</p>
                        <div class="btn-group" role="group">
                            <button class="btn btn-outline-info" onclick="exportData()">
                                <i class="fas fa-download"></i> Export My Data
                            </button>
                            <button class="btn btn-outline-danger" onclick="requestDeletion()">
                                <i class="fas fa-trash"></i> Request Account Deletion
                            </button>
                        </div>
                        
                        <div class="mt-3">
                            <button class="btn btn-primary" onclick="saveSettings('privacy')">
                                <i class="fas fa-save"></i> Save Privacy Settings
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Billing Settings -->
            <div id="billing-section" class="settings-section" style="display: none;">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-credit-card"></i> Billing & Payment Settings</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Default Payment Method</h6>
                                <div class="payment-method-card">
                                    <i class="fab fa-cc-stripe"></i>
                                    <span>{{ settings.billing.default_payment_method }}</span>
                                    <button class="btn btn-sm btn-outline-primary" onclick="changePaymentMethod()">
                                        Change
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6>Auto-Pay</h6>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="auto_pay" 
                                           {% if settings.billing.auto_pay %}checked{% endif %}>
                                    <label class="form-check-label" for="auto_pay">
                                        Enable automatic payments
                                    </label>
                                </div>
                                <small class="text-muted">Automatically pay invoices when due</small>
                            </div>
                        </div>
                        
                        <hr>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="billing_email">Billing Email</label>
                                    <input type="email" class="form-control" id="billing_email" 
                                           value="{{ settings.billing.billing_email }}">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="invoice_frequency">Invoice Frequency</label>
                                    <select class="form-control" id="invoice_frequency">
                                        <option value="monthly" {% if settings.billing.invoice_frequency == 'Monthly' %}selected{% endif %}>Monthly</option>
                                        <option value="quarterly">Quarterly</option>
                                        <option value="annually">Annually</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="billing-history">
                            <h6>Recent Invoices</h6>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Invoice #</th>
                                            <th>Date</th>
                                            <th>Amount</th>
                                            <th>Status</th>
                                            <th>Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>INV-2025-001</td>
                                            <td>Jan 15, 2025</td>
                                            <td>$1,250.00</td>
                                            <td><span class="badge badge-success">Paid</span></td>
                                            <td><a href="javascript:void(0)" class="btn btn-sm btn-outline-primary">Download</a></td>
                                        </tr>
                                        <tr>
                                            <td>INV-2024-012</td>
                                            <td>Dec 15, 2024</td>
                                            <td>$875.00</td>
                                            <td><span class="badge badge-success">Paid</span></td>
                                            <td><a href="javascript:void(0)" class="btn btn-sm btn-outline-primary">Download</a></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        
                        <div class="mt-3">
                            <button class="btn btn-primary" onclick="saveSettings('billing')">
                                <i class="fas fa-save"></i> Save Billing Settings
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Integrations Settings -->
            <div id="integrations-section" class="settings-section" style="display: none;">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-plug"></i> Third-Party Integrations</h5>
                    </div>
                    <div class="card-body">
                        <div class="integration-item">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6><i class="fab fa-slack"></i> Slack</h6>
                                    <small class="text-muted">Get notifications in your Slack workspace</small>
                                </div>
                                <button class="btn btn-outline-primary" onclick="connectIntegration('slack')">
                                    Connect
                                </button>
                            </div>
                        </div>
                        
                        <div class="integration-item">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6><i class="fab fa-microsoft"></i> Microsoft Teams</h6>
                                    <small class="text-muted">Integrate with Microsoft Teams</small>
                                </div>
                                <button class="btn btn-outline-primary" onclick="connectIntegration('teams')">
                                    Connect
                                </button>
                            </div>
                        </div>
                        
                        <div class="integration-item">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6><i class="fas fa-webhook"></i> Webhooks</h6>
                                    <small class="text-muted">Send real-time data to your systems</small>
                                </div>
                                <button class="btn btn-outline-primary" onclick="manageWebhooks()">
                                    Manage
                                </button>
                            </div>
                        </div>
                        
                        <div class="integration-item">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6><i class="fas fa-code"></i> API Access</h6>
                                    <small class="text-muted">Generate API keys for custom integrations</small>
                                </div>
                                <button class="btn btn-outline-info" onclick="manageAPI()">
                                    Manage API Keys
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Advanced Settings -->
            <div id="advanced-section" class="settings-section" style="display: none;">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-wrench"></i> Advanced Settings</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            <strong>Warning:</strong> These settings are for advanced users only. Changes may affect system functionality.
                        </div>
                        
                        <div class="form-group">
                            <label for="api_rate_limit">API Rate Limit (requests per minute)</label>
                            <input type="number" class="form-control" id="api_rate_limit" value="100" min="10" max="1000">
                        </div>
                        
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="debug_mode">
                            <label class="form-check-label" for="debug_mode">
                                Enable debug mode
                            </label>
                        </div>
                        
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="beta_features">
                            <label class="form-check-label" for="beta_features">
                                Enable beta features
                            </label>
                        </div>
                        
                        <hr>
                        
                        <h6>Danger Zone</h6>
                        <div class="danger-zone">
                            <p class="text-danger">These actions cannot be undone.</p>
                            <button class="btn btn-outline-danger" onclick="resetSettings()">
                                <i class="fas fa-undo"></i> Reset All Settings
                            </button>
                        </div>
                        
                        <div class="mt-3">
                            <button class="btn btn-primary" onclick="saveSettings('advanced')">
                                <i class="fas fa-save"></i> Save Advanced Settings
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.settings-section {
    margin-bottom: 2rem;
}

.payment-method-card {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    background-color: #f8f9fa;
}

.integration-item {
    padding: 15px 0;
    border-bottom: 1px solid #eee;
}

.integration-item:last-child {
    border-bottom: none;
}

.danger-zone {
    padding: 15px;
    border: 1px solid #dc3545;
    border-radius: 0.375rem;
    background-color: #f8d7da;
}

.list-group-item {
    border: none;
    padding: 12px 15px;
}

.list-group-item.active {
    background-color: #007bff;
    border-color: #007bff;
}

.form-check {
    margin-bottom: 10px;
}
</style>

<script>
function showSection(sectionName) {
    // Hide all sections
    const sections = document.querySelectorAll('.settings-section');
    sections.forEach(section => section.style.display = 'none');
    
    // Show selected section
    document.getElementById(sectionName + '-section').style.display = 'block';
    
    // Update navigation
    const navItems = document.querySelectorAll('.list-group-item');
    navItems.forEach(item => item.classList.remove('active'));
    event.target.classList.add('active');
}

function saveSettings(category) {
    const formData = new FormData();
    formData.append('setting_type', category);
    
    // Collect all form data for the category
    const section = document.getElementById(category + '-section');
    const inputs = section.querySelectorAll('input, select, textarea');
    
    inputs.forEach(input => {
        if (input.type === 'checkbox') {
            formData.append(input.id, input.checked);
        } else {
            formData.append(input.id, input.value);
        }
    });
    
    fetch('/settings/update', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            showAlert(`${category.charAt(0).toUpperCase() + category.slice(1)} settings saved successfully!`, 'success');
        } else {
            showAlert('Error saving settings', 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('Error saving settings', 'danger');
    });
}

function setup2FA() {
    fetch('/settings/setup-2fa', {
        method: 'POST',
        headers: {'Content-Type': 'application/json'}
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            showAlert('2FA setup initiated. ' + data.message, 'success');
        } else {
            showAlert('Error setting up 2FA', 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('Error setting up 2FA', 'danger');
    });
}

function changePassword() {
    const newPassword = prompt('Enter new password:');
    if (newPassword && newPassword.length >= 8) {
        fetch('/profile/change-password', {
            method: 'POST',
            headers: {'Content-Type': 'application/x-www-form-urlencoded'},
            body: new URLSearchParams({
                'new_password': newPassword,
                'confirm_password': newPassword
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                showAlert('Password changed successfully', 'success');
            } else {
                showAlert('Error changing password', 'danger');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('Error changing password', 'danger');
        });
    } else {
        showAlert('Password must be at least 8 characters long', 'warning');
    }
}

function changePaymentMethod() {
    showAlert('Payment method update functionality coming soon', 'info');
}

function connectIntegration(service) {
    fetch('/settings/connect-integration', {
        method: 'POST',
        headers: {'Content-Type': 'application/x-www-form-urlencoded'},
        body: new URLSearchParams({'service': service})
    })
    .then(response => response.json())
    .then(data => {
        showAlert(`Integration with ${service} initiated`, 'info');
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert(`Error connecting to ${service}`, 'danger');
    });
}

function manageWebhooks() {
    showAlert('Webhook management panel functionality coming soon', 'info');
}

function manageAPI() {
    fetch('/settings/manage-api', {
        method: 'POST',
        headers: {'Content-Type': 'application/json'}
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            showAlert('API keys: ' + data.api_keys.map(key => key.name).join(', '), 'success');
        } else {
            showAlert('Error managing API keys', 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('Error managing API keys', 'danger');
    });
}

function exportData() {
    fetch('/settings/export-data', {
        method: 'POST',
        headers: {'Content-Type': 'application/json'}
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            showAlert(data.message + '. Download will begin shortly.', 'success');
            // Create download link
            const downloadData = "data:text/json;charset=utf-8," + encodeURIComponent(JSON.stringify(data.data));
            const downloadAnchorNode = document.createElement('a');
            downloadAnchorNode.setAttribute("href", downloadData);
            downloadAnchorNode.setAttribute("download", `user-data-${new Date().toISOString().split('T')[0]}.json`);
            document.body.appendChild(downloadAnchorNode);
            downloadAnchorNode.click();
            downloadAnchorNode.remove();
        } else {
            showAlert('Error exporting data', 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('Error exporting data', 'danger');
    });
}

function requestDeletion() {
    if (confirm('Are you sure you want to request account deletion? This action cannot be undone.')) {
        fetch('/settings/request-deletion', {
            method: 'POST',
            headers: {'Content-Type': 'application/json'}
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                showAlert(data.message, 'warning');
            } else {
                showAlert('Error submitting deletion request', 'danger');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('Error submitting deletion request', 'danger');
        });
    }
}

function resetSettings() {
    if (confirm('Are you sure you want to reset all settings to default? This action cannot be undone.')) {
        fetch('/settings/reset', {
            method: 'POST',
            headers: {'Content-Type': 'application/json'}
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                showAlert(data.message, 'warning');
                // Reload page to show reset settings
                setTimeout(() => location.reload(), 2000);
            } else {
                showAlert('Error resetting settings', 'danger');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('Error resetting settings', 'danger');
        });
    }
}

function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="close" data-dismiss="alert">
            <span>&times;</span>
        </button>
    `;
    
    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);
    
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}
</script>
{% endblock %}