<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Multi-Modal Route Optimizer - Cloverics</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="{{ url_for('static', path='css/dashboard.css') }}" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
</head>
<body>
<!-- Authentication Check -->
{% if not user or not user.is_authenticated %}
<script>
    window.location.href = '/login';
</script>
{% endif %}

    {% include 'base.html' %}

    <div class="main-content">
        <!-- Header Section -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2><i class="fas fa-route text-primary me-2"></i>Multi-Modal Route Optimizer</h2>
                <p class="text-muted mb-0">Find the optimal shipping route combining truck, rail, ship, and air transport</p>
            </div>
            <div class="d-flex gap-2">
                <button class="btn btn-outline-primary" onclick="loadSavedSearches()">
                    <i class="fas fa-bookmark me-1"></i>Saved Searches
                </button>
                <button class="btn btn-primary" onclick="showOptimizationTips()">
                    <i class="fas fa-lightbulb me-1"></i>Optimization Tips
                </button>
            </div>
        </div>

        <!-- Route Optimization Form -->
        <div class="row">
            <div class="col-lg-4">
                <div class="card shadow-sm h-100">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-cog me-2"></i>Route Parameters</h5>
                    </div>
                    <div class="card-body">
                        <form id="routeOptimizerForm">
                            <!-- Origin & Destination -->
                            <div class="mb-3">
                                <label class="form-label fw-semibold">Origin</label>
                                <select class="form-select" id="originCountry" required onchange="updateStates('origin')">
                                    <option value="">Select Country</option>
                                    {% for country in countries %}
                                        <option value="{{ country }}">{{ country }}</option>
                                    {% endfor %}
                                </select>
                                <select class="form-select mt-2 d-none" id="originState">
                                    <option value="">Select State</option>
                                </select>
                                <input type="text" class="form-control mt-2" id="originCity" placeholder="Enter city" required>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label fw-semibold">Destination</label>
                                <select class="form-select" id="destinationCountry" required onchange="updateStates('destination')">
                                    <option value="">Select Country</option>
                                    {% for country in countries %}
                                        <option value="{{ country }}">{{ country }}</option>
                                    {% endfor %}
                                </select>
                                <select class="form-select mt-2 d-none" id="destinationState">
                                    <option value="">Select State</option>
                                </select>
                                <input type="text" class="form-control mt-2" id="destinationCity" placeholder="Enter city" required>
                            </div>

                            <!-- Cargo Details -->
                            <div class="mb-3">
                                <label class="form-label fw-semibold">Cargo Type</label>
                                <select class="form-select" id="cargoType" required>
                                    <option value="">Select Cargo Type</option>
                                    <option value="general">General Cargo</option>
                                    <option value="hazardous">Hazardous Materials</option>
                                    <option value="refrigerated">Refrigerated Goods</option>
                                    <option value="fragile">Fragile Items</option>
                                    <option value="oversized">Oversized/Heavy</option>
                                    <option value="liquid">Liquid Bulk</option>
                                    <option value="containers">Containers</option>
                                </select>
                            </div>

                            <div class="row">
                                <div class="col-6">
                                    <label class="form-label fw-semibold">Weight (kg)</label>
                                    <input type="number" class="form-control" id="weight" placeholder="1000" required min="1">
                                </div>
                                <div class="col-6">
                                    <label class="form-label fw-semibold">Value ($)</label>
                                    <input type="number" class="form-control" id="cargoValue" placeholder="10000" min="0">
                                </div>
                            </div>

                            <!-- Optimization Preferences -->
                            <div class="mb-3 mt-3">
                                <label class="form-label fw-semibold">Optimization Priority</label>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="priority" id="costPriority" value="cost" checked>
                                    <label class="form-check-label" for="costPriority">
                                        <i class="fas fa-dollar-sign text-success me-1"></i>Lowest Cost
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="priority" id="timePriority" value="time">
                                    <label class="form-check-label" for="timePriority">
                                        <i class="fas fa-clock text-warning me-1"></i>Fastest Delivery
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="priority" id="balancedPriority" value="balanced">
                                    <label class="form-check-label" for="balancedPriority">
                                        <i class="fas fa-balance-scale text-info me-1"></i>Balanced
                                    </label>
                                </div>
                            </div>

                            <!-- Transport Mode Selection -->
                            <div class="mb-3">
                                <label class="form-label fw-semibold">Available Transport Modes</label>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="allowTruck" checked>
                                    <label class="form-check-label" for="allowTruck">
                                        <i class="fas fa-truck text-primary me-1"></i>Truck
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="allowRail" checked>
                                    <label class="form-check-label" for="allowRail">
                                        <i class="fas fa-train text-success me-1"></i>Rail
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="allowShip" checked>
                                    <label class="form-check-label" for="allowShip">
                                        <i class="fas fa-ship text-info me-1"></i>Ship
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="allowAir">
                                    <label class="form-check-label" for="allowAir">
                                        <i class="fas fa-plane text-warning me-1"></i>Air
                                    </label>
                                </div>
                            </div>

                            <button type="submit" class="btn btn-primary w-100" id="optimizeBtn">
                                <i class="fas fa-search me-2"></i>Find Optimal Routes
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Results Section -->
            <div class="col-lg-8">
                <div class="card shadow-sm h-100">
                    <div class="card-header bg-light">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>Optimization Results</h5>
                            <div id="resultsStats" class="text-muted small"></div>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <!-- Results will be loaded here -->
                        <div id="noResults" class="text-center py-5">
                            <i class="fas fa-route fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">Enter route parameters and click "Find Optimal Routes"</h5>
                            <p class="text-muted mb-0">Our AI will analyze multi-modal combinations for the best shipping solution</p>
                        </div>
                        
                        <div id="optimizationResults" class="d-none">
                            <!-- Route Cards will be inserted here -->
                            <div id="routeCards" class="p-3"></div>
                            
                            <!-- Comparison Chart -->
                            <div class="border-top p-3">
                                <h6 class="fw-semibold mb-3">Route Comparison</h6>
                                <canvas id="routeComparisonChart" height="100"></canvas>
                            </div>
                        </div>
                        
                        <div id="loadingResults" class="text-center py-5 d-none">
                            <div class="spinner-border text-primary mb-3" role="status"></div>
                            <h5 class="text-muted">Optimizing Routes...</h5>
                            <p class="text-muted mb-0">Analyzing multi-modal combinations</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // US States data
        const US_STATES = [
            'Alabama', 'Alaska', 'Arizona', 'Arkansas', 'California', 'Colorado', 'Connecticut',
            'Delaware', 'Florida', 'Georgia', 'Hawaii', 'Idaho', 'Illinois', 'Indiana', 'Iowa',
            'Kansas', 'Kentucky', 'Louisiana', 'Maine', 'Maryland', 'Massachusetts', 'Michigan',
            'Minnesota', 'Mississippi', 'Missouri', 'Montana', 'Nebraska', 'Nevada', 'New Hampshire',
            'New Jersey', 'New Mexico', 'New York', 'North Carolina', 'North Dakota', 'Ohio',
            'Oklahoma', 'Oregon', 'Pennsylvania', 'Rhode Island', 'South Carolina', 'South Dakota',
            'Tennessee', 'Texas', 'Utah', 'Vermont', 'Virginia', 'Washington', 'West Virginia',
            'Wisconsin', 'Wyoming'
        ];

        let routeComparisonChart = null;

        // Update states dropdown when country changes
        function updateStates(type) {
            const countrySelect = document.getElementById(`${type}Country`);
            const stateSelect = document.getElementById(`${type}State`);
            
            if (countrySelect.value === 'United States') {
                stateSelect.classList.remove('d-none');
                stateSelect.innerHTML = '<option value="">Select State</option>';
                US_STATES.forEach(state => {
                    stateSelect.innerHTML += `<option value="${state}">${state}</option>`;
                });
            } else {
                stateSelect.classList.add('d-none');
                stateSelect.value = '';
            }
        }

        // Form submission
        document.getElementById('routeOptimizerForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            await findOptimalRoutes();
        });

        async function findOptimalRoutes() {
            const form = document.getElementById('routeOptimizerForm');
            const formData = new FormData(form);
            
            // Show loading
            document.getElementById('noResults').classList.add('d-none');
            document.getElementById('optimizationResults').classList.add('d-none');
            document.getElementById('loadingResults').classList.remove('d-none');
            
            try {
                const response = await fetch('/api/multi-modal/optimize', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        origin_country: document.getElementById('originCountry').value,
                        origin_state: document.getElementById('originState').value,
                        origin_city: document.getElementById('originCity').value,
                        destination_country: document.getElementById('destinationCountry').value,
                        destination_state: document.getElementById('destinationState').value,
                        destination_city: document.getElementById('destinationCity').value,
                        cargo_type: document.getElementById('cargoType').value,
                        weight: parseFloat(document.getElementById('weight').value),
                        cargo_value: parseFloat(document.getElementById('cargoValue').value) || 0,
                        priority: document.querySelector('input[name="priority"]:checked').value,
                        transport_modes: {
                            truck: document.getElementById('allowTruck').checked,
                            rail: document.getElementById('allowRail').checked,
                            ship: document.getElementById('allowShip').checked,
                            air: document.getElementById('allowAir').checked
                        }
                    })
                });

                const data = await response.json();
                
                if (data.success) {
                    displayOptimizationResults(data.routes);
                } else {
                    throw new Error(data.message || 'Optimization failed');
                }
                
            } catch (error) {
                console.error('Error:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'Optimization Failed',
                    text: error.message || 'Unable to optimize routes. Please try again.',
                    confirmButtonColor: '#0d6efd'
                });
                
                // Show no results
                document.getElementById('loadingResults').classList.add('d-none');
                document.getElementById('noResults').classList.remove('d-none');
            }
        }

        function displayOptimizationResults(routes) {
            // Hide loading, show results
            document.getElementById('loadingResults').classList.add('d-none');
            document.getElementById('optimizationResults').classList.remove('d-none');
            
            // Update stats
            document.getElementById('resultsStats').textContent = `${routes.length} optimized routes found`;
            
            // Display route cards
            const routeCards = document.getElementById('routeCards');
            routeCards.innerHTML = '';
            
            routes.forEach((route, index) => {
                const card = createRouteCard(route, index);
                routeCards.appendChild(card);
            });
            
            // Create comparison chart
            createComparisonChart(routes);
        }

        function createRouteCard(route, index) {
            const card = document.createElement('div');
            card.className = 'card mb-3 border';
            
            const modeIcons = {
                'truck': 'fas fa-truck text-primary',
                'rail': 'fas fa-train text-success',
                'ship': 'fas fa-ship text-info',
                'air': 'fas fa-plane text-warning'
            };
            
            const transportModes = route.transport_sequence.map(mode => 
                `<span class="badge bg-light text-dark me-1"><i class="${modeIcons[mode] || 'fas fa-question'}"></i> ${mode.charAt(0).toUpperCase() + mode.slice(1)}</span>`
            ).join('');
            
            card.innerHTML = `
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start mb-3">
                        <div>
                            <h6 class="fw-semibold mb-1">${route.route_name}</h6>
                            <div class="mb-2">${transportModes}</div>
                            <small class="text-muted">${route.description}</small>
                        </div>
                        <div class="text-end">
                            <div class="h5 text-primary mb-0">$${route.total_cost.toLocaleString()}</div>
                            <small class="text-muted">${route.estimated_days} days</small>
                        </div>
                    </div>
                    
                    <div class="row text-center">
                        <div class="col">
                            <div class="small text-muted">Distance</div>
                            <div class="fw-semibold">${route.total_distance.toLocaleString()} km</div>
                        </div>
                        <div class="col">
                            <div class="small text-muted">Reliability</div>
                            <div class="fw-semibold">${Math.round(route.reliability_score * 100)}%</div>
                        </div>
                        <div class="col">
                            <div class="small text-muted">CO₂ Emissions</div>
                            <div class="fw-semibold">${route.carbon_footprint.toFixed(1)} kg</div>
                        </div>
                        <div class="col">
                            <div class="small text-muted">Score</div>
                            <div class="fw-semibold text-success">${Math.round(route.optimization_score * 100)}/100</div>
                        </div>
                    </div>
                    
                    <div class="mt-3 d-flex gap-2">
                        <button class="btn btn-primary btn-sm" onclick="requestQuote('${route.route_id}')">
                            <i class="fas fa-paper-plane me-1"></i>Request Quote
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" onclick="viewRouteDetails('${route.route_id}')">
                            <i class="fas fa-eye me-1"></i>View Details
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" onclick="saveRoute('${route.route_id}')">
                            <i class="fas fa-bookmark me-1"></i>Save Route
                        </button>
                    </div>
                </div>
            `;
            
            return card;
        }

        function createComparisonChart(routes) {
            const ctx = document.getElementById('routeComparisonChart').getContext('2d');
            
            // Destroy existing chart
            if (routeComparisonChart) {
                routeComparisonChart.destroy();
            }
            
            const labels = routes.map(route => route.route_name);
            const costs = routes.map(route => route.total_cost);
            const days = routes.map(route => route.estimated_days);
            const scores = routes.map(route => Math.round(route.optimization_score * 100));
            
            routeComparisonChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [
                        {
                            label: 'Cost ($)',
                            data: costs,
                            backgroundColor: 'rgba(13, 110, 253, 0.7)',
                            borderColor: 'rgba(13, 110, 253, 1)',
                            borderWidth: 1,
                            yAxisID: 'y'
                        },
                        {
                            label: 'Days',
                            data: days,
                            backgroundColor: 'rgba(25, 135, 84, 0.7)',
                            borderColor: 'rgba(25, 135, 84, 1)',
                            borderWidth: 1,
                            yAxisID: 'y1'
                        },
                        {
                            label: 'Score',
                            data: scores,
                            backgroundColor: 'rgba(255, 193, 7, 0.7)',
                            borderColor: 'rgba(255, 193, 7, 1)',
                            borderWidth: 1,
                            yAxisID: 'y2'
                        }
                    ]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            grid: {
                                drawOnChartArea: false,
                            },
                        },
                        y2: {
                            type: 'linear',
                            display: false
                        }
                    }
                }
            });
        }

        // Action functions
        async function requestQuote(routeId) {
            Swal.fire({
                title: 'Request Quote',
                text: 'Redirecting to quote request form...',
                icon: 'info',
                timer: 1500,
                showConfirmButton: false
            });
            
            // Redirect to quote request with route ID
            setTimeout(() => {
                window.location.href = `/customer/request-quote?route_id=${routeId}`;
            }, 1500);
        }

        async function viewRouteDetails(routeId) {
            Swal.fire({
                title: 'Route Details',
                html: '<div class="text-center"><div class="spinner-border text-primary"></div><p class="mt-2">Loading route details...</p></div>',
                showConfirmButton: false,
                allowOutsideClick: false
            });
            
            // Simulate loading route details
            setTimeout(() => {
                Swal.fire({
                    title: 'Route Details',
                    html: `
                        <div class="text-start">
                            <h6>Route ID: ${routeId}</h6>
                            <p><strong>Transport Modes:</strong> Truck → Rail → Ship</p>
                            <p><strong>Estimated Transit:</strong> 12-15 days</p>
                            <p><strong>Key Stops:</strong> Origin Hub → Rail Terminal → Port → Destination</p>
                            <p><strong>Carrier Network:</strong> Regional carriers + major shipping lines</p>
                            <p><strong>Insurance:</strong> Full coverage available</p>
                            <p><strong>Tracking:</strong> Real-time GPS + milestone updates</p>
                        </div>
                    `,
                    confirmButtonText: 'Close',
                    confirmButtonColor: '#0d6efd'
                });
            }, 1500);
        }

        async function saveRoute(routeId) {
            try {
                const response = await fetch('/api/saved-searches/create', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        route_id: routeId,
                        search_type: 'multi_modal_optimization'
                    })
                });

                const data = await response.json();
                
                if (data.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Route Saved',
                        text: 'Route saved to your saved searches.',
                        timer: 2000,
                        showConfirmButton: false
                    });
                } else {
                    throw new Error(data.message);
                }
                
            } catch (error) {
                Swal.fire({
                    icon: 'error',
                    title: 'Save Failed',
                    text: error.message || 'Unable to save route. Please try again.',
                    confirmButtonColor: '#0d6efd'
                });
            }
        }

        function loadSavedSearches() {
            window.location.href = '/customer/saved-searches';
        }

        function showOptimizationTips() {
            Swal.fire({
                title: 'Optimization Tips',
                html: `
                    <div class="text-start">
                        <h6><i class="fas fa-lightbulb text-warning me-2"></i>Cost Optimization</h6>
                        <ul class="small">
                            <li>Choose rail/ship for heavy cargo over long distances</li>
                            <li>Allow longer transit times for better rates</li>
                            <li>Consider consolidation for smaller shipments</li>
                        </ul>
                        
                        <h6><i class="fas fa-clock text-info me-2"></i>Time Optimization</h6>
                        <ul class="small">
                            <li>Air freight for urgent deliveries under 1000kg</li>
                            <li>Direct truck routes for regional shipments</li>
                            <li>Express rail services for medium distances</li>
                        </ul>
                        
                        <h6><i class="fas fa-leaf text-success me-2"></i>Environmental Impact</h6>
                        <ul class="small">
                            <li>Rail and ship transport have lowest carbon footprint</li>
                            <li>Avoid air freight unless absolutely necessary</li>
                            <li>Choose carriers with sustainability programs</li>
                        </ul>
                    </div>
                `,
                confirmButtonText: 'Got it!',
                confirmButtonColor: '#0d6efd',
                width: '600px'
            });
        }
    </script>
</body>
</html>