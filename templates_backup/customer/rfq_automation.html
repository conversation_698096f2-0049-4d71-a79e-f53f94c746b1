{% extends "base.html" %}

{% block title %}RFQ Automation - Cloverics{% endblock %}

{% block extra_css %}
<style>
    .rfq-card {
        background: white;
        border-radius: 12px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
        padding: 20px;
    }
    .scenario-type-card {
        border: 2px solid #e0e6ed;
        border-radius: 10px;
        padding: 15px;
        margin: 10px;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    .scenario-type-card:hover {
        border-color: #007bff;
        box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);
    }
    .scenario-type-card.selected {
        border-color: #007bff;
        background-color: #f8f9ff;
    }
    .criteria-weight {
        margin-bottom: 10px;
    }
    .weight-slider {
        width: 100%;
        margin: 5px 0;
    }
    .evaluation-result {
        border-left: 4px solid #28a745;
        padding: 15px;
        margin: 10px 0;
        background: #f8f9fa;
    }
    .negotiation-stage {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 8px;
        padding: 15px;
        margin: 10px 0;
    }
    .progress-bar {
        height: 8px;
        background: #e9ecef;
        border-radius: 4px;
        overflow: hidden;
        margin: 10px 0;
    }
    .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, #007bff, #28a745);
        transition: width 0.5s ease;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="page-header">
                <h2><i class="fas fa-cogs me-2"></i>RFQ Automation</h2>
                <p class="text-muted">Advanced Request for Quote management with AI-powered evaluation and negotiation workflows</p>
            </div>

            <!-- Navigation Tabs -->
            <ul class="nav nav-tabs" id="rfqTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="dashboard-tab" data-bs-toggle="tab" data-bs-target="#dashboard" type="button" role="tab">
                        <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="create-scenario-tab" data-bs-toggle="tab" data-bs-target="#create-scenario" type="button" role="tab">
                        <i class="fas fa-plus-circle me-2"></i>Create RFQ
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="responses-tab" data-bs-toggle="tab" data-bs-target="#responses" type="button" role="tab">
                        <i class="fas fa-reply-all me-2"></i>Responses
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="evaluations-tab" data-bs-toggle="tab" data-bs-target="#evaluations" type="button" role="tab">
                        <i class="fas fa-chart-line me-2"></i>Evaluations
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="negotiations-tab" data-bs-toggle="tab" data-bs-target="#negotiations" type="button" role="tab">
                        <i class="fas fa-handshake me-2"></i>Negotiations
                    </button>
                </li>
            </ul>

            <!-- Tab Content -->
            <div class="tab-content" id="rfqTabContent">
                <!-- Dashboard Tab -->
                <div class="tab-pane fade show active" id="dashboard" role="tabpanel">
                    <div class="rfq-card">
                        <h4><i class="fas fa-chart-bar me-2"></i>RFQ Performance Overview</h4>
                        <div class="row" id="dashboardCards">
                            <div class="col-md-3">
                                <div class="card bg-primary text-white">
                                    <div class="card-body">
                                        <h5 class="card-title">Total RFQs</h5>
                                        <h2 id="totalRfqs">0</h2>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-success text-white">
                                    <div class="card-body">
                                        <h5 class="card-title">Active Negotiations</h5>
                                        <h2 id="activeNegotiations">0</h2>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-info text-white">
                                    <div class="card-body">
                                        <h5 class="card-title">Avg Responses</h5>
                                        <h2 id="avgResponses">0</h2>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-warning text-white">
                                    <div class="card-body">
                                        <h5 class="card-title">Completion Rate</h5>
                                        <h2 id="completionRate">0%</h2>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-4">
                            <div class="col-md-12">
                                <h5>Recent RFQ Activity</h5>
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>RFQ ID</th>
                                                <th>Scenario Name</th>
                                                <th>Status</th>
                                                <th>Responses</th>
                                                <th>Created</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody id="recentRfqActivity">
                                            <tr>
                                                <td colspan="6" class="text-center">Loading recent activity...</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Create Scenario Tab -->
                <div class="tab-pane fade" id="create-scenario" role="tabpanel">
                    <div class="rfq-card">
                        <h4><i class="fas fa-plus-circle me-2"></i>Create New RFQ Scenario</h4>
                        
                        <form id="createRfqForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="scenarioName" class="form-label">Scenario Name</label>
                                        <input type="text" class="form-control" id="scenarioName" name="scenario_name" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="deadline" class="form-label">Response Deadline</label>
                                        <input type="datetime-local" class="form-control" id="deadline" name="deadline" required>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Description</label>
                                <textarea class="form-control" id="description" name="description" rows="3" placeholder="Describe your RFQ requirements..."></textarea>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Scenario Type</label>
                                <div class="row" id="scenarioTypes">
                                    <!-- Scenario type cards will be populated here -->
                                </div>
                                <input type="hidden" id="selectedScenarioType" name="scenario_type" value="">
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="budgetMin" class="form-label">Minimum Budget ($)</label>
                                        <input type="number" class="form-control" id="budgetMin" name="budget_min" min="0" step="0.01" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="budgetMax" class="form-label">Maximum Budget ($)</label>
                                        <input type="number" class="form-control" id="budgetMax" name="budget_max" min="0" step="0.01" required>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Evaluation Criteria Weights</label>
                                <div id="criteriaWeights">
                                    <!-- Criteria sliders will be populated here -->
                                </div>
                            </div>

                            <div class="mb-3">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-rocket me-2"></i>Create RFQ Scenario
                                </button>
                                <button type="button" class="btn btn-secondary ms-2" onclick="generateVariations()">
                                    <i class="fas fa-random me-2"></i>Generate Variations
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Responses Tab -->
                <div class="tab-pane fade" id="responses" role="tabpanel">
                    <div class="rfq-card">
                        <h4><i class="fas fa-reply-all me-2"></i>Provider Responses</h4>
                        
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="rfqFilter" class="form-label">Filter by RFQ</label>
                                    <select class="form-select" id="rfqFilter">
                                        <option value="">Select an RFQ...</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">&nbsp;</label>
                                    <button type="button" class="btn btn-info d-block" onclick="evaluateResponses()">
                                        <i class="fas fa-calculator me-2"></i>Evaluate Responses
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div id="responsesList">
                            <div class="text-center py-4">
                                <p class="text-muted">Select an RFQ to view responses</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Evaluations Tab -->
                <div class="tab-pane fade" id="evaluations" role="tabpanel">
                    <div class="rfq-card">
                        <h4><i class="fas fa-chart-line me-2"></i>AI-Powered Evaluations</h4>
                        
                        <div id="evaluationResults">
                            <div class="text-center py-4">
                                <p class="text-muted">No evaluations available yet</p>
                                <p class="small text-muted">Create an RFQ and receive responses to see AI-powered evaluations</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Negotiations Tab -->
                <div class="tab-pane fade" id="negotiations" role="tabpanel">
                    <div class="rfq-card">
                        <h4><i class="fas fa-handshake me-2"></i>Negotiation Workflows</h4>
                        
                        <div id="negotiationWorkflows">
                            <div class="text-center py-4">
                                <p class="text-muted">No active negotiations</p>
                                <p class="small text-muted">Complete RFQ evaluations to initiate structured negotiations</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- RFQ Response Modal -->
<div class="modal fade" id="responseModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Submit RFQ Response</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="responseForm">
                    <input type="hidden" id="responseRfqId" name="rfq_id">
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="providerName" class="form-label">Provider Name</label>
                                <input type="text" class="form-control" id="providerName" name="provider_name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="totalCost" class="form-label">Total Cost ($)</label>
                                <input type="number" class="form-control" id="totalCost" name="total_cost" min="0" step="0.01" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="deliveryTime" class="form-label">Delivery Time (days)</label>
                                <input type="number" class="form-control" id="deliveryTime" name="delivery_time" min="1" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="complianceScore" class="form-label">Compliance Score (0-1)</label>
                                <input type="number" class="form-control" id="complianceScore" name="compliance_score" min="0" max="1" step="0.1" value="0.8">
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="proposalDetails" class="form-label">Proposal Details (JSON)</label>
                        <textarea class="form-control" id="proposalDetails" name="proposal_details" rows="3" placeholder='{"tracking_capability": 0.9, "insurance_coverage": 0.8}'></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="specialTerms" class="form-label">Special Terms</label>
                        <textarea class="form-control" id="specialTerms" name="special_terms" rows="2" placeholder="Any special terms or conditions..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="submitResponse()">Submit Response</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let scenarioTemplates = {};
let selectedRfqId = '';

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    loadDashboardData();
    loadScenarioTemplates();
    
    // Set default deadline to 7 days from now
    const deadline = new Date();
    deadline.setDate(deadline.getDate() + 7);
    document.getElementById('deadline').value = deadline.toISOString().slice(0, 16);
});

// Load dashboard data
async function loadDashboardData() {
    try {
        const response = await fetch('/api/rfq/dashboard');
        const result = await response.json();
        
        if (result.success) {
            updateDashboard(result.dashboard);
        }
    } catch (error) {
        console.error('Error loading dashboard:', error);
    }
}

// Update dashboard display
function updateDashboard(data) {
    document.getElementById('totalRfqs').textContent = data.total_rfqs || 0;
    document.getElementById('activeNegotiations').textContent = data.active_negotiations || 0;
    document.getElementById('avgResponses').textContent = (data.performance_metrics?.average_responses_per_rfq || 0).toFixed(1);
    document.getElementById('completionRate').textContent = Math.round((data.performance_metrics?.completion_rate || 0) * 100) + '%';
    
    // Update recent activity
    const tbody = document.getElementById('recentRfqActivity');
    if (data.recent_activity && data.recent_activity.length > 0) {
        tbody.innerHTML = data.recent_activity.map(activity => `
            <tr>
                <td>${activity.rfq_id.substring(0, 8)}...</td>
                <td>${activity.scenario_name}</td>
                <td><span class="badge bg-info">${activity.status}</span></td>
                <td>${activity.responses_count}</td>
                <td>${new Date(activity.created_at).toLocaleDateString()}</td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="viewRfqDetails('${activity.rfq_id}')">
                        <i class="fas fa-eye"></i>
                    </button>
                </td>
            </tr>
        `).join('');
    } else {
        tbody.innerHTML = '<tr><td colspan="6" class="text-center text-muted">No RFQ activity yet</td></tr>';
    }
}

// Load scenario templates
async function loadScenarioTemplates() {
    try {
        const response = await fetch('/api/rfq/scenario-templates');
        const result = await response.json();
        
        if (result.success) {
            scenarioTemplates = result.templates;
            displayScenarioTypes();
        }
    } catch (error) {
        console.error('Error loading scenario templates:', error);
    }
}

// Display scenario type cards
function displayScenarioTypes() {
    const container = document.getElementById('scenarioTypes');
    container.innerHTML = Object.entries(scenarioTemplates).map(([key, template]) => `
        <div class="col-md-6">
            <div class="scenario-type-card" onclick="selectScenarioType('${key}')">
                <h6>${template.name}</h6>
                <p class="small text-muted">${template.description}</p>
            </div>
        </div>
    `).join('');
}

// Select scenario type
function selectScenarioType(type) {
    // Remove previous selection
    document.querySelectorAll('.scenario-type-card').forEach(card => {
        card.classList.remove('selected');
    });
    
    // Select current
    event.target.closest('.scenario-type-card').classList.add('selected');
    document.getElementById('selectedScenarioType').value = type;
    
    // Update criteria weights
    updateCriteriaWeights(type);
}

// Update criteria weights sliders
function updateCriteriaWeights(type) {
    const template = scenarioTemplates[type];
    if (!template) return;
    
    const container = document.getElementById('criteriaWeights');
    container.innerHTML = Object.entries(template.default_criteria).map(([criterion, weight]) => `
        <div class="criteria-weight">
            <label class="form-label">${criterion.replace('_', ' ').toUpperCase()}: <span id="${criterion}Weight">${Math.round(weight * 100)}%</span></label>
            <input type="range" class="weight-slider" min="0" max="100" value="${weight * 100}" 
                   onchange="updateWeightDisplay('${criterion}', this.value)">
        </div>
    `).join('');
}

// Update weight display
function updateWeightDisplay(criterion, value) {
    document.getElementById(criterion + 'Weight').textContent = value + '%';
}

// Create RFQ scenario
document.getElementById('createRfqForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    
    // Collect criteria weights
    const criteriaWeights = {};
    document.querySelectorAll('.weight-slider').forEach(slider => {
        const criterion = slider.onchange.toString().match(/'([^']+)'/)[1];
        criteriaWeights[criterion] = slider.value / 100;
    });
    
    formData.append('criteria_weights', JSON.stringify(criteriaWeights));
    formData.append('requirements', JSON.stringify({}));
    formData.append('target_providers', JSON.stringify([]));
    
    try {
        const response = await fetch('/api/rfq/create-scenario', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        if (result.success) {
            Swal.fire({
                title: 'Success!',
                text: 'RFQ scenario created successfully',
                icon: 'success',
                confirmButtonText: 'OK'
            });
            
            // Switch to dashboard tab and refresh
            document.getElementById('dashboard-tab').click();
            loadDashboardData();
            
            // Reset form
            this.reset();
            document.getElementById('selectedScenarioType').value = '';
            document.querySelectorAll('.scenario-type-card').forEach(card => {
                card.classList.remove('selected');
            });
        } else {
            Swal.fire({
                title: 'Error',
                text: result.message || 'Failed to create RFQ scenario',
                icon: 'error'
            });
        }
    } catch (error) {
        console.error('Error creating RFQ:', error);
        Swal.fire({
            title: 'Error',
            text: 'An error occurred while creating the RFQ scenario',
            icon: 'error'
        });
    }
});

// Generate scenario variations
function generateVariations() {
    Swal.fire({
        title: 'Generate Variations',
        text: 'This will create cost-optimized, speed-optimized, and quality-optimized variations of your scenario.',
        icon: 'info',
        showCancelButton: true,
        confirmButtonText: 'Generate',
        cancelButtonText: 'Cancel'
    }).then((result) => {
        if (result.isConfirmed) {
            // Implementation for generating variations
            Swal.fire({
                title: 'Feature Coming Soon',
                text: 'Scenario variations will be available in the next update',
                icon: 'info'
            });
        }
    });
}

// Evaluate responses
function evaluateResponses() {
    const rfqId = document.getElementById('rfqFilter').value;
    if (!rfqId) {
        Swal.fire({
            title: 'No RFQ Selected',
            text: 'Please select an RFQ to evaluate responses',
            icon: 'warning'
        });
        return;
    }
    
    Swal.fire({
        title: 'Evaluating Responses',
        text: 'AI is analyzing all responses...',
        allowOutsideClick: false,
        didOpen: () => {
            Swal.showLoading();
        }
    });
    
    // Simulate evaluation (replace with actual API call)
    setTimeout(() => {
        Swal.fire({
            title: 'Evaluation Complete',
            text: 'All responses have been evaluated using AI scoring',
            icon: 'success'
        });
        
        // Switch to evaluations tab
        document.getElementById('evaluations-tab').click();
    }, 3000);
}

// Submit response
function submitResponse() {
    const form = document.getElementById('responseForm');
    const formData = new FormData(form);
    
    // Implementation for submitting response
    Swal.fire({
        title: 'Response Submitted',
        text: 'Your RFQ response has been submitted successfully',
        icon: 'success'
    });
    
    // Close modal
    const modal = bootstrap.Modal.getInstance(document.getElementById('responseModal'));
    modal.hide();
}

// View RFQ details
function viewRfqDetails(rfqId) {
    selectedRfqId = rfqId;
    
    Swal.fire({
        title: 'RFQ Details',
        html: `
            <div class="text-start">
                <p><strong>RFQ ID:</strong> ${rfqId}</p>
                <p><strong>Status:</strong> <span class="badge bg-info">PUBLISHED</span></p>
                <p><strong>Responses:</strong> 3 received</p>
                <p><strong>Deadline:</strong> ${new Date(Date.now() + 86400000 * 3).toLocaleDateString()}</p>
            </div>
        `,
        showCancelButton: true,
        confirmButtonText: 'View Responses',
        cancelButtonText: 'Close'
    }).then((result) => {
        if (result.isConfirmed) {
            document.getElementById('responses-tab').click();
            document.getElementById('rfqFilter').value = rfqId;
        }
    });
}

// Initialize SweetAlert2 if not already loaded
if (typeof Swal === 'undefined') {
    const script = document.createElement('script');
    script.src = 'https://cdn.jsdelivr.net/npm/sweetalert2@11';
    document.head.appendChild(script);
}
</script>
{% endblock %}