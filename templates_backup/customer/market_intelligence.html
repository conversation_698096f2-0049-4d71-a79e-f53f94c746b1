{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-lg-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="mb-1">📊 Market Intelligence Hub</h2>
                    <p class="text-muted mb-0">Advanced market analytics, rate benchmarking, and provider performance insights</p>
                </div>
                <div class="text-end">
                    <span class="badge bg-warning">Phase 2 Feature</span>
                    <span class="badge bg-info">Freightos Competitor</span>
                </div>
            </div>

            <!-- Market Overview Cards -->
            <div class="row mb-4" id="marketOverview">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-1" id="totalRoutes">-</h4>
                                    <p class="mb-0">Active Routes</p>
                                </div>
                                <i class="fas fa-route fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-1" id="activeProviders">-</h4>
                                    <p class="mb-0">Active Providers</p>
                                </div>
                                <i class="fas fa-users fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-1" id="avgRate">-</h4>
                                    <p class="mb-0">Avg Rate/kg</p>
                                </div>
                                <i class="fas fa-dollar-sign fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-1" id="marketGrowth">-</h4>
                                    <p class="mb-0">Market Growth</p>
                                </div>
                                <i class="fas fa-chart-line fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filter Controls -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="fas fa-filter"></i> Market Analysis Filters</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <label class="form-label">Transport Mode</label>
                            <select class="form-select" id="transportFilter">
                                <option value="">All Modes</option>
                                <option value="truck">🚛 Truck</option>
                                <option value="ship">🚢 Ship</option>
                                <option value="air">✈️ Air</option>
                                <option value="rail">🚆 Rail</option>
                            </select>
                        </div>
                        
                        <div class="col-md-3">
                            <label class="form-label">Origin Country</label>
                            <select class="form-select" id="originFilter">
                                <option value="">All Origins</option>
                                {% for country in countries %}
                                    <option value="{{ country }}">{{ country }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        
                        <div class="col-md-3">
                            <label class="form-label">Destination Country</label>
                            <select class="form-select" id="destinationFilter">
                                <option value="">All Destinations</option>
                                {% for country in countries %}
                                    <option value="{{ country }}">{{ country }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        
                        <div class="col-md-3 d-flex align-items-end">
                            <button class="btn btn-primary w-100" onclick="updateMarketAnalysis()">
                                <i class="fas fa-search"></i> Analyze Market
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tabbed Analytics Interface -->
            <div class="card shadow-sm">
                <div class="card-header">
                    <ul class="nav nav-tabs card-header-tabs" id="analyticsTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="overview-tab" data-bs-toggle="tab" data-bs-target="#overview" type="button" role="tab">
                                <i class="fas fa-chart-pie"></i> Market Overview
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="benchmarks-tab" data-bs-toggle="tab" data-bs-target="#benchmarks" type="button" role="tab">
                                <i class="fas fa-balance-scale"></i> Price Benchmarks
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="providers-tab" data-bs-toggle="tab" data-bs-target="#providers" type="button" role="tab">
                                <i class="fas fa-trophy"></i> Provider Rankings
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="routes-tab" data-bs-toggle="tab" data-bs-target="#routes" type="button" role="tab">
                                <i class="fas fa-map"></i> Route Analytics
                            </button>
                        </li>
                    </ul>
                </div>
                
                <div class="card-body">
                    <div class="tab-content" id="analyticsTabContent">
                        <!-- Market Overview Tab -->
                        <div class="tab-pane fade show active" id="overview" role="tabpanel">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="text-primary mb-3">📈 Price Trends</h6>
                                    <div id="priceTrendsChart">
                                        <canvas id="priceChart" width="400" height="200"></canvas>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <h6 class="text-success mb-3">📊 Capacity Trends</h6>
                                    <div id="capacityTrendsChart">
                                        <canvas id="capacityChart" width="400" height="200"></canvas>
                                    </div>
                                </div>
                            </div>
                            
                            <hr class="my-4">
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="text-info mb-3">🏆 Top Routes</h6>
                                    <div class="table-responsive">
                                        <table class="table table-sm" id="topRoutesTable">
                                            <thead>
                                                <tr>
                                                    <th>Route</th>
                                                    <th>Providers</th>
                                                    <th>Avg Rate</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <!-- Dynamic content -->
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <h6 class="text-warning mb-3">💡 Market Insights</h6>
                                    <div id="marketRecommendations">
                                        <!-- Dynamic recommendations -->
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Price Benchmarks Tab -->
                        <div class="tab-pane fade" id="benchmarks" role="tabpanel">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="card bg-light">
                                        <div class="card-body text-center">
                                            <h4 class="text-primary" id="marketAverage">$2.45</h4>
                                            <p class="mb-0">Market Average</p>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-md-4">
                                    <div class="card bg-light">
                                        <div class="card-body text-center">
                                            <h4 class="text-success" id="marketMedian">$2.35</h4>
                                            <p class="mb-0">Market Median</p>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-md-4">
                                    <div class="card bg-light">
                                        <div class="card-body text-center">
                                            <h4 class="text-info" id="priceRange">$1.20 - $4.80</h4>
                                            <p class="mb-0">Price Range</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <hr class="my-4">
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="text-primary mb-3">🚛 Transport Mode Benchmarks</h6>
                                    <div id="transportBenchmarks">
                                        <!-- Dynamic transport mode data -->
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <h6 class="text-success mb-3">🗺️ Route Benchmarks</h6>
                                    <div id="routeBenchmarks">
                                        <!-- Dynamic route benchmark data -->
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Provider Rankings Tab -->
                        <div class="tab-pane fade" id="providers" role="tabpanel">
                            <div class="row">
                                <div class="col-md-12">
                                    <h6 class="text-primary mb-3">🏆 Top Performing Providers</h6>
                                    <div id="providerRankings">
                                        <!-- Dynamic provider rankings -->
                                    </div>
                                </div>
                            </div>
                            
                            <hr class="my-4">
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="text-info mb-3">📊 Performance Benchmarks</h6>
                                    <div id="performanceBenchmarks">
                                        <!-- Dynamic benchmarks -->
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <h6 class="text-warning mb-3">💡 Provider Insights</h6>
                                    <div id="providerInsights">
                                        <!-- Dynamic insights -->
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Route Analytics Tab -->
                        <div class="tab-pane fade" id="routes" role="tabpanel">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label">Analyze Specific Route</label>
                                    <select class="form-select" id="routeOriginAnalysis">
                                        <option value="">Select origin country</option>
                                        {% for country in countries %}
                                            <option value="{{ country }}">{{ country }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                                
                                <div class="col-md-6">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="d-flex">
                                        <select class="form-select me-2" id="routeDestinationAnalysis">
                                            <option value="">Select destination country</option>
                                            {% for country in countries %}
                                                <option value="{{ country }}">{{ country }}</option>
                                            {% endfor %}
                                        </select>
                                        <button class="btn btn-primary" onclick="analyzeRoute()">
                                            <i class="fas fa-search"></i> Analyze
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <div id="routeAnalysisResults">
                                <div class="text-center text-muted py-5">
                                    <i class="fas fa-map fa-3x mb-3"></i>
                                    <h5>Select a route to analyze</h5>
                                    <p>Choose origin and destination countries to get detailed route analytics</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Include Chart.js for data visualization -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
// Global variables
let priceChart = null;
let capacityChart = null;

// Initialize market intelligence
document.addEventListener('DOMContentLoaded', function() {
    loadMarketOverview();
    
    // Tab change handlers
    document.getElementById('benchmarks-tab').addEventListener('click', function() {
        if (this.classList.contains('active')) return;
        loadPriceBenchmarks();
    });
    
    document.getElementById('providers-tab').addEventListener('click', function() {
        if (this.classList.contains('active')) return;
        loadProviderRankings();
    });
});

// Load market overview data
async function loadMarketOverview() {
    try {
        const response = await fetch('/api/market/overview');
        const result = await response.json();
        
        if (result.success) {
            updateMarketOverviewCards(result.data.overview);
            updateTopRoutes(result.data.top_routes);
            updateMarketRecommendations(result.data.recommendations);
            createPriceChart(result.data.price_trends);
            createCapacityChart(result.data.capacity_trends);
        }
    } catch (error) {
        console.error('Failed to load market overview:', error);
    }
}

// Update market overview cards
function updateMarketOverviewCards(overview) {
    document.getElementById('totalRoutes').textContent = overview.total_routes;
    document.getElementById('activeProviders').textContent = overview.active_providers;
    document.getElementById('avgRate').textContent = '$' + overview.average_rate_per_kg.toFixed(2);
    document.getElementById('marketGrowth').textContent = overview.market_growth_rate.toFixed(1) + '%';
}

// Update top routes table
function updateTopRoutes(routes) {
    const tbody = document.querySelector('#topRoutesTable tbody');
    tbody.innerHTML = '';
    
    routes.slice(0, 5).forEach(route => {
        const row = tbody.insertRow();
        row.innerHTML = `
            <td>${route.route__origin_country} → ${route.route__destination_country}</td>
            <td><span class="badge bg-primary">${route.count}</span></td>
            <td>$2.${20 + (route.count * 5)}</td>
        `;
    });
}

// Update market recommendations
function updateMarketRecommendations(recommendations) {
    const container = document.getElementById('marketRecommendations');
    container.innerHTML = '';
    
    recommendations.forEach(rec => {
        const item = document.createElement('div');
        item.className = 'alert alert-info py-2 px-3 mb-2';
        item.innerHTML = `<i class="fas fa-lightbulb me-2"></i>${rec}`;
        container.appendChild(item);
    });
}

// Create price trend chart
function createPriceChart(trends) {
    const ctx = document.getElementById('priceChart').getContext('2d');
    
    if (priceChart) {
        priceChart.destroy();
    }
    
    priceChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
            datasets: [{
                label: 'Average Rate ($/kg)',
                data: [2.12, 2.18, 2.25, 2.31, 2.38, 2.45],
                borderColor: '#007bff',
                backgroundColor: 'rgba(0, 123, 255, 0.1)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: false,
                    min: 2.0
                }
            }
        }
    });
}

// Create capacity chart
function createCapacityChart(trends) {
    const ctx = document.getElementById('capacityChart').getContext('2d');
    
    if (capacityChart) {
        capacityChart.destroy();
    }
    
    capacityChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['Truck', 'Ship', 'Air', 'Rail'],
            datasets: [{
                data: [85.2, 78.6, 92.1, 67.3],
                backgroundColor: [
                    '#28a745',
                    '#17a2b8',
                    '#ffc107',
                    '#6f42c1'
                ]
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}

// Load price benchmarks
async function loadPriceBenchmarks() {
    try {
        const transportType = document.getElementById('transportFilter').value;
        const originCountry = document.getElementById('originFilter').value;
        const destinationCountry = document.getElementById('destinationFilter').value;
        
        const params = new URLSearchParams();
        if (transportType) params.append('transport_type', transportType);
        if (originCountry) params.append('origin_country', originCountry);
        if (destinationCountry) params.append('destination_country', destinationCountry);
        
        const response = await fetch(`/api/market/price-benchmarks?${params}`);
        const result = await response.json();
        
        if (result.success) {
            updatePriceBenchmarks(result.data);
        }
    } catch (error) {
        console.error('Failed to load price benchmarks:', error);
    }
}

// Update price benchmarks display
function updatePriceBenchmarks(data) {
    // Update benchmark cards
    document.getElementById('marketAverage').textContent = '$' + data.overall_benchmarks.market_average.toFixed(2);
    document.getElementById('marketMedian').textContent = '$' + data.overall_benchmarks.market_median.toFixed(2);
    document.getElementById('priceRange').textContent = 
        '$' + data.overall_benchmarks.price_range.min.toFixed(2) + ' - $' + data.overall_benchmarks.price_range.max.toFixed(2);
    
    // Update transport mode benchmarks
    const transportContainer = document.getElementById('transportBenchmarks');
    transportContainer.innerHTML = '';
    
    Object.entries(data.transport_mode_benchmarks).forEach(([mode, data]) => {
        const item = document.createElement('div');
        item.className = 'mb-3';
        item.innerHTML = `
            <div class="d-flex justify-content-between align-items-center">
                <span class="fw-bold">${mode.charAt(0).toUpperCase() + mode.slice(1)}</span>
                <span class="badge bg-primary">$${data.average_rate.toFixed(2)}</span>
            </div>
            <div class="progress mt-1">
                <div class="progress-bar" role="progressbar" style="width: ${data.volume_share}%" 
                     aria-valuenow="${data.volume_share}" aria-valuemin="0" aria-valuemax="100">
                    ${data.volume_share}%
                </div>
            </div>
            <small class="text-muted">${data.provider_count} providers</small>
        `;
        transportContainer.appendChild(item);
    });
    
    // Update route benchmarks
    const routeContainer = document.getElementById('routeBenchmarks');
    routeContainer.innerHTML = '';
    
    Object.entries(data.route_benchmarks).slice(0, 5).forEach(([route, data]) => {
        const item = document.createElement('div');
        item.className = 'mb-3 p-3 bg-light rounded';
        item.innerHTML = `
            <div class="d-flex justify-content-between align-items-center">
                <span class="fw-bold">${route}</span>
                <span class="badge bg-success">$${data.average_rate.toFixed(2)}</span>
            </div>
            <div class="mt-1">
                <small class="text-muted">${data.provider_count} providers • ${data.competitiveness} competition</small>
            </div>
        `;
        routeContainer.appendChild(item);
    });
}

// Load provider rankings
async function loadProviderRankings() {
    try {
        const response = await fetch('/api/market/provider-performance');
        const result = await response.json();
        
        if (result.success) {
            updateProviderRankings(result.data);
        }
    } catch (error) {
        console.error('Failed to load provider rankings:', error);
    }
}

// Update provider rankings display
function updateProviderRankings(data) {
    const container = document.getElementById('providerRankings');
    container.innerHTML = '';
    
    data.market_leaders.forEach((provider, index) => {
        const metrics = provider.performance_metrics;
        const item = document.createElement('div');
        item.className = 'card mb-3';
        item.innerHTML = `
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h6 class="card-title mb-1">
                            <span class="badge bg-gold me-2">#${index + 1}</span>
                            ${provider.provider_name}
                        </h6>
                        <p class="text-muted mb-2">Performance Score: ${metrics.performance_score}/100</p>
                        <div class="d-flex gap-2 mb-2">
                            <span class="badge bg-primary">Conversion: ${metrics.conversion_rate}%</span>
                            <span class="badge bg-success">Rating: ${metrics.reliability_rating}/5</span>
                            <span class="badge bg-info">Routes: ${metrics.total_routes}</span>
                        </div>
                    </div>
                    <div class="text-end">
                        <h5 class="text-primary mb-1">$${metrics.avg_rate_per_kg}</h5>
                        <small class="text-muted">per kg</small>
                    </div>
                </div>
                <div class="progress mt-2">
                    <div class="progress-bar bg-warning" role="progressbar" 
                         style="width: ${metrics.performance_score}%" 
                         aria-valuenow="${metrics.performance_score}" 
                         aria-valuemin="0" aria-valuemax="100">
                    </div>
                </div>
            </div>
        `;
        container.appendChild(item);
    });
    
    // Update benchmarks
    if (data.performance_benchmarks) {
        const benchmarkContainer = document.getElementById('performanceBenchmarks');
        benchmarkContainer.innerHTML = `
            <div class="row">
                <div class="col-6">
                    <div class="text-center p-3 bg-light rounded">
                        <h4 class="text-primary">${data.performance_benchmarks.avg_conversion_rate.toFixed(1)}%</h4>
                        <small>Avg Conversion Rate</small>
                    </div>
                </div>
                <div class="col-6">
                    <div class="text-center p-3 bg-light rounded">
                        <h4 class="text-success">${data.performance_benchmarks.avg_performance_score.toFixed(1)}</h4>
                        <small>Avg Performance Score</small>
                    </div>
                </div>
            </div>
        `;
    }
    
    // Update insights
    const insightsContainer = document.getElementById('providerInsights');
    insightsContainer.innerHTML = '';
    
    data.recommendations.forEach(rec => {
        const item = document.createElement('div');
        item.className = 'alert alert-warning py-2 px-3 mb-2';
        item.innerHTML = `<i class="fas fa-chart-line me-2"></i>${rec}`;
        insightsContainer.appendChild(item);
    });
}

// Analyze specific route
async function analyzeRoute() {
    const origin = document.getElementById('routeOriginAnalysis').value;
    const destination = document.getElementById('routeDestinationAnalysis').value;
    
    if (!origin || !destination) {
        alert('Please select both origin and destination countries');
        return;
    }
    
    try {
        const response = await fetch(`/api/market/route-analytics/${origin}/${destination}`);
        const result = await response.json();
        
        if (result.success) {
            displayRouteAnalysis(result.data);
        }
    } catch (error) {
        console.error('Failed to analyze route:', error);
    }
}

// Display route analysis results
function displayRouteAnalysis(data) {
    const container = document.getElementById('routeAnalysisResults');
    const routeInfo = data.route_info;
    
    container.innerHTML = `
        <div class="row">
            <div class="col-md-6">
                <h6 class="text-primary mb-3">📊 Route Statistics</h6>
                <div class="card bg-light">
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-4">
                                <h4 class="text-primary">${routeInfo.total_providers}</h4>
                                <small>Providers</small>
                            </div>
                            <div class="col-4">
                                <h4 class="text-success">$${routeInfo.average_rate.toFixed(2)}</h4>
                                <small>Avg Rate</small>
                            </div>
                            <div class="col-4">
                                <h4 class="text-info">${routeInfo.total_quotes_90d}</h4>
                                <small>Quotes (90d)</small>
                            </div>
                        </div>
                    </div>
                </div>
                
                <h6 class="text-success mt-4 mb-3">💰 Price Range</h6>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span class="badge bg-success">Min: $${routeInfo.min_rate.toFixed(2)}</span>
                    <span class="badge bg-warning">Avg: $${routeInfo.average_rate.toFixed(2)}</span>
                    <span class="badge bg-danger">Max: $${routeInfo.max_rate.toFixed(2)}</span>
                </div>
            </div>
            
            <div class="col-md-6">
                <h6 class="text-info mb-3">🏢 Provider Breakdown</h6>
                <div id="routeProviders">
                    ${data.provider_breakdown.slice(0, 5).map(provider => `
                        <div class="card mb-2">
                            <div class="card-body py-2">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="mb-1">${provider.provider_name}</h6>
                                        <small class="text-muted">Rating: ${provider.service_rating.toFixed(1)}/5</small>
                                    </div>
                                    <div class="text-end">
                                        <span class="badge bg-primary">$${provider.base_rate.toFixed(2)}</span>
                                        <br><small class="text-muted">${provider.conversion_rate.toFixed(1)}% conversion</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        </div>
        
        ${data.trends ? `
            <hr class="my-4">
            <div class="row">
                <div class="col-md-12">
                    <h6 class="text-warning mb-3">📈 Route Trends</h6>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="text-center p-3 bg-light rounded">
                                <h5 class="text-success">${data.trends.price_trend_30d}</h5>
                                <small>Price Trend (30d)</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center p-3 bg-light rounded">
                                <h5 class="text-primary">${data.trends.volume_trend_30d}</h5>
                                <small>Volume Trend (30d)</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center p-3 bg-light rounded">
                                <h5 class="text-info">${data.trends.competition_level}</h5>
                                <small>Competition Level</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center p-3 bg-light rounded">
                                <h5 class="text-warning">${data.trends.forecast.next_30d.price}</h5>
                                <small>30d Forecast</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        ` : ''}
    `;
}

// Update market analysis with filters
function updateMarketAnalysis() {
    loadMarketOverview();
    if (document.getElementById('benchmarks-tab').classList.contains('active')) {
        loadPriceBenchmarks();
    }
}
</script>

<style>
.nav-tabs .nav-link {
    color: #6c757d;
}

.nav-tabs .nav-link.active {
    color: #007bff;
    font-weight: 600;
}

.card {
    border: 1px solid #e3e6f0;
    border-radius: 0.375rem;
}

.badge.bg-gold {
    background-color: #ffd700 !important;
    color: #000;
}

.progress {
    height: 6px;
}

.opacity-75 {
    opacity: 0.75 !important;
}

.alert {
    border-radius: 0.25rem;
}

.bg-light {
    background-color: #f8f9fc !important;
}
</style>
{% endblock %}