{% extends "base.html" %}

{% block content %}
<div class="page-container">
    <div class="page-header">
        <h1>Complete Payment</h1>
        {% if payment_type == 'private_shipment' %}
        <p>Secure payment for private container space</p>
        {% else %}
        <p>Secure payment for shipment #{{ shipment.tracking_number }}</p>
        {% endif %}
    </div>

    <!-- Payment Summary -->
    <div class="row mb-4">
        <div class="col-lg-8">
            <div class="payment-card">
                <h5>Payment Methods</h5>
                
                <!-- Payment Method Selection -->
                <div class="payment-methods mb-4">
                    <div class="form-check payment-option">
                        <input class="form-check-input" type="radio" name="payment_method" id="stripe_card" value="stripe" checked>
                        <label class="form-check-label" for="stripe_card">
                            <div class="payment-method-content">
                                <i class="fas fa-credit-card text-primary"></i>
                                <span>Credit/Debit Card</span>
                                <div class="payment-badges">
                                    <img src="/static/images/visa.svg" alt="Visa" class="card-logo">
                                    <img src="/static/images/mastercard.svg" alt="Mastercard" class="card-logo">
                                    <img src="/static/images/amex.svg" alt="Amex" class="card-logo">
                                </div>
                            </div>
                        </label>
                    </div>
                    
                    <div class="form-check payment-option">
                        <input class="form-check-input" type="radio" name="payment_method" id="bank_transfer" value="bank_transfer">
                        <label class="form-check-label" for="bank_transfer">
                            <div class="payment-method-content">
                                <i class="fas fa-university text-success"></i>
                                <span>Bank Transfer</span>
                                <small class="text-muted">Manual verification required</small>
                            </div>
                        </label>
                    </div>
                </div>

                <!-- Stripe Card Form -->
                <div id="stripe-form" class="payment-form-section">
                    <form id="payment-form">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="cardholder_name" class="form-label">Cardholder Name</label>
                                <input type="text" class="form-control" id="cardholder_name" name="cardholder_name" required>
                            </div>
                            <div class="col-md-6">
                                <label for="card_email" class="form-label">Email Address</label>
                                <input type="email" class="form-control" id="card_email" name="email" value="{{ user.email }}" required>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="card-element" class="form-label">Card Information</label>
                            <div id="card-element" class="form-control" style="height: 40px; padding: 10px;">
                                <!-- Stripe Elements will create form elements here -->
                            </div>
                            <div id="card-errors" role="alert" class="text-danger mt-2"></div>
                        </div>
                        
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="save_payment_method">
                            <label class="form-check-label" for="save_payment_method">
                                Save this payment method for future use
                            </label>
                        </div>
                        
                        <!-- Optional Insurance Section -->
                        <div class="insurance-section mb-3 p-3 border rounded bg-light">
                            <h6><i class="fas fa-shield-alt text-info me-2"></i>Shipment Insurance <span class="badge bg-info">Optional</span></h6>
                            <p class="text-muted small mb-3">Insurance is not required but recommended for valuable shipments.</p>
                            
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="radio" name="insurance_option" id="skip_insurance" value="skip" checked>
                                <label class="form-check-label" for="skip_insurance">
                                    <strong>Skip Insurance</strong> - I have my own insurance or prefer to proceed without platform insurance
                                </label>
                            </div>
                            
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="radio" name="insurance_option" id="request_quote" value="quote">
                                <label class="form-check-label" for="request_quote">
                                    <strong>Request Insurance Quote</strong> - Get a quote from our insurance partners
                                </label>
                            </div>
                            
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="insurance_option" id="upload_external" value="upload">
                                <label class="form-check-label" for="upload_external">
                                    <strong>Upload External Insurance</strong> - I have insurance from my own provider
                                </label>
                            </div>
                            
                            <!-- External Insurance Upload -->
                            <div id="external_insurance_upload" class="mt-3 d-none">
                                <label for="insurance_document" class="form-label">Upload Insurance Document (PDF)</label>
                                <input type="file" class="form-control" id="insurance_document" accept=".pdf">
                                <small class="text-muted">Upload your existing insurance policy document</small>
                            </div>
                        </div>
                        
                        <!-- Contract Agreement -->
                        <div class="contract-agreement mb-3 p-3 border rounded">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="contract_agreement" required>
                                <label class="form-check-label" for="contract_agreement">
                                    By clicking Continue, I agree to the terms of shipment with <strong>{{ shipment.logistics_provider__company_name or shipment.logistics_provider__first_name }}</strong>.
                                    <a href="javascript:void(0)" onclick="showContractModal()" class="text-primary">View Contract PDF</a>
                                </label>
                            </div>
                        </div>
                        
                        <button type="submit" id="submit-payment" class="btn btn-primary btn-lg w-100">
                            <span id="button-text">Pay ${{ payment.amount }}</span>
                            <div id="spinner" class="spinner-border spinner-border-sm d-none" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </button>
                    </form>
                </div>

                <!-- Bank Transfer Form -->
                <div id="bank-transfer-form" class="payment-form-section d-none">
                    <div class="bank-details-card">
                        <h6>Bank Transfer Details</h6>
                        <div class="bank-info">
                            <div class="bank-detail-item">
                                <strong>Bank Name:</strong> Cloverics Business Bank
                            </div>
                            <div class="bank-detail-item">
                                <strong>Account Name:</strong> Cloverics Logistics Ltd
                            </div>
                            <div class="bank-detail-item">
                                <strong>Account Number:</strong> **********
                            </div>
                            <div class="bank-detail-item">
                                <strong>Routing Number:</strong> *********
                            </div>
                            <div class="bank-detail-item">
                                <strong>Swift Code:</strong> CLVRUS33
                            </div>
                            <div class="bank-detail-item">
                                <strong>Reference:</strong> 
                                {% if payment_type == 'private_shipment' %}
                                    {{ private_shipment.container_id }}-{{ user.id }}
                                {% else %}
                                    {{ shipment.tracking_number }}
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <form id="bank-transfer-proof-form" enctype="multipart/form-data">
                        <div class="mb-3">
                            <label for="transfer_receipt" class="form-label">Upload Transfer Receipt</label>
                            <input type="file" class="form-control" id="transfer_receipt" name="receipt" accept=".pdf,.jpg,.jpeg,.png" required>
                            <div class="form-text">Upload proof of transfer (PDF, JPG, PNG - Max 5MB)</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="transfer_reference" class="form-label">Transfer Reference Number</label>
                            <input type="text" class="form-control" id="transfer_reference" name="reference" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="transfer_date" class="form-label">Transfer Date</label>
                            <input type="date" class="form-control" id="transfer_date" name="transfer_date" required>
                        </div>
                        
                        <button type="submit" class="btn btn-success btn-lg w-100" onclick="submitTransferProof()">
                            Submit Transfer Proof
                        </button>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- Order Summary -->
        <div class="col-lg-4">
            <div class="summary-card">
                <h5>Order Summary</h5>
                
                <div class="shipment-details">
                    {% if payment_type == 'private_shipment' %}
                    <div class="detail-row">
                        <span>Container ID:</span>
                        <strong>{{ private_shipment.container_id }}</strong>
                    </div>
                    <div class="detail-row">
                        <span>Route:</span>
                        <strong>{{ private_shipment.origin_city }}, {{ private_shipment.origin_country }} → {{ private_shipment.destination_city }}, {{ private_shipment.destination_country }}</strong>
                    </div>
                    <div class="detail-row">
                        <span>Provider:</span>
                        <strong>{{ provider_name }}</strong>
                    </div>
                    <div class="detail-row">
                        <span>Your Cargo:</span>
                        <strong>{{ invitation.weight_kg }}kg / {{ invitation.volume_m3 }}m³</strong>
                    </div>
                    <div class="detail-row">
                        <span>Service Type:</span>
                        <strong>Private Container Sharing</strong>
                    </div>
                    {% if savings %}
                    <div class="detail-row text-success">
                        <span>Your Savings:</span>
                        <strong>${{ savings|round(2) }} (30%)</strong>
                    </div>
                    {% endif %}
                    {% else %}
                    <div class="detail-row">
                        <span>Tracking Number:</span>
                        <strong>{{ shipment.tracking_number }}</strong>
                    </div>
                    <div class="detail-row">
                        <span>Route:</span>
                        <strong>{{ shipment.origin }} → {{ shipment.destination }}</strong>
                    </div>
                    <div class="detail-row">
                        <span>Provider:</span>
                        <strong>{{ shipment.provider_name }}</strong>
                    </div>
                    <div class="detail-row">
                        <span>Weight:</span>
                        <strong>{{ shipment.weight }}kg</strong>
                    </div>
                    <div class="detail-row">
                        <span>Service Type:</span>
                        <strong>{{ shipment.service_type }}</strong>
                    </div>
                    {% endif %}
                </div>
                
                <hr>
                
                <div class="cost-breakdown">
                    {% if payment_type == 'private_shipment' %}
                    <div class="cost-item">
                        <span>Container Space Cost:</span>
                        <span>${{ amount|round(2) }}</span>
                    </div>
                    <div class="cost-item">
                        <span>Platform Fee (0.1%):</span>
                        <span>${{ (amount * 0.001)|round(2) }}</span>
                    </div>
                    {% if savings %}
                    <div class="cost-item text-success">
                        <span>Savings vs Solo Shipment:</span>
                        <span>-${{ savings|round(2) }}</span>
                    </div>
                    {% endif %}
                    <hr>
                    <div class="cost-item total">
                        <strong>Total Amount:</strong>
                        <strong>${{ (amount + amount * 0.001)|round(2) }}</strong>
                    </div>
                    {% else %}
                    <div class="cost-item">
                        <span>Base Shipping Cost:</span>
                        <span>${{ payment.base_amount }}</span>
                    </div>
                    <div class="cost-item">
                        <span>Insurance:</span>
                        <span>${{ payment.insurance_amount }}</span>
                    </div>
                    <div class="cost-item">
                        <span>Processing Fee:</span>
                        <span>${{ payment.processing_fee }}</span>
                    </div>
                    <div class="cost-item">
                        <span>Tax:</span>
                        <span>${{ payment.tax_amount }}</span>
                    </div>
                    <hr>
                    <div class="cost-item total">
                        <strong>Total Amount:</strong>
                        <strong>${{ payment.amount }}</strong>
                    </div>
                    {% endif %}
                </div>
                
                <div class="security-badges">
                    <div class="security-item">
                        <i class="fas fa-shield-alt text-success"></i>
                        <span>SSL Secured</span>
                    </div>
                    <div class="security-item">
                        <i class="fas fa-lock text-success"></i>
                        <span>256-bit Encryption</span>
                    </div>
                    <div class="security-item">
                        <i class="fas fa-credit-card text-success"></i>
                        <span>PCI Compliant</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Contract Modal -->
<div class="modal fade" id="contractModal" tabindex="-1" aria-labelledby="contractModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="contractModalLabel">Shipping Contract Agreement</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="contract-content">
                    <h6>Logistics Service Agreement</h6>
                    <p><strong>Provider:</strong> {{ shipment.logistics_provider__company_name or shipment.logistics_provider__first_name }}</p>
                    <p><strong>Customer:</strong> {{ user.first_name }} {{ user.last_name }}</p>
                    <p><strong>Shipment:</strong> {{ shipment.tracking_number }}</p>
                    
                    <h6>Terms and Conditions</h6>
                    <div class="terms-content">
                        <p>1. The logistics provider agrees to transport the specified cargo from {{ shipment.origin_city }}, {{ shipment.origin_country }} to {{ shipment.destination_city }}, {{ shipment.destination_country }}.</p>
                        <p>2. The total cost for this service is ${{ shipment.total_price }}.</p>
                        <p>3. The customer agrees to prepare cargo according to specified requirements.</p>
                        <p>4. Both parties agree to use the Cloverics platform for communication and tracking.</p>
                        <p>5. Disputes will be resolved through the Cloverics dispute resolution system.</p>
                        <p>6. This agreement is governed by international shipping regulations.</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" onclick="acceptContract()">I Accept Terms</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Contract Modal Functions
function showContractModal() {
    const modal = new bootstrap.Modal(document.getElementById('contractModal'));
    modal.show();
}

function acceptContract() {
    const checkbox = document.getElementById('contract_agreement');
    checkbox.checked = true;
    const modal = bootstrap.Modal.getInstance(document.getElementById('contractModal'));
    modal.hide();
    
    // Show success message
    Swal.fire({
        icon: 'success',
        title: 'Contract Accepted',
        text: 'You can now proceed with payment',
        timer: 2000,
        showConfirmButton: false
    });
}

// Payment Method Toggle
document.addEventListener('DOMContentLoaded', function() {
    const stripeRadio = document.getElementById('stripe_card');
    const bankRadio = document.getElementById('bank_transfer');
    const stripeForm = document.getElementById('stripe-form');
    const bankForm = document.getElementById('bank-transfer-form');
    
    function togglePaymentMethod() {
        if (stripeRadio.checked) {
            stripeForm.classList.remove('d-none');
            bankForm.classList.add('d-none');
        } else if (bankRadio.checked) {
            stripeForm.classList.add('d-none');
            bankForm.classList.remove('d-none');
        }
    }
    
    stripeRadio.addEventListener('change', togglePaymentMethod);
    bankRadio.addEventListener('change', togglePaymentMethod);
    
    // Initialize
    togglePaymentMethod();
    
    // Bank Transfer Form Submission
    const bankTransferForm = document.getElementById('bank-transfer-proof-form');
    if (bankTransferForm) {
        bankTransferForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            
            {% if payment_type == 'private_shipment' %}
            formData.append('invitation_id', '{{ invitation.id }}');
            const uploadUrl = '/api/private-shipment/upload-bank-transfer-proof';
            {% else %}
            formData.append('shipment_id', '{{ shipment.id }}');
            const uploadUrl = '/api/upload-bank-transfer-proof';
            {% endif %}
            
            // Show loading
            Swal.fire({
                title: 'Uploading...',
                text: 'Please wait while we process your transfer proof',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });
            
            fetch(uploadUrl, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                Swal.close();
                if (data.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Transfer Proof Uploaded',
                        text: 'Your transfer proof has been submitted for verification by the logistics provider.',
                        confirmButtonText: 'Continue'
                    }).then(() => {
                        window.location.href = '/customer/manage-shipments';
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Upload Failed',
                        text: data.error || 'Failed to upload transfer proof'
                    });
                }
            })
            .catch(error => {
                Swal.close();
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'An error occurred while uploading'
                });
            });
        });
    }
});
</script>
{% endblock %}

{% block extra_css %}
<style>
.payment-card, .summary-card {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 1.5rem;
}

.payment-methods {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    overflow: hidden;
}

.payment-option {
    margin: 0;
    border-bottom: 1px solid #dee2e6;
}

.payment-option:last-child {
    border-bottom: none;
}

.payment-option .form-check-label {
    width: 100%;
    padding: 1rem;
    margin: 0;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.payment-option .form-check-label:hover {
    background-color: #f8f9fa;
}

.payment-option .form-check-input:checked + .form-check-label {
    background-color: #e3f2fd;
    border-left: 4px solid #2196f3;
}

.payment-method-content {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.payment-method-content i {
    font-size: 1.5rem;
}

.payment-badges {
    margin-left: auto;
    display: flex;
    gap: 0.5rem;
}

.card-logo {
    height: 24px;
    width: auto;
}

.bank-details-card {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.bank-info {
    display: grid;
    gap: 0.75rem;
}

.bank-detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #dee2e6;
}

.bank-detail-item:last-child {
    border-bottom: none;
}

.shipment-details {
    margin-bottom: 1rem;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.75rem;
}

.cost-breakdown {
    margin-top: 1rem;
}

.cost-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.cost-item.total {
    font-size: 1.1rem;
    padding-top: 0.5rem;
}

.security-badges {
    margin-top: 1.5rem;
    padding-top: 1rem;
    border-top: 1px solid #dee2e6;
}

.security-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    color: #666;
}

#card-element {
    background: white;
    border: 1px solid #ced4da;
    border-radius: 4px;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

#card-element:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.StripeElement {
    height: 40px;
    padding: 10px 12px;
    background-color: white;
    border: 1px solid transparent;
    border-radius: 4px;
}

.StripeElement--focus {
    box-shadow: 0 1px 3px 0 #cfd7df;
}

.StripeElement--invalid {
    border-color: #fa755a;
}

.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

@media (max-width: 768px) {
    .payment-card, .summary-card {
        padding: 1rem;
    }
    
    .payment-method-content {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .payment-badges {
        margin-left: 0;
    }
}
</style>

<script src="https://js.stripe.com/v3/"></script>
<script>
// Payment method switching
document.querySelectorAll('input[name="payment_method"]').forEach(radio => {
    radio.addEventListener('change', function() {
        const stripeForm = document.getElementById('stripe-form');
        const bankForm = document.getElementById('bank-transfer-form');
        
        if (this.value === 'stripe') {
            stripeForm.classList.remove('d-none');
            bankForm.classList.add('d-none');
        } else {
            stripeForm.classList.add('d-none');
            bankForm.classList.remove('d-none');
        }
    });
});

// Stripe integration
const stripe = Stripe('{{ stripe_public_key }}');
const elements = stripe.elements();

const cardElement = elements.create('card', {
    style: {
        base: {
            fontSize: '16px',
            color: '#424770',
            '::placeholder': {
                color: '#aab7c4',
            },
        },
    },
});

cardElement.mount('#card-element');

cardElement.on('change', function(event) {
    const displayError = document.getElementById('card-errors');
    if (event.error) {
        displayError.textContent = event.error.message;
    } else {
        displayError.textContent = '';
    }
});

// Handle form submission
const form = document.getElementById('payment-form');
form.addEventListener('submit', async function(event) {
    event.preventDefault();
    
    const submitButton = document.getElementById('submit-payment');
    const buttonText = document.getElementById('button-text');
    const spinner = document.getElementById('spinner');
    
    submitButton.disabled = true;
    buttonText.classList.add('d-none');
    spinner.classList.remove('d-none');
    
    const {error, paymentMethod} = await stripe.createPaymentMethod({
        type: 'card',
        card: cardElement,
        billing_details: {
            name: document.getElementById('cardholder_name').value,
            email: document.getElementById('card_email').value,
        },
    });
    
    if (error) {
        document.getElementById('card-errors').textContent = error.message;
        submitButton.disabled = false;
        buttonText.classList.remove('d-none');
        spinner.classList.add('d-none');
    } else {
        // Send payment method to server
        fetch('/customer/process-payment/{{ shipment.id }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                payment_method_id: paymentMethod.id,
                save_payment_method: document.getElementById('save_payment_method').checked
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.location.href = `/customer/payment-success/${data.payment_id}`;
            } else {
                document.getElementById('card-errors').textContent = data.error;
                submitButton.disabled = false;
                buttonText.classList.remove('d-none');
                spinner.classList.add('d-none');
            }
        });
    }
});

// Bank transfer form submission
document.getElementById('bank-transfer-proof-form').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    formData.append('shipment_id', '{{ shipment.id }}');
    
    fetch('/customer/submit-transfer-proof', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            window.location.href = `/customer/payment-pending/${data.payment_id}`;
        } else {
            alert('Error submitting transfer proof: ' + data.error);
        }
    });
});

// Handle insurance option selection
document.addEventListener('DOMContentLoaded', function() {
    const insuranceOptions = document.querySelectorAll('input[name="insurance_option"]');
    const externalUpload = document.getElementById('external_insurance_upload');
    
    insuranceOptions.forEach(option => {
        option.addEventListener('change', function() {
            if (this.value === 'upload') {
                externalUpload.classList.remove('d-none');
            } else {
                externalUpload.classList.add('d-none');
            }
        });
    });
});


// Auto-generated button handlers

function submitTransferProof() {
    const fileInput = document.querySelector('input[type="file"]');
    if (!fileInput || !fileInput.files.length) {
        showAlert('Please select a transfer proof file', 'warning');
        return;
    }
    
    const formData = new FormData();
    formData.append('transfer_proof', fileInput.files[0]);
    
    showLoadingSpinner();
    fetch('/payment/submit-proof', {
        method: 'POST',
        body: formData,
        headers: {'X-CSRFToken': getCSRFToken()}
    })
    .then(response => response.json())
    .then(data => {
        hideLoadingSpinner();
        if (data.success) {
            showAlert('Transfer proof submitted successfully', 'success');
        } else {
            showAlert('Submission failed: ' + data.error, 'error');
        }
    })
    .catch(error => {
        hideLoadingSpinner();
        showAlert('Submission failed', 'error');
    });
}
</script>
{% endblock %}