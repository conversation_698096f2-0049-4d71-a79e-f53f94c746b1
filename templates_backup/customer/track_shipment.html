{% extends "base.html" %}

{% block content %}
<div class="page-container">
    <div class="page-header">
        <h1>Track Your Shipment</h1>
        <p>Enter your tracking number to get real-time updates</p>
    </div>

    <!-- Tracking Form -->
    <div class="row mb-4">
        <div class="col-md-8 mx-auto">
            <div class="tracking-search-card">
                <form method="POST" action="/customer/track-shipment">
                    <div class="input-group">
                        <input type="text" class="form-control" id="trackingNumberInput" name="tracking_number" 
                               placeholder="Enter tracking number (e.g., CLV-2025-001847)" required>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i> Track Shipment
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Error Message -->
    {% if error_message %}
    <div class="row">
        <div class="col-12">
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle"></i>
                <strong>{{ error_message }}</strong>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Tracking Results -->
    {% if tracking_data %}
    <div class="row">
        <div class="col-12">
            <div class="tracking-results-card">
                <!-- Shipment Header -->
                <div class="shipment-header">
                    <div class="shipment-info">
                        <h4>Shipment #{{ tracking_data.tracking_number }}</h4>
                        <p class="mb-1">
                            <i class="fas fa-map-marker-alt text-success"></i>
                            {{ tracking_data.origin }}
                            <i class="fas fa-arrow-right mx-2"></i>
                            <i class="fas fa-map-marker-alt text-danger"></i>
                            {{ tracking_data.destination }}
                        </p>
                        <p class="text-muted mb-0">Provider: {{ tracking_data.provider_name }}</p>
                    </div>
                    <div class="status-badge">
                        <span class="badge status-{{ tracking_data.status.lower() }}">
                            {{ tracking_data.status_display }}
                        </span>
                    </div>
                </div>

                <!-- Progress Bar -->
                <div class="tracking-progress">
                    <div class="progress-bar-container">
                        <div class="progress">
                            <div class="progress-bar bg-primary" style="width: {{ tracking_data.progress }}%"></div>
                        </div>
                        <span class="progress-text">{{ tracking_data.progress }}% Complete</span>
                    </div>
                </div>

                <!-- Shipment Details -->
                <div class="row mt-4">
                    <div class="col-md-6">
                        <div class="detail-section">
                            <h6>Shipment Details</h6>
                            <div class="detail-item">
                                <span class="label">Weight:</span>
                                <span class="value">{{ tracking_data.weight }}kg</span>
                            </div>
                            <div class="detail-item">
                                <span class="label">Cargo:</span>
                                <span class="value">{{ tracking_data.cargo_description }}</span>
                            </div>
                            <div class="detail-item">
                                <span class="label">Transport Type:</span>
                                <span class="value">{{ tracking_data.transport_type }}</span>
                            </div>
                            <div class="detail-item">
                                <span class="label">Estimated Delivery:</span>
                                <span class="value">{{ tracking_data.estimated_delivery }}</span>
                            </div>
                            {% if tracking_data.actual_delivery %}
                            <div class="detail-item">
                                <span class="label">Actual Delivery:</span>
                                <span class="value">{{ tracking_data.actual_delivery }}</span>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="detail-section">
                            <h6>Current Location</h6>
                            <div class="current-location">
                                <i class="fas fa-map-marker-alt text-primary"></i>
                                <div class="location-info">
                                    <div class="location-name">{{ tracking_data.current_location }}</div>
                                    <div class="location-time">Status: {{ tracking_data.status_display }}</div>
                                </div>
                            </div>
                            {% if tracking_data.customs_cleared %}
                            <div class="customs-status mt-3">
                                <i class="fas fa-check-circle text-success"></i>
                                <span class="text-success">Customs Cleared</span>
                            </div>
                            {% endif %}
                            {% if tracking_data.insurance_included %}
                            <div class="insurance-status mt-2">
                                <i class="fas fa-shield-alt text-info"></i>
                                <span class="text-info">Insurance Included</span>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Tracking History -->
                <div class="tracking-history mt-4">
                    <h6>Tracking History</h6>
                    <div class="timeline">
                        {% for event in tracking_data.events %}
                        <div class="timeline-item">
                            <div class="timeline-marker"></div>
                            <div class="timeline-content">
                                <div class="event-header">
                                    <span class="event-status">{{ event.event }}</span>
                                    <span class="event-time">{{ event.date }}</span>
                                </div>
                                <div class="event-location">{{ event.location }}</div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>

                <!-- Actions -->
                <div class="shipment-actions mt-4">
                    <a href="/customer/manage-shipments" class="btn btn-outline-primary">
                        <i class="fas fa-info-circle"></i> View Full Details
                    </a>
                    <button class="btn btn-outline-secondary" onclick="printTracking()">
                        <i class="fas fa-print"></i> Print Tracking
                    </button>
                    {% if tracking_data.status == 'IN_TRANSIT' %}
                    <button class="btn btn-outline-warning" onclick="reportIssue('{{ tracking_data.shipment_id }}')">
                        <i class="fas fa-exclamation-triangle"></i> Report Issue
                    </button>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% elif searched %}
    <div class="empty-state">
        <i class="fas fa-search"></i>
        <h4>Tracking Number Not Found</h4>
        <p>Please check your tracking number and try again.</p>
        <small class="text-muted">
            Tracking numbers are usually in the format: CLV-YYYY-XXXXXX
        </small>
    </div>
    {% else %}
    <!-- Recent Shipments -->
    <div class="row">
        <div class="col-12">
            <div class="recent-shipments-card">
                <h5>Your Recent Shipments</h5>
                {% if recent_shipments %}
                <div class="shipments-list">
                    {% for shipment in recent_shipments %}
                    <div class="shipment-item" onclick="trackShipment('{{ shipment.tracking_number }}')">
                        <div class="shipment-basic-info">
                            <div class="tracking-number">#{{ shipment.tracking_number }}</div>
                            <div class="route">{{ shipment.origin }} → {{ shipment.destination }}</div>
                        </div>
                        <div class="shipment-status">
                            <span class="badge status-{{ shipment.status.lower() }}">
                                {{ shipment.status_display }}
                            </span>
                        </div>
                        <div class="shipment-date">{{ shipment.created_date }}</div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="empty-recent">
                    <p class="text-muted">No recent shipments found.</p>
                    <a href="/customer/search-shipping" class="btn btn-primary btn-sm">
                        <i class="fas fa-plus"></i> Create New Shipment
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_css %}
<style>
.tracking-search-card {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.input-group .form-control {
    border-right: none;
    font-size: 1.1rem;
    padding: 0.75rem 1rem;
}

.input-group .btn {
    border-left: none;
    padding: 0.75rem 1.5rem;
}

.tracking-results-card, .recent-shipments-card {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.shipment-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #eee;
}

.status-in_transit { background: #3498db; }
.status-delivered { background: #27ae60; }
.status-pending { background: #f39c12; }
.status-cancelled { background: #e74c3c; }

.tracking-progress {
    margin: 2rem 0;
}

.progress-steps {
    display: flex;
    justify-content: space-between;
    position: relative;
}

.progress-steps::before {
    content: '';
    position: absolute;
    top: 20px;
    left: 10%;
    right: 10%;
    height: 2px;
    background: #ddd;
    z-index: 1;
}

.progress-step {
    text-align: center;
    position: relative;
    z-index: 2;
    flex: 1;
}

.step-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #ddd;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 0.5rem;
    transition: all 0.3s ease;
}

.progress-step.completed .step-icon {
    background: #27ae60;
}

.progress-step.current .step-icon {
    background: #3498db;
    animation: pulse 2s infinite;
}

.step-label {
    font-size: 0.9rem;
    color: #666;
}

.progress-step.completed .step-label,
.progress-step.current .step-label {
    color: #2c3e50;
    font-weight: 500;
}

.detail-section {
    margin-bottom: 1.5rem;
}

.detail-section h6 {
    margin-bottom: 1rem;
    color: #2c3e50;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.detail-item .label {
    color: #666;
}

.detail-item .value {
    font-weight: 500;
}

.current-location {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
}

.location-name {
    font-weight: 500;
    color: #2c3e50;
}

.location-time {
    font-size: 0.9rem;
    color: #666;
}

.timeline {
    margin-top: 1rem;
}

.timeline-item {
    display: flex;
    margin-bottom: 1.5rem;
    position: relative;
}

.timeline-marker {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #3498db;
    margin-right: 1rem;
    margin-top: 0.25rem;
    flex-shrink: 0;
}

.timeline-content {
    flex-grow: 1;
}

.event-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.25rem;
}

.event-status {
    font-weight: 500;
    color: #2c3e50;
}

.event-time {
    color: #666;
    font-size: 0.9rem;
}

.event-location {
    color: #666;
    margin-bottom: 0.25rem;
}

.event-description {
    font-size: 0.9rem;
    color: #666;
}

.shipments-list {
    margin-top: 1rem;
}

.shipment-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border: 1px solid #eee;
    border-radius: 8px;
    margin-bottom: 0.5rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.shipment-item:hover {
    background: #f8f9fa;
    transform: translateY(-1px);
}

.tracking-number {
    font-weight: 600;
    color: #2c3e50;
}

.route {
    color: #666;
    font-size: 0.9rem;
}

.shipment-date {
    color: #666;
    font-size: 0.9rem;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}
</style>
{% endblock %}

{% block extra_js %}
<script>
// Auto-fill tracking number from URL parameter
document.addEventListener('DOMContentLoaded', function() {
    const urlParams = new URLSearchParams(window.location.search);
    const tracking = urlParams.get('tracking');
    
    if (tracking) {
        const trackingInput = document.getElementById('trackingNumberInput');
        if (trackingInput) {
            trackingInput.value = tracking;
            // Auto-submit the form if tracking number was provided
            trackingInput.closest('form').submit();
        }
    }
});

function trackShipment(trackingNumber) {
    document.querySelector('input[name="tracking_number"]').value = trackingNumber;
    document.querySelector('form').submit();
}

function printTracking() {
    window.print();
}

function reportIssue(shipmentId) {
    window.location.href = `/customer/report-issue/${shipmentId}`;
}
</script>
{% endblock %}