{% extends "base.html" %}

{% block content %}
<div class="dashboard-container">
    <div class="dashboard-header">
        <h1>Private Shipment Invitation</h1>
        <p>Review and respond to your exclusive container invitation</p>
    </div>

    <div class="row">
        <div class="col-md-8">
            <!-- Invitation Details Card -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5><i class="fas fa-container me-2"></i>Container Details</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Container ID:</strong> {{ invitation.private_shipment.container_id }}</p>
                            <p><strong>Route:</strong> {{ invitation.private_shipment.origin_city }}, {{ invitation.private_shipment.origin_country }} → {{ invitation.private_shipment.destination_city }}, {{ invitation.private_shipment.destination_country }}</p>
                            <p><strong>Container Type:</strong> {{ invitation.private_shipment.container_type }}</p>
                            <p><strong>Departure Date:</strong> {{ invitation.private_shipment.scheduled_departure_date|date:"M d, Y" }}</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Logistics Provider:</strong> {{ invitation.logistics_provider.company_name|default:invitation.logistics_provider.email }}</p>
                            <p><strong>Total Capacity:</strong> {{ invitation.private_shipment.total_capacity_kg|floatformat:0 }} kg / {{ invitation.private_shipment.total_capacity_m3|floatformat:1 }} m³</p>
                            <p><strong>Available Space:</strong> {{ invitation.private_shipment.get_available_space_kg|floatformat:0 }} kg / {{ invitation.private_shipment.get_available_space_m3|floatformat:1 }} m³</p>
                            <p><strong>Cost per m³:</strong> ${{ invitation.private_shipment.base_cost_per_m3|floatformat:2 }}</p>
                        </div>
                    </div>
                    
                    {% if invitation.invitation_message %}
                    <div class="alert alert-info mt-3">
                        <h6><i class="fas fa-comment me-2"></i>Message from Logistics Provider:</h6>
                        <p class="mb-0">{{ invitation.invitation_message }}</p>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Cargo Details Form -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5><i class="fas fa-boxes me-2"></i>Your Cargo Details</h5>
                </div>
                <div class="card-body">
                    <form id="invitationForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="weight_kg" class="form-label">Weight (kg)*</label>
                                    <input type="number" class="form-control" id="weight_kg" name="weight_kg" 
                                           step="0.1" min="0.1" max="{{ invitation.private_shipment.get_available_space_kg }}" required>
                                    <div class="form-text">Maximum: {{ invitation.private_shipment.get_available_space_kg|floatformat:0 }} kg</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="volume_m3" class="form-label">Volume (m³)*</label>
                                    <input type="number" class="form-control" id="volume_m3" name="volume_m3" 
                                           step="0.1" min="0.1" max="{{ invitation.private_shipment.get_available_space_m3 }}" required>
                                    <div class="form-text">Maximum: {{ invitation.private_shipment.get_available_space_m3|floatformat:1 }} m³</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="cargo_description" class="form-label">Cargo Description*</label>
                            <textarea class="form-control" id="cargo_description" name="cargo_description" 
                                      rows="3" required placeholder="Describe your cargo in detail..."></textarea>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="has_hazardous_items" name="has_hazardous_items">
                                    <label class="form-check-label" for="has_hazardous_items">
                                        Contains hazardous materials
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="special_requirements" class="form-label">Special Requirements</label>
                            <textarea class="form-control" id="special_requirements" name="special_requirements" 
                                      rows="2" placeholder="Any special handling requirements..."></textarea>
                        </div>
                        
                        <!-- Cost Calculation Display -->
                        <div class="alert alert-success" id="costCalculation" style="display: none;">
                            <h6><i class="fas fa-calculator me-2"></i>Cost Calculation</h6>
                            <div class="row">
                                <div class="col-md-4">
                                    <strong>Your Cost:</strong> $<span id="individualCost">0.00</span>
                                </div>
                                <div class="col-md-4">
                                    <strong>You Save:</strong> $<span id="costSavings">0.00</span>
                                </div>
                                <div class="col-md-4">
                                    <strong>Savings:</strong> <span id="savingsPercentage">30</span>%
                                </div>
                            </div>
                        </div>
                        
                        <input type="hidden" id="invitation_id" name="invitation_id" value="{{ invitation.id }}">
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <!-- Action Panel -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5><i class="fas fa-tasks me-2"></i>Actions</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-success btn-lg" onclick="acceptInvitation()">
                            <i class="fas fa-check me-2"></i>Accept Invitation
                        </button>
                        <button type="button" class="btn btn-outline-danger" onclick="declineInvitation()">
                            <i class="fas fa-times me-2"></i>Decline Invitation
                        </button>
                    </div>
                    
                    <div class="mt-3 text-center">
                        <small class="text-muted">
                            <i class="fas fa-clock me-1"></i>
                            Expires: {{ invitation.expires_at|date:"M d, Y H:i" }}
                        </small>
                    </div>
                </div>
            </div>

            <!-- Capacity Visualization -->
            <div class="card">
                <div class="card-header">
                    <h6><i class="fas fa-chart-pie me-2"></i>Container Capacity</h6>
                </div>
                <div class="card-body">
                    <div class="progress mb-2" style="height: 20px;">
                        <div class="progress-bar bg-primary" role="progressbar" 
                             style="width: {{ invitation.private_shipment.capacity_utilization_percentage }}%">
                            {{ invitation.private_shipment.capacity_utilization_percentage|floatformat:1 }}%
                        </div>
                    </div>
                    <small class="text-muted">
                        {{ invitation.private_shipment.current_participants }} of {{ invitation.private_shipment.max_participants }} participants
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Contract Agreement Modal -->
<div class="modal fade" id="contractModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-file-contract me-2"></i>Private Shipment Contract Agreement</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="contract-content" style="max-height: 400px; overflow-y: auto; border: 1px solid #dee2e6; padding: 20px; border-radius: 5px; background-color: #f8f9fa;">
                    <h6>PRIVATE SHIPMENT CONTAINER AGREEMENT</h6>
                    <p><strong>Container ID:</strong> {{ invitation.private_shipment.container_id }}</p>
                    <p><strong>Logistics Provider:</strong> {{ invitation.logistics_provider.company_name|default:invitation.logistics_provider.email }}</p>
                    <p><strong>Customer:</strong> {{ user.company_name|default:user.email }}</p>
                    
                    <h6 class="mt-3">TERMS AND CONDITIONS:</h6>
                    <ol>
                        <li><strong>Shared Container Space:</strong> You are reserving space in a shared container. Final space allocation is based on your declared cargo dimensions.</li>
                        <li><strong>Payment Terms:</strong> Payment is due within 48 hours of invitation acceptance. Failure to pay may result in forfeiture of reserved space.</li>
                        <li><strong>Cargo Responsibility:</strong> Customer is responsible for accurate cargo declaration including weight, dimensions, and hazardous material status.</li>
                        <li><strong>Capacity Management:</strong> If container reaches capacity, additional customers may be waitlisted. Priority is given to confirmed participants.</li>
                        <li><strong>Schedule Compliance:</strong> Cargo must be delivered to logistics provider by specified deadline. Late delivery may result in additional fees.</li>
                        <li><strong>International Shipping:</strong> Customer is responsible for customs declarations and compliance with import/export regulations.</li>
                        <li><strong>Insurance:</strong> Logistics provider's standard cargo insurance applies. Additional coverage available upon request.</li>
                        <li><strong>Cancellation Policy:</strong> Cancellations more than 72 hours before departure: 90% refund. Within 72 hours: 50% refund. Day of departure: No refund.</li>
                        <li><strong>Force Majeure:</strong> Neither party is liable for delays due to circumstances beyond reasonable control.</li>
                        <li><strong>Dispute Resolution:</strong> Disputes will be resolved through binding arbitration in accordance with applicable law.</li>
                    </ol>
                    
                    <div class="alert alert-warning mt-3">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i>Important Notice:</h6>
                        <p class="mb-0">By accepting this invitation, you agree to be bound by these terms and the logistics provider's standard shipping conditions. Please review all terms carefully before proceeding.</p>
                    </div>
                </div>
                
                <div class="form-check mt-3">
                    <input class="form-check-input" type="checkbox" id="contractAccepted">
                    <label class="form-check-label" for="contractAccepted">
                        <strong>I have read and agree to the contract terms and conditions</strong>
                    </label>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-success" id="confirmAcceptance" disabled onclick="processInvitationAcceptance()">
                    <i class="fas fa-check me-2"></i>Accept & Continue to Payment
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Auto-calculate costs when volume changes
document.getElementById('volume_m3').addEventListener('input', function() {
    calculateCosts();
});

function calculateCosts() {
    const volume = parseFloat(document.getElementById('volume_m3').value) || 0;
    const baseCostPerM3 = {{ invitation.private_shipment.base_cost_per_m3|floatformat:2 }};
    
    if (volume > 0) {
        const individualCost = volume * baseCostPerM3;
        const estimatedIndividualCost = individualCost * 1.3; // 30% more expensive if shipped alone
        const savings = estimatedIndividualCost - individualCost;
        
        document.getElementById('individualCost').textContent = individualCost.toFixed(2);
        document.getElementById('costSavings').textContent = savings.toFixed(2);
        document.getElementById('costCalculation').style.display = 'block';
    } else {
        document.getElementById('costCalculation').style.display = 'none';
    }
}

function acceptInvitation() {
    // Validate form
    const form = document.getElementById('invitationForm');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }
    
    // Show contract modal
    const contractModal = new bootstrap.Modal(document.getElementById('contractModal'));
    contractModal.show();
}

// Enable/disable accept button based on contract checkbox
document.getElementById('contractAccepted').addEventListener('change', function() {
    document.getElementById('confirmAcceptance').disabled = !this.checked;
});

function processInvitationAcceptance() {
    const formData = new FormData();
    formData.append('invitation_id', document.getElementById('invitation_id').value);
    formData.append('cargo_description', document.getElementById('cargo_description').value);
    formData.append('weight_kg', document.getElementById('weight_kg').value);
    formData.append('volume_m3', document.getElementById('volume_m3').value);
    formData.append('has_hazardous_items', document.getElementById('has_hazardous_items').checked);
    formData.append('special_requirements', document.getElementById('special_requirements').value);
    formData.append('contract_accepted', true);
    
    fetch('/api/private-shipment/accept-invitation', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            if (data.status === 'waitlisted') {
                Swal.fire({
                    title: 'Added to Waitlist',
                    text: data.message,
                    icon: 'info',
                    confirmButtonText: 'Understood'
                }).then(() => {
                    window.location.href = '/customer/dashboard';
                });
            } else {
                Swal.fire({
                    title: 'Invitation Accepted!',
                    html: `
                        <p>${data.message}</p>
                        <div class="alert alert-success mt-3">
                            <strong>Your Cost:</strong> $${data.individual_cost.toFixed(2)}<br>
                            <strong>You Save:</strong> $${data.cost_savings.toFixed(2)}<br>
                            <strong>Container:</strong> ${data.container_id}
                        </div>
                    `,
                    icon: 'success',
                    confirmButtonText: 'Proceed to Payment'
                }).then(() => {
                    window.location.href = '/customer/payment-form?type=private_shipment&invitation_id=' + document.getElementById('invitation_id').value;
                });
            }
        } else {
            Swal.fire('Error', data.error, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        Swal.fire('Error', 'Failed to process invitation. Please try again.', 'error');
    });
}

function declineInvitation() {
    Swal.fire({
        title: 'Decline Invitation?',
        text: 'Are you sure you want to decline this private shipment invitation?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#dc3545',
        confirmButtonText: 'Yes, Decline'
    }).then((result) => {
        if (result.isConfirmed) {
            // TODO: Implement decline functionality
            Swal.fire('Declined', 'You have declined this invitation.', 'info')
                .then(() => window.location.href = '/customer/dashboard');
        }
    });
}
</script>
{% endblock %}