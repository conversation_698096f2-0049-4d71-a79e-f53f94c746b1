<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="/static/base.css" rel="stylesheet">
    <style>
        .provider-scorecard {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            padding: 20px 0;
        }
        
        .scorecard-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            overflow: hidden;
        }
        
        .provider-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .overall-score {
            font-size: 3.5rem;
            font-weight: bold;
            margin: 15px 0;
        }
        
        .performance-tier {
            font-size: 1.3rem;
            margin-bottom: 15px;
        }
        
        .badges-display {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            justify-content: center;
            margin-top: 15px;
        }
        
        .score-badge {
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .metric-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            padding: 30px;
        }
        
        .metric-display {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            border: 1px solid #e9ecef;
        }
        
        .metric-icon {
            font-size: 2rem;
            margin-bottom: 10px;
        }
        
        .metric-score {
            font-size: 1.8rem;
            font-weight: bold;
            margin: 8px 0;
        }
        
        .metric-bar {
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .metric-fill {
            height: 100%;
            border-radius: 4px;
            transition: width 0.5s ease;
        }
        
        .provider-stats {
            background: #fff;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .stat-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .stat-row:last-child {
            border-bottom: none;
        }
        
        .contact-provider {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: 500;
            transition: transform 0.3s ease;
        }
        
        .contact-provider:hover {
            transform: translateY(-2px);
            color: white;
        }
        
        .back-button {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(255,255,255,0.2);
            border: 1px solid rgba(255,255,255,0.3);
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            text-decoration: none;
            transition: background 0.3s ease;
        }
        
        .back-button:hover {
            background: rgba(255,255,255,0.3);
            color: white;
        }
    </style>
</head>
<body>
    <div class="provider-scorecard">
        <div class="container">
            <!-- Provider Header -->
            <div class="row">
                <div class="col-12">
                    <div class="scorecard-card">
                        <div class="provider-header position-relative">
                            <a href="/dashboard" class="back-button">
                                <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
                            </a>
                            
                            <h1><i class="fas fa-building me-2"></i>{{ scorecard.provider_name }}</h1>
                            <p class="mb-3">Performance Analysis Report</p>
                            
                            <div class="overall-score">{{ "%.1f"|format(scorecard.overall_score) }}/5.0</div>
                            <div class="performance-tier">
                                {% set tier_colors = {
                                    'Excellent': 'success',
                                    'Good': 'primary', 
                                    'Average': 'warning',
                                    'Needs Improvement': 'danger'
                                } %}
                                <span class="badge bg-{{ tier_colors.get(scorecard.performance_tier, 'secondary') }} fs-6">
                                    {{ scorecard.performance_tier }}
                                </span>
                            </div>
                            
                            <!-- Performance Badges -->
                            {% if scorecard.badges %}
                            <div class="badges-display">
                                {% for badge in scorecard.badges %}
                                <span class="score-badge bg-{{ badge.color }}">
                                    <i class="fas fa-{{ badge.icon }}"></i>
                                    {{ badge.name }}
                                </span>
                                {% endfor %}
                            </div>
                            {% endif %}
                            
                            <div class="mt-4">
                                <button class="btn contact-provider" onclick="contactProvider()">
                                    <i class="fas fa-envelope me-2"></i>Contact Provider
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Performance Metrics -->
            <div class="row">
                <div class="col-12">
                    <div class="scorecard-card">
                        <div class="card-body">
                            <h3 class="mb-4"><i class="fas fa-chart-bar me-2"></i>Performance Metrics</h3>
                            <div class="metric-grid">
                                {% set metric_configs = {
                                    'on_time_delivery': {'icon': 'clock', 'color': '#28a745', 'name': 'On-Time Delivery'},
                                    'customer_rating': {'icon': 'star', 'color': '#ffc107', 'name': 'Customer Rating'},
                                    'response_time': {'icon': 'lightning', 'color': '#17a2b8', 'name': 'Response Time'},
                                    'completion_rate': {'icon': 'check-circle', 'color': '#007bff', 'name': 'Completion Rate'},
                                    'pricing_competitiveness': {'icon': 'dollar-sign', 'color': '#28a745', 'name': 'Pricing'},
                                    'communication_quality': {'icon': 'comments', 'color': '#6f42c1', 'name': 'Communication'},
                                    'reliability_score': {'icon': 'shield-alt', 'color': '#fd7e14', 'name': 'Reliability'}
                                } %}
                                
                                {% for metric_key, metric_data in scorecard.metrics.items() %}
                                {% set config = metric_configs.get(metric_key) %}
                                {% if config %}
                                <div class="metric-display">
                                    <div class="metric-icon" style="color: {{ config.color }}">
                                        <i class="fas fa-{{ config.icon }}"></i>
                                    </div>
                                    <h5>{{ config.name }}</h5>
                                    <div class="metric-score" style="color: {{ config.color }}">
                                        {{ "%.1f"|format(metric_data.score) }}/5.0
                                    </div>
                                    <div class="metric-bar">
                                        <div class="metric-fill" style="width: {{ (metric_data.score / 5 * 100)|round }}%; background-color: {{ config.color }};"></div>
                                    </div>
                                    <small class="text-muted">
                                        {% if metric_key == 'on_time_delivery' %}
                                            {{ metric_data.percentage }}% on-time delivery
                                        {% elif metric_key == 'customer_rating' %}
                                            Average: {{ "%.1f"|format(metric_data.rating) }}/5.0
                                        {% elif metric_key == 'response_time' %}
                                            Avg: {{ metric_data.average_hours }} hours
                                        {% elif metric_key == 'completion_rate' %}
                                            {{ metric_data.percentage }}% completion
                                        {% elif metric_key == 'pricing_competitiveness' %}
                                            {{ metric_data.market_position }}
                                        {% elif metric_key == 'communication_quality' %}
                                            Rating: {{ "%.1f"|format(metric_data.rating) }}/5.0
                                        {% elif metric_key == 'reliability_score' %}
                                            {{ metric_data.consistency_level }}
                                        {% endif %}
                                    </small>
                                </div>
                                {% endif %}
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Provider Statistics -->
            <div class="row">
                <div class="col-lg-6">
                    <div class="scorecard-card">
                        <div class="card-body">
                            <h4><i class="fas fa-chart-pie me-2"></i>Provider Statistics</h4>
                            <div class="provider-stats">
                                <div class="stat-row">
                                    <span><i class="fas fa-shipping-fast text-primary me-2"></i>Total Shipments</span>
                                    <strong>{{ scorecard.statistics.total_shipments }}</strong>
                                </div>
                                <div class="stat-row">
                                    <span><i class="fas fa-quote-left text-success me-2"></i>Quote Requests</span>
                                    <strong>{{ scorecard.statistics.total_quotes }}</strong>
                                </div>
                                <div class="stat-row">
                                    <span><i class="fas fa-route text-warning me-2"></i>Active Routes</span>
                                    <strong>{{ scorecard.statistics.active_routes }}</strong>
                                </div>
                                <div class="stat-row">
                                    <span><i class="fas fa-users text-info me-2"></i>Customers Served</span>
                                    <strong>{{ scorecard.statistics.customer_count }}</strong>
                                </div>
                                <div class="stat-row">
                                    <span><i class="fas fa-dollar-sign text-success me-2"></i>Revenue Generated</span>
                                    <strong>${{ "{:,.2f}"|format(scorecard.statistics.revenue_generated) }}</strong>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-6">
                    <div class="scorecard-card">
                        <div class="card-body">
                            <h4><i class="fas fa-trend-up me-2"></i>Performance Trends</h4>
                            {% if scorecard.trends %}
                            <div class="provider-stats">
                                <div class="stat-row">
                                    <span>Last 30 Days Rating</span>
                                    <strong>{{ "%.1f"|format(scorecard.trends.last_30_days.average_rating) }}/5.0</strong>
                                </div>
                                <div class="stat-row">
                                    <span>Last 60 Days Rating</span>
                                    <strong>{{ "%.1f"|format(scorecard.trends.last_60_days.average_rating) }}/5.0</strong>
                                </div>
                                <div class="stat-row">
                                    <span>Last 90 Days Rating</span>
                                    <strong>{{ "%.1f"|format(scorecard.trends.last_90_days.average_rating) }}/5.0</strong>
                                </div>
                                <div class="stat-row">
                                    <span>Performance Trend</span>
                                    <strong>
                                        {% if scorecard.trends.trend_analysis.rating_trend == 'improving' %}
                                            <span class="text-success"><i class="fas fa-arrow-up"></i> Improving</span>
                                        {% elif scorecard.trends.trend_analysis.rating_trend == 'declining' %}
                                            <span class="text-danger"><i class="fas fa-arrow-down"></i> Declining</span>
                                        {% else %}
                                            <span class="text-primary"><i class="fas fa-minus"></i> Stable</span>
                                        {% endif %}
                                    </strong>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Analysis Period Info -->
            <div class="row">
                <div class="col-12">
                    <div class="scorecard-card">
                        <div class="card-body text-center">
                            <p class="text-muted mb-0">
                                <i class="fas fa-calendar-alt me-2"></i>
                                Analysis Period: {{ scorecard.period_days }} days 
                                | Generated: {{ scorecard.calculation_date[:10] }}
                                | Last Updated: {{ scorecard.calculation_date[11:16] }}
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        function contactProvider() {
            Swal.fire({
                title: 'Contact {{ scorecard.provider_name }}',
                html: `
                    <div class="text-start">
                        <p><strong>Request a Quote:</strong></p>
                        <p>Click below to request a personalized quote from this provider:</p>
                        <a href="/search-shipping" class="btn btn-primary w-100 mb-3">
                            <i class="fas fa-search me-2"></i>Search & Request Quote
                        </a>
                        
                        <p><strong>View Routes:</strong></p>
                        <p>See all available routes from this provider:</p>
                        <a href="/search-shipping" class="btn btn-outline-primary w-100 mb-3">
                            <i class="fas fa-route me-2"></i>View Available Routes
                        </a>
                        
                        <p><strong>Direct Contact:</strong></p>
                        <p class="text-muted small">For direct communication, use the messaging system after requesting a quote.</p>
                    </div>
                `,
                showConfirmButton: false,
                showCloseButton: true,
                width: '500px'
            });
        }

        // Animate progress bars on load
        document.addEventListener('DOMContentLoaded', function() {
            const progressBars = document.querySelectorAll('.metric-fill');
            
            setTimeout(() => {
                progressBars.forEach(bar => {
                    const width = bar.style.width;
                    bar.style.width = '0%';
                    setTimeout(() => {
                        bar.style.width = width;
                    }, 100);
                });
            }, 500);
        });
    </script>
</body>
</html>