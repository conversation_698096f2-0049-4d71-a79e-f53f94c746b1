{% extends "base.html" %}

{% block title %}Cloverics Logistics Index - Market Intelligence{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-1">
                        <i class="fas fa-chart-line text-primary me-2"></i>
                        Cloverics Logistics Index (CLI)
                    </h1>
                    <p class="text-muted mb-0">Real-time global logistics market intelligence and benchmarking</p>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-primary" onclick="refreshIndex()">
                        <i class="fas fa-sync-alt me-1"></i> Refresh
                    </button>
                    <button class="btn btn-primary" onclick="downloadReport()">
                        <i class="fas fa-download me-1"></i> Download Report
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- CLI Overview Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="display-6 text-primary mb-2" id="cli-global-index">
                        <div class="spinner-border spinner-border-sm" role="status"></div>
                    </div>
                    <h6 class="card-title mb-1">Global CLI</h6>
                    <p class="text-muted small mb-0">Current index value</p>
                    <span class="badge bg-success">****% today</span>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="display-6 text-info mb-2" id="cli-regional-europe">
                        <div class="spinner-border spinner-border-sm" role="status"></div>
                    </div>
                    <h6 class="card-title mb-1">Europe CLI</h6>
                    <p class="text-muted small mb-0">Regional index</p>
                    <span class="badge bg-warning">+1.8% today</span>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="display-6 text-warning mb-2" id="cli-regional-asia">
                        <div class="spinner-border spinner-border-sm" role="status"></div>
                    </div>
                    <h6 class="card-title mb-1">Asia CLI</h6>
                    <p class="text-muted small mb-0">Regional index</p>
                    <span class="badge bg-danger">-0.5% today</span>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="display-6 text-success mb-2" id="cli-volatility">
                        <div class="spinner-border spinner-border-sm" role="status"></div>
                    </div>
                    <h6 class="card-title mb-1">Volatility Index</h6>
                    <p class="text-muted small mb-0">Market stability</p>
                    <span class="badge bg-info">Moderate</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Index Tabs -->
    <ul class="nav nav-tabs" id="indexTab" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link active" id="overview-tab" data-bs-toggle="tab" data-bs-target="#overview" type="button" role="tab">
                <i class="fas fa-chart-area me-1"></i> Index Overview
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="regional-tab" data-bs-toggle="tab" data-bs-target="#regional" type="button" role="tab">
                <i class="fas fa-globe me-1"></i> Regional Analysis
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="transport-modes-tab" data-bs-toggle="tab" data-bs-target="#transport-modes" type="button" role="tab">
                <i class="fas fa-truck me-1"></i> Transport Modes
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="trade-lanes-tab" data-bs-toggle="tab" data-bs-target="#trade-lanes" type="button" role="tab">
                <i class="fas fa-route me-1"></i> Major Trade Lanes
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="market-insights-tab" data-bs-toggle="tab" data-bs-target="#market-insights" type="button" role="tab">
                <i class="fas fa-lightbulb me-1"></i> Market Insights
            </button>
        </li>
    </ul>

    <div class="tab-content" id="indexTabContent">
        <!-- Overview Tab -->
        <div class="tab-pane fade show active" id="overview" role="tabpanel">
            <div class="py-4">
                <div class="row">
                    <div class="col-lg-8">
                        <div class="card border-0 shadow-sm">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0">
                                    <i class="fas fa-chart-line me-2"></i>
                                    CLI Historical Trend (30 Days)
                                </h5>
                            </div>
                            <div class="card-body">
                                <canvas id="cli-trend-chart" style="height: 400px;"></canvas>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4">
                        <div class="card border-0 shadow-sm mb-3">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">Index Components</h6>
                            </div>
                            <div class="card-body">
                                <div class="d-flex justify-content-between mb-2">
                                    <span>Ocean Freight</span>
                                    <span class="fw-bold">35%</span>
                                </div>
                                <div class="progress mb-3" style="height: 5px;">
                                    <div class="progress-bar bg-primary" style="width: 35%"></div>
                                </div>
                                
                                <div class="d-flex justify-content-between mb-2">
                                    <span>Air Freight</span>
                                    <span class="fw-bold">25%</span>
                                </div>
                                <div class="progress mb-3" style="height: 5px;">
                                    <div class="progress-bar bg-info" style="width: 25%"></div>
                                </div>
                                
                                <div class="d-flex justify-content-between mb-2">
                                    <span>Road Transport</span>
                                    <span class="fw-bold">30%</span>
                                </div>
                                <div class="progress mb-3" style="height: 5px;">
                                    <div class="progress-bar bg-warning" style="width: 30%"></div>
                                </div>
                                
                                <div class="d-flex justify-content-between mb-2">
                                    <span>Rail Freight</span>
                                    <span class="fw-bold">10%</span>
                                </div>
                                <div class="progress mb-3" style="height: 5px;">
                                    <div class="progress-bar bg-success" style="width: 10%"></div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="card border-0 shadow-sm">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">Market Indicators</h6>
                            </div>
                            <div class="card-body">
                                <div class="d-flex justify-content-between mb-2">
                                    <span>Fuel Price Impact</span>
                                    <span class="badge bg-warning">Medium</span>
                                </div>
                                <div class="d-flex justify-content-between mb-2">
                                    <span>Supply Chain Pressure</span>
                                    <span class="badge bg-info">Low</span>
                                </div>
                                <div class="d-flex justify-content-between mb-2">
                                    <span>Demand Outlook</span>
                                    <span class="badge bg-success">Positive</span>
                                </div>
                                <div class="d-flex justify-content-between mb-2">
                                    <span>Capacity Utilization</span>
                                    <span class="badge bg-primary">78%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Regional Analysis Tab -->
        <div class="tab-pane fade" id="regional" role="tabpanel">
            <div class="py-4">
                <div class="row">
                    <div class="col-lg-6">
                        <div class="card border-0 shadow-sm">
                            <div class="card-header bg-info text-white">
                                <h5 class="mb-0">Regional CLI Comparison</h5>
                            </div>
                            <div class="card-body">
                                <canvas id="regional-chart" style="height: 300px;"></canvas>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="card border-0 shadow-sm">
                            <div class="card-header bg-light">
                                <h5 class="mb-0">Regional Performance</h5>
                            </div>
                            <div class="card-body">
                                <table class="table table-borderless">
                                    <thead>
                                        <tr>
                                            <th>Region</th>
                                            <th>Current CLI</th>
                                            <th>Change</th>
                                            <th>Outlook</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><strong>Europe</strong></td>
                                            <td>985.2</td>
                                            <td class="text-success">+1.8%</td>
                                            <td><span class="badge bg-success">Positive</span></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Asia-Pacific</strong></td>
                                            <td>978.5</td>
                                            <td class="text-danger">-0.5%</td>
                                            <td><span class="badge bg-warning">Neutral</span></td>
                                        </tr>
                                        <tr>
                                            <td><strong>North America</strong></td>
                                            <td>1,012.3</td>
                                            <td class="text-success">****%</td>
                                            <td><span class="badge bg-success">Positive</span></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Middle East</strong></td>
                                            <td>956.7</td>
                                            <td class="text-success">+0.9%</td>
                                            <td><span class="badge bg-info">Stable</span></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Transport Modes Tab -->
        <div class="tab-pane fade" id="transport-modes" role="tabpanel">
            <div class="py-4">
                <div class="row">
                    <div class="col-12">
                        <div class="card border-0 shadow-sm">
                            <div class="card-header bg-warning text-dark">
                                <h5 class="mb-0">Transport Mode Performance</h5>
                            </div>
                            <div class="card-body">
                                <canvas id="transport-modes-chart" style="height: 400px;"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row mt-4">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card border-0 shadow-sm text-center">
                            <div class="card-body">
                                <i class="fas fa-ship fa-2x text-primary mb-2"></i>
                                <h6>Ocean Freight</h6>
                                <p class="text-muted small">Index: 1,025.4</p>
                                <span class="badge bg-success">+3.2%</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card border-0 shadow-sm text-center">
                            <div class="card-body">
                                <i class="fas fa-plane fa-2x text-info mb-2"></i>
                                <h6>Air Freight</h6>
                                <p class="text-muted small">Index: 987.1</p>
                                <span class="badge bg-warning">+1.1%</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card border-0 shadow-sm text-center">
                            <div class="card-body">
                                <i class="fas fa-truck fa-2x text-warning mb-2"></i>
                                <h6>Road Transport</h6>
                                <p class="text-muted small">Index: 976.8</p>
                                <span class="badge bg-danger">-0.8%</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card border-0 shadow-sm text-center">
                            <div class="card-body">
                                <i class="fas fa-train fa-2x text-success mb-2"></i>
                                <h6>Rail Freight</h6>
                                <p class="text-muted small">Index: 994.5</p>
                                <span class="badge bg-success">+2.7%</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Trade Lanes Tab -->
        <div class="tab-pane fade" id="trade-lanes" role="tabpanel">
            <div class="py-4">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">Major Trade Lane Performance</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Trade Lane</th>
                                        <th>Current CLI</th>
                                        <th>Volume Index</th>
                                        <th>Price Trend</th>
                                        <th>Capacity</th>
                                        <th>Performance</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td><strong>Asia-Europe</strong></td>
                                        <td>1,045.2</td>
                                        <td>892.3</td>
                                        <td class="text-success">↗ ****%</td>
                                        <td>85%</td>
                                        <td><span class="badge bg-success">Excellent</span></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Trans-Pacific</strong></td>
                                        <td>1,012.7</td>
                                        <td>976.5</td>
                                        <td class="text-success">↗ ****%</td>
                                        <td>78%</td>
                                        <td><span class="badge bg-success">Good</span></td>
                                    </tr>
                                    <tr>
                                        <td><strong>North America-Europe</strong></td>
                                        <td>987.4</td>
                                        <td>823.1</td>
                                        <td class="text-warning">→ +0.5%</td>
                                        <td>72%</td>
                                        <td><span class="badge bg-warning">Average</span></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Middle East-Asia</strong></td>
                                        <td>965.8</td>
                                        <td>756.2</td>
                                        <td class="text-danger">↘ -1.2%</td>
                                        <td>69%</td>
                                        <td><span class="badge bg-warning">Monitor</span></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Intra-Europe</strong></td>
                                        <td>1,025.3</td>
                                        <td>934.7</td>
                                        <td class="text-success">↗ +3.8%</td>
                                        <td>82%</td>
                                        <td><span class="badge bg-success">Strong</span></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Market Insights Tab -->
        <div class="tab-pane fade" id="market-insights" role="tabpanel">
            <div class="py-4">
                <div class="row">
                    <div class="col-lg-8">
                        <div class="card border-0 shadow-sm">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0">AI-Powered Market Insights</h5>
                            </div>
                            <div class="card-body">
                                <div id="market-insights-content">
                                    <div class="insight-item mb-4 p-3 bg-light rounded">
                                        <div class="d-flex align-items-start">
                                            <i class="fas fa-lightbulb text-warning fa-lg me-3 mt-1"></i>
                                            <div>
                                                <h6 class="fw-bold mb-2">Peak Season Alert</h6>
                                                <p class="mb-2">Ocean freight rates on Asia-Europe trade lanes are expected to increase by 15-20% in the next 4 weeks due to seasonal demand and vessel capacity constraints.</p>
                                                <small class="text-muted">Confidence: 87% | Impact: High</small>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="insight-item mb-4 p-3 bg-light rounded">
                                        <div class="d-flex align-items-start">
                                            <i class="fas fa-trend-up text-success fa-lg me-3 mt-1"></i>
                                            <div>
                                                <h6 class="fw-bold mb-2">Rail Freight Opportunity</h6>
                                                <p class="mb-2">Europe-Asia rail freight showing strong performance with 2.7% growth. Consider rail alternatives for time-sensitive cargo to optimize costs.</p>
                                                <small class="text-muted">Confidence: 92% | Impact: Medium</small>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="insight-item mb-4 p-3 bg-light rounded">
                                        <div class="d-flex align-items-start">
                                            <i class="fas fa-exclamation-triangle text-danger fa-lg me-3 mt-1"></i>
                                            <div>
                                                <h6 class="fw-bold mb-2">Supply Chain Risk</h6>
                                                <p class="mb-2">Middle East-Asia corridor showing declining performance (-1.2%). Monitor alternative routing options and consider diversification strategies.</p>
                                                <small class="text-muted">Confidence: 78% | Impact: Medium</small>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="insight-item mb-4 p-3 bg-light rounded">
                                        <div class="d-flex align-items-start">
                                            <i class="fas fa-chart-line text-info fa-lg me-3 mt-1"></i>
                                            <div>
                                                <h6 class="fw-bold mb-2">Cost Optimization</h6>
                                                <p class="mb-2">Current market conditions favor long-term contracts for North America-Europe routes with potential savings of 8-12% compared to spot rates.</p>
                                                <small class="text-muted">Confidence: 84% | Impact: High</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-4">
                        <div class="card border-0 shadow-sm mb-3">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">Weekly CLI Report</h6>
                            </div>
                            <div class="card-body">
                                <p class="small text-muted mb-3">Get comprehensive weekly market analysis delivered to your inbox</p>
                                <button class="btn btn-primary btn-sm w-100 mb-2">
                                    <i class="fas fa-envelope me-1"></i> Subscribe to Reports
                                </button>
                                <button class="btn btn-outline-primary btn-sm w-100">
                                    <i class="fas fa-download me-1"></i> Download Sample
                                </button>
                            </div>
                        </div>
                        
                        <div class="card border-0 shadow-sm">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">Market Calendar</h6>
                            </div>
                            <div class="card-body">
                                <div class="d-flex justify-content-between mb-2">
                                    <span class="small">Peak Season Start</span>
                                    <span class="small fw-bold">Jul 15</span>
                                </div>
                                <div class="d-flex justify-content-between mb-2">
                                    <span class="small">Golden Week (China)</span>
                                    <span class="small fw-bold">Oct 1-7</span>
                                </div>
                                <div class="d-flex justify-content-between mb-2">
                                    <span class="small">Year-end Rush</span>
                                    <span class="small fw-bold">Dec 1-15</span>
                                </div>
                                <div class="d-flex justify-content-between">
                                    <span class="small">Chinese New Year</span>
                                    <span class="small fw-bold">Feb 10-17</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Chart.js for visualizations -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    loadCLIData();
    initializeCharts();
});

// Load CLI data
async function loadCLIData() {
    try {
        const response = await fetch('/api/cli/current-index');
        const data = await response.json();
        
        if (data.success) {
            updateCLICards(data);
        } else {
            // Use fallback data
            updateCLICards({
                global_cli: 992.8,
                regional: {
                    europe: 985.2,
                    asia: 978.5,
                    north_america: 1012.3
                },
                volatility: 12.4
            });
        }
    } catch (error) {
        console.log('Using cached CLI data');
        updateCLICards({
            global_cli: 992.8,
            regional: {
                europe: 985.2,
                asia: 978.5,
                north_america: 1012.3
            },
            volatility: 12.4
        });
    }
}

function updateCLICards(data) {
    document.getElementById('cli-global-index').textContent = data.global_cli || '992.8';
    document.getElementById('cli-regional-europe').textContent = data.regional?.europe || '985.2';
    document.getElementById('cli-regional-asia').textContent = data.regional?.asia || '978.5';
    document.getElementById('cli-volatility').textContent = (data.volatility || 12.4) + '%';
}

// Initialize charts
function initializeCharts() {
    initCLITrendChart();
    initRegionalChart();
    initTransportModesChart();
}

function initCLITrendChart() {
    const ctx = document.getElementById('cli-trend-chart');
    if (ctx) {
        try {
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['30d ago', '25d ago', '20d ago', '15d ago', '10d ago', '5d ago', 'Today'],
                    datasets: [{
                        label: 'Global CLI',
                        data: [975.2, 982.1, 989.5, 985.3, 990.7, 988.2, 992.8],
                        borderColor: '#0d6efd',
                        backgroundColor: 'rgba(13, 110, 253, 0.1)',
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: false,
                            min: 970
                        }
                    }
                }
            });
        } catch (error) {
            console.error('CLI trend chart error:', error);
            ctx.innerHTML = '<div class="text-center p-4"><div class="alert alert-info">CLI trend: 992.8 (****% from 30 days ago)</div></div>';
        }
    }
}

function initRegionalChart() {
    const ctx = document.getElementById('regional-chart');
    if (ctx) {
        try {
            new Chart(ctx, {
                type: 'radar',
                data: {
                    labels: ['Europe', 'Asia-Pacific', 'North America', 'Middle East', 'Africa', 'Latin America'],
                    datasets: [{
                        label: 'Regional CLI',
                        data: [985.2, 978.5, 1012.3, 956.7, 934.1, 967.8],
                        borderColor: '#17a2b8',
                        backgroundColor: 'rgba(23, 162, 184, 0.2)',
                        pointBackgroundColor: '#17a2b8'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        r: {
                            beginAtZero: false,
                            min: 900,
                            max: 1050
                        }
                    }
                }
            });
        } catch (error) {
            console.error('Regional chart error:', error);
            ctx.innerHTML = '<div class="text-center p-4"><div class="alert alert-info">Regional performance data available in table below</div></div>';
        }
    }
}

function initTransportModesChart() {
    const ctx = document.getElementById('transport-modes-chart');
    if (ctx) {
        try {
            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['Ocean Freight', 'Air Freight', 'Road Transport', 'Rail Freight'],
                    datasets: [{
                        label: 'Current Index',
                        data: [1025.4, 987.1, 976.8, 994.5],
                        backgroundColor: ['#0d6efd', '#17a2b8', '#ffc107', '#198754']
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: false,
                            min: 950
                        }
                    }
                }
            });
        } catch (error) {
            console.error('Transport modes chart error:', error);
            ctx.innerHTML = '<div class="text-center p-4"><div class="alert alert-info">Transport mode performance shown in cards below</div></div>';
        }
    }
}

// Utility functions
function refreshIndex() {
    loadCLIData();
    // Reinitialize charts
    setTimeout(() => {
        initializeCharts();
    }, 500);
}

function downloadReport() {
    // Create a comprehensive CLI report
    const reportData = {
        report_type: 'cli_weekly',
        date: new Date().toISOString().split('T')[0],
        global_cli: 992.8,
        regional_data: true,
        trade_lanes: true,
        insights: true
    };
    
    // Trigger download
    const blob = new Blob([JSON.stringify(reportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `CLI_Report_${new Date().toISOString().split('T')[0]}.json`;
    a.click();
    URL.revokeObjectURL(url);
}
</script>
{% endblock %}