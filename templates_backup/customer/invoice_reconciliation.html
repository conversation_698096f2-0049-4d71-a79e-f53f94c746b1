{% extends "base.html" %}
{% set active_page = "invoice_reconciliation" %}

{% block title %}Invoice Audit & Reconciliation - Cloverics{% endblock %}

{% block extra_css %}
<style>
    .invoice-card {
        border: 1px solid #e5e5e5;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
        background: white;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .invoice-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 15px;
        border-bottom: 1px solid #e5e5e5;
    }
    
    .status-badge {
        padding: 5px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 600;
        text-transform: uppercase;
    }
    
    .status-pending { background-color: #fff3cd; color: #856404; }
    .status-reconciled { background-color: #d4edda; color: #155724; }
    .status-discrepancy { background-color: #f8d7da; color: #721c24; }
    .status-approved { background-color: #d1ecf1; color: #0c5460; }
    .status-disputed { background-color: #f5c6cb; color: #721c24; }
    
    .confidence-score {
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 11px;
        font-weight: 600;
    }
    
    .confidence-high { background-color: #d4edda; color: #155724; }
    .confidence-medium { background-color: #fff3cd; color: #856404; }
    .confidence-low { background-color: #f8d7da; color: #721c24; }
    
    .discrepancy-item {
        background: #f8f9fa;
        border-left: 4px solid #dc3545;
        padding: 10px;
        margin: 5px 0;
        border-radius: 4px;
    }
    
    .discrepancy-critical { border-left-color: #dc3545; }
    .discrepancy-high { border-left-color: #fd7e14; }
    .discrepancy-medium { border-left-color: #ffc107; }
    .discrepancy-low { border-left-color: #28a745; }
    
    .analytics-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 20px;
    }
    
    .analytics-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin-top: 15px;
    }
    
    .analytics-item {
        text-align: center;
        padding: 15px;
        background: rgba(255,255,255,0.1);
        border-radius: 8px;
    }
    
    .analytics-number {
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 5px;
    }
    
    .analytics-label {
        font-size: 12px;
        opacity: 0.9;
    }
    
    .action-buttons {
        display: flex;
        gap: 10px;
        margin-top: 15px;
        flex-wrap: wrap;
    }
    
    .btn-reconcile {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        color: white;
    }
    
    .btn-reconcile:hover {
        background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
        color: white;
    }
    
    .filter-section {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        margin-bottom: 20px;
    }
    
    .invoice-form {
        background: white;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin-bottom: 20px;
    }
    
    .recommendation-list {
        background: #e8f4fd;
        border: 1px solid #b8daff;
        border-radius: 6px;
        padding: 15px;
        margin-top: 10px;
    }
    
    .recommendation-item {
        margin: 5px 0;
        font-size: 13px;
        color: #0056b3;
    }
    
    .audit-trail {
        background: #f8f9fa;
        border-radius: 6px;
        padding: 15px;
        margin-top: 15px;
    }
    
    .audit-entry {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid #e5e5e5;
    }
    
    .audit-entry:last-child {
        border-bottom: none;
    }
    
    .carrier-performance {
        background: white;
        border-radius: 8px;
        padding: 15px;
        margin: 10px 0;
        border: 1px solid #e5e5e5;
    }
    
    .progress-bar {
        width: 100%;
        height: 8px;
        background: #e9ecef;
        border-radius: 4px;
        overflow: hidden;
        margin: 5px 0;
    }
    
    .progress-fill {
        height: 100%;
        transition: width 0.3s ease;
    }
    
    .tab-content {
        padding: 20px 0;
    }
    
    .modal-body {
        max-height: 400px;
        overflow-y: auto;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <h2><i class="fas fa-file-invoice-dollar"></i> Invoice Audit & Reconciliation</h2>
                <p>Automated invoice matching and reconciliation with audit trails</p>
            </div>
        </div>
    </div>

    <!-- Analytics Overview -->
    <div class="row">
        <div class="col-12">
            <div class="analytics-card">
                <h4><i class="fas fa-chart-line"></i> Reconciliation Analytics</h4>
                <div class="analytics-grid" id="analyticsGrid">
                    <div class="analytics-item">
                        <div class="analytics-number" id="totalInvoices">0</div>
                        <div class="analytics-label">Total Invoices</div>
                    </div>
                    <div class="analytics-item">
                        <div class="analytics-number" id="autoMatched">0</div>
                        <div class="analytics-label">Auto Matched</div>
                    </div>
                    <div class="analytics-item">
                        <div class="analytics-number" id="pendingReview">0</div>
                        <div class="analytics-label">Pending Review</div>
                    </div>
                    <div class="analytics-item">
                        <div class="analytics-number" id="averageConfidence">0%</div>
                        <div class="analytics-label">Avg Confidence</div>
                    </div>
                    <div class="analytics-item">
                        <div class="analytics-number" id="discrepancyRate">0%</div>
                        <div class="analytics-label">Discrepancy Rate</div>
                    </div>
                    <div class="analytics-item">
                        <div class="analytics-number" id="totalAmount">$0</div>
                        <div class="analytics-label">Total Amount</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Interface -->
    <div class="row">
        <div class="col-12">
            <div class="invoice-card">
                <!-- Tab Navigation -->
                <ul class="nav nav-tabs" id="invoiceTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="invoices-tab" data-bs-toggle="tab" data-bs-target="#invoices" type="button" role="tab">
                            <i class="fas fa-list"></i> Invoice Records
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="create-tab" data-bs-toggle="tab" data-bs-target="#create" type="button" role="tab">
                            <i class="fas fa-plus"></i> Create Invoice
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="analytics-tab" data-bs-toggle="tab" data-bs-target="#analytics" type="button" role="tab">
                            <i class="fas fa-chart-bar"></i> Detailed Analytics
                        </button>
                    </li>
                </ul>

                <div class="tab-content" id="invoiceTabContent">
                    <!-- Invoice Records Tab -->
                    <div class="tab-pane fade show active" id="invoices" role="tabpanel">
                        <!-- Filter Section -->
                        <div class="filter-section">
                            <div class="row">
                                <div class="col-md-3">
                                    <label for="statusFilter" class="form-label">Status Filter</label>
                                    <select class="form-select" id="statusFilter">
                                        <option value="">All Statuses</option>
                                        <option value="pending_reconciliation">Pending</option>
                                        <option value="reconciled">Reconciled</option>
                                        <option value="discrepancy">Discrepancy</option>
                                        <option value="approved">Approved</option>
                                        <option value="disputed">Disputed</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label for="carrierFilter" class="form-label">Carrier</label>
                                    <input type="text" class="form-control" id="carrierFilter" placeholder="Filter by carrier">
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">&nbsp;</label>
                                    <button class="btn btn-primary d-block" onclick="applyFilters()">
                                        <i class="fas fa-filter"></i> Apply
                                    </button>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">&nbsp;</label>
                                    <button class="btn btn-outline-secondary d-block" onclick="refreshInvoices()">
                                        <i class="fas fa-refresh"></i> Refresh
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Invoice List -->
                        <div id="invoicesList">
                            <div class="text-center p-4">
                                <i class="fas fa-spinner fa-spin fa-2x text-muted"></i>
                                <p class="mt-2 text-muted">Loading invoices...</p>
                            </div>
                        </div>
                    </div>

                    <!-- Create Invoice Tab -->
                    <div class="tab-pane fade" id="create" role="tabpanel">
                        <div class="invoice-form">
                            <h5><i class="fas fa-plus-circle"></i> Create New Invoice Record</h5>
                            <form id="createInvoiceForm">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="invoiceNumber" class="form-label">Invoice Number *</label>
                                            <input type="text" class="form-control" id="invoiceNumber" name="invoice_number" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="amount" class="form-label">Amount *</label>
                                            <div class="input-group">
                                                <span class="input-group-text">$</span>
                                                <input type="number" class="form-control" id="amount" name="amount" step="0.01" required>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="carrierName" class="form-label">Carrier Name *</label>
                                            <input type="text" class="form-control" id="carrierName" name="carrier_name" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="serviceDate" class="form-label">Service Date *</label>
                                            <input type="date" class="form-control" id="serviceDate" name="service_date" required>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="trackingNumber" class="form-label">Tracking Number</label>
                                            <input type="text" class="form-control" id="trackingNumber" name="tracking_number">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="shipmentReference" class="form-label">Shipment Reference</label>
                                            <input type="text" class="form-control" id="shipmentReference" name="shipment_reference">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="serviceType" class="form-label">Service Type</label>
                                            <select class="form-select" id="serviceType" name="service_type">
                                                <option value="">Select service type</option>
                                                <option value="express">Express</option>
                                                <option value="standard">Standard</option>
                                                <option value="economy">Economy</option>
                                                <option value="freight">Freight</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="origin" class="form-label">Origin</label>
                                            <input type="text" class="form-control" id="origin" name="origin" placeholder="City, Country">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="destination" class="form-label">Destination</label>
                                            <input type="text" class="form-control" id="destination" name="destination" placeholder="City, Country">
                                        </div>
                                    </div>
                                </div>
                                <div class="text-end">
                                    <button type="button" class="btn btn-outline-secondary me-2" onclick="resetForm()">
                                        <i class="fas fa-undo"></i> Reset
                                    </button>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i> Create Invoice Record
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Analytics Tab -->
                    <div class="tab-pane fade" id="analytics" role="tabpanel">
                        <div id="detailedAnalytics">
                            <div class="text-center p-4">
                                <i class="fas fa-spinner fa-spin fa-2x text-muted"></i>
                                <p class="mt-2 text-muted">Loading detailed analytics...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Invoice Details Modal -->
<div class="modal fade" id="invoiceDetailsModal" tabindex="-1" aria-labelledby="invoiceDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="invoiceDetailsModalLabel">Invoice Details & Reconciliation</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="invoiceDetailsContent">
                <!-- Content loaded dynamically -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Approval/Dispute Modal -->
<div class="modal fade" id="actionModal" tabindex="-1" aria-labelledby="actionModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="actionModalLabel">Invoice Action</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="actionForm">
                    <input type="hidden" id="actionInvoiceId" name="invoice_id">
                    <input type="hidden" id="actionType" name="action_type">
                    <div class="mb-3">
                        <label for="actionNotes" class="form-label" id="actionNotesLabel">Notes</label>
                        <textarea class="form-control" id="actionNotes" name="notes" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="confirmActionBtn" onclick="confirmAction()">Confirm</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentInvoices = [];
let currentAnalytics = {};

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    loadAnalytics();
    loadInvoices();
    
    // Form submission
    document.getElementById('createInvoiceForm').addEventListener('submit', function(e) {
        e.preventDefault();
        createInvoice();
    });
});

// Load analytics data
async function loadAnalytics() {
    try {
        const response = await fetch('/api/invoice/analytics');
        const data = await response.json();
        
        if (data.success) {
            currentAnalytics = data.analytics;
            updateAnalyticsDisplay(data.analytics);
        }
    } catch (error) {
        console.error('Error loading analytics:', error);
    }
}

// Update analytics display
function updateAnalyticsDisplay(analytics) {
    document.getElementById('totalInvoices').textContent = analytics.total_invoices || 0;
    document.getElementById('autoMatched').textContent = analytics.auto_matched || 0;
    document.getElementById('pendingReview').textContent = analytics.manual_review || 0;
    document.getElementById('averageConfidence').textContent = Math.round((analytics.average_confidence_score || 0) * 100) + '%';
    document.getElementById('discrepancyRate').textContent = Math.round(analytics.discrepancy_rate || 0) + '%';
    document.getElementById('totalAmount').textContent = '$' + (analytics.total_amount || 0).toLocaleString();
}

// Load invoices
async function loadInvoices(statusFilter = null) {
    try {
        const url = statusFilter ? `/api/invoice/list?status_filter=${statusFilter}` : '/api/invoice/list';
        const response = await fetch(url);
        const data = await response.json();
        
        if (data.success) {
            currentInvoices = data.invoices;
            displayInvoices(data.invoices);
        } else {
            document.getElementById('invoicesList').innerHTML = `
                <div class="text-center p-4">
                    <i class="fas fa-exclamation-triangle fa-2x text-warning"></i>
                    <p class="mt-2 text-muted">Error loading invoices: ${data.error}</p>
                </div>
            `;
        }
    } catch (error) {
        console.error('Error loading invoices:', error);
        document.getElementById('invoicesList').innerHTML = `
            <div class="text-center p-4">
                <i class="fas fa-exclamation-triangle fa-2x text-danger"></i>
                <p class="mt-2 text-muted">Error loading invoices</p>
            </div>
        `;
    }
}

// Display invoices
function displayInvoices(invoices) {
    const container = document.getElementById('invoicesList');
    
    if (!invoices || invoices.length === 0) {
        container.innerHTML = `
            <div class="text-center p-4">
                <i class="fas fa-inbox fa-2x text-muted"></i>
                <p class="mt-2 text-muted">No invoices found</p>
                <button class="btn btn-primary" onclick="document.getElementById('create-tab').click()">
                    <i class="fas fa-plus"></i> Create First Invoice
                </button>
            </div>
        `;
        return;
    }
    
    let html = '';
    
    invoices.forEach(invoice => {
        const statusClass = `status-${invoice.status.replace('_', '')}`;
        const reconciliationResult = invoice.reconciliation_result || {};
        const confidenceScore = reconciliationResult.confidence_score || 0;
        const confidenceClass = confidenceScore >= 0.8 ? 'confidence-high' : 
                               confidenceScore >= 0.5 ? 'confidence-medium' : 'confidence-low';
        
        html += `
            <div class="invoice-card mb-3">
                <div class="invoice-header">
                    <div>
                        <h6 class="mb-1">${invoice.invoice_number}</h6>
                        <small class="text-muted">${invoice.carrier_name} • ${invoice.service_date}</small>
                    </div>
                    <div class="text-end">
                        <span class="status-badge ${statusClass}">${invoice.status.replace('_', ' ')}</span>
                        ${confidenceScore > 0 ? `<div class="mt-1"><span class="confidence-score ${confidenceClass}">${Math.round(confidenceScore * 100)}% match</span></div>` : ''}
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-8">
                        <div class="row">
                            <div class="col-sm-6">
                                <strong>Amount:</strong> ${invoice.currency} ${invoice.amount.toLocaleString()}
                            </div>
                            <div class="col-sm-6">
                                <strong>Status:</strong> ${invoice.reconciliation_status || 'Not processed'}
                            </div>
                        </div>
                        ${invoice.discrepancies && invoice.discrepancies.length > 0 ? `
                            <div class="mt-2">
                                <small class="text-warning">
                                    <i class="fas fa-exclamation-triangle"></i> 
                                    ${invoice.discrepancies.length} discrepancy(ies) found
                                </small>
                            </div>
                        ` : ''}
                    </div>
                    <div class="col-md-4">
                        <div class="action-buttons">
                            <button class="btn btn-sm btn-outline-primary" onclick="viewInvoiceDetails('${invoice.invoice_id}')">
                                <i class="fas fa-eye"></i> Details
                            </button>
                            ${invoice.status === 'pending_reconciliation' ? `
                                <button class="btn btn-sm btn-reconcile" onclick="performReconciliation('${invoice.invoice_id}')">
                                    <i class="fas fa-balance-scale"></i> Reconcile
                                </button>
                            ` : ''}
                            ${invoice.status === 'reconciled' || invoice.status === 'discrepancy' ? `
                                <button class="btn btn-sm btn-success" onclick="approveInvoice('${invoice.invoice_id}')">
                                    <i class="fas fa-check"></i> Approve
                                </button>
                                <button class="btn btn-sm btn-warning" onclick="disputeInvoice('${invoice.invoice_id}')">
                                    <i class="fas fa-flag"></i> Dispute
                                </button>
                            ` : ''}
                        </div>
                    </div>
                </div>
            </div>
        `;
    });
    
    container.innerHTML = html;
}

// Create new invoice
async function createInvoice() {
    try {
        const formData = new FormData(document.getElementById('createInvoiceForm'));
        
        const response = await fetch('/api/invoice/create', {
            method: 'POST',
            body: formData
        });
        
        const data = await response.json();
        
        if (data.success) {
            Swal.fire({
                icon: 'success',
                title: 'Invoice Created',
                text: 'Invoice record created successfully',
                timer: 2000,
                showConfirmButton: false
            });
            
            // Reset form and refresh data
            document.getElementById('createInvoiceForm').reset();
            document.getElementById('invoices-tab').click();
            loadInvoices();
            loadAnalytics();
        } else {
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: data.error || 'Failed to create invoice'
            });
        }
    } catch (error) {
        console.error('Error creating invoice:', error);
        Swal.fire({
            icon: 'error',
            title: 'Error',
            text: 'Failed to create invoice'
        });
    }
}

// Perform reconciliation
async function performReconciliation(invoiceId) {
    try {
        Swal.fire({
            title: 'Processing...',
            text: 'Performing automated reconciliation',
            allowOutsideClick: false,
            showConfirmButton: false,
            willOpen: () => {
                Swal.showLoading();
            }
        });
        
        const response = await fetch(`/api/invoice/${invoiceId}/reconcile`, {
            method: 'POST'
        });
        
        const data = await response.json();
        Swal.close();
        
        if (data.success) {
            const result = data.reconciliation_result;
            
            Swal.fire({
                icon: result.status === 'matched' ? 'success' : 'warning',
                title: 'Reconciliation Complete',
                html: `
                    <p><strong>Status:</strong> ${result.status}</p>
                    <p><strong>Confidence:</strong> ${Math.round(result.confidence_score * 100)}%</p>
                    ${result.discrepancies.length > 0 ? `<p><strong>Discrepancies:</strong> ${result.discrepancies.length}</p>` : ''}
                `,
                showConfirmButton: true
            });
            
            loadInvoices();
            loadAnalytics();
        } else {
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: data.error || 'Reconciliation failed'
            });
        }
    } catch (error) {
        Swal.close();
        console.error('Error performing reconciliation:', error);
        Swal.fire({
            icon: 'error',
            title: 'Error',
            text: 'Reconciliation failed'
        });
    }
}

// View invoice details
function viewInvoiceDetails(invoiceId) {
    const invoice = currentInvoices.find(inv => inv.invoice_id === invoiceId);
    if (!invoice) return;
    
    const reconciliationResult = invoice.reconciliation_result || {};
    const discrepancies = reconciliationResult.discrepancies || [];
    const recommendations = reconciliationResult.recommendations || [];
    
    let html = `
        <div class="invoice-details">
            <h6>Invoice Information</h6>
            <div class="row mb-3">
                <div class="col-sm-6"><strong>Invoice Number:</strong> ${invoice.invoice_number}</div>
                <div class="col-sm-6"><strong>Amount:</strong> ${invoice.currency} ${invoice.amount.toLocaleString()}</div>
                <div class="col-sm-6"><strong>Carrier:</strong> ${invoice.carrier_name}</div>
                <div class="col-sm-6"><strong>Service Date:</strong> ${invoice.service_date}</div>
                <div class="col-sm-6"><strong>Status:</strong> ${invoice.status}</div>
                <div class="col-sm-6"><strong>Created:</strong> ${new Date(invoice.created_at).toLocaleDateString()}</div>
            </div>
            
            ${reconciliationResult.confidence_score ? `
                <h6>Reconciliation Results</h6>
                <div class="mb-3">
                    <p><strong>Confidence Score:</strong> ${Math.round(reconciliationResult.confidence_score * 100)}%</p>
                    <p><strong>Status:</strong> ${reconciliationResult.status}</p>
                    <p><strong>Auto-approval Eligible:</strong> ${reconciliationResult.auto_approval_eligible ? 'Yes' : 'No'}</p>
                </div>
            ` : ''}
            
            ${discrepancies.length > 0 ? `
                <h6>Discrepancies</h6>
                <div class="mb-3">
                    ${discrepancies.map(disc => `
                        <div class="discrepancy-item discrepancy-${disc.severity}">
                            <strong>${disc.type}:</strong> ${disc.description}<br>
                            <small>Expected: ${disc.expected} | Actual: ${disc.actual}</small>
                        </div>
                    `).join('')}
                </div>
            ` : ''}
            
            ${recommendations.length > 0 ? `
                <h6>Recommendations</h6>
                <div class="recommendation-list">
                    ${recommendations.map(rec => `<div class="recommendation-item">• ${rec}</div>`).join('')}
                </div>
            ` : ''}
        </div>
    `;
    
    document.getElementById('invoiceDetailsContent').innerHTML = html;
    new bootstrap.Modal(document.getElementById('invoiceDetailsModal')).show();
}

// Approve invoice
function approveInvoice(invoiceId) {
    document.getElementById('actionInvoiceId').value = invoiceId;
    document.getElementById('actionType').value = 'approve';
    document.getElementById('actionModalLabel').textContent = 'Approve Invoice';
    document.getElementById('actionNotesLabel').textContent = 'Approval Notes (optional)';
    document.getElementById('confirmActionBtn').className = 'btn btn-success';
    document.getElementById('confirmActionBtn').innerHTML = '<i class="fas fa-check"></i> Approve';
    
    new bootstrap.Modal(document.getElementById('actionModal')).show();
}

// Dispute invoice
function disputeInvoice(invoiceId) {
    document.getElementById('actionInvoiceId').value = invoiceId;
    document.getElementById('actionType').value = 'dispute';
    document.getElementById('actionModalLabel').textContent = 'Dispute Invoice';
    document.getElementById('actionNotesLabel').textContent = 'Dispute Reason (required)';
    document.getElementById('confirmActionBtn').className = 'btn btn-warning';
    document.getElementById('confirmActionBtn').innerHTML = '<i class="fas fa-flag"></i> Dispute';
    
    new bootstrap.Modal(document.getElementById('actionModal')).show();
}

// Confirm action
async function confirmAction() {
    try {
        const invoiceId = document.getElementById('actionInvoiceId').value;
        const actionType = document.getElementById('actionType').value;
        const notes = document.getElementById('actionNotes').value;
        
        if (actionType === 'dispute' && !notes.trim()) {
            Swal.fire({
                icon: 'warning',
                title: 'Missing Information',
                text: 'Dispute reason is required'
            });
            return;
        }
        
        const formData = new FormData();
        if (actionType === 'approve') {
            formData.append('approval_notes', notes);
        } else {
            formData.append('dispute_reason', notes);
        }
        
        const response = await fetch(`/api/invoice/${invoiceId}/${actionType}`, {
            method: 'POST',
            body: formData
        });
        
        const data = await response.json();
        
        if (data.success) {
            bootstrap.Modal.getInstance(document.getElementById('actionModal')).hide();
            
            Swal.fire({
                icon: 'success',
                title: actionType === 'approve' ? 'Invoice Approved' : 'Invoice Disputed',
                text: data.message,
                timer: 2000,
                showConfirmButton: false
            });
            
            // Reset form and refresh data
            document.getElementById('actionForm').reset();
            loadInvoices();
            loadAnalytics();
        } else {
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: data.error || `Failed to ${actionType} invoice`
            });
        }
    } catch (error) {
        console.error(`Error ${actionType}ing invoice:`, error);
        Swal.fire({
            icon: 'error',
            title: 'Error',
            text: `Failed to ${actionType} invoice`
        });
    }
}

// Apply filters
function applyFilters() {
    const statusFilter = document.getElementById('statusFilter').value;
    loadInvoices(statusFilter);
}

// Refresh invoices
function refreshInvoices() {
    document.getElementById('statusFilter').value = '';
    document.getElementById('carrierFilter').value = '';
    loadInvoices();
    loadAnalytics();
}

// Reset form
function resetForm() {
    document.getElementById('createInvoiceForm').reset();
}

// Load detailed analytics when tab is clicked
document.getElementById('analytics-tab').addEventListener('click', function() {
    loadDetailedAnalytics();
});

// Load detailed analytics
function loadDetailedAnalytics() {
    if (!currentAnalytics.carrier_performance) {
        document.getElementById('detailedAnalytics').innerHTML = `
            <div class="text-center p-4">
                <i class="fas fa-chart-bar fa-2x text-muted"></i>
                <p class="mt-2 text-muted">No detailed analytics available</p>
            </div>
        `;
        return;
    }
    
    let html = `
        <div class="row">
            <div class="col-12">
                <h5><i class="fas fa-truck"></i> Carrier Performance</h5>
            </div>
        </div>
        <div class="row">
    `;
    
    Object.entries(currentAnalytics.carrier_performance).forEach(([carrier, performance]) => {
        const autoMatchRate = performance.total_invoices > 0 ? 
            (performance.auto_matched / performance.total_invoices * 100) : 0;
        const discrepancyRate = performance.total_invoices > 0 ? 
            (performance.discrepancies / performance.total_invoices * 100) : 0;
        
        html += `
            <div class="col-md-6 mb-3">
                <div class="carrier-performance">
                    <h6>${carrier}</h6>
                    <div class="row">
                        <div class="col-6">
                            <small>Total Invoices</small>
                            <div class="h5">${performance.total_invoices}</div>
                        </div>
                        <div class="col-6">
                            <small>Total Amount</small>
                            <div class="h5">$${performance.total_amount.toLocaleString()}</div>
                        </div>
                    </div>
                    <div class="mt-2">
                        <small>Auto-match Rate: ${autoMatchRate.toFixed(1)}%</small>
                        <div class="progress-bar">
                            <div class="progress-fill bg-success" style="width: ${autoMatchRate}%"></div>
                        </div>
                    </div>
                    <div class="mt-1">
                        <small>Discrepancy Rate: ${discrepancyRate.toFixed(1)}%</small>
                        <div class="progress-bar">
                            <div class="progress-fill bg-warning" style="width: ${discrepancyRate}%"></div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });
    
    html += '</div>';
    
    document.getElementById('detailedAnalytics').innerHTML = html;
}


// Auto-generated function implementations

function debugGetElementById() {
    // Generic function implementation
    console.log('debugGetElementById called');
    showAlert('Function debugGetElementById executed', 'info');
}
</script>
{% endblock %}