{% extends "base.html" %}

{% block title %}Customer Support - Cloverics{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2">Customer Support</h1>
        <div class="btn-toolbar mb-2 mb-md-0">
            <div class="btn-group me-2">
                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="refreshSupport()">
                    <i class="fas fa-sync-alt"></i> Refresh
                </button>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Support Categories -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">How can we help you?</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for category in support_categories %}
                        <div class="col-md-6 mb-3">
                            <div class="support-category" onclick="selectCategory('{{ category.id }}')">
                                <div class="support-icon">
                                    <i class="fas fa-{{ category.icon }}"></i>
                                </div>
                                <h6>{{ category.name }}</h6>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>

            <!-- Contact Form -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Create Support Ticket</h5>
                </div>
                <div class="card-body">
                    <form id="supportTicketForm" onsubmit="submitSupportTicket(event)">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="category" class="form-label">Category</label>
                                <select class="form-select" id="category" name="category" required>
                                    <option value="">Select a category</option>
                                    {% for category in support_categories %}
                                    <option value="{{ category.id }}">{{ category.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="priority" class="form-label">Priority</label>
                                <select class="form-select" id="priority" name="priority">
                                    <option value="low">Low</option>
                                    <option value="medium" selected>Medium</option>
                                    <option value="high">High</option>
                                    <option value="urgent">Urgent</option>
                                </select>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="subject" class="form-label">Subject</label>
                            <input type="text" class="form-control" id="subject" name="subject" required>
                        </div>
                        <div class="mb-3">
                            <label for="message" class="form-label">Message</label>
                            <textarea class="form-control" id="message" name="message" rows="5" required></textarea>
                        </div>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-paper-plane"></i> Submit Ticket
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Support Sidebar -->
        <div class="col-md-4">
            <!-- Quick Help -->
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">Quick Help</h6>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <a href="/help" class="list-group-item list-group-item-action">
                            <i class="fas fa-question-circle text-primary"></i>
                            FAQ & Help Center
                        </a>
                        <a href="/contact" class="list-group-item list-group-item-action">
                            <i class="fas fa-phone text-success"></i>
                            Contact Information
                        </a>
                        <a href="javascript:void(0)" class="list-group-item list-group-item-action" onclick="openChatWidget()">
                            <i class="fas fa-comments text-info"></i>
                            Live Chat Support
                        </a>
                    </div>
                </div>
            </div>

            <!-- Recent Tickets -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="card-title mb-0">Recent Tickets</h6>
                </div>
                <div class="card-body">
                    {% if recent_tickets %}
                        {% for ticket in recent_tickets %}
                        <div class="support-ticket-item">
                            <div class="d-flex justify-content-between">
                                <span class="ticket-id">#{{ ticket.id }}</span>
                                <span class="badge bg-{{ 'success' if ticket.status == 'resolved' else 'warning' if ticket.status == 'open' else 'secondary' }}">
                                    {{ ticket.status.title() }}
                                </span>
                            </div>
                            <div class="ticket-subject">{{ ticket.subject }}</div>
                            <div class="text-muted small">{{ ticket.created }}</div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <p class="text-muted">No recent support tickets</p>
                    {% endif %}
                </div>
            </div>

            <!-- Emergency Contact -->
            <div class="card mt-4">
                <div class="card-header bg-danger text-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-exclamation-triangle"></i>
                        Emergency Support
                    </h6>
                </div>
                <div class="card-body">
                    <p class="small">For urgent shipment issues or emergencies:</p>
                    <div class="text-center">
                        <a href="tel:******-CLOVERICS" class="btn btn-danger btn-sm">
                            <i class="fas fa-phone"></i> Call Now
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.support-category {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    height: 120px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.support-category:hover {
    border-color: #4f46e5;
    background-color: #f8f9ff;
    transform: translateY(-2px);
}

.support-category.selected {
    border-color: #4f46e5;
    background-color: #4f46e5;
    color: white;
}

.support-icon {
    font-size: 2rem;
    margin-bottom: 10px;
    color: #4f46e5;
}

.support-category.selected .support-icon {
    color: white;
}

.support-ticket-item {
    border-bottom: 1px solid #e9ecef;
    padding: 10px 0;
}

.support-ticket-item:last-child {
    border-bottom: none;
}

.ticket-id {
    font-weight: 600;
    color: #4f46e5;
}

.ticket-subject {
    font-size: 0.9rem;
    margin: 5px 0;
}
</style>

<script>
function selectCategory(categoryId) {
    // Remove previous selection
    document.querySelectorAll('.support-category').forEach(cat => {
        cat.classList.remove('selected');
    });
    
    // Add selection to clicked category
    event.target.closest('.support-category').classList.add('selected');
    
    // Set form category
    document.getElementById('category').value = categoryId;
    
    // Scroll to form
    document.getElementById('supportTicketForm').scrollIntoView({behavior: 'smooth'});
}

async function submitSupportTicket(event) {
    event.preventDefault();
    
    const formData = new FormData(event.target);
    
    try {
        const response = await fetch('/api/support/create-ticket', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        if (result.status === 'success') {
            alert('Support ticket created successfully! Ticket ID: ' + result.ticket_id);
            event.target.reset();
            location.reload();
        } else {
            alert('Error creating support ticket: ' + result.message);
        }
    } catch (error) {
        alert('Error creating support ticket. Please try again.');
        console.error('Support ticket error:', error);
    }
}

function refreshSupport() {
    location.reload();
}

function openChatWidget() {
    // This will trigger the chatbot widget
    if (typeof toggleChatbot === 'function') {
        toggleChatbot();
    } else {
        alert('Live chat will be available soon. Please create a support ticket for immediate assistance.');
    }
}
</script>
{% endblock %}