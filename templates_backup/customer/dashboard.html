{% extends "base.html" %}

{% block content %}
<div class="dashboard-container">
    <div class="dashboard-header">
        <h1>{{ _('customer_dashboard') }}</h1>
        <p>{{ _('welcome_back') }}, {{ user.company_name or user.email }}</p>
    </div>

    <!-- Quick Stats -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-primary">
                    <i class="fas fa-boxes"></i>
                </div>
                <div class="stat-content">
                    <h3>{{ active_shipments }}</h3>
                    <p>{{ _('active_shipments') }}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-success">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stat-content">
                    <h3>{{ total_shipments }}</h3>
                    <p>{{ _('total_shipments') }}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-info">
                    <i class="fas fa-dollar-sign"></i>
                </div>
                <div class="stat-content">
                    <h3>$12,847</h3>
                    <p>{{ _('total_spent') }}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-warning">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="stat-content">
                    <h3>{{ pending_quotes }}</h3>
                    <p>{{ _('pending_quotes') }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="quick-actions-card">
                <h5>{{ _('quick_actions') }}</h5>
                <div class="action-buttons">
                    <a href="/customer/search-shipping" class="btn btn-primary">
                        <i class="fas fa-search"></i> {{ _('search_shipping') }}
                    </a>
                    <a href="/customer/track-shipment" class="btn btn-outline-primary">
                        <i class="fas fa-map-marked-alt"></i> {{ _('track_shipment') }}
                    </a>

                    <a href="/shipment/37" class="btn btn-outline-success">
                        <i class="fas fa-route"></i> {{ _('shipment_process_flow') }}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Shipments and Quotes -->
    <div class="row">
        <div class="col-lg-8">
            <!-- Tabbed Interface for Shipments and Quotes -->
            <div class="data-card">
                <div class="card-header">
                    <ul class="nav nav-tabs card-header-tabs" id="dashboardTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="shipments-tab" data-bs-toggle="tab" data-bs-target="#shipments" type="button" role="tab">
                                <i class="fas fa-boxes"></i> {{ _('recent_shipments') }}
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="quotes-tab" data-bs-toggle="tab" data-bs-target="#quotes" type="button" role="tab">
                                <i class="fas fa-quote-left"></i> {{ _('my_quotes') }}
                            </button>
                        </li>
                    </ul>
                </div>
                <div class="tab-content">
                    <!-- Shipments Tab -->
                    <div class="tab-pane fade show active" id="shipments" role="tabpanel">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">{{ _('recent_shipments') }}</h6>
                            <a href="/customer/manage-shipments" class="btn btn-sm btn-outline-primary">{{ _('view_all') }}</a>
                        </div>
                <div class="card-body">
                    {% if shipments %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Origin</th>
                                    <th>Destination</th>
                                    <th>Status</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for shipment in shipments %}
                                <tr>
                                    <td>#{{ shipment.id }}</td>
                                    <td>{{ shipment.origin }}</td>
                                    <td>{{ shipment.destination }}</td>
                                    <td>
                                        <span class="status-badge status-{{ shipment.status.lower() }}">
                                            {{ shipment.status }}
                                        </span>
                                    </td>
                                    <td>{{ shipment.created_at }}</td>
                                    <td>
                                        <a href="/customer/manage-shipments" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="empty-state">
                        <i class="fas fa-box-open"></i>
                        <h6>{{ _('no_shipments_yet') }}</h6>
                        <p>{{ _('start_by_searching') }}</p>
                        <a href="/customer/search-shipping" class="btn btn-primary">{{ _('search_shipping') }}</a>
                    </div>
                    {% endif %}
                        </div>
                    </div>
                    
                    <!-- Quotes Tab -->
                    <div class="tab-pane fade" id="quotes" role="tabpanel">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">My Quote Requests</h6>
                            <a href="/customer/search-shipping" class="btn btn-sm btn-outline-primary">Request New Quote</a>
                        </div>
                        <div class="card-body">
                            {% if quotes %}
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Reference</th>
                                            <th>Provider</th>
                                            <th>Route</th>
                                            <th>Weight</th>
                                            <th>Status</th>
                                            <th>Submitted</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for quote in quotes %}
                                        <tr>
                                            <td><strong>{{ quote.quote_reference }}</strong></td>
                                            <td>{{ quote.provider_name }}</td>
                                            <td>{{ quote.route }}</td>
                                            <td>{{ quote.cargo_weight }} kg</td>
                                            <td>
                                                <span class="status-badge status-{{ quote.status.lower() }}">
                                                    {{ quote.status|title }}
                                                </span>
                                            </td>
                                            <td>{{ quote.created_at.strftime('%Y-%m-%d') if quote.created_at else 'N/A' }}</td>
                                            <td>
                                                <button class="btn btn-sm btn-outline-primary" onclick="viewQuoteDetails('{{ quote.id }}')">
                                                    <i class="fas fa-eye"></i> View
                                                </button>
                                                {% if quote.status == 'quoted' or quote.status == 'sent' %}
                                                <button class="btn btn-sm btn-success" onclick="acceptQuote('{{ quote.id }}')">
                                                    <i class="fas fa-check"></i> Accept
                                                </button>
                                                <button class="btn btn-sm btn-danger" onclick="rejectQuote('{{ quote.id }}')">
                                                    <i class="fas fa-times"></i> Reject
                                                </button>
                                                {% endif %}
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            {% else %}
                            <div class="empty-state">
                                <i class="fas fa-quote-left"></i>
                                <h6>No quotes yet</h6>
                                <p>Request quotes from logistics providers to see them here</p>
                                <a href="/customer/search-shipping" class="btn btn-primary">Search for Routes</a>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Notifications -->
            <div class="data-card mb-3">
                <div class="card-header">
                    <h6>Recent Notifications</h6>
                </div>
                <div class="card-body">
                    {% if notifications %}
                        {% for notification in notifications %}
                        <div class="notification-item">
                            <div class="notification-icon {{ notification.color }}">
                                <i class="{{ notification.icon }}"></i>
                            </div>
                            <div class="notification-content">
                                <p>{{ notification.message }}</p>
                                <small>{{ notification.time_ago }}</small>
                            </div>
                        </div>
                        {% endfor %}
                        <a href="/notifications" class="btn btn-sm btn-outline-primary mt-2">View All</a>
                    {% else %}
                    <div class="empty-state">
                        <i class="fas fa-bell-slash"></i>
                        <h6>No notifications yet</h6>
                        <p>You'll see important updates here</p>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Quick Stats -->
            <div class="data-card">
                <div class="card-header">
                    <h6>This Month</h6>
                </div>
                <div class="card-body">
                    <div class="mini-stat">
                        <span class="label">Shipments Created</span>
                        <span class="value">8</span>
                    </div>
                    <div class="mini-stat">
                        <span class="label">Amount Spent</span>
                        <span class="value">$2,450</span>
                    </div>
                    <div class="mini-stat">
                        <span class="label">Avg. Delivery Time</span>
                        <span class="value">5.2 days</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.dashboard-container {
    padding: 1.5rem;
}

.dashboard-header h1 {
    color: #1E88E5;
    margin-bottom: 0.5rem;
}

.dashboard-header p {
    color: #666;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    border-radius: 10px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    height: 100%;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    color: white;
    font-size: 1.5rem;
}

.stat-content h3 {
    margin: 0;
    font-size: 2rem;
    font-weight: bold;
}

.stat-content p {
    margin: 0;
    color: #666;
    font-size: 0.9rem;
}

.quick-actions-card {
    background: white;
    border-radius: 10px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.action-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.data-card {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 1.5rem;
}

.card-header {
    padding: 1.5rem 1.5rem 0;
    display: flex;
    justify-content: between;
    align-items: center;
}

.card-body {
    padding: 1.5rem;
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
}

.status-pending {
    background: #FFF3CD;
    color: #856404;
}

.status-in_transit {
    background: #D1ECF1;
    color: #0C5460;
}

.status-delivered {
    background: #D4EDDA;
    color: #155724;
}

.status-cancelled {
    background: #F8D7DA;
    color: #721C24;
}

.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: #666;
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.notification-item {
    display: flex;
    align-items: flex-start;
    padding: 1rem 0;
    border-bottom: 1px solid #eee;
}

.notification-item:last-child {
    border-bottom: none;
}

.notification-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    color: white;
    font-size: 0.9rem;
}

.notification-content p {
    margin: 0;
    font-size: 0.9rem;
}

.notification-content small {
    color: #999;
}

.mini-stat {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #eee;
}

.mini-stat:last-child {
    border-bottom: none;
}

.mini-stat .label {
    color: #666;
    font-size: 0.9rem;
}

.mini-stat .value {
    font-weight: bold;
    color: #1E88E5;
}

/* Quote status colors */
.status-quoted {
    background: #CCE5FF;
    color: #0066CC;
}

.status-sent {
    background: #E1F5FE;
    color: #01579B;
}

.status-accepted {
    background: #D4EDDA;
    color: #155724;
}

.status-declined {
    background: #F8D7DA;
    color: #721C24;
}

.status-expired {
    background: #F1F3F4;
    color: #5F6368;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
// Quote management functions
function viewQuoteDetails(quoteId) {
    // Create a modal to show quote details
    Swal.fire({
        title: 'Loading Quote Details...',
        text: 'Please wait while we fetch the quote information.',
        allowOutsideClick: false,
        showConfirmButton: false,
        didOpen: () => {
            Swal.showLoading();
            
            // Fetch quote details via API
            fetch(`/api/customer/quote/${quoteId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const quote = data.quote;
                        Swal.fire({
                            title: `Quote ${quote.reference}`,
                            html: `
                                <div class="quote-details">
                                    <div class="row mb-3">
                                        <div class="col-6"><strong>Provider:</strong></div>
                                        <div class="col-6">${quote.provider_name}</div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-6"><strong>Route:</strong></div>
                                        <div class="col-6">${quote.route}</div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-6"><strong>Weight:</strong></div>
                                        <div class="col-6">${quote.cargo_weight} kg</div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-6"><strong>Dimensions:</strong></div>
                                        <div class="col-6">${quote.cargo_dimensions || 'Not specified'}</div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-6"><strong>Pickup Date:</strong></div>
                                        <div class="col-6">${quote.pickup_date}</div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-6"><strong>Status:</strong></div>
                                        <div class="col-6"><span class="badge bg-primary">${quote.status}</span></div>
                                    </div>
                                    ${quote.quoted_price ? `
                                    <div class="row mb-3">
                                        <div class="col-6"><strong>Quoted Price:</strong></div>
                                        <div class="col-6"><span class="text-success"><strong>$${quote.quoted_price}</strong></span></div>
                                    </div>
                                    ` : ''}
                                    ${quote.provider_response ? `
                                    <div class="row mb-3">
                                        <div class="col-12"><strong>Provider Response:</strong></div>
                                        <div class="col-12 mt-2"><div class="alert alert-info">${quote.provider_response}</div></div>
                                    </div>
                                    ` : ''}
                                </div>
                            `,
                            confirmButtonText: 'Close',
                            confirmButtonColor: '#1E88E5'
                        });
                    } else {
                        Swal.fire('Error', data.error || 'Failed to load quote details', 'error');
                    }
                })
                .catch(error => {
                    console.error('Error fetching quote details:', error);
                    Swal.fire('Error', 'Failed to load quote details', 'error');
                });
        }
    });
}

function acceptQuote(quoteId) {
    Swal.fire({
        title: 'Accept Quote?',
        text: 'Are you sure you want to accept this quote? This will create a shipment.',
        icon: 'question',
        showCancelButton: true,
        confirmButtonText: 'Accept Quote',
        cancelButtonText: 'Cancel',
        confirmButtonColor: '#28a745'
    }).then((result) => {
        if (result.isConfirmed) {
            // Show loading
            Swal.fire({
                title: 'Processing...',
                text: 'Creating shipment from accepted quote...',
                allowOutsideClick: false,
                showConfirmButton: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });
            
            // Send accept request
            fetch(`/api/customer/quote/${quoteId}/accept`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire({
                        title: 'Quote Accepted!',
                        text: 'Your shipment has been created. Redirecting to payment...',
                        icon: 'success',
                        confirmButtonText: 'Continue'
                    }).then(() => {
                        if (data.redirect_url) {
                            window.location.href = data.redirect_url;
                        } else {
                            location.reload();
                        }
                    });
                } else {
                    Swal.fire('Error', data.error || 'Failed to accept quote', 'error');
                }
            })
            .catch(error => {
                console.error('Error accepting quote:', error);
                Swal.fire('Error', 'Failed to accept quote', 'error');
            });
        }
    });
}

function rejectQuote(quoteId) {
    Swal.fire({
        title: 'Reject Quote?',
        text: 'Are you sure you want to reject this quote? This action cannot be undone.',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'Reject Quote',
        cancelButtonText: 'Cancel',
        confirmButtonColor: '#dc3545'
    }).then((result) => {
        if (result.isConfirmed) {
            // Show loading
            Swal.fire({
                title: 'Processing...',
                text: 'Rejecting quote...',
                allowOutsideClick: false,
                showConfirmButton: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });
            
            // Send reject request
            fetch(`/api/customer/quote/${quoteId}/reject`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire({
                        title: 'Quote Rejected',
                        text: 'The quote has been rejected successfully.',
                        icon: 'success',
                        confirmButtonText: 'OK'
                    }).then(() => {
                        location.reload();
                    });
                } else {
                    Swal.fire('Error', data.error || 'Failed to reject quote', 'error');
                }
            })
            .catch(error => {
                console.error('Error rejecting quote:', error);
                Swal.fire('Error', 'Failed to reject quote', 'error');
            });
        }
    });
}

// Initialize Bootstrap tabs if not already done
document.addEventListener('DOMContentLoaded', function() {
    // Enable Bootstrap tabs
    const triggerTabList = [].slice.call(document.querySelectorAll('#dashboardTabs button'));
    triggerTabList.forEach(function (triggerEl) {
        const tabTrigger = new bootstrap.Tab(triggerEl);
        
        triggerEl.addEventListener('click', function (event) {
            event.preventDefault();
            tabTrigger.show();
        });
    });
});
</script>
{% endblock %}