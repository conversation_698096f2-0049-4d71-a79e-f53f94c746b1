{% extends "base.html" %}
{% set active_page = "compliance_automation" %}

{% block title %}Compliance Automation - Cloverics{% endblock %}

{% block extra_css %}
<style>
.compliance-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    border-radius: 10px;
    margin-bottom: 2rem;
}

.compliance-card {
    background: white;
    border-radius: 10px;
    padding: 1.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 1.5rem;
    transition: transform 0.2s ease;
}

.compliance-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.tab-pane {
    min-height: 400px;
    padding: 1rem 0;
}

.compliance-metric {
    text-align: center;
    padding: 1rem;
    border-radius: 8px;
    background: white;
    border: 1px solid #e2e8f0;
    margin: 0.5rem;
}

.compliance-metric-value {
    font-size: 2rem;
    font-weight: bold;
    color: #2d3748;
}

.compliance-metric-label {
    font-size: 0.875rem;
    color: #718096;
    margin-top: 0.5rem;
}

.compliance-status {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-compliant {
    background: #c6f6d5;
    color: #2f855a;
}

.status-non-compliant {
    background: #fed7d7;
    color: #c53030;
}

.status-pending {
    background: #fef5e7;
    color: #d69e2e;
}

.status-requires-action {
    background: #fbb6ce;
    color: #b83280;
}

.risk-level {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 600;
}

.risk-critical {
    background: #feb2b2;
    color: #742a2a;
}

.risk-high {
    background: #fbb6ce;
    color: #742a2a;
}

.risk-medium {
    background: #fef5e7;
    color: #744210;
}

.risk-low {
    background: #c6f6d5;
    color: #2f855a;
}

.framework-badge {
    display: inline-block;
    background: #edf2f7;
    color: #2d3748;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 500;
    margin: 0.25rem;
}

.violation-item {
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    background: white;
}

.violation-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.scan-progress {
    background: #f7fafc;
    border-radius: 8px;
    padding: 1rem;
    margin: 1rem 0;
}

.progress-bar {
    background: #e2e8f0;
    border-radius: 10px;
    height: 8px;
    overflow: hidden;
    margin: 0.5rem 0;
}

.progress-fill {
    background: linear-gradient(90deg, #48bb78, #38a169);
    height: 100%;
    transition: width 0.5s ease;
}

.compliance-chart {
    height: 300px;
    margin: 1rem 0;
}

.btn-scan {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.2s ease;
}

.btn-scan:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
    color: white;
}

.btn-resolve {
    background: #48bb78;
    border: none;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-size: 0.875rem;
}

.btn-resolve:hover {
    background: #38a169;
    color: white;
}

.frameworks-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin: 1rem 0;
}

.framework-card {
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 1rem;
    text-align: center;
    transition: all 0.2s ease;
    cursor: pointer;
}

.framework-card:hover {
    border-color: #667eea;
    background: #f8fafc;
}

.framework-card.active {
    border-color: #667eea;
    background: #f7fafc;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.alert-info {
    background: #ebf8ff;
    border: 1px solid #bee3f8;
    color: #2c5282;
    padding: 1rem;
    border-radius: 8px;
    margin: 1rem 0;
}

.mt-4 { margin-top: 1.5rem; }
.mb-4 { margin-bottom: 1.5rem; }
.text-center { text-align: center; }
.d-none { display: none; }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <div class="compliance-header">
                <h2><i class="fas fa-shield-alt"></i> Compliance Automation</h2>
                <p>Automated compliance monitoring, regulatory validation, and audit trail management</p>
            </div>
        </div>
    </div>

    <!-- Compliance Dashboard Overview -->
    <div class="row">
        <div class="col-12">
            <div class="compliance-card">
                <h4><i class="fas fa-tachometer-alt"></i> Compliance Dashboard</h4>
                <div class="row" id="dashboardMetrics">
                    <div class="col-md-3">
                        <div class="compliance-metric">
                            <div class="compliance-metric-value" id="totalViolations">-</div>
                            <div class="compliance-metric-label">Total Violations</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="compliance-metric">
                            <div class="compliance-metric-value" id="criticalViolations">-</div>
                            <div class="compliance-metric-label">Critical Violations</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="compliance-metric">
                            <div class="compliance-metric-value" id="complianceScore">-</div>
                            <div class="compliance-metric-label">Compliance Score</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="compliance-metric">
                            <div class="compliance-metric-value" id="resolutionRate">-</div>
                            <div class="compliance-metric-label">Resolution Rate</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Compliance Interface -->
    <div class="row">
        <div class="col-12">
            <div class="compliance-card">
                <ul class="nav nav-tabs" id="complianceTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="scan-tab" data-bs-toggle="tab" data-bs-target="#scan" type="button" role="tab">
                            <i class="fas fa-search"></i> Compliance Scan
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="violations-tab" data-bs-toggle="tab" data-bs-target="#violations" type="button" role="tab">
                            <i class="fas fa-exclamation-triangle"></i> Violations
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="rules-tab" data-bs-toggle="tab" data-bs-target="#rules" type="button" role="tab">
                            <i class="fas fa-clipboard-list"></i> Compliance Rules
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="frameworks-tab" data-bs-toggle="tab" data-bs-target="#frameworks" type="button" role="tab">
                            <i class="fas fa-cogs"></i> Frameworks
                        </button>
                    </li>
                </ul>

                <div class="tab-content" id="complianceTabContent">
                    <!-- Compliance Scan Tab -->
                    <div class="tab-pane fade show active" id="scan" role="tabpanel">
                        <div class="text-center mt-4">
                            <h5>Automated Compliance Scanning</h5>
                            <p class="text-muted">Run comprehensive compliance validation across all your data and processes</p>
                            
                            <button id="startScanBtn" class="btn btn-scan">
                                <i class="fas fa-play"></i> Start Compliance Scan
                            </button>
                            
                            <div id="scanProgress" class="scan-progress d-none">
                                <h6><i class="fas fa-sync fa-spin"></i> Scanning in Progress...</h6>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: 0%"></div>
                                </div>
                                <div id="scanStatus">Initializing scan...</div>
                            </div>
                            
                            <div id="scanResults" class="mt-4 d-none">
                                <h6>Scan Results</h6>
                                <div id="scanSummary"></div>
                                <div id="scanDetails"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Violations Tab -->
                    <div class="tab-pane fade" id="violations" role="tabpanel">
                        <div class="mt-4">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5>Compliance Violations</h5>
                                <button class="btn btn-outline-primary" onclick="loadViolations()">
                                    <i class="fas fa-refresh"></i> Refresh
                                </button>
                            </div>
                            
                            <div id="violationsList">
                                <div class="alert-info">
                                    <i class="fas fa-info-circle"></i> 
                                    Run a compliance scan to identify violations and compliance issues.
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Compliance Rules Tab -->
                    <div class="tab-pane fade" id="rules" role="tabpanel">
                        <div class="mt-4">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5>Compliance Rules</h5>
                                <select id="frameworkFilter" class="form-select" style="width: 200px;">
                                    <option value="">All Frameworks</option>
                                </select>
                            </div>
                            
                            <div id="rulesList">
                                <div class="text-center">
                                    <div class="loading-spinner"></div>
                                    <div>Loading compliance rules...</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Frameworks Tab -->
                    <div class="tab-pane fade" id="frameworks" role="tabpanel">
                        <div class="mt-4">
                            <h5>Compliance Frameworks</h5>
                            <p class="text-muted">Select frameworks to customize your compliance monitoring</p>
                            
                            <div id="frameworksGrid" class="frameworks-grid">
                                <div class="text-center">
                                    <div class="loading-spinner"></div>
                                    <div>Loading frameworks...</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Violation Detail Modal -->
<div class="modal fade" id="violationModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Violation Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="violationDetails">
                <div class="text-center">
                    <div class="loading-spinner"></div>
                    <div>Loading violation details...</div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-resolve" id="resolveViolationBtn" style="display: none;" onclick="markAsResolved(this)">
                    Mark as Resolved
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Resolution Modal -->
<div class="modal fade" id="resolutionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Resolve Violation</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="resolutionForm">
                    <div class="mb-3">
                        <label for="resolutionNotes" class="form-label">Resolution Notes</label>
                        <textarea class="form-control" id="resolutionNotes" name="resolution_notes" rows="4" 
                                placeholder="Describe how this violation was resolved..." required></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-resolve" id="submitResolutionBtn" onclick="submitResolution()">
                    Submit Resolution
                </button>
            </div>
        </div>
    </div>
</div>

<script>
let currentViolationId = null;

// Load dashboard on page load
document.addEventListener('DOMContentLoaded', function() {
    loadDashboard();
    loadRules();
    loadFrameworks();
});

// Start compliance scan
document.getElementById('startScanBtn').addEventListener('click', function() {
    startComplianceScan();
});

async function loadDashboard() {
    try {
        const response = await fetch('/api/compliance/dashboard');
        const data = await response.json();
        
        if (data.success) {
            updateDashboardMetrics(data.dashboard.summary);
        }
    } catch (error) {
        console.error('Error loading dashboard:', error);
    }
}

function updateDashboardMetrics(summary) {
    document.getElementById('totalViolations').textContent = summary.total_violations || 0;
    document.getElementById('criticalViolations').textContent = summary.critical_violations || 0;
    document.getElementById('complianceScore').textContent = Math.round(summary.overall_compliance_score || 0) + '%';
    document.getElementById('resolutionRate').textContent = Math.round(summary.resolution_rate || 0) + '%';
}

async function startComplianceScan() {
    const scanBtn = document.getElementById('startScanBtn');
    const scanProgress = document.getElementById('scanProgress');
    const scanResults = document.getElementById('scanResults');
    
    // Show progress
    scanBtn.style.display = 'none';
    scanProgress.classList.remove('d-none');
    scanResults.classList.add('d-none');
    
    // Simulate scan progress
    let progress = 0;
    const progressBar = scanProgress.querySelector('.progress-fill');
    const statusText = document.getElementById('scanStatus');
    
    const progressInterval = setInterval(() => {
        progress += Math.random() * 20;
        if (progress > 95) progress = 95;
        
        progressBar.style.width = progress + '%';
        
        if (progress < 30) {
            statusText.textContent = 'Scanning user data and permissions...';
        } else if (progress < 60) {
            statusText.textContent = 'Validating shipment compliance...';
        } else if (progress < 90) {
            statusText.textContent = 'Checking regulatory requirements...';
        } else {
            statusText.textContent = 'Finalizing compliance report...';
        }
    }, 500);
    
    try {
        const response = await fetch('/api/compliance/scan', {
            method: 'POST'
        });
        const data = await response.json();
        
        clearInterval(progressInterval);
        progressBar.style.width = '100%';
        statusText.textContent = 'Scan completed!';
        
        setTimeout(() => {
            scanProgress.classList.add('d-none');
            scanResults.classList.remove('d-none');
            displayScanResults(data);
            scanBtn.style.display = 'inline-block';
            
            // Reload dashboard
            loadDashboard();
        }, 1000);
        
    } catch (error) {
        clearInterval(progressInterval);
        console.error('Error running scan:', error);
        scanProgress.classList.add('d-none');
        scanBtn.style.display = 'inline-block';
        
        Swal.fire({
            title: 'Scan Error',
            text: 'Failed to complete compliance scan. Please try again.',
            icon: 'error'
        });
    }
}

function displayScanResults(data) {
    const summaryDiv = document.getElementById('scanSummary');
    const detailsDiv = document.getElementById('scanDetails');
    
    if (data.success) {
        const summary = data.summary;
        
        summaryDiv.innerHTML = `
            <div class="row">
                <div class="col-md-4">
                    <div class="compliance-metric">
                        <div class="compliance-metric-value">${summary.total_entities_scanned}</div>
                        <div class="compliance-metric-label">Entities Scanned</div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="compliance-metric">
                        <div class="compliance-metric-value">${summary.total_violations_found}</div>
                        <div class="compliance-metric-label">Violations Found</div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="compliance-metric">
                        <div class="compliance-metric-value">${Math.round(summary.overall_compliance_rate)}%</div>
                        <div class="compliance-metric-label">Compliance Rate</div>
                    </div>
                </div>
            </div>
        `;
        
        // Display scan details
        let detailsHtml = '<h6>Scan Details</h6>';
        for (const [entityId, result] of Object.entries(data.results)) {
            const statusClass = result.compliance_status === 'compliant' ? 'status-compliant' : 'status-non-compliant';
            detailsHtml += `
                <div class="violation-item">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <strong>${result.entity_type.toUpperCase()} ${entityId}</strong>
                            <span class="compliance-status ${statusClass}">${result.compliance_status}</span>
                        </div>
                        <div>${result.violations_count} violations</div>
                    </div>
                </div>
            `;
        }
        detailsDiv.innerHTML = detailsHtml;
        
    } else {
        summaryDiv.innerHTML = `<div class="alert alert-danger">Scan failed: ${data.error}</div>`;
    }
}

async function loadRules() {
    try {
        const response = await fetch('/api/compliance/rules');
        const data = await response.json();
        
        if (data.success) {
            displayRules(data.rules);
            populateFrameworkFilter(data.rules);
        }
    } catch (error) {
        console.error('Error loading rules:', error);
        document.getElementById('rulesList').innerHTML = '<div class="alert alert-danger">Failed to load compliance rules</div>';
    }
}

function displayRules(rules) {
    const rulesList = document.getElementById('rulesList');
    let html = '';
    
    for (const [ruleId, rule] of Object.entries(rules)) {
        const riskClass = `risk-${rule.risk_level}`;
        const statusClass = rule.status === 'compliant' ? 'status-compliant' : 
                           rule.status === 'pending_review' ? 'status-pending' : 'status-non-compliant';
        
        html += `
            <div class="violation-item">
                <div class="violation-header">
                    <div>
                        <strong>${rule.name}</strong>
                        <span class="framework-badge">${rule.framework.toUpperCase()}</span>
                        <span class="risk-level ${riskClass}">${rule.risk_level.toUpperCase()}</span>
                    </div>
                    <span class="compliance-status ${statusClass}">${rule.status.replace('_', ' ').toUpperCase()}</span>
                </div>
                <p>${rule.description}</p>
                <div>
                    <small><strong>Type:</strong> ${rule.compliance_type.replace('_', ' ')}</small><br>
                    <small><strong>Frequency:</strong> ${rule.frequency}</small><br>
                    <small><strong>Automated:</strong> ${rule.automated_check ? 'Yes' : 'No'}</small>
                </div>
            </div>
        `;
    }
    
    rulesList.innerHTML = html;
}

function populateFrameworkFilter(rules) {
    const filter = document.getElementById('frameworkFilter');
    const frameworks = [...new Set(Object.values(rules).map(rule => rule.framework))];
    
    frameworks.forEach(framework => {
        const option = document.createElement('option');
        option.value = framework;
        option.textContent = framework.replace('_', ' ').toUpperCase();
        filter.appendChild(option);
    });
    
    filter.addEventListener('change', function() {
        const selectedFramework = this.value;
        if (selectedFramework) {
            const filteredRules = Object.fromEntries(
                Object.entries(rules).filter(([_, rule]) => rule.framework === selectedFramework)
            );
            displayRules(filteredRules);
        } else {
            displayRules(rules);
        }
    });
}

async function loadFrameworks() {
    try {
        const response = await fetch('/api/compliance/frameworks');
        const data = await response.json();
        
        if (data.success) {
            displayFrameworks(data.frameworks);
        }
    } catch (error) {
        console.error('Error loading frameworks:', error);
        document.getElementById('frameworksGrid').innerHTML = '<div class="alert alert-danger">Failed to load frameworks</div>';
    }
}

function displayFrameworks(frameworks) {
    const frameworksGrid = document.getElementById('frameworksGrid');
    let html = '';
    
    frameworks.forEach(framework => {
        html += `
            <div class="framework-card" onclick="toggleFramework('${framework.id}')">
                <h6>${framework.name}</h6>
                <p class="text-muted">${framework.description}</p>
                <small class="text-success">✓ Active</small>
            </div>
        `;
    });
    
    frameworksGrid.innerHTML = html;
}

function toggleFramework(frameworkId) {
    const card = event.currentTarget;
    card.classList.toggle('active');
    
    Swal.fire({
        title: 'Framework Settings',
        text: `Framework ${frameworkId} toggled successfully`,
        icon: 'success',
        timer: 1500,
        showConfirmButton: false
    });
}

// Violation detail modal handlers
document.getElementById('resolveViolationBtn').addEventListener('click', function() {
    document.getElementById('resolutionModal').style.display = 'block';
});

document.getElementById('submitResolutionBtn').addEventListener('click', function() {
    submitResolution();
});

async function showViolationDetails(violationId) {
    currentViolationId = violationId;
    const modal = new bootstrap.Modal(document.getElementById('violationModal'));
    modal.show();
    
    try {
        const response = await fetch(`/api/compliance/violation/${violationId}`);
        const data = await response.json();
        
        if (data.success) {
            displayViolationDetails(data.violation);
        } else {
            document.getElementById('violationDetails').innerHTML = `<div class="alert alert-danger">Error: ${data.error}</div>`;
        }
    } catch (error) {
        console.error('Error loading violation details:', error);
        document.getElementById('violationDetails').innerHTML = '<div class="alert alert-danger">Failed to load violation details</div>';
    }
}

function displayViolationDetails(violation) {
    const detailsDiv = document.getElementById('violationDetails');
    const resolveBtn = document.getElementById('resolveViolationBtn');
    
    const statusClass = violation.current_status === 'compliant' ? 'status-compliant' : 
                       violation.current_status === 'pending_review' ? 'status-pending' : 'status-non-compliant';
    
    detailsDiv.innerHTML = `
        <div class="mb-3">
            <h6>Violation Information</h6>
            <p><strong>ID:</strong> ${violation.id}</p>
            <p><strong>Description:</strong> ${violation.description}</p>
            <p><strong>Entity:</strong> ${violation.entity_type} (${violation.entity_id})</p>
            <p><strong>Severity:</strong> <span class="risk-level risk-${violation.severity}">${violation.severity.toUpperCase()}</span></p>
            <p><strong>Status:</strong> <span class="compliance-status ${statusClass}">${violation.current_status.replace('_', ' ').toUpperCase()}</span></p>
            <p><strong>Detected:</strong> ${new Date(violation.detected_at).toLocaleString()}</p>
        </div>
        
        <div class="mb-3">
            <h6>Remediation Actions</h6>
            <ul>
                ${violation.remediation_actions.map(action => `<li>${action}</li>`).join('')}
            </ul>
        </div>
    `;
    
    // Show resolve button if not already resolved
    if (violation.current_status !== 'compliant') {
        resolveBtn.style.display = 'inline-block';
    } else {
        resolveBtn.style.display = 'none';
    }
}

async function submitResolution() {
    if (!currentViolationId) return;
    
    const formData = new FormData(document.getElementById('resolutionForm'));
    
    try {
        const response = await fetch(`/api/compliance/violation/${currentViolationId}/resolve`, {
            method: 'POST',
            body: formData
        });
        
        const data = await response.json();
        
        if (data.success) {
            Swal.fire({
                title: 'Resolution Submitted',
                text: 'Violation has been marked as resolved successfully',
                icon: 'success'
            });
            
            // Close modals
            bootstrap.Modal.getInstance(document.getElementById('resolutionModal')).hide();
            bootstrap.Modal.getInstance(document.getElementById('violationModal')).hide();
            
            // Reload dashboard
            loadDashboard();
        } else {
            Swal.fire({
                title: 'Resolution Failed',
                text: data.error,
                icon: 'error'
            });
        }
    } catch (error) {
        console.error('Error submitting resolution:', error);
        Swal.fire({
            title: 'Resolution Failed',
            text: 'Failed to submit resolution. Please try again.',
            icon: 'error'
        });
    }
}

async function loadViolations() {
    // This would load actual violations from the scan results
    const violationsList = document.getElementById('violationsList');
    violationsList.innerHTML = `
        <div class="alert-info">
            <i class="fas fa-info-circle"></i> 
            No recent violations found. Run a compliance scan to check for issues.
        </div>
    `;
}


// Auto-generated button handlers

function markAsResolved(button) {
    const itemId = button.dataset.id || button.closest('[data-id]')?.dataset.id;
    if (!itemId) {
        showAlert('Item ID not found', 'error');
        return;
    }
    
    fetch(`/compliance/resolve/${itemId}`, {
        method: 'POST',
        headers: {'X-CSRFToken': getCSRFToken()}
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('Marked as resolved', 'success');
            button.closest('tr, .item').style.opacity = '0.5';
        } else {
            showAlert('Failed to mark as resolved', 'error');
        }
    })
    .catch(error => showAlert('Action failed', 'error'));
}
</script>
{% endblock %}