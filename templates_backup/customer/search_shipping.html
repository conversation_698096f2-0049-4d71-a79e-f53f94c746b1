{% extends "base.html" %}

{% block content %}
<div class="page-container">
    <div class="page-header">
        <h1>{{ _('search_shipping_options') }}</h1>
        <p>{{ _('find_best_logistics_providers') }}</p>
    </div>

    <!-- Search Form -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="search-card">
                <form method="POST" action="/customer/search-shipping">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="origin_country">{{ _('origin_country') }}</label>
                                <select class="form-select" name="origin_country" id="origin_country" required>
                                    <option value="">{{ _('select_origin_country') }}</option>
                                    {% for country in countries %}
                                        <option value="{{ country }}">{{ country }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="origin_city">Origin City</label>
                                <input type="text" class="form-control" name="origin_city" id="origin_city" placeholder="Enter origin city">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="destination_country">{{ _('destination_country') }}</label>
                                <select class="form-select" name="destination_country" id="destination_country" required>
                                    <option value="">{{ _('select_destination_country') }}</option>
                                    {% for country in countries %}
                                        <option value="{{ country }}">{{ country }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="destination_city">Destination City</label>
                                <input type="text" class="form-control" name="destination_city" id="destination_city" placeholder="Enter destination city">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="provider_type">Provider Type</label>
                                <select class="form-select" name="provider_type" id="provider_type">
                                    <option value="all">All Providers</option>
                                    <option value="logistics">🚛 Logistics Companies</option>
                                    <option value="driver">👤 Individual Drivers</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="transport_type">Transport Type</label>
                                <select class="form-select" name="transport_type" id="transport_type">
                                    <option value="">Any Transport</option>
                                    <option value="truck">Truck</option>
                                    <option value="ship">Ship</option>
                                    <option value="air">Air</option>
                                    <option value="rail">Rail</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>&nbsp;</label>
                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-primary flex-fill">
                                        <i class="fas fa-search"></i> Search Routes
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary" onclick="clearFilters()">
                                        <i class="fas fa-times"></i> Clear
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- US States Row (Hidden by default) -->
                    <div class="row" id="us_states_row" style="display: none;">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="origin_state">Origin State</label>
                                <select class="form-select" name="origin_state" id="origin_state">
                                    <option value="">Select State</option>
                                    {% for state in us_states %}
                                    <option value="{{ state }}">{{ state }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="destination_state">Destination State</label>
                                <select class="form-select" name="destination_state" id="destination_state">
                                    <option value="">Select State</option>
                                    {% for state in us_states %}
                                    <option value="{{ state }}">{{ state }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-12">&nbsp;</div>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="transport_type">Transport Type</label>
                                <select class="form-select" name="transport_type" id="transport_type">
                                    <option value="">Any transport type</option>
                                    <option value="AIR">Air Freight</option>
                                    <option value="SEA">Sea Freight</option>
                                    <option value="LAND">Road Transportation</option>
                                    <option value="RAIL">Rail Transport</option>
                                </select>
                            </div>
                        </div>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="cargo_weight">Cargo Weight (kg)</label>
                                <input type="number" class="form-control" name="cargo_weight" id="cargo_weight" min="1" placeholder="Enter weight">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="cargo_dimensions">Dimensions (L×W×H cm)</label>
                                <input type="text" class="form-control" name="cargo_dimensions" id="cargo_dimensions" placeholder="100×50×30">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="cargo_type">Cargo Type</label>
                                <select class="form-select" name="cargo_type" id="cargo_type">
                                    <option value="GENERAL">General Cargo</option>
                                    <option value="FRAGILE">Fragile Items</option>
                                    <option value="HAZARDOUS">Hazardous Materials</option>
                                    <option value="PERISHABLE">Perishable Goods</option>
                                    <option value="ELECTRONICS">Electronics</option>
                                    <option value="TEXTILES">Textiles</option>
                                    <option value="MACHINERY">Machinery</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="urgency">Urgency Level</label>
                                <select class="form-select" name="urgency" id="urgency">
                                    <option value="STANDARD">Standard</option>
                                    <option value="EXPRESS">Express</option>
                                    <option value="URGENT">Urgent</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="text-center">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-search"></i> Search Routes
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Search Results -->
    {% if routes %}
    <div class="row">
        <div class="col-12">
            <div class="results-header">
                <h4>Available Routes ({{ routes|length }} found)</h4>
                <div class="sort-options">
                    <select class="form-select form-select-sm" onchange="sortResults(this.value)">
                        <option value="price">Sort by Price</option>
                        <option value="time">Sort by Transit Time</option>
                        <option value="rating">Sort by Rating</option>
                    </select>
                </div>
            </div>
        </div>
    </div>

    <div class="row" id="routes-container">
        {% for route in routes %}
        <div class="col-lg-6 mb-4">
            <div class="route-card">
                <div class="route-header">
                    <div class="provider-info">
                        <h5>{{ route.provider_name }}</h5>
                        <div class="rating">
                            {% for i in range(5) %}
                                {% if i < route.rating %}
                                    <i class="fas fa-star text-warning"></i>
                                {% else %}
                                    <i class="far fa-star text-muted"></i>
                                {% endif %}
                            {% endfor %}
                            <span class="rating-text">({{ route.rating }}/5)</span>
                        </div>
                    </div>
                    <div class="price-tag">
                        <span class="price">${{ route.price_per_kg }}</span>
                        <span class="unit">/kg</span>
                    </div>
                </div>
                
                <div class="route-details">
                    <div class="route-path">
                        <i class="fas fa-map-marker-alt text-success"></i>
                        <span>{{ route.origin_country }}</span>
                        <i class="fas fa-arrow-right mx-2"></i>
                        <i class="fas fa-map-marker-alt text-danger"></i>
                        <span>{{ route.destination_country }}</span>
                    </div>
                    
                    <div class="route-specs">
                        <div class="spec-item">
                            <i class="fas fa-truck"></i>
                            <span>{{ route.transport_type }}</span>
                        </div>
                        <div class="spec-item">
                            <i class="fas fa-clock"></i>
                            <span>{{ route.transit_time }} days</span>
                        </div>
                        <div class="spec-item">
                            <i class="fas fa-weight"></i>
                            <span>Max {{ route.max_weight }}kg</span>
                        </div>
                    </div>
                    
                    <div class="route-features">
                        {% if route.insurance_included %}
                        <span class="feature-badge">
                            <i class="fas fa-shield-alt"></i> Insurance Included
                        </span>
                        {% endif %}
                        {% if route.tracking_available %}
                        <span class="feature-badge">
                            <i class="fas fa-satellite"></i> Real-time Tracking
                        </span>
                        {% endif %}
                        {% if route.customs_handling %}
                        <span class="feature-badge">
                            <i class="fas fa-file-alt"></i> Customs Handling
                        </span>
                        {% endif %}
                    </div>
                </div>
                
                <div class="route-actions">
                    <button class="btn btn-outline-primary" onclick="viewDetails('{{ route.id }}')">
                        <i class="fas fa-info-circle"></i> View Details
                    </button>
                    <button class="btn btn-primary" onclick="requestQuote('{{ route.id }}')">
                        <i class="fas fa-paper-plane"></i> Request Quote
                    </button>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    {% else %}
    <div class="empty-state">
        <i class="fas fa-search"></i>
        <h4>Find Your Perfect Shipping Route</h4>
        <p>Enter your shipping details above to discover available logistics providers and routes.</p>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_css %}
<style>
.search-card {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
}

.route-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.route-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
}

.route-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.provider-info h5 {
    margin: 0;
    color: #2c3e50;
    font-weight: 600;
}

.rating {
    margin-top: 0.25rem;
}

.rating-text {
    margin-left: 0.5rem;
    font-size: 0.9rem;
    color: #666;
}

.price-tag {
    text-align: right;
}

.price {
    font-size: 1.5rem;
    font-weight: 700;
    color: #27ae60;
}

.unit {
    font-size: 0.9rem;
    color: #666;
}

.route-path {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    font-weight: 500;
}

.route-specs {
    display: flex;
    gap: 1.5rem;
    margin-bottom: 1rem;
    flex-wrap: wrap;
}

.spec-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #666;
    font-size: 0.9rem;
}

.spec-item i {
    color: #3498db;
}

.route-features {
    margin-bottom: 1.5rem;
}

.feature-badge {
    display: inline-block;
    background: #e8f5e8;
    color: #27ae60;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
}

.route-actions {
    display: flex;
    gap: 0.75rem;
}

.route-actions .btn {
    flex: 1;
}

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.sort-options {
    min-width: 200px;
}

.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: #666;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 1rem;
    color: #ddd;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
function sortResults(criteria) {
    console.log('Sorting by:', criteria);
    
    const container = document.getElementById('routes-container');
    const routes = Array.from(container.children);
    
    routes.sort((a, b) => {
        switch(criteria) {
            case 'price':
                const priceA = parseFloat(a.querySelector('.price').textContent.replace('$', ''));
                const priceB = parseFloat(b.querySelector('.price').textContent.replace('$', ''));
                return priceA - priceB;
                
            case 'time':
                const timeA = parseInt(a.querySelector('.spec-item:nth-child(2) span').textContent);
                const timeB = parseInt(b.querySelector('.spec-item:nth-child(2) span').textContent);
                return timeA - timeB;
                
            case 'rating':
                const ratingA = parseFloat(a.querySelector('.rating-text').textContent.match(/\(([\d.]+)/)[1]);
                const ratingB = parseFloat(b.querySelector('.rating-text').textContent.match(/\(([\d.]+)/)[1]);
                return ratingB - ratingA; // Higher rating first
                
            default:
                return 0;
        }
    });
    
    // Clear container and append sorted routes
    container.innerHTML = '';
    routes.forEach(route => container.appendChild(route));
}

function viewDetails(routeId) {
    window.location.href = `/customer/route-details/${routeId}`;
}

function requestQuote(routeId) {
    window.location.href = `/customer/request-quote/${routeId}`;
}

// US States visibility logic
function checkUSSelection() {
    const originCountry = document.getElementById('origin_country');
    const destinationCountry = document.getElementById('destination_country');
    const statesRow = document.getElementById('us_states_row');
    
    if (!originCountry || !destinationCountry || !statesRow) {
        console.log('Missing elements:', {originCountry, destinationCountry, statesRow});
        return;
    }
    
    const originValue = originCountry.value;
    const destinationValue = destinationCountry.value;
    
    // Show states if either origin OR destination is United States
    if (originValue === 'United States' || destinationValue === 'United States') {
        statesRow.style.display = 'block';
        
        // Hide origin state dropdown if origin is not US
        const originStateDiv = document.querySelector('#us_states_row .col-md-3:first-child');
        if (originValue !== 'United States' && originStateDiv) {
            originStateDiv.style.display = 'none';
            const originStateSelect = document.getElementById('origin_state');
            if (originStateSelect) originStateSelect.value = '';
        } else if (originStateDiv) {
            originStateDiv.style.display = 'block';
        }
        
        // Hide destination state dropdown if destination is not US
        const destinationStateDiv = document.querySelector('#us_states_row .col-md-3:last-child');
        if (destinationValue !== 'United States' && destinationStateDiv) {
            destinationStateDiv.style.display = 'none';
            const destinationStateSelect = document.getElementById('destination_state');
            if (destinationStateSelect) destinationStateSelect.value = '';
        } else if (destinationStateDiv) {
            destinationStateDiv.style.display = 'block';
        }
    } else {
        statesRow.style.display = 'none';
        // Clear state selections when hiding
        const originStateSelect = document.getElementById('origin_state');
        const destinationStateSelect = document.getElementById('destination_state');
        if (originStateSelect) originStateSelect.value = '';
        if (destinationStateSelect) destinationStateSelect.value = '';
    }
}

// Initialize default sorting on page load
document.addEventListener('DOMContentLoaded', function() {
    const sortSelect = document.querySelector('.sort-options select');
    if (sortSelect) {
        sortSelect.addEventListener('change', function() {
            sortResults(this.value);
        });
    }
    
    // Add event listeners to country dropdowns
    document.getElementById('origin_country').addEventListener('change', checkUSSelection);
    document.getElementById('destination_country').addEventListener('change', checkUSSelection);
    
    // Check US selection on page load
    checkUSSelection();
});


// Auto-generated function implementations

function clearFilters() {
    // Filter functionality
    const filterPanel = document.getElementById('filterPanel') || document.querySelector('.filter-panel');
    if (filterPanel) {
        filterPanel.style.display = filterPanel.style.display === 'none' ? 'block' : 'none';
    }
    applyFilters();
}
</script>
{% endblock %}