<!-- Document Signing Component for RightSignature Integration -->
<div class="document-signing-widget">
    <div class="card">
        <div class="card-header">
            <h5 class="card-title">
                <i class="fas fa-pen-nib me-2"></i>
                Document Signing
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <div class="signing-info">
                        <h6>Ready for Signature</h6>
                        <p class="text-muted">Digital signature required for document completion</p>
                        <div class="document-details">
                            <p><strong>Document:</strong> <span id="document-name">{{ document_name }}</span></p>
                            <p><strong>Type:</strong> <span id="document-type">{{ document_type }}</span></p>
                            <p><strong>Status:</strong> <span id="signature-status" class="badge bg-warning">Pending</span></p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="signing-actions">
                        <button class="btn btn-primary btn-lg mb-3" onclick="startSigning()">
                            <i class="fas fa-signature me-2"></i>
                            Sign Document
                        </button>
                        <button class="btn btn-outline-info mb-3" onclick="previewDocument()">
                            <i class="fas fa-eye me-2"></i>
                            Preview Document
                        </button>
                        <button class="btn btn-outline-success mb-3" onclick="checkSigningStatus()">
                            <i class="fas fa-sync-alt me-2"></i>
                            Check Status
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Signing Progress -->
            <div class="signing-progress mt-3" id="signing-progress" style="display: none;">
                <div class="progress mb-3">
                    <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                </div>
                <div class="progress-steps">
                    <div class="step active" data-step="1">
                        <i class="fas fa-file-upload"></i>
                        <span>Upload</span>
                    </div>
                    <div class="step" data-step="2">
                        <i class="fas fa-envelope"></i>
                        <span>Send</span>
                    </div>
                    <div class="step" data-step="3">
                        <i class="fas fa-signature"></i>
                        <span>Sign</span>
                    </div>
                    <div class="step" data-step="4">
                        <i class="fas fa-download"></i>
                        <span>Complete</span>
                    </div>
                </div>
            </div>
            
            <!-- Signers List -->
            <div class="signers-list mt-3">
                <h6>Required Signers</h6>
                <div class="list-group">
                    {% for signer in signers %}
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <strong>{{ signer.name }}</strong>
                            <small class="text-muted d-block">{{ signer.email }}</small>
                        </div>
                        <div>
                            <span class="badge bg-{{ 'success' if signer.signed else 'warning' }}">
                                {{ 'Signed' if signer.signed else 'Pending' }}
                            </span>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Document Signing JavaScript Functions
function startSigning() {
    const documentType = document.getElementById('document-type').textContent;
    const documentPath = '{{ document_path }}';
    const documentId = '{{ document_id }}';
    
    // Show progress
    document.getElementById('signing-progress').style.display = 'block';
    updateProgress(25);
    
    // Create signature request
    const signatureData = {
        document_type: documentType.toLowerCase(),
        document_path: documentPath,
        document_id: documentId,
        signers: [
            {% for signer in signers %}
            {
                email: '{{ signer.email }}',
                name: '{{ signer.name }}',
                role: '{{ signer.role }}'
            }{% if not loop.last %},{% endif %}
            {% endfor %}
        ]
    };
    
    fetch('/api/document/sign', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(signatureData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            alert('Error: ' + data.error);
            document.getElementById('signing-progress').style.display = 'none';
        } else {
            updateProgress(75);
            document.getElementById('signature-status').textContent = 'Sent for Signature';
            document.getElementById('signature-status').className = 'badge bg-info';
            
            // Store signature request ID for status checking
            localStorage.setItem('signatureRequestId', data.signature_request_id);
            
            // Show success message
            setTimeout(() => {
                updateProgress(100);
                alert('Document sent for signature successfully!');
            }, 1000);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error sending document for signature');
        document.getElementById('signing-progress').style.display = 'none';
    });
}

function previewDocument() {
    const documentPath = '{{ document_path }}';
    
    if (documentPath) {
        // Open document in new tab for preview
        window.open(documentPath, '_blank');
    } else {
        alert('Document not available for preview');
    }
}

function checkSigningStatus() {
    const signatureRequestId = localStorage.getItem('signatureRequestId');
    
    if (!signatureRequestId) {
        alert('No signature request found. Please start signing process first.');
        return;
    }
    
    fetch(`/api/document/status/${signatureRequestId}`)
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            alert('Error checking status: ' + data.error);
        } else {
            const status = data.status;
            const statusElement = document.getElementById('signature-status');
            
            statusElement.textContent = status.charAt(0).toUpperCase() + status.slice(1);
            
            if (status === 'signed') {
                statusElement.className = 'badge bg-success';
                alert('Document has been signed successfully!');
                
                // Show download button
                const downloadBtn = document.createElement('button');
                downloadBtn.className = 'btn btn-outline-success ms-2';
                downloadBtn.innerHTML = '<i class="fas fa-download me-2"></i>Download';
                downloadBtn.onclick = () => downloadSignedDocument(signatureRequestId);
                document.querySelector('.signing-actions').appendChild(downloadBtn);
                
            } else if (status === 'declined') {
                statusElement.className = 'badge bg-danger';
                alert('Document signing was declined.');
            } else if (status === 'expired') {
                statusElement.className = 'badge bg-secondary';
                alert('Document signature request has expired.');
            } else {
                statusElement.className = 'badge bg-warning';
            }
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error checking signature status');
    });
}

function downloadSignedDocument(signatureRequestId) {
    window.open(`/api/document/download/${signatureRequestId}`, '_blank');
}

function updateProgress(percentage) {
    const progressBar = document.querySelector('.progress-bar');
    progressBar.style.width = percentage + '%';
    
    // Update step indicators
    const steps = document.querySelectorAll('.step');
    steps.forEach((step, index) => {
        const stepNumber = index + 1;
        if (stepNumber <= percentage / 25) {
            step.classList.add('active');
        }
    });
}

// Auto-check status on page load if signature request exists
document.addEventListener('DOMContentLoaded', function() {
    const signatureRequestId = localStorage.getItem('signatureRequestId');
    if (signatureRequestId) {
        setTimeout(checkSigningStatus, 2000);
    }
});
</script>

<style>
/* Document Signing Component Styles */
.document-signing-widget {
    max-width: 100%;
    margin: 20px 0;
}

.signing-info h6 {
    color: #2c3e50;
    margin-bottom: 10px;
}

.document-details p {
    margin-bottom: 8px;
    font-size: 14px;
}

.signing-actions {
    text-align: center;
}

.signing-actions .btn {
    display: block;
    width: 100%;
    margin-bottom: 10px;
}

.signing-progress {
    border-top: 1px solid #dee2e6;
    padding-top: 20px;
}

.progress-steps {
    display: flex;
    justify-content: space-between;
    margin-top: 15px;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #6c757d;
    font-size: 12px;
}

.step.active {
    color: #007bff;
}

.step i {
    font-size: 18px;
    margin-bottom: 5px;
}

.signers-list {
    border-top: 1px solid #dee2e6;
    padding-top: 20px;
}

.signers-list h6 {
    color: #2c3e50;
    margin-bottom: 15px;
}

.list-group-item {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    margin-bottom: 8px;
}

/* Responsive design */
@media (max-width: 768px) {
    .signing-actions .btn {
        margin-bottom: 15px;
    }
    
    .progress-steps {
        flex-wrap: wrap;
    }
    
    .step {
        width: 50%;
        margin-bottom: 15px;
    }
}
</style>