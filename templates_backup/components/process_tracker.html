<!-- Process Tracker Component - Universal shipment process visualization -->
<div class="process-tracker mb-4" data-shipment-id="{{ shipment.id }}">
    <div class="card">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">
                <i class="fas fa-route"></i> 
                Shipment Process Flow - {{ shipment.tracking_number }}
                <span class="badge badge-light ml-2">{{ shipment.get_origin_type_display }}</span>
            </h5>
        </div>
        <div class="card-body">
            <!-- Progress Bar -->
            <div class="progress mb-3" style="height: 8px;">
                <div class="progress-bar bg-success" 
                     role="progressbar" 
                     style="width: {{ progress_percentage }}%"
                     aria-valuenow="{{ progress_percentage }}" 
                     aria-valuemin="0" 
                     aria-valuemax="100">
                </div>
            </div>
            
            <!-- Stage Timeline -->
            <div class="stage-timeline">
                {% set all_stages = [
                    ('created', 'Shipment Created', 'fas fa-file-alt'),
                    ('contract', 'Contract Signing', 'fas fa-file-signature'),
                    ('payment', 'Payment Processing', 'fas fa-credit-card'),
                    ('container', 'Container Assignment', 'fas fa-shipping-fast'),
                    ('driver', 'Driver Assignment', 'fas fa-truck'),
                    ('customs', 'Customs Declaration', 'fas fa-passport'),
                    ('insurance', 'Insurance Handling', 'fas fa-shield-alt'),
                    ('trip', 'Trip Progress', 'fas fa-route'),
                    ('delivery', 'Delivery Confirmation', 'fas fa-box'),
                    ('feedback', 'Rating & Feedback', 'fas fa-star'),
                    ('completed', 'Completed', 'fas fa-check-circle')
                ] %}
                
                <div class="row">
                    {% for stage_code, stage_name, icon in all_stages %}
                        {% set is_current = shipment.process_stage == stage_code %}
                        {% set is_completed = stage_code in completed_stages %}
                        {% set is_next = stage_code in next_stages %}
                        
                        <div class="col-lg-2 col-md-3 col-sm-4 col-6 mb-3">
                            <div class="stage-item text-center 
                                {% if is_current %}stage-current{% elif is_completed %}stage-completed{% elif is_next %}stage-next{% else %}stage-pending{% endif %}">
                                
                                <div class="stage-icon mb-2">
                                    <i class="{{ icon }} 
                                       {% if is_current %}text-warning{% elif is_completed %}text-success{% elif is_next %}text-info{% else %}text-muted{% endif %}"></i>
                                </div>
                                
                                <div class="stage-title">
                                    <small class="font-weight-bold 
                                           {% if is_current %}text-warning{% elif is_completed %}text-success{% elif is_next %}text-info{% else %}text-muted{% endif %}">
                                        {{ stage_name }}
                                    </small>
                                </div>
                                
                                {% if stage_code in (stage_timestamps or {}) %}
                                    <div class="stage-time">
                                        <small class="text-muted">
                                            {% if stage_timestamps[stage_code] and stage_timestamps[stage_code].__class__.__name__ != 'str' %}
                                                {{ stage_timestamps[stage_code].strftime('%b %d, %H:%M') }}
                                            {% else %}
                                                {{ stage_timestamps[stage_code] if stage_timestamps[stage_code] else 'Not set' }}
                                            {% endif %}
                                        </small>
                                    </div>
                                {% endif %}
                                
                                {% if is_current %}
                                    <div class="current-indicator mt-1">
                                        <span class="badge badge-warning pulse">Current</span>
                                    </div>
                                {% elif is_completed %}
                                    <div class="completed-indicator mt-1">
                                        <span class="badge badge-success">
                                            <i class="fas fa-check"></i>
                                        </span>
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    {% endfor %}
                </div>
            </div>
            
            <!-- Action Buttons for Current Stage -->
            {% if next_stages and user.user_type in ['CUSTOMER', 'LOGISTICS_PROVIDER', 'ADMIN'] %}
                <div class="stage-actions mt-4 p-3 bg-light rounded">
                    <h6 class="text-primary mb-3">
                        <i class="fas fa-tasks"></i> Available Actions for {{ shipment.get_process_stage_display }}
                    </h6>
                    
                    <div class="row">
                        {% for next_stage in next_stages %}
                            <div class="col-md-6 col-lg-4 mb-2">
                                <button class="btn btn-primary btn-sm w-100 stage-action-btn"
                                        data-stage="{{ next_stage }}"
                                        data-shipment-id="{{ shipment.id }}"
                                        onclick="showStageActionModal('{{ next_stage }}', 'advance')">
                                    <i class="fas fa-arrow-right"></i>
                                    Advance to {{ next_stage|title }}
                                </button>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            {% endif %}
            
            <!-- Process Information Panel -->
            <div class="process-info mt-4">
                <div class="row">
                    <div class="col-md-4">
                        <div class="info-card">
                            <small class="text-muted">Origin Type</small>
                            <div class="font-weight-bold">{{ shipment.get_origin_type_display }}</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="info-card">
                            <small class="text-muted">Current Stage</small>
                            <div class="font-weight-bold text-primary">{{ shipment.get_process_stage_display }}</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="info-card">
                            <small class="text-muted">Progress</small>
                            <div class="font-weight-bold text-success">{{ progress_percentage }}% Complete</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.process-tracker .stage-item {
    transition: all 0.3s ease;
    padding: 10px;
    border-radius: 8px;
}

.process-tracker .stage-current {
    background-color: #fff3cd;
    border: 2px solid #ffc107;
}

.process-tracker .stage-completed {
    background-color: #d4edda;
    border: 2px solid #28a745;
}

.process-tracker .stage-next {
    background-color: #d1ecf1;
    border: 2px solid #17a2b8;
}

.process-tracker .stage-pending {
    background-color: #f8f9fa;
    border: 2px solid #dee2e6;
}

.process-tracker .stage-icon i {
    font-size: 1.5rem;
}

.process-tracker .pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.process-tracker .info-card {
    background: #f8f9fa;
    padding: 10px;
    border-radius: 5px;
    margin-bottom: 10px;
}

.process-tracker .stage-actions {
    border-left: 4px solid #007bff;
}
</style>
<script>
// Auto-generated function implementations

function showStageActionModal(id) {
    // View functionality
    const viewUrl = `/view/${id || 'item'}`;
    window.open(viewUrl, '_blank');
}
</script>
