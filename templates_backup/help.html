{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <h1><i class="fas fa-question-circle"></i> Help Center</h1>
                <p class="text-muted">Find answers to common questions and learn how to use Cloverics</p>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Search Help -->
        <div class="col-12 mb-4">
            <div class="card">
                <div class="card-body">
                    <div class="input-group">
                        <input type="text" class="form-control form-control-lg" placeholder="Search help articles..." id="helpSearch">
                        <div class="input-group-append">
                            <button class="btn btn-primary" onclick="searchHelp()">
                                <i class="fas fa-search"></i> Search
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Help Categories -->
        <div class="col-md-3">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-list"></i> Categories</h5>
                </div>
                <div class="list-group list-group-flush">
                    {% for topic in help_topics %}
                    <a href="#{{ topic.category|lower|replace(' ', '-') }}" class="list-group-item list-group-item-action" onclick="showCategory('{{ topic.category|lower|replace(' ', '-') }}')">
                        <i class="fas fa-folder"></i> {{ topic.category }}
                        <span class="badge badge-secondary float-right">{{ topic.articles|length }}</span>
                    </a>
                    {% endfor %}
                </div>
            </div>

            <!-- Quick Contact -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5><i class="fas fa-headset"></i> Need More Help?</h5>
                </div>
                <div class="card-body">
                    <p class="text-muted">Can't find what you're looking for?</p>
                    <a href="/contact" class="btn btn-primary btn-block">
                        <i class="fas fa-envelope"></i> Contact Support
                    </a>
                    <a href="tel:******-CLOVER" class="btn btn-outline-primary btn-block">
                        <i class="fas fa-phone"></i> Call Support
                    </a>
                    <small class="text-muted d-block mt-2">
                        <i class="fas fa-clock"></i> Support Hours: 24/7
                    </small>
                </div>
            </div>
        </div>

        <!-- Help Content -->
        <div class="col-md-9">
            {% for topic in help_topics %}
            <div class="help-category" id="{{ topic.category|lower|replace(' ', '-') }}">
                <div class="card">
                    <div class="card-header">
                        <h4><i class="fas fa-folder-open"></i> {{ topic.category }}</h4>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            {% for article in topic.articles %}
                            <div class="col-md-6 mb-3">
                                <div class="help-article" onclick="openArticle('{{ article.title }}')">
                                    <h6><i class="fas fa-file-alt"></i> {{ article.title }}</h6>
                                    <p class="text-muted">Click to read this article</p>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}

            <!-- FAQ Section -->
            <div class="card mt-4">
                <div class="card-header">
                    <h4><i class="fas fa-question-circle"></i> Frequently Asked Questions</h4>
                </div>
                <div class="card-body">
                    <div class="accordion" id="faqAccordion">
                        <div class="card">
                            <div class="card-header" id="faq1">
                                <h2 class="mb-0">
                                    <button class="btn btn-link" type="button" data-toggle="collapse" data-target="#faqAnswer1" onclick="showFAQAnswer('tracking')">
                                        How do I track my shipment?
                                    </button>
                                </h2>
                            </div>
                            <div id="faqAnswer1" class="collapse show" data-parent="#faqAccordion">
                                <div class="card-body">
                                    You can track your shipment by entering your tracking number on the <a href="/customer/track-shipment">Track Shipment</a> page. You'll receive real-time updates on your shipment's location and status.
                                </div>
                            </div>
                        </div>

                        <div class="card">
                            <div class="card-header" id="faq2">
                                <h2 class="mb-0">
                                    <button class="btn btn-link collapsed" type="button" data-toggle="collapse" data-target="#faqAnswer2" onclick="showFAQAnswer('payments')">
                                        What payment methods do you accept?
                                    </button>
                                </h2>
                            </div>
                            <div id="faqAnswer2" class="collapse" data-parent="#faqAccordion">
                                <div class="card-body">
                                    We accept all major credit cards (Visa, Mastercard, American Express), PayPal, bank transfers, and for enterprise clients, we offer net payment terms.
                                </div>
                            </div>
                        </div>

                        <div class="card">
                            <div class="card-header" id="faq3">
                                <h2 class="mb-0">
                                    <button class="btn btn-link collapsed" type="button" data-toggle="collapse" data-target="#faqAnswer3" onclick="showFAQAnswer('quotes')">
                                        How do I get a shipping quote?
                                    </button>
                                </h2>
                            </div>
                            <div id="faqAnswer3" class="collapse" data-parent="#faqAccordion">
                                <div class="card-body">
                                    Visit our <a href="/customer/search-shipping">Search Shipping</a> page, enter your shipment details, and we'll provide instant quotes from multiple logistics providers.
                                </div>
                            </div>
                        </div>

                        <div class="card">
                            <div class="card-header" id="faq4">
                                <h2 class="mb-0">
                                    <button class="btn btn-link collapsed" type="button" data-toggle="collapse" data-target="#faqAnswer4" onclick="showFAQAnswer('delays')">
                                        What if my shipment is delayed?
                                    </button>
                                </h2>
                            </div>
                            <div id="faqAnswer4" class="collapse" data-parent="#faqAccordion">
                                <div class="card-body">
                                    If your shipment is delayed, you'll receive automatic notifications. You can also contact the logistics provider directly through our messaging system or reach out to our support team for assistance.
                                </div>
                            </div>
                        </div>

                        <div class="card">
                            <div class="card-header" id="faq5">
                                <h2 class="mb-0">
                                    <button class="btn btn-link collapsed" type="button" data-toggle="collapse" data-target="#faqAnswer5" onclick="showFAQAnswer('provider')">
                                        How do I become a logistics provider?
                                    </button>
                                </h2>
                            </div>
                            <div id="faqAnswer5" class="collapse" data-parent="#faqAccordion">
                                <div class="card-body">
                                    To join our network as a logistics provider, contact our business development team. We'll guide you through the registration, verification, and onboarding process.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Video Tutorials -->
            <div class="card mt-4">
                <div class="card-header">
                    <h4><i class="fas fa-play-circle"></i> Video Tutorials</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <div class="video-tutorial" onclick="playVideo('getting-started')">
                                <div class="video-thumbnail">
                                    <i class="fas fa-play-circle fa-3x"></i>
                                    <h6>Getting Started with Cloverics</h6>
                                    <small class="text-muted">5:30 minutes</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="video-tutorial" onclick="playVideo('shipping-quote')">
                                <div class="video-thumbnail">
                                    <i class="fas fa-play-circle fa-3x"></i>
                                    <h6>How to Get a Shipping Quote</h6>
                                    <small class="text-muted">3:15 minutes</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="video-tutorial" onclick="playVideo('tracking')">
                                <div class="video-thumbnail">
                                    <i class="fas fa-play-circle fa-3x"></i>
                                    <h6>Tracking Your Shipments</h6>
                                    <small class="text-muted">2:45 minutes</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.help-article {
    padding: 15px;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    cursor: pointer;
    transition: all 0.2s;
    height: 100%;
}

.help-article:hover {
    border-color: #007bff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.help-category {
    margin-bottom: 2rem;
}

.video-tutorial {
    cursor: pointer;
    text-align: center;
    padding: 20px;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    transition: all 0.2s;
    height: 100%;
}

.video-tutorial:hover {
    border-color: #007bff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.video-thumbnail {
    color: #007bff;
}

.list-group-item {
    border: none;
    padding: 12px 15px;
}

.accordion .card {
    border: 1px solid #dee2e6;
    margin-bottom: 5px;
}

.accordion .btn-link {
    text-decoration: none;
    color: #495057;
    font-weight: 500;
}

.accordion .btn-link:hover {
    text-decoration: none;
    color: #007bff;
}
</style>

<script>
function searchHelp() {
    const searchTerm = document.getElementById('helpSearch').value.toLowerCase();
    if (searchTerm.trim() === '') {
        showAlert('Please enter a search term', 'warning');
        return;
    }
    
    // Mock search functionality
    showAlert(`Searching for "${searchTerm}"...`, 'info');
    
    // Simulate search results
    setTimeout(() => {
        showAlert(`Found 3 articles matching "${searchTerm}"`, 'success');
    }, 1000);
}

function showCategory(categoryId) {
    // Hide all categories
    const categories = document.querySelectorAll('.help-category');
    categories.forEach(cat => cat.style.display = 'none');
    
    // Show selected category
    const targetCategory = document.getElementById(categoryId);
    if (targetCategory) {
        targetCategory.style.display = 'block';
        targetCategory.scrollIntoView({ behavior: 'smooth' });
    }
    
    // Update navigation
    const navItems = document.querySelectorAll('.list-group-item');
    navItems.forEach(item => item.classList.remove('active'));
    event.target.classList.add('active');
}

function openArticle(title) {
    showAlert(`Opening article: "${title}"`, 'info');
    // In a real implementation, this would open the full article
}

function playVideo(videoId) {
    showAlert(`Playing video: ${videoId}`, 'info');
    // In a real implementation, this would open a video player
}

function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="close" data-dismiss="alert">
            <span>&times;</span>
        </button>
    `;
    
    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);
    
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}

// Show all categories by default
document.addEventListener('DOMContentLoaded', function() {
    const categories = document.querySelectorAll('.help-category');
    categories.forEach(cat => cat.style.display = 'block');
});


// Auto-generated button handlers

function showFAQAnswer(category) {
    // Show FAQ answer
    const answers = {
        'tracking': 'You can track your shipment using the tracking number provided in your email confirmation. Go to "Track Shipment" page and enter your tracking number.',
        'payments': 'We accept credit cards, bank transfers, and digital payments through our secure payment gateway.',
        'quotes': 'Click "Search Shipping" to get instant quotes from verified logistics providers. Compare prices and services to choose the best option.',
        'delays': 'If your shipment is delayed, contact your logistics provider directly through the platform. You can also file a claim if needed.',
        'provider': 'To become a logistics provider, click "Register as Provider" and complete the verification process with your company documents.'
    };
    
    showAlert(answers[category] || 'Information not available', 'info');
}
</script>
{% endblock %}