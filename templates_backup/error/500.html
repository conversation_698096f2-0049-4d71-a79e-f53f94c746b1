{% extends "base.html" %}

{% block title %}System Error - Cloverics{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-body text-center">
                    <div class="error-icon mb-4">
                        <i class="fas fa-exclamation-triangle fa-4x text-warning"></i>
                    </div>
                    <h2 class="card-title">System Error</h2>
                    <p class="card-text text-muted">
                        We encountered an unexpected error while processing your request.
                    </p>
                    <div class="mt-4">
                        <a href="/dashboard" class="btn btn-primary">
                            <i class="fas fa-home"></i> Return to Dashboard
                        </a>
                        <a href="/" class="btn btn-outline-secondary">
                            <i class="fas fa-globe"></i> Go to Homepage
                        </a>
                    </div>
                    {% if error %}
                    <div class="mt-4">
                        <details>
                            <summary>Technical Details</summary>
                            <pre class="text-left mt-2">{{ error }}</pre>
                        </details>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}