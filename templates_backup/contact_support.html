{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <h1><i class="fas fa-headset"></i> Contact & Support</h1>
                <p class="text-muted">Get help with your Cloverics platform experience</p>
            </div>
        </div>
    </div>

    <!-- Role-specific Support Categories -->
    {% if support_categories %}
    <div class="row mb-5">
        <div class="col-12">
            <h3><i class="fas fa-user-tag"></i> {{ user.user_type.replace('_', ' ').title() }} Support</h3>
            <p class="text-muted mb-4">Support categories specifically designed for your role</p>
            <div class="row">
                {% for category in support_categories %}
                <div class="col-md-6 col-lg-3 mb-4">
                    <div class="support-category-card">
                        <div class="support-icon">
                            <i class="{{ category.icon }} fa-2x"></i>
                        </div>
                        <h5>{{ category.title }}</h5>
                        <p>{{ category.description }}</p>
                        <button class="btn btn-outline-primary btn-sm" onclick="openSupportModal('{{ category.title }}')">
                            Get Help
                        </button>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Common Support Options -->
    <div class="row mb-5">
        <div class="col-12">
            <h3><i class="fas fa-question-circle"></i> General Support</h3>
            <p class="text-muted mb-4">Common support options for all users</p>
            <div class="row">
                {% for support in common_support %}
                <div class="col-md-6 col-lg-3 mb-4">
                    <div class="support-category-card">
                        <div class="support-icon">
                            <i class="{{ support.icon }} fa-2x"></i>
                        </div>
                        <h5>{{ support.title }}</h5>
                        <p>{{ support.description }}</p>
                        <button class="btn btn-outline-success btn-sm" onclick="openSupportModal('{{ support.title }}')">
                            Access
                        </button>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>

    <!-- Quick Contact Form -->
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-envelope"></i> Quick Contact Form</h5>
                </div>
                <div class="card-body">
                    <form id="contactForm" method="POST" action="/contact/submit">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Your Name *</label>
                                    <input type="text" class="form-control" name="name" value="{{ user.first_name }} {{ user.last_name }}" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Email Address *</label>
                                    <input type="email" class="form-control" name="email" value="{{ user.email }}" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Subject *</label>
                            <select class="form-control" name="subject" required>
                                <option value="">Select a topic...</option>
                                <option value="Technical Support">Technical Support</option>
                                <option value="Billing Question">Billing Question</option>
                                <option value="Feature Request">Feature Request</option>
                                <option value="Bug Report">Bug Report</option>
                                <option value="Account Issue">Account Issue</option>
                                <option value="General Inquiry">General Inquiry</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Priority Level</label>
                            <select class="form-control" name="priority">
                                <option value="low">Low - General question</option>
                                <option value="medium" selected>Medium - Need assistance</option>
                                <option value="high">High - Urgent issue</option>
                                <option value="critical">Critical - Service down</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Message *</label>
                            <textarea class="form-control" name="message" rows="5" required placeholder="Please describe your issue or question in detail..."></textarea>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-paper-plane"></i> Send Message
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Contact Information -->
    <div class="row mt-5">
        <div class="col-12">
            <div class="contact-info-section">
                <div class="row">
                    <div class="col-md-4 text-center">
                        <div class="contact-info-card">
                            <i class="fas fa-phone fa-2x text-primary mb-3"></i>
                            <h5>Phone Support</h5>
                            <p>+1 (555) 123-CLOVER<br>
                            Monday - Friday: 8 AM - 8 PM EST<br>
                            Saturday: 9 AM - 5 PM EST</p>
                        </div>
                    </div>
                    <div class="col-md-4 text-center">
                        <div class="contact-info-card">
                            <i class="fas fa-envelope fa-2x text-success mb-3"></i>
                            <h5>Email Support</h5>
                            <p><EMAIL><br>
                            Response within 24 hours<br>
                            Priority support available</p>
                        </div>
                    </div>
                    <div class="col-md-4 text-center">
                        <div class="contact-info-card">
                            <i class="fas fa-comments fa-2x text-info mb-3"></i>
                            <h5>Live Chat</h5>
                            <p>Available 24/7<br>
                            Instant responses<br>
                            Click the chat icon below</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Support Modal -->
<div class="modal fade" id="supportModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Support Information</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="modalContent">
                    <!-- Content will be populated by JavaScript -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" onclick="startLiveChat()">Start Live Chat</button>
            </div>
        </div>
    </div>
</div>

<script>
function openSupportModal(category) {
    const modal = new bootstrap.Modal(document.getElementById('supportModal'));
    const modalContent = document.getElementById('modalContent');
    
    // Support content based on category
    const supportContent = {
        'Shipping & Tracking': `
            <h6>Shipping & Tracking Support</h6>
            <ul>
                <li>Track your shipments in real-time</li>
                <li>Get delivery updates and notifications</li>
                <li>Report delivery issues</li>
                <li>Update delivery addresses</li>
            </ul>
            <p><strong>Average response time:</strong> 2 hours</p>
        `,
        'Billing & Payments': `
            <h6>Billing & Payment Support</h6>
            <ul>
                <li>Payment method issues</li>
                <li>Invoice questions</li>
                <li>Refund requests</li>
                <li>Billing disputes</li>
            </ul>
            <p><strong>Average response time:</strong> 4 hours</p>
        `,
        'General FAQ': `
            <h6>Frequently Asked Questions</h6>
            <ul>
                <li>How to create a shipment</li>
                <li>Understanding pricing</li>
                <li>Account setup and verification</li>
                <li>Platform navigation tips</li>
            </ul>
            <p><strong>Available:</strong> 24/7 instant access</p>
        `,
        'Live Support': `
            <h6>Live Support</h6>
            <p>Connect with our support team for immediate assistance with any questions or issues.</p>
            <ul>
                <li>Real-time chat support</li>
                <li>Screen sharing available</li>
                <li>Priority queue for urgent issues</li>
            </ul>
            <p><strong>Available:</strong> 24/7</p>
        `
    };
    
    modalContent.innerHTML = supportContent[category] || `
        <h6>${category} Support</h6>
        <p>Our support team is ready to help you with ${category.toLowerCase()} related questions and issues.</p>
        <p>Please use the contact form below or start a live chat for immediate assistance.</p>
    `;
    
    modal.show();
}

function startLiveChat() {
    // Simulate live chat functionality
    alert('Live chat feature will be available soon! Please use the contact form or call our support line.');
}

// Form submission handling
document.getElementById('contactForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const submitButton = this.querySelector('button[type="submit"]');
    const originalText = submitButton.innerHTML;
    
    // Show loading state
    submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Sending...';
    submitButton.disabled = true;
    
    // Submit to server
    fetch('/contact/submit', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        // Remove any existing alerts
        const existingAlert = document.querySelector('.alert');
        if (existingAlert) {
            existingAlert.remove();
        }
        
        // Show response message
        const alertDiv = document.createElement('div');
        if (data.status === 'success') {
            alertDiv.className = 'alert alert-success alert-dismissible fade show';
            alertDiv.innerHTML = `
                <i class="fas fa-check-circle"></i> <strong>Message sent successfully!</strong> 
                Your ticket number is ${data.ticket_number}. We'll get back to you within 24 hours.
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            // Reset form on success
            this.reset();
        } else {
            alertDiv.className = 'alert alert-danger alert-dismissible fade show';
            alertDiv.innerHTML = `
                <i class="fas fa-exclamation-triangle"></i> <strong>Error:</strong> 
                ${data.message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
        }
        
        // Insert the alert at the top of the form
        const form = document.getElementById('contactForm');
        form.insertBefore(alertDiv, form.firstChild);
        
        // Auto-hide success alert after 8 seconds
        if (data.status === 'success') {
            setTimeout(() => {
                alertDiv.remove();
            }, 8000);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        
        // Show error message
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-danger alert-dismissible fade show';
        alertDiv.innerHTML = `
            <i class="fas fa-exclamation-triangle"></i> <strong>Connection Error:</strong> 
            Unable to send message. Please try again or contact us directly.
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        const form = document.getElementById('contactForm');
        form.insertBefore(alertDiv, form.firstChild);
    })
    .finally(() => {
        // Restore button state
        submitButton.innerHTML = originalText;
        submitButton.disabled = false;
    });
});
</script>

<style>
.page-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e9ecef;
}

.page-header h1 {
    color: #2c3e50;
    font-weight: 600;
}

.support-category-card {
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 10px;
    padding: 1.5rem;
    text-align: center;
    height: 100%;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.support-category-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    border-color: #007bff;
}

.support-icon {
    color: #007bff;
    margin-bottom: 1rem;
}

.support-category-card h5 {
    color: #2c3e50;
    margin-bottom: 1rem;
}

.support-category-card p {
    color: #6c757d;
    margin-bottom: 1.5rem;
    font-size: 0.9rem;
}

.contact-info-section {
    background: #f8f9fa;
    padding: 3rem 2rem;
    border-radius: 10px;
    margin-top: 2rem;
}

.contact-info-card {
    padding: 1.5rem;
}

.contact-info-card h5 {
    color: #2c3e50;
    margin-bottom: 1rem;
}

.contact-info-card p {
    color: #6c757d;
    margin: 0;
    line-height: 1.6;
}

.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
</style>
{% endblock %}