{% extends "base.html" %}

{% block content %}
<div class="dashboard-container">
    <div class="dashboard-header">
        <h1>Earnings Dashboard</h1>
        <p>Track your income and payment history</p>
    </div>

    <!-- Earnings Summary -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-success">
                    <i class="fas fa-dollar-sign"></i>
                </div>
                <div class="stat-content">
                    <h3>${{ "{:,.0f}".format(earnings.monthly_total) }}</h3>
                    <p>This Month</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-primary">
                    <i class="fas fa-calendar-week"></i>
                </div>
                <div class="stat-content">
                    <h3>${{ "{:,.0f}".format(earnings.weekly_total) }}</h3>
                    <p>This Week</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-warning">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="stat-content">
                    <h3>${{ "{:,.0f}".format(earnings.pending_payments) }}</h3>
                    <p>Pending</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-info">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="stat-content">
                    <h3>${{ "{:,.0f}".format(earnings.total_earned) }}</h3>
                    <p>Total Earned</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Performance Metrics -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>Performance Metrics</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <div class="text-center">
                                <h4 class="text-primary">{{ earnings.trips_completed }}</h4>
                                <small>Trips Completed</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center">
                                <h4 class="text-success">${{ earnings.average_per_trip }}</h4>
                                <small>Average per Trip</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>Payment Status</h5>
                </div>
                <div class="card-body">
                    <div class="progress mb-2">
                        <div class="progress-bar bg-success" style="width: 85%"></div>
                    </div>
                    <p class="text-muted">85% of payments received on time</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Payments -->
    <div class="card">
        <div class="card-header">
            <h5>Recent Payments</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Trip ID</th>
                            <th>Amount</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for payment in earnings.recent_payments %}
                        <tr>
                            <td>{{ payment.date }}</td>
                            <td>{{ payment.trip_id }}</td>
                            <td>${{ payment.amount }}</td>
                            <td>
                                {% if payment.status == "Paid" %}
                                    <span class="badge bg-success">{{ payment.status }}</span>
                                {% else %}
                                    <span class="badge bg-warning">{{ payment.status }}</span>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}