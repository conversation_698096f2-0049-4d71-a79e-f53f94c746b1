{% extends "base.html" %}

{% block content %}
<div class="dashboard-container">
    <div class="dashboard-header">
        <h1>Location Tracking</h1>
        <p>Real-time location and route management</p>
    </div>

    <!-- Current Status -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5>Current Location & Status</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Current Location</label>
                                <input type="text" class="form-control" value="{{ location.current_location }}" readonly>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Status</label>
                                <span class="badge bg-success fs-6">{{ location.status }}</span>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Last Updated</label>
                                <input type="text" class="form-control" value="{{ location.last_updated }}" readonly>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Destination</label>
                                <input type="text" class="form-control" value="{{ location.destination }}" readonly>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">ETA</label>
                                <input type="text" class="form-control" value="{{ location.eta }}" readonly>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Distance Remaining</label>
                                <input type="text" class="form-control" value="{{ location.distance_remaining }}" readonly>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5>Location Controls</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button class="btn btn-primary" onclick="updateDriverLocation()">Update Location</button>
                        <button class="btn btn-success" onclick="markArrival()">Mark Arrival</button>
                        <button class="btn btn-warning" onclick="reportIssue()">Report Issue</button>
                        <button class="btn btn-info" onclick="shareETA()">Share ETA</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Map Placeholder -->
    <div class="card mb-4">
        <div class="card-header">
            <h5>Route Map</h5>
        </div>
        <div class="card-body">
            <div class="map-placeholder bg-light border rounded" style="height: 400px; display: flex; align-items: center; justify-content: center;">
                <div class="text-center">
                    <i class="fas fa-map-marked-alt fa-3x text-muted mb-3"></i>
                    <h5>Interactive Route Map</h5>
                    <p class="text-muted">Real-time GPS tracking and route optimization</p>
                    <p><strong>Current Coordinates:</strong> {{ location.latitude }}, {{ location.longitude }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Location History -->
    <div class="card">
        <div class="card-header">
            <h5>Location History</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Time</th>
                            <th>Location</th>
                            <th>Status</th>
                            <th>Speed</th>
                            <th>Notes</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>15:30</td>
                            <td>Interstate 5, CA</td>
                            <td><span class="badge bg-success">En Route</span></td>
                            <td>65 mph</td>
                            <td>On schedule</td>
                        </tr>
                        <tr>
                            <td>14:45</td>
                            <td>Rest Stop, Bakersfield</td>
                            <td><span class="badge bg-warning">Break</span></td>
                            <td>0 mph</td>
                            <td>15-minute break</td>
                        </tr>
                        <tr>
                            <td>13:20</td>
                            <td>Grapevine, CA</td>
                            <td><span class="badge bg-success">En Route</span></td>
                            <td>55 mph</td>
                            <td>Mountain pass</td>
                        </tr>
                        <tr>
                            <td>11:00</td>
                            <td>Los Angeles Port</td>
                            <td><span class="badge bg-info">Pickup</span></td>
                            <td>0 mph</td>
                            <td>Cargo loaded</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}