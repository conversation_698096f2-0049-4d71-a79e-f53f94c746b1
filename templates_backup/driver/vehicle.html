{% extends "base.html" %}

{% block content %}
<div class="dashboard-container">
    <div class="dashboard-header">
        <h1>Vehicle Management</h1>
        <p>Manage your truck information and maintenance records</p>
    </div>

    <!-- Vehicle Information -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5>Vehicle Information</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Make & Model</label>
                                <input type="text" class="form-control" value="{{ vehicle.make }} {{ vehicle.model }}" readonly>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Year</label>
                                <input type="text" class="form-control" value="{{ vehicle.year }}" readonly>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">License Plate</label>
                                <input type="text" class="form-control" value="{{ vehicle.plate_number }}" readonly>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Truck Type</label>
                                <input type="text" class="form-control" value="{{ vehicle.truck_type }}" readonly>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Capacity</label>
                                <input type="text" class="form-control" value="{{ vehicle.capacity }}" readonly>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Fuel Type</label>
                                <input type="text" class="form-control" value="{{ vehicle.fuel_type }}" readonly>
                            </div>
                        </div>
                    </div>
                    <button class="btn btn-primary" onclick="editVehicleInfo()">Edit Vehicle Info</button>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5>Quick Stats</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>Current Mileage</strong>
                        <p class="h4 text-primary">{{ "{:,}".format(vehicle.mileage) }} km</p>
                    </div>
                    <div class="mb-3">
                        <strong>Fuel Efficiency</strong>
                        <p class="h4 text-success">{{ vehicle.fuel_efficiency }} L/100km</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Maintenance & Compliance -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>Maintenance Schedule</h5>
                </div>
                <div class="card-body">
                    <div class="list-group">
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <h6>Last Inspection</h6>
                                <small class="text-muted">{{ vehicle.last_inspection }}</small>
                            </div>
                            <span class="badge bg-success">Current</span>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <h6>Next Oil Change</h6>
                                <small class="text-muted">Due in 2,500 km</small>
                            </div>
                            <span class="badge bg-warning">Due Soon</span>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <h6>Tire Rotation</h6>
                                <small class="text-muted">Due in 5,000 km</small>
                            </div>
                            <span class="badge bg-secondary">Scheduled</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>Compliance & Insurance</h5>
                </div>
                <div class="card-body">
                    <div class="list-group">
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <h6>Insurance</h6>
                                <small class="text-muted">Expires: {{ vehicle.insurance_expiry }}</small>
                            </div>
                            <span class="badge bg-success">Active</span>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <h6>Commercial License</h6>
                                <small class="text-muted">Class A CDL</small>
                            </div>
                            <span class="badge bg-success">Valid</span>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <h6>DOT Inspection</h6>
                                <small class="text-muted">Annual inspection</small>
                            </div>
                            <span class="badge bg-success">Current</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Maintenance History -->
    <div class="card">
        <div class="card-header">
            <h5>Maintenance History</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Service Type</th>
                            <th>Mileage</th>
                            <th>Cost</th>
                            <th>Provider</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>2025-01-15</td>
                            <td>Annual Inspection</td>
                            <td>148,500 km</td>
                            <td>$250</td>
                            <td>ABC Truck Service</td>
                        </tr>
                        <tr>
                            <td>2024-12-20</td>
                            <td>Oil Change</td>
                            <td>146,200 km</td>
                            <td>$180</td>
                            <td>QuickLube Pro</td>
                        </tr>
                        <tr>
                            <td>2024-11-10</td>
                            <td>Brake Service</td>
                            <td>143,800 km</td>
                            <td>$420</td>
                            <td>ABC Truck Service</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}