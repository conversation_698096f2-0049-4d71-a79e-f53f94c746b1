{% extends "base.html" %}

{% block content %}
<div class="dashboard-container">
    <div class="dashboard-header">
        <h1>Driver Dashboard</h1>
        <p>Welcome back, {{ user.first_name or user.email.split('@')[0] }}</p>
    </div>

    <!-- Quick Stats -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-primary">
                    <i class="fas fa-route"></i>
                </div>
                <div class="stat-content">
                    <h3>{{ active_routes }}</h3>
                    <p>Active Routes</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-success">
                    <i class="fas fa-truck"></i>
                </div>
                <div class="stat-content">
                    <h3>{{ total_deliveries }}</h3>
                    <p>Total Deliveries</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-info">
                    <i class="fas fa-dollar-sign"></i>
                </div>
                <div class="stat-content">
                    <h3>${{ monthly_earnings }}</h3>
                    <p>Monthly Earnings</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-warning">
                    <i class="fas fa-star"></i>
                </div>
                <div class="stat-content">
                    <h3>{{ current_rating }}</h3>
                    <p>Current Rating</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5>Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <button class="btn btn-primary btn-block mb-2" onclick="showAvailableJobs()">
                                <i class="fas fa-list"></i> View Available Jobs
                            </button>
                        </div>
                        <div class="col-md-4">
                            <button class="btn btn-success btn-block mb-2" onclick="updateLocation()">
                                <i class="fas fa-map-marker-alt"></i> Update Location
                            </button>
                        </div>
                        <div class="col-md-4">
                            <button class="btn btn-info btn-block mb-2" onclick="viewEarnings()">
                                <i class="fas fa-chart-line"></i> View Earnings
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5>Recent Activity</h5>
                </div>
                <div class="card-body">
                    <div class="activity-list">
                        {% for activity in recent_activity %}
                        <div class="activity-item">
                            <div class="activity-icon">
                                <i class="fas fa-circle"></i>
                            </div>
                            <div class="activity-content">
                                <strong>{{ activity.action }}</strong>
                                <p>{{ activity.details }}</p>
                                <small class="text-muted">{{ activity.time }}</small>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function showAvailableJobs() {
    window.location.href = '/driver/available-jobs';
}

function updateLocation() {
    window.location.href = '/driver/location';
}

function viewEarnings() {
    window.location.href = '/driver/earnings';
}
</script>
{% endblock %}