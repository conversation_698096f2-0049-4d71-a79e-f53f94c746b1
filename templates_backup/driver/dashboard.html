<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Driver Dashboard - Cloverics</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
        }
        .nav-link {
            color: rgba(255,255,255,0.8);
            border-radius: 8px;
            margin: 4px 0;
            transition: all 0.3s ease;
        }
        .nav-link:hover, .nav-link.active {
            background-color: rgba(255,255,255,0.1);
            color: white;
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 15px;
            color: white;
            transition: transform 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-5px);
        }
        .route-card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        .route-card:hover {
            box-shadow: 0 8px 15px rgba(0,0,0,0.15);
            transform: translateY(-2px);
        }
        .driver-badge {
            background: linear-gradient(135deg, #ff6b6b, #ffa726);
            color: white;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
        }
        .approval-alert {
            border-left: 4px solid #ffc107;
            background: #fff3cd;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .success-alert {
            border-left: 4px solid #28a745;
            background: #d4edda;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar p-3">
                <div class="text-center mb-4">
                    <h4><i class="fas fa-truck"></i> Cloverics</h4>
                    <p class="mb-0">Independent Driver Portal</p>
                </div>
                
                <div class="text-center mb-4">
                    <div class="driver-badge">
                        <i class="fas fa-user-circle"></i> Individual Driver
                    </div>
                    <h6 class="mt-2 mb-0">{{ eligibility.driver_info.full_name if eligibility.get('driver_info') else user.email }}</h6>
                    <small>{{ eligibility.driver_info.truck_type if eligibility.get('driver_info') else 'Driver' }}</small>
                </div>

                <nav class="nav flex-column">
                    <a class="nav-link active" href="#dashboard"><i class="fas fa-tachometer-alt me-2"></i>Dashboard</a>
                    {% if can_operate %}
                    <a class="nav-link" href="#routes"><i class="fas fa-route me-2"></i>My Routes</a>
                    <a class="nav-link" href="#create-route"><i class="fas fa-plus-circle me-2"></i>Create Route</a>
                    {% endif %}
                    <a class="nav-link" href="#profile"><i class="fas fa-user me-2"></i>Profile</a>
                    <a class="nav-link" href="#earnings"><i class="fas fa-dollar-sign me-2"></i>Earnings</a>
                    <a class="nav-link" href="/logout"><i class="fas fa-sign-out-alt me-2"></i>Logout</a>
                </nav>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 p-4">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2>Independent Driver Dashboard</h2>
                    <div class="d-flex align-items-center">
                        <span class="badge bg-success me-2">
                            <i class="fas fa-check-circle"></i> {{ statistics.verification_status.title() }}
                        </span>
                        <span class="text-muted">Rating: {{ "%.1f"|format(statistics.driver_rating) }}/5.0</span>
                    </div>
                </div>

                <!-- Eligibility Status -->
                {% if not can_operate %}
                <div class="approval-alert">
                    <h5><i class="fas fa-exclamation-triangle"></i> Approval Required</h5>
                    <p class="mb-2">{{ eligibility.reason }}</p>
                    <small>Your driver profile must be verified and approved by our admin team before you can create routes and accept shipment offers independently.</small>
                </div>
                {% else %}
                <div class="success-alert">
                    <h5><i class="fas fa-check-circle"></i> Approved for Independent Operation</h5>
                    <p class="mb-0">You are approved to create routes and accept shipment offers independently. Welcome to the Cloverics driver network!</p>
                </div>
                {% endif %}

                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-md-3 mb-3">
                        <div class="card stat-card">
                            <div class="card-body text-center">
                                <i class="fas fa-route fa-2x mb-2"></i>
                                <h4 class="mb-0">{{ statistics.total_routes }}</h4>
                                <small>Total Routes</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card stat-card">
                            <div class="card-body text-center">
                                <i class="fas fa-check-circle fa-2x mb-2"></i>
                                <h4 class="mb-0">{{ statistics.active_routes }}</h4>
                                <small>Active Routes</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card stat-card">
                            <div class="card-body text-center">
                                <i class="fas fa-quote-left fa-2x mb-2"></i>
                                <h4 class="mb-0">{{ statistics.total_quotes }}</h4>
                                <small>Quote Requests</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card stat-card">
                            <div class="card-body text-center">
                                <i class="fas fa-truck fa-2x mb-2"></i>
                                <h4 class="mb-0">{{ statistics.capacity_kg }}kg</h4>
                                <small>Max Capacity</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Routes Section -->
                <div class="row" id="routes-section">
                    <div class="col-12">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h4>My Routes</h4>
                            {% if can_operate %}
                            <button class="btn btn-primary" onclick="showCreateRouteModal()">
                                <i class="fas fa-plus"></i> Create New Route
                            </button>
                            {% endif %}
                        </div>

                        {% if routes %}
                        <div class="row">
                            {% for route in routes %}
                            <div class="col-md-6 col-lg-4 mb-3">
                                <div class="card route-card">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-start mb-3">
                                            <h6 class="card-title mb-0">
                                                {{ route.origin_city }} → {{ route.destination_city }}
                                            </h6>
                                            <span class="badge bg-{{ 'success' if route.is_active else 'secondary' }}">
                                                {{ 'Active' if route.is_active else 'Inactive' }}
                                            </span>
                                        </div>
                                        
                                        <p class="text-muted small mb-2">
                                            <i class="fas fa-truck me-1"></i>{{ route.transport_type.title() }}
                                        </p>
                                        
                                        <div class="row text-center">
                                            <div class="col-4">
                                                <small class="text-muted">Rate</small>
                                                <div class="fw-bold">${{ "%.2f"|format(route.base_price) }}</div>
                                            </div>
                                            <div class="col-4">
                                                <small class="text-muted">Days</small>
                                                <div class="fw-bold">{{ route.estimated_days }}</div>
                                            </div>
                                            <div class="col-4">
                                                <small class="text-muted">Capacity</small>
                                                <div class="fw-bold">{{ route.available_space }}kg</div>
                                            </div>
                                        </div>
                                        
                                        <div class="mt-3">
                                            <button class="btn btn-sm btn-outline-primary me-2" onclick="editRoute({{ route.id }})">
                                                <i class="fas fa-edit"></i> Edit
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger" onclick="deleteRoute({{ route.id }})">
                                                <i class="fas fa-trash"></i> Delete
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-route fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No Routes Created Yet</h5>
                            {% if can_operate %}
                            <p class="text-muted">Create your first route to start accepting shipment offers.</p>
                            <button class="btn btn-primary" onclick="showCreateRouteModal()">
                                <i class="fas fa-plus"></i> Create Your First Route
                            </button>
                            {% else %}
                            <p class="text-muted">Routes will appear here once you're approved for independent operation.</p>
                            {% endif %}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Create Route Modal -->
    {% if can_operate %}
    <div class="modal fade" id="createRouteModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Create New Route</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="createRouteForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Origin Country</label>
                                <select class="form-select" name="origin_country" required>
                                    <option value="">Select Origin Country</option>
                                    <option value="Turkey">Turkey</option>
                                    <option value="Germany">Germany</option>
                                    <option value="United States">United States</option>
                                    <option value="China">China</option>
                                    <option value="Azerbaijan">Azerbaijan</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Destination Country</label>
                                <select class="form-select" name="destination_country" required>
                                    <option value="">Select Destination Country</option>
                                    <option value="Turkey">Turkey</option>
                                    <option value="Germany">Germany</option>
                                    <option value="United States">United States</option>
                                    <option value="China">China</option>
                                    <option value="Azerbaijan">Azerbaijan</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Origin City (Optional)</label>
                                <input type="text" class="form-control" name="origin_city" placeholder="e.g., Istanbul">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Destination City (Optional)</label>
                                <input type="text" class="form-control" name="destination_city" placeholder="e.g., Berlin">
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label class="form-label">Transport Type</label>
                                <select class="form-select" name="transport_type" required>
                                    <option value="truck">Truck</option>
                                    <option value="van">Van</option>
                                    <option value="flatbed">Flatbed</option>
                                    <option value="refrigerated">Refrigerated</option>
                                </select>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label class="form-label">Base Rate ($)</label>
                                <input type="number" class="form-control" name="base_rate" step="0.01" min="1" required>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label class="form-label">Estimated Days</label>
                                <input type="number" class="form-control" name="estimated_days" min="1" max="30" value="7" required>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Maximum Capacity (kg)</label>
                            <input type="number" class="form-control" name="max_capacity" min="100" max="{{ statistics.capacity_kg }}" value="{{ statistics.capacity_kg }}" required>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="createRoute()">Create Route</button>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <script>
        function showCreateRouteModal() {
            const modal = new bootstrap.Modal(document.getElementById('createRouteModal'));
            modal.show();
        }

        async function createRoute() {
            const form = document.getElementById('createRouteForm');
            const formData = new FormData(form);
            const data = Object.fromEntries(formData.entries());

            try {
                const response = await fetch('/api/driver/create-route', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(data)
                });

                const result = await response.json();

                if (result.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Route Created!',
                        text: result.message,
                        confirmButtonColor: '#28a745'
                    }).then(() => {
                        location.reload();
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: result.error,
                        confirmButtonColor: '#dc3545'
                    });
                }
            } catch (error) {
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'Failed to create route. Please try again.',
                    confirmButtonColor: '#dc3545'
                });
            }
        }

        function editRoute(routeId) {
            Swal.fire({
                icon: 'info',
                title: 'Edit Route',
                text: 'Route editing functionality will be available soon.',
                confirmButtonColor: '#007bff'
            });
        }

        function deleteRoute(routeId) {
            Swal.fire({
                title: 'Delete Route?',
                text: 'Are you sure you want to delete this route? This action cannot be undone.',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#dc3545',
                cancelButtonColor: '#6c757d',
                confirmButtonText: 'Yes, delete it!'
            }).then((result) => {
                if (result.isConfirmed) {
                    // TODO: Implement route deletion
                    Swal.fire({
                        icon: 'success',
                        title: 'Deleted!',
                        text: 'Route has been deleted.',
                        confirmButtonColor: '#28a745'
                    });
                }
            });
        }
    </script>
</body>
</html>