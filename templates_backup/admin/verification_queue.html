{% extends "base.html" %}

{% block title %}Verification Queue - Admin - Cloverics{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-user-check text-primary"></i> Verification Queue</h2>
                <div class="btn-group" role="group">
                    <button class="btn btn-outline-success" onclick="approveSelected()">
                        <i class="fas fa-check"></i> Approve Selected
                    </button>
                    <button class="btn btn-outline-danger" onclick="rejectSelected()">
                        <i class="fas fa-times"></i> Reject Selected
                    </button>
                </div>
            </div>

            <!-- Verification Statistics -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>{{ verification_stats.pending }}</h4>
                                    <p class="mb-0">Pending Review</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-clock fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>{{ verification_stats.approved_today }}</h4>
                                    <p class="mb-0">Approved Today</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-check fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-danger text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>{{ verification_stats.rejected_today }}</h4>
                                    <p class="mb-0">Rejected Today</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-times fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>{{ verification_stats.avg_processing_time }}h</h4>
                                    <p class="mb-0">Avg Processing Time</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-stopwatch fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Verification Filters -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <select class="form-select" id="typeFilter" onchange="filterQueue()">
                                <option value="">All Types</option>
                                <option value="logistics">Logistics Provider</option>
                                <option value="customer">Customer</option>
                                <option value="driver">Independent Driver</option>
                                <option value="insurance">Insurance Provider</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" id="statusFilter" onchange="filterQueue()">
                                <option value="">All Status</option>
                                <option value="pending">Pending</option>
                                <option value="under_review">Under Review</option>
                                <option value="requires_documents">Requires Documents</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" id="priorityFilter" onchange="filterQueue()">
                                <option value="">All Priorities</option>
                                <option value="high">High Priority</option>
                                <option value="normal">Normal</option>
                                <option value="low">Low Priority</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <input type="text" class="form-control" id="searchFilter" placeholder="Search by name or email..." onkeyup="filterQueue()">
                        </div>
                    </div>
                </div>
            </div>

            <!-- Verification Queue Table -->
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Pending Verifications</h5>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                            <label class="form-check-label" for="selectAll">
                                Select All
                            </label>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Select</th>
                                    <th>User</th>
                                    <th>Type</th>
                                    <th>Submitted</th>
                                    <th>Priority</th>
                                    <th>Status</th>
                                    <th>Documents</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="verificationTableBody">
                                {% for item in verification_queue %}
                                <tr data-type="{{ item.user_type }}" data-status="{{ item.status }}" data-priority="{{ item.priority }}">
                                    <td>
                                        <input class="form-check-input verification-checkbox" type="checkbox" value="{{ item.id }}">
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <img src="https://ui-avatars.com/api/?name={{ item.user_name }}&size=40" 
                                                 class="rounded-circle me-2" width="40" height="40">
                                            <div>
                                                <strong>{{ item.user_name }}</strong><br>
                                                <small class="text-muted">{{ item.email }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-primary">{{ item.user_type|title }}</span>
                                    </td>
                                    <td>{{ item.submitted_at|date:"M d, Y H:i" }}</td>
                                    <td>
                                        <span class="badge bg-{% if item.priority == 'high' %}danger{% elif item.priority == 'normal' %}success{% else %}secondary{% endif %}">
                                            {{ item.priority|title }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-{% if item.status == 'pending' %}warning{% elif item.status == 'under_review' %}info{% else %}secondary{% endif %}">
                                            {{ item.status|title }}
                                        </span>
                                    </td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-primary" onclick="viewDocuments({{ item.id }})">
                                            <i class="fas fa-file-alt"></i> View ({{ item.document_count }})
                                        </button>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button class="btn btn-sm btn-success" onclick="approveUser({{ item.id }})">
                                                <i class="fas fa-check"></i>
                                            </button>
                                            <button class="btn btn-sm btn-danger" onclick="rejectUser({{ item.id }})">
                                                <i class="fas fa-times"></i>
                                            </button>
                                            <button class="btn btn-sm btn-info" onclick="requestDocuments({{ item.id }})">
                                                <i class="fas fa-file-upload"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="8" class="text-center py-4">
                                        <i class="fas fa-check-circle fa-3x text-success mb-3"></i><br>
                                        <h5 class="text-muted">No pending verifications</h5>
                                        <p class="text-muted">All users are verified!</p>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function filterQueue() {
    const typeFilter = document.getElementById('typeFilter').value;
    const statusFilter = document.getElementById('statusFilter').value;
    const priorityFilter = document.getElementById('priorityFilter').value;
    const searchFilter = document.getElementById('searchFilter').value.toLowerCase();
    
    const rows = document.querySelectorAll('#verificationTableBody tr');
    
    rows.forEach(row => {
        const type = row.dataset.type;
        const status = row.dataset.status;
        const priority = row.dataset.priority;
        const text = row.textContent.toLowerCase();
        
        const typeMatch = !typeFilter || type === typeFilter;
        const statusMatch = !statusFilter || status === statusFilter;
        const priorityMatch = !priorityFilter || priority === priorityFilter;
        const textMatch = !searchFilter || text.includes(searchFilter);
        
        if (typeMatch && statusMatch && priorityMatch && textMatch) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}

function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.verification-checkbox');
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
}

function approveUser(userId) {
    Swal.fire({
        title: 'Approve User',
        text: 'Are you sure you want to approve this user?',
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#28a745',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'Yes, approve!'
    }).then((result) => {
        if (result.isConfirmed) {
            Swal.fire('Approved!', 'User has been approved.', 'success');
        }
    });
}

function rejectUser(userId) {
    Swal.fire({
        title: 'Reject User',
        text: 'Are you sure you want to reject this user?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#dc3545',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'Yes, reject!'
    }).then((result) => {
        if (result.isConfirmed) {
            Swal.fire('Rejected!', 'User has been rejected.', 'success');
        }
    });
}

function requestDocuments(userId) {
    Swal.fire({
        title: 'Request Additional Documents',
        input: 'textarea',
        inputLabel: 'Specify required documents:',
        inputPlaceholder: 'Please provide additional documents...',
        showCancelButton: true,
        confirmButtonText: 'Send Request'
    }).then((result) => {
        if (result.isConfirmed) {
            Swal.fire('Request Sent!', 'Document request has been sent to user.', 'success');
        }
    });
}

function viewDocuments(userId) {
    Swal.fire({
        title: 'User Documents',
        html: '<div class="text-center"><i class="fas fa-file-alt fa-3x text-primary mb-3"></i><br>Loading documents...</div>',
        width: 600,
        showCloseButton: true,
        showConfirmButton: false
    });
}

function approveSelected() {
    const selected = document.querySelectorAll('.verification-checkbox:checked');
    if (selected.length === 0) {
        Swal.fire('No Selection', 'Please select users to approve.', 'warning');
        return;
    }
    
    Swal.fire({
        title: `Approve ${selected.length} Users`,
        text: 'Are you sure you want to approve all selected users?',
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#28a745',
        confirmButtonText: 'Yes, approve all!'
    }).then((result) => {
        if (result.isConfirmed) {
            Swal.fire('Approved!', `${selected.length} users have been approved.`, 'success');
        }
    });
}

function rejectSelected() {
    const selected = document.querySelectorAll('.verification-checkbox:checked');
    if (selected.length === 0) {
        Swal.fire('No Selection', 'Please select users to reject.', 'warning');
        return;
    }
    
    Swal.fire({
        title: `Reject ${selected.length} Users`,
        text: 'Are you sure you want to reject all selected users?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#dc3545',
        confirmButtonText: 'Yes, reject all!'
    }).then((result) => {
        if (result.isConfirmed) {
            Swal.fire('Rejected!', `${selected.length} users have been rejected.`, 'success');
        }
    });
}
</script>
{% endblock %}