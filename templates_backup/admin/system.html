{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <h1><i class="fas fa-server"></i> System Management</h1>
                <p class="text-muted">Monitor system health and performance metrics</p>
            </div>
        </div>
    </div>

    <!-- System Status Overview -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-heartbeat"></i> System Health</h5>
                </div>
                <div class="card-body">
                    <div class="status-item">
                        <span class="status-label">Server Status:</span>
                        <span class="badge badge-success">{{ system.server_status }}</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">Database:</span>
                        <span class="badge badge-success">{{ system.database_status }}</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">API Response:</span>
                        <span class="badge badge-info">{{ system.api_response_time }}</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">System Uptime:</span>
                        <span class="badge badge-primary">{{ system.uptime }}</span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-line"></i> Performance Metrics</h5>
                </div>
                <div class="card-body">
                    <div class="metric-item">
                        <span class="metric-label">Requests Today:</span>
                        <span class="metric-value text-primary">{{ system.total_requests_today }}</span>
                    </div>
                    <div class="metric-item">
                        <span class="metric-label">Error Rate:</span>
                        <span class="metric-value text-success">{{ system.error_rate }}</span>
                    </div>
                    <div class="metric-item">
                        <span class="metric-label">Avg Response Time:</span>
                        <span class="metric-value text-info">{{ system.api_response_time }}</span>
                    </div>
                    <div class="metric-item">
                        <span class="metric-label">Active Users:</span>
                        <span class="metric-value text-warning">127</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-tools"></i> Quick Actions</h5>
                </div>
                <div class="card-body">
                    <button class="btn btn-primary btn-block mb-2">
                        <i class="fas fa-sync"></i> Restart Services
                    </button>
                    <button class="btn btn-info btn-block mb-2">
                        <i class="fas fa-database"></i> Database Backup
                    </button>
                    <button class="btn btn-warning btn-block mb-2">
                        <i class="fas fa-broom"></i> Clear Cache
                    </button>
                    <button class="btn btn-secondary btn-block">
                        <i class="fas fa-download"></i> Export Logs
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Shipment Oversight Table -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5><i class="fas fa-shipping-fast"></i> Shipment Oversight Monitor</h5>
                    <div class="header-actions">
                        <button class="btn btn-sm btn-outline-primary" onclick="refreshShipments()">
                            <i class="fas fa-sync-alt"></i> Refresh
                        </button>
                        <button class="btn btn-sm btn-outline-secondary" onclick="exportShipments()">
                            <i class="fas fa-download"></i> Export
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Shipment ID</th>
                                    <th>Status</th>
                                    <th>Route</th>
                                    <th>Provider</th>
                                    <th>Customer</th>
                                    <th>Cargo Type</th>
                                    <th>Total Price</th>
                                    <th>Risk Level</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if shipment_oversight %}
                                    {% for shipment in shipment_oversight %}
                                    <tr class="{% if shipment.risk_level == 'HIGH' %}table-danger{% elif shipment.risk_level == 'MEDIUM' %}table-warning{% endif %}">
                                        <td><span class="badge bg-secondary">#{{ shipment.id }}</span></td>
                                        <td>
                                            {% if shipment.status == 'DELIVERED' %}
                                                <span class="badge bg-success">{{ shipment.status }}</span>
                                            {% elif shipment.status == 'IN_TRANSIT' %}
                                                <span class="badge bg-info">{{ shipment.status }}</span>
                                            {% elif shipment.status == 'PENDING' %}
                                                <span class="badge bg-warning">{{ shipment.status }}</span>
                                            {% else %}
                                                <span class="badge bg-secondary">{{ shipment.status }}</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ shipment.route }}</td>
                                        <td>
                                            <span class="badge bg-primary">{{ shipment.provider }}</span>
                                        </td>
                                        <td>
                                            <span class="badge bg-info">{{ shipment.customer }}</span>
                                        </td>
                                        <td>{{ shipment.cargo_type }}</td>
                                        <td><strong>{{ shipment.total_price }}</strong></td>
                                        <td>
                                            {% if shipment.risk_level == 'HIGH' %}
                                                <span class="badge bg-danger">High Risk</span>
                                            {% elif shipment.risk_level == 'MEDIUM' %}
                                                <span class="badge bg-warning">Medium Risk</span>
                                            {% else %}
                                                <span class="badge bg-success">Low Risk</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ shipment.created_date }}</td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button class="btn btn-sm btn-outline-primary" onclick="viewShipmentDetails({{ shipment.id }})">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-warning" onclick="editShipment({{ shipment.id }})">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="9" class="text-center text-muted">
                                            <i class="fas fa-info-circle"></i> No shipment data available
                                        </td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Customs Integration Health -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-passport"></i> Customs Integration Health</h5>
                </div>
                <div class="card-body">
                    <div class="metric-row">
                        <span class="metric-label">Declarations without Officer</span>
                        <span class="metric-value text-warning">{{ customs_health.unassigned_percentage }}%</span>
                    </div>
                    <div class="metric-row">
                        <span class="metric-label">Pending Declarations</span>
                        <span class="metric-value text-info">{{ customs_health.pending_declarations }}</span>
                    </div>
                    <div class="metric-row">
                        <span class="metric-label">Active Customs Officers</span>
                        <span class="metric-value text-primary">{{ customs_health.active_officers }}</span>
                    </div>
                    <div class="metric-row">
                        <span class="metric-label">PDF Download Success Rate</span>
                        <span class="metric-value text-success">{{ customs_health.pdf_success_rate }}%</span>
                    </div>
                    {% if customs_health.active_officers < 5 %}
                    <div class="alert alert-warning mt-3">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>Warning:</strong> Only {{ customs_health.active_officers }} customs officers registered
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-pie"></i> Live Audit Events</h5>
                </div>
                <div class="card-body" style="max-height: 300px; overflow-y: auto;">
                    {% if recent_audit_events %}
                        {% for event in recent_audit_events %}
                        <div class="audit-event-item">
                            <div class="d-flex justify-content-between">
                                <span class="event-time text-muted">{{ event.time }}</span>
                                <span class="badge bg-{{ event.event_color }}">{{ event.event_type }}</span>
                            </div>
                            <div class="event-details">
                                <strong>{{ event.actor }}</strong>
                                {% if event.target %} → {{ event.target }}{% endif %}
                                {% if event.shipment_id %}<span class="badge bg-secondary ms-1">#{{ event.shipment_id }}</span>{% endif %}
                            </div>
                            <div class="event-description">
                                <small>{{ event.description }}</small>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center text-muted">
                            <i class="fas fa-clock fa-2x mb-2"></i>
                            <p>No recent audit events</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- System Logs -->
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-list-alt"></i> Recent System Logs</h5>
                </div>
                <div class="card-body">
                    <div class="log-container">
                        <div class="log-entry log-info">
                            <span class="log-time">2025-01-26 14:32:15</span>
                            <span class="log-level badge badge-info">INFO</span>
                            <span class="log-message">FastAPI application started successfully on port 5000</span>
                        </div>
                        <div class="log-entry log-success">
                            <span class="log-time">2025-01-26 14:31:45</span>
                            <span class="log-level badge badge-success">SUCCESS</span>
                            <span class="log-message">Database connection established</span>
                        </div>
                        <div class="log-entry log-info">
                            <span class="log-time">2025-01-26 14:30:12</span>
                            <span class="log-level badge badge-info">INFO</span>
                            <span class="log-message">User authentication system initialized</span>
                        </div>
                        <div class="log-entry log-warning">
                            <span class="log-time">2025-01-26 14:29:33</span>
                            <span class="log-level badge badge-warning">WARNING</span>
                            <span class="log-message">Demo data initialization: duplicate key constraint</span>
                        </div>
                        <div class="log-entry log-info">
                            <span class="log-time">2025-01-26 14:28:55</span>
                            <span class="log-level badge badge-info">INFO</span>
                            <span class="log-message">Template engine configured successfully</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-cogs"></i> System Configuration</h5>
                </div>
                <div class="card-body">
                    <div class="config-item">
                        <label>Environment:</label>
                        <span class="badge badge-success">Production</span>
                    </div>
                    <div class="config-item">
                        <label>Python Version:</label>
                        <span>3.11.x</span>
                    </div>
                    <div class="config-item">
                        <label>FastAPI Version:</label>
                        <span>0.104.x</span>
                    </div>
                    <div class="config-item">
                        <label>Database:</label>
                        <span>PostgreSQL 15</span>
                    </div>
                    <div class="config-item">
                        <label>SSL Status:</label>
                        <span class="badge badge-success">Enabled</span>
                    </div>
                    <div class="config-item">
                        <label>Cache:</label>
                        <span class="badge badge-info">Redis Active</span>
                    </div>
                </div>
            </div>

            <div class="card mt-3">
                <div class="card-header">
                    <h5><i class="fas fa-shield-alt"></i> Security Status</h5>
                </div>
                <div class="card-body">
                    <div class="security-item">
                        <i class="fas fa-check text-success"></i>
                        <span>SSL Certificate Valid</span>
                    </div>
                    <div class="security-item">
                        <i class="fas fa-check text-success"></i>
                        <span>Firewall Active</span>
                    </div>
                    <div class="security-item">
                        <i class="fas fa-check text-success"></i>
                        <span>Rate Limiting Enabled</span>
                    </div>
                    <div class="security-item">
                        <i class="fas fa-check text-success"></i>
                        <span>Authentication Required</span>
                    </div>
                    <div class="security-item">
                        <i class="fas fa-exclamation-triangle text-warning"></i>
                        <span>2FA Optional</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.status-item, .metric-item, .config-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    padding: 5px 0;
    border-bottom: 1px solid #eee;
}

.log-container {
    max-height: 400px;
    overflow-y: auto;
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
}

.log-entry {
    padding: 8px;
    margin-bottom: 5px;
    border-radius: 3px;
    background-color: #f8f9fa;
}

.log-time {
    color: #666;
    margin-right: 10px;
}

.log-level {
    margin-right: 10px;
    font-size: 0.8em;
}

.log-message {
    color: #333;
}

.security-item {
    margin-bottom: 8px;
}

.security-item i {
    margin-right: 8px;
    width: 16px;
}

/* Admin Oversight Styles */
.header-actions .btn {
    margin-left: 5px;
}

.metric-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

.metric-label {
    font-weight: 500;
    color: #666;
}

.metric-value {
    font-weight: 600;
}

.audit-event-item {
    padding: 12px;
    margin-bottom: 15px;
    background-color: #f8f9fa;
    border-left: 4px solid #007bff;
    border-radius: 4px;
}

.audit-event-item:last-child {
    margin-bottom: 0;
}

.event-time {
    font-size: 0.85em;
}

.event-details {
    font-size: 0.9em;
    margin: 5px 0;
}

.event-description {
    color: #666;
}

.table-danger {
    background-color: #f8d7da;
}

.table-warning {
    background-color: #fff3cd;
}
</style>

<script>
// Admin System Oversight Functions
function refreshShipments() {
    Swal.fire({
        title: 'Refreshing Shipments...',
        text: 'Updating shipment oversight data',
        allowOutsideClick: false,
        didOpen: () => {
            Swal.showLoading();
        }
    });
    
    setTimeout(() => {
        Swal.fire({
            icon: 'success',
            title: 'Data Refreshed',
            text: 'Shipment oversight table updated successfully',
            timer: 2000,
            showConfirmButton: false
        });
        location.reload();
    }, 2000);
}

function exportShipments() {
    const table = document.querySelector('.table');
    const rows = table.querySelectorAll('tbody tr');
    
    if (rows.length === 0 || rows[0].cells.length === 1) {
        Swal.fire({
            icon: 'warning',
            title: 'No Data',
            text: 'No shipment data available to export'
        });
        return;
    }
    
    let csvContent = "Shipment ID,Status,Route,Driver Assigned,Customs Declared,Insurance Linked,Risk Level,Created\n";
    
    rows.forEach(row => {
        if (row.cells.length > 1) {
            const cells = Array.from(row.cells).slice(0, -1); // Exclude actions column
            const rowData = cells.map(cell => {
                const text = cell.textContent.trim();
                return `"${text.replace(/"/g, '""')}"`;
            }).join(',');
            csvContent += rowData + "\n";
        }
    });
    
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.setAttribute('href', url);
    a.setAttribute('download', `shipment_oversight_${new Date().toISOString().split('T')[0]}.csv`);
    a.click();
    
    Swal.fire({
        icon: 'success',
        title: 'Export Complete',
        text: 'Shipment data exported successfully',
        timer: 2000,
        showConfirmButton: false
    });
}

function viewShipmentDetails(shipmentId) {
    Swal.fire({
        title: `Shipment #${shipmentId} Details`,
        html: `
            <div class="text-left">
                <p><strong>Shipment ID:</strong> #${shipmentId}</p>
                <p><strong>Status:</strong> <span class="badge bg-info">IN_TRANSIT</span></p>
                <p><strong>Route:</strong> Istanbul → Baku</p>
                <p><strong>Cargo:</strong> Electronics (250 kg)</p>
                <p><strong>Driver:</strong> John Miller</p>
                <p><strong>Customs:</strong> Declared ✓</p>
                <p><strong>Insurance:</strong> Policy #INS-${shipmentId}</p>
                <p><strong>Risk Assessment:</strong> Low Risk</p>
                <hr>
                <p><strong>Next Actions:</strong></p>
                <ul class="list-unstyled">
                    <li>• Monitor driver location</li>
                    <li>• Track customs clearance</li>
                    <li>• Verify insurance coverage</li>
                </ul>
            </div>
        `,
        width: 600,
        confirmButtonText: 'Close',
        confirmButtonColor: '#007bff'
    });
}

function editShipment(shipmentId) {
    Swal.fire({
        title: `Edit Shipment #${shipmentId}`,
        html: `
            <div class="form-group text-left">
                <label>Status:</label>
                <select class="form-control" id="editStatus">
                    <option value="PENDING">Pending</option>
                    <option value="IN_TRANSIT" selected>In Transit</option>
                    <option value="DELIVERED">Delivered</option>
                    <option value="CANCELLED">Cancelled</option>
                </select>
            </div>
            <div class="form-group text-left mt-3">
                <label>Risk Level:</label>
                <select class="form-control" id="editRisk">
                    <option value="LOW" selected>Low Risk</option>
                    <option value="MEDIUM">Medium Risk</option>
                    <option value="HIGH">High Risk</option>
                </select>
            </div>
            <div class="form-group text-left mt-3">
                <label>Admin Notes:</label>
                <textarea class="form-control" id="editNotes" rows="3" placeholder="Add administrative notes..."></textarea>
            </div>
        `,
        width: 500,
        showCancelButton: true,
        confirmButtonText: 'Save Changes',
        cancelButtonText: 'Cancel',
        confirmButtonColor: '#28a745',
        preConfirm: () => {
            const status = document.getElementById('editStatus').value;
            const risk = document.getElementById('editRisk').value;
            const notes = document.getElementById('editNotes').value;
            
            return { status, risk, notes };
        }
    }).then((result) => {
        if (result.isConfirmed) {
            Swal.fire({
                icon: 'success',
                title: 'Shipment Updated',
                text: `Shipment #${shipmentId} has been updated successfully`,
                timer: 2000,
                showConfirmButton: false
            });
        }
    });
}
</script>
{% endblock %}