{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <h1><i class="fas fa-cogs"></i> System Settings</h1>
                <p class="text-muted">Configure platform settings and monitor system status</p>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- System Status -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-server"></i> System Status</h5>
                </div>
                <div class="card-body">
                    <div class="status-item">
                        <span class="status-label">Platform Status:</span>
                        <span class="badge badge-success">{{ settings.platform_status }}</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">Database Size:</span>
                        <span>{{ settings.database_size }}</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">Active Sessions:</span>
                        <span>{{ settings.active_sessions }}</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">Server Uptime:</span>
                        <span>{{ settings.server_uptime }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Service Status -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-check-circle"></i> Service Status</h5>
                </div>
                <div class="card-body">
                    <div class="status-item">
                        <span class="status-label">Email Service:</span>
                        <span class="badge badge-success">{{ settings.email_service }}</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">Payment Gateway:</span>
                        <span class="badge badge-success">{{ settings.payment_gateway }}</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">API Status:</span>
                        <span class="badge badge-success">{{ settings.api_status }}</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">Last Backup:</span>
                        <span>{{ settings.last_backup }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-tools"></i> Configuration Settings</h5>
                </div>
                <div class="card-body">
                    <form>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Maintenance Mode</label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" {% if settings.maintenance_mode %}checked{% endif %}>
                                        <label class="form-check-label">
                                            Enable maintenance mode
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Platform Settings</label>
                                    <select class="form-control">
                                        <option>Production</option>
                                        <option>Staging</option>
                                        <option>Development</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <button type="submit" class="btn btn-primary" onclick="saveSystemSettings()">Save Settings</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.status-item {
    display: flex;
    justify-content: space-between;
    padding: 0.5rem 0;
    border-bottom: 1px solid #eee;
}
.status-item:last-child {
    border-bottom: none;
}
.status-label {
    font-weight: 500;
}
</style>
{% endblock %}