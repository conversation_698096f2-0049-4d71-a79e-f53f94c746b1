{% extends "base.html" %}

{% block content %}
<div class="dashboard-container">
    <div class="dashboard-header">
        <h1>Admin Dashboard</h1>
        <p>System Overview and Management</p>
    </div>

    <!-- Quick Stats -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-primary">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stat-content">
                    <h3>{{ total_users }}</h3>
                    <p>Total Users</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-success">
                    <i class="fas fa-shipping-fast"></i>
                </div>
                <div class="stat-content">
                    <h3>{{ total_shipments }}</h3>
                    <p>Total Shipments</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-info">
                    <i class="fas fa-credit-card"></i>
                </div>
                <div class="stat-content">
                    <h3>{{ total_payments }}</h3>
                    <p>Total Payments</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="stat-content">
                    <h3>5</h3>
                    <p>Pending Verifications</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="quick-actions-card">
                <h5>Admin Actions</h5>
                <div class="action-buttons">
                    <a href="/admin/users" class="btn btn-primary">
                        <i class="fas fa-users"></i> Manage Users
                    </a>
                    <a href="/admin/system-settings" class="btn btn-outline-primary">
                        <i class="fas fa-cogs"></i> System Settings
                    </a>
                    <a href="/admin/verification" class="btn btn-outline-primary">
                        <i class="fas fa-check-circle"></i> Verifications
                    </a>
                    <a href="/admin/analytics" class="btn btn-outline-primary">
                        <i class="fas fa-chart-bar"></i> Analytics
                    </a>
                    <a href="/admin/performance" class="btn btn-outline-primary">
                        <i class="fas fa-tachometer-alt"></i> Performance Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- System Status -->
    <div class="row">
        <div class="col-lg-8">
            <div class="data-card">
                <div class="card-header">
                    <h5>System Status</h5>
                    <span class="badge bg-success">All Systems Operational</span>
                </div>
                <div class="card-body">
                    <div class="system-status">
                        <div class="status-item">
                            <div class="status-indicator bg-success"></div>
                            <div class="status-info">
                                <h6>Database</h6>
                                <p>PostgreSQL running normally</p>
                            </div>
                            <div class="status-value">
                                <span class="text-success">Online</span>
                            </div>
                        </div>
                        <div class="status-item">
                            <div class="status-indicator bg-success"></div>
                            <div class="status-info">
                                <h6>Payment Gateway</h6>
                                <p>Stripe integration active</p>
                            </div>
                            <div class="status-value">
                                <span class="text-success">Connected</span>
                            </div>
                        </div>
                        <div class="status-item">
                            <div class="status-indicator bg-warning"></div>
                            <div class="status-info">
                                <h6>Email Service</h6>
                                <p>SMTP configuration needed</p>
                            </div>
                            <div class="status-value">
                                <span class="text-warning">Limited</span>
                            </div>
                        </div>
                        <div class="status-item">
                            <div class="status-indicator bg-success"></div>
                            <div class="status-info">
                                <h6>File Storage</h6>
                                <p>Local storage available</p>
                            </div>
                            <div class="status-value">
                                <span class="text-success">Ready</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Live Audit Feed Panel -->
            <div class="data-card mb-3">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6><i class="fas fa-eye"></i> Live Audit Feed</h6>
                    <span class="badge bg-info">Real-time</span>
                </div>
                <div class="card-body audit-feed" style="max-height: 400px; overflow-y: auto;">
                    {% if recent_events %}
                        {% for event in recent_events %}
                        <div class="audit-item">
                            <div class="audit-time">
                                <small class="text-muted">{{ event.created_at.strftime('%H:%M') }}</small>
                            </div>
                            <div class="audit-content">
                                <div class="audit-event">
                                    <span class="event-type badge bg-{{ event.event_color }} me-1">{{ event.event_type }}</span>
                                    <strong>{{ event.actor_username }}</strong>
                                    {% if event.target_username %}
                                        → <span class="text-info">{{ event.target_username }}</span>
                                    {% endif %}
                                </div>
                                <div class="audit-details">
                                    <small>{{ event.description }}</small>
                                    {% if event.shipment_id %}
                                        <span class="badge bg-secondary ms-1">#{{ event.shipment_id }}</span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center text-muted">
                            <i class="fas fa-clock fa-2x mb-2"></i>
                            <p>No recent activity</p>
                        </div>
                    {% endif %}
                </div>
                <div class="card-footer text-center">
                    <a href="/admin/system" class="btn btn-sm btn-outline-primary">View All Events</a>
                </div>
            </div>

            <!-- Quick Stats -->
            <div class="data-card">
                <div class="card-header">
                    <h6>Platform Health</h6>
                </div>
                <div class="card-body">
                    <div class="mini-stat">
                        <span class="label">Uptime</span>
                        <span class="value text-success">99.9%</span>
                    </div>
                    <div class="mini-stat">
                        <span class="label">Response Time</span>
                        <span class="value">0.001s</span>
                    </div>
                    <div class="mini-stat">
                        <span class="label">Active Sessions</span>
                        <span class="value">147</span>
                    </div>
                    <div class="mini-stat">
                        <span class="label">Storage Used</span>
                        <span class="value text-info">2.4GB</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.system-status {
    padding: 1rem 0;
}

.status-item {
    display: flex;
    align-items: center;
    padding: 1rem 0;
    border-bottom: 1px solid #eee;
}

.status-item:last-child {
    border-bottom: none;
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 1rem;
}

.status-info {
    flex-grow: 1;
}

.status-info h6 {
    margin: 0;
    font-size: 0.95rem;
    font-weight: 600;
}

.status-info p {
    margin: 0;
    font-size: 0.85rem;
    color: #666;
}

.status-value {
    font-weight: 500;
    font-size: 0.9rem;
}
</style>
{% endblock %}