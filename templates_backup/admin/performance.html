{% extends "base.html" %}

{% block content %}
<div class="dashboard-container">
    <div class="dashboard-header">
        <h1><i class="fas fa-chart-line"></i> Performance Dashboard</h1>
        <p>Platform Analytics and Payment Monitoring</p>
    </div>

    <!-- Key Performance Indicators -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-success">
                    <i class="fas fa-dollar-sign"></i>
                </div>
                <div class="stat-content">
                    <h3>${{ total_revenue|round(2) }}</h3>
                    <p>Total Revenue</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-info">
                    <i class="fas fa-credit-card"></i>
                </div>
                <div class="stat-content">
                    <h3>{{ total_payments }}</h3>
                    <p>Total Payments</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-warning">
                    <i class="fas fa-university"></i>
                </div>
                <div class="stat-content">
                    <h3>{{ bank_transfers }}</h3>
                    <p>Bank Transfers</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-primary">
                    <i class="fab fa-stripe"></i>
                </div>
                <div class="stat-content">
                    <h3>{{ stripe_payments }}</h3>
                    <p>Stripe Payments</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Payment Monitoring Panel -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="data-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5><i class="fas fa-money-check-alt"></i> Recent Payment Monitoring</h5>
                    <div class="header-actions">
                        <button class="btn btn-sm btn-outline-primary" onclick="refreshPayments()">
                            <i class="fas fa-sync-alt"></i> Refresh
                        </button>
                        <button class="btn btn-sm btn-outline-secondary" onclick="exportPayments()">
                            <i class="fas fa-download"></i> Export
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Payment ID</th>
                                    <th>Method</th>
                                    <th>Amount</th>
                                    <th>Status</th>
                                    <th>Customer</th>
                                    <th>Shipment</th>
                                    <th>Date</th>
                                    <th>Verified</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if recent_payments %}
                                    {% for payment in recent_payments %}
                                    <tr>
                                        <td><span class="badge bg-secondary">#{{ payment.id }}</span></td>
                                        <td>
                                            {% if payment.payment_method == 'STRIPE' %}
                                                <span class="badge bg-primary"><i class="fab fa-stripe"></i> Stripe</span>
                                            {% elif payment.payment_method == 'BANK_TRANSFER' %}
                                                <span class="badge bg-warning"><i class="fas fa-university"></i> Bank</span>
                                            {% else %}
                                                <span class="badge bg-secondary">{{ payment.payment_method }}</span>
                                            {% endif %}
                                        </td>
                                        <td><strong>${{ payment.amount|round(2) }}</strong></td>
                                        <td>
                                            {% if payment.status == 'COMPLETED' %}
                                                <span class="badge bg-success">Completed</span>
                                            {% elif payment.status == 'PENDING' %}
                                                <span class="badge bg-warning">Pending</span>
                                            {% elif payment.status == 'FAILED' %}
                                                <span class="badge bg-danger">Failed</span>
                                            {% else %}
                                                <span class="badge bg-secondary">{{ payment.status }}</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ payment.payer_email }}</td>
                                        <td>
                                            {% if payment.shipment_id %}
                                                <a href="/admin/shipment/{{ payment.shipment_id }}" class="text-decoration-none">
                                                    #{{ payment.shipment_id }}
                                                </a>
                                            {% else %}
                                                <span class="text-muted">N/A</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ payment.payment_date.strftime('%m/%d/%Y') if payment.payment_date else 'N/A' }}</td>
                                        <td>
                                            {% if payment.is_verified %}
                                                <span class="badge bg-success"><i class="fas fa-check"></i> Yes</span>
                                            {% else %}
                                                <span class="badge bg-danger"><i class="fas fa-times"></i> No</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button class="btn btn-sm btn-outline-primary" onclick="viewPaymentDetails({{ payment.id }})">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                {% if payment.payment_method == 'BANK_TRANSFER' and not payment.is_verified %}
                                                <button class="btn btn-sm btn-outline-success" onclick="verifyPayment({{ payment.id }})">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                                {% endif %}
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="9" class="text-center text-muted">
                                            <i class="fas fa-info-circle"></i> No payment data available
                                        </td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Revenue Analytics -->
    <div class="row mb-4">
        <div class="col-lg-8">
            <div class="data-card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-bar"></i> Revenue Analytics</h5>
                </div>
                <div class="card-body">
                    <canvas id="revenueChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
        <div class="col-lg-4">
            <div class="data-card">
                <div class="card-header">
                    <h5><i class="fas fa-pie-chart"></i> Payment Methods</h5>
                </div>
                <div class="card-body">
                    <canvas id="paymentMethodChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Platform Performance Metrics -->
    <div class="row">
        <div class="col-lg-6">
            <div class="data-card">
                <div class="card-header">
                    <h5><i class="fas fa-tachometer-alt"></i> Platform Metrics</h5>
                </div>
                <div class="card-body">
                    <div class="metric-row">
                        <span class="metric-label">Average Transaction Value</span>
                        <span class="metric-value text-primary">${{ avg_payment_amount|round(2) }}</span>
                    </div>
                    <div class="metric-row">
                        <span class="metric-label">Payment Success Rate</span>
                        <span class="metric-value text-success">{{ payment_success_rate|round(1) }}%</span>
                    </div>
                    <div class="metric-row">
                        <span class="metric-label">Bank Transfer Verification Rate</span>
                        <span class="metric-value text-warning">{{ bank_verification_rate|round(1) }}%</span>
                    </div>
                    <div class="metric-row">
                        <span class="metric-label">Platform Fee Revenue</span>
                        <span class="metric-value text-info">${{ platform_fee_revenue|round(2) }}</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-6">
            <div class="data-card">
                <div class="card-header">
                    <h5><i class="fas fa-exclamation-triangle"></i> Payment Alerts</h5>
                </div>
                <div class="card-body">
                    {% if payment_alerts %}
                        {% for alert in payment_alerts %}
                        <div class="alert alert-{{ alert.type }} alert-dismissible fade show" role="alert">
                            <strong>{{ alert.title }}</strong> {{ alert.message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center text-muted">
                            <i class="fas fa-check-circle fa-2x mb-2 text-success"></i>
                            <p>No payment alerts at this time</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // Revenue Chart
    const revenueCtx = document.getElementById('revenueChart').getContext('2d');
    new Chart(revenueCtx, {
        type: 'line',
        data: {
            labels: {{ revenue_labels|safe }},
            datasets: [{
                label: 'Monthly Revenue',
                data: {{ revenue_data|safe }},
                borderColor: '#1E88E5',
                backgroundColor: 'rgba(30, 136, 229, 0.1)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return '$' + value.toLocaleString();
                        }
                    }
                }
            }
        }
    });

    // Payment Method Chart
    const methodCtx = document.getElementById('paymentMethodChart').getContext('2d');
    new Chart(methodCtx, {
        type: 'doughnut',
        data: {
            labels: ['Stripe', 'Bank Transfer'],
            datasets: [{
                data: [{{ stripe_payments }}, {{ bank_transfers }}],
                backgroundColor: ['#6f42c1', '#fd7e14']
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // Utility functions
    function refreshPayments() {
        location.reload();
    }

    function exportPayments() {
        window.open('/admin/payments/export', '_blank');
    }

    function viewPaymentDetails(paymentId) {
        // Implement payment details modal or redirect
        alert('Payment details for ID: ' + paymentId);
    }

    function verifyPayment(paymentId) {
        if (confirm('Verify this bank transfer payment?')) {
            fetch(`/admin/payments/${paymentId}/verify`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]')?.value || ''
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('Error verifying payment: ' + data.error);
                }
            })
            .catch(error => {
                alert('Error: ' + error);
            });
        }
    }
</script>

<style>
    .metric-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.75rem 0;
        border-bottom: 1px solid #f0f0f0;
    }
    
    .metric-row:last-child {
        border-bottom: none;
    }
    
    .metric-label {
        font-weight: 500;
        color: #6c757d;
    }
    
    .metric-value {
        font-weight: 700;
        font-size: 1.1em;
    }
    
    .header-actions .btn {
        margin-left: 0.5rem;
    }
</style>
{% endblock %}