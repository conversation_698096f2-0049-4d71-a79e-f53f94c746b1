{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <h1><i class="fas fa-headset"></i> Support Tickets Management</h1>
                <p class="text-muted">Manage and respond to user support requests across all user types</p>
            </div>
        </div>
    </div>

    <!-- Statistics Dashboard -->
    <div class="row mb-4">
        <div class="col-md-2">
            <div class="stat-card bg-primary">
                <div class="stat-number">{{ stats.total_tickets }}</div>
                <div class="stat-label">Total Tickets</div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="stat-card bg-warning">
                <div class="stat-number">{{ stats.open_tickets }}</div>
                <div class="stat-label">Open</div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="stat-card bg-info">
                <div class="stat-number">{{ stats.in_progress }}</div>
                <div class="stat-label">In Progress</div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="stat-card bg-success">
                <div class="stat-number">{{ stats.resolved_tickets }}</div>
                <div class="stat-label">Resolved</div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="stat-card bg-danger">
                <div class="stat-number">{{ stats.critical_priority }}</div>
                <div class="stat-label">Critical</div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="stat-card bg-orange">
                <div class="stat-number">{{ stats.high_priority }}</div>
                <div class="stat-label">High Priority</div>
            </div>
        </div>
    </div>

    <!-- Filter and Actions -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="filter-section">
                <select class="form-control" id="statusFilter" onchange="filterTickets()">
                    <option value="">All Statuses</option>
                    <option value="open">Open</option>
                    <option value="in_progress">In Progress</option>
                    <option value="resolved">Resolved</option>
                </select>
            </div>
        </div>
        <div class="col-md-6">
            <div class="filter-section">
                <select class="form-control" id="priorityFilter" onchange="filterTickets()">
                    <option value="">All Priorities</option>
                    <option value="critical">Critical</option>
                    <option value="high">High</option>
                    <option value="medium">Medium</option>
                    <option value="low">Low</option>
                </select>
            </div>
        </div>
    </div>

    <!-- Support Tickets Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-ticket-alt"></i> Support Tickets</h5>
                </div>
                <div class="card-body">
                    {% if tickets %}
                        <div class="table-responsive">
                            <table class="table table-striped" id="ticketsTable">
                                <thead>
                                    <tr>
                                        <th>Ticket #</th>
                                        <th>User</th>
                                        <th>User Type</th>
                                        <th>Subject</th>
                                        <th>Category</th>
                                        <th>Priority</th>
                                        <th>Status</th>
                                        <th>Created</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for ticket in tickets %}
                                    <tr data-status="{{ ticket.status }}" data-priority="{{ ticket.priority }}">
                                        <td>
                                            <strong>{{ ticket.ticket_number }}</strong>
                                        </td>
                                        <td>
                                            <div>
                                                <strong>{{ ticket.user_name }}</strong><br>
                                                <small class="text-muted">{{ ticket.user_email }}</small><br>
                                                <small class="text-muted">{{ ticket.company_name }}</small>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary">{{ ticket.user_type.replace('_', ' ').title() }}</span>
                                        </td>
                                        <td>{{ ticket.subject }}</td>
                                        <td>
                                            <span class="badge bg-info">{{ ticket.category.title() }}</span>
                                        </td>
                                        <td>
                                            {% if ticket.priority == 'critical' %}
                                                <span class="badge bg-danger">Critical</span>
                                            {% elif ticket.priority == 'high' %}
                                                <span class="badge bg-warning">High</span>
                                            {% elif ticket.priority == 'medium' %}
                                                <span class="badge bg-primary">Medium</span>
                                            {% else %}
                                                <span class="badge bg-secondary">Low</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if ticket.status == 'open' %}
                                                <span class="badge bg-warning">Open</span>
                                            {% elif ticket.status == 'in_progress' %}
                                                <span class="badge bg-info">In Progress</span>
                                            {% elif ticket.status == 'resolved' %}
                                                <span class="badge bg-success">Resolved</span>
                                            {% else %}
                                                <span class="badge bg-secondary">{{ ticket.status.title() }}</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ ticket.created_at }}</td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary" onclick="viewTicket({{ ticket.id }})" title="View Details">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                {% if ticket.status != 'resolved' %}
                                                <button class="btn btn-outline-success" onclick="respondToTicket({{ ticket.id }})" title="Respond">
                                                    <i class="fas fa-reply"></i>
                                                </button>
                                                {% endif %}
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="empty-state text-center py-5">
                            <i class="fas fa-ticket-alt fa-3x text-muted mb-3"></i>
                            <h5>No Support Tickets</h5>
                            <p class="text-muted">No support tickets have been submitted yet.</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Ticket Details Modal -->
<div class="modal fade" id="ticketDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Ticket Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="ticketDetailsContent">
                <!-- Content will be populated by JavaScript -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Response Modal -->
<div class="modal fade" id="responseModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Respond to Ticket</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="responseForm">
                <div class="modal-body">
                    <input type="hidden" id="responseTicketId">
                    <div class="mb-3">
                        <label class="form-label">Status *</label>
                        <select class="form-control" name="status" required>
                            <option value="in_progress">In Progress</option>
                            <option value="resolved">Resolved</option>
                            <option value="open">Reopen</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Response *</label>
                        <textarea class="form-control" name="response" rows="5" required placeholder="Type your response to the user..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-paper-plane"></i> Send Response
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function filterTickets() {
    const statusFilter = document.getElementById('statusFilter').value;
    const priorityFilter = document.getElementById('priorityFilter').value;
    const rows = document.querySelectorAll('#ticketsTable tbody tr');
    
    rows.forEach(row => {
        const status = row.getAttribute('data-status');
        const priority = row.getAttribute('data-priority');
        
        const statusMatch = !statusFilter || status === statusFilter;
        const priorityMatch = !priorityFilter || priority === priorityFilter;
        
        if (statusMatch && priorityMatch) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}

function viewTicket(ticketId) {
    // Find ticket data from the table
    const tickets = {{ tickets | tojson }};
    const ticket = tickets.find(t => t.id === ticketId);
    
    if (ticket) {
        const content = `
            <div class="ticket-details">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Ticket Information</h6>
                        <p><strong>Ticket #:</strong> ${ticket.ticket_number}</p>
                        <p><strong>Subject:</strong> ${ticket.subject}</p>
                        <p><strong>Category:</strong> ${ticket.category}</p>
                        <p><strong>Priority:</strong> ${ticket.priority}</p>
                        <p><strong>Status:</strong> ${ticket.status}</p>
                        <p><strong>Created:</strong> ${ticket.created_at}</p>
                    </div>
                    <div class="col-md-6">
                        <h6>User Information</h6>
                        <p><strong>Name:</strong> ${ticket.user_name}</p>
                        <p><strong>Email:</strong> ${ticket.user_email}</p>
                        <p><strong>User Type:</strong> ${ticket.user_type.replace('_', ' ')}</p>
                        <p><strong>Company:</strong> ${ticket.company_name}</p>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-12">
                        <h6>User Message</h6>
                        <div class="message-box">
                            ${ticket.message}
                        </div>
                    </div>
                </div>
                ${ticket.admin_response ? `
                <div class="row mt-3">
                    <div class="col-12">
                        <h6>Admin Response</h6>
                        <div class="response-box">
                            ${ticket.admin_response}
                        </div>
                    </div>
                </div>
                ` : ''}
            </div>
        `;
        
        document.getElementById('ticketDetailsContent').innerHTML = content;
        const modal = new bootstrap.Modal(document.getElementById('ticketDetailsModal'));
        modal.show();
    }
}

function respondToTicket(ticketId) {
    document.getElementById('responseTicketId').value = ticketId;
    const modal = new bootstrap.Modal(document.getElementById('responseModal'));
    modal.show();
}

// Response form submission
document.getElementById('responseForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const ticketId = document.getElementById('responseTicketId').value;
    const formData = new FormData(this);
    
    fetch(`/admin/support-tickets/${ticketId}/respond`, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            alert('Response sent successfully!');
            location.reload();
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while sending the response.');
    });
});
</script>

<style>
.page-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e9ecef;
}

.page-header h1 {
    color: #2c3e50;
    font-weight: 600;
}

.stat-card {
    color: white;
    padding: 1.5rem;
    border-radius: 10px;
    text-align: center;
    margin-bottom: 1rem;
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

.bg-orange {
    background-color: #fd7e14;
}

.filter-section {
    margin-bottom: 1rem;
}

.message-box, .response-box {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    padding: 1rem;
    margin: 0.5rem 0;
}

.response-box {
    background: #e7f3ff;
    border-color: #b8daff;
}

.empty-state {
    padding: 3rem 1rem;
}

.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
    background-color: #f8f9fa;
}
</style>
{% endblock %}