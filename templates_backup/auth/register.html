{% extends "base_public.html" %}

{% block content %}
<div class="auth-container">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="auth-card">
                <div class="auth-header">
                    <h2>🍀 Create Account</h2>
                    <p>Join the Cloverics logistics network</p>
                </div>

                {% if error %}
                <div class="alert alert-danger" role="alert">
                    {{ error }}
                </div>
                {% endif %}

                {% if success %}
                <div class="alert alert-success" role="alert">
                    {{ success }}
                </div>
                {% endif %}

                <form method="POST" action="/register">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="first_name" class="form-label">First Name</label>
                            <input type="text" class="form-control" id="first_name" name="first_name" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="last_name" class="form-label">Last Name</label>
                            <input type="text" class="form-control" id="last_name" name="last_name" required>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">Email Address</label>
                            <input type="email" class="form-control" id="email" name="email" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="phone_number" class="form-label">Phone Number</label>
                            <input type="tel" class="form-control" id="phone_number" name="phone_number" required>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="company_name" class="form-label">Company Name</label>
                        <input type="text" class="form-control" id="company_name" name="company_name" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="user_type" class="form-label">Account Type</label>
                        <select class="form-control" id="user_type" name="user_type" required>
                            <option value="">Select account type</option>
                            <option value="CUSTOMER">Customer - Ship goods worldwide</option>
                            <option value="LOGISTICS_PROVIDER">Logistics Provider - Offer shipping services</option>
                            <option value="INSURANCE_PROVIDER">Insurance Provider - Provide coverage</option>
                            <option value="INDEPENDENT_DRIVER">Independent Driver - Transport locally</option>
                        </select>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="password" class="form-label">Password</label>
                            <input type="password" class="form-control" id="password" name="password" required>
                            <small class="form-text text-muted">
                                Password must be at least 8 characters with uppercase, lowercase, number, and special character
                            </small>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="confirm_password" class="form-label">Confirm Password</label>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                        </div>
                    </div>
                    
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="terms" required>
                        <label class="form-check-label" for="terms">
                            I agree to the <a href="/terms">Terms of Service</a> and <a href="/privacy">Privacy Policy</a>
                        </label>
                    </div>
                    
                    <button type="submit" class="btn btn-primary w-100 mb-3">Create Account</button>
                </form>

                <div class="auth-footer">
                    <p>Already have an account? <a href="/login">Sign in here</a></p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.auth-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, #1E88E5 0%, #0D47A1 100%);
    padding: 2rem 0;
}

.auth-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.auth-header {
    text-align: center;
    margin-bottom: 2rem;
}

.auth-header h2 {
    color: #1E88E5;
    margin-bottom: 0.5rem;
}

.auth-header p {
    color: #666;
    margin: 0;
}

.form-label {
    font-weight: 500;
    color: #333;
}

.form-control {
    border-radius: 8px;
    border: 1px solid #ddd;
    padding: 12px;
}

.form-control:focus {
    border-color: #1E88E5;
    box-shadow: 0 0 0 0.2rem rgba(30, 136, 229, 0.25);
}

.btn-primary {
    background: #1E88E5;
    border: none;
    border-radius: 8px;
    padding: 12px;
    font-weight: 500;
}

.btn-primary:hover {
    background: #1976D2;
}

.auth-footer {
    text-align: center;
    margin-top: 1rem;
}

.auth-footer p {
    margin: 0.5rem 0;
    color: #666;
}

.auth-footer a {
    color: #1E88E5;
    text-decoration: none;
}

.auth-footer a:hover {
    text-decoration: underline;
}
</style>
{% endblock %}