{% extends "base.html" %}

{% block title %}Edit Route - Cloverics{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <h1><i class="fas fa-edit"></i> Edit Route</h1>
                <p class="text-muted">Update route details and pricing</p>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h3>Route Information</h3>
                <a href="/logistics/routes" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Routes
                </a>
            </div>

            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-route"></i> Edit Route Details</h5>
                </div>
                <div class="card-body">
                    <form id="editRouteForm" action="/logistics/route/{{ route.id }}/update" method="post">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="origin_country">Origin Country</label>
                                    <input type="text" class="form-control" id="origin_country" 
                                           name="origin_country" value="{{ route.origin_country }}" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="origin_city">Origin City</label>
                                    <input type="text" class="form-control" id="origin_city" 
                                           name="origin_city" value="{{ route.origin_city }}" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="destination_country">Destination Country</label>
                                    <input type="text" class="form-control" id="destination_country" 
                                           name="destination_country" value="{{ route.destination_country }}" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="destination_city">Destination City</label>
                                    <input type="text" class="form-control" id="destination_city" 
                                           name="destination_city" value="{{ route.destination_city }}" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="transport_type">Transport Type</label>
                                    <select class="form-control" id="transport_type" name="transport_type" required>
                                        <option value="truck" {% if route.transport_type == 'truck' %}selected{% endif %}>Truck</option>
                                        <option value="ship" {% if route.transport_type == 'ship' %}selected{% endif %}>Ship</option>
                                        <option value="air" {% if route.transport_type == 'air' %}selected{% endif %}>Air</option>
                                        <option value="rail" {% if route.transport_type == 'rail' %}selected{% endif %}>Rail</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="max_weight">Max Weight (kg)</label>
                                    <input type="number" class="form-control" id="max_weight" 
                                           name="max_weight" value="{{ route.max_weight_kg }}" 
                                           step="0.01" min="0" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="base_rate">Base Rate ($/kg)</label>
                                    <input type="number" class="form-control" id="base_rate" 
                                           name="base_rate" value="{{ route.base_rate_per_kg }}" 
                                           step="0.01" min="0" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="estimated_days">Estimated Days</label>
                                    <input type="number" class="form-control" id="estimated_days" 
                                           name="estimated_days" value="{{ route.estimated_days }}" 
                                           min="1" required>
                                </div>
                            </div>
                        </div>

                        <div class="form-group mb-3">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="is_active" 
                                       name="is_active" {% if route.is_active %}checked{% endif %}>
                                <label class="form-check-label" for="is_active">
                                    Active Route
                                </label>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <button type="button" class="btn btn-danger" onclick="deleteRoute()">
                                <i class="fas fa-trash"></i> Delete Route
                            </button>
                            <div>
                                <button type="button" class="btn btn-secondary" onclick="history.back()">
                                    <i class="fas fa-times"></i> Cancel
                                </button>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Update Route
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-bar"></i> Route Statistics</h5>
                </div>
                <div class="card-body">
                    <div class="stat-item mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Total Shipments:</span>
                            <strong>0</strong>
                        </div>
                    </div>
                    <div class="stat-item mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Revenue Generated:</span>
                            <strong>$0</strong>
                        </div>
                    </div>
                    <div class="stat-item mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Average Rating:</span>
                            <strong>N/A</strong>
                        </div>
                    </div>
                    <div class="stat-item mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Status:</span>
                            {% if route.is_active %}
                                <span class="badge badge-success">Active</span>
                            {% else %}
                                <span class="badge badge-secondary">Inactive</span>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function deleteRoute() {
    if (confirm('Are you sure you want to delete this route? This action cannot be undone.')) {
        fetch(`/logistics/route/{{ route.id }}/delete`, {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Route deleted successfully!');
                window.location.href = '/logistics/routes';
            } else {
                alert('Error deleting route: ' + (data.error || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while deleting the route.');
        });
    }
}


// Auto-generated function implementations

function history.back() {
    // Generic function implementation
    console.log('history.back called');
    showAlert('Function history.back executed', 'info');
}
</script>
{% endblock %}