{% extends "base.html" %}

{% block content %}
<div class="dashboard-container">
    <div class="dashboard-header">
        <h1>Logistics Provider Dashboard</h1>
        <p>Welcome back, {{ user.company_name or user.email }}</p>
    </div>

    <!-- Quick Stats -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-primary">
                    <i class="fas fa-route"></i>
                </div>
                <div class="stat-content">
                    <h3>{{ active_routes }}</h3>
                    <p>Active Routes</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-success">
                    <i class="fas fa-shipping-fast"></i>
                </div>
                <div class="stat-content">
                    <h3>{{ total_shipments }}</h3>
                    <p>Total Shipments</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-info">
                    <i class="fas fa-dollar-sign"></i>
                </div>
                <div class="stat-content">
                    <h3>${{ "%.0f"|format(monthly_revenue) }}</h3>
                    <p>Monthly Revenue</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-warning">
                    <i class="fas fa-handshake"></i>
                </div>
                <div class="stat-content">
                    <h3>{{ pending_quotes }}</h3>
                    <p>Pending Quotes</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="quick-actions-card">
                <h5>Quick Actions</h5>
                <div class="action-buttons">
                    <a href="/logistics/manage-shipments" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add Route
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Routes -->
    <div class="row">
        <div class="col-lg-8">
            <div class="data-card">
                <div class="card-header">
                    <h5>Recent Routes</h5>
                    <a href="/logistics/manage-shipments" class="btn btn-sm btn-outline-primary">Manage All</a>
                </div>
                <div class="card-body">
                    {% if routes %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Route</th>
                                    <th>Transport Type</th>
                                    <th>Capacity</th>
                                    <th>Rate</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for route in routes %}
                                <tr>
                                    <td>{{ route.origin_country }} → {{ route.destination_country }}</td>
                                    <td>
                                        <span class="badge bg-secondary">{{ route.transport_type }}</span>
                                    </td>
                                    <td>{{ route.capacity_kg }}kg</td>
                                    <td>${{ route.base_rate }}/kg</td>
                                    <td>
                                        <span class="status-badge {% if route.is_active %}status-active{% else %}status-inactive{% endif %}">
                                            {% if route.is_active %}Active{% else %}Inactive{% endif %}
                                        </span>
                                    </td>
                                    <td>
                                        <a href="/logistics/route/{{ route.id }}" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="empty-state">
                        <i class="fas fa-route"></i>
                        <h6>No routes configured</h6>
                        <p>Start by adding your first shipping route</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Performance Metrics -->
            <div class="data-card mb-3">
                <div class="card-header">
                    <h6>Performance This Month</h6>
                </div>
                <div class="card-body">
                    <div class="mini-stat">
                        <span class="label">Completion Rate</span>
                        <span class="value text-success">{{ performance_metrics.completion_rate }}%</span>
                    </div>
                    <div class="mini-stat">
                        <span class="label">Average Delivery Time</span>
                        <span class="value">{{ performance_metrics.average_delivery_time }} days</span>
                    </div>
                    <div class="mini-stat">
                        <span class="label">Customer Rating</span>
                        <span class="value text-warning">⭐ {{ performance_metrics.customer_satisfaction }}/5</span>
                    </div>
                    <div class="mini-stat">
                        <span class="label">Revenue Growth</span>
                        <span class="value text-info">{{ performance_metrics.revenue_growth }}%</span>
                    </div>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="data-card">
                <div class="card-header">
                    <h6>Recent Activity</h6>
                </div>
                <div class="card-body">
                    {% if recent_activity %}
                        {% for activity in recent_activity %}
                        <div class="notification-item">
                            <div class="notification-icon bg-info">
                                <i class="fas fa-shipping-fast"></i>
                            </div>
                            <div class="notification-content">
                                <p>{{ activity.message }}</p>
                                <small>{{ activity.date }} at {{ activity.time }}</small>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center py-3">
                            <p class="text-muted">No recent activity</p>
                            <small>Start managing shipments to see activity here</small>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.status-active {
    background: #D4EDDA;
    color: #155724;
}

.status-inactive {
    background: #F8D7DA;
    color: #721C24;
}

.badge {
    font-size: 0.75rem;
}
</style>
{% endblock %}