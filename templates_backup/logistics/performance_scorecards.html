<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Performance Scorecards - Cloverics</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="/static/base.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <style>
        .scorecard-container {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px 0;
        }
        
        .scorecard-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            overflow: hidden;
        }
        
        .scorecard-header {
            background: linear-gradient(135deg, #43cea2 0%, #185a9d 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .overall-score {
            font-size: 4rem;
            font-weight: bold;
            margin: 20px 0;
        }
        
        .performance-tier {
            font-size: 1.5rem;
            margin-bottom: 20px;
        }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            padding: 30px;
        }
        
        .metric-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            transition: transform 0.3s ease;
        }
        
        .metric-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }
        
        .metric-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
        }
        
        .metric-score {
            font-size: 2rem;
            font-weight: bold;
            margin: 10px 0;
        }
        
        .metric-description {
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .badges-container {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            justify-content: center;
            margin: 20px 0;
        }
        
        .performance-badge {
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .progress-ring {
            width: 100px;
            height: 100px;
            margin: 10px auto;
        }
        
        .progress-ring-circle {
            fill: none;
            stroke-width: 8;
            r: 40;
            cx: 50;
            cy: 50;
        }
        
        .trends-chart {
            height: 300px;
            margin: 20px 0;
        }
        
        .recommendations-list {
            background: #e7f3ff;
            border-left: 4px solid #007bff;
            padding: 20px;
            margin: 20px 0;
        }
        
        .statistics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .stat-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #e9ecef;
        }
        
        .control-panel {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }
        
        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 5px solid #f3f3f3;
            border-top: 5px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .alert-item {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }
        
        .comparison-mode {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    {% include 'logistics/logistics_sidebar.html' %}
    
    <div class="main-content">
        <div class="scorecard-container">
            <div class="container">
                <!-- Page Header -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="control-panel">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h1><i class="fas fa-chart-line me-2"></i>Performance Scorecards</h1>
                                    <p class="text-muted mb-0">Comprehensive analysis of your logistics performance metrics</p>
                                </div>
                                <div class="d-flex gap-2">
                                    <select id="periodSelector" class="form-select" style="width: auto;">
                                        <option value="30">Last 30 Days</option>
                                        <option value="90" selected>Last 90 Days</option>
                                        <option value="180">Last 6 Months</option>
                                        <option value="365">Last Year</option>
                                    </select>
                                    <button class="btn btn-primary" onclick="refreshScorecard()">
                                        <i class="fas fa-sync-alt me-1"></i>Refresh
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Main Scorecard -->
                <div class="row">
                    <div class="col-12">
                        <div class="scorecard-card" id="mainScorecardCard">
                            <div class="scorecard-header">
                                <h2 id="providerName">{{ user.company_name or (user.first_name + ' ' + user.last_name) }}</h2>
                                <div class="overall-score" id="overallScore">--.--</div>
                                <div class="performance-tier" id="performanceTier">Loading...</div>
                                <div class="badges-container" id="performanceBadges"></div>
                            </div>
                            
                            <div class="metrics-grid" id="metricsGrid">
                                <!-- Metrics will be populated dynamically -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Performance Trends Chart -->
                <div class="row">
                    <div class="col-lg-8">
                        <div class="scorecard-card">
                            <div class="card-body">
                                <h4><i class="fas fa-trending-up me-2"></i>Performance Trends</h4>
                                <canvas id="trendsChart" class="trends-chart"></canvas>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4">
                        <div class="scorecard-card">
                            <div class="card-body">
                                <h4><i class="fas fa-chart-pie me-2"></i>Score Breakdown</h4>
                                <canvas id="breakdownChart" style="height: 300px;"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Statistics and Recommendations -->
                <div class="row">
                    <div class="col-lg-6">
                        <div class="scorecard-card">
                            <div class="card-body">
                                <h4><i class="fas fa-chart-bar me-2"></i>Key Statistics</h4>
                                <div class="statistics-grid" id="statisticsGrid">
                                    <!-- Statistics will be populated dynamically -->
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="scorecard-card">
                            <div class="card-body">
                                <h4><i class="fas fa-lightbulb me-2"></i>Performance Recommendations</h4>
                                <div id="recommendationsList">
                                    <div class="text-center py-4">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">Loading recommendations...</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Performance Alerts -->
                <div class="row" id="alertsSection" style="display: none;">
                    <div class="col-12">
                        <div class="scorecard-card">
                            <div class="card-body">
                                <h4><i class="fas fa-exclamation-triangle me-2"></i>Performance Alerts</h4>
                                <div id="alertsList"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Top Performers Comparison -->
                <div class="row">
                    <div class="col-12">
                        <div class="scorecard-card">
                            <div class="card-body">
                                <h4><i class="fas fa-trophy me-2"></i>Platform Top Performers</h4>
                                <div class="comparison-mode">
                                    <p class="text-muted">See how you compare with the top performing logistics providers on our platform</p>
                                    <div id="topPerformersTable">
                                        <div class="text-center py-4">
                                            <div class="spinner-border text-primary" role="status">
                                                <span class="visually-hidden">Loading top performers...</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay" style="display: none;">
        <div class="loading-spinner"></div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentScorecard = null;
        let trendsChart = null;
        let breakdownChart = null;

        // Initialize the scorecard on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadScorecard();
            loadTopPerformers();
        });

        // Load scorecard data
        async function loadScorecard() {
            showLoading(true);
            
            try {
                const days = document.getElementById('periodSelector').value;
                const response = await fetch(`/api/scorecard/my-performance?days_back=${days}`);
                const data = await response.json();
                
                if (data.success) {
                    currentScorecard = data.scorecard;
                    displayScorecard(data.scorecard);
                    displayCharts(data.scorecard);
                    displayStatistics(data.scorecard);
                    displayRecommendations(data.scorecard);
                    displayAlerts(data.scorecard);
                } else {
                    Swal.fire('Error', data.error, 'error');
                }
            } catch (error) {
                console.error('Error loading scorecard:', error);
                Swal.fire('Error', 'Failed to load performance data', 'error');
            }
            
            showLoading(false);
        }

        // Display main scorecard
        function displayScorecard(scorecard) {
            document.getElementById('overallScore').textContent = scorecard.overall_score || '0.00';
            document.getElementById('performanceTier').textContent = scorecard.performance_tier || 'Unknown';
            
            // Display performance badges
            const badgesContainer = document.getElementById('performanceBadges');
            badgesContainer.innerHTML = '';
            
            if (scorecard.badges && scorecard.badges.length > 0) {
                scorecard.badges.forEach(badge => {
                    const badgeElement = document.createElement('span');
                    badgeElement.className = `performance-badge badge bg-${badge.color}`;
                    badgeElement.innerHTML = `<i class="fas fa-${badge.icon} me-1"></i>${badge.name}`;
                    badgeElement.title = badge.description;
                    badgesContainer.appendChild(badgeElement);
                });
            }
            
            // Display metrics
            const metricsGrid = document.getElementById('metricsGrid');
            metricsGrid.innerHTML = '';
            
            const metricsConfig = {
                'on_time_delivery': { icon: 'clock', color: '#28a745', name: 'On-Time Delivery' },
                'customer_rating': { icon: 'star', color: '#ffc107', name: 'Customer Rating' },
                'response_time': { icon: 'lightning', color: '#17a2b8', name: 'Response Time' },
                'completion_rate': { icon: 'check-circle', color: '#007bff', name: 'Completion Rate' },
                'pricing_competitiveness': { icon: 'dollar-sign', color: '#28a745', name: 'Pricing Competitiveness' },
                'communication_quality': { icon: 'comments', color: '#6f42c1', name: 'Communication Quality' },
                'reliability_score': { icon: 'shield-alt', color: '#fd7e14', name: 'Reliability Score' }
            };
            
            if (scorecard.metrics) {
                Object.keys(scorecard.metrics).forEach(metricKey => {
                    const metric = scorecard.metrics[metricKey];
                    const config = metricsConfig[metricKey];
                    
                    if (config) {
                        const metricCard = createMetricCard(metric, config);
                        metricsGrid.appendChild(metricCard);
                    }
                });
            }
        }

        // Create metric card
        function createMetricCard(metric, config) {
            const card = document.createElement('div');
            card.className = 'metric-card';
            
            const score = metric.score || 0;
            const percentage = (score / 5) * 100;
            
            card.innerHTML = `
                <div class="metric-icon" style="color: ${config.color};">
                    <i class="fas fa-${config.icon}"></i>
                </div>
                <h5>${config.name}</h5>
                <div class="metric-score" style="color: ${config.color};">${score.toFixed(1)}/5.0</div>
                <div class="progress mb-2">
                    <div class="progress-bar" style="width: ${percentage}%; background-color: ${config.color};" role="progressbar"></div>
                </div>
                <div class="metric-description">
                    ${getMetricDescription(metric, config.name)}
                </div>
            `;
            
            return card;
        }

        // Get metric description
        function getMetricDescription(metric, name) {
            switch (name) {
                case 'On-Time Delivery':
                    return `${metric.percentage || 0}% of deliveries on time`;
                case 'Customer Rating':
                    return `Average rating: ${metric.rating || 0}/5.0`;
                case 'Response Time':
                    return `Average: ${metric.average_hours || 0} hours`;
                case 'Completion Rate':
                    return `${metric.percentage || 0}% completion rate`;
                case 'Pricing Competitiveness':
                    return metric.market_position || 'Market average';
                case 'Communication Quality':
                    return `Communication rating: ${metric.rating || 0}/5.0`;
                case 'Reliability Score':
                    return metric.consistency_level || 'Average';
                default:
                    return 'Performance metric';
            }
        }

        // Display charts
        function displayCharts(scorecard) {
            // Trends chart
            if (trendsChart) {
                trendsChart.destroy();
            }
            
            const trendsCtx = document.getElementById('trendsChart').getContext('2d');
            const trendsData = scorecard.trends || {};
            
            trendsChart = new Chart(trendsCtx, {
                type: 'line',
                data: {
                    labels: ['30 Days', '60 Days', '90 Days'],
                    datasets: [{
                        label: 'Average Rating',
                        data: [
                            trendsData.last_30_days?.average_rating || 0,
                            trendsData.last_60_days?.average_rating || 0,
                            trendsData.last_90_days?.average_rating || 0
                        ],
                        borderColor: '#007bff',
                        backgroundColor: 'rgba(0, 123, 255, 0.1)',
                        tension: 0.4
                    }, {
                        label: 'Completion Rate',
                        data: [
                            trendsData.last_30_days?.completion_rate || 0,
                            trendsData.last_60_days?.completion_rate || 0,
                            trendsData.last_90_days?.completion_rate || 0
                        ],
                        borderColor: '#28a745',
                        backgroundColor: 'rgba(40, 167, 69, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 5
                        }
                    }
                }
            });
            
            // Breakdown chart
            if (breakdownChart) {
                breakdownChart.destroy();
            }
            
            const breakdownCtx = document.getElementById('breakdownChart').getContext('2d');
            const metrics = scorecard.metrics || {};
            
            breakdownChart = new Chart(breakdownCtx, {
                type: 'doughnut',
                data: {
                    labels: Object.keys(metrics).map(key => key.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())),
                    datasets: [{
                        data: Object.values(metrics).map(m => m.score || 0),
                        backgroundColor: [
                            '#007bff',
                            '#28a745',
                            '#ffc107',
                            '#dc3545',
                            '#17a2b8',
                            '#6f42c1',
                            '#fd7e14'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        // Display statistics
        function displayStatistics(scorecard) {
            const statisticsGrid = document.getElementById('statisticsGrid');
            const stats = scorecard.statistics || {};
            
            const statisticsHtml = `
                <div class="stat-item">
                    <i class="fas fa-shipping-fast text-primary mb-2" style="font-size: 1.5rem;"></i>
                    <h5>${stats.total_shipments || 0}</h5>
                    <small class="text-muted">Total Shipments</small>
                </div>
                <div class="stat-item">
                    <i class="fas fa-quote-left text-success mb-2" style="font-size: 1.5rem;"></i>
                    <h5>${stats.total_quotes || 0}</h5>
                    <small class="text-muted">Quote Requests</small>
                </div>
                <div class="stat-item">
                    <i class="fas fa-route text-warning mb-2" style="font-size: 1.5rem;"></i>
                    <h5>${stats.active_routes || 0}</h5>
                    <small class="text-muted">Active Routes</small>
                </div>
                <div class="stat-item">
                    <i class="fas fa-dollar-sign text-info mb-2" style="font-size: 1.5rem;"></i>
                    <h5>$${(stats.revenue_generated || 0).toLocaleString()}</h5>
                    <small class="text-muted">Revenue Generated</small>
                </div>
                <div class="stat-item">
                    <i class="fas fa-users text-secondary mb-2" style="font-size: 1.5rem;"></i>
                    <h5>${stats.customer_count || 0}</h5>
                    <small class="text-muted">Unique Customers</small>
                </div>
            `;
            
            statisticsGrid.innerHTML = statisticsHtml;
        }

        // Display recommendations
        function displayRecommendations(scorecard) {
            const recommendationsList = document.getElementById('recommendationsList');
            const recommendations = scorecard.recommendations || [];
            
            if (recommendations.length === 0) {
                recommendationsList.innerHTML = '<p class="text-muted">No specific recommendations at this time. Keep up the good work!</p>';
                return;
            }
            
            let html = '<div class="recommendations-list">';
            recommendations.forEach((rec, index) => {
                html += `
                    <div class="d-flex align-items-start mb-3">
                        <div class="badge bg-primary rounded-pill me-3 mt-1">${index + 1}</div>
                        <div>
                            <p class="mb-0">${rec}</p>
                        </div>
                    </div>
                `;
            });
            html += '</div>';
            
            recommendationsList.innerHTML = html;
        }

        // Display alerts
        function displayAlerts(scorecard) {
            const alertsSection = document.getElementById('alertsSection');
            const alertsList = document.getElementById('alertsList');
            const alerts = scorecard.alerts || [];
            
            if (alerts.length === 0) {
                alertsSection.style.display = 'none';
                return;
            }
            
            alertsSection.style.display = 'block';
            
            let html = '';
            alerts.forEach(alert => {
                const severityClass = alert.severity === 'high' ? 'warning' : 'info';
                html += `
                    <div class="alert alert-${severityClass} d-flex align-items-center" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <div>
                            <strong>${alert.type.replace('_', ' ').toUpperCase()}:</strong> ${alert.message}
                            ${alert.recommendation ? `<br><small class="text-muted">Recommendation: ${alert.recommendation}</small>` : ''}
                        </div>
                    </div>
                `;
            });
            
            alertsList.innerHTML = html;
        }

        // Load top performers
        async function loadTopPerformers() {
            try {
                const response = await fetch('/api/scorecard/top-performers?limit=5');
                const data = await response.json();
                
                if (data.success && data.top_performers) {
                    displayTopPerformers(data.top_performers);
                }
            } catch (error) {
                console.error('Error loading top performers:', error);
            }
        }

        // Display top performers
        function displayTopPerformers(performers) {
            const container = document.getElementById('topPerformersTable');
            
            if (performers.length === 0) {
                container.innerHTML = '<p class="text-muted">No performance data available for comparison.</p>';
                return;
            }
            
            let html = `
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Rank</th>
                                <th>Provider</th>
                                <th>Average Rating</th>
                                <th>Total Shipments</th>
                                <th>Performance Tier</th>
                            </tr>
                        </thead>
                        <tbody>
            `;
            
            performers.forEach((performer, index) => {
                const tierBadge = getTierBadge(performer.performance_tier);
                html += `
                    <tr>
                        <td>
                            <span class="badge bg-primary">#${index + 1}</span>
                        </td>
                        <td>${performer.provider_name}</td>
                        <td>
                            <div class="d-flex align-items-center">
                                <span class="me-2">${performer.average_rating.toFixed(1)}</span>
                                <div class="text-warning">
                                    ${getStarRating(performer.average_rating)}
                                </div>
                            </div>
                        </td>
                        <td>${performer.total_shipments}</td>
                        <td>${tierBadge}</td>
                    </tr>
                `;
            });
            
            html += '</tbody></table></div>';
            container.innerHTML = html;
        }

        // Helper functions
        function getTierBadge(tier) {
            const colors = {
                'Excellent': 'success',
                'Good': 'primary',
                'Average': 'warning',
                'Needs Improvement': 'danger'
            };
            return `<span class="badge bg-${colors[tier] || 'secondary'}">${tier}</span>`;
        }

        function getStarRating(rating) {
            const fullStars = Math.floor(rating);
            const halfStar = rating % 1 >= 0.5;
            let stars = '';
            
            for (let i = 0; i < fullStars; i++) {
                stars += '<i class="fas fa-star"></i>';
            }
            if (halfStar) {
                stars += '<i class="fas fa-star-half-alt"></i>';
            }
            for (let i = fullStars + (halfStar ? 1 : 0); i < 5; i++) {
                stars += '<i class="far fa-star"></i>';
            }
            
            return stars;
        }

        function showLoading(show) {
            document.getElementById('loadingOverlay').style.display = show ? 'flex' : 'none';
        }

        // Refresh scorecard
        function refreshScorecard() {
            loadScorecard();
        }

        // Period selector change handler
        document.getElementById('periodSelector').addEventListener('change', function() {
            loadScorecard();
        });
    </script>
</body>
</html>