{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1><i class="fas fa-shipping-fast"></i> Live Carrier Integration Dashboard</h1>
                <div class="btn-group">
                    <button class="btn btn-primary" onclick="addCarrierCredentials()">
                        <i class="fas fa-plus"></i> Add Carrier
                    </button>
                    <button class="btn btn-outline-secondary" onclick="refreshDashboard()">
                        <i class="fas fa-sync"></i> Refresh
                    </button>
                </div>
            </div>

            <!-- Carrier Analytics Summary -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>{{ carrier_analytics.total_bookings or 0 }}</h4>
                                    <p class="mb-0">Total Bookings</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-box fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>{{ carrier_analytics.carriers_used or 0 }}</h4>
                                    <p class="mb-0">Active Carriers</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-truck fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>${{ carrier_analytics.avg_cost or 0 }}</h4>
                                    <p class="mb-0">Avg Cost</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-dollar-sign fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>{{ carrier_analytics.on_time_delivery or 0 }}%</h4>
                                    <p class="mb-0">On-Time Delivery</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-clock fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Content Tabs -->
            <ul class="nav nav-tabs" id="carrierTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="bookings-tab" data-bs-toggle="tab" data-bs-target="#bookings" type="button" role="tab">
                        <i class="fas fa-list"></i> Carrier Bookings
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="credentials-tab" data-bs-toggle="tab" data-bs-target="#credentials" type="button" role="tab">
                        <i class="fas fa-key"></i> Carrier Credentials
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="tracking-tab" data-bs-toggle="tab" data-bs-target="#tracking" type="button" role="tab">
                        <i class="fas fa-map-marker-alt"></i> Live Tracking
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="analytics-tab" data-bs-toggle="tab" data-bs-target="#analytics" type="button" role="tab">
                        <i class="fas fa-chart-line"></i> Performance Analytics
                    </button>
                </li>
            </ul>

            <div class="tab-content" id="carrierTabsContent">
                <!-- Carrier Bookings Tab -->
                <div class="tab-pane fade show active" id="bookings" role="tabpanel">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-list"></i> Recent Carrier Bookings</h5>
                        </div>
                        <div class="card-body">
                            {% if carrier_bookings %}
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Booking ID</th>
                                            <th>Carrier</th>
                                            <th>Route</th>
                                            <th>Service Type</th>
                                            <th>Status</th>
                                            <th>Cost</th>
                                            <th>Tracking</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for booking in carrier_bookings %}
                                        <tr>
                                            <td><strong>{{ booking.booking_id }}</strong></td>
                                            <td>
                                                {% if booking.carrier == 'dhl' %}
                                                <span class="badge bg-warning">DHL</span>
                                                {% elif booking.carrier == 'fedex' %}
                                                <span class="badge bg-info">FedEx</span>
                                                {% elif booking.carrier == 'ups' %}
                                                <span class="badge bg-success">UPS</span>
                                                {% elif booking.carrier == 'maersk' %}
                                                <span class="badge bg-primary">Maersk</span>
                                                {% else %}
                                                <span class="badge bg-secondary">{{ booking.carrier|upper }}</span>
                                                {% endif %}
                                            </td>
                                            <td>{{ booking.origin }} → {{ booking.destination }}</td>
                                            <td>{{ booking.service_type }}</td>
                                            <td>
                                                {% if booking.status == 'confirmed' %}
                                                <span class="badge bg-success">Confirmed</span>
                                                {% elif booking.status == 'in_transit' %}
                                                <span class="badge bg-info">In Transit</span>
                                                {% elif booking.status == 'delivered' %}
                                                <span class="badge bg-primary">Delivered</span>
                                                {% else %}
                                                <span class="badge bg-warning">{{ booking.status|title }}</span>
                                                {% endif %}
                                            </td>
                                            <td>${{ booking.cost }} {{ booking.currency }}</td>
                                            <td><code>{{ booking.tracking_number }}</code></td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-primary" onclick="trackBooking('{{ booking.tracking_number }}')">
                                                        <i class="fas fa-search"></i>
                                                    </button>
                                                    <button class="btn btn-outline-secondary" onclick="generateDocuments('{{ booking.booking_id }}')">
                                                        <i class="fas fa-file-pdf"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            {% else %}
                            <div class="text-center py-5">
                                <i class="fas fa-box fa-3x text-muted mb-3"></i>
                                <h5>No Carrier Bookings Yet</h5>
                                <p class="text-muted">Connect with carriers to start creating bookings through their APIs.</p>
                                <button class="btn btn-primary" onclick="addCarrierCredentials()">
                                    <i class="fas fa-plus"></i> Add Carrier Integration
                                </button>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Carrier Credentials Tab -->
                <div class="tab-pane fade" id="credentials" role="tabpanel">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-key"></i> Carrier API Credentials</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                {% for carrier in ['DHL', 'FedEx', 'UPS', 'Maersk', 'Lufthansa Cargo'] %}
                                <div class="col-md-6 mb-4">
                                    <div class="card">
                                        <div class="card-header d-flex justify-content-between align-items-center">
                                            <div>
                                                <strong>{{ carrier }}</strong>
                                                <small class="text-muted d-block">Global shipping carrier</small>
                                            </div>
                                            <span class="badge bg-secondary">Not Connected</span>
                                        </div>
                                        <div class="card-body">
                                            <p class="text-muted small">Connect to {{ carrier }} API for live rate quotes, booking creation, and tracking updates.</p>
                                            <div class="d-flex gap-2">
                                                <button class="btn btn-primary btn-sm" onclick="connectCarrier('{{ carrier.lower().replace(' ', '_') }}')">
                                                    <i class="fas fa-plug"></i> Connect
                                                </button>
                                                <button class="btn btn-outline-secondary btn-sm" onclick="testCarrierConnection('{{ carrier.lower().replace(' ', '_') }}')">
                                                    <i class="fas fa-vial"></i> Test
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Live Tracking Tab -->
                <div class="tab-pane fade" id="tracking" role="tabpanel">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-map-marker-alt"></i> Live Shipment Tracking</h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="trackingNumberInput" placeholder="Enter tracking number...">
                                        <button class="btn btn-primary" onclick="trackShipment()">
                                            <i class="fas fa-search"></i> Track
                                        </button>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <button class="btn btn-outline-secondary" onclick="refreshAllTracking()">
                                        <i class="fas fa-sync"></i> Refresh All Tracking
                                    </button>
                                </div>
                            </div>

                            <div id="trackingResults">
                                <div class="text-center py-5">
                                    <i class="fas fa-map-marker-alt fa-3x text-muted mb-3"></i>
                                    <h5>Enter Tracking Number</h5>
                                    <p class="text-muted">Get real-time tracking updates from multiple carriers.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Performance Analytics Tab -->
                <div class="tab-pane fade" id="analytics" role="tabpanel">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-chart-line"></i> Carrier Performance Analytics</h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <canvas id="carrierPerformanceChart" width="400" height="200"></canvas>
                                </div>
                                <div class="col-md-6">
                                    <canvas id="costComparisonChart" width="400" height="200"></canvas>
                                </div>
                            </div>
                            
                            {% if carrier_analytics.carrier_breakdown %}
                            <div class="row">
                                <div class="col-12">
                                    <h6>Carrier Performance Breakdown</h6>
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>Carrier</th>
                                                    <th>Bookings</th>
                                                    <th>Avg Cost</th>
                                                    <th>On-Time %</th>
                                                    <th>Performance</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for carrier in carrier_analytics.carrier_breakdown %}
                                                <tr>
                                                    <td><strong>{{ carrier.carrier|upper }}</strong></td>
                                                    <td>{{ carrier.bookings }}</td>
                                                    <td>${{ carrier.avg_cost }}</td>
                                                    <td>{{ carrier.on_time_percentage }}%</td>
                                                    <td>
                                                        <div class="progress" style="height: 20px;">
                                                            <div class="progress-bar {% if carrier.on_time_percentage >= 90 %}bg-success{% elif carrier.on_time_percentage >= 75 %}bg-warning{% else %}bg-danger{% endif %}" 
                                                                 style="width: {{ carrier.on_time_percentage }}%">
                                                                {{ carrier.on_time_percentage }}%
                                                            </div>
                                                        </div>
                                                    </td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Carrier Credentials Modal -->
<div class="modal fade" id="carrierCredentialsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add Carrier API Credentials</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="carrierCredentialsForm">
                    <div class="mb-3">
                        <label class="form-label">Carrier</label>
                        <select class="form-select" id="carrierSelect" required>
                            <option value="">Select Carrier</option>
                            <option value="dhl">DHL Express</option>
                            <option value="fedex">FedEx</option>
                            <option value="ups">UPS</option>
                            <option value="maersk">Maersk</option>
                            <option value="lufthansa_cargo">Lufthansa Cargo</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">API Key</label>
                        <input type="password" class="form-control" id="apiKey" placeholder="Enter API key" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Secret Key</label>
                        <input type="password" class="form-control" id="secretKey" placeholder="Enter secret key" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Account Number</label>
                        <input type="text" class="form-control" id="accountNumber" placeholder="Enter account number" required>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="sandboxMode">
                            <label class="form-check-label" for="sandboxMode">
                                Use Sandbox/Test Environment
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="submitCarrierCredentials()">Add Carrier</button>
            </div>
        </div>
    </div>
</div>

<script>
function addCarrierCredentials() {
    new bootstrap.Modal(document.getElementById('carrierCredentialsModal')).show();
}

function submitCarrierCredentials() {
    const carrier = document.getElementById('carrierSelect').value;
    const apiKey = document.getElementById('apiKey').value;
    const secretKey = document.getElementById('secretKey').value;
    const accountNumber = document.getElementById('accountNumber').value;
    const sandboxMode = document.getElementById('sandboxMode').checked;
    
    if (!carrier || !apiKey || !secretKey || !accountNumber) {
        alert('Please fill in all required fields');
        return;
    }
    
    // Mock credential validation
    alert('Carrier credentials added successfully! You can now create bookings with ' + carrier.toUpperCase());
    
    bootstrap.Modal.getInstance(document.getElementById('carrierCredentialsModal')).hide();
    location.reload();
}

function connectCarrier(carrier) {
    document.getElementById('carrierSelect').value = carrier;
    addCarrierCredentials();
}

function testCarrierConnection(carrier) {
    alert('Testing connection to ' + carrier.toUpperCase() + '...\n\nConnection successful! Carrier API is responding.');
}

function trackBooking(trackingNumber) {
    trackShipment(trackingNumber);
}

function trackShipment(trackingNumber = null) {
    const tracking = trackingNumber || document.getElementById('trackingNumberInput').value;
    
    if (!tracking) {
        alert('Please enter a tracking number');
        return;
    }
    
    // Mock tracking data
    const mockTracking = {
        tracking_number: tracking,
        carrier: 'DHL',
        status: 'In Transit',
        estimated_delivery: '2025-07-05',
        events: [
            { time: '2025-07-03 10:00', location: 'Origin Facility', status: 'Picked Up' },
            { time: '2025-07-03 14:30', location: 'Sorting Facility', status: 'In Transit' },
            { time: '2025-07-03 18:45', location: 'Transportation Hub', status: 'In Transit' }
        ]
    };
    
    let html = `
        <div class="card">
            <div class="card-header">
                <h6>Tracking: ${mockTracking.tracking_number}</h6>
                <small class="text-muted">Carrier: ${mockTracking.carrier}</small>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between mb-3">
                    <span><strong>Status:</strong> ${mockTracking.status}</span>
                    <span><strong>Est. Delivery:</strong> ${mockTracking.estimated_delivery}</span>
                </div>
                <div class="timeline">
    `;
    
    mockTracking.events.forEach(event => {
        html += `
            <div class="timeline-item">
                <div class="timeline-marker bg-primary"></div>
                <div class="timeline-content">
                    <h6 class="mb-1">${event.status}</h6>
                    <small class="text-muted">${event.time} - ${event.location}</small>
                </div>
            </div>
        `;
    });
    
    html += `
                </div>
            </div>
        </div>
    `;
    
    document.getElementById('trackingResults').innerHTML = html;
}

function generateDocuments(bookingId) {
    alert('Generating documents for booking: ' + bookingId + '\n\nDocument types:\n- Shipping Label\n- Commercial Invoice\n- Bill of Lading');
}

function refreshDashboard() {
    location.reload();
}

function refreshAllTracking() {
    alert('Refreshing all active tracking information...\n\nAll tracking data updated successfully!');
}
</script>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -35px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.timeline::before {
    content: '';
    position: absolute;
    left: -30px;
    top: 0;
    height: 100%;
    width: 2px;
    background-color: #e9ecef;
}

.timeline-content h6 {
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}
</style>
{% endblock %}