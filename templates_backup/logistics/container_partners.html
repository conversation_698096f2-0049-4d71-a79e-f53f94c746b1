{% extends "base.html" %}

{% block content %}
<div class="dashboard-container">
    <div class="dashboard-header">
        <h1>Container Partners - {{ container.container_id }}</h1>
        <p>Manage partners and view capacity utilization for shared container</p>
    </div>

    <!-- Container Overview Stats -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-primary">
                    <i class="fas fa-route"></i>
                </div>
                <div class="stat-content">
                    <h3>{{ container.route }}</h3>
                    <p>Route</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-success">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stat-content">
                    <h3>{{ container.participants }}</h3>
                    <p>Partners</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-warning">
                    <i class="fas fa-percentage"></i>
                </div>
                <div class="stat-content">
                    <h3>{{ container.utilization_rate }}%</h3>
                    <p>Utilization</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-info">
                    <i class="fas fa-calendar"></i>
                </div>
                <div class="stat-content">
                    <h3>{{ container.departure_date }}</h3>
                    <p>Departure</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Capacity Overview -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="stat-card">
                <h4>Weight Capacity</h4>
                <div class="progress mb-2">
                    <div class="progress-bar bg-primary" style="width: {{ container.utilization_rate }}%"></div>
                </div>
                <small>{{ container.used_capacity_kg|round }} / {{ container.total_capacity_kg|round }} kg</small>
                <br><small class="text-success">Available: {{ container.available_weight|round }} kg</small>
            </div>
        </div>
        <div class="col-md-6">
            <div class="stat-card">
                <h4>Volume Capacity</h4>
                <div class="progress mb-2">
                    <div class="progress-bar bg-success" style="width: {{ container.utilization_rate }}%"></div>
                </div>
                <small>{{ container.used_capacity_m3|round(1) }} / {{ container.total_capacity_m3|round(1) }} m³</small>
                <br><small class="text-success">Available: {{ container.available_volume|round(1) }} m³</small>
            </div>
        </div>
    </div>

    <!-- Partners Table -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">Container Partners</h5>
            <div class="btn-group">
                <button class="btn btn-outline-primary btn-sm" onclick="exportPartnersData()">
                    <i class="fas fa-download"></i> Export
                </button>
                <button class="btn btn-primary btn-sm" onclick="refreshPartners()">
                    <i class="fas fa-sync"></i> Refresh
                </button>
            </div>
        </div>
        <div class="card-body">
            {% if partners %}
            <div class="table-responsive">
                <table class="table table-hover" id="partnersTable">
                    <thead>
                        <tr>
                            <th>Customer</th>
                            <th>Cargo Description</th>
                            <th>Weight (kg)</th>
                            <th>Volume (m³)</th>
                            <th>Cost</th>
                            <th>Status</th>
                            <th>Join Date</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for partner in partners %}
                        <tr id="partner-{{ partner.id }}">
                            <td>
                                <div>
                                    <strong>{{ partner.customer_name }}</strong>
                                    <br><small class="text-muted">{{ partner.customer_email }}</small>
                                </div>
                            </td>
                            <td>
                                <span class="cargo-desc">{{ partner.cargo_description }}</span>
                                {% if partner.special_requirements %}
                                <br><small class="text-info"><i class="fas fa-exclamation-circle"></i> {{ partner.special_requirements }}</small>
                                {% endif %}
                            </td>
                            <td>{{ partner.weight_kg|round }}</td>
                            <td>{{ partner.volume_m3|round(1) }}</td>
                            <td class="text-success">
                                <strong>${{ partner.individual_cost|round(2) }}</strong>
                            </td>
                            <td>
                                <span class="badge badge-{{ partner.status_class }}">
                                    {{ partner.status_display }}
                                </span>
                            </td>
                            <td>{{ partner.invited_at }}</td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    {% if partner.status == 'PENDING' %}
                                    <button class="btn btn-success btn-sm" onclick="approvePartner({{ partner.id }})">
                                        <i class="fas fa-check"></i> Approve
                                    </button>
                                    <button class="btn btn-danger btn-sm" onclick="declinePartner({{ partner.id }})">
                                        <i class="fas fa-times"></i> Decline
                                    </button>
                                    {% elif partner.status == 'ACCEPTED' %}
                                    <button class="btn btn-info btn-sm" onclick="contactCustomer('{{ partner.customer_email }}')">
                                        <i class="fas fa-envelope"></i> Contact
                                    </button>
                                    {% else %}
                                    <button class="btn btn-outline-secondary btn-sm" disabled>
                                        <i class="fas fa-info-circle"></i> {{ partner.status_display.0 }}
                                    </button>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No Partners Yet</h5>
                <p class="text-muted">Customers can request to join this shared container from the shipping search page.</p>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Approve/Decline Modal -->
<div class="modal fade" id="actionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="actionModalTitle">Confirm Action</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p id="actionModalBody"></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="confirmActionBtn" onclick="confirmAction(this)">Confirm</button>
            </div>
        </div>
    </div>
</div>

<style>
.stat-card {
    background: white;
    border: 1px solid #e3e6f0;
    border-radius: 0.35rem;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    padding: 1.25rem;
    margin-bottom: 1rem;
}

.stat-card h3 {
    font-size: 1.75rem;
    font-weight: 700;
    margin: 0;
    color: #5a5c69;
}

.stat-card h4 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #5a5c69;
}

.stat-icon {
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
    float: left;
    margin-right: 1rem;
}

.stat-content {
    overflow: hidden;
}

.cargo-desc {
    max-width: 200px;
    display: inline-block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.progress {
    height: 8px;
}
</style>

<script>
let currentPartnerId = null;
let currentAction = null;

function approvePartner(partnerId) {
    currentPartnerId = partnerId;
    currentAction = 'approve';
    
    document.getElementById('actionModalTitle').textContent = 'Approve Partner';
    document.getElementById('actionModalBody').textContent = 'Are you sure you want to approve this partner to join the shared container?';
    document.getElementById('confirmActionBtn').className = 'btn btn-success';
    document.getElementById('confirmActionBtn').textContent = 'Approve';
    
    new bootstrap.Modal(document.getElementById('actionModal')).show();
}

function declinePartner(partnerId) {
    currentPartnerId = partnerId;
    currentAction = 'decline';
    
    document.getElementById('actionModalTitle').textContent = 'Decline Partner';
    document.getElementById('actionModalBody').textContent = 'Are you sure you want to decline this partner request?';
    document.getElementById('confirmActionBtn').className = 'btn btn-danger';
    document.getElementById('confirmActionBtn').textContent = 'Decline';
    
    new bootstrap.Modal(document.getElementById('actionModal')).show();
}

document.getElementById('confirmActionBtn').addEventListener('click', function() {
    if (currentAction === 'approve') {
        processApproval(currentPartnerId);
    } else if (currentAction === 'decline') {
        processDecline(currentPartnerId);
    }
    
    bootstrap.Modal.getInstance(document.getElementById('actionModal')).hide();
});

async function processApproval(partnerId) {
    try {
        const response = await fetch(`/api/shared-container/invitation/${partnerId}/approve`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });
        
        const result = await response.json();
        
        if (result.success) {
            Swal.fire({
                title: 'Success!',
                text: 'Partner approved successfully',
                icon: 'success',
                timer: 2000
            });
            
            // Update the row
            updatePartnerRow(partnerId, 'ACCEPTED', 'Approved', 'success');
        } else {
            Swal.fire('Error', result.error || 'Failed to approve partner', 'error');
        }
    } catch (error) {
        console.error('Error approving partner:', error);
        Swal.fire('Error', 'Network error occurred', 'error');
    }
}

async function processDecline(partnerId) {
    try {
        const response = await fetch(`/api/shared-container/invitation/${partnerId}/decline`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });
        
        const result = await response.json();
        
        if (result.success) {
            Swal.fire({
                title: 'Declined',
                text: 'Partner request declined',
                icon: 'info',
                timer: 2000
            });
            
            // Update the row
            updatePartnerRow(partnerId, 'DECLINED', 'Declined', 'danger');
        } else {
            Swal.fire('Error', result.error || 'Failed to decline partner', 'error');
        }
    } catch (error) {
        console.error('Error declining partner:', error);
        Swal.fire('Error', 'Network error occurred', 'error');
    }
}

function updatePartnerRow(partnerId, status, statusText, statusClass) {
    const row = document.getElementById(`partner-${partnerId}`);
    if (row) {
        // Update status badge
        const statusCell = row.cells[5];
        statusCell.innerHTML = `<span class="badge badge-${statusClass}">${statusText}</span>`;
        
        // Update actions cell
        const actionsCell = row.cells[7];
        if (status === 'ACCEPTED') {
            const email = row.cells[0].querySelector('small').textContent;
            actionsCell.innerHTML = `
                <button class="btn btn-info btn-sm" onclick="contactCustomer('${email}')">
                    <i class="fas fa-envelope"></i> Contact
                </button>
            `;
        } else {
            actionsCell.innerHTML = `
                <button class="btn btn-outline-secondary btn-sm" disabled>
                    <i class="fas fa-info-circle"></i> ${statusText}
                </button>
            `;
        }
    }
}

function contactCustomer(email) {
    Swal.fire({
        title: 'Contact Customer',
        html: `
            <p>Customer Email: <strong>${email}</strong></p>
            <div class="mt-3">
                <button class="btn btn-primary me-2" onclick="window.open('mailto:${email}', '_blank')">
                    <i class="fas fa-envelope"></i> Send Email
                </button>
                <button class="btn btn-info" onclick="window.location.href='/messages/compose?to=${email}'">
                    <i class="fas fa-comments"></i> Send Message
                </button>
            </div>
        `,
        showConfirmButton: false,
        showCancelButton: true,
        cancelButtonText: 'Close'
    });
}

function exportPartnersData() {
    const table = document.getElementById('partnersTable');
    let csv = 'Customer Name,Customer Email,Cargo Description,Weight (kg),Volume (m³),Cost,Status,Join Date\n';
    
    const rows = table.querySelectorAll('tbody tr');
    rows.forEach(row => {
        const cells = row.querySelectorAll('td');
        const customerName = cells[0].querySelector('strong').textContent;
        const customerEmail = cells[0].querySelector('small').textContent;
        const cargoDesc = cells[1].querySelector('.cargo-desc').textContent;
        const weight = cells[2].textContent;
        const volume = cells[3].textContent;
        const cost = cells[4].querySelector('strong').textContent;
        const status = cells[5].querySelector('.badge').textContent;
        const joinDate = cells[6].textContent;
        
        csv += `"${customerName}","${customerEmail}","${cargoDesc}","${weight}","${volume}","${cost}","${status}","${joinDate}"\n`;
    });
    
    const blob = new Blob([csv], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `container-partners-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
}

function refreshPartners() {
    window.location.reload();
}


// Auto-generated function implementations

function window.open() {
    // Generic function implementation
    console.log('window.open called');
    showAlert('Function window.open executed', 'info');
}

function window.location.href='/messages/compose?to=${email}'() {
    // Generic function implementation
    console.log('window.location.href='/messages/compose?to=${email}' called');
    showAlert('Function window.location.href='/messages/compose?to=${email}' executed', 'info');
}


// Auto-generated button handlers

function confirmAction(button) {
    const action = button.textContent.trim();
    if (confirm(`Are you sure you want to ${action.toLowerCase()}?`)) {
        const itemId = button.dataset.id || button.closest('[data-id]')?.dataset.id;
        fetch(`/api/confirm-action/${itemId}`, {
            method: 'POST',
            headers: {'X-CSRFToken': getCSRFToken()}
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('Action confirmed successfully', 'success');
                location.reload();
            } else {
                showAlert('Confirmation failed', 'error');
            }
        })
        .catch(error => showAlert('Action failed', 'error'));
    }
}
</script>
{% endblock %}