{% extends "base.html" %}

{% block content %}
<div class="dashboard-container">
    <div class="dashboard-header">
        <h1>Documentation & Resources</h1>
        <p>Access platform guides, API documentation, and support resources</p>
    </div>

    <!-- Quick Access Menu -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card text-center h-100">
                <div class="card-body">
                    <i class="fas fa-book fa-3x text-primary mb-3"></i>
                    <h5>User Guides</h5>
                    <p class="text-muted">Step-by-step platform tutorials</p>
                    <button class="btn btn-outline-primary" onclick="showGuides()">Browse Guides</button>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center h-100">
                <div class="card-body">
                    <i class="fas fa-code fa-3x text-success mb-3"></i>
                    <h5>API Documentation</h5>
                    <p class="text-muted">Integration and development docs</p>
                    <button class="btn btn-outline-success" onclick="showApiDocs()">View API Docs</button>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center h-100">
                <div class="card-body">
                    <i class="fas fa-question-circle fa-3x text-warning mb-3"></i>
                    <h5>FAQ</h5>
                    <p class="text-muted">Frequently asked questions</p>
                    <button class="btn btn-outline-warning" onclick="showFAQ()">View FAQ</button>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center h-100">
                <div class="card-body">
                    <i class="fas fa-headset fa-3x text-info mb-3"></i>
                    <h5>Support</h5>
                    <p class="text-muted">Contact technical support</p>
                    <button class="btn btn-outline-info" onclick="getSupport()">Get Support</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Documentation Categories -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>Getting Started</h5>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <a href="javascript:void(0)" class="list-group-item list-group-item-action">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">Platform Overview</h6>
                                <small>5 min read</small>
                            </div>
                            <p class="mb-1">Introduction to Cloverics logistics platform features and capabilities</p>
                        </a>
                        <a href="javascript:void(0)" class="list-group-item list-group-item-action">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">Account Setup</h6>
                                <small>3 min read</small>
                            </div>
                            <p class="mb-1">Complete guide to setting up your logistics provider account</p>
                        </a>
                        <a href="javascript:void(0)" class="list-group-item list-group-item-action">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">First Shipment</h6>
                                <small>8 min read</small>
                            </div>
                            <p class="mb-1">Step-by-step guide to processing your first shipment</p>
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>Advanced Features</h5>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <a href="javascript:void(0)" class="list-group-item list-group-item-action">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">Container Sharing</h6>
                                <small>10 min read</small>
                            </div>
                            <p class="mb-1">Optimize container utilization through partnership networks</p>
                        </a>
                        <a href="javascript:void(0)" class="list-group-item list-group-item-action">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">Private Shipments</h6>
                                <small>7 min read</small>
                            </div>
                            <p class="mb-1">Managing exclusive and premium shipping services</p>
                        </a>
                        <a href="javascript:void(0)" class="list-group-item list-group-item-action">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">API Integration</h6>
                                <small>15 min read</small>
                            </div>
                            <p class="mb-1">Connect your systems with Cloverics APIs</p>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- API Reference -->
    <div class="card mb-4">
        <div class="card-header">
            <h5>API Reference</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-4">
                    <h6>Shipment Management</h6>
                    <ul class="list-unstyled">
                        <li><a href="javascript:void(0)" class="text-decoration-none">POST /api/shipments</a></li>
                        <li><a href="javascript:void(0)" class="text-decoration-none">GET /api/shipments/{id}</a></li>
                        <li><a href="javascript:void(0)" class="text-decoration-none">PUT /api/shipments/{id}</a></li>
                        <li><a href="javascript:void(0)" class="text-decoration-none">DELETE /api/shipments/{id}</a></li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h6>Tracking & Updates</h6>
                    <ul class="list-unstyled">
                        <li><a href="javascript:void(0)" class="text-decoration-none">GET /api/tracking/{number}</a></li>
                        <li><a href="javascript:void(0)" class="text-decoration-none">POST /api/tracking/update</a></li>
                        <li><a href="javascript:void(0)" class="text-decoration-none">GET /api/notifications</a></li>
                        <li><a href="javascript:void(0)" class="text-decoration-none">POST /api/webhooks</a></li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h6>Payment & Billing</h6>
                    <ul class="list-unstyled">
                        <li><a href="javascript:void(0)" class="text-decoration-none">POST /api/payments</a></li>
                        <li><a href="javascript:void(0)" class="text-decoration-none">GET /api/invoices</a></li>
                        <li><a href="javascript:void(0)" class="text-decoration-none">GET /api/commission</a></li>
                        <li><a href="javascript:void(0)" class="text-decoration-none">POST /api/refunds</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Video Tutorials -->
    <div class="card mb-4">
        <div class="card-header">
            <h5>Video Tutorials</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-4">
                    <div class="card">
                        <div class="video-placeholder bg-dark text-white d-flex align-items-center justify-content-center" style="height: 180px;">
                            <i class="fas fa-play-circle fa-3x"></i>
                        </div>
                        <div class="card-body">
                            <h6>Platform Introduction</h6>
                            <p class="card-text">Overview of all platform features and navigation</p>
                            <small class="text-muted">Duration: 12:30</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="video-placeholder bg-dark text-white d-flex align-items-center justify-content-center" style="height: 180px;">
                            <i class="fas fa-play-circle fa-3x"></i>
                        </div>
                        <div class="card-body">
                            <h6>Quote Management</h6>
                            <p class="card-text">Creating and managing shipping quotes efficiently</p>
                            <small class="text-muted">Duration: 8:45</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="video-placeholder bg-dark text-white d-flex align-items-center justify-content-center" style="height: 180px;">
                            <i class="fas fa-play-circle fa-3x"></i>
                        </div>
                        <div class="card-body">
                            <h6>Performance Analytics</h6>
                            <p class="card-text">Understanding metrics and improving performance</p>
                            <small class="text-muted">Duration: 15:20</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Support Contact -->
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>Contact Support</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>Technical Support</strong><br>
                        <i class="fas fa-envelope me-2"></i><EMAIL><br>
                        <i class="fas fa-phone me-2"></i>+1 (555) 123-TECH
                    </div>
                    <div class="mb-3">
                        <strong>Business Hours</strong><br>
                        Monday - Friday: 8:00 AM - 8:00 PM EST<br>
                        Saturday: 9:00 AM - 5:00 PM EST
                    </div>
                    <button class="btn btn-primary" onclick="submitSupportTicket()">Submit Support Ticket</button>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>Platform Status</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <div class="status-indicator bg-success rounded-circle me-3" style="width: 12px; height: 12px;"></div>
                        <span>All Systems Operational</span>
                    </div>
                    <div class="mb-2">
                        <small class="text-muted">API Response Time:</small> <span class="text-success">145ms</span>
                    </div>
                    <div class="mb-2">
                        <small class="text-muted">Database Status:</small> <span class="text-success">Healthy</span>
                    </div>
                    <div class="mb-3">
                        <small class="text-muted">Last Updated:</small> <span>Just now</span>
                    </div>
                    <a href="javascript:void(0)" class="btn btn-outline-info" onclick="viewStatusPage()">View Status Page</a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function showGuides() {
    // Scroll to and highlight the documentation sections
    const guidesSection = document.querySelector('.card-header h5');
    if (guidesSection && guidesSection.textContent === 'Getting Started') {
        guidesSection.closest('.card').scrollIntoView({ behavior: 'smooth', block: 'center' });
        guidesSection.closest('.card').style.border = '2px solid #007bff';
        setTimeout(() => {
            guidesSection.closest('.card').style.border = '';
        }, 3000);
    }
    alert('📚 User Guides Section\n\nBrowse through our comprehensive getting started guides and advanced feature tutorials below to master the Cloverics platform.');
}

function showApiDocs() {
    // Scroll to API Reference section
    const apiSection = document.querySelector('.card-header h5');
    const apiCards = Array.from(document.querySelectorAll('.card-header h5')).find(h => h.textContent === 'API Reference');
    if (apiCards) {
        apiCards.closest('.card').scrollIntoView({ behavior: 'smooth', block: 'center' });
        apiCards.closest('.card').style.border = '2px solid #28a745';
        setTimeout(() => {
            apiCards.closest('.card').style.border = '';
        }, 3000);
    }
    alert('💻 API Documentation\n\nExplore our complete API reference below with endpoints for shipment management, tracking, payments, and webhook integrations. Use these APIs to integrate Cloverics with your existing systems.');
}

function showFAQ() {
    const faqContent = `
🤔 Frequently Asked Questions

Q: How do I create my first shipment?
A: Go to Dashboard → New Shipment and fill in the shipment details. Our system will automatically match you with available logistics providers.

Q: How are shipping rates calculated?
A: Rates are calculated based on distance, weight, cargo type, transport method, and market conditions. View detailed pricing in our pricing calculator.

Q: Can I track shipments in real-time?
A: Yes! Use our tracking system with the tracking number provided. Real-time updates are available for all active shipments.

Q: What payment methods do you accept?
A: We support Stripe payments (cards) and bank transfers. All transactions are secure and encrypted.

Q: How do I contact customer support?
A: Use the "Contact Support" button below <NAME_EMAIL> during business hours.
    `;
    alert(faqContent);
}

function getSupport() {
    // Redirect to contact support page
    window.location.href = '/logistics/contact-support';
}

function submitSupportTicket() {
    // Redirect to contact support page with focus on ticket form
    window.location.href = '/logistics/contact-support?focus=ticket';
}

function viewStatusPage() {
    const statusInfo = `
🟢 Platform Status - All Systems Operational

✅ API Services: Online (145ms response time)
✅ Database: Healthy (99.9% uptime)
✅ Payment Processing: Active
✅ Tracking Services: Operational
✅ Notification System: Running
✅ File Upload System: Available

Last System Check: Just now
Next Maintenance: No scheduled maintenance

For real-time status updates, bookmark this page or follow @ClovericsStatus for announcements.
    `;
    alert(statusInfo);
}
</script>
{% endblock %}