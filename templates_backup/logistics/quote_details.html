{% extends "base.html" %}

{% block title %}Quote Details - {{ quote.quote_reference }} - Cloverics{% endblock %}

{% block content %}
<div class="container mt-4">
    <!-- Success/Error Messages -->
    <script>
        // Check URL parameters for success/error messages
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.get('sent') === 'true') {
            alert('Quote sent successfully to customer!');
        } else if (urlParams.get('updated') === 'true') {
            alert('Quote updated successfully!');
        } else if (urlParams.get('error') === 'send_failed') {
            alert('Failed to send quote. Please try again.');
        } else if (urlParams.get('error') === 'update_failed') {
            alert('Failed to update quote. Please try again.');
        }
    </script>
    
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">Quote Details - {{ quote.quote_reference }}</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5>Customer Information</h5>
                            <p><strong>Customer:</strong> {{ quote.customer.company_name or quote.customer.email }}</p>
                            <p><strong>Contact:</strong> {{ quote.customer.email }}</p>
                        </div>
                        <div class="col-md-6">
                            <h5>Quote Information</h5>
                            <p><strong>Quote Reference:</strong> {{ quote.quote_reference }}</p>
                            <p><strong>Status:</strong> 
                                <span class="badge {% if quote.status == 'pending' %}badge-warning{% elif quote.status == 'quoted' %}badge-info{% elif quote.status == 'sent' %}badge-success{% elif quote.status == 'cancelled' %}badge-danger{% endif %}">
                                    {{ quote.status|title }}
                                </span>
                            </p>
                            <p><strong>Quoted Price:</strong> ${{ quote.quoted_price or 'TBD' }}</p>
                            <p><strong>Created:</strong> {{ quote.created_at.strftime('%Y-%m-%d %H:%M') }}</p>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h5>Cargo Details</h5>
                            <p><strong>Weight:</strong> {{ quote.cargo_weight }} kg</p>
                            <p><strong>Dimensions:</strong> {{ quote.cargo_dimensions }}</p>
                        </div>
                        <div class="col-md-6">
                            <h5>Schedule</h5>
                            <p><strong>Pickup Date:</strong> {{ quote.pickup_date }}</p>
                            <p><strong>Delivery Date:</strong> {{ quote.delivery_date }}</p>
                        </div>
                    </div>
                    
                    {% if quote.special_requirements %}
                    <hr>
                    <h5>Special Requirements</h5>
                    <p>{{ quote.special_requirements }}</p>
                    {% endif %}
                    
                    {% if quote.provider_response %}
                    <hr>
                    <h5>Provider Response</h5>
                    <p>{{ quote.provider_response }}</p>
                    {% endif %}
                    
                    <hr>
                    
                    <div class="text-right">
                        <a href="/logistics/quote-management" class="btn btn-secondary">Back to Quote Management</a>
                        {% if quote.status != 'cancelled' %}
                        <button class="btn btn-primary" onclick="editQuote({{ quote.id }})">Edit Quote</button>
                        {% if quote.status != 'sent' %}
                        <button class="btn btn-success" onclick="sendQuote({{ quote.id }})">Send Quote</button>
                        {% endif %}
                        <button class="btn btn-danger" onclick="cancelQuote({{ quote.id }})">Cancel Quote</button>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function editQuote(quoteId) {
    // Redirect to edit form or show modal
    window.location.href = `/logistics/edit-quote/${quoteId}`;
}

function sendQuote(quoteId) {
    if (confirm('Are you sure you want to send this quote to the customer?')) {
        // Use GET request for better browser compatibility
        window.location.href = `/logistics/send-quote/${quoteId}/action`;
    }
}

function cancelQuote(quoteId) {
    const reason = prompt('Please provide a reason for cancelling this quote:');
    if (reason !== null) {
        const formData = new FormData();
        formData.append('reason', reason);
        
        fetch(`/logistics/cancel-quote/${quoteId}`, {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Quote cancelled successfully!');
                location.reload();
            } else {
                alert('Failed to cancel quote: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while cancelling the quote.');
        });
    }
}
</script>
{% endblock %}