{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1><i class="fas fa-code"></i> Developer Portal & API Tools</h1>
                <div class="btn-group">
                    <button class="btn btn-primary" onclick="generateNewAPIKey()">
                        <i class="fas fa-plus"></i> Generate API Key
                    </button>
                    <button class="btn btn-outline-secondary" onclick="refreshAnalytics()">
                        <i class="fas fa-sync"></i> Refresh
                    </button>
                </div>
            </div>

            <!-- Quick Stats Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>{{ api_keys|length }}</h4>
                                    <p class="mb-0">Active API Keys</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-key fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>{{ usage_analytics.total_requests or 0 }}</h4>
                                    <p class="mb-0">API Requests (30d)</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-chart-line fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>{{ usage_analytics.avg_response_time or 0 }}ms</h4>
                                    <p class="mb-0">Avg Response Time</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-clock fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>{{ usage_analytics.success_rate or 0 }}%</h4>
                                    <p class="mb-0">Success Rate</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-check-circle fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Content Tabs -->
            <ul class="nav nav-tabs" id="developerTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="api-keys-tab" data-bs-toggle="tab" data-bs-target="#api-keys" type="button" role="tab">
                        <i class="fas fa-key"></i> API Keys
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="documentation-tab" data-bs-toggle="tab" data-bs-target="#documentation" type="button" role="tab">
                        <i class="fas fa-book"></i> API Documentation
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="analytics-tab" data-bs-toggle="tab" data-bs-target="#analytics" type="button" role="tab">
                        <i class="fas fa-chart-bar"></i> Usage Analytics
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="testing-tab" data-bs-toggle="tab" data-bs-target="#testing" type="button" role="tab">
                        <i class="fas fa-vial"></i> API Testing
                    </button>
                </li>
            </ul>

            <div class="tab-content" id="developerTabsContent">
                <!-- API Keys Tab -->
                <div class="tab-pane fade show active" id="api-keys" role="tabpanel">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-key"></i> Your API Keys</h5>
                        </div>
                        <div class="card-body">
                            {% if api_keys %}
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Name</th>
                                            <th>Key</th>
                                            <th>Permissions</th>
                                            <th>Status</th>
                                            <th>Created</th>
                                            <th>Last Used</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for key in api_keys %}
                                        <tr>
                                            <td><strong>{{ key.name }}</strong></td>
                                            <td>
                                                <code class="api-key-display">{{ key.key_id[:8] }}...{{ key.key_id[-4:] }}</code>
                                                <button class="btn btn-sm btn-outline-secondary ms-2" onclick="copyAPIKey('{{ key.key_id }}')">
                                                    <i class="fas fa-copy"></i>
                                                </button>
                                            </td>
                                            <td>
                                                {% for permission in key.permissions %}
                                                <span class="badge bg-primary me-1">{{ permission }}</span>
                                                {% endfor %}
                                            </td>
                                            <td>
                                                {% if key.status == 'active' %}
                                                <span class="badge bg-success">Active</span>
                                                {% else %}
                                                <span class="badge bg-danger">{{ key.status|title }}</span>
                                                {% endif %}
                                            </td>
                                            <td>{{ key.created_at }}</td>
                                            <td>{{ key.last_used or 'Never' }}</td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-primary" onclick="editAPIKey('{{ key.key_id }}')">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button class="btn btn-outline-danger" onclick="revokeAPIKey('{{ key.key_id }}')">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            {% else %}
                            <div class="text-center py-5">
                                <i class="fas fa-key fa-3x text-muted mb-3"></i>
                                <h5>No API Keys Yet</h5>
                                <p class="text-muted">Generate your first API key to start integrating with our platform.</p>
                                <button class="btn btn-primary" onclick="generateNewAPIKey()">
                                    <i class="fas fa-plus"></i> Generate First API Key
                                </button>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Documentation Tab -->
                <div class="tab-pane fade" id="documentation" role="tabpanel">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-book"></i> API Documentation</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="list-group">
                                        <a href="#getting-started" class="list-group-item list-group-item-action active" onclick="showDocSection('getting-started')">
                                            <i class="fas fa-play"></i> Getting Started
                                        </a>
                                        <a href="#authentication" class="list-group-item list-group-item-action" onclick="showDocSection('authentication')">
                                            <i class="fas fa-lock"></i> Authentication
                                        </a>
                                        <a href="#endpoints" class="list-group-item list-group-item-action" onclick="showDocSection('endpoints')">
                                            <i class="fas fa-link"></i> API Endpoints
                                        </a>
                                        <a href="#rate-limits" class="list-group-item list-group-item-action" onclick="showDocSection('rate-limits')">
                                            <i class="fas fa-tachometer-alt"></i> Rate Limits
                                        </a>
                                        <a href="#examples" class="list-group-item list-group-item-action" onclick="showDocSection('examples')">
                                            <i class="fas fa-code"></i> Code Examples
                                        </a>
                                    </div>
                                </div>
                                <div class="col-md-8">
                                    <div id="doc-content">
                                        <div id="getting-started" class="doc-section">
                                            <h4>Getting Started</h4>
                                            <p>Welcome to the Cloverics API! Our RESTful API allows you to integrate logistics services into your applications.</p>
                                            <h6>Base URL</h6>
                                            <pre><code>https://api.cloverics.com/v1/</code></pre>
                                            <h6>Quick Start</h6>
                                            <ol>
                                                <li>Generate an API key from the API Keys tab</li>
                                                <li>Include the API key in your request headers</li>
                                                <li>Start making API calls</li>
                                            </ol>
                                        </div>
                                        <div id="authentication" class="doc-section" style="display: none;">
                                            <h4>Authentication</h4>
                                            <p>All API requests must include your API key in the Authorization header:</p>
                                            <pre><code>Authorization: Bearer YOUR_API_KEY</code></pre>
                                            <div class="alert alert-warning">
                                                <i class="fas fa-exclamation-triangle"></i>
                                                Keep your API keys secure and never expose them in client-side code.
                                            </div>
                                        </div>
                                        <div id="endpoints" class="doc-section" style="display: none;">
                                            <h4>Available Endpoints</h4>
                                            <div class="accordion" id="endpointsAccordion">
                                                <div class="accordion-item">
                                                    <h2 class="accordion-header">
                                                        <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#shipping-endpoints" onclick="loadAPISection('shipping')">
                                                            Shipping & Rates
                                                        </button>
                                                    </h2>
                                                    <div id="shipping-endpoints" class="accordion-collapse collapse show">
                                                        <div class="accordion-body">
                                                            <p><code>GET /rates</code> - Get shipping rates</p>
                                                            <p><code>POST /bookings</code> - Create booking</p>
                                                            <p><code>GET /tracking/{id}</code> - Track shipment</p>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="accordion-item">
                                                    <h2 class="accordion-header">
                                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#document-endpoints" onclick="loadAPISection('documents')">
                                                            Documents
                                                        </button>
                                                    </h2>
                                                    <div id="document-endpoints" class="accordion-collapse collapse">
                                                        <div class="accordion-body">
                                                            <p><code>POST /documents</code> - Generate documents</p>
                                                            <p><code>GET /documents/{id}</code> - Download document</p>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div id="rate-limits" class="doc-section" style="display: none;">
                                            <h4>Rate Limits</h4>
                                            <p>API requests are limited to prevent abuse:</p>
                                            <ul>
                                                <li>Standard: 1,000 requests per hour</li>
                                                <li>Premium: 10,000 requests per hour</li>
                                            </ul>
                                            <p>Rate limit headers are included in all responses:</p>
                                            <pre><code>X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1625097600</code></pre>
                                        </div>
                                        <div id="examples" class="doc-section" style="display: none;">
                                            <h4>Code Examples</h4>
                                            <h6>JavaScript/Node.js</h6>
                                            <pre><code>const response = await fetch('https://api.cloverics.com/v1/rates', {
  headers: {
    'Authorization': 'Bearer YOUR_API_KEY',
    'Content-Type': 'application/json'
  }
});
const data = await response.json();</code></pre>
                                            <h6>Python</h6>
                                            <pre><code>import requests

headers = {
    'Authorization': 'Bearer YOUR_API_KEY',
    'Content-Type': 'application/json'
}

response = requests.get('https://api.cloverics.com/v1/rates', headers=headers)
data = response.json()</code></pre>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Analytics Tab -->
                <div class="tab-pane fade" id="analytics" role="tabpanel">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-chart-bar"></i> Usage Analytics</h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <canvas id="requestsChart" width="400" height="200"></canvas>
                                </div>
                                <div class="col-md-6">
                                    <canvas id="responseTimeChart" width="400" height="200"></canvas>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12">
                                    <h6>Recent API Calls</h6>
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>Timestamp</th>
                                                    <th>Endpoint</th>
                                                    <th>Method</th>
                                                    <th>Status</th>
                                                    <th>Response Time</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td>2025-07-03 10:30:15</td>
                                                    <td>/v1/rates</td>
                                                    <td><span class="badge bg-success">GET</span></td>
                                                    <td><span class="badge bg-success">200</span></td>
                                                    <td>245ms</td>
                                                </tr>
                                                <tr>
                                                    <td>2025-07-03 10:28:42</td>
                                                    <td>/v1/bookings</td>
                                                    <td><span class="badge bg-primary">POST</span></td>
                                                    <td><span class="badge bg-success">201</span></td>
                                                    <td>187ms</td>
                                                </tr>
                                                <tr>
                                                    <td>2025-07-03 10:25:33</td>
                                                    <td>/v1/tracking/12345</td>
                                                    <td><span class="badge bg-success">GET</span></td>
                                                    <td><span class="badge bg-success">200</span></td>
                                                    <td>156ms</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Testing Tab -->
                <div class="tab-pane fade" id="testing" role="tabpanel">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-vial"></i> API Testing Console</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>Request Configuration</h6>
                                    <form id="apiTestForm">
                                        <div class="mb-3">
                                            <label class="form-label">Method</label>
                                            <select class="form-select" id="testMethod">
                                                <option value="GET">GET</option>
                                                <option value="POST">POST</option>
                                                <option value="PUT">PUT</option>
                                                <option value="DELETE">DELETE</option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Endpoint</label>
                                            <div class="input-group">
                                                <span class="input-group-text">https://api.cloverics.com/v1/</span>
                                                <input type="text" class="form-control" id="testEndpoint" placeholder="rates">
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Headers</label>
                                            <textarea class="form-control" id="testHeaders" rows="3" placeholder='{"Content-Type": "application/json"}'></textarea>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Request Body</label>
                                            <textarea class="form-control" id="testBody" rows="5" placeholder='{"origin": "New York", "destination": "Los Angeles"}'></textarea>
                                        </div>
                                        <button type="button" class="btn btn-primary" onclick="sendTestRequest()">
                                            <i class="fas fa-play"></i> Send Request
                                        </button>
                                    </form>
                                </div>
                                <div class="col-md-6">
                                    <h6>Response</h6>
                                    <div id="testResponse" class="border rounded p-3" style="min-height: 400px; background-color: #f8f9fa;">
                                        <p class="text-muted">Response will appear here after sending a request.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Generate API Key Modal -->
<div class="modal fade" id="apiKeyModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Generate New API Key</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="generateKeyForm">
                    <div class="mb-3">
                        <label class="form-label">Key Name</label>
                        <input type="text" class="form-control" id="keyName" placeholder="My Integration Key" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Permissions</label>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="readRates" checked>
                            <label class="form-check-label" for="readRates">Read Rates</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="writeBookings">
                            <label class="form-check-label" for="writeBookings">Create Bookings</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="readTracking" checked>
                            <label class="form-check-label" for="readTracking">Read Tracking</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="readDocuments">
                            <label class="form-check-label" for="readDocuments">Read Documents</label>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Expiration (optional)</label>
                        <select class="form-select" id="keyExpiration">
                            <option value="">Never</option>
                            <option value="30">30 days</option>
                            <option value="90">90 days</option>
                            <option value="365">1 year</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="submitGenerateKey()">Generate Key</button>
            </div>
        </div>
    </div>
</div>

<script>
function generateNewAPIKey() {
    new bootstrap.Modal(document.getElementById('apiKeyModal')).show();
}

function submitGenerateKey() {
    const keyName = document.getElementById('keyName').value;
    if (!keyName) {
        alert('Please enter a key name');
        return;
    }
    
    // Mock API key generation
    const newKey = 'clv_' + Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
    
    alert('API Key Generated: ' + newKey + '\n\nPlease copy this key now - you won\'t be able to see it again!');
    
    bootstrap.Modal.getInstance(document.getElementById('apiKeyModal')).hide();
    location.reload();
}

function copyAPIKey(keyId) {
    navigator.clipboard.writeText(keyId).then(() => {
        alert('API key copied to clipboard!');
    });
}

function revokeAPIKey(keyId) {
    if (confirm('Are you sure you want to revoke this API key? This action cannot be undone.')) {
        alert('API key revoked successfully.');
        location.reload();
    }
}

function editAPIKey(keyId) {
    alert('Edit functionality coming soon!');
}

function refreshAnalytics() {
    location.reload();
}

function showDocSection(sectionId) {
    // Hide all sections
    document.querySelectorAll('.doc-section').forEach(section => {
        section.style.display = 'none';
    });
    
    // Show selected section
    document.getElementById(sectionId).style.display = 'block';
    
    // Update active nav item
    document.querySelectorAll('.list-group-item').forEach(item => {
        item.classList.remove('active');
    });
    event.target.classList.add('active');
}

function sendTestRequest() {
    const method = document.getElementById('testMethod').value;
    const endpoint = document.getElementById('testEndpoint').value;
    const headers = document.getElementById('testHeaders').value;
    const body = document.getElementById('testBody').value;
    
    // Mock response
    const mockResponse = {
        status: 200,
        headers: {
            'Content-Type': 'application/json',
            'X-RateLimit-Remaining': '999'
        },
        body: {
            success: true,
            data: {
                message: 'This is a mock response for testing purposes',
                timestamp: new Date().toISOString()
            }
        }
    };
    
    document.getElementById('testResponse').innerHTML = '<pre>' + JSON.stringify(mockResponse, null, 2) + '</pre>';
}


// Auto-generated button handlers

function loadAPISection(section) {
    // Load API documentation section
    const sections = document.querySelectorAll('.api-section');
    sections.forEach(s => s.style.display = 'none');
    
    const targetSection = document.getElementById(`${section}-section`);
    if (targetSection) {
        targetSection.style.display = 'block';
    }
    
    // Update active tab
    const tabs = document.querySelectorAll('.api-tab');
    tabs.forEach(t => t.classList.remove('active'));
    event.target.classList.add('active');
}
</script>

<style>
.api-key-display {
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
}

.doc-section {
    min-height: 400px;
}

.doc-section h4 {
    color: #0d6efd;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 10px;
    margin-bottom: 20px;
}

.doc-section pre {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 15px;
    font-size: 0.9em;
}
</style>
{% endblock %}