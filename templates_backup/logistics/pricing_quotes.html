{% extends "base.html" %}

{% block title %}Quote & Rate Management - Cloverics{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Quote & Rate Management</h1>
        <div class="btn-group">
            <button class="btn btn-primary" onclick="createNewRate()">
                <i class="fas fa-plus"></i> Add New Rate
            </button>
            <button class="btn btn-success" onclick="sendBulkQuotes()">
                <i class="fas fa-paper-plane"></i> Send Quotes
            </button>
        </div>
    </div>

    <!-- Unified Tabs -->
    <ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a class="nav-link active" data-bs-toggle="tab" href="#quotes-section" role="tab">
                <i class="fas fa-quote-left"></i> Quote Requests ({{ quote_requests|length }})
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link" data-bs-toggle="tab" href="#rates-section" role="tab">
                <i class="fas fa-dollar-sign"></i> Rate Management ({{ shipping_rates|length }})
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link" data-bs-toggle="tab" href="#analytics-section" role="tab">
                <i class="fas fa-chart-line"></i> Pricing Analytics
            </a>
        </li>
    </ul>

    <div class="tab-content mt-4">
        <!-- Quote Requests Section -->
        <div class="tab-pane fade show active" id="quotes-section" role="tabpanel">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-quote-left"></i> Quote Requests Management</h5>
                </div>
                <div class="card-body">
                    {% if quote_requests %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Customer</th>
                                    <th>Route</th>
                                    <th>Cargo Details</th>
                                    <th>Status</th>
                                    <th>Price</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for quote in quote_requests %}
                                <tr>
                                    <td>
                                        <strong>{{ quote.customer_name }}</strong><br>
                                        <small class="text-muted">{{ quote.customer_email }}</small>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ quote.origin_country }}</span>
                                        <i class="fas fa-arrow-right mx-1"></i>
                                        <span class="badge bg-info">{{ quote.destination_country }}</span>
                                    </td>
                                    <td>
                                        {{ quote.cargo_weight }}kg<br>
                                        <small class="text-muted">{{ quote.cargo_type }}</small>
                                    </td>
                                    <td>
                                        <span class="badge {% if quote.status == 'pending' %}bg-warning{% elif quote.status == 'sent' %}bg-success{% else %}bg-secondary{% endif %}">
                                            {{ quote.status|title }}
                                        </span>
                                    </td>
                                    <td>
                                        {% if quote.quoted_price %}
                                            <strong>${{ "%.2f"|format(quote.quoted_price) }}</strong>
                                        {% else %}
                                            <span class="text-muted">TBD</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary" onclick="editQuote({{ quote.id }})">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-outline-success" onclick="sendQuote({{ quote.id }})">
                                                <i class="fas fa-paper-plane"></i>
                                            </button>
                                            <button class="btn btn-outline-info" onclick="viewQuote({{ quote.id }})">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-quote-left fa-3x text-muted mb-3"></i>
                        <h5>No Quote Requests</h5>
                        <p class="text-muted">Quote requests from customers will appear here.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Rate Management Section -->
        <div class="tab-pane fade" id="rates-section" role="tabpanel">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-dollar-sign"></i> Shipping Rate Management</h5>
                </div>
                <div class="card-body">
                    {% if shipping_rates %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Route</th>
                                    <th>Transport Type</th>
                                    <th>Base Price</th>
                                    <th>Price per KG</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for rate in shipping_rates %}
                                <tr>
                                    <td>
                                        <strong>{{ rate.route_summary }}</strong><br>
                                        <small class="text-muted">{{ rate.estimated_days }} days</small>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">{{ rate.transport_type|title }}</span>
                                    </td>
                                    <td>
                                        <strong>${{ "%.2f"|format(rate.base_price) }}</strong>
                                    </td>
                                    <td>
                                        ${{ "%.2f"|format(rate.price_per_kg) }}
                                    </td>
                                    <td>
                                        <span class="badge {% if rate.is_active %}bg-success{% else %}bg-danger{% endif %}">
                                            {% if rate.is_active %}Active{% else %}Inactive{% endif %}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary" onclick="editRate({{ rate.id }})">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-outline-{% if rate.is_active %}danger{% else %}success{% endif %}" 
                                                    onclick="toggleRate({{ rate.id }})">
                                                <i class="fas fa-{% if rate.is_active %}pause{% else %}play{% endif %}"></i>
                                            </button>
                                            <button class="btn btn-outline-info" onclick="duplicateRate({{ rate.id }})">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-dollar-sign fa-3x text-muted mb-3"></i>
                        <h5>No Shipping Rates</h5>
                        <p class="text-muted">Create your first shipping rate to start receiving quotes.</p>
                        <button class="btn btn-primary" onclick="createNewRate()">
                            <i class="fas fa-plus"></i> Create First Rate
                        </button>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Pricing Analytics Section -->
        <div class="tab-pane fade" id="analytics-section" role="tabpanel">
            <div class="row">
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-header">
                            <h6><i class="fas fa-chart-bar"></i> Quote Conversion Rate</h6>
                        </div>
                        <div class="card-body text-center">
                            <h3 class="text-success">{{ pricing_analytics.conversion_rate }}%</h3>
                            <p class="text-muted">Quotes converted to shipments</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-header">
                            <h6><i class="fas fa-dollar-sign"></i> Average Rate per KG</h6>
                        </div>
                        <div class="card-body text-center">
                            <h3 class="text-primary">${{ "%.2f"|format(pricing_analytics.avg_rate_per_kg) }}</h3>
                            <p class="text-muted">Market competitive rate</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-header">
                            <h6><i class="fas fa-route"></i> Top Routes</h6>
                        </div>
                        <div class="card-body text-center">
                            <h3 class="text-info">{{ pricing_analytics.top_routes_count }}</h3>
                            <p class="text-muted">Most requested routes</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-header">
                            <h6><i class="fas fa-check-circle"></i> Acceptance Rate</h6>
                        </div>
                        <div class="card-body text-center">
                            <h3 class="text-warning">{{ pricing_analytics.acceptance_rate }}%</h3>
                            <p class="text-muted">Quotes accepted by customers</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Enhanced Analytics Charts -->
            <div class="row mt-4">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h6><i class="fas fa-chart-line"></i> Top Requested Routes</h6>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Route</th>
                                            <th>Requests</th>
                                            <th>Avg Rate/KG</th>
                                            <th>Success Rate</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for route_stat in pricing_analytics.top_routes %}
                                        <tr>
                                            <td><strong>{{ route_stat.route }}</strong></td>
                                            <td><span class="badge bg-primary">{{ route_stat.requests }}</span></td>
                                            <td>${{ "%.2f"|format(route_stat.avg_rate) }}</td>
                                            <td>
                                                <div class="progress" style="height: 15px;">
                                                    <div class="progress-bar bg-success" style="width: {{ route_stat.success_rate }}%">
                                                        {{ route_stat.success_rate }}%
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h6><i class="fas fa-box"></i> Container Size Analytics</h6>
                        </div>
                        <div class="card-body">
                            {% for container_stat in pricing_analytics.container_analytics %}
                            <div class="mb-3">
                                <div class="d-flex justify-content-between">
                                    <span>{{ container_stat.size }}</span>
                                    <strong>${{ "%.2f"|format(container_stat.avg_rate) }}</strong>
                                </div>
                                <div class="progress mt-1" style="height: 8px;">
                                    <div class="progress-bar bg-info" style="width: {{ container_stat.usage_percent }}%"></div>
                                </div>
                                <small class="text-muted">{{ container_stat.usage_percent }}% of quotes</small>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mt-4">
                <div class="card-header">
                    <h6><i class="fas fa-chart-line"></i> Pricing Recommendations</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for recommendation in pricing_analytics.recommendations %}
                        <div class="col-md-4 mb-3">
                            <div class="alert alert-info">
                                <h6>{{ recommendation.route }}</h6>
                                <p class="mb-1">{{ recommendation.suggestion }}</p>
                                <small class="text-muted">{{ recommendation.reason }}</small>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function createNewRate() {
    Swal.fire({
        title: 'Create New Rate',
        html: `
            <div class="text-left">
                <div class="mb-3">
                    <label class="form-label">Route</label>
                    <input type="text" class="form-control" id="newRateRoute" placeholder="Origin → Destination">
                </div>
                <div class="mb-3">
                    <label class="form-label">Transport Type</label>
                    <select class="form-control" id="newRateTransport">
                        <option value="truck">Truck</option>
                        <option value="ship">Ship</option>
                        <option value="air">Air</option>
                        <option value="rail">Rail</option>
                    </select>
                </div>
                <div class="mb-3">
                    <label class="form-label">Base Price ($)</label>
                    <input type="number" class="form-control" id="newRateBase" placeholder="500.00">
                </div>
                <div class="mb-3">
                    <label class="form-label">Price per KG ($)</label>
                    <input type="number" step="0.01" class="form-control" id="newRatePerKg" placeholder="2.50">
                </div>
            </div>
        `,
        showCancelButton: true,
        confirmButtonText: 'Create Rate',
        preConfirm: () => {
            return {
                route: document.getElementById('newRateRoute').value,
                transport: document.getElementById('newRateTransport').value,
                base_price: document.getElementById('newRateBase').value,
                price_per_kg: document.getElementById('newRatePerKg').value
            }
        }
    }).then((result) => {
        if (result.isConfirmed) {
            // Submit to API
            fetch('/api/logistics/rates/create', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify(result.value)
            }).then(() => {
                location.reload();
            });
        }
    });
}

function editQuote(quoteId) {
    window.location.href = `/logistics/quote/${quoteId}/edit`;
}

function sendQuote(quoteId) {
    fetch(`/api/logistics/quote/${quoteId}/send`, {method: 'POST'})
        .then(() => location.reload());
}

function viewQuote(quoteId) {
    window.location.href = `/logistics/quote/${quoteId}`;
}

function editRate(rateId) {
    window.location.href = `/logistics/rate/${rateId}/edit`;
}

function toggleRate(rateId) {
    fetch(`/api/logistics/rate/${rateId}/toggle`, {method: 'POST'})
        .then(() => location.reload());
}

function duplicateRate(rateId) {
    fetch(`/api/logistics/rate/${rateId}/duplicate`, {method: 'POST'})
        .then(() => location.reload());
}

function sendBulkQuotes() {
    Swal.fire({
        title: 'Send All Pending Quotes?',
        text: 'This will send quotes to all customers with pending requests.',
        icon: 'question',
        showCancelButton: true,
        confirmButtonText: 'Send All'
    }).then((result) => {
        if (result.isConfirmed) {
            fetch('/api/logistics/quotes/send-bulk', {method: 'POST'})
                .then(() => {
                    Swal.fire('Success!', 'All quotes have been sent.', 'success');
                    location.reload();
                });
        }
    });
}
</script>
{% endblock %}