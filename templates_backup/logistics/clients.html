{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <h1><i class="fas fa-users"></i> My Clients</h1>
                <p class="text-muted">Manage your customer relationships and business partnerships</p>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5><i class="fas fa-handshake"></i> Client Portfolio</h5>
                    <div class="btn-group">
                        <button class="btn btn-primary btn-sm" onclick="showAddClientModal()" id="addClientBtn">
                            <i class="fas fa-user-plus"></i> Add Client
                        </button>
                        <button class="btn btn-outline-primary btn-sm" onclick="toggleSearch()" id="searchToggleBtn">
                            <i class="fas fa-search"></i> Search
                        </button>
                        <button class="btn btn-outline-primary btn-sm" onclick="exportClients()">
                            <i class="fas fa-download"></i> Export
                        </button>
                    </div>
                </div>
                
                <!-- Search Section (Initially Hidden) -->
                <div id="searchSection" class="card-body border-bottom" style="display: none;">
                    <div class="row">
                        <div class="col-md-4">
                            <input type="text" class="form-control form-control-sm" id="searchInput" placeholder="Search by company name, contact, or email...">
                        </div>
                        <div class="col-md-3">
                            <select class="form-control form-control-sm" id="statusFilter">
                                <option value="">All Status</option>
                                <option value="Active">Active</option>
                                <option value="Inactive">Inactive</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <input type="date" class="form-control form-control-sm" id="dateFilter" placeholder="Last shipment date">
                        </div>
                        <div class="col-md-2">
                            <button class="btn btn-sm btn-primary" onclick="performSearch()">
                                <i class="fas fa-search"></i> Filter
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="card-body">
                    {% if clients %}
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Company</th>
                                        <th>Contact Person</th>
                                        <th>Contact Details</th>
                                        <th>Business Metrics</th>
                                        <th>Last Activity</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for client in clients %}
                                    <tr>
                                        <td>
                                            <strong>{{ client.company_name }}</strong>
                                        </td>
                                        <td>
                                            <strong>{{ client.contact_name }}</strong>
                                        </td>
                                        <td>
                                            <small class="text-muted">
                                                <i class="fas fa-envelope"></i> {{ client.email }}<br>
                                                <i class="fas fa-phone"></i> {{ client.phone }}
                                            </small>
                                        </td>
                                        <td>
                                            <span class="badge badge-info">{{ client.total_shipments }} shipments</span><br>
                                            <strong class="text-success">{{ client.total_revenue }}</strong>
                                        </td>
                                        <td>{{ client.last_shipment }}</td>
                                        <td>
                                            {% if client.status == 'Active' %}
                                                <span class="badge badge-success">Active</span>
                                            {% else %}
                                                <span class="badge badge-secondary">{{ client.status }}</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary" title="View Details" 
                                                        onclick="viewClientDetails('{{ client.user_id }}', '{{ client.company_name }}', '{{ client.contact_name }}', '{{ client.email }}', '{{ client.phone }}', '{{ client.total_shipments }}', '{{ client.total_revenue }}')">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button class="btn btn-outline-success" title="Send Message" 
                                                        onclick="sendClientMessage('{{ client.user_id }}', '{{ client.contact_name }}', '{{ client.company_name }}')">
                                                    <i class="fas fa-envelope"></i>
                                                </button>
                                                <button class="btn btn-outline-info" title="Call" 
                                                        onclick="callClient('{{ client.phone }}', '{{ client.contact_name }}')">
                                                    <i class="fas fa-phone"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="empty-state text-center py-5">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <h5>No clients yet</h5>
                            <p class="text-muted">Start building relationships by accepting shipping quotes</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 class="text-primary">{{ clients|length }}</h3>
                    <p class="card-text">Total Clients</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 class="text-success">
                        {% set active_clients = clients|selectattr('status', 'equalto', 'Active')|list|length %}
                        {{ active_clients }}
                    </h3>
                    <p class="card-text">Active Clients</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 class="text-info">
                        {% set total_shipments = clients|sum(attribute='total_shipments') %}
                        {{ total_shipments }}
                    </h3>
                    <p class="card-text">Total Shipments</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 class="text-warning">$78,000</h3>
                    <p class="card-text">Total Revenue</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Client Details Modal -->
<div class="modal fade" id="clientDetailsModal" tabindex="-1" role="dialog" aria-labelledby="clientDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="clientDetailsModalLabel">Client Details</h5>
                <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close" onclick="closeModal('clientDetailsModal')">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="clientDetailsBody">
                <!-- Content will be populated by JavaScript -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" onclick="closeModal('clientDetailsModal')">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Send Message Modal -->
<div class="modal fade" id="sendMessageModal" tabindex="-1" role="dialog" aria-labelledby="sendMessageModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="sendMessageModalLabel">Send Message</h5>
                <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close" onclick="closeModal('sendMessageModal')">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="messageForm">
                    <input type="hidden" id="messageClientId" name="client_id">
                    <div class="form-group">
                        <label for="messageSubject">Subject</label>
                        <input type="text" class="form-control" id="messageSubject" name="subject" required>
                    </div>
                    <div class="form-group">
                        <label for="messageContent">Message</label>
                        <textarea class="form-control" id="messageContent" name="content" rows="4" required></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" onclick="closeModal('sendMessageModal')">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="sendMessage()">Send Message</button>
            </div>
        </div>
    </div>
</div>

<script>
// View Client Details Function
function viewClientDetails(userId, companyName, contactName, email, phone, totalShipments, totalRevenue) {
    const detailsHTML = `
        <div class="row">
            <div class="col-md-6">
                <h6><i class="fas fa-building"></i> Company Information</h6>
                <ul class="list-unstyled">
                    <li><strong>Company:</strong> ${companyName}</li>
                    <li><strong>Contact Person:</strong> ${contactName}</li>
                    <li><strong>Email:</strong> ${email}</li>
                    <li><strong>Phone:</strong> ${phone}</li>
                </ul>
            </div>
            <div class="col-md-6">
                <h6><i class="fas fa-chart-bar"></i> Business Metrics</h6>
                <ul class="list-unstyled">
                    <li><strong>Total Shipments:</strong> ${totalShipments}</li>
                    <li><strong>Total Revenue:</strong> ${totalRevenue}</li>
                    <li><strong>Client Status:</strong> <span class="badge badge-success">Active</span></li>
                    <li><strong>Partnership Since:</strong> Jan 2024</li>
                </ul>
            </div>
        </div>
        <hr>
        <div class="row">
            <div class="col-12">
                <h6><i class="fas fa-history"></i> Recent Activity</h6>
                <div class="alert alert-info">
                    <small>Latest shipment activity and communication history would be displayed here.</small>
                </div>
            </div>
        </div>
    `;
    
    document.getElementById('clientDetailsBody').innerHTML = detailsHTML;
    document.getElementById('clientDetailsModalLabel').innerHTML = `Client Details - ${companyName}`;
    $('#clientDetailsModal').modal('show');
}

// Send Message Function
function sendClientMessage(userId, contactName, companyName) {
    // Set the client ID in the hidden field
    document.getElementById('messageClientId').value = userId;
    document.getElementById('sendMessageModalLabel').innerHTML = `Send Message to ${contactName} (${companyName})`;
    document.getElementById('messageSubject').value = '';
    document.getElementById('messageContent').value = '';
    
    // Show modal using Bootstrap 5 method
    const modal = new bootstrap.Modal(document.getElementById('sendMessageModal'));
    modal.show();
}

// Call Client Function
function callClient(phone, contactName) {
    if (phone && phone !== 'N/A') {
        const confirmed = confirm(`Call ${contactName} at ${phone}?\n\nThis will open your default phone application or dialer.`);
        if (confirmed) {
            // Try to initiate call - this works on mobile devices
            window.location.href = `tel:${phone}`;
            
            // Show success message
            alert(`Initiating call to ${contactName} at ${phone}\n\nIf the call doesn't start automatically, please dial ${phone} manually.`);
        }
    } else {
        alert('Phone number not available for this client.');
    }
}

// Send Message Implementation - Database Connected
async function sendMessage() {
    const form = document.getElementById('messageForm');
    const formData = new FormData(form);
    
    // Basic validation
    if (!formData.get('subject') || !formData.get('content')) {
        alert('Please fill in both subject and message content.');
        return;
    }
    
    // Show loading state
    const sendButton = event.target;
    const originalText = sendButton.innerHTML;
    sendButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Sending...';
    sendButton.disabled = true;
    
    try {
        // Send message to database via FastAPI endpoint
        const response = await fetch('/logistics/send-client-message', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        if (result.success) {
            alert(result.message || 'Message sent successfully!');
            
            // Close modal using our closeModal function
            closeModal('sendMessageModal');
            
            // Clear form
            form.reset();
        } else {
            alert('Error: ' + (result.error || 'Failed to send message'));
        }
    } catch (error) {
        console.error('Error sending message:', error);
        alert('Network error occurred while sending message');
    } finally {
        // Reset button
        sendButton.innerHTML = originalText;
        sendButton.disabled = false;
    }
}

// Close Modal Function
function closeModal(modalId) {
    try {
        // Try Bootstrap 5 method first
        const modal = document.getElementById(modalId);
        if (modal) {
            const bootstrapModal = bootstrap.Modal.getInstance(modal);
            if (bootstrapModal) {
                bootstrapModal.hide();
            } else {
                // Fallback - manually hide modal
                modal.style.display = 'none';
                modal.classList.remove('show');
                modal.setAttribute('aria-hidden', 'true');
                
                // Remove backdrop
                const backdrop = document.querySelector('.modal-backdrop');
                if (backdrop) {
                    backdrop.remove();
                }
                
                // Remove modal-open class from body
                document.body.classList.remove('modal-open');
                document.body.style.overflow = '';
                document.body.style.paddingRight = '';
            }
        }
    } catch (error) {
        console.log('Modal close method not available, using fallback');
        // Fallback method
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.style.display = 'none';
            modal.classList.remove('show');
            modal.setAttribute('aria-hidden', 'true');
        }
    }
}

// Search Toggle Function
function toggleSearch() {
    const searchSection = document.getElementById('searchSection');
    const toggleBtn = document.getElementById('searchToggleBtn');
    
    if (searchSection.style.display === 'none') {
        searchSection.style.display = 'block';
        toggleBtn.innerHTML = '<i class="fas fa-times"></i> Hide Search';
        toggleBtn.classList.remove('btn-outline-primary');
        toggleBtn.classList.add('btn-primary');
    } else {
        searchSection.style.display = 'none';
        toggleBtn.innerHTML = '<i class="fas fa-search"></i> Search';
        toggleBtn.classList.remove('btn-primary');
        toggleBtn.classList.add('btn-outline-primary');
        
        // Clear search when hiding
        clearSearch();
    }
}

// Perform Search Function
function performSearch() {
    const searchInput = document.getElementById('searchInput').value.toLowerCase();
    const statusFilter = document.getElementById('statusFilter').value;
    const dateFilter = document.getElementById('dateFilter').value;
    
    const table = document.querySelector('table tbody');
    const rows = table.querySelectorAll('tr');
    
    rows.forEach(row => {
        const companyName = row.cells[0].textContent.toLowerCase();
        const contactName = row.cells[1].textContent.toLowerCase();
        const email = row.cells[1].textContent.toLowerCase();
        const status = row.cells[4].textContent.trim();
        const lastShipment = row.cells[3].textContent;
        
        let showRow = true;
        
        // Text search
        if (searchInput && !companyName.includes(searchInput) && 
            !contactName.includes(searchInput) && !email.includes(searchInput)) {
            showRow = false;
        }
        
        // Status filter
        if (statusFilter && !status.includes(statusFilter)) {
            showRow = false;
        }
        
        // Date filter
        if (dateFilter && lastShipment !== dateFilter) {
            showRow = false;
        }
        
        row.style.display = showRow ? '' : 'none';
    });
    
    // Show results count
    const visibleRows = Array.from(rows).filter(row => row.style.display !== 'none').length;
    console.log(`Search results: ${visibleRows} clients found`);
}

// Clear Search Function
function clearSearch() {
    document.getElementById('searchInput').value = '';
    document.getElementById('statusFilter').value = '';
    document.getElementById('dateFilter').value = '';
    
    // Show all rows
    const table = document.querySelector('table tbody');
    const rows = table.querySelectorAll('tr');
    rows.forEach(row => {
        row.style.display = '';
    });
}

// Export Clients Function
function exportClients() {
    const button = event.target;
    const originalText = button.innerHTML;
    
    // Show loading state
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Exporting...';
    button.disabled = true;
    
    try {
        // Get table data
        const table = document.querySelector('table');
        const rows = Array.from(table.querySelectorAll('tbody tr')).filter(row => row.style.display !== 'none');
        
        // Prepare CSV data
        const csvData = [];
        csvData.push(['Company Name', 'Contact Name', 'Email', 'Phone', 'Total Shipments', 'Total Revenue', 'Last Shipment', 'Status']);
        
        rows.forEach(row => {
            const cells = row.querySelectorAll('td');
            if (cells.length >= 6) {
                csvData.push([
                    cells[0].textContent.trim(),
                    cells[1].querySelector('strong').textContent.trim(),
                    cells[1].querySelector('i.fa-envelope').nextSibling.textContent.trim(),
                    cells[1].querySelector('i.fa-phone').nextSibling.textContent.trim(),
                    cells[2].querySelector('.badge').textContent.replace(' shipments', '').trim(),
                    cells[2].querySelector('strong').textContent.trim(),
                    cells[3].textContent.trim(),
                    cells[4].querySelector('.badge').textContent.trim()
                ]);
            }
        });
        
        // Convert to CSV
        const csvContent = csvData.map(row => 
            row.map(field => `"${field.replace(/"/g, '""')}"`).join(',')
        ).join('\n');
        
        // Create and download file
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        
        const today = new Date().toISOString().split('T')[0];
        link.download = `cloverics_clients_${today}.csv`;
        
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        alert('Client data exported successfully!');
        
    } catch (error) {
        console.error('Export error:', error);
        alert('Error exporting data. Please try again.');
    } finally {
        // Reset button
        button.innerHTML = originalText;
        button.disabled = false;
    }
}

// Add Enter key support for search input
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                performSearch();
            }
        });
    }
});

// Add Client Modal Functions
function showAddClientModal() {
    document.getElementById('addClientModal').style.display = 'block';
    document.getElementById('addClientEmail').value = '';
    document.getElementById('generateReferralCode').checked = true;
}

function closeAddClientModal() {
    document.getElementById('addClientModal').style.display = 'none';
}

async function submitAddClient() {
    const email = document.getElementById('addClientEmail').value.trim();
    const generateCode = document.getElementById('generateReferralCode').checked;
    
    if (!email) {
        alert('Please enter a customer email address');
        return;
    }
    
    if (!email.includes('@')) {
        alert('Please enter a valid email address');
        return;
    }
    
    const submitBtn = document.getElementById('submitAddClientBtn');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Adding...';
    submitBtn.disabled = true;
    
    try {
        const formData = new FormData();
        formData.append('client_email', email);
        
        const response = await fetch('/api/logistics/add-client', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        if (result.status === 'success') {
            alert(`Client added successfully!\nEmail: ${email}\nReferral Code: ${result.referral_code}`);
            closeAddClientModal();
            window.location.reload(); // Refresh to show new client
        } else {
            alert(`Error: ${result.message}`);
        }
    } catch (error) {
        console.error('Add client error:', error);
        alert('Error adding client. Please try again.');
    } finally {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }
}

// Bootstrap Modal Support (if not already loaded)
if (typeof $ === 'undefined') {
    // Fallback for modal functionality without jQuery
    document.addEventListener('DOMContentLoaded', function() {
        // Add basic modal functionality if Bootstrap JS is not available
        console.log('Client management buttons are ready');
    });
}
</script>

<!-- Add Client Modal -->
<div id="addClientModal" class="modal" style="display: none; position: fixed; z-index: 1050; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5);">
    <div class="modal-dialog" style="position: relative; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 500px;">
        <div class="modal-content bg-white" style="border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);">
            <div class="modal-header bg-primary text-white" style="padding: 15px; border-radius: 8px 8px 0 0;">
                <h5 class="modal-title"><i class="fas fa-user-plus"></i> Add New Client</h5>
                <button type="button" class="btn btn-link text-white p-0" onclick="closeAddClientModal()" style="font-size: 24px; line-height: 1;">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body" style="padding: 20px;">
                <form id="addClientForm">
                    <div class="form-group mb-3">
                        <label for="addClientEmail" class="form-label">Customer Email Address</label>
                        <input type="email" class="form-control" id="addClientEmail" placeholder="<EMAIL>" required>
                        <small class="form-text text-muted">Enter the email address of an existing customer account</small>
                    </div>
                    
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="generateReferralCode" checked>
                        <label class="form-check-label" for="generateReferralCode">
                            Generate automatic referral code
                        </label>
                        <small class="form-text text-muted d-block">This will create a unique referral code for tracking business from this client</small>
                    </div>
                </form>
            </div>
            <div class="modal-footer" style="padding: 15px; border-top: 1px solid #dee2e6;">
                <button type="button" class="btn btn-secondary" onclick="closeAddClientModal()">Cancel</button>
                <button type="button" class="btn btn-primary" id="submitAddClientBtn" onclick="submitAddClient()">
                    <i class="fas fa-user-plus"></i> Add Client
                </button>
            </div>
        </div>
    </div>
</div>

{% endblock %}