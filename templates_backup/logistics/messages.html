{% extends "base.html" %}

{% block title %}Messages - Cloverics{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-comments text-primary"></i> Messages & Communications</h2>
                <button class="btn btn-primary" onclick="composeMessage()">
                    <i class="fas fa-plus"></i> New Message
                </button>
            </div>

            <!-- Message Statistics -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>{{ messages.total_count }}</h4>
                                    <p class="mb-0">Total Messages</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-envelope fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>{{ messages.unread_count }}</h4>
                                    <p class="mb-0">Unread</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-bell fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>{{ messages.customer_messages }}</h4>
                                    <p class="mb-0">From Customers</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-user fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>{{ messages.urgent_count }}</h4>
                                    <p class="mb-0">Urgent</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-exclamation fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Message Management Tabs -->
            <ul class="nav nav-tabs mb-4" id="messageTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="inbox-tab" data-bs-toggle="tab" data-bs-target="#inbox" type="button" role="tab">
                        <i class="fas fa-inbox"></i> Inbox
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="customers-tab" data-bs-toggle="tab" data-bs-target="#customers" type="button" role="tab">
                        <i class="fas fa-users"></i> Customer Messages
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="sent-tab" data-bs-toggle="tab" data-bs-target="#sent" type="button" role="tab">
                        <i class="fas fa-paper-plane"></i> Sent
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="compose-tab" data-bs-toggle="tab" data-bs-target="#compose" type="button" role="tab">
                        <i class="fas fa-edit"></i> Compose
                    </button>
                </li>
            </ul>

            <!-- Tab Content -->
            <div class="tab-content" id="messageTabContent">
                <!-- Inbox Tab -->
                <div class="tab-pane fade show active" id="inbox" role="tabpanel">
                    <div class="card">
                        <div class="card-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">All Messages</h5>
                                <div class="btn-group" role="group">
                                    <button class="btn btn-sm btn-outline-primary" onclick="markAllRead()">
                                        <i class="fas fa-check"></i> Mark All Read
                                    </button>
                                    <button class="btn btn-sm btn-outline-secondary" onclick="refreshMessages()">
                                        <i class="fas fa-sync"></i> Refresh
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            {% if messages.inbox %}
                                {% for message in messages.inbox %}
                                <div class="message-item border-bottom py-3 {% if not message.is_read %}bg-light{% endif %}">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div class="flex-grow-1">
                                            <div class="d-flex align-items-center mb-2">
                                                <strong class="me-2">{{ message.sender_name }}</strong>
                                                <span class="badge bg-{% if message.sender_type == 'CUSTOMER' %}primary{% else %}secondary{% endif %} me-2">
                                                    {{ message.sender_type }}
                                                </span>
                                                {% if message.priority == 'urgent' %}
                                                <span class="badge bg-danger me-2">URGENT</span>
                                                {% endif %}
                                                <small class="text-muted">{{ message.created_at.strftime('%b %d, %Y %H:%M') }}</small>
                                            </div>
                                            <h6 class="mb-2">{{ message.subject }}</h6>
                                            <p class="mb-2 text-muted">{{ message.content[:150] }}{% if message.content|length > 150 %}...{% endif %}</p>
                                            <div class="d-flex gap-2">
                                                <button class="btn btn-sm btn-primary" onclick="viewMessage({{ message.id }})">
                                                    <i class="fas fa-eye"></i> View
                                                </button>
                                                <button class="btn btn-sm btn-outline-primary" onclick="replyMessage({{ message.id }})">
                                                    <i class="fas fa-reply"></i> Reply
                                                </button>
                                                <button class="btn btn-sm btn-outline-success" onclick="markImportant({{ message.id }})">
                                                    <i class="fas fa-star"></i> Important
                                                </button>
                                            </div>
                                        </div>
                                        {% if not message.is_read %}
                                        <span class="badge bg-success">New</span>
                                        {% endif %}
                                    </div>
                                </div>
                                {% endfor %}
                            {% else %}
                                <div class="text-center py-5">
                                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">No messages</h5>
                                    <p class="text-muted">Customer messages will appear here</p>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Customer Messages Tab -->
                <div class="tab-pane fade" id="customers" role="tabpanel">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Customer Communications</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Customer</th>
                                            <th>Subject</th>
                                            <th>Date</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% if messages.customer_list %}
                                            {% for msg in messages.customer_list %}
                                            <tr class="{% if not msg.is_read %}table-light{% endif %}">
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <img src="https://ui-avatars.com/api/?name={{ msg.sender_name }}&size=32" 
                                                             class="rounded-circle me-2" width="32" height="32">
                                                        <div>
                                                            <strong>{{ msg.sender_name }}</strong><br>
                                                            <small class="text-muted">{{ msg.sender_email }}</small>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <strong>{{ msg.subject }}</strong><br>
                                                    <small class="text-muted">{{ msg.content[:50] }}{% if msg.content|length > 50 %}...{% endif %}</small>
                                                </td>
                                                <td>{{ msg.created_at.strftime('%b %d, %H:%M') }}</td>
                                                <td>
                                                    <span class="badge bg-{% if msg.is_read %}success{% else %}warning{% endif %}">
                                                        {% if msg.is_read %}Read{% else %}Unread{% endif %}
                                                    </span>
                                                </td>
                                                <td>
                                                    <div class="btn-group" role="group">
                                                        <button class="btn btn-sm btn-outline-primary" onclick="viewMessage({{ msg.id }})">
                                                            <i class="fas fa-eye"></i>
                                                        </button>
                                                        <button class="btn btn-sm btn-outline-success" onclick="replyMessage({{ msg.id }})">
                                                            <i class="fas fa-reply"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                            {% endfor %}
                                        {% else %}
                                            <tr>
                                                <td colspan="5" class="text-center py-4">
                                                    <i class="fas fa-users fa-2x text-muted mb-2"></i><br>
                                                    No customer messages
                                                </td>
                                            </tr>
                                        {% endif %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sent Messages Tab -->
                <div class="tab-pane fade" id="sent" role="tabpanel">
                    <div class="card">
                        <div class="card-body">
                            <!-- Similar to customer template but for sent messages -->
                            <div class="text-center py-5">
                                <i class="fas fa-paper-plane fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">Sent Messages</h5>
                                <p class="text-muted">Messages you send will appear here</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Compose Tab -->
                <div class="tab-pane fade" id="compose" role="tabpanel">
                    <div class="card">
                        <div class="card-body">
                            <form id="composeForm" onsubmit="sendMessage(event)">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="recipient" class="form-label">Recipient</label>
                                            <select class="form-select" id="recipient" name="recipient_id" required>
                                                <option value="">Select recipient...</option>
                                                {% for customer in customers %}
                                                <option value="{{ customer.id }}">{{ customer.company_name }} (Customer)</option>
                                                {% endfor %}
                                                <option value="admin">Admin Support</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="priority" class="form-label">Priority</label>
                                            <select class="form-select" id="priority" name="priority">
                                                <option value="normal">Normal</option>
                                                <option value="urgent">Urgent</option>
                                                <option value="low">Low</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="subject" class="form-label">Subject</label>
                                    <input type="text" class="form-control" id="subject" name="subject" required>
                                </div>
                                <div class="mb-3">
                                    <label for="content" class="form-label">Message</label>
                                    <textarea class="form-control" id="content" name="content" rows="6" required></textarea>
                                </div>
                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-paper-plane"></i> Send Message
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary" onclick="clearForm()">
                                        <i class="fas fa-times"></i> Clear
                                    </button>
                                    <button type="button" class="btn btn-outline-info" onclick="saveDraft()">
                                        <i class="fas fa-save"></i> Save Draft
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function composeMessage() {
    document.getElementById('compose-tab').click();
}

function viewMessage(messageId) {
    Swal.fire({
        title: 'View Message',
        text: 'Loading message details...',
        icon: 'info'
    });
}

function replyMessage(messageId) {
    document.getElementById('compose-tab').click();
    Swal.fire({
        title: 'Reply Mode',
        text: 'Compose tab opened for reply',
        icon: 'success'
    });
}

function markAllRead() {
    Swal.fire({
        title: 'Mark All Read',
        text: 'All messages marked as read',
        icon: 'success'
    });
}

function refreshMessages() {
    location.reload();
}

function markImportant(messageId) {
    Swal.fire({
        title: 'Marked Important',
        text: 'Message marked as important',
        icon: 'success'
    });
}

function sendMessage(event) {
    event.preventDefault();
    
    Swal.fire({
        title: 'Sending Message',
        text: 'Please wait...',
        allowOutsideClick: false,
        didOpen: () => {
            Swal.showLoading();
        }
    });
    
    setTimeout(() => {
        Swal.fire({
            title: 'Message Sent!',
            text: 'Your message has been sent successfully.',
            icon: 'success'
        });
        event.target.reset();
        document.getElementById('inbox-tab').click();
    }, 1500);
}

function clearForm() {
    document.getElementById('composeForm').reset();
}

function saveDraft() {
    Swal.fire({
        title: 'Draft Saved',
        text: 'Your message has been saved as draft',
        icon: 'success'
    });
}
</script>
{% endblock %}