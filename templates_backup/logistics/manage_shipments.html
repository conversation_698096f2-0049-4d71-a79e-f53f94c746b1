{% extends "base.html" %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-shipping-fast text-primary"></i> Manage Shipments</h2>
                <div>
                    <button class="btn btn-primary" onclick="showCreateShipmentModal()">
                        <i class="fas fa-plus"></i> Create New Route
                    </button>
                </div>
            </div>

            <!-- Route Type Filter Tabs -->
            <ul class="nav nav-tabs mb-4" id="routeTypeTabs">
                <li class="nav-item">
                    <a class="nav-link active" id="all-routes-tab" data-bs-toggle="tab" href="#all-routes">
                        <i class="fas fa-globe"></i> All Routes <span class="badge bg-primary ms-1" id="all-count">0</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" id="private-routes-tab" data-bs-toggle="tab" href="#private-routes">
                        <i class="fas fa-lock text-warning"></i> Private Routes <span class="badge bg-warning ms-1" id="private-count">0</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" id="assigned-shipments-tab" data-bs-toggle="tab" href="#assigned-shipments">
                        <i class="fas fa-shipping-fast text-success"></i> Assigned Shipments <span class="badge bg-success ms-1" id="shipments-count">0</span>
                    </a>
                </li>
            </ul>

            <!-- Tab Content -->
            <div class="tab-content" id="routeTypeTabContent">
                <!-- All Routes Tab -->
                <div class="tab-pane fade show active" id="all-routes">
                    <!-- Container Sharing Overview -->
                    <div class="card mb-3">
                        <div class="card-body">
                            <h6 class="card-title"><i class="fas fa-cube text-info"></i> Container Sharing Overview</h6>
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="card bg-primary text-white">
                                        <div class="card-body text-center py-2">
                                            <h5 id="totalContainers">0</h5>
                                            <small>Total Containers</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-success text-white">
                                        <div class="card-body text-center py-2">
                                            <h5 id="availableContainers">0</h5>
                                            <small>Available</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-warning text-white">
                                        <div class="card-body text-center py-2">
                                            <h5 id="partialContainers">0</h5>
                                            <small>Partial</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-danger text-white">
                                        <div class="card-body text-center py-2">
                                            <h5 id="fullContainers">0</h5>
                                            <small>Full</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- All Routes Table -->
                    <div class="card">
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover" id="allRoutesTable">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>Route</th>
                                            <th>Type</th>
                                            <th>Transport</th>
                                            <th>Capacity</th>
                                            <th>Status</th>
                                            <th>Fill Threshold</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="allRoutesBody">
                                        <!-- Routes will be loaded here -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Private Routes Tab -->
                <div class="tab-pane fade" id="private-routes">
                    <div class="card">
                        <div class="card-body">
                            <p class="text-muted mb-3">
                                <i class="fas fa-info-circle"></i> 
                                Private routes are only visible to invited clients. Manage your client relationships below.
                            </p>
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <button class="btn btn-outline-primary" onclick="showInviteClientModal()">
                                        <i class="fas fa-user-plus"></i> Invite New Client
                                    </button>
                                </div>
                                <div class="col-md-6 text-end">
                                    <button class="btn btn-outline-info" onclick="viewClientList()">
                                        <i class="fas fa-users"></i> View All Clients (<span id="clientCount">0</span>)
                                    </button>
                                </div>
                            </div>
                            <div id="privateRoutesContent">
                                <!-- Private routes content -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Assigned Shipments Tab -->
                <div class="tab-pane fade" id="assigned-shipments">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5><i class="fas fa-shipping-fast"></i> Assigned Shipments</h5>
                            <div class="btn-group">
                                <button class="btn btn-outline-primary btn-sm" onclick="toggleShipmentFilter()" id="filterBtn">
                                    <i class="fas fa-filter"></i> Filter
                                </button>
                                <button class="btn btn-outline-primary btn-sm" onclick="exportShipments()">
                                    <i class="fas fa-download"></i> Export
                                </button>
                            </div>
                        </div>
                        
                        <!-- Filter Panel (Initially Hidden) -->
                        <div id="filterPanel" class="card-body border-top" style="display: none;">
                            <div class="row">
                                <div class="col-md-3">
                                    <label for="filterTracking">Tracking Number</label>
                                    <input type="text" class="form-control form-control-sm" id="filterTracking" placeholder="Search tracking...">
                                </div>
                                <div class="col-md-3">
                                    <label for="filterCustomer">Customer</label>
                                    <input type="text" class="form-control form-control-sm" id="filterCustomer" placeholder="Search customer...">
                                </div>
                                <div class="col-md-2">
                                    <label for="filterStatus">Status</label>
                                    <select class="form-control form-control-sm" id="filterStatus">
                                        <option value="">All Statuses</option>
                                        <option value="IN_TRANSIT">In Transit</option>
                                        <option value="DELIVERED">Delivered</option>
                                        <option value="PENDING">Pending</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label for="filterCargo">Cargo Type</label>
                                    <input type="text" class="form-control form-control-sm" id="filterCargo" placeholder="Cargo type...">
                                </div>
                                <div class="col-md-2">
                                    <button class="btn btn-primary btn-sm mt-4" onclick="applyShipmentFilters()">Apply Filters</button>
                                    <button class="btn btn-secondary btn-sm mt-4 ml-1" onclick="clearShipmentFilters()">Clear</button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped" id="assignedShipmentsTable">
                                    <thead>
                                        <tr>
                                            <th>Tracking Number</th>
                                            <th>Customer</th>
                                            <th>Route</th>
                                            <th>Cargo Details</th>
                                            <th>Status</th>
                                            <th>Revenue</th>
                                            <th>Date</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="assignedShipmentsBody">
                                        <!-- Shipments will be loaded here -->
                                    </tbody>
                                </table>
                            </div>
                            
                            <!-- Empty State -->
                            <div id="shipmentsEmptyState" class="empty-state text-center py-5" style="display: none;">
                                <i class="fas fa-shipping-fast fa-3x text-muted mb-3"></i>
                                <h5>No shipments assigned</h5>
                                <p class="text-muted">Shipments will appear here when customers accept your quotes</p>
                            </div>
                        </div>
                    </div>

                    <!-- Shipment Statistics Cards -->
                    <div class="row mt-4">
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h3 class="text-primary" id="totalShipments">0</h3>
                                    <p class="card-text">Total Shipments</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h3 class="text-success" id="deliveredShipments">0</h3>
                                    <p class="card-text">Delivered</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h3 class="text-warning" id="inTransitShipments">0</h3>
                                    <p class="card-text">In Transit</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h3 class="text-info" id="totalRevenue">$0</h3>
                                    <p class="card-text">Total Revenue</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>


            </div>
        </div>
    </div>
</div>

<!-- Create Route Modal -->
<div class="modal fade" id="createRouteModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Create New Route</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="createRouteForm">
                <div class="modal-body">
                    <!-- Service Type Selection -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Service Type</label>
                                <select class="form-select" id="serviceType" onchange="updateServiceTypeOptions()">
                                    <option value="public">Public Shared Route</option>
                                    <option value="private">Private Dedicated Route</option>
                                </select>
                                <small class="text-muted" id="serviceDescription">Public route includes shared capacity and container optimization</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Transport Type</label>
                                <select class="form-select" id="transportType" name="transport_type" required>
                                    <option value="">Select transport</option>
                                    <option value="truck">🚛 Truck</option>
                                    <option value="ship">🚢 Ship</option>
                                    <option value="air">✈️ Air</option>
                                    <option value="rail">🚂 Rail</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Container Type Selection -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Container Type</label>
                                <select class="form-select" id="containerType" name="container_type" required>
                                    <option value="">Select container type</option>
                                    <option value="1">📦 Standard 40ft (30.48 tons, 12.19×2.44×2.59m)</option>
                                    <option value="3">📦 20ft Standard (30.48 tons, 6.06×2.44×2.59m)</option>
                                    <option value="4">📦 40ft Standard (30.48 tons, 12.19×2.44×2.59m)</option>
                                    <option value="5">📦 40ft High Cube (30.48 tons, 12.19×2.44×2.90m)</option>
                                    <option value="6">❄️ Refrigerated (27 tons, 5.44×2.29×2.23m)</option>
                                    <option value="7">🚛 Flat Rack (45 tons, 5.94×2.35×2.39m)</option>
                                    <option value="8">📭 Open Top (27 tons, 5.89×2.35×2.34m)</option>
                                    <option value="9">📦 Standard Container (5 tons, 12×2.5×2.7m)</option>
                                </select>
                                <small class="text-muted">Select the container type with capacity and dimensions</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <!-- Space for future additional fields -->
                        </div>
                    </div>

                    <!-- Route Location Information -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Origin Country</label>
                                <select class="form-select" id="originCountry" name="origin_country" onchange="updateOriginStatesRoute()" required>
                                    <option value="">Select origin country</option>
                                    {% for country in countries %}
                                    <option value="{{ country }}">{{ country }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="mb-3" id="originStateGroupRoute" style="display: none;">
                                <label class="form-label">Origin State</label>
                                <select class="form-select" id="originStateRoute" name="origin_state">
                                    <option value="">Select State</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Origin City</label>
                                <input type="text" class="form-control" id="originCity" name="origin_city" required>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Destination Country</label>
                                <select class="form-select" id="destinationCountry" name="destination_country" onchange="updateDestinationStatesRoute()" required>
                                    <option value="">Select destination country</option>
                                    {% for country in countries %}
                                    <option value="{{ country }}">{{ country }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="mb-3" id="destinationStateGroupRoute" style="display: none;">
                                <label class="form-label">Destination State</label>
                                <select class="form-select" id="destinationStateRoute" name="destination_state">
                                    <option value="">Select State</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Destination City</label>
                                <input type="text" class="form-control" id="destinationCity" name="destination_city" required>
                            </div>
                        </div>
                    </div>

                    <!-- Pricing and Capacity Information -->
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Base Price ($/shipment)</label>
                                <input type="number" class="form-control" id="basePrice" name="base_price" step="0.01" placeholder="250.00" required min="0.01">
                                <small class="text-muted">Base price per shipment</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Price per kg ($/kg)</label>
                                <input type="number" class="form-control" id="pricePerKg" name="price_per_kg" step="0.01" placeholder="2.50" required min="0.01">
                                <small class="text-muted">Price per kilogram</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Price per km ($/km)</label>
                                <input type="number" class="form-control" id="pricePerKm" name="price_per_km" step="0.01" placeholder="1.00" required min="0.01">
                                <small class="text-muted">Price per kilometer</small>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Available Space (kg)</label>
                                <input type="number" class="form-control" id="availableSpace" name="available_space" required min="1" placeholder="5000">
                                <small class="text-muted">Available cargo space in kg</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Distance (km)</label>
                                <input type="number" class="form-control" id="distance" name="distance_km" required min="1" placeholder="500">
                                <small class="text-muted">Approximate route distance</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Volume Factor</label>
                                <input type="number" class="form-control" id="volumeFactor" name="volume_factor" step="0.1" value="10.0" required min="0.1">
                                <small class="text-muted">Volume pricing multiplier</small>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Duration (days)</label>
                                <input type="number" class="form-control" id="duration" name="typical_duration_days" required min="1" placeholder="7">
                                <small class="text-muted">Route transit time</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Estimated Delivery (days)</label>
                                <input type="number" class="form-control" id="estimatedDays" name="estimated_days" required min="1" placeholder="7">
                                <small class="text-muted">Expected delivery time</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Weight Factor</label>
                                <input type="number" class="form-control" id="weightFactor" name="weight_factor" step="0.1" value="1.5" required min="0.1">
                                <small class="text-muted">Weight pricing multiplier</small>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Fuel Surcharge (%)</label>
                                <input type="number" class="form-control" id="fuelSurcharge" name="fuel_surcharge" step="0.01" value="8.00" required min="0">
                                <small class="text-muted">Fuel cost percentage</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Handling Fee ($)</label>
                                <input type="number" class="form-control" id="handlingFee" name="handling_fee" step="0.01" value="25.00" required min="0">
                                <small class="text-muted">Processing and handling fee</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Min Fill Threshold (%)</label>
                                <input type="number" class="form-control" id="minFillThreshold" name="min_fill_threshold" value="70" min="0" max="100" placeholder="70">
                                <small class="text-muted">Minimum capacity before shipping</small>
                            </div>
                        </div>
                    </div>

                    <!-- Private Client Selection (only for private routes) -->
                    <div id="privateClientOptions" style="display: none;">
                        <div class="mb-3">
                            <label class="form-label">Private Client</label>
                            <select class="form-select" id="privateClient" name="private_client">
                                <option value="">Select Client (Optional - can invite later)</option>
                                {% for client in clients %}
                                <option value="{{ client.id }}">{{ client.name }} - {{ client.email }}</option>
                                {% endfor %}
                            </select>
                            <small class="text-muted">You can invite additional clients after creating the route</small>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Create Route
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Invite Client Modal -->
<div class="modal fade" id="inviteClientModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Invite New Client</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="inviteClientForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Client Email</label>
                        <input type="email" class="form-control" id="clientEmail" name="client_email" required>
                        <small class="text-muted">Enter the email address of the customer you want to invite</small>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Referral Code (Auto-generated)</label>
                        <input type="text" class="form-control" id="referralCode" name="referral_code" readonly>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-paper-plane"></i> Send Invitation
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Capacity Update Modal -->
<div class="modal fade" id="capacityUpdateModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Update Capacity</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="capacityUpdateForm">
                <div class="modal-body">
                    <input type="hidden" id="updateRouteId" name="route_id">
                    <div class="mb-3">
                        <label class="form-label">Current Capacity Status</label>
                        <div class="progress mb-2">
                            <div class="progress-bar" id="currentProgressBar" role="progressbar"></div>
                        </div>
                        <small class="text-muted" id="currentCapacityText"></small>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">New Available Capacity (kg)</label>
                        <input type="number" class="form-control" id="newCapacity" name="new_capacity" required min="0">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Update Notes</label>
                        <textarea class="form-control" id="updateNotes" name="notes" rows="3" placeholder="Optional: Reason for capacity update"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Update Capacity
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Status Update Modal -->
<div class="modal fade" id="statusUpdateModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Update Shipment Status</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="statusUpdateForm">
                    <input type="hidden" id="updateShipmentId">
                    <div class="form-group">
                        <label for="updateShipmentStatus">New Status</label>
                        <select class="form-control" id="updateShipmentStatus" required>
                            <option value="">Select status</option>
                            <option value="PENDING">Pending</option>
                            <option value="IN_TRANSIT">In Transit</option>
                            <option value="DELIVERED">Delivered</option>
                            <option value="CANCELLED">Cancelled</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="statusNotes">Notes (Optional)</label>
                        <textarea class="form-control" id="statusNotes" rows="3" placeholder="Add any notes about this status update..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="submitStatusUpdate()">Update Status</button>
            </div>
        </div>
    </div>
</div>

<script>
// Unified Logistics Management System
let allRoutes = [];
let clients = [];

document.addEventListener('DOMContentLoaded', function() {
    loadAllData();
    setupEventHandlers();
});

function loadAllData() {
    Promise.all([
        loadRoutes(),
        loadClients(),
        loadAssignedShipments()
    ]).then(() => {
        updateTabCounts();
        renderRouteTable();
    }).catch(error => {
        console.error('Error in loadAllData:', error);
    });
}

async function loadRoutes() {
    try {
        const response = await fetch('/api/logistics/unified-routes');
        const data = await response.json();
        
        if (data.success) {
            allRoutes = data.routes;
            console.log('Routes loaded:', allRoutes.length);
        } else {
            console.error('Error loading routes:', data.error);
            allRoutes = [];
        }
    } catch (error) {
        console.error('Error loading routes:', error);
        allRoutes = [];
    }
}

async function loadClients() {
    try {
        console.log('🔄 Loading clients from API...');
        const response = await fetch('/api/logistics/clients');
        console.log('👥 Clients Response status:', response.status, response.statusText);
        
        const text = await response.text();
        console.log('👥 Clients Raw Response:', text);
        
        if (!text || text.trim() === '') {
            console.error('❌ Empty clients response body');
            clients = [];
            return;
        }
        
        const data = JSON.parse(text);
        console.log('✅ Clients Parsed data:', data);
        
        clients = data.clients || data || [];
        document.getElementById('clientCount').textContent = clients.length;
        console.log('👥 Clients loaded count:', clients.length);
        
    } catch (error) {
        console.error('❌ Error loading clients:', error);
        console.error('❌ Clients Error type:', error.constructor.name);
        console.error('❌ Clients Error message:', error.message);
        clients = [];
    }
}

function updateTabCounts() {
    const publicRoutes = allRoutes.filter(r => !r.is_private);
    const privateRoutes = allRoutes.filter(r => r.is_private);
    const containerRoutes = allRoutes.filter(r => r.container_status && r.container_status !== 'available');
    
    document.getElementById('all-count').textContent = allRoutes.length;
    document.getElementById('public-count').textContent = publicRoutes.length;
    document.getElementById('private-count').textContent = privateRoutes.length;
    document.getElementById('container-count').textContent = containerRoutes.length;
    
    // Update container statistics
    document.getElementById('totalContainers').textContent = allRoutes.length;
    document.getElementById('availableContainers').textContent = allRoutes.filter(r => r.container_status === 'available').length;
    document.getElementById('partialContainers').textContent = allRoutes.filter(r => r.container_status === 'partial').length;
    document.getElementById('fullContainers').textContent = allRoutes.filter(r => r.container_status === 'full').length;
}

function renderRouteTable() {
    const tbody = document.getElementById('allRoutesBody');
    if (!tbody) {
        console.error('allRoutesBody table element not found');
        return;
    }
    
    tbody.innerHTML = '';
    
    console.log('Rendering routes:', allRoutes.length, 'routes');
    
    allRoutes.forEach((route, index) => {
        try {
            // Add safety checks for division by zero
            const capacityPercent = route.total_capacity_kg > 0 ? 
                Math.round((1 - (route.available_capacity_kg / route.total_capacity_kg)) * 100) : 0;
            const statusBadge = getStatusBadge(route.container_status);
            const typeBadge = getTypeBadge(route.is_private);
        
        const row = `
            <tr>
                <td>
                    <strong>${route.origin_city}, ${route.origin_country}</strong><br>
                    <i class="fas fa-arrow-right text-muted"></i> ${route.destination_city}, ${route.destination_country}
                </td>
                <td>${typeBadge}</td>
                <td><span class="badge bg-info">${getTransportIcon(route.shipping_type)} ${route.shipping_type}</span></td>
                <td>
                    <div class="progress mb-1" style="height: 20px;">
                        <div class="progress-bar ${getProgressBarClass(capacityPercent)}" 
                             style="width: ${capacityPercent}%" 
                             title="${capacityPercent}% filled">
                            ${capacityPercent}%
                        </div>
                    </div>
                    <small class="text-muted">${(route.available_capacity_kg || 0).toLocaleString()} / ${(route.total_capacity_kg || 0).toLocaleString()} kg</small>
                </td>
                <td>${statusBadge}</td>
                <td>
                    ${(route.min_fill_threshold || 0) > 0 ? 
                        `<span class="badge bg-warning">${route.min_fill_threshold}% min</span>` : 
                        '<span class="text-muted">No minimum</span>'
                    }
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="editRoute(${route.id})" title="Edit Route">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-outline-success" onclick="updateCapacity(${route.id})" title="Update Capacity">
                            <i class="fas fa-weight"></i>
                        </button>
                        <button class="btn btn-outline-info" onclick="viewDetails(${route.id})" title="View Details">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="deleteRoute(${route.id})" title="Delete Route">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
            tbody.insertAdjacentHTML('beforeend', row);
        } catch (error) {
            console.error('Error rendering route at index', index, ':', error, route);
        }
    });
}

function getTypeBadge(isPrivate) {
    return isPrivate ? 
        '<span class="badge bg-warning"><i class="fas fa-lock"></i> Private</span>' :
        '<span class="badge bg-success"><i class="fas fa-globe"></i> Public</span>';
}

function getStatusBadge(status) {
    const badges = {
        'available': '<span class="badge bg-success">Available</span>',
        'partial': '<span class="badge bg-warning">Partial</span>',
        'full': '<span class="badge bg-danger">Full</span>',
        'shipped': '<span class="badge bg-info">Shipped</span>'
    };
    return badges[status] || '<span class="badge bg-secondary">Unknown</span>';
}

function getTransportIcon(type) {
    const icons = {
        'truck': '🚛',
        'ship': '🚢',
        'air': '✈️',
        'rail': '🚂'
    };
    return icons[type] || '📦';
}

function getProgressBarClass(percent) {
    if (percent < 50) return 'bg-success';
    if (percent < 80) return 'bg-warning';
    return 'bg-danger';
}

function setupEventHandlers() {
    // Create Route Form
    const createRouteForm = document.getElementById('createRouteForm');
    if (createRouteForm) {
        createRouteForm.addEventListener('submit', function(e) {
            e.preventDefault();
            createRoute();
        });
    }
    
    // Invite Client Form
    const inviteClientForm = document.getElementById('inviteClientForm');
    if (inviteClientForm) {
        inviteClientForm.addEventListener('submit', function(e) {
            e.preventDefault();
            inviteClient();
        });
    }
    
    // Capacity Update Form
    const capacityUpdateForm = document.getElementById('capacityUpdateForm');
    if (capacityUpdateForm) {
        capacityUpdateForm.addEventListener('submit', function(e) {
            e.preventDefault();
            submitCapacityUpdate();
        });
    }
    
    // Auto-generate referral code
    const clientEmailInput = document.getElementById('clientEmail');
    if (clientEmailInput) {
        clientEmailInput.addEventListener('input', function() {
            const email = this.value;
            if (email) {
                const referralCode = 'MALIK_' + email.substring(0, 4).toUpperCase();
                const referralCodeInput = document.getElementById('referralCode');
                if (referralCodeInput) {
                    referralCodeInput.value = referralCode;
                }
            }
        });
    }
}

function showCreateShipmentModal() {
    const modal = new bootstrap.Modal(document.getElementById('createRouteModal'));
    modal.show();
}

// Service Type Management Functions
function updateServiceTypeOptions() {
    const serviceType = document.getElementById('serviceType').value;
    const privateOptions = document.getElementById('privateClientOptions');
    const serviceDescription = document.getElementById('serviceDescription');
    
    // Show private client selection only for private service
    privateOptions.style.display = serviceType === 'private' ? 'block' : 'none';
    
    // Update description text based on service type
    if (serviceType === 'private') {
        serviceDescription.textContent = 'Private route includes dedicated capacity, exclusive access, and premium service';
    } else {
        serviceDescription.textContent = 'Public route includes shared capacity and container optimization';
    }
}

// US States data and management
const US_STATES = [
    'Alabama', 'Alaska', 'Arizona', 'Arkansas', 'California', 'Colorado', 'Connecticut',
    'Delaware', 'Florida', 'Georgia', 'Hawaii', 'Idaho', 'Illinois', 'Indiana', 'Iowa',
    'Kansas', 'Kentucky', 'Louisiana', 'Maine', 'Maryland', 'Massachusetts', 'Michigan',
    'Minnesota', 'Mississippi', 'Missouri', 'Montana', 'Nebraska', 'Nevada', 'New Hampshire',
    'New Jersey', 'New Mexico', 'New York', 'North Carolina', 'North Dakota', 'Ohio',
    'Oklahoma', 'Oregon', 'Pennsylvania', 'Rhode Island', 'South Carolina', 'South Dakota',
    'Tennessee', 'Texas', 'Utah', 'Vermont', 'Virginia', 'Washington', 'West Virginia',
    'Wisconsin', 'Wyoming'
];

function updateOriginStatesRoute() {
    const countrySelect = document.getElementById('originCountry');
    const stateGroup = document.getElementById('originStateGroupRoute');
    const stateSelect = document.getElementById('originStateRoute');
    
    if (countrySelect.value === 'United States') {
        // Clear existing options
        stateSelect.innerHTML = '<option value="">Select State</option>';
        
        // Add US states
        US_STATES.forEach(state => {
            const option = new Option(state, state);
            stateSelect.add(option);
        });
        
        stateGroup.style.display = 'block';
    } else {
        stateGroup.style.display = 'none';
    }
}

function updateDestinationStatesRoute() {
    const countrySelect = document.getElementById('destinationCountry');
    const stateGroup = document.getElementById('destinationStateGroupRoute');
    const stateSelect = document.getElementById('destinationStateRoute');
    
    if (countrySelect.value === 'United States') {
        // Clear existing options
        stateSelect.innerHTML = '<option value="">Select State</option>';
        
        // Add US states
        US_STATES.forEach(state => {
            const option = new Option(state, state);
            stateSelect.add(option);
        });
        
        stateGroup.style.display = 'block';
    } else {
        stateGroup.style.display = 'none';
    }
}

function showPrivateShipmentModal() {
    const isPrivateCheckbox = document.getElementById('isPrivate');
    if (isPrivateCheckbox) {
        isPrivateCheckbox.checked = true;
    }
    const modal = new bootstrap.Modal(document.getElementById('createRouteModal'));
    modal.show();
}

function showInviteClientModal() {
    const modal = new bootstrap.Modal(document.getElementById('inviteClientModal'));
    modal.show();
}

function showCreateContainerModal() {
    // Show create route modal with focus on container sharing
    showCreateShipmentModal();
}

async function createRoute() {
    try {
        // Collect form data with enhanced field support
        const formData = new FormData();
        
        // Service and location information
        const serviceType = document.getElementById('serviceType').value;
        formData.append('service_type', serviceType);
        formData.append('transport_type', document.getElementById('transportType').value);
        formData.append('container_type', document.getElementById('containerType').value);
        formData.append('origin_country', document.getElementById('originCountry').value);
        formData.append('origin_state', document.getElementById('originStateRoute').value || '');
        formData.append('origin_city', document.getElementById('originCity').value);
        formData.append('destination_country', document.getElementById('destinationCountry').value);
        formData.append('destination_state', document.getElementById('destinationStateRoute').value || '');
        formData.append('destination_city', document.getElementById('destinationCity').value);
        
        // Pricing and capacity information
        formData.append('base_rate', document.getElementById('baseRate').value);
        formData.append('total_capacity_kg', document.getElementById('totalCapacity').value);
        formData.append('distance_km', document.getElementById('distance').value);
        formData.append('typical_duration_days', document.getElementById('duration').value);
        formData.append('min_fill_threshold', document.getElementById('minFillThreshold').value);
        
        // Private route settings
        formData.append('is_private', serviceType === 'private' ? 'true' : 'false');
        if (serviceType === 'private') {
            const privateClient = document.getElementById('privateClient').value;
            if (privateClient) {
                formData.append('private_client', privateClient);
            }
        }
        
        const response = await fetch('/api/logistics/routes/create', {
            method: 'POST',
            body: formData,
            credentials: 'include'
        });
        
        const result = await response.json();
        if (result.success) {
            const modal = bootstrap.Modal.getInstance(document.getElementById('createRouteModal'));
            modal.hide();
            document.getElementById('createRouteForm').reset();
            
            // Reset conditional visibility
            document.getElementById('privateClientOptions').style.display = 'none';
            document.getElementById('originStateGroupRoute').style.display = 'none';
            document.getElementById('destinationStateGroupRoute').style.display = 'none';
            
            Swal.fire('Success!', 'Route created successfully', 'success');
            loadAllData();
        } else {
            Swal.fire('Error!', result.error || 'Failed to create route', 'error');
        }
    } catch (error) {
        console.error('Error creating route:', error);
        Swal.fire('Error!', 'Network error occurred', 'error');
    }
}

async function inviteClient() {
    try {
        const formData = new FormData($('#inviteClientForm')[0]);
        const response = await fetch('/api/logistics/invite-client', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        if (result.success) {
            $('#inviteClientModal').modal('hide');
            $('#inviteClientForm')[0].reset();
            Swal.fire('Success!', 'Client invitation sent successfully', 'success');
            loadClients();
        } else {
            Swal.fire('Error!', result.error || 'Failed to send invitation', 'error');
        }
    } catch (error) {
        console.error('Error inviting client:', error);
        Swal.fire('Error!', 'Network error occurred', 'error');
    }
}

function updateCapacity(routeId) {
    const route = allRoutes.find(r => r.id === routeId);
    if (!route) return;
    
    $('#updateRouteId').val(routeId);
    $('#newCapacity').val(route.available_capacity_kg);
    
    const capacityPercent = Math.round((1 - (route.available_capacity_kg / route.total_capacity_kg)) * 100);
    $('#currentProgressBar').css('width', capacityPercent + '%')
                           .removeClass('bg-success bg-warning bg-danger')
                           .addClass(getProgressBarClass(capacityPercent));
    $('#currentCapacityText').text(`${route.available_capacity_kg.toLocaleString()} kg available of ${route.total_capacity_kg.toLocaleString()} kg total (${100-capacityPercent}% available)`);
    
    $('#capacityUpdateModal').modal('show');
}

async function submitCapacityUpdate() {
    try {
        const formData = new FormData($('#capacityUpdateForm')[0]);
        const response = await fetch('/api/logistics/update-capacity', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        if (result.success) {
            $('#capacityUpdateModal').modal('hide');
            $('#capacityUpdateForm')[0].reset();
            Swal.fire('Success!', 'Capacity updated successfully', 'success');
            loadAllData();
        } else {
            Swal.fire('Error!', result.error || 'Failed to update capacity', 'error');
        }
    } catch (error) {
        console.error('Error updating capacity:', error);
        Swal.fire('Error!', 'Network error occurred', 'error');
    }
}

function editRoute(routeId) {
    // Navigate to route edit page
    window.location.href = `/logistics/route/${routeId}`;
}

function viewDetails(routeId) {
    // Navigate to route details page
    window.location.href = `/logistics/route/${routeId}/details`;
}

async function deleteRoute(routeId) {
    const result = await Swal.fire({
        title: 'Are you sure?',
        text: 'This will permanently delete the route and all associated data.',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Yes, delete it!'
    });
    
    if (result.isConfirmed) {
        try {
            const response = await fetch(`/api/logistics/routes/${routeId}`, {
                method: 'DELETE'
            });
            
            const data = await response.json();
            if (data.success) {
                Swal.fire('Deleted!', 'Route has been deleted.', 'success');
                loadAllData();
            } else {
                Swal.fire('Error!', data.error || 'Failed to delete route', 'error');
            }
        } catch (error) {
            console.error('Error deleting route:', error);
            Swal.fire('Error!', 'Network error occurred', 'error');
        }
    }
}

function viewClientList() {
    // Navigate to clients management page
    window.location.href = '/logistics/clients';
}

// Assigned Shipments Functions
async function loadAssignedShipments() {
    try {
        const response = await fetch('/api/logistics/assigned-shipments');
        const result = await response.json();
        
        if (result.success) {
            displayAssignedShipments(result.shipments);
            updateShipmentsStats(result.shipments);
            document.getElementById('shipments-count').textContent = result.shipments.length;
        } else {
            console.error('Error loading assigned shipments:', result.error);
            showEmptyShipmentsState();
        }
    } catch (error) {
        console.error('Error loading assigned shipments:', error);
        showEmptyShipmentsState();
    }
}

function displayAssignedShipments(shipments) {
    const tbody = document.getElementById('assignedShipmentsBody');
    const emptyState = document.getElementById('shipmentsEmptyState');
    
    if (!shipments || shipments.length === 0) {
        tbody.innerHTML = '';
        emptyState.style.display = 'block';
        return;
    }
    
    emptyState.style.display = 'none';
    tbody.innerHTML = '';
    
    shipments.forEach(shipment => {
        const row = `
            <tr>
                <td><strong>${shipment.tracking_number}</strong></td>
                <td><strong>${shipment.customer_name}</strong></td>
                <td>
                    <small class="text-muted">${shipment.origin}</small><br>
                    <i class="fas fa-arrow-down text-primary"></i><br>
                    <small class="text-muted">${shipment.destination}</small>
                </td>
                <td>
                    <span class="badge badge-info">${shipment.cargo_type}</span><br>
                    <small>${shipment.weight}</small>
                </td>
                <td>${getShipmentStatusBadge(shipment.status)}</td>
                <td><strong class="text-success">${shipment.revenue}</strong></td>
                <td>${shipment.created_date}</td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <a href="/shipment/${shipment.id}" class="btn btn-outline-primary" title="View Process Flow">
                            <i class="fas fa-route"></i>
                        </a>
                        <button class="btn btn-outline-secondary" title="Update Status" 
                                onclick="updateShipmentStatus('${shipment.id}', '${shipment.tracking_number}')">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-outline-info" title="Track" 
                                onclick="trackShipment('${shipment.tracking_number}')">
                            <i class="fas fa-map-marker-alt"></i>
                        </button>
                        <button class="btn btn-outline-success" title="Contact Customer" 
                                onclick="contactCustomer('${shipment.customer_email}', '${shipment.customer_name}')">
                            <i class="fas fa-envelope"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
        tbody.insertAdjacentHTML('beforeend', row);
    });
}

function getShipmentStatusBadge(status) {
    const badges = {
        'IN_TRANSIT': '<span class="badge badge-primary">In Transit</span>',
        'DELIVERED': '<span class="badge badge-success">Delivered</span>',
        'PENDING': '<span class="badge badge-warning">Pending</span>',
        'CANCELLED': '<span class="badge badge-danger">Cancelled</span>'
    };
    return badges[status] || `<span class="badge badge-secondary">${status}</span>`;
}

function updateShipmentsStats(shipments) {
    const total = shipments.length;
    const delivered = shipments.filter(s => s.status === 'DELIVERED').length;
    const inTransit = shipments.filter(s => s.status === 'IN_TRANSIT').length;
    const totalRevenue = shipments.reduce((sum, s) => sum + parseFloat(s.revenue.replace(/[^0-9.-]+/g, '')), 0);
    
    document.getElementById('totalShipments').textContent = total;
    document.getElementById('deliveredShipments').textContent = delivered;
    document.getElementById('inTransitShipments').textContent = inTransit;
    document.getElementById('totalRevenue').textContent = `$${totalRevenue.toLocaleString()}`;
}

function showEmptyShipmentsState() {
    document.getElementById('assignedShipmentsBody').innerHTML = '';
    document.getElementById('shipmentsEmptyState').style.display = 'block';
    document.getElementById('shipments-count').textContent = '0';
    
    // Reset stats
    document.getElementById('totalShipments').textContent = '0';
    document.getElementById('deliveredShipments').textContent = '0';
    document.getElementById('inTransitShipments').textContent = '0';
    document.getElementById('totalRevenue').textContent = '$0';
}

function updateShipmentStatus(shipmentId, trackingNumber) {
    document.getElementById('updateShipmentId').value = shipmentId;
    document.getElementById('updateShipmentStatus').value = '';
    document.getElementById('statusNotes').value = '';
    
    const modal = new bootstrap.Modal(document.getElementById('statusUpdateModal'));
    modal.show();
}

async function submitStatusUpdate() {
    try {
        const shipmentId = document.getElementById('updateShipmentId').value;
        const status = document.getElementById('updateShipmentStatus').value;
        const notes = document.getElementById('statusNotes').value;
        
        if (!status) {
            Swal.fire('Error!', 'Please select a status', 'error');
            return;
        }
        
        const response = await fetch(`/api/logistics/shipment/${shipmentId}/status`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ status, notes })
        });
        
        const result = await response.json();
        if (result.success) {
            const modal = bootstrap.Modal.getInstance(document.getElementById('statusUpdateModal'));
            modal.hide();
            Swal.fire('Success!', 'Shipment status updated successfully', 'success');
            loadAssignedShipments();
        } else {
            Swal.fire('Error!', result.error || 'Failed to update status', 'error');
        }
    } catch (error) {
        console.error('Error updating shipment status:', error);
        Swal.fire('Error!', 'Network error occurred', 'error');
    }
}

function trackShipment(trackingNumber) {
    Swal.fire({
        title: 'Track Shipment',
        html: `
            <div class="text-start">
                <p><strong>Tracking Number:</strong> ${trackingNumber}</p>
                <p><strong>Last Update:</strong> In Transit - Package at sorting facility</p>
                <p><strong>Expected Delivery:</strong> 2-3 business days</p>
                <div class="progress">
                    <div class="progress-bar bg-info" role="progressbar" style="width: 65%"></div>
                </div>
                <small class="text-muted">65% Complete</small>
            </div>
        `,
        icon: 'info',
        confirmButtonText: 'Close'
    });
}

function contactCustomer(email, name) {
    Swal.fire({
        title: 'Contact Customer',
        html: `
            <div class="text-start">
                <p><strong>Customer:</strong> ${name}</p>
                <p><strong>Email:</strong> ${email}</p>
                <div class="btn-group w-100 mt-3">
                    <button class="btn btn-primary" onclick="window.location.href='mailto:${email}'">
                        <i class="fas fa-envelope"></i> Send Email
                    </button>
                    <button class="btn btn-info" onclick="window.open('/logistics/messages?customer=${email}', '_blank')">
                        <i class="fas fa-comments"></i> Send Message
                    </button>
                </div>
            </div>
        `,
        showConfirmButton: false,
        showCloseButton: true
    });
}

function toggleShipmentFilter() {
    const filterPanel = document.getElementById('filterPanel');
    const filterBtn = document.getElementById('filterBtn');
    
    if (filterPanel.style.display === 'none') {
        filterPanel.style.display = 'block';
        filterBtn.innerHTML = '<i class="fas fa-times"></i> Hide Filter';
    } else {
        filterPanel.style.display = 'none';
        filterBtn.innerHTML = '<i class="fas fa-filter"></i> Filter';
    }
}

function applyShipmentFilters() {
    const tracking = document.getElementById('filterTracking').value.toLowerCase();
    const customer = document.getElementById('filterCustomer').value.toLowerCase();
    const status = document.getElementById('filterStatus').value;
    const cargo = document.getElementById('filterCargo').value.toLowerCase();
    
    const rows = document.querySelectorAll('#assignedShipmentsTable tbody tr');
    
    rows.forEach(row => {
        const trackingText = row.cells[0].textContent.toLowerCase();
        const customerText = row.cells[1].textContent.toLowerCase();
        const statusText = row.cells[4].textContent;
        const cargoText = row.cells[3].textContent.toLowerCase();
        
        const matchesTracking = !tracking || trackingText.includes(tracking);
        const matchesCustomer = !customer || customerText.includes(customer);
        const matchesStatus = !status || statusText.includes(status);
        const matchesCargo = !cargo || cargoText.includes(cargo);
        
        if (matchesTracking && matchesCustomer && matchesStatus && matchesCargo) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}

function clearShipmentFilters() {
    document.getElementById('filterTracking').value = '';
    document.getElementById('filterCustomer').value = '';
    document.getElementById('filterStatus').value = '';
    document.getElementById('filterCargo').value = '';
    
    const rows = document.querySelectorAll('#assignedShipmentsTable tbody tr');
    rows.forEach(row => row.style.display = '');
}

function exportShipments() {
    const table = document.getElementById('assignedShipmentsTable');
    let csv = 'Tracking Number,Customer,Origin,Destination,Cargo Type,Weight,Status,Revenue,Date\n';
    
    const rows = table.querySelectorAll('tbody tr:not([style*="display: none"])');
    rows.forEach(row => {
        const cells = row.querySelectorAll('td');
        const rowData = [
            cells[0].textContent.trim(),
            cells[1].textContent.trim(),
            cells[2].textContent.split('\n')[0].trim(),
            cells[2].textContent.split('\n')[2].trim(),
            cells[3].textContent.split('\n')[0].trim(),
            cells[3].textContent.split('\n')[1].trim(),
            cells[4].textContent.trim(),
            cells[5].textContent.trim(),
            cells[6].textContent.trim()
        ];
        csv += rowData.map(field => `"${field}"`).join(',') + '\n';
    });
    
    const blob = new Blob([csv], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `assigned_shipments_${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
}

// Function already defined above - removing duplicate

// U.S. State control function
function checkUSSelection() {
    const originCountry = document.getElementById('originCountry').value;
    const destinationCountry = document.getElementById('destinationCountry').value;
    
    const originStateDiv = document.getElementById('originStateDiv');
    const destinationStateDiv = document.getElementById('destinationStateDiv');
    
    if (originCountry === 'United States') {
        originStateDiv.style.display = 'block';
    } else {
        originStateDiv.style.display = 'none';
        document.getElementById('originState').value = '';
    }
    
    if (destinationCountry === 'United States') {
        destinationStateDiv.style.display = 'block';
    } else {
        destinationStateDiv.style.display = 'none';
        document.getElementById('destinationState').value = '';
    }
}

// Add Route Form Handler
document.addEventListener('DOMContentLoaded', function() {
    const addRouteForm = document.getElementById('createRouteForm');
    if (addRouteForm) {
        addRouteForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Handle origin location
            const originCountry = document.getElementById('originCountry').value;
            const originState = document.getElementById('originState').value;
            const originCity = document.getElementById('originCity').value;
            
            // Handle destination location  
            const destinationCountry = document.getElementById('destinationCountry').value;
            const destinationState = document.getElementById('destinationState').value;
            const destinationCity = document.getElementById('destinationCity').value;
            
            // Build location strings
            const originLocation = originCountry === 'United States' && originState ? 
                                  `${originCity}, ${originState}, ${originCountry}` : `${originCity}, ${originCountry}`;
            const destinationLocation = destinationCountry === 'United States' && destinationState ? 
                                       `${destinationCity}, ${destinationState}, ${destinationCountry}` : `${destinationCity}, ${destinationCountry}`;
            
            const formData = {
                origin_country: originCountry,
                origin_state: originState || '',
                origin_city: originCity,
                destination_country: destinationCountry,
                destination_state: destinationState || '',
                destination_city: destinationCity,
                transport_type: document.getElementById('transportType').value,
                container_type: document.getElementById('containerType').value,
                base_price: parseFloat(document.getElementById('basePrice').value),
                price_per_kg: parseFloat(document.getElementById('pricePerKg').value),
                price_per_km: parseFloat(document.getElementById('pricePerKm').value),
                estimated_days: parseInt(document.getElementById('estimatedDays').value),
                available_space: parseInt(document.getElementById('availableSpace').value),
                distance_km: parseFloat(document.getElementById('distance').value),
                typical_duration_days: parseInt(document.getElementById('duration').value),
                volume_factor: parseFloat(document.getElementById('volumeFactor').value),
                weight_factor: parseFloat(document.getElementById('weightFactor').value),
                fuel_surcharge: parseFloat(document.getElementById('fuelSurcharge').value),
                handling_fee: parseFloat(document.getElementById('handlingFee').value),
                total_capacity_kg: parseInt(document.getElementById('availableSpace').value),
                available_capacity_kg: parseInt(document.getElementById('availableSpace').value),
                min_fill_threshold: parseInt(document.getElementById('minFillThreshold').value) || 70,
                is_private: document.getElementById('serviceType').value === 'private',
                is_active: true
            };
            
            // Submit to API
            fetch('/api/logistics/routes/create', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const modal = bootstrap.Modal.getInstance(document.getElementById('createRouteModal'));
                    modal.hide();
                    Swal.fire('Success!', 'Route created successfully', 'success');
                    loadAllData(); // Reload the data
                } else {
                    Swal.fire('Error!', data.error || 'Failed to create route', 'error');
                }
            })
            .catch(error => {
                console.error('Error creating route:', error);
                Swal.fire('Error!', 'Network error occurred', 'error');
            });
        });
    }
    
    // Add event listeners for country dropdowns
    const originCountry = document.getElementById('originCountry');
    const destinationCountry = document.getElementById('destinationCountry');
    
    if (originCountry) {
        originCountry.addEventListener('change', checkUSSelection);
    }
    if (destinationCountry) {
        destinationCountry.addEventListener('change', checkUSSelection);
    }
});


// Support functions for contact actions
function sendEmail(email) {
    window.location.href = `mailto:${email}`;
}

function openNewWindow(url) {
    window.open(url, '_blank');
}
</script>

<!-- Duplicate modal removed - using createRouteModal instead -->

<style>
.progress {
    position: relative;
}

.progress-bar {
    transition: width 0.3s ease;
}

.nav-tabs .nav-link {
    color: #495057;
}

.nav-tabs .nav-link.active {
    font-weight: 600;
}

.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

.table th {
    font-weight: 600;
    border-top: none;
}

.badge {
    font-size: 0.75em;
}
</style>
{% endblock %}