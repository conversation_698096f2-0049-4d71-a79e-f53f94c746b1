{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <h1><i class="fas fa-headset"></i> Contact Support</h1>
                <p class="text-muted">Get in touch with our support team for assistance</p>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Contact Form -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-envelope"></i> Send us a Message</h5>
                </div>
                <div class="card-body">
                    <form id="contactForm" onsubmit="submitContactForm(event)">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="name">Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="name" name="name" 
                                           value="{% if user %}{{ user.first_name }} {{ user.last_name }}{% endif %}" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="email">Email Address <span class="text-danger">*</span></label>
                                    <input type="email" class="form-control" id="email" name="email" 
                                           value="{% if user %}{{ user.email }}{% endif %}" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="priority">Priority</label>
                                    <select class="form-control" id="priority" name="priority">
                                        <option value="low">Low - General inquiry</option>
                                        <option value="medium" selected>Medium - Standard support</option>
                                        <option value="high">High - Urgent issue</option>
                                        <option value="critical">Critical - System down</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="category">Category</label>
                                    <select class="form-control" id="category" name="category">
                                        <option value="general">General Support</option>
                                        <option value="technical">Technical Issue</option>
                                        <option value="billing">Billing & Payments</option>
                                        <option value="shipping">Shipping & Tracking</option>
                                        <option value="account">Account Management</option>
                                        <option value="feature">Feature Request</option>
                                        <option value="bug">Bug Report</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="subject">Subject <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="subject" name="subject" 
                                   placeholder="Brief description of your inquiry" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="message">Message <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="message" name="message" rows="6" 
                                      placeholder="Please provide detailed information about your inquiry..." required></textarea>
                            <small class="form-text text-muted">Minimum 20 characters required</small>
                        </div>
                        
                        <div class="form-group">
                            <label for="attachment">Attachment (Optional)</label>
                            <input type="file" class="form-control-file" id="attachment" name="attachment" 
                                   accept=".pdf,.doc,.docx,.txt,.png,.jpg,.jpeg">
                            <small class="form-text text-muted">Max file size: 10MB. Allowed formats: PDF, DOC, TXT, PNG, JPG</small>
                        </div>
                        
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="copyMe" name="copyMe" checked>
                            <label class="form-check-label" for="copyMe">
                                Send me a copy of this message
                            </label>
                        </div>
                        
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-paper-plane"></i> Send Message
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Contact Information -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-info-circle"></i> Contact Information</h5>
                </div>
                <div class="card-body">
                    <div class="contact-method">
                        <i class="fas fa-phone text-primary"></i>
                        <div>
                            <strong>Phone Support</strong>
                            <p>******-CLOVER (258-8377)<br>
                            24/7 Support Available</p>
                        </div>
                    </div>
                    
                    <div class="contact-method">
                        <i class="fas fa-envelope text-primary"></i>
                        <div>
                            <strong>Email Support</strong>
                            <p><EMAIL><br>
                            Response within 2 hours</p>
                        </div>
                    </div>
                    
                    <div class="contact-method">
                        <i class="fas fa-comments text-primary"></i>
                        <div>
                            <strong>Live Chat</strong>
                            <p>Available 24/7<br>
                            <button class="btn btn-sm btn-outline-primary" onclick="startLiveChat()">Start Chat</button></p>
                        </div>
                    </div>
                    
                    <div class="contact-method">
                        <i class="fas fa-map-marker-alt text-primary"></i>
                        <div>
                            <strong>Headquarters</strong>
                            <p>1234 Business Center Dr<br>
                            New York, NY 10001<br>
                            United States</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Response Time -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5><i class="fas fa-clock"></i> Response Times</h5>
                </div>
                <div class="card-body">
                    <div class="response-time">
                        <span class="badge badge-danger">Critical</span>
                        <span>15 minutes</span>
                    </div>
                    <div class="response-time">
                        <span class="badge badge-warning">High</span>
                        <span>1 hour</span>
                    </div>
                    <div class="response-time">
                        <span class="badge badge-info">Medium</span>
                        <span>4 hours</span>
                    </div>
                    <div class="response-time">
                        <span class="badge badge-secondary">Low</span>
                        <span>24 hours</span>
                    </div>
                </div>
            </div>

            <!-- Quick Links -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5><i class="fas fa-external-link-alt"></i> Quick Links</h5>
                </div>
                <div class="card-body">
                    <a href="/help" class="btn btn-outline-primary btn-block mb-2">
                        <i class="fas fa-question-circle"></i> Help Center
                    </a>
                    <a href="/customer/track-shipment" class="btn btn-outline-info btn-block mb-2">
                        <i class="fas fa-search"></i> Track Shipment
                    </a>
                    <a href="/customer/manage-shipments" class="btn btn-outline-success btn-block mb-2">
                        <i class="fas fa-boxes"></i> My Shipments
                    </a>
                    <a href="/profile" class="btn btn-outline-secondary btn-block">
                        <i class="fas fa-user"></i> Account Settings
                    </a>
                </div>
            </div>

            <!-- System Status -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5><i class="fas fa-server"></i> System Status</h5>
                </div>
                <div class="card-body">
                    <div class="system-status">
                        <span class="status-indicator online"></span>
                        <span>All Systems Operational</span>
                    </div>
                    <div class="system-status">
                        <span class="status-indicator online"></span>
                        <span>Tracking Services</span>
                    </div>
                    <div class="system-status">
                        <span class="status-indicator online"></span>
                        <span>Payment Processing</span>
                    </div>
                    <div class="system-status">
                        <span class="status-indicator online"></span>
                        <span>API Services</span>
                    </div>
                    <small class="text-muted d-block mt-2">
                        <i class="fas fa-external-link-alt"></i> 
                        <a href="javascript:void(0)" onclick="showSystemStatus()">View detailed status</a>
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.contact-method {
    display: flex;
    align-items: flex-start;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

.contact-method:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.contact-method i {
    font-size: 1.5rem;
    margin-right: 15px;
    margin-top: 5px;
}

.contact-method div {
    flex: 1;
}

.contact-method strong {
    display: block;
    margin-bottom: 5px;
}

.contact-method p {
    margin: 0;
    color: #6c757d;
}

.response-time {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    padding-bottom: 8px;
    border-bottom: 1px solid #eee;
}

.response-time:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.system-status {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 10px;
}

.status-indicator.online {
    background-color: #28a745;
}

.status-indicator.offline {
    background-color: #dc3545;
}

.status-indicator.maintenance {
    background-color: #ffc107;
}

.form-group label {
    font-weight: 500;
    color: #495057;
}

.text-danger {
    color: #dc3545;
}

.card {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}
</style>

<script>
function submitContactForm(event) {
    event.preventDefault();
    
    // Basic form validation
    const name = document.getElementById('name').value.trim();
    const email = document.getElementById('email').value.trim();
    const subject = document.getElementById('subject').value.trim();
    const message = document.getElementById('message').value.trim();
    
    if (name === '' || email === '' || subject === '' || message === '') {
        showAlert('Please fill in all required fields', 'danger');
        return;
    }
    
    if (message.length < 20) {
        showAlert('Message must be at least 20 characters long', 'warning');
        return;
    }
    
    // Simulate form submission
    const submitBtn = event.target.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Sending...';
    submitBtn.disabled = true;
    
    const formData = new FormData(event.target);
    
    fetch('/contact/submit', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            showAlert(data.message, 'success');
            document.getElementById('contactForm').reset();
        } else {
            showAlert('Error sending message. Please try again.', 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('Error sending message. Please try again.', 'danger');
    })
    .finally(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
}

function startLiveChat() {
    showAlert('Live chat window will open shortly', 'info');
    // In a real implementation, this would open a chat widget
}

function showSystemStatus() {
    showAlert('System status page will open', 'info');
    // In a real implementation, this would open a status dashboard
}

function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="close" data-dismiss="alert">
            <span>&times;</span>
        </button>
    `;
    
    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);
    
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}

// Character counter for message field
document.getElementById('message').addEventListener('input', function() {
    const messageLength = this.value.length;
    const minLength = 20;
    
    if (messageLength < minLength) {
        this.style.borderColor = '#dc3545';
    } else {
        this.style.borderColor = '#28a745';
    }
});

// Auto-populate category based on current page or user type
document.addEventListener('DOMContentLoaded', function() {
    {% if user and user.user_type == 'LOGISTICS_PROVIDER' %}
        document.getElementById('category').value = 'account';
    {% endif %}
});
</script>
{% endblock %}