{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <h1><i class="fas fa-edit"></i> Compose Message</h1>
                <p class="text-muted">Send a new message to another user</p>
            </div>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-envelope"></i> New Message</h5>
                </div>
                <div class="card-body">
                    {% if error %}
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i> {{ error }}
                    </div>
                    {% endif %}
                    
                    <form method="POST" action="/messages/compose">
                        <div class="form-group mb-3">
                            <label for="recipient_email" class="form-label">To (Email):</label>
                            <input type="email" class="form-control" id="recipient_email" name="recipient_email" 
                                   value="{{ reply_to or '' }}" required>
                            <small class="form-text text-muted">Enter the recipient's email address</small>
                        </div>
                        
                        <div class="form-group mb-3">
                            <label for="subject" class="form-label">Subject:</label>
                            <input type="text" class="form-control" id="subject" name="subject" 
                                   value="{{ subject or '' }}" required>
                        </div>
                        
                        <div class="form-group mb-3">
                            <label for="content" class="form-label">Message:</label>
                            <textarea class="form-control" id="content" name="content" rows="8" required>{{ original_content or '' }}</textarea>
                        </div>
                        
                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-paper-plane"></i> Send Message
                            </button>
                            <a href="/messages" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.form-group {
    margin-bottom: 1rem;
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.btn-primary {
    background-color: #007bff;
    border-color: #007bff;
}

.btn-secondary {
    background-color: #6c757d;
    border-color: #6c757d;
}

textarea.form-control {
    resize: vertical;
    min-height: 120px;
}
</style>
{% endblock %}