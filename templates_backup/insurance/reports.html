{% extends "base.html" %}

{% block content %}
<div class="dashboard-container">
    <div class="dashboard-header">
        <h1>Insurance Reports</h1>
        <p>Generate and analyze insurance performance and risk assessment reports</p>
    </div>

    <!-- Report Statistics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-primary">
                    <i class="fas fa-chart-bar"></i>
                </div>
                <div class="stat-content">
                    <h3>1,847</h3>
                    <p>Policies Analyzed</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-success">
                    <i class="fas fa-dollar-sign"></i>
                </div>
                <div class="stat-content">
                    <h3>$8.4M</h3>
                    <p>Premium Revenue</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="stat-content">
                    <h3>234</h3>
                    <p>Claims Processed</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-info">
                    <i class="fas fa-percentage"></i>
                </div>
                <div class="stat-content">
                    <h3>96.2%</h3>
                    <p>Profitability Rate</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Report Generation -->
    <div class="card mb-4">
        <div class="card-header">
            <h5>Generate New Report</h5>
        </div>
        <div class="card-body">
            <form>
    <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                <div class="row">
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label class="form-label">Report Type</label>
                            <select class="form-select">
                                <option>Claims Analysis</option>
                                <option>Risk Assessment</option>
                                <option>Premium Performance</option>
                                <option>Underwriting Summary</option>
                                <option>Financial Overview</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label class="form-label">Time Period</label>
                            <select class="form-select">
                                <option>Last 30 Days</option>
                                <option>Last Quarter</option>
                                <option>Last 6 Months</option>
                                <option>Year to Date</option>
                                <option>Custom Range</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label class="form-label">Export Format</label>
                            <select class="form-select">
                                <option>PDF Report</option>
                                <option>Excel Spreadsheet</option>
                                <option>CSV Data</option>
                                <option>PowerPoint</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label class="form-label">&nbsp;</label>
                            <button type="submit" class="btn btn-primary form-control" onclick="generateReport()">Generate Report</button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Performance Analytics -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5>Premium Revenue Trend (Last 6 Months)</h5>
                </div>
                <div class="card-body">
                    <div class="chart-placeholder bg-light border rounded" style="height: 300px; display: flex; align-items: center; justify-content: center;">
                        <div class="text-center">
                            <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                            <h6>Premium Revenue Performance</h6>
                            <div class="row text-center mt-3">
                                <div class="col-2"><small>Aug: $1.2M</small></div>
                                <div class="col-2"><small>Sep: $1.4M</small></div>
                                <div class="col-2"><small>Oct: $1.6M</small></div>
                                <div class="col-2"><small>Nov: $1.5M</small></div>
                                <div class="col-2"><small>Dec: $1.8M</small></div>
                                <div class="col-2"><small>Jan: $2.1M</small></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5>Key Performance Indicators</h5>
                </div>
                <div class="card-body">
                    <div class="kpi-metrics">
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>Loss Ratio</span>
                                <span class="text-success">3.8%</span>
                            </div>
                            <div class="progress mt-1">
                                <div class="progress-bar bg-success" style="width: 15%"></div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>Expense Ratio</span>
                                <span class="text-primary">12.4%</span>
                            </div>
                            <div class="progress mt-1">
                                <div class="progress-bar bg-primary" style="width: 25%"></div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>Combined Ratio</span>
                                <span class="text-warning">16.2%</span>
                            </div>
                            <div class="progress mt-1">
                                <div class="progress-bar bg-warning" style="width: 32%"></div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>Return on Equity</span>
                                <span class="text-info">18.7%</span>
                            </div>
                            <div class="progress mt-1">
                                <div class="progress-bar bg-info" style="width: 75%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Reports -->
    <div class="card mb-4">
        <div class="card-header">
            <h5>Recent Reports</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Report Name</th>
                            <th>Type</th>
                            <th>Generated</th>
                            <th>Period</th>
                            <th>Size</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Monthly Claims Analysis</td>
                            <td>Claims Analysis</td>
                            <td>2025-01-26 16:30</td>
                            <td>December 2024</td>
                            <td>3.2 MB</td>
                            <td><span class="badge bg-success">Ready</span></td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary" onclick="downloadReport('Monthly Claims Analysis')">Download</button>
                                <button class="btn btn-sm btn-outline-info" onclick="viewReport('Monthly Claims Analysis')">View</button>
                            </td>
                        </tr>
                        <tr>
                            <td>Q4 Risk Assessment Report</td>
                            <td>Risk Assessment</td>
                            <td>2025-01-25 14:20</td>
                            <td>Q4 2024</td>
                            <td>4.8 MB</td>
                            <td><span class="badge bg-success">Ready</span></td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary" onclick="downloadReport('Q4 Risk Assessment Report')">Download</button>
                                <button class="btn btn-sm btn-outline-info" onclick="viewReport('Q4 Risk Assessment Report')">View</button>
                            </td>
                        </tr>
                        <tr>
                            <td>Premium Performance Analysis</td>
                            <td>Premium Performance</td>
                            <td>2025-01-24 11:45</td>
                            <td>Year 2024</td>
                            <td>2.1 MB</td>
                            <td><span class="badge bg-success">Ready</span></td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary" onclick="downloadReport('Premium Performance Analysis')">Download</button>
                                <button class="btn btn-sm btn-outline-info" onclick="viewReport('Premium Performance Analysis')">View</button>
                            </td>
                        </tr>
                        <tr>
                            <td>Underwriting Summary Report</td>
                            <td>Underwriting Summary</td>
                            <td>2025-01-26 09:30</td>
                            <td>January 2025</td>
                            <td>1.6 MB</td>
                            <td><span class="badge bg-warning">Processing</span></td>
                            <td>
                                <button class="btn btn-sm btn-outline-secondary" disabled onclick="downloadReport(this)">Download</button>
                                <button class="btn btn-sm btn-outline-secondary" disabled onclick="viewReport(this)">View</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Risk Analysis -->
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>Risk Distribution</h5>
                </div>
                <div class="card-body">
                    <div class="chart-placeholder bg-light border rounded" style="height: 250px; display: flex; align-items: center; justify-content: center;">
                        <div class="text-center">
                            <i class="fas fa-chart-pie fa-3x text-muted mb-3"></i>
                            <h6>Risk Categories Distribution</h6>
                            <div class="mt-3">
                                <small><span class="badge bg-success">Low Risk: 62%</span></small><br>
                                <small><span class="badge bg-warning">Medium Risk: 28%</span></small><br>
                                <small><span class="badge bg-danger">High Risk: 10%</span></small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>Claims Frequency by Type</h5>
                </div>
                <div class="card-body">
                    <div class="claims-breakdown">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span>Water Damage</span>
                            <span class="text-primary">34%</span>
                        </div>
                        <div class="progress mb-3">
                            <div class="progress-bar bg-primary" style="width: 34%"></div>
                        </div>
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span>Theft/Piracy</span>
                            <span class="text-warning">28%</span>
                        </div>
                        <div class="progress mb-3">
                            <div class="progress-bar bg-warning" style="width: 28%"></div>
                        </div>
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span>Transit Damage</span>
                            <span class="text-info">22%</span>
                        </div>
                        <div class="progress mb-3">
                            <div class="progress-bar bg-info" style="width: 22%"></div>
                        </div>
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span>Total Loss</span>
                            <span class="text-danger">16%</span>
                        </div>
                        <div class="progress mb-3">
                            <div class="progress-bar bg-danger" style="width: 16%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function generateReport() {
    event.preventDefault();
    
    const reportType = document.querySelector('select:nth-of-type(1)').value;
    const timePeriod = document.querySelector('select:nth-of-type(2)').value;
    const exportFormat = document.querySelector('select:nth-of-type(3)').value;
    
    const button = event.target;
    const originalText = button.textContent;
    button.textContent = 'Generating...';
    button.disabled = true;
    
    setTimeout(() => {
        alert('Report Generation Started!\n\nReport Type: ' + reportType + '\nTime Period: ' + timePeriod + '\nFormat: ' + exportFormat + '\n\nEstimated completion: 2-5 minutes\nYou will receive notification when ready for download.');
        
        button.textContent = originalText;
        button.disabled = false;
    }, 1500);
}

function downloadReport(reportName) {
    const button = event.target;
    const originalText = button.textContent;
    button.textContent = 'Downloading...';
    button.disabled = true;
    
    setTimeout(() => {
        alert('Download started for: ' + reportName + '\n\nFile will be saved to your Downloads folder.\nFormat: PDF\nSize: 3.2 MB (estimated)\n\nReport includes:\n• Executive summary\n• Detailed analytics\n• Charts and graphs\n• Recommendations');
        
        button.textContent = originalText;
        button.disabled = false;
    }, 2000);
}

function viewReport(reportName) {
    alert('Opening report preview: ' + reportName + '\n\nReport Contents:\n• Executive Summary\n• Key Performance Indicators\n• Detailed Analysis Charts\n• Risk Assessment Data\n• Claims and Premium Trends\n• Recommendations and Action Items\n\nFull interactive view would open in new window with:\n• Drill-down capabilities\n• Export options\n• Print functionality\n• Share and collaborate features');
}
</script>
{% endblock %}