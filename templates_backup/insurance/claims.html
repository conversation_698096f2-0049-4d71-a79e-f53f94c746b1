{% extends "base.html" %}

{% block content %}
<div class="dashboard-container">
    <div class="dashboard-header">
        <h1>Claims Management</h1>
        <p>Process and manage insurance claims for cargo damage and loss</p>
    </div>

    <!-- Claims Statistics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-primary">
                    <i class="fas fa-file-medical"></i>
                </div>
                <div class="stat-content">
                    <h3>47</h3>
                    <p>Open Claims</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-success">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stat-content">
                    <h3>234</h3>
                    <p>Settled This Month</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-warning">
                    <i class="fas fa-dollar-sign"></i>
                </div>
                <div class="stat-content">
                    <h3>$845K</h3>
                    <p>Claims Value</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-info">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="stat-content">
                    <h3>12.5 days</h3>
                    <p>Avg. Settlement</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Priority Claims -->
    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5>Priority Claims</h5>
            <button class="btn btn-primary" onclick="showNewClaimModal()">New Claim</button>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Claim Number</th>
                            <th>Policy</th>
                            <th>Claimant</th>
                            <th>Incident Type</th>
                            <th>Claim Amount</th>
                            <th>Date Filed</th>
                            <th>Priority</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>CLM-2025-1834</td>
                            <td>INS-2025-4567</td>
                            <td>Global Import Corp</td>
                            <td>Water Damage</td>
                            <td>$125,000</td>
                            <td>2025-01-25</td>
                            <td><span class="badge bg-danger">High</span></td>
                            <td><span class="badge bg-warning">Under Investigation</span></td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary" onclick="reviewClaim('CLM-2025-1834')">Review</button>
                                <button class="btn btn-sm btn-outline-info" onclick="viewClaimDetails('CLM-2025-1834')">Details</button>
                            </td>
                        </tr>
                        <tr>
                            <td>CLM-2025-1835</td>
                            <td>INS-2025-4568</td>
                            <td>TechCorp Solutions</td>
                            <td>Theft</td>
                            <td>$89,500</td>
                            <td>2025-01-24</td>
                            <td><span class="badge bg-warning">Medium</span></td>
                            <td><span class="badge bg-info">Documentation Review</span></td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary" onclick="reviewClaim('CLM-2025-1835')">Review</button>
                                <button class="btn btn-sm btn-outline-info" onclick="viewClaimDetails('CLM-2025-1835')">Details</button>
                            </td>
                        </tr>
                        <tr>
                            <td>CLM-2025-1836</td>
                            <td>INS-2025-4570</td>
                            <td>Luxury Goods Inc</td>
                            <td>Transit Damage</td>
                            <td>$245,000</td>
                            <td>2025-01-23</td>
                            <td><span class="badge bg-danger">High</span></td>
                            <td><span class="badge bg-success">Approved</span></td>
                            <td>
                                <button class="btn btn-sm btn-outline-success" onclick="processPayment('CLM-2025-1836')">Process Payment</button>
                                <button class="btn btn-sm btn-outline-info" onclick="viewClaimDetails('CLM-2025-1836')">Details</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Claims Processing -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>Claims by Type</h5>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <h6>Water/Weather Damage</h6>
                                <small class="text-muted">Maritime and weather-related claims</small>
                            </div>
                            <span class="badge bg-primary">18 claims</span>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <h6>Theft/Piracy</h6>
                                <small class="text-muted">Security-related incidents</small>
                            </div>
                            <span class="badge bg-danger">12 claims</span>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <h6>Transit Damage</h6>
                                <small class="text-muted">Handling and transport damage</small>
                            </div>
                            <span class="badge bg-warning">8 claims</span>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <h6>Total Loss</h6>
                                <small class="text-muted">Complete cargo loss</small>
                            </div>
                            <span class="badge bg-info">9 claims</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>Settlement Performance</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <div class="text-center mb-3">
                                <h4 class="text-success">87.2%</h4>
                                <small>Settlement Rate</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center mb-3">
                                <h4 class="text-primary">12.5 days</h4>
                                <small>Avg. Time</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center mb-3">
                                <h4 class="text-warning">$3.6K</h4>
                                <small>Avg. Amount</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center mb-3">
                                <h4 class="text-info">94.8%</h4>
                                <small>Customer Satisfaction</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Claims Activity -->
    <div class="card">
        <div class="card-header">
            <h5>Recent Claims Activity</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Time</th>
                            <th>Claim Number</th>
                            <th>Action</th>
                            <th>Amount</th>
                            <th>Adjuster</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>16:45</td>
                            <td>CLM-2025-1836</td>
                            <td>Payment Processed</td>
                            <td>$245,000</td>
                            <td>Adjuster Williams</td>
                            <td><span class="badge bg-success">Settled</span></td>
                        </tr>
                        <tr>
                            <td>15:20</td>
                            <td>CLM-2025-1834</td>
                            <td>Investigation Update</td>
                            <td>$125,000</td>
                            <td>Adjuster Chen</td>
                            <td><span class="badge bg-info">Investigation</span></td>
                        </tr>
                        <tr>
                            <td>14:15</td>
                            <td>CLM-2025-1837</td>
                            <td>Claim Filed</td>
                            <td>$67,500</td>
                            <td>Auto-Assigned</td>
                            <td><span class="badge bg-warning">New</span></td>
                        </tr>
                        <tr>
                            <td>13:30</td>
                            <td>CLM-2025-1835</td>
                            <td>Documentation Review</td>
                            <td>$89,500</td>
                            <td>Adjuster Rodriguez</td>
                            <td><span class="badge bg-primary">Review</span></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- New Claim Modal -->
<div class="modal fade" id="newClaimModal" tabindex="-1" aria-labelledby="newClaimModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="newClaimModalLabel">Create New Insurance Claim</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="newClaimForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="shipmentNumber" class="form-label">Shipment Number</label>
                                <select class="form-select" id="shipmentNumber" name="shipmentNumber" required>
                                    <option value="">Select Shipment</option>
                                    <option value="SH-2025-001">SH-2025-001 (Global Import Corp)</option>
                                    <option value="SH-2025-002">SH-2025-002 (TechCorp Solutions)</option>
                                    <option value="SH-2025-003">SH-2025-003 (Industrial Supply Co)</option>
                                    <option value="SH-2025-004">SH-2025-004 (Luxury Goods Inc)</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="incidentType" class="form-label">Incident Type</label>
                                <select class="form-select" id="incidentType" name="incidentType" required>
                                    <option value="">Select Type</option>
                                    <option value="WATER_DAMAGE">Water Damage</option>
                                    <option value="THEFT">Theft</option>
                                    <option value="DAMAGE">Transit Damage</option>
                                    <option value="LOSS">Total Loss</option>
                                    <option value="FIRE_DAMAGE">Fire Damage</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="claimAmount" class="form-label">Claim Amount ($)</label>
                                <input type="number" class="form-control" id="claimAmount" name="claimAmount" required min="100" step="100">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="incidentDate" class="form-label">Incident Date</label>
                                <input type="date" class="form-control" id="incidentDate" name="incidentDate" required>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="description" class="form-label">Incident Description</label>
                        <textarea class="form-control" id="description" name="description" rows="3" required placeholder="Describe the incident and damages..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="createNewClaim()">File Claim</button>
            </div>
        </div>
    </div>
</div>

<script>
function showNewClaimModal() {
    document.getElementById('newClaimModal').style.display = 'block';
    new bootstrap.Modal(document.getElementById('newClaimModal')).show();
}

function createNewClaim() {
    const form = document.getElementById('newClaimForm');
    const formData = new FormData(form);
    
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }
    
    const button = event.target;
    const originalText = button.textContent;
    button.textContent = 'Filing...';
    button.disabled = true;
    
    fetch('/api/insurance/claims/create', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Claim filed successfully! Claim number: ' + (data.claim_number || 'CLM-' + new Date().getTime()));
            bootstrap.Modal.getInstance(document.getElementById('newClaimModal')).hide();
            form.reset();
            location.reload();
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error filing claim. Please try again.');
    })
    .finally(() => {
        button.textContent = originalText;
        button.disabled = false;
    });
}

function reviewClaim(claimNumber) {
    alert('Opening claim review for ' + claimNumber + '\n\nReview includes:\n- Incident verification\n- Documentation assessment\n- Coverage validation\n- Settlement recommendation\n- Adjuster assignment');
}

function viewClaimDetails(claimNumber) {
    alert('Viewing detailed information for claim ' + claimNumber + '\n\nDetails include:\n- Policy information\n- Incident report\n- Supporting documents\n- Communication history\n- Assessment reports\n- Timeline of events');
}

function processPayment(claimNumber) {
    if (confirm('Process payment for claim ' + claimNumber + '?\n\nThis will:\n• Generate settlement check\n• Update claim status to "Settled"\n• Send payment notification to customer\n• Close the claim file')) {
        // Create form and submit to backend
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '/insurance/claims/process-payment';
        form.style.display = 'none';
        
        const claimInput = document.createElement('input');
        claimInput.type = 'hidden';
        claimInput.name = 'claim_number';
        claimInput.value = claimNumber;
        form.appendChild(claimInput);
        
        document.body.appendChild(form);
        
        const checkNumber = 'CHK-' + new Date().getTime();
        alert('Payment processed successfully for claim ' + claimNumber + '!\n\nStatus: Settled\nCheck Number: ' + checkNumber + '\nPayment notification sent to customer');
        form.submit();
    }
}
</script>
{% endblock %}