{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <h1><i class="fas fa-user-circle"></i> My Profile</h1>
                <p class="text-muted">Manage your account information and preferences</p>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Personal Information -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-user"></i> Personal Information</h5>
                </div>
                <div class="card-body">
                    <form id="profileForm" onsubmit="updateProfile(event)">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="first_name">First Name</label>
                                    <input type="text" class="form-control" id="first_name" name="first_name" 
                                           value="{{ profile.personal_info.first_name }}" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="last_name">Last Name</label>
                                    <input type="text" class="form-control" id="last_name" name="last_name" 
                                           value="{{ profile.personal_info.last_name }}" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="email">Email Address</label>
                                    <input type="email" class="form-control" id="email" name="email" 
                                           value="{{ profile.personal_info.email }}" readonly>
                                    <small class="form-text text-muted">Contact support to change your email</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="phone">Phone Number</label>
                                    <input type="tel" class="form-control" id="phone" name="phone" 
                                           value="{{ profile.personal_info.phone }}" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="country">Country</label>
                                    <select class="form-control" id="country" name="country">
                                        <option value="United States" {% if profile.personal_info.country == 'United States' %}selected{% endif %}>United States</option>
                                        <option value="Canada">Canada</option>
                                        <option value="United Kingdom">United Kingdom</option>
                                        <option value="Germany">Germany</option>
                                        <option value="France">France</option>
                                        <option value="Other">Other</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="timezone">Timezone</label>
                                    <select class="form-control" id="timezone" name="timezone">
                                        <option value="UTC-5 (Eastern Time)" {% if profile.personal_info.timezone == 'UTC-5 (Eastern Time)' %}selected{% endif %}>UTC-5 (Eastern Time)</option>
                                        <option value="UTC-6 (Central Time)">UTC-6 (Central Time)</option>
                                        <option value="UTC-7 (Mountain Time)">UTC-7 (Mountain Time)</option>
                                        <option value="UTC-8 (Pacific Time)">UTC-8 (Pacific Time)</option>
                                        <option value="UTC+0 (GMT)">UTC+0 (GMT)</option>
                                        <option value="UTC+1 (CET)">UTC+1 (CET)</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Save Changes
                        </button>
                    </form>
                </div>
            </div>

            <!-- Company Information -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5><i class="fas fa-building"></i> Company Information</h5>
                </div>
                <div class="card-body">
                    <form id="companyForm" onsubmit="updateCompany(event)">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="company_name">Company Name</label>
                                    <input type="text" class="form-control" id="company_name" name="company_name" 
                                           value="{{ profile.company_info.company_name }}" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="company_type">Company Type</label>
                                    <input type="text" class="form-control" id="company_type" name="company_type" 
                                           value="{{ profile.company_info.company_type }}" readonly>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="tax_id">Tax ID</label>
                                    <input type="text" class="form-control" id="tax_id" name="tax_id" 
                                           value="{{ profile.company_info.tax_id }}">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="website">Website</label>
                                    <input type="url" class="form-control" id="website" name="website" 
                                           value="{{ profile.company_info.website }}">
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="address">Business Address</label>
                            <textarea class="form-control" id="address" name="address" rows="2">{{ profile.company_info.address }}</textarea>
                        </div>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Update Company Info
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Account Summary -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-bar"></i> Account Summary</h5>
                </div>
                <div class="card-body">
                    <div class="profile-stat">
                        <label>Member Since</label>
                        <span class="value">{{ profile.account_stats.member_since }}</span>
                    </div>
                    <div class="profile-stat">
                        <label>Total Shipments</label>
                        <span class="value text-primary">{{ profile.account_stats.total_shipments }}</span>
                    </div>
                    <div class="profile-stat">
                        <label>Completed Shipments</label>
                        <span class="value text-success">{{ profile.account_stats.completed_shipments }}</span>
                    </div>
                    <div class="profile-stat">
                        <label>Total Spent</label>
                        <span class="value text-info">{{ profile.account_stats.total_spent }}</span>
                    </div>
                    <div class="profile-stat">
                        <label>Verification Status</label>
                        <span class="badge badge-success">
                            <i class="fas fa-check-circle"></i> {{ profile.account_stats.verification_status }}
                        </span>
                    </div>
                </div>
            </div>

            <!-- Quick Preferences -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5><i class="fas fa-cog"></i> Quick Preferences</h5>
                </div>
                <div class="card-body">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="email_notifications" 
                               {% if profile.preferences.email_notifications %}checked{% endif %}>
                        <label class="form-check-label" for="email_notifications">
                            Email Notifications
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="sms_notifications" 
                               {% if profile.preferences.sms_notifications %}checked{% endif %}>
                        <label class="form-check-label" for="sms_notifications">
                            SMS Notifications
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="marketing_emails" 
                               {% if profile.preferences.marketing_emails %}checked{% endif %}>
                        <label class="form-check-label" for="marketing_emails">
                            Marketing Emails
                        </label>
                    </div>
                    <div class="form-group mt-3">
                        <label for="language">Language</label>
                        <select class="form-control" id="language">
                            <option value="English" {% if profile.preferences.language == 'English' %}selected{% endif %}>English</option>
                            <option value="Spanish">Spanish</option>
                            <option value="French">French</option>
                            <option value="German">German</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="currency">Currency</label>
                        <select class="form-control" id="currency">
                            <option value="USD" {% if profile.preferences.currency == 'USD' %}selected{% endif %}>USD</option>
                            <option value="EUR">EUR</option>
                            <option value="GBP">GBP</option>
                            <option value="CAD">CAD</option>
                        </select>
                    </div>
                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="savePreferences()">
                        <i class="fas fa-save"></i> Save Preferences
                    </button>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5><i class="fas fa-lightning-bolt"></i> Quick Actions</h5>
                </div>
                <div class="card-body">
                    <a href="/settings" class="btn btn-outline-secondary btn-block mb-2">
                        <i class="fas fa-cogs"></i> Advanced Settings
                    </a>
                    <a href="/customer/manage-shipments" class="btn btn-outline-info btn-block mb-2">
                        <i class="fas fa-boxes"></i> My Shipments
                    </a>
                    <a href="/notifications" class="btn btn-outline-warning btn-block mb-2">
                        <i class="fas fa-bell"></i> Notifications
                    </a>
                    <button class="btn btn-outline-danger btn-block" onclick="changePassword()">
                        <i class="fas fa-key"></i> Change Password
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.profile-stat {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.profile-stat:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.profile-stat label {
    font-weight: 500;
    color: #666;
}

.profile-stat .value {
    font-weight: 600;
}

.form-group {
    margin-bottom: 1rem;
}

.card {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}
</style>

<script>
function updateProfile(event) {
    event.preventDefault();
    const formData = new FormData(event.target);
    
    fetch('/profile/update', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            showAlert('Profile updated successfully!', 'success');
        } else {
            showAlert('Error updating profile', 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('Error updating profile', 'danger');
    });
}

function updateCompany(event) {
    event.preventDefault();
    const formData = new FormData();
    
    // Get form values and append to FormData
    formData.append('first_name', document.getElementById('first_name').value);
    formData.append('last_name', document.getElementById('last_name').value);
    formData.append('phone', document.getElementById('phone').value);
    formData.append('country', document.getElementById('country').value);
    formData.append('company_name', document.getElementById('company_name').value);
    formData.append('tax_id', document.getElementById('tax_id').value);
    formData.append('address', document.getElementById('address').value);
    
    fetch('/profile/update', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            showAlert('Company information updated successfully!', 'success');
        } else {
            showAlert('Error updating company information', 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('Error updating company information', 'danger');
    });
}

function savePreferences() {
    const formData = new FormData();
    formData.append('email_notifications', document.getElementById('email_notifications').checked);
    formData.append('sms_notifications', document.getElementById('sms_notifications').checked);
    formData.append('marketing_emails', document.getElementById('marketing_emails').checked);
    formData.append('language', document.getElementById('language').value);
    formData.append('currency', document.getElementById('currency').value);
    
    fetch('/profile/preferences', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            showAlert('Preferences saved successfully!', 'success');
        } else {
            showAlert('Error saving preferences', 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('Error saving preferences', 'danger');
    });
}

function changePassword() {
    // Create password change modal
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Change Password</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="passwordForm">
                        <div class="form-group mb-3">
                            <label for="current_password">Current Password</label>
                            <input type="password" class="form-control" id="current_password" name="current_password" required>
                        </div>
                        <div class="form-group mb-3">
                            <label for="new_password">New Password</label>
                            <input type="password" class="form-control" id="new_password" name="new_password" required>
                        </div>
                        <div class="form-group mb-3">
                            <label for="confirm_password">Confirm New Password</label>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-danger" onclick="submitPasswordChange()">Change Password</button>
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    const bootstrapModal = new bootstrap.Modal(modal);
    bootstrapModal.show();
    
    // Clean up modal when closed
    modal.addEventListener('hidden.bs.modal', () => {
        document.body.removeChild(modal);
    });
}

function submitPasswordChange() {
    const formData = new FormData();
    formData.append('current_password', document.getElementById('current_password').value);
    formData.append('new_password', document.getElementById('new_password').value);
    formData.append('confirm_password', document.getElementById('confirm_password').value);
    
    fetch('/profile/change-password', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            showAlert('Password changed successfully!', 'success');
            // Close the modal
            bootstrap.Modal.getInstance(document.querySelector('.modal')).hide();
        } else {
            showAlert(data.message || 'Error changing password', 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('Error changing password', 'danger');
    });
}

function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="close" data-dismiss="alert">
            <span>&times;</span>
        </button>
    `;
    
    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);
    
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}
</script>
{% endblock %}